package common

import (
	"context"
	"strings"

	"github.com/pkg/errors"
	"go.uber.org/fx"

	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/knowledgebase/knowledgebase"
	"code.byted.org/devgpt/kiwis/lib/config"
	libtcc "code.byted.org/devgpt/kiwis/lib/tcc"
	"code.byted.org/devgpt/kiwis/port/abase"
	dbclient "code.byted.org/devgpt/kiwis/port/db"
	"code.byted.org/devgpt/kiwis/port/github"
	bytekitex "code.byted.org/kite/kitex/byted"
	"code.byted.org/paas/cloud-sdk-go/jwt"
)

var KnowledgeBaseClientModule = fx.Options(
	fx.Provide(
		func(conf config.KnowledgeBaseConfig) (knowledgebase.Client, error) {
			return knowledgebase.NewClientWithBytedConfig(conf.PSM, bytekitex.NewClientConfig())
		},
	),
)

var DBModule = fx.Options(
	fx.Provide(func(conf config.RDSConfig) (dbclient.Client, error) {
		return dbclient.NewRDSClient(conf)
	}),
)

var AbaseModule = fx.Options(
	fx.Provide(fx.Annotate(
		func(conf config.CodeAssistAgentConfig) (*abase.Client, error) {
			option := abase.NewOption()
			return abase.NewClient(conf.MessageAbaseConfig.PSM, "", conf.MessageAbaseConfig.Table, option)
		},
		fx.ResultTags(`name:"message_abase_client"`)),
	),
	fx.Provide(fx.Annotate(
		func(conf config.CodeAssistAgentConfig) (*abase.Client, error) {
			option := abase.NewOption()
			return abase.NewClient(conf.ToolAbaseConfig.PSM, "", conf.ToolAbaseConfig.Table, option)
		},
		fx.ResultTags(`name:"observation_abase_client"`)),
	),
)

var GithubModule = fx.Options(
	// github client
	fx.Provide(func(tccConfig *config.CodeAssistTCCConfig) github.Client {
		client, err := github.NewClient(tccConfig.GithubConfig.GetPointer())
		if err != nil {
			panic(err)
		}
		return client
	}),

	// github org installation
	fx.Provide(func(githubClient github.Client, githubTccConfig *libtcc.GenericConfig[config.GithubConfig]) *config.OrgInstallationCollection {
		orgNames := strings.TrimSpace(githubTccConfig.GetValue().GithubOrgNames)
		var res = &config.OrgInstallationCollection{
			OrgInstallationIDs: make(map[string]int64),
		}
		if orgNames == "" {
			panic(errors.New("github org name is empty"))
		}
		var orgNameArr = strings.Split(orgNames, ",")
		res.OrgNames = orgNameArr
		for _, orgName := range orgNameArr {
			installID, err := githubClient.GetOrgInstallationID(context.Background(), orgName)
			if err != nil {
				panic(errors.Wrapf(err, "get org %s installation id failed", orgName))
			}
			res.OrgInstallationIDs[orgName] = installID
		}
		return res
	}),
)

// JWTModule 使用公司的paas/cloud-sdk-go库，考虑到port中的jwt并未实现缓存功能，因此这里使用公司库
var JWTModule = fx.Options(
	fx.Provide(func() jwt.Generator {
		return jwt.NewGenerator(jwt.WithRegion(jwt.RegionCN))
	}),
)
