package main

import (
	"code.byted.org/gopkg/logs/v2"
	"context"
	"encoding/json"
	"fmt"
	"github.com/pkg/errors"
	"io"
	"time"

	"code.byted.org/bytefaas/faas-go/bytefaas"
	"code.byted.org/bytefaas/faas-go/events"
	kitexClient "code.byted.org/kite/kitex/client"
	"code.byted.org/kite/kitex/client/callopt"

	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/codeassist"
	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/codeassist/assistantservice"
)

const evaluationClusterName = "offline_test"

var assistantClient assistantservice.Client
var assistantStreamClient assistantservice.StreamClient

func helloHTTP(ctx context.Context, r *events.HTTPRequest) (*events.EventResponse, error) {
	logs.V1.CtxInfo(ctx, "received http request %v", r)
	switch r.Path {
	case "/desensitize_context":
		// desensitize file content of context, return compress files download link
		return desensitize(ctx, r)
	case "/get_repo_info":
		// return repo info of given repo resource key
		return getRepoInfo(ctx, r)
	case "/chat":
		return chat(ctx, r)
	}
	return nil, fmt.Errorf("unknown path: %s", r.Path)
}

type DesensitizeRequest struct {
	ResourceType string   `json:"ResourceType"`
	ResourceKeys []string `json:"ResourceKeys"`
}

type DesensitizeResponse struct {
	DesensitizedContextID           string `json:"DesensitizedContextID"`
	DesensitizedContextDownloadLink string `json:"DesensitizedContextDownloadLink"`
}

func desensitize(ctx context.Context, r *events.HTTPRequest) (*events.EventResponse, error) {
	var req DesensitizeRequest
	err := json.Unmarshal(r.Body, &req)
	if err != nil {
		return nil, err
	}

	desensitizeContextReq := &codeassist.DesensitizeContextRequest{
		ResourceKeys: req.ResourceKeys,
	}
	switch req.ResourceType {
	case "file":
		desensitizeContextReq.Type = codeassist.ResourceType_File
	case "directory":
		desensitizeContextReq.Type = codeassist.ResourceType_Directory
	default:
		return nil, fmt.Errorf("unknown resource type: %s", req.ResourceType)
	}

	desensitizeContextResp, err := assistantClient.DesensitizeContext(ctx, desensitizeContextReq, callopt.WithCluster(evaluationClusterName))
	if err != nil {
		return nil, err
	}

	resp := &DesensitizeResponse{
		DesensitizedContextID:           desensitizeContextResp.DesensitizedContextID,
		DesensitizedContextDownloadLink: desensitizeContextResp.DesensitizedContextDownloadLink,
	}
	bodyStr, _ := json.Marshal(resp)
	return &events.EventResponse{
		Headers: map[string]string{
			"Content-Type": "application/json",
		},
		Body: bodyStr,
	}, nil
}

type GetRepoInfoRequest struct {
	RepoResourceKey string `json:"RepoResourceKey"`
}

type GetRepoInfoResponse struct {
	RepoName    string `json:"RepoName"`
	RepoLink    string `json:"RepoLink"`
	Ref         string `json:"Ref"`
	Language    string `json:"Language"`
	Description string `json:"Description"`
	Size        int64  `json:"Size"`
}

func getRepoInfo(ctx context.Context, r *events.HTTPRequest) (*events.EventResponse, error) {
	var req GetRepoInfoRequest
	err := json.Unmarshal(r.Body, &req)
	if err != nil {
		return nil, err
	}

	getContextInfoReq := &codeassist.GetContextInfoRequest{
		Identifier: &codeassist.ContextIdentifier{
			Type:        codeassist.ResourceType_Repository,
			ResourceKey: req.RepoResourceKey,
		},
	}

	getContextInfoResp, err := assistantClient.GetContextInfo(ctx, getContextInfoReq)
	if err != nil {
		return nil, err
	}

	resp := &GetRepoInfoResponse{
		RepoName:    getContextInfoResp.RepositoryContextInfo.Name,
		RepoLink:    getContextInfoResp.RepositoryContextInfo.Link,
		Ref:         getContextInfoResp.RepositoryContextInfo.Ref,
		Language:    getContextInfoResp.RepositoryContextInfo.Language,
		Description: getContextInfoResp.RepositoryContextInfo.Description,
		Size:        getContextInfoResp.RepositoryContextInfo.Size,
	}
	bodyStr, _ := json.Marshal(resp)
	return &events.EventResponse{
		Headers: map[string]string{
			"Content-Type": "application/json",
		},
		Body: bodyStr,
	}, nil
}

type ChatRequest struct {
	Content        string `json:"Content"`
	FileName       string `json:"FileName"`
	FileURI        string `json:"FileURI"`
	UserID         int64  `json:"UserId"`
	MessageID      int64  `json:"MessageId"`
	ConversationID int64  `json:"ConversationId"`
}

func chat(ctx context.Context, r *events.HTTPRequest) (*events.EventResponse, error) {
	var req ChatRequest
	err := json.Unmarshal(r.Body, &req)
	if err != nil {
		return nil, err
	}

	chatReq := &codeassist.ChatRequest{
		UserMessage: &codeassist.Message{
			Role:    codeassist.ChatMessageRole_USER,
			Content: req.Content,
			ContextBlocks: []*codeassist.ContextBlock{
				{
					Type: codeassist.ContextBlockType_FILE_BLOCK,
					FileBlock: &codeassist.FileBlock{
						Identifiers: []*codeassist.ContextIdentifier{
							{
								Type:        codeassist.ResourceType_File,
								ResourceKey: req.FileURI,
								Name:        &req.FileName,
							},
						},
					},
				},
			},
			BizInfo: &codeassist.BizInfo{
				MessageID:      &req.MessageID,
				ConversationID: &req.ConversationID,
			},
		},
		UserID: req.UserID,
	}

	chatResp, err := assistantStreamClient.Chat(ctx, chatReq)
	if err != nil {
		return nil, err
	}

	var result []*codeassist.ChatEventContent
	for {
		receive, err := chatResp.Recv()
		if err != nil {
			if errors.Is(err, io.EOF) {
				logs.V1.CtxInfo(ctx, "finish")
			} else {
				logs.V1.CtxError(ctx, "has error: %v", err)
			}
			break
		}
		result = append(result, receive.EventContent)
	}
	bodyStr, _ := json.Marshal(result)
	return &events.EventResponse{
		Headers: map[string]string{
			"Content-Type": "application/json",
		},
		Body: bodyStr,
	}, nil
}

func main() {
	// init rpc client
	assistantClient = assistantservice.MustNewClient("flow.codeassist.assistant", kitexClient.WithRPCTimeout(time.Second*600))
	assistantStreamClient = assistantservice.MustNewStreamClient("flow.codeassist.assistant")
	// Make the handler available for Http Call by Bytefaas Function
	bytefaas.Start(helloHTTP)
}
