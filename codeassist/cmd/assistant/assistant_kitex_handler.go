package main

import (
	"context"

	"github.com/samber/lo"
	"go.uber.org/fx"

	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/codeassist"
	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/common"
	agenthandler "code.byted.org/devgpt/kiwis/codeassist/agent/handler"
	chathandler "code.byted.org/devgpt/kiwis/codeassist/chat/handler"
	"code.byted.org/devgpt/kiwis/codeassist/context/handler"
	reviewhandler "code.byted.org/devgpt/kiwis/codeassist/review/handler"
	sandboxhandler "code.byted.org/devgpt/kiwis/codeassist/sandbox/handler"
)

var _ codeassist.AssistantService = &AssistantKitexHandler{}

type AssistantKitexHandler struct {
	fx.In
	ChatHandler    *chathandler.ChatHandler
	ContextHandler *handler.ContextHandler
	SandboxHandler *sandboxhandler.SandboxHandler
	ReviewHandler  *reviewhandler.ReviewHandler
	AgentHandler   *agenthandler.AgentHandler
}

func (k *AssistantKitexHandler) Ping(ctx context.Context, req *codeassist.PingRequest) (r *codeassist.PingResponse, err error) {
	return &codeassist.PingResponse{
		Code:    lo.ToPtr(common.ErrorCode_ErrUndefined),
		Message: lo.ToPtr("pong"),
	}, nil
}

func (k *AssistantKitexHandler) Chat(req *codeassist.ChatRequest, stream codeassist.AssistantService_ChatServer) (err error) {
	return k.ChatHandler.Chat(req, stream)
}

func (k *AssistantKitexHandler) PromptsRender(ctx context.Context, req *codeassist.PromptsRenderRequest) (r *codeassist.PromptsRenderResponse, err error) {
	return k.ChatHandler.PromptsRender(ctx, req)
}

func (k *AssistantKitexHandler) E2EPromptsRender(ctx context.Context, req *codeassist.E2EPromptsRenderRequest) (r *codeassist.E2EPromptsRenderResponse, err error) {
	return k.ChatHandler.E2EPromptsRender(ctx, req)
}

func (k *AssistantKitexHandler) HomePage(ctx context.Context, req *codeassist.HomePageRequest) (r *codeassist.HomePageResponse, err error) {
	return k.ChatHandler.HomePage(ctx, req)

}

func (k *AssistantKitexHandler) SniffRepositoryLink(ctx context.Context, req *codeassist.SniffRepositoryLinkRequest) (r *codeassist.SniffRepositoryLinkResponse, err error) {
	return k.ContextHandler.SniffRepositoryLink(ctx, req)
}

func (k *AssistantKitexHandler) CreateContext(ctx context.Context, req *codeassist.CreateContextRequest) (r *codeassist.CreateContextResponse, err error) {
	return k.ContextHandler.CreateContext(ctx, req)
}

func (k *AssistantKitexHandler) MGetContextStatus(ctx context.Context, req *codeassist.MGetContextStatusRequest) (r *codeassist.MGetContextStatusResponse, err error) {
	return k.ContextHandler.MGetContextStatus(ctx, req)
}

func (k *AssistantKitexHandler) GetContextDirectoryNodes(ctx context.Context, req *codeassist.GetContextDirectoryNodesRequest) (r *codeassist.GetContextDirectoryNodesResponse, err error) {
	return k.ContextHandler.GetContextDirectoryNodes(ctx, req)
}

func (k *AssistantKitexHandler) GetRepoFileContent(ctx context.Context, req *codeassist.GetRepoFileContentRequest) (r *codeassist.GetRepoFileContentResponse, err error) {
	return k.ContextHandler.GetRepoFileContent(ctx, req)
}

func (k *AssistantKitexHandler) GetContextInfo(ctx context.Context, req *codeassist.GetContextInfoRequest) (r *codeassist.GetContextInfoResponse, err error) {
	return k.ContextHandler.GetContextInfo(ctx, req)
}

func (k *AssistantKitexHandler) GetContextDownloadURL(ctx context.Context, req *codeassist.GetContextDownloadURLRequest) (r *codeassist.GetContextDownloadURLResponse, err error) {
	return k.ContextHandler.GetContextDownloadURL(ctx, req)
}

func (k *AssistantKitexHandler) GetCodeResourceDownloadURL(ctx context.Context, req *codeassist.GetCodeResourceDownloadURLRequest) (r *codeassist.GetCodeResourceDownloadURLResponse, err error) {
	return k.ContextHandler.GetCodeResourceDownloadURL(ctx, req)
}

func (k *AssistantKitexHandler) DesensitizeContext(ctx context.Context, req *codeassist.DesensitizeContextRequest) (r *codeassist.DesensitizeContextResponse, err error) {
	return k.ContextHandler.DesensitizeContext(ctx, req)
}

func (k *AssistantKitexHandler) CodeExecutable(ctx context.Context, req *codeassist.CodeExecutableRequest) (r *codeassist.CodeExecutableResponse, err error) {
	return k.SandboxHandler.CodeExecutable(ctx, req)
}

func (k *AssistantKitexHandler) RunCode(ctx context.Context, req *codeassist.RunCodeRequest) (r *codeassist.RunCodeResponse, err error) {
	return k.SandboxHandler.RunCode(ctx, req)
}

func (k *AssistantKitexHandler) BatchCheckFilePath(ctx context.Context, req *codeassist.BatchCheckFilePathRequest) (r *codeassist.BatchCheckFilePathResponse, err error) {
	return k.ContextHandler.BatchCheckFilePath(ctx, req)
}

func (k *AssistantKitexHandler) ImageReview(ctx context.Context, req *codeassist.ImageReviewRequest) (r *codeassist.ImageReviewResponse, err error) {
	return k.ReviewHandler.ImageReview(ctx, req)
}

func (k *AssistantKitexHandler) RunCodeV2(ctx context.Context, req *codeassist.RunCodeRequestV2) (r *codeassist.RunCodeResponseV2, err error) {
	return k.SandboxHandler.RunCodeV2(ctx, req)
}

func (k *AssistantKitexHandler) GetArtifactTemplateFile(ctx context.Context, req *codeassist.GetArtifactTemplateFileRequest) (r *codeassist.GetArtifactTemplateFileResponse, err error) {
	return k.SandboxHandler.GetArtifactTemplateFile(ctx, req)
}

func (k *AssistantKitexHandler) GetArtifactTemplateDir(ctx context.Context, req *codeassist.GetArtifactTemplateDirRequest) (r *codeassist.GetArtifactTemplateDirResponse, err error) {
	return k.SandboxHandler.GetArtifactTemplateDir(ctx, req)
}

func (k *AssistantKitexHandler) CompileCodeArtifact(ctx context.Context, req *codeassist.CompileCodeArtifactRequest) (r *codeassist.CompileCodeArtifactResponse, err error) {
	return k.SandboxHandler.CompileCodeArtifact(ctx, req)
}

func (k *AssistantKitexHandler) GetCompileStatus(ctx context.Context, req *codeassist.GetCompileStatusRequest) (r *codeassist.GetCompileStatusResponse, err error) {
	return k.SandboxHandler.GetCompileStatus(ctx, req)
}

func (k *AssistantKitexHandler) GetArtifactsCodeURI(ctx context.Context, req *codeassist.GetArtifactsCodeURIRequest) (r *codeassist.GetArtifactsCodeURIResponse, err error) {
	return k.SandboxHandler.GetArtifactsCodeURI(ctx, req)
}

func (k *AssistantKitexHandler) SearchImages(ctx context.Context, req *codeassist.SearchImagesRequest) (r *codeassist.SearchImagesResponse, err error) {
	return k.ChatHandler.SearchImages(ctx, req)
}

func (k *AssistantKitexHandler) CreateSandbox(ctx context.Context, req *codeassist.CreateSandboxRequest) (r *codeassist.CreateSandboxResponse, err error) {
	return k.SandboxHandler.ProcessDataCreateSandbox(ctx, req)
}

func (k *AssistantKitexHandler) ReleaseSandbox(ctx context.Context, req *codeassist.ReleaseSandboxRequest) (r *codeassist.ReleaseSandboxResponse, err error) {
	return k.SandboxHandler.ProcessDataReleaseSandbox(ctx, req)
}

func (k *AssistantKitexHandler) ExecuteCode(ctx context.Context, req *codeassist.ExecuteCodeRequest) (r *codeassist.ExecuteCodeResponse, err error) {
	return k.SandboxHandler.ProcessDataExecuteCode(ctx, req)
}

func (k *AssistantKitexHandler) UploadFile(ctx context.Context, req *codeassist.UploadFileRequest) (r *codeassist.UploadFileResponse, err error) {
	return k.SandboxHandler.ProcessDataUploadFile(ctx, req)
}

func (k *AssistantKitexHandler) DownloadFile(ctx context.Context, req *codeassist.DownloadFileRequest) (r *codeassist.DownloadFileResponse, err error) {
	return k.SandboxHandler.ProcessDataDownloadFile(ctx, req)
}

func (k *AssistantKitexHandler) InterruptCodeAgentTask(ctx context.Context, req *codeassist.InterruptCodeAgentTaskRequest) (r *codeassist.InterruptCodeAgentTaskResponse, err error) {
	return k.AgentHandler.InterruptCodeAgentTask(ctx, req)
}

func (k *AssistantKitexHandler) ExecuteJobCallback(ctx context.Context, req *codeassist.ExecuteJobCallbackReq) (r *codeassist.ExecuteJobCallbackResp, err error) {
	return k.AgentHandler.RecoverFromSchedulerCallBack(ctx, req)
}
