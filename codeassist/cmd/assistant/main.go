package main

import (
	"go.uber.org/fx"

	"code.byted.org/devgpt/kiwis/codeassist/agent"
	"code.byted.org/devgpt/kiwis/codeassist/app"
	"code.byted.org/devgpt/kiwis/codeassist/chat"
	cmdcommon "code.byted.org/devgpt/kiwis/codeassist/cmd/common"
	contextmodule "code.byted.org/devgpt/kiwis/codeassist/context"
	"code.byted.org/devgpt/kiwis/codeassist/journal"
	"code.byted.org/devgpt/kiwis/codeassist/resolver"
	"code.byted.org/devgpt/kiwis/codeassist/review"
	"code.byted.org/devgpt/kiwis/codeassist/sandbox"
	"code.byted.org/devgpt/kiwis/copilotstack/llm"
	"code.byted.org/devgpt/kiwis/lib/metrics"
	"code.byted.org/devgpt/kiwis/port/dkms"
	"code.byted.org/devgpt/kiwis/port/fornax"
	"code.byted.org/devgpt/kiwis/port/llmops"
	"code.byted.org/devgpt/kiwis/port/redis"
)

func main() {
	opts := fx.Options(
		fx.Invoke(metrics.InitCopilotMetric),
		fx.Invoke(metrics.InitCodeAssistMetric),
		configModule,
		kitexModule,
		dkms.Module,
		tccModule,
		cmdcommon.IDGenModule,
		cmdcommon.KnowledgeBaseClientModule,
		cmdcommon.DBModule,
		cmdcommon.GithubModule,
		cmdcommon.JWTModule,
		redis.Module,
		journal.Module,
		resolver.Module,
		chat.Module,
		app.Module,
		sandbox.Module,
		review.Module,
		llmops.Module,
		llm.Module,
		contextmodule.Module,
		agent.Module,
		cmdcommon.AbaseModule,
	)
	fx.New(opts).Run()
	// 确保 fornax trace 完整上报
	fornax.Close()
}
