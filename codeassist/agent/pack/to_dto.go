package pack

import (
	impb "code.byted.org/flow/alice_protocol/im_pb"

	chatentity "code.byted.org/devgpt/kiwis/codeassist/chat/entity"
)

func BlockTypeToDTO(blockType int32) chatentity.ChatContentType {
	switch blockType {
	case int32(impb.ContentType_CONTENT_TYPE_BLOCK_TEXT):
		return chatentity.ChatContentTypeBlockText
	case int32(impb.ContentType_CONTENT_TYPE_BLOCK_RESEARCH_PROCESS_CARD):
		return chatentity.ChatContentTypeBlockResearchProcessCard
	case int32(impb.ContentType_CONTENT_TYPE_BLOCK_CODE):
		return chatentity.ChatContentTypeBlockCode
	case int32(impb.ContentType_CONTENT_TYPE_BLOCK_RESEARCH_WEBPAGE):
		return chatentity.ChatContentTypeBlockResearchWebpage
	case int32(impb.ContentType_CONTENT_TYPE_BLOCK_FILE_OPERATION):
		return chatentity.ChatContentTypeBlockFileOperation
	case int32(impb.ContentType_CONTENT_TYPE_BLOCK_FILE):
		return chatentity.ChatContentTypeBlockFile
	case int32(impb.ContentType_CONTENT_TYPE_BLOCK_ARTIFACT_CODE_FILE):
		return chatentity.ChatContentTypeBlockArtifactCodeFile
	case int32(impb.ContentType_CONTENT_TYPE_BLOCK_CODE_ASSIST_PROCESS_BLOCK):
		return chatentity.ChatContentTypeBlockCodeAssistProcess
	case int32(impb.ContentType_CONTENT_TYPE_BLOCK_ARTIFACT):
		return chatentity.ChatContentTypeBlockArtifact
	default:
		return ""
	}
}
