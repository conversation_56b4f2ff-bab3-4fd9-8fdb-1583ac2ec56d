package service

import (
	"context"

	"code.byted.org/devgpt/kiwis/codeassist/agent/entity"
)

//go:generate go run go.uber.org/mock/mockgen@latest -destination ../mock/service/memory_service.go -package service code.byted.org/devgpt/kiwis/codeassist/agent/service MemoryService
type MemoryService interface {
	SaveAgentRunStep(ctx context.Context, opt *SaveAgentRunStepOption) (string, error)
	UpdateAgentRunStep(ctx context.Context, opt *UpdateAgentRunStepOption) error
	AddToolCall(ctx context.Context, toolCall *entity.AgentRunStepToolCall) error
	GetAgentRunSteps(ctx context.Context, opt *GetAgentRunStepOption) ([]*entity.AgentRunStep, error)
}

type SaveAgentRunStepOption struct {
	AgentRunStep *entity.AgentRunStep
}

type UpdateAgentRunStepOption struct {
	AgentRunStepID string
	Status         entity.AgentRunStepStatus
	RawMessage     string
	Metadata       *entity.AgentRunStepMetaData
}

type GetAgentRunStepOption struct {
	Agent<PERSON>un<PERSON> string
}
