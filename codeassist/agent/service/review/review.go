package review

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"strconv"
	"sync"
	"time"

	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/overpass/common/option/calloption"
	"code.byted.org/overpass/ocean_cloud_review/kitex_gen/ocean/cloud/review"
	"code.byted.org/overpass/ocean_cloud_review/rpc/ocean_cloud_review"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	poolsdk "github.com/sourcegraph/conc/pool"

	"code.byted.org/devgpt/kiwis/codeassist/agent/entity"
	"code.byted.org/devgpt/kiwis/codeassist/agent/service"
	contextservice "code.byted.org/devgpt/kiwis/codeassist/context/service"
)

const (
	textScenarioID     = 200000000210
	imgScenarioID      = 200000000211
	filenameScenarioID = 200000000097
	reportScenarioID   = 200000000212

	// 豆包Bot id
	doubaoBotID = "7338286299411103781"
	// 豆包App id，在samantha中定义
	doubaoAppID = 482431

	codeAssistSource = "pc_codeassist"
)

var _ service.ReviewService = &Service{}

type Service struct {
	DoubaoService contextservice.DoubaoService
}

func (s *Service) TextReview(ctx context.Context, opt *service.ReviewOption) (review.CheckResultEnum, error) {
	param := &entity.BizParam{
		Source:  codeAssistSource,
		Text:    opt.Content,
		Texts:   []string{opt.Content},
		BotID:   doubaoBotID,
		BotType: strconv.Itoa(int(review.BotType_Coco)),
	}

	marshalTextsParam, err := json.Marshal(param)
	if err != nil {
		logs.CtxError(ctx, "marshal texts failed, err: %v", err)
		return review.CheckResultEnum_Other, errors.Errorf("marshal texts failed, err: %v", err)
	}
	strTextsParam := string(marshalTextsParam)

	// 调用审核服务（AsyncReview不是异步服务，此处为调用下游取名有点问题）
	messageID, err := strconv.ParseInt(opt.MessageID, 10, 64)
	if err != nil {
		logs.V1.CtxError(ctx, "invalid message id: %s", opt.MessageID)
		return review.CheckResultEnum_Other, err
	}

	textResp, err := ocean_cloud_review.RawCall.SyncReview(ctx, &review.SyncReviewRequest{
		GeneralReviewParam: &review.GeneralReviewRequestParam{
			ReviewEntityID:   messageID,
			AppID:            doubaoAppID,    // AppID_Doubao，和AppID_Cici区分开
			ReviewScenarioID: textScenarioID, // 文审
		},
		BizJSONParam: &strTextsParam,
	}, calloption.WithReqRespLogsInfo(), calloption.WithRPCTimeout(time.Millisecond*5000))
	if err != nil {
		logs.CtxError(ctx, "review text failed, err: %v", err)
		return review.CheckResultEnum_Other, errors.Errorf("review text failed, err: %v", err)
	}

	return textResp.GetCheckResult_(), nil
}

func (s *Service) ImageReview(ctx context.Context, opt *service.ReviewOption) (review.CheckResultEnum, error) {
	imgData, err := base64.StdEncoding.DecodeString(opt.Content)
	if err != nil {
		return review.CheckResultEnum_Other, errors.Errorf("decode image failed, err: %v", err)
	}
	uri, err := s.DoubaoService.StreamUploadSlice(ctx, imgData, "temp.png", opt.UserID)
	if err != nil {
		return review.CheckResultEnum_Other, errors.Errorf("upload image failed, err: %v", err)
	}
	imgURL, _, err := s.DoubaoService.GetDoubaoURLByURI(ctx, uri, opt.UserID)
	if err != nil {
		return review.CheckResultEnum_Other, errors.Errorf("get doubao url failed, err: %v", err)
	}

	param := &entity.BizParam{
		Source:   codeAssistSource,
		URI:      uri,
		ImageURL: imgURL,
		BotID:    doubaoBotID,
		BotType:  strconv.Itoa(int(review.BotType_Coco)),
	}

	marshalImageParam, err := json.Marshal(param)
	if err != nil {
		logs.CtxError(ctx, "marshal image failed, err: %v", err)
		return review.CheckResultEnum_Other, errors.Errorf("marshal image failed, err: %v", err)
	}
	strImageParam := string(marshalImageParam)

	// 调用审核服务（AsyncReview不是异步服务，此处为调用下游取名有点问题）
	messageID, err := strconv.ParseInt(opt.MessageID, 10, 64)
	if err != nil {
		logs.V1.CtxError(ctx, "invalid message id: %s", opt.MessageID)
		return review.CheckResultEnum_Other, err
	}

	imageResp, err := ocean_cloud_review.RawCall.SyncReview(ctx, &review.SyncReviewRequest{
		GeneralReviewParam: &review.GeneralReviewRequestParam{
			ReviewEntityID:   messageID,
			AppID:            doubaoAppID,   // AppID_Doubao，和AppID_Cici区分开
			ReviewScenarioID: imgScenarioID, // 图审
		},
		BizJSONParam: &strImageParam,
	}, calloption.WithReqRespLogsInfo(), calloption.WithRPCTimeout(time.Millisecond*5000))
	if err != nil {
		logs.CtxError(ctx, "review img failed, err: %v", err)
		return review.CheckResultEnum_Other, errors.Errorf("review img failed, err: %v", err)
	}

	return imageResp.GetCheckResult_(), nil
}

func (s *Service) FullTextReview(ctx context.Context, opt *service.ReviewOption) (review.CheckResultEnum, error) {
	parallelPool := poolsdk.New().WithMaxGoroutines(10).WithErrors()

	var mu sync.Mutex
	slidingTexts := slidingSplit(opt.Content)
	reviewResults := make([]review.CheckResultEnum, 0)
	for _, text := range slidingTexts {
		tempText := text
		parallelPool.Go(func() error {
			reviewResult, err := s.TextReview(ctx, &service.ReviewOption{
				UserID:    opt.UserID,
				MessageID: opt.MessageID,
				Content:   tempText,
			})
			if err != nil {
				logs.V1.CtxError(ctx, "[reviewFullText] text review failed: %v", err)
				return err
			}

			mu.Lock()
			reviewResults = append(reviewResults, reviewResult)
			mu.Unlock()
			return nil
		})
	}
	err := parallelPool.Wait()
	if err != nil {
		return review.CheckResultEnum_Other, err
	}

	if lo.Contains(reviewResults, review.CheckResultEnum_UnPass) {
		return review.CheckResultEnum_UnPass, nil
	}
	return review.CheckResultEnum_Pass, nil
}

func (s *Service) FilenameReview(ctx context.Context, opt *service.ReviewOption) (review.CheckResultEnum, error) {
	param := &entity.BizParam{
		Source:  codeAssistSource,
		Text:    opt.Content,
		Texts:   []string{opt.Content},
		BotID:   doubaoBotID,
		BotType: strconv.Itoa(int(review.BotType_Coco)),
	}

	marshalFilenameParam, err := json.Marshal(param)
	if err != nil {
		logs.CtxError(ctx, "marshal texts failed, err: %v", err)
		return review.CheckResultEnum_Other, errors.Errorf("marshal texts failed, err: %v", err)
	}
	strFilenameParam := string(marshalFilenameParam)

	// 调用审核服务（AsyncReview不是异步服务，此处为调用下游取名有点问题）
	messageID, err := strconv.ParseInt(opt.MessageID, 10, 64)
	if err != nil {
		logs.V1.CtxError(ctx, "invalid message id: %s", opt.MessageID)
		return review.CheckResultEnum_Other, err
	}

	textResp, err := ocean_cloud_review.RawCall.SyncReview(ctx, &review.SyncReviewRequest{
		GeneralReviewParam: &review.GeneralReviewRequestParam{
			ReviewEntityID:   messageID,
			AppID:            doubaoAppID,        // AppID_Doubao，和AppID_Cici区分开
			ReviewScenarioID: filenameScenarioID, // 文审
		},
		BizJSONParam: &strFilenameParam,
	}, calloption.WithReqRespLogsInfo(), calloption.WithRPCTimeout(time.Millisecond*5000))
	if err != nil {
		logs.CtxError(ctx, "review filename failed, err: %v", err)
		return review.CheckResultEnum_Other, errors.Errorf("review text failed, err: %v", err)
	}

	return textResp.GetCheckResult_(), nil
}

func (s *Service) ReportReview(ctx context.Context, opt *service.ReviewOption) (review.CheckResultEnum, error) {
	param := &entity.BizParam{
		Source:  codeAssistSource,
		Text:    opt.Content,
		BotID:   doubaoBotID,
		BotType: strconv.Itoa(int(review.BotType_Coco)),
	}

	marshalReportParam, err := json.Marshal(param)
	if err != nil {
		logs.CtxError(ctx, "marshal report failed, err: %v", err)
		return review.CheckResultEnum_Other, errors.Errorf("marshal report failed, err: %v", err)
	}
	strReportParam := string(marshalReportParam)

	// 调用审核服务（AsyncReview不是异步服务，此处为调用下游取名有点问题）
	messageID, err := strconv.ParseInt(opt.MessageID, 10, 64)
	if err != nil {
		logs.V1.CtxError(ctx, "invalid message id: %s", opt.MessageID)
		return review.CheckResultEnum_Other, err
	}

	textResp, err := ocean_cloud_review.RawCall.SyncReview(ctx, &review.SyncReviewRequest{
		GeneralReviewParam: &review.GeneralReviewRequestParam{
			ReviewEntityID:   messageID,
			AppID:            doubaoAppID,      // AppID_Doubao，和AppID_Cici区分开
			ReviewScenarioID: reportScenarioID, // 报告审核
		},
		BizJSONParam: &strReportParam,
	}, calloption.WithReqRespLogsInfo(), calloption.WithRPCTimeout(time.Millisecond*5000))
	if err != nil {
		logs.CtxError(ctx, "review report failed, err: %v", err)
		return review.CheckResultEnum_Other, errors.Errorf("review text failed, err: %v", err)
	}

	return textResp.GetCheckResult_(), nil
}

// slidingSplit 按照1000长度，重叠100长度，切分文本
const (
	slidingWindowSize    = 1000
	slidingWindowOverlap = 100
)

func slidingSplit(text string) []string {
	b := []byte(text)
	l := len(b)
	if l == 0 || slidingWindowSize >= l {
		return []string{text}
	}

	step := slidingWindowSize - slidingWindowOverlap
	result := make([]string, 0)
	i := 0
	for ; i < l-slidingWindowSize+1; i += step {
		w := b[i : i+slidingWindowSize]
		result = append(result, string(w))
	}
	if i < l {
		result = append(result, string(b[i:l]))
	}

	return result
}
