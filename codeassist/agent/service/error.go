package service

import "github.com/pkg/errors"

var (
	ErrAgentFinished         = errors.New("agent finished")
	ErrSamanthaInternalError = errors.New("samantha internal error")
	ErrReviewNotPass         = errors.New("review not pass")
	ErrReviewInternalError   = errors.New("review internal error")
	ErrTaskNotFound          = errors.New("task not found")
	ToolCallNotReadyError    = errors.New("agent run sandbox not ready")
)
