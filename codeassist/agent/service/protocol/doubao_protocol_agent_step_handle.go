package protocol

import (
	"context"
	"time"

	"code.byted.org/flow/alice_protocol/chat_block"
	impb "code.byted.org/flow/alice_protocol/im_pb"
	samanthaevent "code.byted.org/flow/samantha_nova/pkg/client/pb_gen/event"
	"code.byted.org/gopkg/logs/v2"
	"github.com/pkg/errors"

	"code.byted.org/devgpt/kiwis/codeassist/agent/agents"
	"code.byted.org/devgpt/kiwis/codeassist/agent/entity"
	"code.byted.org/devgpt/kiwis/codeassist/agent/service"
)

// handleAgentStepEvent 处理Agent步骤事件
func (p *DoubaoProtocolImpl) handleAgentStepEvent(ctx context.Context, event *entity.AgentEvent, eventCtx *entity.DoubaoEventContext) (*entity.DoubaoEvent, error) {
	if event.AgentStepEvent == nil {
		logs.V1.CtxError(ctx, "[handleAgentStepEvent] agent step event is nil")
		return nil, errors.New("agent step event is nil")
	}

	var blockWrappers []*entity.BlockWrapper

	stepEvent := event.AgentStepEvent
	// 根据步骤状态处理
	switch stepEvent.Status {
	case entity.AgentStepStatusStarted:
		// 存储AgentRunStep
		agentRunStep := &entity.AgentRunStep{
			AgentRunID: eventCtx.AgentRunID,
			Actor:      stepEvent.Actor,
			Round:      stepEvent.Round,
			ActorIndex: stepEvent.ActorIndex,
		}
		agentRunStepID, err := p.MemoryService.SaveAgentRunStep(ctx, &service.SaveAgentRunStepOption{
			AgentRunStep: agentRunStep,
		})
		if err != nil {
			return nil, err
		}

		// 记录当前Step
		lastAgentRunStep := eventCtx.AgentRunStepCtx
		eventCtx.AgentRunStepCtx = &entity.AgentRunStepContext{
			AgentRunStepID:   agentRunStepID,
			TextBlock:        nil,
			ToolBlocks:       map[string]*entity.BlockContext{},
			InvalidInvokeIDs: map[string]struct{}{},
		}

		if eventCtx.Actor == stepEvent.Actor {
			// 说明上一轮总结出现问题，需要重置上一轮总结内容
			if eventCtx.Actor == agents.SummarizerActor {
				if lastAgentRunStep.TextBlock == nil {
					return nil, nil
				}

				message := &samanthaevent.Message{
					ContentType: int32(impb.ContentType_CONTENT_TYPE_BLOCK_TEXT),
					Reset_:      true,
					Id:          lastAgentRunStep.TextBlock.BlockID,
					Deleted:     true,
				}
				blockWrapper := &entity.BlockWrapper{
					Block: message,
				}
				p.fillingEmptyBlock(ctx, blockWrapper)
				return &entity.DoubaoEvent{
					BlockWrappers: []*entity.BlockWrapper{blockWrapper},
					TaskStage:     eventCtx.TaskStage,
				}, nil
			}
			return nil, nil
		}

		// 暂存当前stage，后面stage会切换
		taskStage := eventCtx.TaskStage

		// 通过判断Actor切换来确定研究卡片状态
		if stepEvent.Actor == agents.AnalyzerActor {
			// 此时不应该存在卡片ID
			if eventCtx.CardBlockID != "" {
				logs.V1.CtxError(ctx, "[handleAgentStepEvent] already has card block: %s", eventCtx.CardBlockID)
				return nil, errors.New("already has card block")
			}

			eventCtx.CardBlockID = generateBlockID()
			eventCtx.CardBeginTimestamp = time.Now().Unix()

			blockWrapper, err := p.wrapResearchProcessCard(ctx, eventCtx, chat_block.Status_STATUS_PROCESSING)
			if err != nil {
				return nil, err
			}

			blockWrappers = append(blockWrappers, blockWrapper)

			// 更新Task状态为异步
			err = p.TaskDAO.UpdateStageByID(ctx, eventCtx.TaskID, entity.TaskStageAsync)
			if err != nil {
				return nil, err
			}
			eventCtx.TaskStage = entity.TaskStageAsync
		} else if stepEvent.Actor == agents.SummarizerActor {
			if !eventCtx.CardHasError {
				blockWrapper, err := p.wrapResearchProcessCard(ctx, eventCtx, chat_block.Status_STATUS_FINISH)
				if err != nil {
					return nil, err
				}

				blockWrappers = append(blockWrappers, blockWrapper)
			} else {
				blockWrapper, err := p.wrapResearchProcessCard(ctx, eventCtx, chat_block.Status_STATUS_FAIL)
				if err != nil {
					return nil, err
				}

				blockWrappers = append(blockWrappers, blockWrapper)
			}
		}

		eventCtx.Actor = stepEvent.Actor

		return &entity.DoubaoEvent{
			BlockWrappers: blockWrappers,
			TaskStage:     taskStage,
		}, nil

	case entity.AgentStepStatusCompleted:
		// 持久化step状态
		err := p.MemoryService.UpdateAgentRunStep(ctx, &service.UpdateAgentRunStepOption{
			AgentRunStepID: eventCtx.AgentRunStepCtx.AgentRunStepID,
			Status:         entity.AgentRunStepStatusCompleted,
		})
		if err != nil {
			return nil, err
		}

		// 快照context
		err = p.CheckPointContext(ctx, eventCtx)
		if err != nil {
			return nil, err
		}

		// 结束上一step所有Block
		finishBlockWrappers, err := p.wrapFinishStep(ctx, eventCtx)
		if err != nil {
			return nil, err
		}
		if len(finishBlockWrappers) > 0 {
			blockWrappers = append(blockWrappers, finishBlockWrappers...)
		}

		// agent在analyze阶段报错了，记录卡片状态为失败
		if stepEvent.Actor == agents.AnalyzerActor && stepEvent.ErrorMessage != "" {
			eventCtx.CardHasError = true
		}

		return &entity.DoubaoEvent{
			BlockWrappers: blockWrappers,
			TaskStage:     eventCtx.TaskStage,
		}, nil

	default:
		// 处理其他状态
		logs.V1.CtxError(ctx, "Unhandled AgentStepEvent status: %v", event.AgentStepEvent.Status)
		return nil, errors.Errorf("Unhandled AgentStepEvent status: %v", event.AgentStepEvent.Status)
	}
}
