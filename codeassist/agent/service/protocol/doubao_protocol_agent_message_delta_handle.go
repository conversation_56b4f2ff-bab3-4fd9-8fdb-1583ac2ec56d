package protocol

import (
	"context"

	"code.byted.org/flow/alice_protocol/chat_block"
	impb "code.byted.org/flow/alice_protocol/im_pb"
	samanthaevent "code.byted.org/flow/samantha_nova/pkg/client/pb_gen/event"
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/overpass/ocean_cloud_review/kitex_gen/ocean/cloud/review"
	"github.com/pkg/errors"
	"github.com/samber/lo"

	"code.byted.org/devgpt/kiwis/codeassist/agent/agents"
	"code.byted.org/devgpt/kiwis/codeassist/agent/entity"
	"code.byted.org/devgpt/kiwis/codeassist/agent/service"
	"code.byted.org/devgpt/kiwis/lib/slidingwindow"
)

// handleAgentMessageDeltaEvent 处理Agent消息增量事件
func (p *DoubaoProtocolImpl) handleAgentMessageDeltaEvent(ctx context.Context, event *entity.AgentEvent, eventCtx *entity.DoubaoEventContext) (*entity.DoubaoEvent, error) {
	if event.AgentMessageDeltaEvent == nil {
		logs.V1.CtxError(ctx, "[handleAgentMessageDeltaEvent] agent message delta event is nil")
		return nil, errors.New("agent message delta event is nil")
	}
	if eventCtx.AgentRunStepCtx == nil {
		logs.V1.CtxError(ctx, "[handleAgentMessageDeltaEvent] invalid cur agent run step id %s", eventCtx.TaskID)
		return nil, errors.New("invalid cur agent run step")
	}

	var blockWrappers []*entity.BlockWrapper
	agentMessageDeltaEvent := event.AgentMessageDeltaEvent
	// 根据DeltaType创建不同类型的Block
	switch agentMessageDeltaEvent.Type {
	case entity.AgentDeltaTypeThink, entity.AgentDeltaTypeInfo:
		if agentMessageDeltaEvent.Actor == agents.AnalyzerActor && agentMessageDeltaEvent.Type == entity.AgentDeltaTypeInfo {
			// think更详细，info和think阶段会有信息重叠，所以把 Info 直接丢弃
			return nil, nil
		}

		// 需要创建TextBlock
		if eventCtx.AgentRunStepCtx.TextBlock == nil {
			eventCtx.AgentRunStepCtx.TextBlock = &entity.BlockContext{
				BlockType:     entity.BlockTypeText,
				BlockID:       generateBlockID(),
				SlidingWindow: slidingwindow.NewSlidingWindow(128, 64),
				Finished:      false,
			}
		}

		if eventCtx.TaskStage == entity.TaskStageAsync {
			content, ok := eventCtx.AgentRunStepCtx.TextBlock.SlidingWindow.AddString(agentMessageDeltaEvent.Content)
			if ok {
				reviewResult, err := p.ReviewService.TextReview(ctx, &service.ReviewOption{
					UserID:    eventCtx.UserID,
					MessageID: eventCtx.MessageID,
					Content:   content,
				})
				if err != nil {
					logs.V1.CtxError(ctx, "[handleAgentMessageDeltaEvent] text review failed: %v", err)
				}
				if reviewResult == review.CheckResultEnum_UnPass {
					logs.V1.CtxError(ctx, "[handleAgentMessageDeltaEvent] text review unpass")
					return nil, service.ErrReviewNotPass
				}
			}
		}

		message := &samanthaevent.Message{
			ContentType: int32(impb.ContentType_CONTENT_TYPE_BLOCK_TEXT),
			Id:          eventCtx.AgentRunStepCtx.TextBlock.BlockID,
		}
		if agentMessageDeltaEvent.Actor == agents.AnalyzerActor {
			message.Pid = eventCtx.CardBlockID
			eventCtx.AgentRunStepCtx.TextBlock.ParentBlockID = eventCtx.CardBlockID
		}

		textBlock := &chat_block.TextBlock{
			Text: agentMessageDeltaEvent.Content,
		}

		blockWrappers = append(blockWrappers, &entity.BlockWrapper{
			Block: message,
			BlockContent: &entity.BlockContent{
				TextBlock: textBlock,
			},
		})

		return &entity.DoubaoEvent{
			BlockWrappers: blockWrappers,
			TaskStage:     eventCtx.TaskStage,
		}, nil

	case entity.AgentDeltaTypeFunction:
		toolID := agentMessageDeltaEvent.MetaData.InvokeID
		toolName := agentMessageDeltaEvent.MetaData.InvokeName
		toolParam := agentMessageDeltaEvent.MetaData.ParamName

		if toolName == "" {
			logs.V1.CtxWarn(ctx, "[handleAgentMessageDeltaEvent] no tool name")
			return nil, nil
		}

		blockType := getToolBlockType(toolName)
		if blockType == entity.BlockTypeUndefined {
			logs.V1.CtxError(ctx, "[handleAgentMessageDeltaEvent] invalid tool name %s", toolName)
			eventCtx.AgentRunStepCtx.InvalidInvokeIDs[toolID] = struct{}{}
			return nil, nil
		}

		if !needRendering(toolName, toolParam) {
			return &entity.DoubaoEvent{
				BlockWrappers: blockWrappers,
				TaskStage:     eventCtx.TaskStage,
			}, nil
		}

		toolBlock, blockExist := eventCtx.AgentRunStepCtx.ToolBlocks[toolID]
		if !blockExist {
			toolBlock = &entity.BlockContext{
				BlockType:     blockType,
				ParentBlockID: eventCtx.CardBlockID,
				BlockID:       generateBlockID(),
				SlidingWindow: slidingwindow.NewSlidingWindow(128, 64),
				Finished:      false,
			}
			eventCtx.AgentRunStepCtx.ToolBlocks[toolID] = toolBlock
			logs.V1.CtxInfo(ctx, "[handleAgentMessageDeltaEvent] add tool: %s with id: %s", toolName, toolID)
		}

		content, ok := toolBlock.SlidingWindow.AddString(agentMessageDeltaEvent.Content)
		if ok {
			reviewResult, err := p.ReviewService.TextReview(ctx, &service.ReviewOption{
				UserID:    eventCtx.UserID,
				MessageID: eventCtx.MessageID,
				Content:   content,
			})
			if err != nil {
				logs.V1.CtxError(ctx, "[handleAgentMessageDeltaEvent] text review failed: %v", err)
			}
			if reviewResult == review.CheckResultEnum_UnPass {
				logs.V1.CtxError(ctx, "[handleAgentMessageDeltaEvent] text review unpass")
				return nil, service.ErrReviewNotPass
			}
		}

		switch blockType {
		case entity.BlockTypeDataAnalysis:
			blockWrapper, err := p.wrapDataAnalysisToolDeltaEvent(ctx, eventCtx, toolBlock, toolParam, agentMessageDeltaEvent.Content)
			if err != nil {
				return nil, err
			}
			blockWrappers = append(blockWrappers, blockWrapper)
		case entity.BlockTypeFileCreate:
			blockWrapper, err := p.wrapFileCreateToolDeltaEvent(ctx, eventCtx, toolBlock, toolParam, agentMessageDeltaEvent.Content)
			if err != nil {
				return nil, err
			}
			blockWrappers = append(blockWrappers, blockWrapper)
		case entity.BlockTypeFileEdit:
			blockWrapper, err := p.wrapFileEditToolDeltaEvent(ctx, eventCtx, toolBlock, toolParam, agentMessageDeltaEvent.Content)
			if err != nil {
				return nil, err
			}
			blockWrappers = append(blockWrappers, blockWrapper)
		case entity.BlockTypeFileView:
			blockWrapper, err := p.wrapFileViewToolDeltaEvent(ctx, eventCtx, toolBlock, toolParam, agentMessageDeltaEvent.Content)
			if err != nil {
				return nil, err
			}
			blockWrappers = append(blockWrappers, blockWrapper)
		case entity.BlockTypeFileList:
			blockWrapper, err := p.wrapListToolDeltaEvent(ctx, eventCtx, toolBlock, toolParam, agentMessageDeltaEvent.Content)
			if err != nil {
				return nil, err
			}
			blockWrappers = append(blockWrappers, blockWrapper)
		default:
			logs.V1.CtxError(ctx, "[handleAgentMessageDeltaEvent] unhandled block type %s", blockType)
			return nil, errors.New("unhandled block type")
		}
		return &entity.DoubaoEvent{
			BlockWrappers: blockWrappers,
			TaskStage:     eventCtx.TaskStage,
		}, nil

	default:
		// 处理其他状态
		logs.V1.CtxError(ctx, "Unhandled AgentMessageDelta type: %v", event.AgentMessageDeltaEvent.Type)
		return nil, errors.New("Unhandled AgentMessageDelta type")
	}
}

func (p *DoubaoProtocolImpl) wrapDataAnalysisToolDeltaEvent(ctx context.Context, eventCtx *entity.DoubaoEventContext, toolBlock *entity.BlockContext, toolParam string, content string) (*entity.BlockWrapper, error) {
	message := &samanthaevent.Message{
		ContentType: int32(impb.ContentType_CONTENT_TYPE_BLOCK_CODE),
		Id:          toolBlock.BlockID,
		Pid:         eventCtx.CardBlockID,
	}

	switch toolParam {
	case entity.ParamNameDataAnalysisCode:
		codeBlock := &chat_block.CodeBlock{
			Code: content,
		}
		return &entity.BlockWrapper{
			Block: message,
			BlockContent: &entity.BlockContent{
				CodeBlock: codeBlock,
			},
		}, nil
	default:
		logs.V1.CtxError(ctx, "[handleAgentMessageDeltaEvent] invalid tool param: %v", toolParam)
		return nil, errors.New("invalid tool param")
	}
}

func (p *DoubaoProtocolImpl) wrapFileCreateToolDeltaEvent(ctx context.Context, eventCtx *entity.DoubaoEventContext, toolBlock *entity.BlockContext, toolParam string, content string) (*entity.BlockWrapper, error) {
	message := &samanthaevent.Message{
		ContentType: int32(impb.ContentType_CONTENT_TYPE_BLOCK_FILE_OPERATION),
		Id:          toolBlock.BlockID,
		Pid:         eventCtx.CardBlockID,
	}

	switch toolParam {
	case entity.ParamNameCreateFilePath:
		fileOperationBlock := &chat_block.FileOperationBlock{
			OperationType: chat_block.FileOperationType_FileOperationType_Create,
			Path:          content,
		}
		return &entity.BlockWrapper{
			Block: message,
			BlockContent: &entity.BlockContent{
				FileOperationBlock: fileOperationBlock,
			},
		}, nil
	case entity.ParamNameCreateFileContent:
		fileOperationBlock := &chat_block.FileOperationBlock{
			OperationType: chat_block.FileOperationType_FileOperationType_Create,
			Content:       content,
		}
		return &entity.BlockWrapper{
			Block: message,
			BlockContent: &entity.BlockContent{
				FileOperationBlock: fileOperationBlock,
			},
		}, nil
	default:
		logs.V1.CtxError(ctx, "[wrapFileCreateToolDeltaEvent] invalid tool param: %v", toolParam)
		return nil, errors.New("invalid tool param")
	}
}

func (p *DoubaoProtocolImpl) wrapFileEditToolDeltaEvent(ctx context.Context, eventCtx *entity.DoubaoEventContext, toolBlock *entity.BlockContext, toolParam string, content string) (*entity.BlockWrapper, error) {
	message := &samanthaevent.Message{
		ContentType: int32(impb.ContentType_CONTENT_TYPE_BLOCK_FILE_OPERATION),
		Id:          toolBlock.BlockID,
		Pid:         eventCtx.CardBlockID,
	}

	switch toolParam {
	case entity.ParamNameEditFilePath:
		fileOperationBlock := &chat_block.FileOperationBlock{
			OperationType: chat_block.FileOperationType_FileOperationType_Edit,
			Path:          content,
		}
		return &entity.BlockWrapper{
			Block: message,
			BlockContent: &entity.BlockContent{
				FileOperationBlock: fileOperationBlock,
			},
		}, nil
	default:
		logs.V1.CtxError(ctx, "[wrapFileEditToolDeltaEvent] invalid tool param: %v", toolParam)
		return nil, errors.New("invalid tool param")
	}
}

func (p *DoubaoProtocolImpl) wrapFileViewToolDeltaEvent(ctx context.Context, eventCtx *entity.DoubaoEventContext, toolBlock *entity.BlockContext, toolParam string, content string) (*entity.BlockWrapper, error) {
	message := &samanthaevent.Message{
		ContentType: int32(impb.ContentType_CONTENT_TYPE_BLOCK_FILE_OPERATION),
		Id:          toolBlock.BlockID,
		Pid:         eventCtx.CardBlockID,
	}

	switch toolParam {
	case entity.ParamNameViewFilePath:
		fileOperationBlock := &chat_block.FileOperationBlock{
			OperationType: chat_block.FileOperationType_FileOperationType_View,
			Path:          content,
		}
		return &entity.BlockWrapper{
			Block: message,
			BlockContent: &entity.BlockContent{
				FileOperationBlock: fileOperationBlock,
			},
		}, nil
	default:
		logs.V1.CtxError(ctx, "[wrapFileViewToolDeltaEvent] invalid tool param: %v", toolParam)
		return nil, errors.New("invalid tool param")
	}
}

func (p *DoubaoProtocolImpl) wrapListToolDeltaEvent(ctx context.Context, eventCtx *entity.DoubaoEventContext, toolBlock *entity.BlockContext, toolParam string, content string) (*entity.BlockWrapper, error) {
	message := &samanthaevent.Message{
		ContentType: int32(impb.ContentType_CONTENT_TYPE_BLOCK_FILE_OPERATION),
		Id:          toolBlock.BlockID,
		Pid:         eventCtx.CardBlockID,
	}

	switch toolParam {
	case entity.ParamNameLSPath:
		fileOperationBlock := &chat_block.FileOperationBlock{
			OperationType: chat_block.FileOperationType_FileOperationType_List,
			Path:          content,
		}
		return &entity.BlockWrapper{
			Block: message,
			BlockContent: &entity.BlockContent{
				FileOperationBlock: fileOperationBlock,
			},
		}, nil
	default:
		logs.V1.CtxError(ctx, "[wrapFileViewToolDeltaEvent] invalid tool param: %v", toolParam)
		return nil, errors.New("invalid tool param")
	}
}

func getToolBlockType(toolName entity.InvokeName) entity.BlockType {
	switch toolName {
	case entity.InvokeNameDataAnalysis:
		return entity.BlockTypeDataAnalysis
	case entity.InvokeNameViewFile:
		return entity.BlockTypeFileView
	case entity.InvokeNameCreateFile:
		return entity.BlockTypeFileCreate
	case entity.InvokeNameEditFile:
		return entity.BlockTypeFileEdit
	case entity.InvokeNameLS:
		return entity.BlockTypeFileList
	}
	return entity.BlockTypeUndefined
}

var (
	needRenderingDataAnalysisParams = []string{entity.ParamNameDataAnalysisCode}
	needRenderingViewFileParams     = []string{entity.ParamNameViewFilePath}
	needRenderingCreateFileParams   = []string{entity.ParamNameCreateFilePath, entity.ParamNameCreateFileContent}
	needRenderingEditFileParams     = []string{entity.ParamNameEditFilePath}
	needRenderingLSParams           = []string{entity.ParamNameLSPath}
)

func needRendering(toolName entity.InvokeName, toolParam string) bool {
	switch toolName {
	case entity.InvokeNameDataAnalysis:
		return lo.Contains(needRenderingDataAnalysisParams, toolParam)
	case entity.InvokeNameViewFile:
		return lo.Contains(needRenderingViewFileParams, toolParam)
	case entity.InvokeNameCreateFile:
		return lo.Contains(needRenderingCreateFileParams, toolParam)
	case entity.InvokeNameEditFile:
		return lo.Contains(needRenderingEditFileParams, toolParam)
	case entity.InvokeNameLS:
		return lo.Contains(needRenderingLSParams, toolParam)
	}

	return false
}
