package protocol

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"strconv"
	"testing"

	"code.byted.org/overpass/ocean_cloud_review/kitex_gen/ocean/cloud/review"
	"github.com/alicebob/miniredis"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/require"
	"go.uber.org/goleak"
	"go.uber.org/mock/gomock"

	"code.byted.org/devgpt/kiwis/codeassist/agent/dal"
	dalimpl "code.byted.org/devgpt/kiwis/codeassist/agent/dal/impl"
	"code.byted.org/devgpt/kiwis/codeassist/agent/dal/po"
	"code.byted.org/devgpt/kiwis/codeassist/agent/entity"
	mockservice "code.byted.org/devgpt/kiwis/codeassist/agent/mock/service"
	"code.byted.org/devgpt/kiwis/codeassist/agent/service"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/port/db"
	"code.byted.org/devgpt/kiwis/port/redis"
)

func newTestProtocol(t *testing.T) (*DoubaoProtocolImpl, *MockOption) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	r, err := miniredis.Run()
	require.NoError(t, err)
	mockRedisCli, err := redis.NewBytedRedisClient(config.RedisConfig{
		PSM:  "",
		Addr: r.Addr(),
	})

	dbCli, _ := db.NewMockSQLiteClient(&po.TaskPO{})
	mockTaskDAO := &dalimpl.TaskDAOImpl{DB: dbCli}

	mockArtifactsService := mockservice.NewMockArtifactsService(ctrl)
	mockMemoryService := mockservice.NewMockMemoryService(ctrl)
	mockReviewService := mockservice.NewMockReviewService(ctrl)

	return &DoubaoProtocolImpl{
			RedisClient: mockRedisCli,

			TaskDAO: mockTaskDAO,

			ArtifactsService: mockArtifactsService,
			MemoryService:    mockMemoryService,
			ReviewService:    mockReviewService,
		}, &MockOption{
			MockTaskDAO: mockTaskDAO,

			MockArtifactsService: mockArtifactsService,
			MockMemoryService:    mockMemoryService,
			MockReviewService:    mockReviewService,
		}
}

type MockOption struct {
	MockTaskDAO dal.TaskDAO

	MockArtifactsService *mockservice.MockArtifactsService
	MockMemoryService    *mockservice.MockMemoryService
	MockReviewService    *mockservice.MockReviewService
}

type MockData struct {
	Events []*entity.AgentEvent `json:"events"`
}

func TestTransfer(t *testing.T) {
	ctx := context.Background()

	var taskID string
	userID := int64(9527)
	conversationID := "123"
	messageID := "123"

	tests := []struct {
		name      string
		mockFunc  func(ctx context.Context, mockOption *MockOption)
		checkFunc func(t *testing.T, protocol *DoubaoProtocolImpl)
	}{
		{
			name: "transfer data",
			mockFunc: func(ctx context.Context, mockOption *MockOption) {
				taskID, _ = mockOption.MockTaskDAO.Create(ctx, &entity.Task{
					UserID:         strconv.FormatInt(userID, 10),
					ConversationID: conversationID,
					MessageID:      messageID,
					Upstream:       entity.TaskUpstreamDoubao,
					Status:         entity.TaskStatusRunning,
					Stage:          entity.TaskStageBegin,
				})

				mockOption.MockArtifactsService.EXPECT().CreateDoubaoArtifact(ctx, gomock.Any()).Return(&entity.DoubaoArtifact{}, nil).AnyTimes()
				mockOption.MockArtifactsService.EXPECT().UploadFile(ctx, gomock.Any(), gomock.Any(), gomock.Any()).Return("", nil).AnyTimes()

				mockOption.MockMemoryService.EXPECT().SaveAgentRunStep(ctx, gomock.Any()).Return("", nil).AnyTimes()
				mockOption.MockMemoryService.EXPECT().UpdateAgentRunStep(ctx, gomock.Any()).Return(nil).AnyTimes()
				mockOption.MockMemoryService.EXPECT().AddToolCall(ctx, gomock.Any()).Return(nil).AnyTimes()

				mockOption.MockReviewService.EXPECT().TextReview(ctx, gomock.Any()).Return(review.CheckResultEnum_Pass, nil).AnyTimes()
				mockOption.MockReviewService.EXPECT().FullTextReview(ctx, gomock.Any()).Return(review.CheckResultEnum_Pass, nil).AnyTimes()
				mockOption.MockReviewService.EXPECT().ImageReview(ctx, gomock.Any()).Return(review.CheckResultEnum_Pass, nil).AnyTimes()
				mockOption.MockReviewService.EXPECT().FilenameReview(ctx, gomock.Any()).Return(review.CheckResultEnum_Pass, nil).AnyTimes()
				mockOption.MockReviewService.EXPECT().ReportReview(ctx, gomock.Any()).Return(review.CheckResultEnum_Pass, nil).AnyTimes()
			},
			checkFunc: func(t *testing.T, p *DoubaoProtocolImpl) {
				defer goleak.VerifyNone(t, goleak.IgnoreCurrent())

				// 加载agent event数据
				data, err := os.ReadFile("../../agents/data_analyzer/demo_events.json")
				require.NoError(t, err)

				mockData := &MockData{}
				err = json.Unmarshal(data, &mockData)
				require.NoError(t, err)

				// 创建doubao event context
				doubaoEventContext := &entity.DoubaoEventContext{
					UserID:     userID,
					MessageID:  messageID,
					TaskID:     taskID,
					TaskStage:  entity.TaskStageSync,
					AgentRunID: "456",
					CurEventID: 0,
				}

				// 循环agent event调用transfer
				for _, event := range mockData.Events {
					if event.EventType == entity.AgentEventTypeAgentToolCall && event.AgentToolCallEvent.InvokeName == entity.InvokeNameDataAnalysis {
						outputsBytes, _ := json.Marshal(event.AgentToolCallEvent.Outputs)
						res := &service.DataAnalysisResult{}
						if err := json.Unmarshal(outputsBytes, &res); err != nil {
							panic(err)
						}
						event.AgentToolCallEvent.Outputs = res
					}

					//fmt.Printf("event: %s\n", util.MarshalStruct(event))
					doubaoEvent, err := p.Transfer(ctx, event, doubaoEventContext)
					fmt.Printf("doubaoEvent err: %v res: %s\n", err, doubaoEvent.BuildString())
					if errors.Is(err, service.ErrAgentFinished) {
						continue
					}
					require.NoError(t, err)
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p, mockOptions := newTestProtocol(t)
			tt.mockFunc(ctx, mockOptions)
			tt.checkFunc(t, p)
		})
	}
}
