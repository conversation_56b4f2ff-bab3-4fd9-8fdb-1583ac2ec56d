package protocol

import (
	"context"
	"path"
	"strings"

	"code.byted.org/flow/alice_protocol/chat_block"
	impb "code.byted.org/flow/alice_protocol/im_pb"
	samanthaevent "code.byted.org/flow/samantha_nova/pkg/client/pb_gen/event"
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/overpass/ocean_cloud_review/kitex_gen/ocean/cloud/review"
	"github.com/pkg/errors"

	"code.byted.org/devgpt/kiwis/codeassist/agent/agents"
	"code.byted.org/devgpt/kiwis/codeassist/agent/entity"
	"code.byted.org/devgpt/kiwis/codeassist/agent/service"
)

const maxStreamEventSize = 20 * 1024

// handleAgentMessageEvent 处理Agent消息事件
func (p *DoubaoProtocolImpl) handleAgentMessageEvent(ctx context.Context, event *entity.AgentEvent, eventCtx *entity.DoubaoEventContext) (*entity.DoubaoEvent, error) {
	if event.AgentMessageEvent == nil {
		logs.V1.CtxError(ctx, "[handleAgentMessageEvent] agent message event is nil")
		return nil, errors.New("agent message event is nil")
	}
	if eventCtx.AgentRunStepCtx == nil {
		logs.V1.CtxError(ctx, "[handleAgentMessageEvent] invalid cur agent run step id %s", eventCtx.TaskID)
		return nil, errors.New("invalid cur agent run step")
	}

	messageEvent := event.AgentMessageEvent

	// 更新agent run step的消息
	err := p.MemoryService.UpdateAgentRunStep(ctx, &service.UpdateAgentRunStepOption{
		AgentRunStepID: eventCtx.AgentRunStepCtx.AgentRunStepID,
		RawMessage:     messageEvent.Content,
	})
	if err != nil {
		logs.V1.CtxError(ctx, "[handleAgentMessageEvent] update agent run step err: %v", err)
		return nil, err
	}

	// 处理产物
	if messageEvent.Actor != agents.SummarizerActor {
		return nil, nil
	}
	if len(messageEvent.Attachments) == 0 {
		logs.V1.CtxWarn(ctx, "[handleAgentMessageEvent] agent message event is empty")
		return nil, nil
	}

	var blockWrappers []*entity.BlockWrapper
	needSplitSend := false
	for _, attachment := range messageEvent.Attachments {
		switch getFileExtension(attachment.Name) {
		case "html", "md":
			// artifacts 防止数据包过大，需要分开发送
			needSplitSend = true
			doubaoArtifact, err := p.ArtifactsService.CreateDoubaoArtifact(ctx, &service.CreateDoubaoArtifactOption{
				UserID:         eventCtx.UserID,
				MessageID:      eventCtx.MessageID,
				AnswerID:       eventCtx.AnswerID,
				ConversationID: eventCtx.ConversationID,
				Title:          strings.TrimSuffix(path.Base(attachment.Name), path.Ext(attachment.Name)),
				Type:           getFileExtension(attachment.Name),
			})
			if err != nil {
				logs.V1.CtxError(ctx, "[handleAgentMessageEvent] create artifacts err: %v", err)
				return nil, err
			}
			artifactsBlockWrappers := p.wrapArtifactsMessageEvent(ctx, eventCtx, attachment, doubaoArtifact)
			if artifactsBlockWrappers == nil {
				continue
			}
			blockWrappers = append(blockWrappers, artifactsBlockWrappers...)
		default:
			uri, err := p.ArtifactsService.UploadFile(ctx, eventCtx.UserID, path.Base(attachment.Name), attachment.RawContent)
			if err != nil {
				logs.V1.CtxError(ctx, "[handleAgentMessageEvent] upload file err: %v", err)
				return nil, err
			}
			fileBlockWrappers := p.wrapFileMessageEvent(ctx, eventCtx, attachment, uri)
			if fileBlockWrappers == nil {
				continue
			}
			blockWrappers = append(blockWrappers, fileBlockWrappers...)
		}
	}
	return &entity.DoubaoEvent{
		BlockWrappers: blockWrappers,
		TaskStage:     eventCtx.TaskStage,
		NeedSplitSend: needSplitSend,
	}, nil
}

// wrapArtifactsMessageEvent 固定流程
func (p *DoubaoProtocolImpl) wrapArtifactsMessageEvent(ctx context.Context, eventCtx *entity.DoubaoEventContext, agentMessageAttachment *entity.AgentMessageAttachment, doubaoArtifact *entity.DoubaoArtifact) []*entity.BlockWrapper {
	reviewResult, err := p.ReviewService.FilenameReview(ctx, &service.ReviewOption{
		Content:   agentMessageAttachment.Name,
		MessageID: eventCtx.MessageID,
		UserID:    eventCtx.UserID,
	})
	if err != nil {
		logs.V1.CtxError(ctx, "[wrapArtifactsMessageEvent] file name review failed: %v", err)
	}
	if reviewResult == review.CheckResultEnum_UnPass {
		logs.V1.CtxInfo(ctx, "[wrapArtifactsMessageEvent] artifacts name review failed")
		return nil
	}

	reviewResult, err = p.ReviewService.ReportReview(ctx, &service.ReviewOption{
		Content:   string(agentMessageAttachment.RawContent),
		MessageID: eventCtx.MessageID,
		UserID:    eventCtx.UserID,
	})
	if err != nil {
		logs.V1.CtxError(ctx, "[wrapArtifactsMessageEvent] report review failed: %v", err)
	}
	if reviewResult == review.CheckResultEnum_UnPass {
		logs.V1.CtxInfo(ctx, "[wrapArtifactsMessageEvent] artifacts content review failed")
		return nil
	}

	var blockWrappers []*entity.BlockWrapper

	// 创建一个父artifactBlock
	artifactBlockID := generateBlockID()

	artifactMessage := &samanthaevent.Message{
		ContentType: int32(impb.ContentType_CONTENT_TYPE_BLOCK_ARTIFACT),
		Id:          artifactBlockID,
	}
	artifactBlock := &chat_block.ArtifactBlock{
		ResourceId:        doubaoArtifact.ResourceId,
		ArtifactMetaId:    doubaoArtifact.ArtifactMetaId,
		ArtifactVersionId: doubaoArtifact.ArtifactVersionId,
		ConversationId:    doubaoArtifact.ConversationId,
		Version:           doubaoArtifact.Version,
		ResourceVersion:   doubaoArtifact.ResourceVersion,
		ArtifactTopic:     doubaoArtifact.ArtifactTopic,
		Title:             doubaoArtifact.Title,
		SourceType:        chat_block.ArtifactSourceType_ArtifactSourceType_DouBaoMainConversation,
		ResourceType:      chat_block.ArtifactResourceType_ArtifactResourceType_Code,
		Status:            chat_block.ArtifactStatus_ArtifactStatus_Generated,
	}
	blockWrappers = append(blockWrappers, &entity.BlockWrapper{
		Block: artifactMessage,
		BlockContent: &entity.BlockContent{
			ArtifactBlock: artifactBlock,
		},
	})

	// 创建一个子artifactCodeFileBlock
	artifactCodeFileBlockID := generateBlockID()

	artifactCodeFileMessage := &samanthaevent.Message{
		ContentType: int32(impb.ContentType_CONTENT_TYPE_BLOCK_ARTIFACT_CODE_FILE),
		Id:          artifactCodeFileBlockID,
		Pid:         artifactBlockID,
	}
	artifactCodeFileBlock := &chat_block.ArtifactCodeFileBlock{
		NodeId:   1,
		Path:     conv.StringPtr(path.Base(agentMessageAttachment.Name)),
		Language: conv.StringPtr(getFileExtension(agentMessageAttachment.Name)),
	}
	blockWrappers = append(blockWrappers, &entity.BlockWrapper{
		Block: artifactCodeFileMessage,
		BlockContent: &entity.BlockContent{
			ArtifactCodeFileBlock: artifactCodeFileBlock,
		},
	})

	// 循环创建Text Block
	textBlockID := generateBlockID()
	contextSplit := splitByLen(agentMessageAttachment.RawContent, maxStreamEventSize)
	for index, content := range contextSplit {
		textMessage := &samanthaevent.Message{
			ContentType: int32(impb.ContentType_CONTENT_TYPE_BLOCK_TEXT),
			Id:          textBlockID,
			Pid:         artifactCodeFileBlockID,
			IsFinish:    index == len(contextSplit)-1,
		}
		textBlock := &chat_block.TextBlock{
			Text: content,
		}
		blockWrappers = append(blockWrappers, &entity.BlockWrapper{
			Block: textMessage,
			BlockContent: &entity.BlockContent{
				TextBlock: textBlock,
			},
		})
	}

	return blockWrappers
}

func (p *DoubaoProtocolImpl) wrapFileMessageEvent(ctx context.Context, eventCtx *entity.DoubaoEventContext, agentMessageAttachment *entity.AgentMessageAttachment, URI string) []*entity.BlockWrapper {
	reviewResult, err := p.ReviewService.FilenameReview(ctx, &service.ReviewOption{
		Content:   agentMessageAttachment.Name,
		MessageID: eventCtx.MessageID,
		UserID:    eventCtx.UserID,
	})
	if err != nil {
		logs.V1.CtxError(ctx, "[handleAgentToolCallEvent] image review failed: %v", err)
	}
	if reviewResult == review.CheckResultEnum_UnPass {
		logs.V1.CtxInfo(ctx, "[handleAgentToolCallEvent] file name review failed")
		return nil
	}

	var blockWrappers []*entity.BlockWrapper
	blockID := generateBlockID()
	message := &samanthaevent.Message{
		ContentType: int32(impb.ContentType_CONTENT_TYPE_BLOCK_FILE),
		Id:          blockID,
		IsFinish:    true,
	}
	block := &chat_block.FileBlock{
		Type: getFileExtension(agentMessageAttachment.Name),
		Name: path.Base(agentMessageAttachment.Name),
		Uri:  URI,
	}
	blockWrappers = append(blockWrappers, &entity.BlockWrapper{
		Block: message,
		BlockContent: &entity.BlockContent{
			FileBlock: block,
		},
	})
	return blockWrappers
}

func splitByLen(content []byte, size int) []string {
	runes := []rune(string(content))
	var res []string
	for i := 0; i < len(runes); i += size {
		end := i + size
		if end > len(runes) {
			end = len(runes)
		}
		res = append(res, string(runes[i:end]))
	}
	return res
}

func getFileExtension(filename string) string {
	return strings.TrimPrefix(path.Ext(filename), ".")
}
