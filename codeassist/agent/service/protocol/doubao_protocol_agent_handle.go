package protocol

import (
	"context"

	"code.byted.org/gopkg/logs/v2"
	"github.com/pkg/errors"

	"code.byted.org/devgpt/kiwis/codeassist/agent/entity"
	"code.byted.org/devgpt/kiwis/codeassist/agent/service"
)

// handleAgentProgressEvent 处理Agent进度事件
func (p *DoubaoProtocolImpl) handleAgentProgressEvent(ctx context.Context, event *entity.AgentEvent, eventCtx *entity.DoubaoEventContext) (*entity.DoubaoEvent, error) {
	switch event.AgentProgressEvent.Status {
	case entity.AgentStatusCreated:
		// 不做任何操作，返回空结果
		return nil, nil

	case entity.AgentStatusCompleted:
		// 记录Task状态从Running为Completed
		if err := p.TaskDAO.UpdateStatusByID(ctx, eventCtx.TaskID, entity.TaskStatusRunning, entity.TaskStatusSucceeded); err != nil {
			logs.V1.CtxError(ctx, "[handleAgentProgressEvent] update task status failed. err: %v", err)
		}
		return nil, service.ErrAgentFinished

	case entity.AgentStatusFailed:
		// 记录Task状态从Running为Failed
		if err := p.TaskDAO.UpdateStatusByID(ctx, eventCtx.TaskID, entity.TaskStatusRunning, entity.TaskStatusFailed); err != nil {
			logs.V1.CtxError(ctx, "[handleAgentProgressEvent] update task status failed. err: %v", err)
		}
		return nil, service.ErrAgentFinished

	case entity.AgentStatusToolRequire:
		// 预留状态，当前不实现，直接返回错误
		return nil, errors.New("tool require not impl yet")

	default:
		// 处理其他状态
		logs.V1.CtxError(ctx, "Unhandled AgentProgressEvent status: %v", event.AgentProgressEvent.Status)
		return nil, errors.Errorf("Unhandled AgentProgressEvent status: %v", event.AgentProgressEvent.Status)
	}
}
