package protocol

import (
	"context"
	"errors"

	"code.byted.org/flow/alice_protocol/chat_block"
	impb "code.byted.org/flow/alice_protocol/im_pb"
	samanthaevent "code.byted.org/flow/samantha_nova/pkg/client/pb_gen/event"
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/overpass/ocean_cloud_review/kitex_gen/ocean/cloud/review"
	"github.com/samber/lo"

	"code.byted.org/devgpt/kiwis/codeassist/agent/entity"
	"code.byted.org/devgpt/kiwis/codeassist/agent/service"
)

// handleAgentToolCallEvent 处理Agent工具调用事件
func (p *DoubaoProtocolImpl) handleAgentToolCallEvent(ctx context.Context, event *entity.AgentEvent, eventCtx *entity.DoubaoEventContext) (*entity.DoubaoEvent, error) {
	if event.AgentToolCallEvent == nil {
		logs.V1.CtxError(ctx, "[handleAgentToolCallEvent] agent tool call event is nil")
		return nil, errors.New("agent message event is nil")
	}
	if eventCtx.AgentRunStepCtx == nil {
		logs.V1.CtxError(ctx, "[handleAgentToolCallEvent] invalid cur agent run step id %s", eventCtx.TaskID)
		return nil, errors.New("invalid cur agent run step")
	}

	var blockWrappers []*entity.BlockWrapper
	// 检查textBlock是否已经结束
	textBlockWrapper, err := p.wrapFinishTextBlock(ctx, eventCtx)
	if err != nil {
		return nil, err
	}
	if textBlockWrapper != nil {
		blockWrappers = append(blockWrappers, textBlockWrapper)
	}

	agentToolCallEvent := event.AgentToolCallEvent
	// 更新工具调用
	toolCall := &entity.AgentRunStepToolCall{
		AgentRunID:     eventCtx.AgentRunID,
		AgentRunStepID: eventCtx.AgentRunStepCtx.AgentRunStepID,
		Index:          0,
		Observation:    agentToolCallEvent.OutputsStr,
		ToolName:       string(agentToolCallEvent.InvokeName),
		Status:         lo.Ternary(agentToolCallEvent.Error == "", "succeed", "failed"),
	}
	err = p.MemoryService.AddToolCall(ctx, toolCall)
	if err != nil {
		return nil, err
	}

	toolID := agentToolCallEvent.InvokeID

	if _, ok := eventCtx.AgentRunStepCtx.InvalidInvokeIDs[toolID]; ok {
		logs.V1.CtxInfo(ctx, "[handleAgentToolCallEvent] drop invalid tool id: %s", toolID)
		return &entity.DoubaoEvent{
			BlockWrappers: blockWrappers,
			TaskStage:     eventCtx.TaskStage,
		}, nil
	}

	toolBlock, blockExist := eventCtx.AgentRunStepCtx.ToolBlocks[toolID]
	if !blockExist {
		logs.V1.CtxError(ctx, "[handleAgentToolCallEvent] agent run step id %s not exist", toolID)
		return nil, errors.New("agent run step id not exist")
	}

	toolBlock.Finished = true
	switch toolBlock.BlockType {
	case entity.BlockTypeDataAnalysis:
		message := &samanthaevent.Message{
			ContentType: int32(impb.ContentType_CONTENT_TYPE_BLOCK_CODE),
			Id:          toolBlock.BlockID,
			Pid:         eventCtx.CardBlockID,
			IsFinish:    true,
		}

		dataAnalysisResult, ok := agentToolCallEvent.Outputs.(*service.DataAnalysisResult)
		if !ok {
			logs.V1.CtxError(ctx, "[handleAgentToolCallEvent] invalid data analysis result")
			return nil, errors.New("invalid data analysis result")
		}
		var stdOutList []*chat_block.CodeStdOutItem
		for _, item := range dataAnalysisResult.ItemList {
			stdOutType := getStdOutType(item.ContentType)
			switch stdOutType {
			case chat_block.CodeStdOutType_CodeStdOutType_Text:
				reviewResult, err := p.ReviewService.FullTextReview(ctx, &service.ReviewOption{
					UserID:    eventCtx.UserID,
					MessageID: eventCtx.MessageID,
					Content:   item.Content,
				})
				if err != nil {
					logs.V1.CtxError(ctx, "[handleAgentToolCallEvent] text review failed: %v", err)
				}
				if reviewResult == review.CheckResultEnum_UnPass {
					return nil, service.ErrReviewNotPass
				}
			case chat_block.CodeStdOutType_CodeStdOutType_Image:
				reviewResult, err := p.ReviewService.ImageReview(ctx, &service.ReviewOption{
					UserID:    eventCtx.UserID,
					MessageID: eventCtx.MessageID,
					Content:   item.Content,
				})
				if err != nil {
					logs.V1.CtxError(ctx, "[handleAgentToolCallEvent] image review failed: %v", err)
				}
				if reviewResult == review.CheckResultEnum_UnPass {
					return nil, service.ErrReviewNotPass
				}
			default:
				logs.V1.CtxError(ctx, "[handleAgentToolCallEvent] invalid std out type: %v", stdOutType)
			}

			stdOutList = append(stdOutList, &chat_block.CodeStdOutItem{
				Type:    stdOutType,
				Content: item.Content,
			})
		}

		codeBlock := &chat_block.CodeBlock{
			StdOutList: stdOutList,
		}
		blockWrapper := &entity.BlockWrapper{
			Block: message,
			BlockContent: &entity.BlockContent{
				CodeBlock: codeBlock,
			},
		}
		blockWrappers = append(blockWrappers, blockWrapper)
		return &entity.DoubaoEvent{
			BlockWrappers: blockWrappers,
			TaskStage:     eventCtx.TaskStage,
		}, nil

	case entity.BlockTypeFileCreate, entity.BlockTypeFileEdit, entity.BlockTypeFileList:
		// 通知状态为Finish
		message := &samanthaevent.Message{
			ContentType: int32(impb.ContentType_CONTENT_TYPE_BLOCK_FILE_OPERATION),
			Id:          toolBlock.BlockID,
			Pid:         eventCtx.CardBlockID,
			IsFinish:    true,
		}
		blockWrapper := &entity.BlockWrapper{
			Block: message,
		}
		if toolBlock.BlockType == entity.BlockTypeFileCreate {
			blockWrapper.BlockContent = &entity.BlockContent{
				FileOperationBlock: &chat_block.FileOperationBlock{
					OperationType: chat_block.FileOperationType_FileOperationType_Create,
				},
			}
		} else if toolBlock.BlockType == entity.BlockTypeFileEdit {
			blockWrapper.BlockContent = &entity.BlockContent{
				FileOperationBlock: &chat_block.FileOperationBlock{
					OperationType: chat_block.FileOperationType_FileOperationType_Edit,
				},
			}
		} else if toolBlock.BlockType == entity.BlockTypeFileList {
			blockWrapper.BlockContent = &entity.BlockContent{
				FileOperationBlock: &chat_block.FileOperationBlock{
					OperationType: chat_block.FileOperationType_FileOperationType_List,
				},
			}
		}
		blockWrappers = append(blockWrappers, blockWrapper)
		return &entity.DoubaoEvent{
			BlockWrappers: blockWrappers,
			TaskStage:     eventCtx.TaskStage,
		}, nil

	case entity.BlockTypeFileView:
		fileViewResult, ok := agentToolCallEvent.Outputs.(*service.ViewFileResult)
		if !ok {
			logs.V1.CtxError(ctx, "[handleAgentToolCallEvent] invalid file view result")
			return nil, errors.New("invalid file view result")
		}

		reviewResult, err := p.ReviewService.FullTextReview(ctx, &service.ReviewOption{
			UserID:    eventCtx.UserID,
			MessageID: eventCtx.MessageID,
			Content:   fileViewResult.Display,
		})
		if err != nil {
			logs.V1.CtxError(ctx, "[handleAgentToolCallEvent] text review failed: %v", err)
		}
		if reviewResult == review.CheckResultEnum_UnPass {
			logs.V1.CtxError(ctx, "[handleAgentToolCallEvent] text review unpass")
			return nil, service.ErrReviewNotPass
		}

		message := &samanthaevent.Message{
			ContentType: int32(impb.ContentType_CONTENT_TYPE_BLOCK_FILE_OPERATION),
			Id:          toolBlock.BlockID,
			Pid:         eventCtx.CardBlockID,
			IsFinish:    true,
		}

		fileOperationBlock := &chat_block.FileOperationBlock{
			OperationType: chat_block.FileOperationType_FileOperationType_View,
			Content:       fileViewResult.Display,
		}
		blockWrapper := &entity.BlockWrapper{
			Block: message,
			BlockContent: &entity.BlockContent{
				FileOperationBlock: fileOperationBlock,
			},
		}
		blockWrappers = append(blockWrappers, blockWrapper)
		return &entity.DoubaoEvent{
			BlockWrappers: blockWrappers,
			TaskStage:     eventCtx.TaskStage,
		}, nil

	default:
		logs.V1.CtxError(ctx, "[handleAgentToolCallEvent] invalid cur tool type %s", toolBlock.BlockType)
		return nil, errors.New("invalid cur tool type")
	}
}

func getStdOutType(contentType string) chat_block.CodeStdOutType {
	switch contentType {
	case "stdout", "stderr":
		return chat_block.CodeStdOutType_CodeStdOutType_Text
	case "image":
		return chat_block.CodeStdOutType_CodeStdOutType_Image
	default:
		return chat_block.CodeStdOutType_CodeStdOutType_Unknown
	}
}
