package protocol

import (
	"context"
	"encoding/json"
	"time"

	"code.byted.org/flow/alice_protocol/chat_block"
	impb "code.byted.org/flow/alice_protocol/im_pb"
	samanthaevent "code.byted.org/flow/samantha_nova/pkg/client/pb_gen/event"
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/overpass/flowpc_chat_eventstream/rpc/flowpc_chat_eventstream"
	"code.byted.org/overpass/ocean_cloud_review/kitex_gen/ocean/cloud/review"
	"github.com/google/uuid"
	"github.com/pkg/errors"

	"code.byted.org/devgpt/kiwis/codeassist/agent/agents"
	"code.byted.org/devgpt/kiwis/codeassist/agent/dal"
	"code.byted.org/devgpt/kiwis/codeassist/agent/entity"
	"code.byted.org/devgpt/kiwis/codeassist/agent/service"
	"code.byted.org/devgpt/kiwis/port/redis"
)

var _ service.DoubaoProtocol = &DoubaoProtocolImpl{}

type DoubaoProtocolImpl struct {
	RedisClient redis.Client

	TaskDAO dal.TaskDAO

	ArtifactsService service.ArtifactsService
	MemoryService    service.MemoryService
	ReviewService    service.ReviewService
}

func (p *DoubaoProtocolImpl) CheckPointContext(ctx context.Context, eventCtx *entity.DoubaoEventContext) error {
	err := p.RedisClient.SetJSON(ctx, eventCtx.TaskID, eventCtx, 2*time.Hour)
	if err != nil {
		logs.V1.CtxWarn(ctx, "redis set context %v error: %v", eventCtx, err)
	}
	return err
}

// RestoreContext 从AgentRunStep中恢复DoubaoEventContext
// 这个方法可以在需要恢复上下文时调用，例如在Agent重启时
func (p *DoubaoProtocolImpl) RestoreContext(ctx context.Context, taskID string) (*entity.DoubaoEventContext, error) {
	var eventCtx entity.DoubaoEventContext
	err := p.RedisClient.GetJSON(ctx, taskID, &eventCtx)
	if err != nil {
		logs.V1.CtxWarn(ctx, "redis get context with task id %s error: %v", taskID, err)
		return nil, err
	}
	// 恢复时一定是异步阶段
	eventCtx.TaskStage = entity.TaskStageAsync
	return &eventCtx, nil
}

// Recover 恢复上一次运行过程中的doubaoEventContext，同时将之前发送了一半的Block进行删除
func (p *DoubaoProtocolImpl) Recover(ctx context.Context, taskID string, userID int64) (*entity.DoubaoEventContext, *entity.DoubaoEvent, error) {
	// 恢复Context
	eventCtx, err := p.RestoreContext(ctx, taskID)
	if err != nil {
		logs.V1.CtxWarn(ctx, "restore context %s error: %v", taskID, err)
		return nil, nil, err
	}
	eventCtx.AgentRunStepCtx = nil

	// 重置Block
	// 获取已处理完成的event
	result, err := flowpc_chat_eventstream.GetCurrentShot(ctx, taskID, userID)
	if err != nil {
		return nil, nil, err
	}

	// 发送重置Block事件
	unstabledBlockIDs := map[string]*samanthaevent.Message{}
	for _, e := range result.StreamEventList {
		if e.EventId <= eventCtx.CurEventID {
			continue
		}
		if e.IsEnd {
			break
		}
		var eventData samanthaevent.BlockList
		err = json.Unmarshal([]byte(e.Data), &eventData)
		if err != nil {
			logs.CtxWarn(ctx, "[Recover] unmarshal event data %s failed, error: ", e.Data, err)
			continue
		}

		for _, block := range eventData.Blocks {
			if _, ok := unstabledBlockIDs[block.Id]; ok {
				continue
			}
			unstabledBlockIDs[block.Id] = block
		}
	}

	// 事件包放大一点，避免跳过
	eventCtx.CurEventID = result.LastEventId + 100

	var blockWrappers []*entity.BlockWrapper
	for unstabledBlockID, unstabledBlock := range unstabledBlockIDs {
		message := &samanthaevent.Message{
			ContentType: unstabledBlock.ContentType,
			Id:          unstabledBlockID,
			Reset_:      true,
			Deleted:     true,
		}
		blockWrapper := &entity.BlockWrapper{
			Block: message,
		}
		p.fillingEmptyBlock(ctx, blockWrapper)
		blockWrappers = append(blockWrappers, blockWrapper)
	}

	doubaoEvent := &entity.DoubaoEvent{
		BlockWrappers: blockWrappers,
		TaskStage:     eventCtx.TaskStage,
	}

	return eventCtx, doubaoEvent, nil
}

func (p *DoubaoProtocolImpl) Transfer(ctx context.Context, event *entity.AgentEvent, eventCtx *entity.DoubaoEventContext) (*entity.DoubaoEvent, error) {
	if event == nil {
		logs.V1.CtxWarn(ctx, "event is nil")
		return nil, nil
	}
	//logs.V1.CtxInfo(ctx, "transfer event: %s", util.MarshalStruct(event))

	// 根据不同的事件类型创建不同的DoubaoEvent
	var doubaoEvent *entity.DoubaoEvent
	var err error
	switch event.EventType {
	case entity.AgentEventTypeAgentProgress:
		doubaoEvent, err = p.handleAgentProgressEvent(ctx, event, eventCtx)
	case entity.AgentEventTypeAgentStep:
		doubaoEvent, err = p.handleAgentStepEvent(ctx, event, eventCtx)
	case entity.AgentEventTypeAgentMessageDelta:
		doubaoEvent, err = p.handleAgentMessageDeltaEvent(ctx, event, eventCtx)
	case entity.AgentEventTypeAgentMessage:
		doubaoEvent, err = p.handleAgentMessageEvent(ctx, event, eventCtx)
	case entity.AgentEventTypeAgentToolCall:
		doubaoEvent, err = p.handleAgentToolCallEvent(ctx, event, eventCtx)
	default:
		// 未知事件类型，返回错误
		return nil, errors.Errorf("unknown event type: %v", event.EventType)
	}

	// 增加事件ID
	if doubaoEvent != nil && doubaoEvent.HasEvent() {
		eventCtx.CurEventID += 1
	}

	return doubaoEvent, err
}

func (p *DoubaoProtocolImpl) PackCancelEvent(ctx context.Context, eventCtx *entity.DoubaoEventContext) *entity.DoubaoEvent {
	eventCtx.CurEventID += 1

	// 持久化step状态
	err := p.MemoryService.UpdateAgentRunStep(ctx, &service.UpdateAgentRunStepOption{
		AgentRunStepID: eventCtx.AgentRunStepCtx.AgentRunStepID,
		Status:         entity.AgentRunStepStatusCancelled,
	})
	if err != nil {
		logs.CtxError(ctx, "[PackCancelEvent] update agent run step failed, error: %v", err)
	}

	var blockWrappers []*entity.BlockWrapper

	// 结束上一step所有Block
	finishBlockWrappers, err := p.wrapFinishStep(ctx, eventCtx)
	if err != nil {
		if errors.Is(err, service.ErrReviewNotPass) {
			return p.PackReviewUnpassedEvent(ctx, eventCtx)
		}
		logs.CtxError(ctx, "[PackCancelEvent] finish step failed, error: %v", err)
	}
	if len(finishBlockWrappers) > 0 {
		blockWrappers = append(blockWrappers, finishBlockWrappers...)
	}

	// agent在analyze阶段报错了，将卡片置为取消状态
	if eventCtx.Actor == agents.AnalyzerActor {
		blockWrapper, err := p.wrapResearchProcessCard(ctx, eventCtx, chat_block.Status_STATUS_CANCEL)
		if err != nil {
			logs.V1.CtxError(ctx, "[PackCancelEvent] research process card failed, error: %v", err)
			return &entity.DoubaoEvent{
				BlockWrappers: blockWrappers,
				TaskStage:     eventCtx.TaskStage,
			}
		}

		blockWrappers = append(blockWrappers, blockWrapper)
	}

	return &entity.DoubaoEvent{
		BlockWrappers: blockWrappers,
		TaskStage:     eventCtx.TaskStage,
	}
}

func (p *DoubaoProtocolImpl) PackReviewUnpassedEvent(ctx context.Context, eventCtx *entity.DoubaoEventContext) *entity.DoubaoEvent {
	eventCtx.CurEventID += 1
	if eventCtx.Actor == agents.AnalyzerActor {
		var blockWrappers []*entity.BlockWrapper

		// 删除当前分析卡片
		delMessage := &samanthaevent.Message{
			ContentType: int32(impb.ContentType_CONTENT_TYPE_BLOCK_CODE_ASSIST_PROCESS_BLOCK),
			Id:          eventCtx.CardBlockID,
			Deleted:     true,
		}
		delBlockWrapper := &entity.BlockWrapper{
			Block: delMessage,
		}
		p.fillingEmptyBlock(ctx, delBlockWrapper)
		blockWrappers = append(blockWrappers, delBlockWrapper)

		// 新建一个卡片
		newMessage := &samanthaevent.Message{
			ContentType: int32(impb.ContentType_CONTENT_TYPE_BLOCK_CODE_ASSIST_PROCESS_BLOCK),
			Id:          generateBlockID(),
		}
		newProcessBlock := &chat_block.CodeAssistProcessBlock{
			Title:             "暂时无法生成对应的结果",
			Status:            chat_block.Status_STATUS_FAIL,
			StartTimestamp:    eventCtx.CardBeginTimestamp,
			EstimatedDuration: int64(time.Since(time.Unix(eventCtx.CardBeginTimestamp, 0)).Seconds()),
			Style:             chat_block.Style_STYLE_TITLE,
		}
		newBlockWrapper := &entity.BlockWrapper{
			Block: newMessage,
			BlockContent: &entity.BlockContent{
				CodeAssistProcessBlock: newProcessBlock,
			},
		}
		blockWrappers = append(blockWrappers, newBlockWrapper)
		return &entity.DoubaoEvent{
			BlockWrappers: blockWrappers,
			TaskStage:     eventCtx.TaskStage,
		}
	} else {
		// 删除当前Block
		message := &samanthaevent.Message{
			ContentType: int32(impb.ContentType_CONTENT_TYPE_BLOCK_TEXT),
			Id:          eventCtx.AgentRunStepCtx.TextBlock.BlockID,
			Reset_:      true,
			Deleted:     true,
		}
		blockWrapper := &entity.BlockWrapper{
			Block: message,
		}
		p.fillingEmptyBlock(ctx, blockWrapper)
		return &entity.DoubaoEvent{
			BlockWrappers: []*entity.BlockWrapper{blockWrapper},
			TaskStage:     eventCtx.TaskStage,
		}
	}
}

// 通用函数
func (p *DoubaoProtocolImpl) wrapFinishStep(ctx context.Context, eventCtx *entity.DoubaoEventContext) ([]*entity.BlockWrapper, error) {
	if eventCtx.AgentRunStepCtx == nil {
		logs.V1.CtxError(ctx, "[wrapFinishStep] current agent run step ctx is nil id %s", eventCtx.TaskID)
		return nil, nil
	}

	var blockWrappers []*entity.BlockWrapper
	blockWrapper, err := p.wrapFinishTextBlock(ctx, eventCtx)
	if err != nil {
		return nil, err
	}
	if blockWrapper != nil {
		blockWrappers = append(blockWrappers, blockWrapper)
	}

	if len(eventCtx.AgentRunStepCtx.ToolBlocks) > 0 {
		for toolKey := range eventCtx.AgentRunStepCtx.ToolBlocks {
			blockWrapper, err := p.wrapFinishToolBlock(ctx, eventCtx, toolKey)
			if err != nil {
				return nil, err
			}
			if blockWrapper != nil {
				blockWrappers = append(blockWrappers, blockWrapper)
			}
		}
	}

	return blockWrappers, nil
}

func (p *DoubaoProtocolImpl) wrapFinishTextBlock(ctx context.Context, eventCtx *entity.DoubaoEventContext) (*entity.BlockWrapper, error) {
	if eventCtx.AgentRunStepCtx == nil {
		logs.V1.CtxError(ctx, "[wrapFinishTextBlock] current agent run step ctx is nil id %s", eventCtx.TaskID)
		return nil, nil
	}

	if eventCtx.AgentRunStepCtx.TextBlock == nil {
		// logs.V1.CtxError(ctx, "[wrapFinishTextBlock] current text block ctx is nil %s", eventCtx.TaskID)
		return nil, nil
	}

	if eventCtx.AgentRunStepCtx.TextBlock.Finished {
		return nil, nil
	}

	if eventCtx.TaskStage == entity.TaskStageAsync {
		content := eventCtx.AgentRunStepCtx.TextBlock.SlidingWindow.GetWindowString()
		reviewResult, err := p.ReviewService.TextReview(ctx, &service.ReviewOption{
			UserID:    eventCtx.UserID,
			MessageID: eventCtx.MessageID,
			Content:   content,
		})
		if err != nil {
			logs.V1.CtxError(ctx, "[wrapFinishTextBlock] text review failed: %v", err)
		}
		if reviewResult == review.CheckResultEnum_UnPass {
			logs.V1.CtxError(ctx, "[wrapFinishTextBlock] text review unpass")
			return nil, service.ErrReviewNotPass
		}
	}

	message := &samanthaevent.Message{
		ContentType: int32(impb.ContentType_CONTENT_TYPE_BLOCK_TEXT),
		Id:          eventCtx.AgentRunStepCtx.TextBlock.BlockID,
		Pid:         eventCtx.AgentRunStepCtx.TextBlock.ParentBlockID,
		IsFinish:    true,
	}
	blockWrapper := &entity.BlockWrapper{
		Block: message,
	}
	p.fillingEmptyBlock(ctx, blockWrapper)

	eventCtx.AgentRunStepCtx.TextBlock.Finished = true
	return blockWrapper, nil
}

func (p *DoubaoProtocolImpl) wrapFinishToolBlock(ctx context.Context, eventCtx *entity.DoubaoEventContext, toolID string) (*entity.BlockWrapper, error) {
	if eventCtx.AgentRunStepCtx == nil {
		logs.V1.CtxError(ctx, "[wrapFinishToolBlock] current agent run step ctx is nil id %s", eventCtx.TaskID)
		return nil, nil
	}

	toolBlock, ok := eventCtx.AgentRunStepCtx.ToolBlocks[toolID]
	if !ok {
		logs.V1.CtxError(ctx, "[wrapFinishToolBlock] tool blocks not exist %s", toolID)
		return nil, nil
	}

	if toolBlock.Finished {
		return nil, nil
	}

	content := toolBlock.SlidingWindow.GetWindowString()
	reviewResult, err := p.ReviewService.TextReview(ctx, &service.ReviewOption{
		UserID:    eventCtx.UserID,
		MessageID: eventCtx.MessageID,
		Content:   content,
	})
	if err != nil {
		logs.V1.CtxError(ctx, "[wrapFinishToolBlock] text review failed: %v", err)
	}
	if reviewResult == review.CheckResultEnum_UnPass {
		logs.V1.CtxError(ctx, "[wrapFinishToolBlock] text review unpass")
		return nil, service.ErrReviewNotPass
	}

	toolBlock.Finished = true
	switch toolBlock.BlockType {
	case entity.BlockTypeDataAnalysis:
		message := &samanthaevent.Message{
			ContentType: int32(impb.ContentType_CONTENT_TYPE_BLOCK_CODE),
			Id:          toolBlock.BlockID,
			Pid:         toolBlock.ParentBlockID,
			IsFinish:    true,
		}
		blockWrapper := &entity.BlockWrapper{
			Block: message,
		}
		p.fillingEmptyBlock(ctx, blockWrapper)

		return blockWrapper, nil
	case entity.BlockTypeFileCreate, entity.BlockTypeFileEdit, entity.BlockTypeFileView, entity.BlockTypeFileList:
		message := &samanthaevent.Message{
			ContentType: int32(impb.ContentType_CONTENT_TYPE_BLOCK_FILE_OPERATION),
			Id:          toolBlock.BlockID,
			Pid:         toolBlock.ParentBlockID,
			IsFinish:    true,
		}
		blockWrapper := &entity.BlockWrapper{
			Block: message,
		}
		p.fillingEmptyBlock(ctx, blockWrapper)

		return blockWrapper, nil
	default:
		logs.V1.CtxError(ctx, "[wrapFinishToolBlock] invalid tool block type: %v", toolBlock.BlockType)
		return nil, nil
	}
}

func (p *DoubaoProtocolImpl) wrapResearchProcessCard(ctx context.Context, eventCtx *entity.DoubaoEventContext, cardStatus chat_block.Status) (*entity.BlockWrapper, error) {
	if eventCtx.CardBlockID == "" {
		logs.V1.CtxError(ctx, "[wrapResearchProcessCard] invalid card block id %s", eventCtx.TaskID)
		return nil, errors.New("invalid card block")
	}

	message := &samanthaevent.Message{
		ContentType: int32(impb.ContentType_CONTENT_TYPE_BLOCK_CODE_ASSIST_PROCESS_BLOCK),
		Id:          eventCtx.CardBlockID,
	}
	processBlock := &chat_block.CodeAssistProcessBlock{
		Status:         cardStatus,
		StartTimestamp: eventCtx.CardBeginTimestamp,
	}

	switch cardStatus {
	case chat_block.Status_STATUS_PROCESSING:
		processBlock.Title = "任务执行中"
	case chat_block.Status_STATUS_FAIL:
		message.IsFinish = true
		processBlock.Title = "任务执行失败"
		processBlock.EstimatedDuration = int64(time.Since(time.Unix(eventCtx.CardBeginTimestamp, 0)).Seconds())
	case chat_block.Status_STATUS_CANCEL:
		message.IsFinish = true
		processBlock.Title = "任务已取消"
		processBlock.EstimatedDuration = int64(time.Since(time.Unix(eventCtx.CardBeginTimestamp, 0)).Seconds())
	case chat_block.Status_STATUS_FINISH:
		message.IsFinish = true
		processBlock.Title = "任务已完成"
		processBlock.EstimatedDuration = int64(time.Since(time.Unix(eventCtx.CardBeginTimestamp, 0)).Seconds())
	default:
		logs.V1.CtxError(ctx, "[handleAgentStepEvent] invalid card status %s", cardStatus)
		return nil, errors.New("invalid card status")
	}

	return &entity.BlockWrapper{
		Block: message,
		BlockContent: &entity.BlockContent{
			CodeAssistProcessBlock: processBlock,
		},
	}, nil
}

func (p *DoubaoProtocolImpl) fillingEmptyBlock(ctx context.Context, wrapper *entity.BlockWrapper) {
	if wrapper.Block == nil {
		logs.V1.CtxError(ctx, "[fillingEmptyBlock] nil wrapper block %+v", wrapper)
		return
	}

	switch wrapper.Block.ContentType {
	case int32(impb.ContentType_CONTENT_TYPE_BLOCK_TEXT):
		wrapper.BlockContent = &entity.BlockContent{
			TextBlock: &chat_block.TextBlock{},
		}
	case int32(impb.ContentType_CONTENT_TYPE_BLOCK_RESEARCH_PROCESS_CARD):
		wrapper.BlockContent = &entity.BlockContent{
			ResearchProcessBlock: &chat_block.ResearchProcessBlock{},
		}
	case int32(impb.ContentType_CONTENT_TYPE_BLOCK_CODE):
		wrapper.BlockContent = &entity.BlockContent{
			CodeBlock: &chat_block.CodeBlock{},
		}
	case int32(impb.ContentType_CONTENT_TYPE_BLOCK_FILE_OPERATION):
		wrapper.BlockContent = &entity.BlockContent{
			FileOperationBlock: &chat_block.FileOperationBlock{},
		}
	case int32(impb.ContentType_CONTENT_TYPE_BLOCK_FILE):
		wrapper.BlockContent = &entity.BlockContent{
			FileBlock: &chat_block.FileBlock{},
		}
	case int32(impb.ContentType_CONTENT_TYPE_BLOCK_ARTIFACT_CODE_FILE):
		wrapper.BlockContent = &entity.BlockContent{
			ArtifactCodeFileBlock: &chat_block.ArtifactCodeFileBlock{},
		}
	case int32(impb.ContentType_CONTENT_TYPE_BLOCK_CODE_ASSIST_PROCESS_BLOCK):
		wrapper.BlockContent = &entity.BlockContent{
			CodeAssistProcessBlock: &chat_block.CodeAssistProcessBlock{},
		}
	case int32(impb.ContentType_CONTENT_TYPE_BLOCK_ARTIFACT):
		wrapper.BlockContent = &entity.BlockContent{
			ArtifactBlock: &chat_block.ArtifactBlock{},
		}
	default:
		logs.V1.CtxError(ctx, "[fillingEmptyBlock] invalid content type: %v", wrapper.Block.ContentType)
		return
	}
}

// generateBlockID 生成唯一的BlockID
func generateBlockID() string {
	return uuid.NewString()
}
