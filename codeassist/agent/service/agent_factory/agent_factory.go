package agent_factory

import (
	"context"

	"code.byted.org/gopkg/logs/v2"
	"github.com/pkg/errors"
	"github.com/samber/lo"

	"code.byted.org/devgpt/kiwis/codeassist/agent/agents"
	"code.byted.org/devgpt/kiwis/codeassist/agent/agents/data_analyzer/actors"
	"code.byted.org/devgpt/kiwis/codeassist/agent/service"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/tcc"
	"code.byted.org/devgpt/kiwis/lib/util"
)

var _ service.AgentFactoryService = &Service{}

type Service struct {
	AgentsBuildConfig *tcc.GenericConfig[config.CodeassistAgentsBuildConfig]
	AgentPromptConfig *tcc.GenericConfig[config.CodeassistAgentPromptConfig]
	AgentToolConfig   *tcc.GenericConfig[config.CodeassistAgentToolConfig]

	agents map[string]agents.Agent
}

func NewService(
	AgentsBuildConfig *tcc.GenericConfig[config.CodeassistAgentsBuildConfig],
	AgentPromptConfig *tcc.GenericConfig[config.CodeassistAgentPromptConfig],
	AgentToolConfig *tcc.GenericConfig[config.CodeassistAgentToolConfig],
) *Service {
	return &Service{
		AgentsBuildConfig: AgentsBuildConfig,
		AgentPromptConfig: AgentPromptConfig,
		AgentToolConfig:   AgentToolConfig,
		agents:            make(map[string]agents.Agent),
	}
}

func (s *Service) Register(ctx context.Context, agent agents.Agent) error {
	if _, ok := s.agents[agent.Name()]; ok {
		return errors.New("agent already exists")
	}
	s.agents[agent.Name()] = agent
	return nil
}

func (s *Service) Get(ctx context.Context, opt service.AgentGetOption) (agents.Agent, *agents.RunContext, error) {
	agent, ok := s.agents[opt.Name]
	if !ok {
		return nil, nil, errors.New("agent not found")
	}

	tccAgentConfig, found := lo.Find(s.AgentsBuildConfig.GetPointer().Agents, func(item *config.CodeassistAgentBuildConfig) bool {
		return item.Name == opt.Name && item.Version == opt.Version
	})
	if !found {
		return nil, nil, errors.New("agent config not found")
	}
	logs.V1.CtxInfo(ctx, "agent config found %s", util.MarshalStruct(tccAgentConfig))

	var actorList []agents.ActorInterface
	for _, actorConfig := range tccAgentConfig.Actors {
		actor, err := s.GetActor(ctx, actorConfig)
		if err != nil {
			return nil, nil, err
		}

		actorList = append(actorList, actor)
	}

	runContext := &agents.RunContext{
		TaskID:         opt.TaskID,
		AgentName:      opt.Name,
		AgentRunID:     opt.AgentRunID,
		UserID:         opt.UserID,
		ConversationID: opt.ConversationID,
		ChunkBatchSize: tccAgentConfig.ChunkBatchSize,
		Actors:         actorList,
	}
	return agent, runContext, nil
}

func (s *Service) GetActor(ctx context.Context, actorConfig *config.CodeassistActorConfig) (agents.ActorInterface, error) {
	systemPromptTemplate, err := s.getPromptTemplate(ctx, actorConfig.PromptKey.SystemPromptKeys)
	if err != nil {
		return nil, err
	}
	contextPromptTemplate, err := s.getPromptTemplate(ctx, actorConfig.PromptKey.ContextPromptKeys)
	if err != nil {
		return nil, err
	}
	userPromptTemplate, err := s.getPromptTemplate(ctx, actorConfig.PromptKey.UserPromptKeys)
	if err != nil {
		return nil, err
	}
	tools, err := s.getToolList(ctx, actorConfig.ToolNameList)
	if err != nil {
		return nil, err
	}

	baseActor := &agents.Actor{
		Name: actorConfig.Name,
		TrimConfig: &agents.TrimConfig{
			MaxTokens:              actorConfig.TrimConfig.MaxTokens,
			MaxHistoryTokens:       actorConfig.TrimConfig.MaxHistoryTokens,
			MaxAgentTrajTokens:     actorConfig.TrimConfig.MaxAgentTrajTokens,
			MaxWorkspaceFilesCount: actorConfig.TrimConfig.MaxWorkspaceFilesCount,
			MaxToolCallLength:      actorConfig.TrimConfig.MaxToolCallLength,
			ToolCallTrimMsg:        actorConfig.TrimConfig.ToolCallTrimMsg,
			AgentTrajTrimMsg:       actorConfig.TrimConfig.AgentTrajTrimMsg,
			HistoryTrimMsg:         actorConfig.TrimConfig.HistoryTrimMsg,
		},
		SystemPromptTemplate:  systemPromptTemplate,
		ContextPromptTemplate: contextPromptTemplate,
		UserPromptTemplate:    userPromptTemplate,
		ToolList:              tools,
		ModelConfig:           &actorConfig.Model,
	}

	switch actorConfig.Name {
	case agents.EchoerActor:
		return &actors.Echoer{
			Actor: *baseActor,
		}, nil
	case agents.AnalyzerActor:
		return &actors.Analyzer{
			Actor: *baseActor,
		}, nil
	case agents.SummarizerActor:
		return &actors.Summarizer{
			Actor: *baseActor,
		}, nil
	default:
		logs.V1.CtxError(ctx, "actor not found with name %s", actorConfig.Name)
		return nil, errors.New("actor not found")
	}
}

// todo: prompt拆开，需要优化
// 先说一下预期哈，这里配置要解决好3个问题，不然agent多了后，扩展不太好
//
// 不同的prompt是隔离的，A promt 发布 不影响 B prompt。 所以所有prompt放在同一个tcc key里有一定风险，同时也不知道哪些是一类的
// agent会有实验，prompt 配置整体需要有版本控制。 所以prompt需要有版本概念，可以控制指向
// prompt会根据一些标签可能做逻辑。所以prompt整体有标签扩展，同时每一个单独块也有标签
//
// 可以把prompt看成一个实体，有版本、有标签、有生命周期
// tool 也需要优化为同样的逻辑
func (s *Service) getPromptTemplate(ctx context.Context, promptTemplateKeys []string) ([]string, error) {
	if len(promptTemplateKeys) == 0 {
		return nil, nil
	}

	var promptTemplates []string
	for _, promptTemplateKey := range promptTemplateKeys {
		promptTemplate, ok := s.AgentPromptConfig.GetPointer().AgentPromptMap[promptTemplateKey]
		if !ok {
			logs.V1.CtxError(ctx, "prompt template not found with key %s", promptTemplateKey)
			return nil, errors.New("prompt template not found")
		}
		promptTemplates = append(promptTemplates, promptTemplate)
	}
	return promptTemplates, nil
}

func (s *Service) getToolList(ctx context.Context, toolNameList []string) ([]*agents.Tool, error) {
	if len(toolNameList) == 0 {
		return nil, nil
	}

	var agentTools []*agents.Tool
	for _, toolName := range toolNameList {
		toolDescription, ok := s.AgentToolConfig.GetPointer().AgentToolMap[toolName]
		if !ok {
			logs.V1.CtxError(ctx, "tool description not found with key %s", toolName)
			return nil, errors.New("tool description not found")
		}
		agentTools = append(agentTools, &agents.Tool{
			Name:        toolName,
			Description: toolDescription,
		})
	}
	return agentTools, nil
}
