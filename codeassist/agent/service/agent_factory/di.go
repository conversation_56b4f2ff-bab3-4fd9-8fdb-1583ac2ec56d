package agent_factory

import (
	"context"

	"code.byted.org/gopkg/logs/v2/log"
	"go.uber.org/fx"

	"code.byted.org/devgpt/kiwis/codeassist/agent/agents"
	"code.byted.org/devgpt/kiwis/codeassist/agent/service"
	"code.byted.org/devgpt/kiwis/lib/di"
)

var Module = fx.Options(
	fx.Provide(NewService),
	fx.Provide(di.Bind(new(Service), new(service.AgentFactoryService))),

	// register agents
	fx.Invoke(func(lc fx.Lifecycle, s service.AgentFactoryService, param agents.Param) error {
		lc.Append(fx.Hook{
			OnStart: func(ctx context.Context) error {
				log.V1.CtxInfo(ctx, "starting register agents...")
				for _, agent := range param.Agents {
					err := s.Register(ctx, agent)
					if err != nil {
						log.V1.CtxFatal(ctx, "failed to register agent %s: %v", agent.Name(), err)
					}
				}
				return nil
			},
		})
		return nil
	}),
)
