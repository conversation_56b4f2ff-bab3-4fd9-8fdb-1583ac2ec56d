package service

import (
	"context"

	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/codeassist"
	chatentity "code.byted.org/devgpt/kiwis/codeassist/chat/entity"
	"code.byted.org/devgpt/kiwis/lib/stream"
)

type TaskService interface {
	NewTask(ctx context.Context, chatContext *chatentity.ChatContext) (string, error)
	RunWithScheduler(ctx context.Context, opt RunTaskOption) error
	Run(ctx context.Context, opt RunTaskOption) error
	RecoverFromSchedulerCallBack(ctx context.Context, req *codeassist.ExecuteJobCallbackReq) error
	InterruptTask(ctx context.Context, messageID string, userID string) error
}

type RunTaskOption struct {
	AgentName       string                                    `json:"agent_name"`
	AgentVersion    string                                    `json:"agent_version"`
	TaskID          string                                    `json:"task_id"`
	UserID          int64                                     `json:"user_id"`
	BizID           int64                                     `json:"biz_id"`
	TenantID        int64                                     `json:"tenant_id"`
	UserMessage     *chatentity.Message                       `json:"user_message"`
	HistoryMessages []*chatentity.Message                     `json:"history_messages"` // 原始历史消息，按时间正序排列
	Send            *stream.SendChannel[chatentity.ChatEvent] `json:"-"`
	NeedRecover     bool                                      `json:"need_recover"`
}
