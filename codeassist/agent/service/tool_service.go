package service

import (
	"context"
)

//go:generate go run go.uber.org/mock/mockgen@latest -destination ../mock/service/tool_service.go -package service code.byted.org/devgpt/kiwis/codeassist/agent/service ToolService
type ToolService interface {
	GetTool(name string) Tool
}

//go:generate go run go.uber.org/mock/mockgen@latest -destination ../mock/service/mock_tool.go -package service code.byted.org/devgpt/kiwis/codeassist/agent/service Tool
type Tool interface {
	Name() string        // 工具名称
	Description() string // 工具描述
	// 返回沙盒未 Ready 情况下特殊标记下
	Run(ctx context.Context, toolContext ToolContext, input map[string]string) (any, error) // 调用工具， error 返回结果只包含调用工具过程的 error
	FormatResult(result any) string                                                         // 格式化为模型需要的格式
	InputSpec() Schema                                                                      // 输入参数的结构
}

type ToolContext struct {
	AgentRunID     string
	AgentName      string
	UserID         int64
	ConversationID string
}

type Schema struct {
	Name        string
	Type        string
	Description string
	Required    bool
	Default     any
	Items       *Schema
	Properties  map[string]Schema
	StreamType  bool
}

type BaseResult struct {
	Output  string
	Error   string
	Display string // 用于展示的内容

}

type CreateFileResult struct {
	BaseResult
}

type DataAnalysisItemResult struct {
	ContentType string
	Content     string
}
type DataAnalysisResult struct {
	ItemList []DataAnalysisItemResult
}

type EditFileResult struct {
	BaseResult
}

type LSResult struct {
	BaseResult
}

type ViewFileResult struct {
	BaseResult
}
