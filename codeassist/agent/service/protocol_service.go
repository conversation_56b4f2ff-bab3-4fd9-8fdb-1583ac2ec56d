package service

import (
	"context"

	"code.byted.org/devgpt/kiwis/codeassist/agent/entity"
)

// DoubaoProtocol 同时负责恢复EventID
type DoubaoProtocol interface {
	Recover(ctx context.Context, taskID string, userID int64) (*entity.DoubaoEventContext, *entity.DoubaoEvent, error)
	Transfer(ctx context.Context, event *entity.AgentEvent, eventContext *entity.DoubaoEventContext) (*entity.DoubaoEvent, error)
	PackCancelEvent(ctx context.Context, eventContext *entity.DoubaoEventContext) *entity.DoubaoEvent
	PackReviewUnpassedEvent(ctx context.Context, eventContext *entity.DoubaoEventContext) *entity.DoubaoEvent
}
