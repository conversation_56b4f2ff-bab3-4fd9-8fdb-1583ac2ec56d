package service

import (
	"context"

	"code.byted.org/devgpt/kiwis/codeassist/agent/agents"
)

// AgentFactoryService 负责Agent的注册，初始化，返回Agent实体
type AgentFactoryService interface {
	// Register 注册一个Agent类型
	// name: Agent类型的名称，用于标识不同类型的Agent
	// creator: 用于创建该类型Agent实例的创建器函数
	// 如果已存在同名的Agent类型，将返回错误
	Register(ctx context.Context, agent agents.Agent) error

	// Get 获取一个Agent实例
	// name: 要获取的Agent类型名称
	// 如果指定名称的Agent类型不存在，将返回错误
	Get(ctx context.Context, opt AgentGetOption) (agents.Agent, *agents.RunContext, error)
}

type AgentGetOption struct {
	UserID         int64
	ConversationID string
	TaskID         string
	AgentRunID     string
	Name           string
	Version        string
}
