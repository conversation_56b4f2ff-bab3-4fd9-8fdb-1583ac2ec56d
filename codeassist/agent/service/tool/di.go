package tool

import (
	"go.uber.org/fx"

	"code.byted.org/devgpt/kiwis/codeassist/agent/service"
	"code.byted.org/devgpt/kiwis/lib/di"
)

var Module = fx.Options(
	fx.Provide(fx.Annotate(NewCreateFileTool, fx.As(new(service.Tool)), fx.ResultTags(`group:"tools"`))),
	fx.Provide(fx.Annotate(NewViewFileTool, fx.As(new(service.Tool)), fx.ResultTags(`group:"tools"`))),
	fx.Provide(fx.Annotate(NewLSTool, fx.As(new(service.Tool)), fx.ResultTags(`group:"tools"`))),
	fx.Provide(fx.Annotate(NewEditFile, fx.As(new(service.Tool)), fx.ResultTags(`group:"tools"`))),
	fx.Provide(fx.Annotate(NewDataAnalysisTool, fx.As(new(service.Tool)), fx.ResultTags(`group:"tools"`))),
	fx.Provide(fx.Annotate(NewToolServiceImpl, fx.ParamTags(`group:"tools"`))),
	fx.Provide(di.Bind(new(ToolServiceImpl), new(service.ToolService))),
)
