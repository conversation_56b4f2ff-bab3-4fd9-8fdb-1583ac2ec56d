package tool

import (
	"context"
	"errors"
	"fmt"

	"code.byted.org/gopkg/logs/v2"

	"code.byted.org/devgpt/kiwis/codeassist/agent/entity"
	"code.byted.org/devgpt/kiwis/codeassist/agent/service"
	sandboxService "code.byted.org/devgpt/kiwis/codeassist/sandbox/service"
	"code.byted.org/devgpt/kiwis/lib/metrics"
)

var _ service.Tool = &EditFileTool{}

type EditFileToolInput struct {
	OldStr string
	NewStr string
	Path   string
}

func mapToEditFileToolInput(input map[string]string) *EditFileToolInput {
	return &EditFileToolInput{
		OldStr: input["old_str"],
		NewStr: input["new_str"],
		Path:   input["path"],
	}
}

type EditFileTool struct {
	agentRunService service.AgentRunService
}

func NewEditFile(agentRunService service.AgentRunService) *EditFileTool {
	return &EditFileTool{
		agentRunService: agentRunService,
	}
}
func (t *EditFileTool) Name() string {
	return "edit_file"

}

func (t *EditFileTool) Description() string {
	return "A tool to edit file."
}

func (t *EditFileTool) Run(ctx context.Context, toolCtx service.ToolContext, input map[string]string) (any, error) {
	var result = &service.EditFileResult{
		BaseResult: service.BaseResult{},
	}

	reportToolCallMetricsFunc := metrics.CodeAssistMetric.ReportAgentToolCallMetrics()
	toolCallStatus := metrics.AgentToolCallSuccess
	defer func() {
		reportToolCallMetricsFunc(toolCtx.AgentName, t.Name(), toolCallStatus)
	}()
	if input == nil {
		toolCallStatus = metrics.AgentToolCallErr
		result.Error = "edit_file tool input params is null"
		result.Display = result.Error
		return result, nil
	}

	inputStruct := mapToEditFileToolInput(input)
	if inputStruct.OldStr == "" {
		toolCallStatus = metrics.AgentToolCallErr
		result.Error = "edit_file tool input params field `old_str` is empty"
		result.Display = result.Error
		return result, nil
	}

	if inputStruct.Path == "" {
		toolCallStatus = metrics.AgentToolCallErr
		result.Error = "edit_file tool input params field `path` is empty"
		result.Display = result.Error
		return result, nil
	}

	agentRun, err := t.agentRunService.GetAgentRun(ctx, toolCtx.AgentRunID)
	if err != nil {
		toolCallStatus = metrics.AgentToolCallErr
		return nil, err
	}

	if agentRun == nil || agentRun.Status < entity.AgentRunStatusRunning {
		toolCallStatus = metrics.AgentToolCallErr
		return nil, service.ToolCallNotReadyError
	}

	logs.V1.CtxInfo(ctx, "edit_file tool run, file path: %s, conversation id: %s", inputStruct.Path, toolCtx.ConversationID)
	resp, err := t.agentRunService.EditFile(ctx, &service.EditFileOpt{
		UserID:         toolCtx.UserID,
		ConversationID: toolCtx.ConversationID,
		OldStr:         inputStruct.OldStr,
		NewStr:         inputStruct.NewStr,
		Path:           inputStruct.Path,
	})

	if err == nil {
		output := "Successfully replaced text."
		if inputStruct.NewStr == "" {
			output += "\nNOTE: You did not provide `new_str`, so the `old_str` was replaced with an empty string, i.e. deleted."
		}
		output += fmt.Sprintf(" New file size: %d bytes.", resp.TotalLines)
		if resp.TotalLines == 0 {
			output += fmt.Sprintf("\nNOTE: The file %s was removed after becoming empty", inputStruct.Path)
		}
		result.Output = output
		result.Display = result.Output
		return result, nil
	}

	logs.V1.CtxWarn(ctx, "edit_file tool failed, err: %+v", err)

	// tool run error
	var sandboxErr *sandboxService.SandboxError
	if errors.As(err, &sandboxErr) {
		toolCallStatus = metrics.AgentToolRunErr
		result.Error = err.Error()
		result.Display = result.Error
		return result, nil
	}

	// tool call 工程上出错
	toolCallStatus = metrics.AgentToolCallErr
	return nil, err
}

func (t *EditFileTool) FormatResult(result any) string {
	if res, ok := result.(*service.EditFileResult); ok {
		return res.Display
	}
	return ""
}

func (t *EditFileTool) InputSpec() service.Schema {
	return service.Schema{
		Name:        "path",
		Type:        "object",
		Description: "The path of the file to edit.",
		Properties: map[string]service.Schema{
			"old_str": {
				Name:        "old_str",
				Type:        "string",
				Description: "`str_replace` 命令的必填参数，用于指定 `path` 文件中待替换的字符串内容",
				Required:    true,
			},
			"new_str": {
				Name:        "new_str",
				Type:        "string",
				Description: "`str_replace` 命令的选填参数，包含要替换的新字符串，如果未提供，则表示删除该字符串",
			},
			"path": {
				Name:        "path",
				Type:        "string",
				Description: "文件或目录的相对路径，例如 `./file.py` 或 `./`。",
				Required:    true,
			},
		},
	}
}
