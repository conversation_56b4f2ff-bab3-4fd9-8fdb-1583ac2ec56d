package tool

import (
	"context"
	"errors"
	"testing"

	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"
	"go.uber.org/mock/gomock"

	"code.byted.org/devgpt/kiwis/codeassist/agent/entity"
	mockservice "code.byted.org/devgpt/kiwis/codeassist/agent/mock/service"
	"code.byted.org/devgpt/kiwis/codeassist/agent/service"
	sandboxService "code.byted.org/devgpt/kiwis/codeassist/sandbox/service"
	"code.byted.org/devgpt/kiwis/lib/metrics"
)

func TestCreateFileTool_Run(t *testing.T) {
	mockey.PatchConvey("Test CreateFileTool Run", t, func() {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockAgentRunService := mockservice.NewMockAgentRunService(ctrl)
		tool := NewCreateFileTool(mockAgentRunService)

		ctx := context.Background()
		toolCtx := service.ToolContext{
			AgentRunID:     "test-agent-run-id",
			UserID:         123,
			ConversationID: "test-conversation-id",
			AgentName:      "data_analysis",
		}

		mockey.PatchConvey("When input is nil", func() {
			mockey.MockGeneric(metrics.CodeAssistMetric.ReportAgentToolCallMetrics).Return(func(agentID string, toolName string, status metrics.AgentToolCallStatus) {
			}).Build()
			result, err := tool.Run(ctx, toolCtx, nil)
			convey.So(err, convey.ShouldBeNil)
			convey.So(result, convey.ShouldNotBeNil)
			createFileResult, ok := result.(*service.CreateFileResult)
			convey.So(ok, convey.ShouldBeTrue)
			convey.So(createFileResult.Error, convey.ShouldEqual, "create_file tool input params is null")
			convey.So(createFileResult.Display, convey.ShouldEqual, "create_file tool input params is null")
		})

		mockey.PatchConvey("When file_text is empty", func() {
			mockey.MockGeneric(metrics.CodeAssistMetric.ReportAgentToolCallMetrics).Return(func(agentID string, toolName string, status metrics.AgentToolCallStatus) {
			}).Build()
			input := map[string]string{
				"file_text": "",
				"path":      "/path/to/file.txt",
			}
			result, err := tool.Run(ctx, toolCtx, input)
			convey.So(err, convey.ShouldBeNil)
			convey.So(result, convey.ShouldNotBeNil)
			createFileResult, ok := result.(*service.CreateFileResult)
			convey.So(ok, convey.ShouldBeTrue)
			convey.So(createFileResult.Error, convey.ShouldEqual, "create_file tool input params field `file_text` is empty")
			convey.So(createFileResult.Display, convey.ShouldEqual, "create_file tool input params field `file_text` is empty")
		})

		mockey.PatchConvey("When path is empty", func() {
			mockey.MockGeneric(metrics.CodeAssistMetric.ReportAgentToolCallMetrics).Return(func(agentID string, toolName string, status metrics.AgentToolCallStatus) {
			}).Build()
			input := map[string]string{
				"file_text": "file content",
				"path":      "",
			}
			result, err := tool.Run(ctx, toolCtx, input)
			convey.So(err, convey.ShouldBeNil)
			convey.So(result, convey.ShouldNotBeNil)
			createFileResult, ok := result.(*service.CreateFileResult)
			convey.So(ok, convey.ShouldBeTrue)
			convey.So(createFileResult.Error, convey.ShouldEqual, "create_file tool input params field `path` is empty")
			convey.So(createFileResult.Display, convey.ShouldEqual, "create_file tool input params field `path` is empty")
		})

		mockey.PatchConvey("When GetAgentRun returns error", func() {
			mockey.MockGeneric(metrics.CodeAssistMetric.ReportAgentToolCallMetrics).Return(func(agentID string, toolName string, status metrics.AgentToolCallStatus) {
			}).Build()
			input := map[string]string{
				"file_text": "file content",
				"path":      "/path/to/file.txt",
			}
			mockAgentRunService.EXPECT().GetAgentRun(ctx, toolCtx.AgentRunID).Return(nil, errors.New("get agent run error"))
			result, err := tool.Run(ctx, toolCtx, input)
			convey.So(err, convey.ShouldNotBeNil)
			convey.So(err.Error(), convey.ShouldEqual, "get agent run error")
			convey.So(result, convey.ShouldBeNil)
		})

		mockey.PatchConvey("When agent run status is not ready", func() {
			mockey.MockGeneric(metrics.CodeAssistMetric.ReportAgentToolCallMetrics).Return(func(agentID string, toolName string, status metrics.AgentToolCallStatus) {
			}).Build()
			input := map[string]string{
				"file_text": "file content",
				"path":      "/path/to/file.txt",
			}
			agentRun := &entity.AgentRun{
				UUID:   toolCtx.AgentRunID,
				Status: entity.AgentRunStatusCreated, // Status is less than AgentRunStatusRunning
			}
			mockAgentRunService.EXPECT().GetAgentRun(ctx, toolCtx.AgentRunID).Return(agentRun, nil)

			result, err := tool.Run(ctx, toolCtx, input)
			convey.So(err, convey.ShouldEqual, service.ToolCallNotReadyError)
			convey.So(result, convey.ShouldBeNil)
		})

		mockey.PatchConvey("When CreateFile returns general error", func() {
			input := map[string]string{
				"file_text": "file content",
				"path":      "/path/to/file.txt",
			}
			agentRun := &entity.AgentRun{
				UUID:   toolCtx.AgentRunID,
				Status: entity.AgentRunStatusRunning,
			}
			mockAgentRunService.EXPECT().GetAgentRun(ctx, toolCtx.AgentRunID).Return(agentRun, nil)
			mockAgentRunService.EXPECT().CreateFile(ctx, &service.CreateFileOpt{
				UserID:         toolCtx.UserID,
				ConversationID: toolCtx.ConversationID,
				Path:           input["path"],
				Content:        []byte(input["file_text"]),
			}).Return(errors.New("create file error"))
			mockey.MockGeneric(metrics.CodeAssistMetric.ReportAgentToolCallMetrics).Return(func(agentRunID, toolName string, status metrics.AgentToolCallStatus) {
				convey.So(status, convey.ShouldEqual, metrics.AgentToolCallErr)
			}).Build()

			result, err := tool.Run(ctx, toolCtx, input)
			convey.So(err, convey.ShouldNotBeNil)
			convey.So(err.Error(), convey.ShouldEqual, "create file error")
			convey.So(result, convey.ShouldBeNil)
		})

		mockey.PatchConvey("When CreateFile returns sandbox error", func() {
			input := map[string]string{
				"file_text": "file content",
				"path":      "/path/to/file.txt",
			}
			agentRun := &entity.AgentRun{
				UUID:   toolCtx.AgentRunID,
				Status: entity.AgentRunStatusRunning,
			}
			sandboxErr := &sandboxService.SandboxError{Message: "sandbox error"}
			mockAgentRunService.EXPECT().GetAgentRun(ctx, toolCtx.AgentRunID).Return(agentRun, nil)
			mockAgentRunService.EXPECT().CreateFile(ctx, &service.CreateFileOpt{
				UserID:         toolCtx.UserID,
				ConversationID: toolCtx.ConversationID,
				Path:           input["path"],
				Content:        []byte(input["file_text"]),
			}).Return(sandboxErr)

			// 使用mockey模拟metrics.CodeAssistMetric.ReportAgentToolCallMetrics
			mockey.MockGeneric(metrics.CodeAssistMetric.ReportAgentToolCallMetrics).Return(func(agentRunID, toolName string, status metrics.AgentToolCallStatus) {
				convey.So(status, convey.ShouldEqual, metrics.AgentToolRunErr)
			}).Build()

			result, err := tool.Run(ctx, toolCtx, input)
			convey.So(err, convey.ShouldBeNil)
			convey.So(result, convey.ShouldNotBeNil)

			createFileResult, ok := result.(*service.CreateFileResult)
			convey.So(ok, convey.ShouldBeTrue)
			convey.So(createFileResult.BaseResult.Error, convey.ShouldEqual, sandboxErr.Error())
			convey.So(createFileResult.BaseResult.Display, convey.ShouldEqual, sandboxErr.Error())
		})

		mockey.PatchConvey("When CreateFile succeeds", func() {
			input := map[string]string{
				"file_text": "file content",
				"path":      "/path/to/file.txt",
			}
			agentRun := &entity.AgentRun{
				UUID:   toolCtx.AgentRunID,
				Status: entity.AgentRunStatusRunning,
			}
			mockAgentRunService.EXPECT().GetAgentRun(ctx, toolCtx.AgentRunID).Return(agentRun, nil)
			mockAgentRunService.EXPECT().CreateFile(ctx, &service.CreateFileOpt{
				UserID:         toolCtx.UserID,
				ConversationID: toolCtx.ConversationID,
				Path:           input["path"],
				Content:        []byte(input["file_text"]),
			}).Return(nil)

			// 使用mockey模拟metrics.CodeAssistMetric.ReportAgentToolCallMetrics
			mockey.MockGeneric(metrics.CodeAssistMetric.ReportAgentToolCallMetrics).Return(func(agentRunID, toolName string, status metrics.AgentToolCallStatus) {
				convey.So(status, convey.ShouldEqual, metrics.AgentToolCallSuccess)
			}).Build()

			result, err := tool.Run(ctx, toolCtx, input)
			convey.So(err, convey.ShouldBeNil)
			convey.So(result, convey.ShouldNotBeNil)

			createFileResult, ok := result.(*service.CreateFileResult)
			convey.So(ok, convey.ShouldBeTrue)
			convey.So(createFileResult.BaseResult.Output, convey.ShouldEqual, "File created successfully")
			convey.So(createFileResult.BaseResult.Display, convey.ShouldEqual, "File created successfully")
		})
	})
}

func TestCreateFileTool_FormatResult(t *testing.T) {
	mockey.PatchConvey("Test CreateFileTool FormatResult", t, func() {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockAgentRunService := mockservice.NewMockAgentRunService(ctrl)
		tool := NewCreateFileTool(mockAgentRunService)

		mockey.PatchConvey("When result is CreateFileResult with output", func() {
			result := &service.CreateFileResult{
				BaseResult: service.BaseResult{
					Output:  "File created successfully",
					Error:   "",
					Display: "File created successfully",
				},
			}

			formatted := tool.FormatResult(result)
			expected := "File created successfully"
			convey.So(formatted, convey.ShouldEqual, expected)
		})

		mockey.PatchConvey("When result is CreateFileResult with error", func() {
			result := &service.CreateFileResult{
				BaseResult: service.BaseResult{
					Output:  "",
					Error:   "create file error",
					Display: "create file error",
				},
			}

			formatted := tool.FormatResult(result)
			expected := "create file error"
			convey.So(formatted, convey.ShouldEqual, expected)
		})

		mockey.PatchConvey("When result is not CreateFileResult", func() {
			result := "not a CreateFileResult"

			formatted := tool.FormatResult(result)
			expected := ""
			convey.So(formatted, convey.ShouldEqual, expected)
		})
	})
}

func TestCreateFileTool_Name(t *testing.T) {
	mockey.PatchConvey("Test CreateFileTool Name", t, func() {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockAgentRunService := mockservice.NewMockAgentRunService(ctrl)
		tool := NewCreateFileTool(mockAgentRunService)

		name := tool.Name()
		convey.So(name, convey.ShouldEqual, "create_file")
	})
}

func TestCreateFileTool_Description(t *testing.T) {
	mockey.PatchConvey("Test CreateFileTool Description", t, func() {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockAgentRunService := mockservice.NewMockAgentRunService(ctrl)
		tool := NewCreateFileTool(mockAgentRunService)

		description := tool.Description()
		convey.So(description, convey.ShouldEqual, "A tool to create a file.")
	})
}

func TestCreateFileTool_InputSpec(t *testing.T) {
	mockey.PatchConvey("Test CreateFileTool InputSpec", t, func() {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockAgentRunService := mockservice.NewMockAgentRunService(ctrl)
		tool := NewCreateFileTool(mockAgentRunService)

		spec := tool.InputSpec()
		convey.So(spec.Name, convey.ShouldEqual, "create file input schema")
		convey.So(spec.Type, convey.ShouldEqual, "Object")
		convey.So(spec.Properties, convey.ShouldNotBeNil)
		convey.So(len(spec.Properties), convey.ShouldEqual, 2)

		convey.So(spec.Properties["file_text"].Name, convey.ShouldEqual, "file_text")
		convey.So(spec.Properties["file_text"].Type, convey.ShouldEqual, "string")
		convey.So(spec.Properties["file_text"].Required, convey.ShouldBeTrue)
		convey.So(spec.Properties["file_text"].StreamType, convey.ShouldBeTrue)

		convey.So(spec.Properties["path"].Name, convey.ShouldEqual, "path")
		convey.So(spec.Properties["path"].Type, convey.ShouldEqual, "string")
		convey.So(spec.Properties["path"].Required, convey.ShouldBeTrue)
	})
}
