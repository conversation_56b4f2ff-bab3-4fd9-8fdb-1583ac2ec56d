package tool

import (
	"context"
	"errors"
	"testing"

	"github.com/bytedance/mockey"
	. "github.com/smartystreets/goconvey/convey"
	"go.uber.org/mock/gomock"

	"code.byted.org/devgpt/kiwis/codeassist/agent/entity"
	mockservice "code.byted.org/devgpt/kiwis/codeassist/agent/mock/service"
	agentservice "code.byted.org/devgpt/kiwis/codeassist/agent/service"
	sandboxservice "code.byted.org/devgpt/kiwis/codeassist/sandbox/service"
	"code.byted.org/devgpt/kiwis/lib/metrics"
)

func TestViewFileTool_Name(t *testing.T) {
	mockey.PatchConvey("Test ViewFileTool Name", t, func() {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockAgentRunService := mockservice.NewMockAgentRunService(ctrl)
		tool := NewViewFileTool(mockAgentRunService)

		So(tool.Name(), ShouldEqual, "view_file")
	})
}

func TestViewFileTool_Description(t *testing.T) {
	mockey.PatchConvey("Test ViewFileTool Description", t, func() {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockAgentRunService := mockservice.NewMockAgentRunService(ctrl)
		tool := NewViewFileTool(mockAgentRunService)

		So(tool.Description(), ShouldEqual, "A tool to view file.")
	})
}

func TestViewFileTool_InputSpec(t *testing.T) {
	mockey.PatchConvey("Test ViewFileTool InputSpec", t, func() {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockAgentRunService := mockservice.NewMockAgentRunService(ctrl)
		tool := NewViewFileTool(mockAgentRunService)

		spec := tool.InputSpec()
		So(spec.Name, ShouldEqual, "view file params schema")
		So(spec.Type, ShouldEqual, "object")
		So(spec.Properties["file_path"].Required, ShouldBeTrue)
		So(spec.Properties["offset"].Default, ShouldEqual, 0)
		So(spec.Properties["limit"].Default, ShouldEqual, 200)
	})
}

func TestViewFileTool_Run(t *testing.T) {
	mockey.PatchConvey("Test ViewFileTool Run", t, func() {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockAgentRunService := mockservice.NewMockAgentRunService(ctrl)
		tool := NewViewFileTool(mockAgentRunService)

		ctx := context.Background()
		toolCtx := agentservice.ToolContext{
			AgentRunID:     "test-agent-run-id",
			UserID:         123,
			ConversationID: "test-conversation-id",
			AgentName:      "data_analysis",
		}

		mockey.PatchConvey("When input is nil", func() {
			// 模拟 metrics.CodeAssistMetric.ReportAgentToolCallMetrics
			mockey.MockGeneric(metrics.CodeAssistMetric.ReportAgentToolCallMetrics).Return(func(agentRunID, toolName string, status metrics.AgentToolCallStatus) {
				So(status, ShouldEqual, metrics.AgentToolCallErr)
				So(agentRunID, ShouldEqual, toolCtx.AgentName)
				So(toolName, ShouldEqual, "view_file")
			}).Build()

			result, err := tool.Run(ctx, toolCtx, nil)

			So(err, ShouldBeNil)
			So(result, ShouldNotBeNil)

			viewFileResult, ok := result.(*agentservice.ViewFileResult)
			So(ok, ShouldBeTrue)
			So(viewFileResult.Error, ShouldContainSubstring, "view_file tool input params is null")
			So(viewFileResult.Display, ShouldEqual, viewFileResult.Error)
		})

		mockey.PatchConvey("When file_path is empty", func() {
			// 模拟 metrics.CodeAssistMetric.ReportAgentToolCallMetrics
			mockey.MockGeneric(metrics.CodeAssistMetric.ReportAgentToolCallMetrics).Return(func(agentRunID, toolName string, status metrics.AgentToolCallStatus) {
				So(status, ShouldEqual, metrics.AgentToolCallErr)
				So(agentRunID, ShouldEqual, toolCtx.AgentName)
				So(toolName, ShouldEqual, "view_file")
			}).Build()

			input := map[string]string{
				"file_path": "",
				"offset":    "0",
				"limit":     "100",
			}

			result, err := tool.Run(ctx, toolCtx, input)

			So(err, ShouldBeNil)
			So(result, ShouldNotBeNil)

			viewFileResult, ok := result.(*agentservice.ViewFileResult)
			So(ok, ShouldBeTrue)
			So(viewFileResult.Error, ShouldContainSubstring, "view_file tool input params field `file_path` is nil")
			So(viewFileResult.Display, ShouldEqual, viewFileResult.Error)
		})

		mockey.PatchConvey("When GetAgentRun returns error", func() {
			// 模拟 metrics.CodeAssistMetric.ReportAgentToolCallMetrics
			mockey.MockGeneric(metrics.CodeAssistMetric.ReportAgentToolCallMetrics).Return(func(agentRunID, toolName string, status metrics.AgentToolCallStatus) {
				So(status, ShouldEqual, metrics.AgentToolCallErr)
				So(agentRunID, ShouldEqual, toolCtx.AgentName)
				So(toolName, ShouldEqual, "view_file")
			}).Build()

			input := map[string]string{
				"file_path": "/path/to/file.txt",
				"offset":    "0",
				"limit":     "100",
			}

			mockAgentRunService.EXPECT().
				GetAgentRun(gomock.Any(), "test-agent-run-id").
				Return(nil, errors.New("agent run not found"))

			result, err := tool.Run(ctx, toolCtx, input)

			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldEqual, "agent run not found")
			So(result, ShouldBeNil)
		})

		mockey.PatchConvey("When agent run status is not ready", func() {
			// 模拟 metrics.CodeAssistMetric.ReportAgentToolCallMetrics
			mockey.MockGeneric(metrics.CodeAssistMetric.ReportAgentToolCallMetrics).Return(func(agentRunID, toolName string, status metrics.AgentToolCallStatus) {
				So(status, ShouldEqual, metrics.AgentToolCallErr)
				So(agentRunID, ShouldEqual, toolCtx.AgentName)
				So(toolName, ShouldEqual, "view_file")
			}).Build()

			input := map[string]string{
				"file_path": "/path/to/file.txt",
				"offset":    "0",
				"limit":     "100",
			}

			agentRun := &entity.AgentRun{
				UUID:   "test-agent-run-id",
				Status: entity.AgentRunStatusCreated, // 状态为 created，未就绪
			}

			mockAgentRunService.EXPECT().
				GetAgentRun(gomock.Any(), "test-agent-run-id").
				Return(agentRun, nil)

			result, err := tool.Run(ctx, toolCtx, input)

			So(err, ShouldNotBeNil)
			So(err, ShouldEqual, agentservice.ToolCallNotReadyError)
			So(result, ShouldBeNil)

		})

		mockey.PatchConvey("When ReadFile returns general error", func() {
			// 模拟 metrics.CodeAssistMetric.ReportAgentToolCallMetrics
			mockey.MockGeneric(metrics.CodeAssistMetric.ReportAgentToolCallMetrics).Return(func(agentRunID, toolName string, status metrics.AgentToolCallStatus) {
				So(status, ShouldEqual, metrics.AgentToolCallErr)
				So(agentRunID, ShouldEqual, toolCtx.AgentName)
				So(toolName, ShouldEqual, "view_file")
			}).Build()

			input := map[string]string{
				"file_path": "/path/to/file.txt",
				"offset":    "10",
				"limit":     "50",
			}

			agentRun := &entity.AgentRun{
				UUID:   "test-agent-run-id",
				Status: entity.AgentRunStatusRunning, // 状态已就绪
			}

			mockAgentRunService.EXPECT().
				GetAgentRun(gomock.Any(), "test-agent-run-id").
				Return(agentRun, nil)

			mockAgentRunService.EXPECT().
				ReadFile(gomock.Any(), &agentservice.ReadFileOpt{
					UserID:         123,
					ConversationID: "test-conversation-id",
					Path:           "/path/to/file.txt",
					Offset:         10,
					Limit:          50,
				}).
				Return(nil, errors.New("file not found"))

			result, err := tool.Run(ctx, toolCtx, input)

			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldEqual, "file not found")
			So(result, ShouldBeNil)
		})

		mockey.PatchConvey("When ReadFile returns sandbox error", func() {
			// 模拟 metrics.CodeAssistMetric.ReportAgentToolCallMetrics
			mockey.MockGeneric(metrics.CodeAssistMetric.ReportAgentToolCallMetrics).Return(func(agentRunID, toolName string, status metrics.AgentToolCallStatus) {
				So(agentRunID, ShouldEqual, toolCtx.AgentName)
				So(toolName, ShouldEqual, "view_file")
				So(status, ShouldEqual, metrics.AgentToolRunErr)
			}).Build()

			input := map[string]string{
				"file_path": "/path/to/file.txt",
				"offset":    "10",
				"limit":     "50",
			}

			agentRun := &entity.AgentRun{
				UUID:   "test-agent-run-id",
				Status: entity.AgentRunStatusRunning,
			}

			mockAgentRunService.EXPECT().
				GetAgentRun(gomock.Any(), "test-agent-run-id").
				Return(agentRun, nil)

			// 创建一个沙盒错误
			sandboxErr := &sandboxservice.SandboxError{Message: "sandbox error"}

			mockAgentRunService.EXPECT().
				ReadFile(gomock.Any(), &agentservice.ReadFileOpt{
					UserID:         123,
					ConversationID: "test-conversation-id",
					Path:           "/path/to/file.txt",
					Offset:         10,
					Limit:          50,
				}).
				Return(nil, sandboxErr)

			result, err := tool.Run(ctx, toolCtx, input)

			So(err, ShouldBeNil)
			So(result, ShouldNotBeNil)

			viewFileResult, ok := result.(*agentservice.ViewFileResult)
			So(ok, ShouldBeTrue)
			So(viewFileResult.Error, ShouldEqual, sandboxErr.Error())
			So(viewFileResult.Display, ShouldEqual, sandboxErr.Error())
		})

		mockey.PatchConvey("When ReadFile succeeds with partial content", func() {
			// 模拟 metrics.CodeAssistMetric.ReportAgentToolCallMetrics
			mockey.MockGeneric(metrics.CodeAssistMetric.ReportAgentToolCallMetrics).Return(func(agentRunID, toolName string, status metrics.AgentToolCallStatus) {
				So(status, ShouldEqual, metrics.AgentToolCallSuccess)
				So(agentRunID, ShouldEqual, toolCtx.AgentName)
				So(toolName, ShouldEqual, "view_file")
			}).Build()

			input := map[string]string{
				"file_path": "/path/to/file.txt",
				"offset":    "5",
				"limit":     "10",
			}

			agentRun := &entity.AgentRun{
				UUID:   "test-agent-run-id",
				Status: entity.AgentRunStatusRunning,
			}

			readFileResp := &sandboxservice.ReadFileResponse{
				Path:        "/path/to/file.txt",
				Content:     []byte("line 6\nline 7\nline 8\nline 9\nline 10\nline 11\nline 12\nline 13\nline 14\nline 15"),
				ContentSize: 100,
				FileSize:    200,
				LineCount:   10,
				TotalLines:  20, // 总共有20行，但只读取了10行
			}

			mockAgentRunService.EXPECT().
				GetAgentRun(gomock.Any(), "test-agent-run-id").
				Return(agentRun, nil)

			mockAgentRunService.EXPECT().
				ReadFile(gomock.Any(), &agentservice.ReadFileOpt{
					UserID:         123,
					ConversationID: "test-conversation-id",
					Path:           "/path/to/file.txt",
					Offset:         5,
					Limit:          10,
				}).
				Return(readFileResp, nil)

			result, err := tool.Run(ctx, toolCtx, input)

			So(err, ShouldBeNil)
			So(result, ShouldNotBeNil)

			viewFileResult, ok := result.(*agentservice.ViewFileResult)
			So(ok, ShouldBeTrue)
			So(viewFileResult.Error, ShouldBeEmpty)
			// So(viewFileResult.Output, ShouldContainSubstring, "filePath: /path/to/file.txt #startLine: 6 #endLine: 16")
			So(viewFileResult.Output, ShouldContainSubstring, "line 6\nline 7\nline 8\nline 9\nline 10\nline 11\nline 12\nline 13\nline 14\nline 15")
			So(viewFileResult.Output, ShouldContainSubstring, "[... 5 more lines not shown. Use 'offset' and 'limit' parameters to read more.]")

		})

		mockey.PatchConvey("When ReadFile succeeds with complete content", func() {
			// 模拟 metrics.CodeAssistMetric.ReportAgentToolCallMetrics
			var capturedStatus metrics.AgentToolCallStatus
			mockey.MockGeneric(metrics.CodeAssistMetric.ReportAgentToolCallMetrics).Return(func(agentRunID, toolName string, status metrics.AgentToolCallStatus) {
				capturedStatus = status
				So(agentRunID, ShouldEqual, toolCtx.AgentName)
				So(toolName, ShouldEqual, "view_file")
			}).Build()

			input := map[string]string{
				"file_path": "/path/to/file.txt",
				"offset":    "0",
				"limit":     "20",
			}

			agentRun := &entity.AgentRun{
				UUID:   "test-agent-run-id",
				Status: entity.AgentRunStatusRunning,
			}

			readFileResp := &sandboxservice.ReadFileResponse{
				Path:        "/path/to/file.txt",
				Content:     []byte("complete file content"),
				ContentSize: 100,
				FileSize:    100,
				LineCount:   5,
				TotalLines:  5, // 总共有5行，全部读取
			}

			mockAgentRunService.EXPECT().
				GetAgentRun(gomock.Any(), "test-agent-run-id").
				Return(agentRun, nil)

			mockAgentRunService.EXPECT().
				ReadFile(gomock.Any(), &agentservice.ReadFileOpt{
					UserID:         123,
					ConversationID: "test-conversation-id",
					Path:           "/path/to/file.txt",
					Offset:         0,
					Limit:          20,
				}).
				Return(readFileResp, nil)

			result, err := tool.Run(ctx, toolCtx, input)

			So(err, ShouldBeNil)
			So(result, ShouldNotBeNil)

			viewFileResult, ok := result.(*agentservice.ViewFileResult)
			So(ok, ShouldBeTrue)
			So(viewFileResult.Error, ShouldBeEmpty)
			// So(viewFileResult.Output, ShouldContainSubstring, "filePath: /path/to/file.txt #startLine: 1 #endLine: 21")
			So(viewFileResult.Output, ShouldContainSubstring, "complete file content")
			// 不应该包含更多行的提示
			So(viewFileResult.Output, ShouldNotContainSubstring, "more lines not shown")
			So(capturedStatus, ShouldEqual, metrics.AgentToolCallSuccess)
		})

		mockey.PatchConvey("When offset and limit are invalid strings", func() {
			// 模拟 metrics.CodeAssistMetric.ReportAgentToolCallMetrics
			var capturedStatus metrics.AgentToolCallStatus
			mockey.MockGeneric(metrics.CodeAssistMetric.ReportAgentToolCallMetrics).Return(func(agentRunID, toolName string, status metrics.AgentToolCallStatus) {
				capturedStatus = status
				So(agentRunID, ShouldEqual, toolCtx.AgentName)
				So(toolName, ShouldEqual, "view_file")
			}).Build()

			input := map[string]string{
				"file_path": "/path/to/file.txt",
				"offset":    "invalid", // 无效的偏移量
				"limit":     "invalid", // 无效的限制
			}

			agentRun := &entity.AgentRun{
				UUID:   "test-agent-run-id",
				Status: entity.AgentRunStatusRunning,
			}

			readFileResp := &sandboxservice.ReadFileResponse{
				Path:        "/path/to/file.txt",
				Content:     []byte("file content"),
				ContentSize: 100,
				FileSize:    100,
				LineCount:   5,
				TotalLines:  5,
			}

			mockAgentRunService.EXPECT().
				GetAgentRun(gomock.Any(), "test-agent-run-id").
				Return(agentRun, nil)

			// 应该使用默认值：offset=0, limit=200
			mockAgentRunService.EXPECT().
				ReadFile(gomock.Any(), &agentservice.ReadFileOpt{
					UserID:         123,
					ConversationID: "test-conversation-id",
					Path:           "/path/to/file.txt",
					Offset:         0,   // 默认值
					Limit:          200, // 默认值
				}).
				Return(readFileResp, nil)

			result, err := tool.Run(ctx, toolCtx, input)

			So(err, ShouldBeNil)
			So(result, ShouldNotBeNil)

			viewFileResult, ok := result.(*agentservice.ViewFileResult)
			So(ok, ShouldBeTrue)
			So(viewFileResult.Error, ShouldBeEmpty)
			// So(viewFileResult.Output, ShouldContainSubstring, "filePath: /path/to/file.txt #startLine: 1 #endLine: 201")
			So(capturedStatus, ShouldEqual, metrics.AgentToolCallSuccess)
		})

		mockey.PatchConvey("When file extension is not supported", func() {
			// 模拟 metrics.CodeAssistMetric.ReportAgentToolCallMetrics
			var capturedStatus metrics.AgentToolCallStatus
			mockey.MockGeneric(metrics.CodeAssistMetric.ReportAgentToolCallMetrics).Return(func(agentRunID, toolName string, status metrics.AgentToolCallStatus) {
				capturedStatus = status
				So(agentRunID, ShouldEqual, toolCtx.AgentName)
				So(toolName, ShouldEqual, "view_file")
			}).Build()

			input := map[string]string{
				"file_path": "/path/to/data.csv", // 不支持的文件类型
				"offset":    "0",
				"limit":     "100",
			}

			result, err := tool.Run(ctx, toolCtx, input)

			So(err, ShouldBeNil)
			So(result, ShouldNotBeNil)

			viewFileResult, ok := result.(*agentservice.ViewFileResult)
			So(ok, ShouldBeTrue)
			So(viewFileResult.Error, ShouldContainSubstring, "数据文件无法读取，请使用 `data_analysis` 工具进行读取并分析")
			So(viewFileResult.Display, ShouldEqual, viewFileResult.Error)
			So(capturedStatus, ShouldEqual, metrics.AgentToolCallErr)
		})
	})
}

func TestViewFileTool_FormatResult(t *testing.T) {
	mockey.PatchConvey("Test ViewFileTool FormatResult", t, func() {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockAgentRunService := mockservice.NewMockAgentRunService(ctrl)
		tool := NewViewFileTool(mockAgentRunService)

		mockey.PatchConvey("When result is ViewFileResult", func() {
			result := &agentservice.ViewFileResult{
				BaseResult: agentservice.BaseResult{
					Output:  "file content",
					Error:   "",
					Display: "file content",
				},
			}

			formatted := tool.FormatResult(result)

			So(formatted, ShouldContainSubstring, "file content")
		})

		mockey.PatchConvey("When result is not ViewFileResult", func() {
			result := "not a ViewFileResult"
			formatted := tool.FormatResult(result)
			So(formatted, ShouldEqual, "")
		})
	})
}

func TestMapToViewFileToolInput(t *testing.T) {
	mockey.PatchConvey("Test mapToViewFileToolInput", t, func() {
		mockey.PatchConvey("With valid input", func() {
			input := map[string]string{
				"file_path": "/path/to/file.txt",
				"offset":    "10",
				"limit":     "50",
			}

			result := mapToViewFileToolInput(input)

			So(result.FilePath, ShouldEqual, "/path/to/file.txt")
			So(result.Offset, ShouldEqual, 10)
			So(result.Limit, ShouldEqual, 50)
		})

		mockey.PatchConvey("With invalid offset and limit", func() {
			input := map[string]string{
				"file_path": "/path/to/file.txt",
				"offset":    "invalid",
				"limit":     "invalid",
			}

			result := mapToViewFileToolInput(input)

			So(result.FilePath, ShouldEqual, "/path/to/file.txt")
			So(result.Offset, ShouldEqual, 0)  // 默认值
			So(result.Limit, ShouldEqual, 200) // 默认值
		})

		mockey.PatchConvey("With missing offset and limit", func() {
			input := map[string]string{
				"file_path": "/path/to/file.txt",
			}

			result := mapToViewFileToolInput(input)

			So(result.FilePath, ShouldEqual, "/path/to/file.txt")
			So(result.Offset, ShouldEqual, 0)  // 默认值
			So(result.Limit, ShouldEqual, 200) // 默认值
		})
	})
}
