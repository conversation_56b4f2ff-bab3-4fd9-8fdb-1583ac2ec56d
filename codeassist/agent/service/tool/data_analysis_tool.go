package tool

import (
	"context"
	"fmt"
	"strings"

	"code.byted.org/gopkg/logs/v2"

	"code.byted.org/devgpt/kiwis/codeassist/agent/entity"
	"code.byted.org/devgpt/kiwis/codeassist/agent/service"
	sandboxService "code.byted.org/devgpt/kiwis/codeassist/sandbox/service"
	"code.byted.org/devgpt/kiwis/lib/metrics"
)

var _ service.Tool = &DataAnalysisTool{}

const LanguagePython = "python"

type DataAnalysisToolInput struct {
	CodeScript string `json:"code_script"`
}

func mapToDataAnalysisToolInput(input map[string]string) *DataAnalysisToolInput {
	return &DataAnalysisToolInput{
		CodeScript: input["code_script"],
	}
}

type DataAnalysisTool struct {
	agentRunService service.AgentRunService
}

func NewDataAnalysisTool(agentRunService service.AgentRunService) *DataAnalysisTool {
	return &DataAnalysisTool{
		agentRunService: agentRunService,
	}
}

func (t *DataAnalysisTool) Name() string {
	return "data_analysis"
}

func (t *DataAnalysisTool) Description() string {
	return "A tool to analysis data."
}

func (t *DataAnalysisTool) Run(ctx context.Context, toolCtx service.ToolContext, input map[string]string) (any, error) {
	var result = &service.DataAnalysisResult{
		ItemList: make([]service.DataAnalysisItemResult, 0),
	}
	reportToolCallMetricsFunc := metrics.CodeAssistMetric.ReportAgentToolCallMetrics()
	toolCallStatus := metrics.AgentToolCallSuccess
	defer func() {
		reportToolCallMetricsFunc(toolCtx.AgentName, t.Name(), toolCallStatus)
	}()

	if input == nil {
		toolCallStatus = metrics.AgentToolCallErr
		result.ItemList = append(result.ItemList, service.DataAnalysisItemResult{
			Content:     "data_analysis tool input params is null",
			ContentType: string(sandboxService.CodeOutputTypeStderr),
		})
		return result, nil
	}

	inputStruct := mapToDataAnalysisToolInput(input)
	if inputStruct.CodeScript == "" {
		toolCallStatus = metrics.AgentToolCallErr
		result.ItemList = append(result.ItemList, service.DataAnalysisItemResult{
			Content:     "data_analysis tool input params field `code_script` is empty",
			ContentType: string(sandboxService.CodeOutputTypeStderr),
		})
		return result, nil
	}

	agentRun, err := t.agentRunService.GetAgentRun(ctx, toolCtx.AgentRunID)
	if err != nil {
		toolCallStatus = metrics.AgentToolCallErr
		return nil, err
	}
	if agentRun == nil || agentRun.Status < entity.AgentRunStatusRunning {
		toolCallStatus = metrics.AgentToolCallErr
		return nil, service.ToolCallNotReadyError
	}
	logs.V1.CtxInfo(ctx, "data_analysis tool run, conversation id: %s", toolCtx.ConversationID)

	resp, err := t.agentRunService.ExecuteCode(ctx, &service.ExecuteCodeOpt{
		UserID:         toolCtx.UserID,
		ConversationID: toolCtx.ConversationID,
		Code:           inputStruct.CodeScript,
		Language:       LanguagePython,
		ShareContext:   true,
	})

	if err != nil {
		logs.V1.CtxWarn(ctx, "data_analysis tool execute code failed, err: %+v", err)
		toolCallStatus = metrics.AgentToolCallErr
		return nil, err
	}

	if len(resp.RunResult) == 0 {
		result.ItemList = append(result.ItemList, service.DataAnalysisItemResult{
			Content:     "",
			ContentType: string(sandboxService.CodeOutputTypeStdout),
		})
		return result, nil
	}

	for _, v := range resp.RunResult {
		if v.Type == sandboxService.CodeOutputTypeStderr {
			toolCallStatus = metrics.AgentToolRunErr
		}
		result.ItemList = append(result.ItemList, service.DataAnalysisItemResult{
			ContentType: string(v.Type),
			Content:     v.Content,
		})
	}

	return result, nil
}

func (t *DataAnalysisTool) FormatResult(result any) string {
	var output strings.Builder
	if res, ok := result.(*service.DataAnalysisResult); ok {
		stdout := ""
		stderr := ""
		for _, v := range res.ItemList {
			switch v.ContentType {
			case "stdout":
				stdout += v.Content
			case "stderr":
				stderr += v.Content
			}
		}
		output.WriteString(fmt.Sprintf("```STDOUT\n%s\n```\n", stdout))
		output.WriteString(fmt.Sprintf("```STDERR\n%s\n```", stderr))
	} else {
		output.WriteString("```STDOUT\n```\n")
		output.WriteString("```STDERR\n```")
	}
	return output.String()
}

func (t *DataAnalysisTool) InputSpec() service.Schema {
	return service.Schema{
		Name:        "data",
		Type:        "object",
		Description: "The data to analysis.",
		Properties: map[string]service.Schema{
			"code_script": {
				Type:        "string",
				Description: "需要被执行的完整python代码，用于数据载入、分析和处理",
				Required:    true,
				StreamType:  true,
			},
		},
	}
}
