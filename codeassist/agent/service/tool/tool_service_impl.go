package tool

import "code.byted.org/devgpt/kiwis/codeassist/agent/service"

type ToolServiceImpl struct {
	tools map[string]service.Tool
}

func NewToolServiceImpl(tools []service.Tool) *ToolServiceImpl {
	t := &ToolServiceImpl{
		tools: make(map[string]service.Tool),
	}
	for _, tool := range tools {
		t.tools[tool.Name()] = tool
	}
	return t
}

func (t *ToolServiceImpl) GetTool(name string) service.Tool {
	return t.tools[name]
}
