package tool

import (
	"context"
	"errors"
	"testing"

	"code.byted.org/gopkg/logs/v2"
	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"
	"go.uber.org/mock/gomock"

	"code.byted.org/devgpt/kiwis/codeassist/agent/entity"
	mockService "code.byted.org/devgpt/kiwis/codeassist/agent/mock/service"
	"code.byted.org/devgpt/kiwis/codeassist/agent/service"
	sandboxService "code.byted.org/devgpt/kiwis/codeassist/sandbox/service"
	"code.byted.org/devgpt/kiwis/lib/metrics"
)

func TestDataAnalysisTool_Name(t *testing.T) {
	mockey.PatchConvey("TestDataAnalysisTool_Name", t, func() {
		dataAnalysisTool := NewDataAnalysisTool(nil)
		convey.So(dataAnalysisTool.Name(), convey.ShouldEqual, "data_analysis")
	})
}

func TestDataAnalysisTool_Description(t *testing.T) {
	mockey.PatchConvey("TestDataAnalysisTool_Description", t, func() {
		dataAnalysisTool := NewDataAnalysisTool(nil)
		convey.So(dataAnalysisTool.Description(), convey.ShouldEqual, "A tool to analysis data.")
	})
}

func TestDataAnalysisTool_Run(t *testing.T) {
	mockey.PatchConvey("TestDataAnalysisTool_Run", t, func() {
		ctrl := gomock.NewController(t)
		mockAgentRunService := mockService.NewMockAgentRunService(ctrl)
		dataAnalysisTool := NewDataAnalysisTool(mockAgentRunService)

		mockey.PatchConvey("Input params is nil", func() {
			mockey.MockGeneric(metrics.CodeAssistMetric.ReportAgentToolCallMetrics).Return(func(agentRunID, toolName string, status metrics.AgentToolCallStatus) {
			}).Build()
			result, err := dataAnalysisTool.Run(context.Background(), service.ToolContext{}, nil)
			convey.So(result, convey.ShouldNotBeNil)
			convey.So(err, convey.ShouldBeNil)
			dataAnalysisResult, ok := result.(*service.DataAnalysisResult)
			convey.So(ok, convey.ShouldBeTrue)
			convey.So(len(dataAnalysisResult.ItemList), convey.ShouldEqual, 1)
			convey.So(dataAnalysisResult.ItemList[0].ContentType, convey.ShouldEqual, "stderr")
			convey.So(dataAnalysisResult.ItemList[0].Content, convey.ShouldEqual, "data_analysis tool input params is null")
		})

		mockey.PatchConvey("CodeScript is empty", func() {
			mockey.MockGeneric(metrics.CodeAssistMetric.ReportAgentToolCallMetrics).Return(func(agentRunID, toolName string, status metrics.AgentToolCallStatus) {
			}).Build()
			result, err := dataAnalysisTool.Run(context.Background(), service.ToolContext{}, map[string]string{
				"code_script": "",
			})
			convey.So(result, convey.ShouldNotBeNil)
			convey.So(err, convey.ShouldBeNil)
			dataAnalysisResult, ok := result.(*service.DataAnalysisResult)
			convey.So(ok, convey.ShouldBeTrue)
			convey.So(len(dataAnalysisResult.ItemList), convey.ShouldEqual, 1)
			convey.So(dataAnalysisResult.ItemList[0].ContentType, convey.ShouldEqual, "stderr")
			convey.So(dataAnalysisResult.ItemList[0].Content, convey.ShouldEqual, "data_analysis tool input params field `code_script` is empty")
		})

		mockey.PatchConvey("GetAgentRun error", func() {
			mockey.MockGeneric(metrics.CodeAssistMetric.ReportAgentToolCallMetrics).Return(func(agentRunID, toolName string, status metrics.AgentToolCallStatus) {
			}).Build()
			mockAgentRunService.EXPECT().GetAgentRun(gomock.Any(), gomock.Any()).Return(nil, errors.New("get agent run error"))

			result, err := dataAnalysisTool.Run(context.Background(), service.ToolContext{AgentRunID: "test-id"}, map[string]string{
				"code_script": "print('Hello, World!')",
			})
			convey.So(result, convey.ShouldBeNil)
			convey.So(err, convey.ShouldNotBeNil)
			convey.So(err.Error(), convey.ShouldEqual, "get agent run error")
		})

		mockey.PatchConvey("AgentRun not ready", func() {
			mockey.MockGeneric(metrics.CodeAssistMetric.ReportAgentToolCallMetrics).Return(func(agentRunID, toolName string, status metrics.AgentToolCallStatus) {
			}).Build()
			mockAgentRunService.EXPECT().GetAgentRun(gomock.Any(), gomock.Any()).Return(&entity.AgentRun{Status: entity.AgentRunStatusCreated}, nil)
			result, err := dataAnalysisTool.Run(context.Background(), service.ToolContext{AgentRunID: "test-id"}, map[string]string{
				"code_script": "print('Hello, World!')",
			})
			convey.So(result, convey.ShouldBeNil)
			convey.So(err, convey.ShouldEqual, service.ToolCallNotReadyError)
		})

		mockey.PatchConvey("ExecuteCode returns general error", func() {
			mockAgentRunService.EXPECT().GetAgentRun(gomock.Any(), gomock.Any()).Return(&entity.AgentRun{Status: entity.AgentRunStatusRunning, UserID: 123}, nil)
			mockAgentRunService.EXPECT().ExecuteCode(gomock.Any(), gomock.Any()).Return(nil, errors.New("execute code error"))

			// Mock logs.V1.CtxWarn to avoid actual logging during test
			mockey.Mock(logs.V1.CtxWarn).Return().Build()

			// Mock metrics reporting to verify toolCallStatus
			mockey.MockGeneric(metrics.CodeAssistMetric.ReportAgentToolCallMetrics).Return(func(agentRunID, toolName string, status metrics.AgentToolCallStatus) {
				convey.So(status, convey.ShouldEqual, metrics.AgentToolCallErr)
			}).Build()

			result, err := dataAnalysisTool.Run(context.Background(), service.ToolContext{AgentRunID: "test-id", ConversationID: "conv-id"}, map[string]string{
				"code_script": "print('Hello, World!')",
			})
			convey.So(result, convey.ShouldBeNil)
			convey.So(err, convey.ShouldNotBeNil)
			convey.So(err.Error(), convey.ShouldEqual, "execute code error")
		})

		mockey.PatchConvey("ExecuteCode returns sandbox error", func() {
			mockAgentRunService.EXPECT().GetAgentRun(gomock.Any(), gomock.Any()).Return(&entity.AgentRun{Status: entity.AgentRunStatusRunning, UserID: 123}, nil)

			// Create a SandboxError
			sandboxErr := &sandboxService.SandboxError{Message: "sandbox execution error"}
			mockAgentRunService.EXPECT().ExecuteCode(gomock.Any(), gomock.Any()).Return(nil, sandboxErr)

			// Mock logs.V1.CtxWarn to avoid actual logging during test
			mockey.Mock(logs.V1.CtxWarn).Return().Build()

			// Mock metrics reporting to verify toolCallStatus
			mockey.MockGeneric(metrics.CodeAssistMetric.ReportAgentToolCallMetrics).Return(func(agentRunID, toolName string, status metrics.AgentToolCallStatus) {
				convey.So(status, convey.ShouldEqual, metrics.AgentToolCallErr)
			}).Build()

			result, err := dataAnalysisTool.Run(context.Background(), service.ToolContext{AgentRunID: "test-id", ConversationID: "conv-id"}, map[string]string{
				"code_script": "print('Hello, World!')",
			})
			convey.So(result, convey.ShouldBeNil)
			convey.So(err, convey.ShouldNotBeNil)
			convey.So(err.Error(), convey.ShouldContainSubstring, "sandbox execution error")
			_, isSandboxErr := err.(*sandboxService.SandboxError)
			convey.So(isSandboxErr, convey.ShouldBeTrue)
		})

		mockey.PatchConvey("Empty RunResult", func() {
			mockey.MockGeneric(metrics.CodeAssistMetric.ReportAgentToolCallMetrics).Return(func(agentRunID, toolName string, status metrics.AgentToolCallStatus) {
				convey.So(status, convey.ShouldEqual, metrics.AgentToolCallSuccess)
			}).Build()
			mockAgentRunService.EXPECT().GetAgentRun(gomock.Any(), gomock.Any()).Return(&entity.AgentRun{Status: entity.AgentRunStatusRunning, UserID: 123}, nil)
			mockAgentRunService.EXPECT().ExecuteCode(gomock.Any(), gomock.Any()).Return(&sandboxService.ExecuteResponse{RunResult: []*sandboxService.ExecuteCommandResult{}}, nil)

			result, err := dataAnalysisTool.Run(context.Background(), service.ToolContext{AgentRunID: "test-id", ConversationID: "conv-id"}, map[string]string{
				"code_script": "print('Hello, World!')",
			})
			convey.So(result, convey.ShouldNotBeNil)
			convey.So(err, convey.ShouldBeNil)
			dataAnalysisResult, ok := result.(*service.DataAnalysisResult)
			convey.So(ok, convey.ShouldBeTrue)
			convey.So(len(dataAnalysisResult.ItemList), convey.ShouldEqual, 1)
			convey.So(dataAnalysisResult.ItemList[0].ContentType, convey.ShouldEqual, "stdout")
			convey.So(dataAnalysisResult.ItemList[0].Content, convey.ShouldEqual, "")
		})

		mockey.PatchConvey("Success with stdout", func() {
			mockAgentRunService.EXPECT().GetAgentRun(gomock.Any(), gomock.Any()).Return(&entity.AgentRun{Status: entity.AgentRunStatusRunning, UserID: 123}, nil)
			mockAgentRunService.EXPECT().ExecuteCode(gomock.Any(), gomock.Any()).Return(&sandboxService.ExecuteResponse{RunResult: []*sandboxService.ExecuteCommandResult{
				{Type: sandboxService.CodeOutputTypeStdout, Content: "Hello, World!"},
			}}, nil)

			// Mock metrics reporting to verify toolCallStatus
			mockey.MockGeneric(metrics.CodeAssistMetric.ReportAgentToolCallMetrics).Return(func(agentRunID, toolName string, status metrics.AgentToolCallStatus) {
				convey.So(status, convey.ShouldEqual, metrics.AgentToolCallSuccess)
			}).Build()

			result, err := dataAnalysisTool.Run(context.Background(), service.ToolContext{AgentRunID: "test-id", ConversationID: "conv-id"}, map[string]string{
				"code_script": "print('Hello, World!')",
			})
			convey.So(result, convey.ShouldNotBeNil)
			convey.So(err, convey.ShouldBeNil)
			dataAnalysisResult, ok := result.(*service.DataAnalysisResult)
			convey.So(ok, convey.ShouldBeTrue)
			convey.So(len(dataAnalysisResult.ItemList), convey.ShouldEqual, 1)
			convey.So(dataAnalysisResult.ItemList[0].ContentType, convey.ShouldEqual, "stdout")
			convey.So(dataAnalysisResult.ItemList[0].Content, convey.ShouldEqual, "Hello, World!")
		})

		mockey.PatchConvey("Success with stderr", func() {
			mockAgentRunService.EXPECT().GetAgentRun(gomock.Any(), gomock.Any()).Return(&entity.AgentRun{Status: entity.AgentRunStatusRunning, UserID: 123}, nil)
			mockAgentRunService.EXPECT().ExecuteCode(gomock.Any(), gomock.Any()).Return(&sandboxService.ExecuteResponse{RunResult: []*sandboxService.ExecuteCommandResult{
				{Type: sandboxService.CodeOutputTypeStderr, Content: "Error: invalid syntax"},
			}}, nil)

			// Mock metrics reporting to verify toolCallStatus
			metricsMock := mockey.MockGeneric(metrics.CodeAssistMetric.ReportAgentToolCallMetrics).Return(func(agentRunID, toolName string, status metrics.AgentToolCallStatus) {
				convey.So(status, convey.ShouldEqual, metrics.AgentToolRunErr)
			}).Build()
			defer metricsMock.UnPatch()

			result, err := dataAnalysisTool.Run(context.Background(), service.ToolContext{AgentRunID: "test-id", ConversationID: "conv-id"}, map[string]string{
				"code_script": "print('Hello, World!')",
			})
			convey.So(result, convey.ShouldNotBeNil)
			convey.So(err, convey.ShouldBeNil)
			dataAnalysisResult, ok := result.(*service.DataAnalysisResult)
			convey.So(ok, convey.ShouldBeTrue)
			convey.So(len(dataAnalysisResult.ItemList), convey.ShouldEqual, 1)
			convey.So(dataAnalysisResult.ItemList[0].ContentType, convey.ShouldEqual, "stderr")
			convey.So(dataAnalysisResult.ItemList[0].Content, convey.ShouldEqual, "Error: invalid syntax")
		})

		mockey.PatchConvey("Success with multiple outputs", func() {
			mockAgentRunService.EXPECT().GetAgentRun(gomock.Any(), gomock.Any()).Return(&entity.AgentRun{Status: entity.AgentRunStatusRunning, UserID: 123}, nil)
			mockAgentRunService.EXPECT().ExecuteCode(gomock.Any(), gomock.Any()).Return(&sandboxService.ExecuteResponse{RunResult: []*sandboxService.ExecuteCommandResult{
				{Type: sandboxService.CodeOutputTypeStdout, Content: "Hello, World!"},
				{Type: sandboxService.CodeOutputTypeStderr, Content: "Warning: deprecated function"},
				{Type: sandboxService.CodeOutputTypeImage, Content: "base64encodedimage"},
			}}, nil)

			mockey.MockGeneric(metrics.CodeAssistMetric.ReportAgentToolCallMetrics).Return(func(agentRunID, toolName string, status metrics.AgentToolCallStatus) {
				convey.So(status, convey.ShouldEqual, metrics.AgentToolRunErr)
			}).Build()

			result, err := dataAnalysisTool.Run(context.Background(), service.ToolContext{AgentRunID: "test-id", ConversationID: "conv-id"}, map[string]string{
				"code_script": "print('Hello, World!')",
			})
			convey.So(result, convey.ShouldNotBeNil)
			convey.So(err, convey.ShouldBeNil)
			dataAnalysisResult, ok := result.(*service.DataAnalysisResult)
			convey.So(ok, convey.ShouldBeTrue)
			convey.So(len(dataAnalysisResult.ItemList), convey.ShouldEqual, 3)
			convey.So(dataAnalysisResult.ItemList[0].ContentType, convey.ShouldEqual, "stdout")
			convey.So(dataAnalysisResult.ItemList[0].Content, convey.ShouldEqual, "Hello, World!")
			convey.So(dataAnalysisResult.ItemList[1].ContentType, convey.ShouldEqual, "stderr")
			convey.So(dataAnalysisResult.ItemList[1].Content, convey.ShouldEqual, "Warning: deprecated function")
			convey.So(dataAnalysisResult.ItemList[2].ContentType, convey.ShouldEqual, "image")
			convey.So(dataAnalysisResult.ItemList[2].Content, convey.ShouldEqual, "base64encodedimage")
		})

		mockey.PatchConvey("Verify ExecuteCode parameters", func() {
			mockAgentRunService.EXPECT().GetAgentRun(gomock.Any(), gomock.Any()).Return(&entity.AgentRun{Status: entity.AgentRunStatusRunning, UserID: 123}, nil)
			mockAgentRunService.EXPECT().ExecuteCode(gomock.Any(), gomock.Any()).Do(func(_ context.Context, opt *service.ExecuteCodeOpt) {
				// Verify that the parameters are correctly passed
				convey.So(opt.UserID, convey.ShouldEqual, int64(123))
				convey.So(opt.ConversationID, convey.ShouldEqual, "conv-id")
				convey.So(opt.Code, convey.ShouldEqual, "import pandas as pd\ndf = pd.DataFrame({'A': [1, 2, 3]})\nprint(df)")
				convey.So(opt.Language, convey.ShouldEqual, "python")
			}).Return(&sandboxService.ExecuteResponse{RunResult: []*sandboxService.ExecuteCommandResult{
				{Type: sandboxService.CodeOutputTypeStdout, Content: "   A\n0  1\n1  2\n2  3"},
			}}, nil)
			mockey.MockGeneric(metrics.CodeAssistMetric.ReportAgentToolCallMetrics).Return(func(agentRunID, toolName string, status metrics.AgentToolCallStatus) {
				convey.So(status, convey.ShouldEqual, metrics.AgentToolCallSuccess)
			}).Build()

			result, err := dataAnalysisTool.Run(context.Background(), service.ToolContext{AgentRunID: "test-id", UserID: 123, ConversationID: "conv-id"}, map[string]string{
				"code_script": "import pandas as pd\ndf = pd.DataFrame({'A': [1, 2, 3]})\nprint(df)",
			})
			convey.So(result, convey.ShouldNotBeNil)
			convey.So(err, convey.ShouldBeNil)
		})
	})
}

func TestDataAnalysisTool_FormatResult(t *testing.T) {
	mockey.PatchConvey("TestDataAnalysisTool_FormatResult", t, func() {
		dataAnalysisTool := NewDataAnalysisTool(nil)

		mockey.PatchConvey("Format with stdout", func() {
			dataAnalysisResult := &service.DataAnalysisResult{
				ItemList: []service.DataAnalysisItemResult{
					{ContentType: "stdout", Content: "Hello, World!"},
				},
			}

			formatted := dataAnalysisTool.FormatResult(dataAnalysisResult)
			convey.So(formatted, convey.ShouldContainSubstring, "```STDOUT\nHello, World!\n```\n")
			convey.So(formatted, convey.ShouldContainSubstring, "```STDERR\n\n```")
		})

		mockey.PatchConvey("Format with stderr", func() {
			dataAnalysisResult := &service.DataAnalysisResult{
				ItemList: []service.DataAnalysisItemResult{
					{ContentType: "stderr", Content: "Error: invalid syntax"},
				},
			}

			formatted := dataAnalysisTool.FormatResult(dataAnalysisResult)
			convey.So(formatted, convey.ShouldContainSubstring, "```STDOUT\n\n```")
			convey.So(formatted, convey.ShouldContainSubstring, "```STDERR\nError: invalid syntax\n```")
		})

		mockey.PatchConvey("Format with multiple outputs", func() {
			dataAnalysisResult := &service.DataAnalysisResult{
				ItemList: []service.DataAnalysisItemResult{
					{ContentType: "stdout", Content: "Hello, World!"},
					{ContentType: "stderr", Content: "Warning: deprecated function"},
				},
			}

			formatted := dataAnalysisTool.FormatResult(dataAnalysisResult)
			convey.So(formatted, convey.ShouldContainSubstring, "```STDOUT\nHello, World!\n```")
			convey.So(formatted, convey.ShouldContainSubstring, "```STDERR\nWarning: deprecated function\n```")
		})

		mockey.PatchConvey("Format non-DataAnalysisResult", func() {
			formatted := dataAnalysisTool.FormatResult("not a DataAnalysisResult")
			convey.So(formatted, convey.ShouldEqual, "```STDOUT\n```\n```STDERR\n```")
		})
	})
}

func TestDataAnalysisTool_InputSpec(t *testing.T) {
	mockey.PatchConvey("TestDataAnalysisTool_InputSpec", t, func() {
		dataAnalysisTool := NewDataAnalysisTool(nil)
		spec := dataAnalysisTool.InputSpec()

		convey.So(spec.Name, convey.ShouldEqual, "data")
		convey.So(spec.Type, convey.ShouldEqual, "object")
		convey.So(spec.Description, convey.ShouldEqual, "The data to analysis.")
		convey.So(spec.Properties, convey.ShouldNotBeNil)
		convey.So(len(spec.Properties), convey.ShouldEqual, 1)

		// Check code_script property
		codeProp, ok := spec.Properties["code_script"]
		convey.So(ok, convey.ShouldBeTrue)
		convey.So(codeProp.Type, convey.ShouldEqual, "string")
		convey.So(codeProp.Description, convey.ShouldEqual, "需要被执行的完整python代码，用于数据载入、分析和处理")
		convey.So(codeProp.Required, convey.ShouldBeTrue)
		convey.So(codeProp.StreamType, convey.ShouldBeTrue)
	})
}

func TestMapToDataAnalysisToolInput(t *testing.T) {
	mockey.PatchConvey("TestMapToDataAnalysisToolInput", t, func() {
		mockey.PatchConvey("With valid input", func() {
			input := map[string]string{
				"code_script": "print('Hello, World!')",
			}
			result := mapToDataAnalysisToolInput(input)
			convey.So(result, convey.ShouldNotBeNil)
			convey.So(result.CodeScript, convey.ShouldEqual, "print('Hello, World!')")
		})

		mockey.PatchConvey("With empty input", func() {
			input := map[string]string{}
			result := mapToDataAnalysisToolInput(input)
			convey.So(result, convey.ShouldNotBeNil)
			convey.So(result.CodeScript, convey.ShouldEqual, "")
		})

		mockey.PatchConvey("With nil input", func() {
			var input map[string]string
			result := mapToDataAnalysisToolInput(input)
			convey.So(result, convey.ShouldNotBeNil)
			convey.So(result.CodeScript, convey.ShouldEqual, "")
		})
	})
}
