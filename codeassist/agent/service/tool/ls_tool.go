package tool

import (
	"context"
	"fmt"
	"strings"

	"code.byted.org/gopkg/logs/v2"

	"code.byted.org/devgpt/kiwis/codeassist/agent/entity"
	"code.byted.org/devgpt/kiwis/codeassist/agent/service"
	sandboxService "code.byted.org/devgpt/kiwis/codeassist/sandbox/service"
	"code.byted.org/devgpt/kiwis/lib/metrics"
)

var _ service.Tool = &LSTool{}

const MAX_LS_FILE_NUM = 500
const CROP_MESSAGE = "该目录下有超过 %d 个文件，请给出更具体的路径来探索嵌套目录，前 %d 个文件和目录如下:\n\n"
const LanguageShell = "shell"

type LSToolInput struct {
	Path       string
	ShowHidden bool
}

func mapToLSToolInput(input map[string]string) *LSToolInput {
	return &LSToolInput{
		Path:       input["path"],
		ShowHidden: input["show_hidden"] == "true",
	}
}

type LSTool struct {
	agentRunService service.AgentRunService
}

func NewLSTool(agentRunService service.AgentRunService) *LSTool {
	return &LSTool{
		agentRunService: agentRunService,
	}
}

func (t *LSTool) Name() string {
	return "ls"
}

func (t *LSTool) Description() string {
	return "A tool to list files."
}

func (t *LSTool) Run(ctx context.Context, toolCtx service.ToolContext, input map[string]string) (any, error) {
	var lsResult = &service.LSResult{
		BaseResult: service.BaseResult{},
	}
	reportToolCallMetricsFunc := metrics.CodeAssistMetric.ReportAgentToolCallMetrics()
	toolCallStatus := metrics.AgentToolCallSuccess
	defer func() {
		reportToolCallMetricsFunc(toolCtx.AgentName, t.Name(), toolCallStatus)
	}()
	if input == nil {
		toolCallStatus = metrics.AgentToolCallErr
		lsResult.Error = "ls tool input params is null"
		lsResult.Display = lsResult.Error
		return lsResult, nil
	}

	inputStruct := mapToLSToolInput(input)
	if inputStruct.Path == "" {
		toolCallStatus = metrics.AgentToolCallErr
		lsResult.Error = "ls tool input params field `path` is empty"
		lsResult.Display = lsResult.Error
		return lsResult, nil
	}

	agentRun, err := t.agentRunService.GetAgentRun(ctx, toolCtx.AgentRunID)
	if err != nil {
		toolCallStatus = metrics.AgentToolCallErr
		return nil, err
	}
	if agentRun == nil || agentRun.Status < entity.AgentRunStatusRunning {
		toolCallStatus = metrics.AgentToolCallErr
		return nil, service.ToolCallNotReadyError
	}
	logs.V1.CtxInfo(ctx, "ls tool run, path: %s, conversation id: %s", inputStruct.Path, toolCtx.ConversationID)

	cmd := fmt.Sprintf("tree %s", inputStruct.Path)
	if inputStruct.ShowHidden {
		cmd = fmt.Sprintf("tree -a %s", inputStruct.Path)
	}
	resp, err := t.agentRunService.ExecuteCode(ctx, &service.ExecuteCodeOpt{
		UserID:         toolCtx.UserID,
		ConversationID: toolCtx.ConversationID,
		Code:           cmd,
		Language:       LanguageShell,
	})

	if err != nil {
		logs.V1.CtxWarn(ctx, "ls tool failed, err: %+v", err)
		toolCallStatus = metrics.AgentToolCallErr
		return nil, err
	}

	for _, v := range resp.RunResult {
		switch v.Type {
		case sandboxService.CodeOutputTypeStdout:
			output := v.Content
			allFiles := strings.Split(output, "\n")
			if len(allFiles) > MAX_LS_FILE_NUM {
				output = fmt.Sprintf(CROP_MESSAGE, MAX_LS_FILE_NUM, MAX_LS_FILE_NUM) +
					"\n" +
					strings.Join(allFiles[:MAX_LS_FILE_NUM], "\n")
			}
			lsResult.Output = output
			lsResult.Display = output
		case sandboxService.CodeOutputTypeStderr:
			toolCallStatus = metrics.AgentToolRunErr
			lsResult.Error = v.Content
			lsResult.Display = lsResult.Error
		}
	}
	return lsResult, nil
}

func (t *LSTool) FormatResult(result any) string {
	if res, ok := result.(*service.LSResult); ok {
		return res.Display
	}
	return ""
}

func (t *LSTool) InputSpec() service.Schema {
	return service.Schema{
		Name:        "ls tool input schema",
		Type:        "object",
		Description: "The path of the file to ls.",
		Properties: map[string]service.Schema{
			"path": {
				Name:        "path",
				Type:        "string",
				Description: "必填参数，目录的相对路径，例如 `/mnt/workspace/`",
				Required:    true,
			},
			"show_hidden": {
				Name:        "show_hidden",
				Type:        "bool",
				Description: "可选参数，是否显示隐藏文件，默认不显示",
				Default:     false,
			},
		},
	}
}
