package tool

import (
	"context"
	"errors"

	"code.byted.org/gopkg/logs/v2"

	"code.byted.org/devgpt/kiwis/codeassist/agent/entity"
	"code.byted.org/devgpt/kiwis/codeassist/agent/service"
	sandboxService "code.byted.org/devgpt/kiwis/codeassist/sandbox/service"
	"code.byted.org/devgpt/kiwis/lib/metrics"
)

var _ service.Tool = &CreateFileTool{}

type CreateFileInput struct {
	FileText string `json:"file_text"`
	Path     string `json:"path"`
}

func mapToCreateFileInput(input map[string]string) *CreateFileInput {
	return &CreateFileInput{
		FileText: input["file_text"],
		Path:     input["path"],
	}
}

type CreateFileTool struct {
	agentRunService service.AgentRunService
}

func NewCreateFileTool(agentRunService service.AgentRunService) *CreateFileTool {
	return &CreateFileTool{
		agentRunService: agentRunService,
	}
}

func (t *CreateFileTool) Name() string {
	return "create_file"
}

func (t *CreateFileTool) Description() string {
	return "A tool to create a file."
}

func (t *CreateFileTool) Run(ctx context.Context, toolCtx service.ToolContext, input map[string]string) (any, error) {
	var result = &service.CreateFileResult{
		BaseResult: service.BaseResult{},
	}

	reportToolCallMetricsFunc := metrics.CodeAssistMetric.ReportAgentToolCallMetrics()
	toolCallStatus := metrics.AgentToolCallSuccess
	defer func() {
		reportToolCallMetricsFunc(toolCtx.AgentName, t.Name(), toolCallStatus)
	}()
	if input == nil {
		toolCallStatus = metrics.AgentToolCallErr
		result.Error = "create_file tool input params is null"
		result.Display = result.Error
		return result, nil
	}
	inputStruct := mapToCreateFileInput(input)
	if inputStruct.FileText == "" {
		toolCallStatus = metrics.AgentToolCallErr
		result.Error = "create_file tool input params field `file_text` is empty"
		result.Display = result.Error
		return result, nil
	}

	if inputStruct.Path == "" {
		toolCallStatus = metrics.AgentToolCallErr
		result.Error = "create_file tool input params field `path` is empty"
		result.Display = result.Error
		return result, nil
	}

	agentRun, err := t.agentRunService.GetAgentRun(ctx, toolCtx.AgentRunID)
	if err != nil {
		toolCallStatus = metrics.AgentToolCallErr
		return nil, err
	}
	if agentRun == nil || agentRun.Status < entity.AgentRunStatusRunning {
		toolCallStatus = metrics.AgentToolCallErr
		return nil, service.ToolCallNotReadyError
	}
	logs.V1.CtxInfo(ctx, "create_file tool run, file path: %s, conversation id: %s", inputStruct.Path, toolCtx.ConversationID)
	err = t.agentRunService.CreateFile(ctx, &service.CreateFileOpt{
		UserID:         toolCtx.UserID,
		ConversationID: toolCtx.ConversationID,
		Path:           inputStruct.Path,
		Content:        []byte(inputStruct.FileText),
	})

	if err == nil {
		result.BaseResult.Output = "File created successfully"
		result.BaseResult.Display = result.BaseResult.Output
		return result, nil
	}

	logs.V1.CtxWarn(ctx, "create_file tool failed, err: %+v", err)

	// tool run err
	var sandboxErr *sandboxService.SandboxError
	if errors.As(err, &sandboxErr) {
		toolCallStatus = metrics.AgentToolRunErr
		result.BaseResult.Error = err.Error()
		result.BaseResult.Display = result.BaseResult.Error
		return result, nil
	}

	// tool 工程上出错
	toolCallStatus = metrics.AgentToolCallErr
	return nil, err
}

func (t *CreateFileTool) FormatResult(result any) string {
	if res, ok := result.(*service.CreateFileResult); ok {
		return res.Display
	}
	return ""
}

func (t *CreateFileTool) InputSpec() service.Schema {
	return service.Schema{
		Name: "create file input schema",
		Type: "Object",
		Properties: map[string]service.Schema{
			"file_text": {
				Name:        "file_text",
				Type:        "string",
				Description: "必填参数，用户指定待创建文件的内容",
				Required:    true,
				StreamType:  true,
			},
			"path": {
				Name:        "path",
				Type:        "string",
				Description: "文件或目录的相对路径，例如 `/mnt/workspace/file.py`",
				Required:    true,
			},
		},
	}
}
