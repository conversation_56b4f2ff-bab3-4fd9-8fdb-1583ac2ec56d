package tool

import (
	"context"
	"errors"
	"testing"

	"code.byted.org/gopkg/logs/v2"
	"github.com/bytedance/mockey"
	. "github.com/smartystreets/goconvey/convey"
	"go.uber.org/mock/gomock"

	"code.byted.org/devgpt/kiwis/codeassist/agent/entity"
	mockService "code.byted.org/devgpt/kiwis/codeassist/agent/mock/service"
	"code.byted.org/devgpt/kiwis/codeassist/agent/service"
	sandboxService "code.byted.org/devgpt/kiwis/codeassist/sandbox/service"
	"code.byted.org/devgpt/kiwis/lib/metrics"
)

func TestEditFileTool_Run(t *testing.T) {
	Ctrl := gomock.NewController(t)
	defer Ctrl.Finish()

	mockAgentRunService := mockService.NewMockAgentRunService(Ctrl)
	editFileTool := NewEditFile(mockAgentRunService)

	Ctx := context.Background()
	toolCtx := service.ToolContext{
		AgentRunID:     "test-agent-run-id",
		UserID:         123,
		ConversationID: "test-conversation-id",
	}

	mockey.PatchConvey("Test EditFileTool Run", t, func() {
		mockey.PatchConvey("When input is nil", func() {
			// Mock metrics reporting to verify toolCallStatus
			mockey.MockGeneric(metrics.CodeAssistMetric.ReportAgentToolCallMetrics).Return(func(agentRunID, toolName string, status metrics.AgentToolCallStatus) {
				So(status, ShouldEqual, metrics.AgentToolCallErr)
			}).Build()

			result, err := editFileTool.Run(Ctx, toolCtx, nil)

			So(err, ShouldBeNil)
			So(result, ShouldNotBeNil)
			editFileResult, ok := result.(*service.EditFileResult)
			So(ok, ShouldBeTrue)
			So(editFileResult.Error, ShouldContainSubstring, "edit_file tool input params is null")
			So(editFileResult.Display, ShouldContainSubstring, "edit_file tool input params is null")
		})

		mockey.PatchConvey("When old_str is empty", func() {
			input := map[string]string{
				"path":    "/path/to/file",
				"old_str": "",
				"new_str": "new content",
			}

			// Mock metrics reporting to verify toolCallStatus
			mockey.MockGeneric(metrics.CodeAssistMetric.ReportAgentToolCallMetrics).Return(func(agentRunID, toolName string, status metrics.AgentToolCallStatus) {
				So(status, ShouldEqual, metrics.AgentToolCallErr)
			}).Build()

			result, err := editFileTool.Run(Ctx, toolCtx, input)

			So(err, ShouldBeNil)
			So(result, ShouldNotBeNil)
			editFileResult, ok := result.(*service.EditFileResult)
			So(ok, ShouldBeTrue)
			So(editFileResult.Error, ShouldContainSubstring, "edit_file tool input params field `old_str` is empty")
			So(editFileResult.Display, ShouldContainSubstring, "edit_file tool input params field `old_str` is empty")
		})

		mockey.PatchConvey("When path is empty", func() {
			input := map[string]string{
				"path":    "",
				"old_str": "old content",
				"new_str": "new content",
			}

			// Mock metrics reporting to verify toolCallStatus
			mockey.MockGeneric(metrics.CodeAssistMetric.ReportAgentToolCallMetrics).Return(func(agentRunID, toolName string, status metrics.AgentToolCallStatus) {
				So(status, ShouldEqual, metrics.AgentToolCallErr)
			}).Build()

			result, err := editFileTool.Run(Ctx, toolCtx, input)

			So(err, ShouldBeNil)
			So(result, ShouldNotBeNil)
			editFileResult, ok := result.(*service.EditFileResult)
			So(ok, ShouldBeTrue)
			So(editFileResult.Error, ShouldContainSubstring, "edit_file tool input params field `path` is empty")
			So(editFileResult.Display, ShouldContainSubstring, "edit_file tool input params field `path` is empty")
		})

		mockey.PatchConvey("When GetAgentRun returns error", func() {
			input := map[string]string{
				"path":    "/path/to/file",
				"old_str": "old content",
				"new_str": "new content",
			}

			// Mock metrics reporting to verify toolCallStatus
			mockey.MockGeneric(metrics.CodeAssistMetric.ReportAgentToolCallMetrics).Return(func(agentRunID, toolName string, status metrics.AgentToolCallStatus) {
				So(status, ShouldEqual, metrics.AgentToolCallErr)
			}).Build()

			mockAgentRunService.EXPECT().GetAgentRun(gomock.Any(), "test-agent-run-id").Return(nil, errors.New("get agent run error"))

			result, err := editFileTool.Run(Ctx, toolCtx, input)

			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldEqual, "get agent run error")
			So(result, ShouldBeNil)
		})

		mockey.PatchConvey("When AgentRun status is not ready", func() {
			input := map[string]string{
				"path":    "/path/to/file",
				"old_str": "old content",
				"new_str": "new content",
			}

			agentRun := &entity.AgentRun{
				Status: entity.AgentRunStatusCreated,
			}

			// Mock metrics reporting to verify toolCallStatus
			mockey.MockGeneric(metrics.CodeAssistMetric.ReportAgentToolCallMetrics).Return(func(agentRunID, toolName string, status metrics.AgentToolCallStatus) {
				So(status, ShouldEqual, metrics.AgentToolCallErr)
			}).Build()

			mockAgentRunService.EXPECT().GetAgentRun(gomock.Any(), "test-agent-run-id").Return(agentRun, nil)

			result, err := editFileTool.Run(Ctx, toolCtx, input)

			So(err, ShouldEqual, service.ToolCallNotReadyError)
			So(result, ShouldBeNil)
		})

		mockey.PatchConvey("When EditFile returns general error", func() {
			input := map[string]string{
				"path":    "/path/to/file",
				"old_str": "old content",
				"new_str": "new content",
			}

			agentRun := &entity.AgentRun{
				Status: entity.AgentRunStatusRunning,
			}

			// Mock logs.V1.CtxWarn to avoid actual logging during test
			mockey.Mock(logs.V1.CtxWarn).Return().Build()

			// Mock metrics reporting to verify toolCallStatus
			mockey.MockGeneric(metrics.CodeAssistMetric.ReportAgentToolCallMetrics).Return(func(agentRunID, toolName string, status metrics.AgentToolCallStatus) {
				So(status, ShouldEqual, metrics.AgentToolCallErr)
			}).Build()

			mockAgentRunService.EXPECT().GetAgentRun(gomock.Any(), "test-agent-run-id").Return(agentRun, nil)
			mockAgentRunService.EXPECT().EditFile(gomock.Any(), &service.EditFileOpt{
				UserID:         123,
				ConversationID: "test-conversation-id",
				Path:           "/path/to/file",
				OldStr:         "old content",
				NewStr:         "new content",
			}).Return(nil, errors.New("edit file error"))

			result, err := editFileTool.Run(Ctx, toolCtx, input)

			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldEqual, "edit file error")
			So(result, ShouldBeNil)
		})

		mockey.PatchConvey("When EditFile returns sandbox error", func() {
			input := map[string]string{
				"path":    "/path/to/file",
				"old_str": "old content",
				"new_str": "new content",
			}

			agentRun := &entity.AgentRun{
				Status: entity.AgentRunStatusRunning,
			}

			// Create a SandboxError
			sandboxErr := &sandboxService.SandboxError{Message: "sandbox execution error"}

			// Mock logs.V1.CtxWarn to avoid actual logging during test
			mockey.Mock(logs.V1.CtxWarn).Return().Build()

			// Mock metrics reporting to verify toolCallStatus
			mockey.MockGeneric(metrics.CodeAssistMetric.ReportAgentToolCallMetrics).Return(func(agentRunID, toolName string, status metrics.AgentToolCallStatus) {
				So(status, ShouldEqual, metrics.AgentToolRunErr)
			}).Build()

			mockAgentRunService.EXPECT().GetAgentRun(gomock.Any(), "test-agent-run-id").Return(agentRun, nil)
			mockAgentRunService.EXPECT().EditFile(gomock.Any(), &service.EditFileOpt{
				UserID:         123,
				ConversationID: "test-conversation-id",
				Path:           "/path/to/file",
				OldStr:         "old content",
				NewStr:         "new content",
			}).Return(nil, sandboxErr)

			result, err := editFileTool.Run(Ctx, toolCtx, input)

			So(err, ShouldBeNil)
			So(result, ShouldNotBeNil)
			editFileResult, ok := result.(*service.EditFileResult)
			So(ok, ShouldBeTrue)
			So(editFileResult.Error, ShouldContainSubstring, "sandbox execution error")
			So(editFileResult.Display, ShouldContainSubstring, "sandbox execution error")
		})

		mockey.PatchConvey("When EditFile succeeds with new_str", func() {
			input := map[string]string{
				"path":    "/path/to/file",
				"old_str": "old content",
				"new_str": "new content",
			}

			agentRun := &entity.AgentRun{
				Status: entity.AgentRunStatusRunning,
			}

			editFileResponse := &sandboxService.EditFileResponse{
				Path:       "/path/to/file",
				FileSize:   100,
				TotalLines: 10,
			}

			// Mock metrics reporting to verify toolCallStatus
			mockey.MockGeneric(metrics.CodeAssistMetric.ReportAgentToolCallMetrics).Return(func(agentRunID, toolName string, status metrics.AgentToolCallStatus) {
				So(status, ShouldEqual, metrics.AgentToolCallSuccess)
			}).Build()

			mockAgentRunService.EXPECT().GetAgentRun(gomock.Any(), "test-agent-run-id").Return(agentRun, nil)
			mockAgentRunService.EXPECT().EditFile(gomock.Any(), &service.EditFileOpt{
				UserID:         123,
				ConversationID: "test-conversation-id",
				Path:           "/path/to/file",
				OldStr:         "old content",
				NewStr:         "new content",
			}).Return(editFileResponse, nil)

			result, err := editFileTool.Run(Ctx, toolCtx, input)

			So(err, ShouldBeNil)
			So(result, ShouldNotBeNil)
			editFileResult, ok := result.(*service.EditFileResult)
			So(ok, ShouldBeTrue)
			So(editFileResult.Error, ShouldEqual, "")
			So(editFileResult.Output, ShouldEqual, "Successfully replaced text. New file size: 10 bytes.")
			So(editFileResult.Display, ShouldEqual, "Successfully replaced text. New file size: 10 bytes.")
		})

		mockey.PatchConvey("When EditFile succeeds with empty new_str", func() {
			input := map[string]string{
				"path":    "/path/to/file",
				"old_str": "old content",
				"new_str": "",
			}

			agentRun := &entity.AgentRun{
				Status: entity.AgentRunStatusRunning,
			}

			editFileResponse := &sandboxService.EditFileResponse{
				Path:       "/path/to/file",
				FileSize:   50,
				TotalLines: 5,
			}
			mockey.MockGeneric(metrics.CodeAssistMetric.ReportAgentToolCallMetrics).Return(func(agentRunID, toolName string, status metrics.AgentToolCallStatus) {
				So(status, ShouldEqual, metrics.AgentToolCallSuccess)
			}).Build()
			mockAgentRunService.EXPECT().GetAgentRun(gomock.Any(), "test-agent-run-id").Return(agentRun, nil)
			mockAgentRunService.EXPECT().EditFile(gomock.Any(), &service.EditFileOpt{
				UserID:         123,
				ConversationID: "test-conversation-id",
				Path:           "/path/to/file",
				OldStr:         "old content",
				NewStr:         "",
			}).Return(editFileResponse, nil)

			result, err := editFileTool.Run(Ctx, toolCtx, input)

			So(err, ShouldBeNil)
			So(result, ShouldNotBeNil)
			editFileResult, ok := result.(*service.EditFileResult)
			So(ok, ShouldBeTrue)
			So(editFileResult.Error, ShouldEqual, "")
			So(editFileResult.Output, ShouldEqual, "Successfully replaced text.\nNOTE: You did not provide `new_str`, so the `old_str` was replaced with an empty string, i.e. deleted. New file size: 5 bytes.")
			So(editFileResult.Display, ShouldEqual, "Successfully replaced text.\nNOTE: You did not provide `new_str`, so the `old_str` was replaced with an empty string, i.e. deleted. New file size: 5 bytes.")
		})

		mockey.PatchConvey("When EditFile succeeds but file becomes empty", func() {
			input := map[string]string{
				"path":    "/path/to/file",
				"old_str": "old content",
				"new_str": "new content",
			}

			agentRun := &entity.AgentRun{
				Status: entity.AgentRunStatusRunning,
			}

			editFileResponse := &sandboxService.EditFileResponse{
				Path:       "/path/to/file",
				FileSize:   0,
				TotalLines: 0,
			}
			mockey.MockGeneric(metrics.CodeAssistMetric.ReportAgentToolCallMetrics).Return(func(agentRunID, toolName string, status metrics.AgentToolCallStatus) {
				So(status, ShouldEqual, metrics.AgentToolCallSuccess)
			}).Build()
			mockAgentRunService.EXPECT().GetAgentRun(gomock.Any(), "test-agent-run-id").Return(agentRun, nil)
			mockAgentRunService.EXPECT().EditFile(gomock.Any(), &service.EditFileOpt{
				UserID:         123,
				ConversationID: "test-conversation-id",
				Path:           "/path/to/file",
				OldStr:         "old content",
				NewStr:         "new content",
			}).Return(editFileResponse, nil)

			result, err := editFileTool.Run(Ctx, toolCtx, input)

			So(err, ShouldBeNil)
			So(result, ShouldNotBeNil)
			editFileResult, ok := result.(*service.EditFileResult)
			So(ok, ShouldBeTrue)
			So(editFileResult.Error, ShouldEqual, "")
			So(editFileResult.Output, ShouldEqual, "Successfully replaced text. New file size: 0 bytes.\nNOTE: The file /path/to/file was removed after becoming empty")
			So(editFileResult.Display, ShouldEqual, "Successfully replaced text. New file size: 0 bytes.\nNOTE: The file /path/to/file was removed after becoming empty")
		})
	})
}

func TestEditFileTool_FormatResult(t *testing.T) {
	Ctrl := gomock.NewController(t)
	defer Ctrl.Finish()

	mockAgentRunService := mockService.NewMockAgentRunService(Ctrl)
	editFileTool := NewEditFile(mockAgentRunService)

	mockey.PatchConvey("Test EditFileTool FormatResult", t, func() {
		mockey.PatchConvey("When result is EditFileResult", func() {
			result := &service.EditFileResult{
				BaseResult: service.BaseResult{
					Output:  "output",
					Error:   "error",
					Display: "display",
				},
			}

			formatted := editFileTool.FormatResult(result)
			expected := "display"

			So(formatted, ShouldEqual, expected)
		})

		mockey.PatchConvey("When result is not EditFileResult", func() {
			result := "not an EditFileResult"

			formatted := editFileTool.FormatResult(result)
			expected := ""

			So(formatted, ShouldEqual, expected)
		})
	})
}

func TestEditFileTool_Name(t *testing.T) {
	Ctrl := gomock.NewController(t)
	defer Ctrl.Finish()

	mockAgentRunService := mockService.NewMockAgentRunService(Ctrl)
	editFileTool := NewEditFile(mockAgentRunService)

	mockey.PatchConvey("Test EditFileTool Name", t, func() {
		So(editFileTool.Name(), ShouldEqual, "edit_file")
	})
}

func TestEditFileTool_Description(t *testing.T) {
	Ctrl := gomock.NewController(t)
	defer Ctrl.Finish()

	mockAgentRunService := mockService.NewMockAgentRunService(Ctrl)
	editFileTool := NewEditFile(mockAgentRunService)

	mockey.PatchConvey("Test EditFileTool Description", t, func() {
		So(editFileTool.Description(), ShouldEqual, "A tool to edit file.")
	})
}

func TestEditFileTool_InputSpec(t *testing.T) {
	Ctrl := gomock.NewController(t)
	defer Ctrl.Finish()

	mockAgentRunService := mockService.NewMockAgentRunService(Ctrl)
	editFileTool := NewEditFile(mockAgentRunService)

	mockey.PatchConvey("Test EditFileTool InputSpec", t, func() {
		spec := editFileTool.InputSpec()

		So(spec.Name, ShouldEqual, "path")
		So(spec.Type, ShouldEqual, "object")
		So(spec.Description, ShouldEqual, "The path of the file to edit.")
		So(spec.Properties, ShouldNotBeNil)
		So(len(spec.Properties), ShouldEqual, 3)

		So(spec.Properties["old_str"].Name, ShouldEqual, "old_str")
		So(spec.Properties["old_str"].Type, ShouldEqual, "string")
		So(spec.Properties["old_str"].Required, ShouldBeTrue)

		So(spec.Properties["new_str"].Name, ShouldEqual, "new_str")
		So(spec.Properties["new_str"].Type, ShouldEqual, "string")
		So(spec.Properties["new_str"].Required, ShouldBeFalse)

		So(spec.Properties["path"].Name, ShouldEqual, "path")
		So(spec.Properties["path"].Type, ShouldEqual, "string")
		So(spec.Properties["path"].Required, ShouldBeTrue)
	})
}
