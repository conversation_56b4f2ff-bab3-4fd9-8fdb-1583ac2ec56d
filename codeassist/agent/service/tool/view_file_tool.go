package tool

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"

	"code.byted.org/gopkg/logs/v2"

	"code.byted.org/devgpt/kiwis/codeassist/agent/entity"
	"code.byted.org/devgpt/kiwis/codeassist/agent/service"
	sandboxService "code.byted.org/devgpt/kiwis/codeassist/sandbox/service"
	"code.byted.org/devgpt/kiwis/lib/metrics"
)

var _ service.Tool = &ViewFileTool{}

type ViewFileToolInput struct {
	FilePath string
	Offset   int
	Limit    int
}

func mapToViewFileToolInput(input map[string]string) *ViewFileToolInput {
	offset, err := strconv.Atoi(input["offset"])
	if err != nil {
		offset = 0
	}
	limit, err := strconv.Atoi(input["limit"])
	if err != nil {
		limit = 200
	}
	return &ViewFileToolInput{
		FilePath: input["file_path"],
		Offset:   offset,
		Limit:    limit,
	}
}

type ViewFileTool struct {
	agentRunService service.AgentRunService
}

func NewViewFileTool(agentRunService service.AgentRunService) *ViewFileTool {
	return &ViewFileTool{
		agentRunService: agentRunService,
	}
}

func (t *ViewFileTool) Name() string {
	return "view_file"
}

func (t *ViewFileTool) Description() string {
	return "A tool to view file."
}

func (t *ViewFileTool) Run(ctx context.Context, toolCtx service.ToolContext, input map[string]string) (any, error) {
	result := &service.ViewFileResult{
		BaseResult: service.BaseResult{},
	}
	reportToolCallMetricsFunc := metrics.CodeAssistMetric.ReportAgentToolCallMetrics()
	toolCallStatus := metrics.AgentToolCallSuccess
	defer func() {
		reportToolCallMetricsFunc(toolCtx.AgentName, t.Name(), toolCallStatus)
	}()
	if input == nil {
		toolCallStatus = metrics.AgentToolCallErr
		result.Error = "view_file tool input params is null"
		result.Display = result.Error
		return result, nil
	}
	inputStruct := mapToViewFileToolInput(input)
	if inputStruct.FilePath == "" {
		toolCallStatus = metrics.AgentToolCallErr
		result.Error = "view_file tool input params field `file_path` is nil"
		result.Display = result.Error
		return result, nil
	}

	unsupportedExts := []string{".xlsx", ".xls", ".csv", ".sql", ".json"}
	for _, ext := range unsupportedExts {
		if strings.HasSuffix(inputStruct.FilePath, ext) {
			logs.V1.CtxWarn(ctx, "view_file tool file path not supported, file path: %s", inputStruct.FilePath)
			toolCallStatus = metrics.AgentToolCallErr
			result.Error = fmt.Sprintf("数据文件无法读取，请使用 `data_analysis` 工具进行读取并分析: %s", inputStruct.FilePath)
			result.Display = result.Error
			return result, nil
		}
	}

	agentRun, err := t.agentRunService.GetAgentRun(ctx, toolCtx.AgentRunID)
	if err != nil {
		toolCallStatus = metrics.AgentToolCallErr
		return nil, err
	}
	if agentRun == nil || agentRun.Status < entity.AgentRunStatusRunning {
		toolCallStatus = metrics.AgentToolCallErr
		return nil, service.ToolCallNotReadyError
	}
	logs.V1.CtxInfo(ctx, "view_file tool run, file path: %s, offset: %d, limit: %d, conversation id: %s", inputStruct.FilePath, inputStruct.Offset, inputStruct.Limit, toolCtx.ConversationID)
	resp, err := t.agentRunService.ReadFile(ctx, &service.ReadFileOpt{
		UserID:         toolCtx.UserID,
		ConversationID: toolCtx.ConversationID,
		Path:           inputStruct.FilePath,
		Offset:         inputStruct.Offset,
		Limit:          inputStruct.Limit,
	})

	if err == nil {
		var output strings.Builder
		// output.WriteString(fmt.Sprintf("filePath: %s #startLine: %d #endLine: %d", inputStruct.FilePath, inputStruct.Offset+1, inputStruct.Offset+1+inputStruct.Limit))
		// output.WriteString("\n" + string(resp.Content))
		output.WriteString(string(resp.Content))
		if resp.LineCount < resp.TotalLines-inputStruct.Offset {
			output.WriteString(fmt.Sprintf("\n\n[... %d more lines not shown. Use 'offset' and 'limit' parameters to read more.]", resp.TotalLines-inputStruct.Offset-resp.LineCount))
		}
		result.Output = output.String()
		result.Display = result.Output
		return result, nil
	}

	logs.V1.CtxWarn(ctx, "view_file tool failed, err: %+v", err)
	// tool run err
	var sandboxErr *sandboxService.SandboxError
	if errors.As(err, &sandboxErr) {
		toolCallStatus = metrics.AgentToolRunErr
		result.Error = err.Error()
		result.Display = result.Error
		return result, nil
	}

	toolCallStatus = metrics.AgentToolCallErr
	return nil, err
}

func (t *ViewFileTool) FormatResult(result any) string {
	if res, ok := result.(*service.ViewFileResult); ok {
		return res.Display
	}
	return ""
}

func (t *ViewFileTool) InputSpec() service.Schema {
	return service.Schema{
		Name:        "view file params schema",
		Type:        "object",
		Description: "The path of the file to view.",
		Properties: map[string]service.Schema{
			"file_path": {
				Name:        "file_path",
				Type:        "string",
				Description: "必填参数，文件的绝对路径，例如 `/mnt/workspace/file.py`",
				Required:    true,
			},
			"offset": {
				Name:        "offset",
				Type:        "integer",
				Description: "可选参数，从第几行开始读取，默认是 0，一般在文件内容过长时使用",
				Default:     0,
			},
			"limit": {
				Name:        "limit",
				Type:        "integer",
				Description: "可选参数，读取多少行，默认是 {MAX_LINES_TO_READ} 行，一般在文件内容过长时使用",
				Default:     200,
			},
		},
	}
}
