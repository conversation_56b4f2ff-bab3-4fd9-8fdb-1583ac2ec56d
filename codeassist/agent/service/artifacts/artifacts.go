package artifacts

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"time"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/overpass/common/option/calloption"
	"code.byted.org/overpass/flow_samantha_misc/kitex_gen/code"
	commonartifact "code.byted.org/overpass/flow_samantha_misc/kitex_gen/flow/samantha/common/artifact"
	"code.byted.org/overpass/flow_samantha_misc/kitex_gen/flow/samantha/misc"
	"code.byted.org/overpass/flow_samantha_misc/kitex_gen/flow/samantha/misc/artifact"
	"code.byted.org/overpass/flow_samantha_misc/rpc/flow_samantha_misc"
	"code.byted.org/security/go-polaris/request"
	"github.com/cenkalti/backoff/v4"
	"github.com/google/uuid"
	"github.com/pkg/errors"

	"code.byted.org/devgpt/kiwis/codeassist/agent/entity"
	"code.byted.org/devgpt/kiwis/codeassist/agent/service"
	"code.byted.org/devgpt/kiwis/lib/util"
	"code.byted.org/devgpt/kiwis/port/flowresource"
)

const maxDownloadSize = 200 * 1024 * 1024

// agent 产物管理，实现产物上传
var _ service.ArtifactsService = &Service{}

type Service struct {
	FlowResourceClient *flowresource.SDKClient `name:"agent_flow_resource_client"`
}

func (s *Service) CreateDoubaoArtifact(ctx context.Context, opt *service.CreateDoubaoArtifactOption) (*entity.DoubaoArtifact, error) {
	messageID, err := strconv.ParseInt(opt.MessageID, 10, 64)
	if err != nil {
		logs.V1.CtxError(ctx, "invalid message id: %s", opt.MessageID)
		return nil, err
	}
	conversationID, err := strconv.ParseInt(opt.ConversationID, 10, 64)
	if err != nil {
		logs.V1.CtxError(ctx, "invalid conversation id: %s", opt.ConversationID)
		return nil, err
	}
	answerID, err := strconv.ParseInt(opt.AnswerID, 10, 64)
	if err != nil {
		logs.V1.CtxError(ctx, "invalid answer id: %s", opt.AnswerID)
		return nil, err
	}

	// 创建code文件
	codeID, version, err := s.CreateCodeArtifact(ctx, &misc.CreateCodeArtifactRequest{
		CreatorId: opt.UserID,
		Title:     &opt.Title,
		SourceMeta: &code.CodeSourceMeta{
			ConversationId:    &conversationID,
			AnswerMessageId:   &answerID,
			QuestionMessageId: &messageID,
		},
	})
	if err != nil {
		return nil, err
	}

	identifier := uuid.NewString()
	artifactEntity := &artifact.CreateArtifactRequest{
		UserId:            opt.UserID,
		QuestionMessageId: messageID,
		ConversationId:    conversationID,
		ArtifactId:        identifier,
		Type:              "webview",
		Title:             opt.Title,
		ResourceType:      int64(commonartifact.ArtifactResourceType_Code),
		ResourceId:        strconv.FormatInt(codeID, 10),
		ResourceVersion:   conv.Int32Ptr(version),
		Status:            conv.Int64Ptr(2),
	}

	id, err := s.CreateArtifact(ctx, artifactEntity)
	if err != nil {
		return nil, err
	}

	// 构造根artifact
	result := &entity.DoubaoArtifact{
		ResourceId:        strconv.FormatInt(codeID, 10),
		ArtifactMetaId:    strconv.FormatInt(id, 10),
		ArtifactVersionId: "",
		ConversationId:    opt.ConversationID,
		Version:           1,
		ResourceVersion:   int64(version),
		ArtifactTopic:     identifier,
		Title:             opt.Title,
	}
	return result, nil
}

func (s *Service) CreateArtifact(ctx context.Context, req *artifact.CreateArtifactRequest) (id int64, err error) {
	resp := &artifact.CreateArtifactResponse{}

	resp, err = flow_samantha_misc.RawCall.CreateArtifact(ctx, req)

	if err != nil {
		logs.CtxError(ctx, "[samanthaMiscService] CreateArtifact error. err: %v, req: %v", err, util.MarshalStruct(req))
		return 0, service.ErrSamanthaInternalError
	}
	if resp == nil {
		logs.CtxError(ctx, "[samanthaMiscService] CreateArtifact resp is nil. err: %v, req: %v", err, util.MarshalStruct(req))
		return 0, service.ErrSamanthaInternalError
	}
	if baseResp := resp.GetBaseResp(); baseResp != nil && baseResp.StatusCode != 0 {
		logs.CtxError(ctx, "[samanthaMiscService] CreateArtifact status error. code: %v, msg: %v", baseResp.GetStatusCode(), baseResp.GetStatusMessage())
		return 0, service.ErrSamanthaInternalError
	}
	return resp.GetId(), nil
}

func (s *Service) CreateCodeArtifact(ctx context.Context, req *misc.CreateCodeArtifactRequest) (id int64, version int32, err error) {
	resp, err := flow_samantha_misc.RawCall.CreateCodeArtifact(ctx, req, calloption.WithReqRespLogsInfo())
	if err != nil {
		return 0, 0, errors.WithStack(err)
	}
	return resp.CodeId, resp.Version, nil
}

func (s *Service) UploadFile(ctx context.Context, userID int64, filename string, content []byte) (string, error) {
	uri, err := s.FlowResourceClient.UploadFileResource(ctx, filename, content, userID)
	if err != nil {
		return "", errors.Errorf("[UploadFile] upload image failed, err: %v", err)
	}
	logs.V1.CtxInfo(ctx, "[UploadFile] upload image success. uri: %v", uri)
	return uri, nil
}

func (s *Service) DownloadFile(ctx context.Context, uri string, userID int64) ([]byte, error) {
	imgURL, backImgURL, err := s.FlowResourceClient.SignResourceURL(ctx, uri, userID)
	if err != nil {
		return nil, errors.Errorf("[GetImageURLFromBytes] get doubao url failed, err: %v", err)
	}

	var content []byte
	backoffConf := backoff.NewExponentialBackOff()
	backoffConf.InitialInterval = time.Duration(50) * time.Millisecond
	err = backoff.Retry(func() error {
		content, err = s.DownloadFileToMemory(ctx, imgURL)
		if err == nil {
			return nil
		}

		// 失败后直接尝试从另一个域名下载，而不是一个域名重试完成后再重试另一个域名
		content, err = s.DownloadFileToMemory(ctx, backImgURL)
		return err
	}, backoff.WithMaxRetries(backoffConf, 2))

	if err != nil {
		logs.V1.CtxError(ctx, "agent download file: %s failed, err: %v", uri, err)
		return nil, err
	}
	return content, nil
}

func (s *Service) DownloadFileToMemory(ctx context.Context, url string) ([]byte, error) {
	client := request.NewClient()
	resp, err := client.Get(url)
	if err != nil {
		logs.V1.CtxWarn(ctx, "failed to download file from CDN URL: %s with err: %v", url, err)
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		logs.V1.CtxWarn(ctx, "failed to download file from CDN URL: %s with err: %v", url, err)
		return nil, fmt.Errorf("imageX URL: %s, status code is not 200. code: %s", url, resp.Status)
	}

	limitedReader := io.LimitReader(resp.Body, maxDownloadSize)

	var buf bytes.Buffer
	_, err = io.Copy(&buf, limitedReader)
	if err != nil {
		return nil, err
	}

	logs.V1.CtxInfo(ctx, "download resource file with size: %d", len(buf.Bytes()))

	return buf.Bytes(), nil
}
