package service

import (
	"context"

	"code.byted.org/devgpt/kiwis/codeassist/agent/entity"
	sandboxService "code.byted.org/devgpt/kiwis/codeassist/sandbox/service"
)

type UploadType string

const (
	UploadTypeUserInputFile       UploadType = "user_input_file"
	UploadTypeAgentOutputFile     UploadType = "agent_output_file"
	UploadTypeAgentOutputArtifact UploadType = "agent_output_artifact"
)

//go:generate go run go.uber.org/mock/mockgen@latest -destination ../mock/service/agent_run_service.go -package service code.byted.org/devgpt/kiwis/codeassist/agent/service AgentRunService
type AgentRunService interface {
	// 创建 AgentRun 实例
	CreateOrGet(ctx context.Context, opt *CreateOrGetOption) (*entity.AgentRun, bool, error) // default status: created   1. 创建或者获取 agent run 实例; 2. bind sandbox，返回是否新建的sandbox 标识

	// 获取 AgentRun 实例
	GetAgentRun(ctx context.Context, agentRunID string) (*entity.AgentRun, error)

	// 更新 AgentRun 状态
	UpdateAgentRunStatus(ctx context.Context, agentRunID string, status entity.AgentRunStatus) error

	// sandbox 服务
	ReleaseSandbox(ctx context.Context, userID int64, conversationID string) error
	UploadFile(ctx context.Context, opt *UploadFileOption) error
	DownloadFile(ctx context.Context, opt *DownloadFileOption) (map[string][]byte, error)
	CreateFile(ctx context.Context, opt *CreateFileOpt) error
	ReadFile(ctx context.Context, opt *ReadFileOpt) (*sandboxService.ReadFileResponse, error)
	EditFile(ctx context.Context, opt *EditFileOpt) (*sandboxService.EditFileResponse, error)
	ExecuteCode(ctx context.Context, opt *ExecuteCodeOpt) (*sandboxService.ExecuteResponse, error)
}

type CreateOrGetOption struct {
	TaskID         string
	UserID         int64
	ConversationID string
	AgentID        string
	AgentVersion   string
	AgentMeta      *entity.AgentRunMeta
	AgentInput     *entity.AgentRunInput
}

type UploadFileOption struct {
	UserID         int64
	ConversationID string
	Files          map[string][]byte
}

type DownloadFileOption struct {
	UserID         int64
	ConversationID string
	Files          []string
}

type CreateFileOpt struct {
	UserID         int64
	ConversationID string
	Path           string
	Content        []byte
}

type ReadFileOpt struct {
	UserID         int64
	ConversationID string
	Path           string
	Offset         int
	Limit          int
}

type EditFileOpt struct {
	UserID         int64
	ConversationID string
	Path           string
	OldStr         string
	NewStr         string
}

type ExecuteCodeOpt struct {
	UserID             int64
	ConversationID     string
	Code               string
	CopyRuntimeTimeout float64
	CompileTimeout     float64
	RunTimeout         float64
	Stdin              string
	Language           string
	Files              map[string]*string
	FetchFiles         []string
	LinkPrefix         string
	ShareContext       bool
}
