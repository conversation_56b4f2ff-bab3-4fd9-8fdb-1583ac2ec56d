package memory

import (
	"context"
	"errors"
	"testing"

	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"
	"go.uber.org/mock/gomock"

	"code.byted.org/devgpt/kiwis/codeassist/agent/entity"
	"code.byted.org/devgpt/kiwis/codeassist/agent/mock/dal"
	"code.byted.org/devgpt/kiwis/codeassist/agent/service"
	dbmock "code.byted.org/devgpt/kiwis/port/db/mock"
	dkmsmock "code.byted.org/devgpt/kiwis/port/dkms/mock"
)

func TestMemoryServiceImpl_SaveAgentRunStep(t *testing.T) {
	mockey.PatchConvey("TestMemoryServiceImpl_SaveAgentRunStep", t, func() {
		ctrl := gomock.NewController(t)
		runStepDao := dal.NewMockAgentRunStepDAO(ctrl)
		runStepDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return("123", nil)
		memoryService := NewMemoryServiceImpl(nil, nil, runStepDao, nil, nil, nil)
		uid, err := memoryService.SaveAgentRunStep(context.Background(), &service.SaveAgentRunStepOption{
			AgentRunStep: &entity.AgentRunStep{},
		})
		convey.So(err, convey.ShouldBeNil)
		convey.So(uid, convey.ShouldEqual, "123")
	})

	mockey.PatchConvey("TestMemoryServiceImpl_SaveAgentRunStep_NilAgentRunStep", t, func() {
		memoryService := NewMemoryServiceImpl(nil, nil, nil, nil, nil, nil)
		uid, err := memoryService.SaveAgentRunStep(context.Background(), &service.SaveAgentRunStepOption{
			AgentRunStep: nil,
		})
		convey.So(err, convey.ShouldNotBeNil)
		convey.So(err.Error(), convey.ShouldContainSubstring, "agent run step is nil")
		convey.So(uid, convey.ShouldEqual, "")
	})

	mockey.PatchConvey("TestMemoryServiceImpl_SaveAgentRunStep_DAOError", t, func() {
		ctrl := gomock.NewController(t)
		runStepDao := dal.NewMockAgentRunStepDAO(ctrl)
		runStepDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return("", errors.New("dbmock error"))
		memoryService := NewMemoryServiceImpl(nil, nil, runStepDao, nil, nil, nil)
		uid, err := memoryService.SaveAgentRunStep(context.Background(), &service.SaveAgentRunStepOption{
			AgentRunStep: &entity.AgentRunStep{},
		})
		convey.So(err, convey.ShouldNotBeNil)
		convey.So(err.Error(), convey.ShouldContainSubstring, "dbmock error")
		convey.So(uid, convey.ShouldEqual, "")
	})
}

func TestMemoryServiceImpl_UpdateAgentRunStep(t *testing.T) {
	mockey.PatchConvey("TestMemoryServiceImpl_UpdateAgentRunStep_StatusOnly", t, func() {
		ctrl := gomock.NewController(t)
		runStepDao := dal.NewMockAgentRunStepDAO(ctrl)
		runStepDao.EXPECT().UpdateByID(gomock.Any(), "test-step-id", entity.AgentRunStepStatusCompleted, gomock.Any()).Return(nil)
		memoryService := NewMemoryServiceImpl(nil, nil, runStepDao, nil, nil, nil)
		err := memoryService.UpdateAgentRunStep(context.Background(), &service.UpdateAgentRunStepOption{
			AgentRunStepID: "test-step-id",
			Status:         entity.AgentRunStepStatusCompleted,
			Metadata:       &entity.AgentRunStepMetaData{},
		})
		convey.So(err, convey.ShouldBeNil)
	})

	mockey.PatchConvey("TestMemoryServiceImpl_UpdateAgentRunStep_StatusError", t, func() {
		ctrl := gomock.NewController(t)
		runStepDao := dal.NewMockAgentRunStepDAO(ctrl)
		runStepDao.EXPECT().UpdateByID(gomock.Any(), "test-step-id", entity.AgentRunStepStatusCompleted, gomock.Any()).Return(errors.New("update error"))
		memoryService := NewMemoryServiceImpl(nil, nil, runStepDao, nil, nil, nil)
		err := memoryService.UpdateAgentRunStep(context.Background(), &service.UpdateAgentRunStepOption{
			AgentRunStepID: "test-step-id",
			Status:         entity.AgentRunStepStatusCompleted,
			Metadata:       &entity.AgentRunStepMetaData{},
		})
		convey.So(err, convey.ShouldNotBeNil)
		convey.So(err.Error(), convey.ShouldContainSubstring, "update error")
	})

	mockey.PatchConvey("TestMemoryServiceImpl_UpdateAgentRunStep_SaveMessage", t, func() {
		ctrl := gomock.NewController(t)
		dkmsClient := dkmsmock.NewMockClient(ctrl)
		runStepMessageDao := dal.NewMockAgentRunStepMessageDAO(ctrl)
		dkmsClient.EXPECT().Encrypt("test-message").Return("encrypted-message", nil)
		runStepMessageDao.EXPECT().Set(gomock.Any(), "test-step-id", "encrypted-message").Return(nil)
		memoryService := NewMemoryServiceImpl(nil, dkmsClient, nil, runStepMessageDao, nil, nil)
		err := memoryService.UpdateAgentRunStep(context.Background(), &service.UpdateAgentRunStepOption{
			AgentRunStepID: "test-step-id",
			RawMessage:     "test-message",
		})
		convey.So(err, convey.ShouldBeNil)
	})

	mockey.PatchConvey("TestMemoryServiceImpl_UpdateAgentRunStep_EncryptError", t, func() {
		ctrl := gomock.NewController(t)
		dkmsClient := dkmsmock.NewMockClient(ctrl)
		dkmsClient.EXPECT().Encrypt("test-message").Return("", errors.New("encrypt error"))
		memoryService := NewMemoryServiceImpl(nil, dkmsClient, nil, nil, nil, nil)
		err := memoryService.UpdateAgentRunStep(context.Background(), &service.UpdateAgentRunStepOption{
			AgentRunStepID: "test-step-id",
			RawMessage:     "test-message",
		})
		convey.So(err, convey.ShouldNotBeNil)
		convey.So(err.Error(), convey.ShouldContainSubstring, "encrypt error")
	})

	mockey.PatchConvey("TestMemoryServiceImpl_UpdateAgentRunStep_SaveMessageError", t, func() {
		ctrl := gomock.NewController(t)
		dkmsClient := dkmsmock.NewMockClient(ctrl)
		runStepMessageDao := dal.NewMockAgentRunStepMessageDAO(ctrl)
		dkmsClient.EXPECT().Encrypt("test-message").Return("encrypted-message", nil)
		runStepMessageDao.EXPECT().Set(gomock.Any(), "test-step-id", "encrypted-message").Return(errors.New("save message error"))
		memoryService := NewMemoryServiceImpl(nil, dkmsClient, nil, runStepMessageDao, nil, nil)
		err := memoryService.UpdateAgentRunStep(context.Background(), &service.UpdateAgentRunStepOption{
			AgentRunStepID: "test-step-id",
			RawMessage:     "test-message",
		})
		convey.So(err, convey.ShouldNotBeNil)
		convey.So(err.Error(), convey.ShouldContainSubstring, "save message error")
	})
}

func TestMemoryServiceImpl_AddToolCall(t *testing.T) {
	mockey.PatchConvey("TestMemoryServiceImpl_AddToolCall_NilToolCall", t, func() {
		memoryService := NewMemoryServiceImpl(nil, nil, nil, nil, nil, nil)
		err := memoryService.AddToolCall(context.Background(), nil)
		convey.So(err, convey.ShouldNotBeNil)
		convey.So(err.Error(), convey.ShouldContainSubstring, "tool call is nil")
	})

	mockey.PatchConvey("TestMemoryServiceImpl_AddToolCall_Success", t, func() {
		ctrl := gomock.NewController(t)
		dbClient := dbmock.NewMockClient(ctrl)
		toolCallDao := dal.NewMockAgentRunStepToolCallDAO(ctrl)
		dkmsClient := dkmsmock.NewMockClient(ctrl)
		toolCallObservationDao := dal.NewMockToolCallObservationDAO(ctrl)

		toolCall := &entity.AgentRunStepToolCall{
			UUID:           "test-uuid",
			AgentRunStepID: "test-step-id",
			ToolName:       "test-tool",
			Index:          1,
			Observation:    "test-observation",
		}

		toolCallDao.EXPECT().Create(gomock.Any(), toolCall).Return("test-uuid", nil)
		dkmsClient.EXPECT().Encrypt("test-observation").Return("encrypted-observation", nil)
		toolCallObservationDao.EXPECT().Set(gomock.Any(), "test-uuid", "encrypted-observation").Return(nil)

		memoryService := NewMemoryServiceImpl(dbClient, dkmsClient, nil, nil, toolCallDao, toolCallObservationDao)
		err := memoryService.AddToolCall(context.Background(), toolCall)
		convey.So(err, convey.ShouldBeNil)
	})

	mockey.PatchConvey("TestMemoryServiceImpl_AddToolCall_CreateError", t, func() {
		ctrl := gomock.NewController(t)
		dbClient := dbmock.NewMockClient(ctrl)
		toolCallDao := dal.NewMockAgentRunStepToolCallDAO(ctrl)

		toolCall := &entity.AgentRunStepToolCall{
			UUID:           "test-uuid",
			AgentRunStepID: "test-step-id",
			ToolName:       "test-tool",
			Index:          1,
			Observation:    "test-observation",
		}

		toolCallDao.EXPECT().Create(gomock.Any(), toolCall).Return("", errors.New("create error"))

		memoryService := NewMemoryServiceImpl(dbClient, nil, nil, nil, toolCallDao, nil)
		err := memoryService.AddToolCall(context.Background(), toolCall)
		convey.So(err, convey.ShouldNotBeNil)
		convey.So(err.Error(), convey.ShouldContainSubstring, "create error")
	})

	mockey.PatchConvey("TestMemoryServiceImpl_AddToolCall_EncryptError", t, func() {
		ctrl := gomock.NewController(t)
		dbClient := dbmock.NewMockClient(ctrl)
		toolCallDao := dal.NewMockAgentRunStepToolCallDAO(ctrl)
		dkmsClient := dkmsmock.NewMockClient(ctrl)

		toolCall := &entity.AgentRunStepToolCall{
			UUID:           "test-uuid",
			AgentRunStepID: "test-step-id",
			ToolName:       "test-tool",
			Index:          1,
			Observation:    "test-observation",
		}

		toolCallDao.EXPECT().Create(gomock.Any(), toolCall).Return("test-uuid", nil)
		dkmsClient.EXPECT().Encrypt("test-observation").Return("", errors.New("encrypt error"))

		memoryService := NewMemoryServiceImpl(dbClient, dkmsClient, nil, nil, toolCallDao, nil)
		err := memoryService.AddToolCall(context.Background(), toolCall)
		convey.So(err, convey.ShouldNotBeNil)
		convey.So(err.Error(), convey.ShouldContainSubstring, "encrypt error")
	})

	mockey.PatchConvey("TestMemoryServiceImpl_AddToolCall_SaveObservationError", t, func() {
		ctrl := gomock.NewController(t)
		dbClient := dbmock.NewMockClient(ctrl)
		toolCallDao := dal.NewMockAgentRunStepToolCallDAO(ctrl)
		dkmsClient := dkmsmock.NewMockClient(ctrl)
		toolCallObservationDao := dal.NewMockToolCallObservationDAO(ctrl)

		toolCall := &entity.AgentRunStepToolCall{
			UUID:           "test-uuid",
			AgentRunStepID: "test-step-id",
			ToolName:       "test-tool",
			Index:          1,
			Observation:    "test-observation",
		}
		toolCallDao.EXPECT().Create(gomock.Any(), toolCall).Return("test-uuid", nil)
		dkmsClient.EXPECT().Encrypt("test-observation").Return("encrypted-observation", nil)
		toolCallObservationDao.EXPECT().Set(gomock.Any(), "test-uuid", "encrypted-observation").Return(errors.New("save observation error"))

		memoryService := NewMemoryServiceImpl(dbClient, dkmsClient, nil, nil, toolCallDao, toolCallObservationDao)
		err := memoryService.AddToolCall(context.Background(), toolCall)
		convey.So(err, convey.ShouldNotBeNil)
		convey.So(err.Error(), convey.ShouldContainSubstring, "save observation error")
	})
}

func TestMemoryServiceImpl_GetAgentRunSteps(t *testing.T) {
	mockey.PatchConvey("TestMemoryServiceImpl_GetAgentRunSteps_Success", t, func() {
		ctrl := gomock.NewController(t)
		runStepDao := dal.NewMockAgentRunStepDAO(ctrl)
		runStepMessageDao := dal.NewMockAgentRunStepMessageDAO(ctrl)
		toolCallDao := dal.NewMockAgentRunStepToolCallDAO(ctrl)
		toolCallObservationDao := dal.NewMockToolCallObservationDAO(ctrl)
		dkmsClient := dkmsmock.NewMockClient(ctrl)

		// 准备测试数据
		runSteps := []*entity.AgentRunStep{
			{
				UUID:       "step-1",
				AgentRunID: "run-1",
				Actor:      "actor-1",
				Round:      1,
			},
			{
				UUID:       "step-2",
				AgentRunID: "run-1",
				Actor:      "actor-2",
				Round:      2,
			},
		}

		toolCalls := []*entity.AgentRunStepToolCall{
			{
				UUID:           "tool-1",
				AgentRunStepID: "step-1",
				ToolName:       "tool-1",
				Index:          1,
			},
		}

		// 设置 mock 行为
		runStepDao.EXPECT().GetByAgentRunID(gomock.Any(), "run-1", entity.AgentRunStepStatusCompleted).Return(runSteps, nil)

		runStepMessageDao.EXPECT().Get(gomock.Any(), "step-1").Return("encrypted-message-1", nil)
		runStepMessageDao.EXPECT().Get(gomock.Any(), "step-2").Return("encrypted-message-2", nil)

		dkmsClient.EXPECT().Decrypt("encrypted-message-1").Return("raw-message-1", nil)
		dkmsClient.EXPECT().Decrypt("encrypted-message-2").Return("raw-message-2", nil)

		toolCallDao.EXPECT().GetByAgentRunStepID(gomock.Any(), "step-1").Return(toolCalls, nil)
		toolCallDao.EXPECT().GetByAgentRunStepID(gomock.Any(), "step-2").Return([]*entity.AgentRunStepToolCall{}, nil)

		toolCallObservationDao.EXPECT().Get(gomock.Any(), "tool-1").Return("encrypted-observation-1", nil)
		dkmsClient.EXPECT().Decrypt("encrypted-observation-1").Return("raw-observation-1", nil)

		// 执行测试
		memoryService := NewMemoryServiceImpl(nil, dkmsClient, runStepDao, runStepMessageDao, toolCallDao, toolCallObservationDao)
		result, err := memoryService.GetAgentRunSteps(context.Background(), &service.GetAgentRunStepOption{
			AgentRunID: "run-1",
		})

		// 验证结果
		convey.So(err, convey.ShouldBeNil)
		convey.So(result, convey.ShouldHaveLength, 2)
		convey.So(result[0].RawMessage, convey.ShouldEqual, "raw-message-1")
		convey.So(result[1].RawMessage, convey.ShouldEqual, "raw-message-2")
		convey.So(result[0].AgentRunStepToolCallList, convey.ShouldHaveLength, 1)
		convey.So(result[0].AgentRunStepToolCallList[0].Observation, convey.ShouldEqual, "raw-observation-1")
	})

	mockey.PatchConvey("TestMemoryServiceImpl_GetAgentRunSteps_GetRunStepsError", t, func() {
		ctrl := gomock.NewController(t)
		runStepDao := dal.NewMockAgentRunStepDAO(ctrl)

		// 设置 mock 行为
		runStepDao.EXPECT().GetByAgentRunID(gomock.Any(), "run-1", entity.AgentRunStepStatusCompleted).Return(nil, errors.New("get run steps error"))

		// 执行测试
		memoryService := NewMemoryServiceImpl(nil, nil, runStepDao, nil, nil, nil)
		result, err := memoryService.GetAgentRunSteps(context.Background(), &service.GetAgentRunStepOption{
			AgentRunID: "run-1",
		})

		// 验证结果
		convey.So(err, convey.ShouldNotBeNil)
		convey.So(err.Error(), convey.ShouldContainSubstring, "get run steps error")
		convey.So(result, convey.ShouldBeNil)
	})

	mockey.PatchConvey("TestMemoryServiceImpl_GetAgentRunSteps_GetMessageError", t, func() {
		ctrl := gomock.NewController(t)
		runStepDao := dal.NewMockAgentRunStepDAO(ctrl)
		runStepMessageDao := dal.NewMockAgentRunStepMessageDAO(ctrl)

		// 准备测试数据
		runSteps := []*entity.AgentRunStep{
			{
				UUID:       "step-1",
				AgentRunID: "run-1",
				Actor:      "actor-1",
				Round:      1,
			},
		}

		// 设置 mock 行为
		runStepDao.EXPECT().GetByAgentRunID(gomock.Any(), "run-1", entity.AgentRunStepStatusCompleted).Return(runSteps, nil)
		runStepMessageDao.EXPECT().Get(gomock.Any(), "step-1").Return("", errors.New("get message error"))

		// 执行测试
		memoryService := NewMemoryServiceImpl(nil, nil, runStepDao, runStepMessageDao, nil, nil)
		result, err := memoryService.GetAgentRunSteps(context.Background(), &service.GetAgentRunStepOption{
			AgentRunID: "run-1",
		})

		// 验证结果
		convey.So(err, convey.ShouldNotBeNil)
		convey.So(err.Error(), convey.ShouldContainSubstring, "get message error")
		convey.So(result, convey.ShouldBeNil)
	})

	mockey.PatchConvey("TestMemoryServiceImpl_GetAgentRunSteps_DecryptError", t, func() {
		ctrl := gomock.NewController(t)
		runStepDao := dal.NewMockAgentRunStepDAO(ctrl)
		runStepMessageDao := dal.NewMockAgentRunStepMessageDAO(ctrl)
		dkmsClient := dkmsmock.NewMockClient(ctrl)

		// 准备测试数据
		runSteps := []*entity.AgentRunStep{
			{
				UUID:       "step-1",
				AgentRunID: "run-1",
				Actor:      "actor-1",
				Round:      1,
			},
		}

		// 设置 mock 行为
		runStepDao.EXPECT().GetByAgentRunID(gomock.Any(), "run-1", entity.AgentRunStepStatusCompleted).Return(runSteps, nil)
		runStepMessageDao.EXPECT().Get(gomock.Any(), "step-1").Return("encrypted-message-1", nil)
		dkmsClient.EXPECT().Decrypt("encrypted-message-1").Return("", errors.New("decrypt error"))

		// 执行测试
		memoryService := NewMemoryServiceImpl(nil, dkmsClient, runStepDao, runStepMessageDao, nil, nil)
		result, err := memoryService.GetAgentRunSteps(context.Background(), &service.GetAgentRunStepOption{
			AgentRunID: "run-1",
		})

		// 验证结果
		convey.So(err, convey.ShouldNotBeNil)
		convey.So(err.Error(), convey.ShouldContainSubstring, "decrypt error")
		convey.So(result, convey.ShouldBeNil)
	})

	mockey.PatchConvey("TestMemoryServiceImpl_GetAgentRunSteps_GetToolCallsError", t, func() {
		ctrl := gomock.NewController(t)
		runStepDao := dal.NewMockAgentRunStepDAO(ctrl)
		runStepMessageDao := dal.NewMockAgentRunStepMessageDAO(ctrl)
		toolCallDao := dal.NewMockAgentRunStepToolCallDAO(ctrl)
		dkmsClient := dkmsmock.NewMockClient(ctrl)

		// 准备测试数据
		runSteps := []*entity.AgentRunStep{
			{
				UUID:       "step-1",
				AgentRunID: "run-1",
				Actor:      "actor-1",
				Round:      1,
			},
		}

		// 设置 mock 行为
		runStepDao.EXPECT().GetByAgentRunID(gomock.Any(), "run-1", entity.AgentRunStepStatusCompleted).Return(runSteps, nil)
		runStepMessageDao.EXPECT().Get(gomock.Any(), "step-1").Return("encrypted-message-1", nil)
		dkmsClient.EXPECT().Decrypt("encrypted-message-1").Return("raw-message-1", nil)
		toolCallDao.EXPECT().GetByAgentRunStepID(gomock.Any(), "step-1").Return(nil, errors.New("get tool calls error"))

		// 执行测试
		memoryService := NewMemoryServiceImpl(nil, dkmsClient, runStepDao, runStepMessageDao, toolCallDao, nil)
		result, err := memoryService.GetAgentRunSteps(context.Background(), &service.GetAgentRunStepOption{
			AgentRunID: "run-1",
		})

		// 验证结果
		convey.So(err, convey.ShouldNotBeNil)
		convey.So(err.Error(), convey.ShouldContainSubstring, "get tool calls error")
		convey.So(result, convey.ShouldBeNil)
	})

	mockey.PatchConvey("TestMemoryServiceImpl_GetAgentRunSteps_GetToolCallObservationError", t, func() {
		ctrl := gomock.NewController(t)
		runStepDao := dal.NewMockAgentRunStepDAO(ctrl)
		runStepMessageDao := dal.NewMockAgentRunStepMessageDAO(ctrl)
		toolCallDao := dal.NewMockAgentRunStepToolCallDAO(ctrl)
		toolCallObservationDao := dal.NewMockToolCallObservationDAO(ctrl)
		dkmsClient := dkmsmock.NewMockClient(ctrl)

		// 准备测试数据
		runSteps := []*entity.AgentRunStep{
			{
				UUID:       "step-1",
				AgentRunID: "run-1",
				Actor:      "actor-1",
				Round:      1,
			},
		}

		toolCalls := []*entity.AgentRunStepToolCall{
			{
				UUID:           "tool-1",
				AgentRunStepID: "step-1",
				ToolName:       "tool-1",
				Index:          1,
			},
		}

		// 设置 mock 行为
		runStepDao.EXPECT().GetByAgentRunID(gomock.Any(), "run-1", entity.AgentRunStepStatusCompleted).Return(runSteps, nil)
		runStepMessageDao.EXPECT().Get(gomock.Any(), "step-1").Return("encrypted-message-1", nil)
		dkmsClient.EXPECT().Decrypt("encrypted-message-1").Return("raw-message-1", nil)
		toolCallDao.EXPECT().GetByAgentRunStepID(gomock.Any(), "step-1").Return(toolCalls, nil)
		toolCallObservationDao.EXPECT().Get(gomock.Any(), "tool-1").Return("", errors.New("get observation error"))

		// 执行测试
		memoryService := NewMemoryServiceImpl(nil, dkmsClient, runStepDao, runStepMessageDao, toolCallDao, toolCallObservationDao)
		result, err := memoryService.GetAgentRunSteps(context.Background(), &service.GetAgentRunStepOption{
			AgentRunID: "run-1",
		})

		// 验证结果
		convey.So(err, convey.ShouldNotBeNil)
		convey.So(err.Error(), convey.ShouldContainSubstring, "get observation error")
		convey.So(result, convey.ShouldBeNil)
	})

	mockey.PatchConvey("TestMemoryServiceImpl_GetAgentRunSteps_DecryptObservationError", t, func() {
		ctrl := gomock.NewController(t)
		runStepDao := dal.NewMockAgentRunStepDAO(ctrl)
		runStepMessageDao := dal.NewMockAgentRunStepMessageDAO(ctrl)
		toolCallDao := dal.NewMockAgentRunStepToolCallDAO(ctrl)
		toolCallObservationDao := dal.NewMockToolCallObservationDAO(ctrl)
		dkmsClient := dkmsmock.NewMockClient(ctrl)

		// 准备测试数据
		runSteps := []*entity.AgentRunStep{
			{
				UUID:       "step-1",
				AgentRunID: "run-1",
				Actor:      "actor-1",
				Round:      1,
			},
		}

		toolCalls := []*entity.AgentRunStepToolCall{
			{
				UUID:           "tool-1",
				AgentRunStepID: "step-1",
				ToolName:       "tool-1",
				Index:          1,
			},
		}

		// 设置 mock 行为
		runStepDao.EXPECT().GetByAgentRunID(gomock.Any(), "run-1", entity.AgentRunStepStatusCompleted).Return(runSteps, nil)
		runStepMessageDao.EXPECT().Get(gomock.Any(), "step-1").Return("encrypted-message-1", nil)
		dkmsClient.EXPECT().Decrypt("encrypted-message-1").Return("raw-message-1", nil)
		toolCallDao.EXPECT().GetByAgentRunStepID(gomock.Any(), "step-1").Return(toolCalls, nil)
		toolCallObservationDao.EXPECT().Get(gomock.Any(), "tool-1").Return("encrypted-observation-1", nil)
		dkmsClient.EXPECT().Decrypt("encrypted-observation-1").Return("", errors.New("decrypt observation error"))

		// 执行测试
		memoryService := NewMemoryServiceImpl(nil, dkmsClient, runStepDao, runStepMessageDao, toolCallDao, toolCallObservationDao)
		result, err := memoryService.GetAgentRunSteps(context.Background(), &service.GetAgentRunStepOption{
			AgentRunID: "run-1",
		})

		// 验证结果
		convey.So(err, convey.ShouldNotBeNil)
		convey.So(err.Error(), convey.ShouldContainSubstring, "decrypt observation error")
		convey.So(result, convey.ShouldBeNil)
	})
}
