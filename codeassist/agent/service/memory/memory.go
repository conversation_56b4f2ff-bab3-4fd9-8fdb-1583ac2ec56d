package memory

import (
	"context"
	"sort"

	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/gopkg/pkg/errors"
	poolsdk "github.com/sourcegraph/conc/pool"

	"code.byted.org/devgpt/kiwis/codeassist/agent/dal"
	"code.byted.org/devgpt/kiwis/codeassist/agent/entity"
	"code.byted.org/devgpt/kiwis/codeassist/agent/service"
	"code.byted.org/devgpt/kiwis/port/db"
	"code.byted.org/devgpt/kiwis/port/dkms"
)

// 实现 history 存储，任务恢复时，实现 history 回捞拼装
var _ service.MemoryService = &MemoryServiceImpl{}

type MemoryServiceImpl struct {
	dbClient               db.Client
	dkmsClient             dkms.Client
	runStepDao             dal.AgentRunStepDAO
	runStepMessageDao      dal.AgentRunStepMessageDAO
	toolCallDao            dal.AgentRunStepToolCallDAO
	toolCallObservationDao dal.ToolCallObservationDAO
}

func NewMemoryServiceImpl(dbClient db.Client,
	dkmsClient dkms.Client,
	runStepDao dal.AgentRunStepDAO,
	runStepMessageDao dal.AgentRunStepMessageDAO,
	toolCallDao dal.AgentRunStepToolCallDAO,
	toolCallObservationDao dal.ToolCallObservationDAO) *MemoryServiceImpl {
	return &MemoryServiceImpl{
		dbClient:               dbClient,
		dkmsClient:             dkmsClient,
		runStepDao:             runStepDao,
		runStepMessageDao:      runStepMessageDao,
		toolCallDao:            toolCallDao,
		toolCallObservationDao: toolCallObservationDao,
	}
}

func (s *MemoryServiceImpl) SaveAgentRunStep(ctx context.Context, opt *service.SaveAgentRunStepOption) (string, error) {
	runStep := opt.AgentRunStep
	if runStep == nil {
		return "", errors.New("agent run step is nil")
	}
	runStep.Status = entity.AgentRunStepStatusRunning

	runStepID, err := s.runStepDao.Create(ctx, runStep)
	if err != nil {
		return "", err
	}
	return runStepID, nil
}

func (s *MemoryServiceImpl) UpdateAgentRunStep(ctx context.Context, opt *service.UpdateAgentRunStepOption) error {
	if opt == nil {
		return errors.New("update agent run step option is nil")
	}
	logs.V1.CtxInfo(ctx, "UpdateAgentRunStep, status: %d, meta: %v", opt.Status, opt.Metadata)
	// update status to db
	if opt.Status != 0 || opt.Metadata != nil {
		err := s.runStepDao.UpdateByID(ctx, opt.AgentRunStepID, opt.Status, opt.Metadata)
		if err != nil {
			return err
		}
	}

	// save raw message to abase
	if opt.RawMessage != "" {
		cipherMessage, err := s.dkmsClient.Encrypt(opt.RawMessage)
		if err != nil {
			return err
		}
		return s.runStepMessageDao.Set(ctx, opt.AgentRunStepID, cipherMessage)
	}
	return nil
}

func (s *MemoryServiceImpl) AddToolCall(ctx context.Context, toolCall *entity.AgentRunStepToolCall) error {
	if toolCall == nil {
		return errors.New("tool call is nil")
	}
	// save tool call to db
	toolCallID, err := s.toolCallDao.Create(ctx, toolCall)
	if err != nil {
		return err
	}

	if toolCall.Observation == "" {
		logs.V1.CtxWarn(ctx, "tool call observation is empty")
		return nil
	}
	// save tool call observation to abase
	cipherMessage, err := s.dkmsClient.Encrypt(toolCall.Observation)
	if err != nil {
		return err
	}
	return s.toolCallObservationDao.Set(ctx, toolCallID, cipherMessage)
}

func (s *MemoryServiceImpl) GetAgentRunSteps(ctx context.Context, opt *service.GetAgentRunStepOption) ([]*entity.AgentRunStep, error) {
	if opt == nil || opt.AgentRunID == "" {
		return nil, errors.New("get agent run step option is nil")
	}
	runSteps, err := s.runStepDao.GetByAgentRunID(ctx, opt.AgentRunID, entity.AgentRunStepStatusCompleted)
	if err != nil {
		return nil, err
	}

	g := poolsdk.New().WithErrors()
	for _, runStep := range runSteps {
		g.Go(func() (err error) {
			message, err := s.runStepMessageDao.Get(ctx, runStep.UUID)
			if err != nil {
				return
			}
			rawMessage, err := s.dkmsClient.Decrypt(message)
			if err != nil {
				return
			}
			runStep.RawMessage = rawMessage
			toolcalls, err := s.toolCallDao.GetByAgentRunStepID(ctx, runStep.UUID)
			if err != nil {
				return
			}

			for _, toolcall := range toolcalls {
				observation, err := s.toolCallObservationDao.Get(ctx, toolcall.UUID)
				if err != nil {
					return err
				}
				rawObservation, err := s.dkmsClient.Decrypt(observation)
				if err != nil {
					return err
				}
				toolcall.Observation = rawObservation
			}
			runStep.AgentRunStepToolCallList = toolcalls
			return nil
		})
	}

	if err := g.Wait(); err != nil {
		return nil, err
	}

	sort.Slice(runSteps, func(i, j int) bool {
		return runSteps[i].Round < runSteps[j].Round
	})

	return runSteps, nil
}
