package service

import (
	"context"

	"code.byted.org/overpass/ocean_cloud_review/kitex_gen/ocean/cloud/review"
)

//go:generate go run go.uber.org/mock/mockgen@latest -destination ../mock/service/review_service.go -package service code.byted.org/devgpt/kiwis/codeassist/agent/service ReviewService
type ReviewService interface {
	TextReview(ctx context.Context, opt *ReviewOption) (review.CheckResultEnum, error)
	FullTextReview(ctx context.Context, opt *ReviewOption) (review.CheckResultEnum, error)
	ImageReview(ctx context.Context, opt *ReviewOption) (review.CheckResultEnum, error)
	FilenameReview(ctx context.Context, opt *ReviewOption) (review.CheckResultEnum, error)
	ReportReview(ctx context.Context, opt *ReviewOption) (review.CheckResultEnum, error)
}

type ReviewOption struct {
	UserID    int64
	MessageID string
	Content   string
}
