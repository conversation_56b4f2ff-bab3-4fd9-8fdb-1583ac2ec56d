package service

import (
	"context"

	"code.byted.org/devgpt/kiwis/codeassist/agent/entity"
)

//go:generate go run go.uber.org/mock/mockgen@latest -destination ../mock/service/artifacts_service.go -package service code.byted.org/devgpt/kiwis/codeassist/agent/service ArtifactsService
type ArtifactsService interface {
	CreateDoubaoArtifact(ctx context.Context, opt *CreateDoubaoArtifactOption) (*entity.DoubaoArtifact, error)
	UploadFile(ctx context.Context, userID int64, filename string, content []byte) (string, error)
	DownloadFile(ctx context.Context, uri string, userID int64) ([]byte, error)
}

type CreateDoubaoArtifactOption struct {
	UserID         int64
	MessageID      string
	AnswerID       string
	ConversationID string
	Title          string
	Type           string
}
