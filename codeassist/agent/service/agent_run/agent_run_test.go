package agent_run

import (
	"context"
	"strconv"
	"testing"
	"time"

	gmetrics "code.byted.org/gopkg/metrics/generic"
	"github.com/bytedance/mockey"
	"github.com/pkg/errors"
	. "github.com/smartystreets/goconvey/convey"
	"go.uber.org/mock/gomock"

	"code.byted.org/devgpt/kiwis/codeassist/agent/entity"
	"code.byted.org/devgpt/kiwis/codeassist/agent/mock/dal"
	"code.byted.org/devgpt/kiwis/codeassist/agent/service"
	sandboxEntity "code.byted.org/devgpt/kiwis/codeassist/sandbox/entity"
	sandboxService "code.byted.org/devgpt/kiwis/codeassist/sandbox/service"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/metrics"
	"code.byted.org/devgpt/kiwis/lib/tcc"
)

func TestAgentRunServiceImpl_CreateOrGet(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// 创建模拟对象
	mockAgentRunDAO := dal.NewMockAgentRunDAO(ctrl)
	mockSandbox := &mockSandboxManagerService{}
	localCache := NewLocalCache()

	// 创建 AgentSandboxConfig
	sandboxAPIConfig := &tcc.GenericConfig[config.AgentSandboxConfig]{}

	// 创建被测试的服务
	agentRunService := NewAgentRunService(mockAgentRunDAO, mockSandbox, localCache, sandboxAPIConfig)

	mockey.PatchConvey("Test CreateOrGet", t, func() {
		mockey.MockGeneric((*tcc.GenericConfig[config.AgentSandboxConfig]).GetPointer).Return(&config.AgentSandboxConfig{
			DataAnalysis: &config.AgentSandboxAPIConfig{
				NumLimit:  5,
				AliveTime: 3600,
			},
		}).Build()

		mockey.MockGeneric(metrics.CodeAssistMetric.CodeAssistAgentSandboxAllocation.WithTags).Return(
			gmetrics.Store{},
		).Build()
		mockey.Mock((gmetrics.Store).Store).Return(nil).Build()

		ctx := context.Background()

		mockey.PatchConvey("When agent run does not exist", func() {
			// 设置模拟行为
			mockAgentRunDAO.EXPECT().GetByTaskID(gomock.Any(), "task123").Return(nil, nil)
			mockAgentRunDAO.EXPECT().Create(gomock.Any(), gomock.Any()).DoAndReturn(
				func(_ context.Context, agentRun *entity.AgentRun) (string, error) {
					return "uuid123", nil
				})
			mockAgentRunDAO.EXPECT().UpdateStatus(gomock.Any(), "uuid123", entity.AgentRunStatusReady).Return(nil)

			// 设置 mockSandbox 行为
			mockSandbox.GetSandboxByIdentifierFunc = func(ctx context.Context, identifier sandboxEntity.SandboxIdentifier) (*sandboxEntity.Sandbox, error) {
				return nil, nil
			}
			mockSandbox.GetAllSandboxDetailByAllocationIDFunc = func(ctx context.Context, opt *sandboxService.GetAllSandboxesOpt) ([]*sandboxEntity.Sandbox, error) {
				return []*sandboxEntity.Sandbox{}, nil
			}
			mockSandbox.CreateSandboxFunc = func(ctx context.Context, opt *sandboxService.CreateSandboxOpt) (*sandboxEntity.Sandbox, error) {
				return &sandboxEntity.Sandbox{
					ID: "sandbox123",
					SandboxIdentifier: sandboxEntity.SandboxIdentifier{
						AllocationID: strconv.FormatInt(123, 10),
						SessionID:    "conv123",
						FunctionType: sandboxEntity.FunctionTypeAgent,
						SandboxType:  sandboxEntity.SandboxTypeShare,
					},
					Status: sandboxEntity.SandboxRuntimeStatusRunning,
				}, nil
			}

			// 执行测试
			agentRun, isNew, err := agentRunService.CreateOrGet(ctx, &service.CreateOrGetOption{
				TaskID:         "task123",
				UserID:         123,
				ConversationID: "conv123",
				AgentID:        "agent123",
				AgentVersion:   "v1",
				AgentMeta:      &entity.AgentRunMeta{Config: "config"},
				AgentInput:     &entity.AgentRunInput{},
			})

			// 验证结果
			So(err, ShouldBeNil)
			So(isNew, ShouldBeTrue)
			So(agentRun, ShouldNotBeNil)
			So(agentRun.UUID, ShouldEqual, "uuid123")
			So(agentRun.TaskID, ShouldEqual, "task123")
			So(agentRun.Status, ShouldEqual, entity.AgentRunStatusReady)
			So(agentRun.WorkspaceDir, ShouldEqual, "/mnt/conv123")
		})

		mockey.PatchConvey("When agent run exists", func() {
			existingAgentRun := &entity.AgentRun{
				UUID:           "uuid123",
				TaskID:         "task123",
				UserID:         123,
				ConversationID: "conv123",
				AgentID:        "agent123",
				AgentVersion:   "v1",
				Status:         entity.AgentRunStatusCreated,
				AgentMeta:      &entity.AgentRunMeta{Config: "config"},
				AgentInput:     &entity.AgentRunInput{},
			}

			// 设置模拟行为
			mockAgentRunDAO.EXPECT().GetByTaskID(gomock.Any(), "task123").Return(existingAgentRun, nil)
			mockAgentRunDAO.EXPECT().UpdateStatus(gomock.Any(), "uuid123", entity.AgentRunStatusReady).Return(nil)

			// 设置 mockSandbox 行为
			mockSandbox.GetSandboxByIdentifierFunc = func(ctx context.Context, identifier sandboxEntity.SandboxIdentifier) (*sandboxEntity.Sandbox, error) {
				return &sandboxEntity.Sandbox{
					ID: "sandbox123",
					SandboxIdentifier: sandboxEntity.SandboxIdentifier{
						AllocationID: strconv.FormatInt(123, 10),
						SessionID:    "conv123",
						FunctionType: sandboxEntity.FunctionTypeAgent,
						SandboxType:  sandboxEntity.SandboxTypeShare,
					},
					Status: sandboxEntity.SandboxRuntimeStatusRunning,
				}, nil
			}

			// 执行测试
			agentRun, isNew, err := agentRunService.CreateOrGet(ctx, &service.CreateOrGetOption{
				TaskID:         "task123",
				UserID:         123,
				ConversationID: "conv123",
				AgentID:        "agent123",
				AgentVersion:   "v1",
				AgentMeta:      &entity.AgentRunMeta{Config: "config"},
				AgentInput:     &entity.AgentRunInput{},
			})

			// 验证结果
			So(err, ShouldBeNil)
			So(isNew, ShouldBeFalse)
			So(agentRun, ShouldNotBeNil)
			So(agentRun.UUID, ShouldEqual, "uuid123")
			So(agentRun.Status, ShouldEqual, entity.AgentRunStatusReady)
			So(agentRun.WorkspaceDir, ShouldEqual, "/mnt/conv123")
		})

		mockey.PatchConvey("When GetByTaskID returns error", func() {
			// 设置模拟行为
			mockAgentRunDAO.EXPECT().GetByTaskID(gomock.Any(), "task123").Return(nil, errors.New("db error"))

			// 执行测试
			agentRun, isNew, err := agentRunService.CreateOrGet(ctx, &service.CreateOrGetOption{
				TaskID:         "task123",
				UserID:         123,
				ConversationID: "conv123",
				AgentID:        "agent123",
				AgentVersion:   "v1",
				AgentMeta:      &entity.AgentRunMeta{Config: "config"},
				AgentInput:     &entity.AgentRunInput{},
			})

			// 验证结果
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "db error")
			So(isNew, ShouldBeFalse)
			So(agentRun, ShouldBeNil)
		})

		mockey.PatchConvey("When options is nil", func() {
			// 执行测试
			agentRun, isNew, err := agentRunService.CreateOrGet(ctx, nil)

			// 验证结果
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "create or get option is nil")
			So(isNew, ShouldBeFalse)
			So(agentRun, ShouldBeNil)
		})
	})
}

func TestAgentRunServiceImpl_GetAgentRun(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// 创建模拟对象
	mockAgentRunDAO := dal.NewMockAgentRunDAO(ctrl)
	mockSandbox := &mockSandboxManagerService{}
	localCache := NewLocalCache()

	// 创建 AgentSandboxConfig
	sandboxAPIConfig := &tcc.GenericConfig[config.AgentSandboxConfig]{}

	mockey.PatchConvey("Test GetAgentRun", t, func() {
		ctx := context.Background()
		mockey.MockGeneric((*tcc.GenericConfig[config.AgentSandboxConfig]).GetPointer).Return(&config.AgentSandboxConfig{
			DataAnalysis: &config.AgentSandboxAPIConfig{
				NumLimit:  5,
				AliveTime: 3600,
			},
		}).Build()

		// 创建被测试的服务
		agentRunService := NewAgentRunService(mockAgentRunDAO, mockSandbox, localCache, sandboxAPIConfig)

		mockey.PatchConvey("When agent run is in local cache", func() {
			// 准备本地缓存数据
			cachedAgentRun := &entity.AgentRun{
				UUID:           "uuid123",
				TaskID:         "task123",
				UserID:         123,
				ConversationID: "conv123",
				AgentID:        "agent123",
				AgentVersion:   "v1",
				Status:         entity.AgentRunStatusReady,
				AgentMeta:      &entity.AgentRunMeta{Config: "config"},
				AgentInput:     &entity.AgentRunInput{},
			}
			localCache.SetAgentRun(cachedAgentRun)

			// 执行测试
			agentRun, err := agentRunService.GetAgentRun(ctx, "uuid123")

			// 验证结果
			So(err, ShouldBeNil)
			So(agentRun, ShouldNotBeNil)
			So(agentRun.UUID, ShouldEqual, "uuid123")
			So(agentRun.TaskID, ShouldEqual, "task123")
		})

		mockey.PatchConvey("When agent run is not in local cache but in DB", func() {
			// 清除本地缓存
			localCache.DelAgentRun("uuid123")

			// 设置模拟行为
			dbAgentRun := &entity.AgentRun{
				UUID:           "uuid123",
				TaskID:         "task123",
				UserID:         123,
				ConversationID: "conv123",
				AgentID:        "agent123",
				AgentVersion:   "v1",
				Status:         entity.AgentRunStatusRunning,
				AgentMeta:      &entity.AgentRunMeta{Config: "config"},
				AgentInput:     &entity.AgentRunInput{},
			}
			mockAgentRunDAO.EXPECT().GetByID(gomock.Any(), "uuid123").Return(dbAgentRun, nil)

			// 执行测试
			agentRun, err := agentRunService.GetAgentRun(ctx, "uuid123")

			// 验证结果
			So(err, ShouldBeNil)
			So(agentRun, ShouldNotBeNil)
			So(agentRun.UUID, ShouldEqual, "uuid123")
			So(agentRun.TaskID, ShouldEqual, "task123")

			// 验证是否已添加到本地缓存
			cachedAgentRun, err := localCache.GetAgentRun("uuid123")
			So(err, ShouldBeNil)
			So(cachedAgentRun, ShouldNotBeNil)
		})

		mockey.PatchConvey("When agent run is not found", func() {
			// 清除本地缓存
			localCache.DelAgentRun("uuid456")

			// 设置模拟行为
			mockAgentRunDAO.EXPECT().GetByID(gomock.Any(), "uuid456").Return(nil, nil)

			// 执行测试
			agentRun, err := agentRunService.GetAgentRun(ctx, "uuid456")

			// 验证结果
			So(err, ShouldBeNil)
			So(agentRun, ShouldBeNil)
		})

		mockey.PatchConvey("When DB returns error", func() {
			// 清除本地缓存
			localCache.DelAgentRun("uuid123")

			// 设置模拟行为
			mockAgentRunDAO.EXPECT().GetByID(gomock.Any(), "uuid123").Return(nil, errors.New("db error"))

			// 执行测试
			agentRun, err := agentRunService.GetAgentRun(ctx, "uuid123")

			// 验证结果
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "db error")
			So(agentRun, ShouldBeNil)
		})
	})
}

func TestAgentRunServiceImpl_UpdateAgentRunStatus(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// 创建模拟对象
	mockAgentRunDAO := dal.NewMockAgentRunDAO(ctrl)
	mockSandbox := &mockSandboxManagerService{}
	localCache := NewLocalCache()

	// 创建 AgentSandboxConfig
	sandboxAPIConfig := &tcc.GenericConfig[config.AgentSandboxConfig]{}

	// 创建被测试的服务
	agentRunService := NewAgentRunService(mockAgentRunDAO, mockSandbox, localCache, sandboxAPIConfig)

	mockey.PatchConvey("Test UpdateAgentRunStatus", t, func() {
		ctx := context.Background()
		mockey.MockGeneric((*tcc.GenericConfig[config.AgentSandboxConfig]).GetPointer).Return(&config.AgentSandboxConfig{
			DataAnalysis: &config.AgentSandboxAPIConfig{
				NumLimit:  5,
				AliveTime: 3600,
			},
		}).Build()
		mockey.PatchConvey("When update succeeds", func() {
			// 设置模拟行为
			mockAgentRunDAO.EXPECT().UpdateStatus(gomock.Any(), "uuid123", entity.AgentRunStatusRunning).Return(nil)

			// 执行测试
			err := agentRunService.UpdateAgentRunStatus(ctx, "uuid123", entity.AgentRunStatusRunning)

			// 验证结果
			So(err, ShouldBeNil)
		})

		mockey.PatchConvey("When update fails", func() {
			// 设置模拟行为
			mockAgentRunDAO.EXPECT().UpdateStatus(gomock.Any(), "uuid123", entity.AgentRunStatusRunning).Return(errors.New("update error"))

			// 执行测试
			err := agentRunService.UpdateAgentRunStatus(ctx, "uuid123", entity.AgentRunStatusRunning)

			// 验证结果
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "update error")
		})
	})
}

func TestAgentRunServiceImpl_ReleaseSandbox(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// 创建模拟对象
	mockAgentRunDAO := dal.NewMockAgentRunDAO(ctrl)
	mockSandbox := &mockSandboxManagerService{}
	localCache := NewLocalCache()

	// 创建 AgentSandboxConfig
	sandboxAPIConfig := &tcc.GenericConfig[config.AgentSandboxConfig]{}

	// 创建被测试的服务
	agentRunService := NewAgentRunService(mockAgentRunDAO, mockSandbox, localCache, sandboxAPIConfig)

	mockey.PatchConvey("Test ReleaseSandbox", t, func() {
		ctx := context.Background()
		mockey.MockGeneric((*tcc.GenericConfig[config.AgentSandboxConfig]).GetPointer).Return(&config.AgentSandboxConfig{
			DataAnalysis: &config.AgentSandboxAPIConfig{
				NumLimit:  5,
				AliveTime: 3600,
			},
		}).Build()

		mockey.PatchConvey("When release succeeds", func() {
			// 设置 mockSandbox 行为
			mockSandbox.ClearSandboxOrInstanceFunc = func(ctx context.Context, identifier sandboxEntity.SandboxIdentifier) error {
				return nil
			}

			// 执行测试
			err := agentRunService.ReleaseSandbox(ctx, 123, "conv123")

			// 验证结果
			So(err, ShouldBeNil)
		})

		mockey.PatchConvey("When release fails", func() {
			// 设置 mockSandbox 行为
			mockSandbox.ClearSandboxOrInstanceFunc = func(ctx context.Context, identifier sandboxEntity.SandboxIdentifier) error {
				return errors.New("release error")
			}

			// 执行测试
			err := agentRunService.ReleaseSandbox(ctx, 123, "conv123")

			// 验证结果
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "release error")
		})
	})
}

func TestAgentRunServiceImpl_UploadFile(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// 创建模拟对象
	mockAgentRunDAO := dal.NewMockAgentRunDAO(ctrl)
	mockSandbox := &mockSandboxManagerService{}
	localCache := NewLocalCache()

	// 创建 AgentSandboxConfig
	sandboxAPIConfig := &tcc.GenericConfig[config.AgentSandboxConfig]{}

	// 创建被测试的服务
	agentRunService := NewAgentRunService(mockAgentRunDAO, mockSandbox, localCache, sandboxAPIConfig)

	mockey.PatchConvey("Test UploadFile", t, func() {
		mockey.MockGeneric((*tcc.GenericConfig[config.AgentSandboxConfig]).GetPointer).Return(&config.AgentSandboxConfig{
			DataAnalysis: &config.AgentSandboxAPIConfig{
				NumLimit:  5,
				AliveTime: 3600,
			},
		}).Build()
		ctx := context.Background()

		mockey.PatchConvey("When upload succeeds", func() {
			// 设置 mockSandbox 行为
			mockSandbox.CreateFileFunc = func(ctx context.Context, identifier sandboxEntity.SandboxIdentifier, opt *sandboxService.CreateFileOpt) (*sandboxService.CreateFileResponse, error) {
				return &sandboxService.CreateFileResponse{
					Path: opt.Path,
				}, nil
			}

			// 执行测试
			files := map[string][]byte{
				"test.txt":  []byte("test content"),
				"test2.txt": []byte("test content 2"),
			}
			err := agentRunService.UploadFile(ctx, &service.UploadFileOption{
				UserID:         123,
				ConversationID: "conv123",
				Files:          files,
			})

			// 验证结果
			So(err, ShouldBeNil)
		})

		mockey.PatchConvey("When upload fails", func() {
			// 设置 mockSandbox 行为
			mockSandbox.CreateFileFunc = func(ctx context.Context, identifier sandboxEntity.SandboxIdentifier, opt *sandboxService.CreateFileOpt) (*sandboxService.CreateFileResponse, error) {
				return nil, errors.New("upload error")
			}

			// 执行测试
			files := map[string][]byte{
				"test.txt": []byte("test content"),
			}
			err := agentRunService.UploadFile(ctx, &service.UploadFileOption{
				UserID:         123,
				ConversationID: "conv123",
				Files:          files,
			})

			// 验证结果
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "upload file fail")
		})
	})
}

func TestAgentRunServiceImpl_ExecuteCode(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// 创建模拟对象
	mockAgentRunDAO := dal.NewMockAgentRunDAO(ctrl)
	mockSandbox := &mockSandboxManagerService{}
	localCache := NewLocalCache()

	// 创建 AgentSandboxConfig
	sandboxAPIConfig := &tcc.GenericConfig[config.AgentSandboxConfig]{}

	// 创建被测试的服务
	agentRunService := NewAgentRunService(mockAgentRunDAO, mockSandbox, localCache, sandboxAPIConfig)

	mockey.PatchConvey("Test ExecuteCode", t, func() {
		ctx := context.Background()
		mockey.MockGeneric((*tcc.GenericConfig[config.AgentSandboxConfig]).GetPointer).Return(&config.AgentSandboxConfig{
			DataAnalysis: &config.AgentSandboxAPIConfig{
				NumLimit:  5,
				AliveTime: 3600,
			},
		}).Build()

		mockey.PatchConvey("When execution succeeds", func() {
			// 设置 mockSandbox 行为
			mockSandbox.ExecuteCodeFunc = func(ctx context.Context, identifier sandboxEntity.SandboxIdentifier, opt *sandboxService.ExecuteOpt) (*sandboxService.ExecuteResponse, error) {
				return &sandboxService.ExecuteResponse{
					Status:  "success",
					Message: "execution result",
					RunResult: []*sandboxService.ExecuteCommandResult{
						{
							Content:    "execution result",
							ReturnCode: 0,
						},
					},
				}, nil
			}

			// 执行测试
			resp, err := agentRunService.ExecuteCode(ctx, &service.ExecuteCodeOpt{
				UserID:         123,
				ConversationID: "conv123",
				Code:           "print('hello')",
				Language:       sandboxEntity.LanguageTypePython,
			})

			// 验证结果
			So(err, ShouldBeNil)
			So(resp, ShouldNotBeNil)
			So(resp.Status, ShouldEqual, "success")
			So(resp.Message, ShouldEqual, "execution result")
			So(len(resp.RunResult), ShouldEqual, 1)
			So(resp.RunResult[0].Content, ShouldEqual, "execution result")
			So(resp.RunResult[0].ReturnCode, ShouldEqual, 0)
		})

		mockey.PatchConvey("When execution fails", func() {
			// 设置 mockSandbox 行为
			mockSandbox.ExecuteCodeFunc = func(ctx context.Context, identifier sandboxEntity.SandboxIdentifier, opt *sandboxService.ExecuteOpt) (*sandboxService.ExecuteResponse, error) {
				return nil, errors.New("execution error")
			}

			// 执行测试
			resp, err := agentRunService.ExecuteCode(ctx, &service.ExecuteCodeOpt{
				UserID:         123,
				ConversationID: "conv123",
				Code:           "print('hello')",
				Language:       sandboxEntity.LanguageTypePython,
			})

			// 验证结果
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "sandbox execute code fail")
			So(resp, ShouldBeNil)
		})
	})
}

func TestAgentRunServiceImpl_DownloadFile(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// 创建模拟对象
	mockAgentRunDAO := dal.NewMockAgentRunDAO(ctrl)
	mockSandbox := &mockSandboxManagerService{}
	localCache := NewLocalCache()

	// 创建 AgentSandboxConfig
	sandboxAPIConfig := &tcc.GenericConfig[config.AgentSandboxConfig]{}

	// 创建被测试的服务
	agentRunService := NewAgentRunService(mockAgentRunDAO, mockSandbox, localCache, sandboxAPIConfig)

	mockey.PatchConvey("Test DownloadFile", t, func() {
		ctx := context.Background()
		mockey.MockGeneric((*tcc.GenericConfig[config.AgentSandboxConfig]).GetPointer).Return(&config.AgentSandboxConfig{
			DataAnalysis: &config.AgentSandboxAPIConfig{
				NumLimit:  5,
				AliveTime: 3600,
			},
		}).Build()

		mockey.PatchConvey("When download succeeds", func() {
			// 设置 mockSandbox 行为
			mockSandbox.DownloadFileFunc = func(ctx context.Context, identifier sandboxEntity.SandboxIdentifier, opt *sandboxService.DownloadFileOpt) (*sandboxService.DownloadFileResponse, error) {
				return &sandboxService.DownloadFileResponse{
					Path:    opt.Path,
					Content: []byte("file content"),
				}, nil
			}

			// 执行测试
			files := []string{"test.txt"}
			resp, err := agentRunService.DownloadFile(ctx, &service.DownloadFileOption{
				UserID:         123,
				ConversationID: "conv123",
				Files:          files,
			})

			// 验证结果
			So(err, ShouldBeNil)
			So(resp, ShouldNotBeNil)
			So(len(resp), ShouldEqual, 1)
			So(string(resp["test.txt"]), ShouldEqual, "file content")
		})

		mockey.PatchConvey("When download fails", func() {
			// 设置 mockSandbox 行为
			mockSandbox.DownloadFileFunc = func(ctx context.Context, identifier sandboxEntity.SandboxIdentifier, opt *sandboxService.DownloadFileOpt) (*sandboxService.DownloadFileResponse, error) {
				return nil, errors.New("download error")
			}

			// 执行测试
			files := []string{"test.txt"}
			resp, err := agentRunService.DownloadFile(ctx, &service.DownloadFileOption{
				UserID:         123,
				ConversationID: "conv123",
				Files:          files,
			})

			// 验证结果
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "download file fail")
			So(len(resp), ShouldEqual, 0)
		})
	})
}

func TestAgentRunServiceImpl_CreateFile(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// 创建模拟对象
	mockAgentRunDAO := dal.NewMockAgentRunDAO(ctrl)
	mockSandbox := &mockSandboxManagerService{}
	localCache := NewLocalCache()

	// 创建 AgentSandboxConfig
	sandboxAPIConfig := &tcc.GenericConfig[config.AgentSandboxConfig]{}

	// 创建被测试的服务
	agentRunService := NewAgentRunService(mockAgentRunDAO, mockSandbox, localCache, sandboxAPIConfig)

	mockey.PatchConvey("Test CreateFile", t, func() {
		ctx := context.Background()
		mockey.MockGeneric((*tcc.GenericConfig[config.AgentSandboxConfig]).GetPointer).Return(&config.AgentSandboxConfig{
			DataAnalysis: &config.AgentSandboxAPIConfig{
				NumLimit:  5,
				AliveTime: 3600,
			},
		}).Build()

		mockey.PatchConvey("When create succeeds", func() {
			// 设置 mockSandbox 行为
			mockSandbox.CreateFileFunc = func(ctx context.Context, identifier sandboxEntity.SandboxIdentifier, opt *sandboxService.CreateFileOpt) (*sandboxService.CreateFileResponse, error) {
				return &sandboxService.CreateFileResponse{
					Path: opt.Path,
				}, nil
			}

			// 执行测试
			err := agentRunService.CreateFile(ctx, &service.CreateFileOpt{
				UserID:         123,
				ConversationID: "conv123",
				Path:           "test.txt",
				Content:        []byte("file content"),
			})

			// 验证结果
			So(err, ShouldBeNil)
		})

		mockey.PatchConvey("When create fails", func() {
			// 设置 mockSandbox 行为
			mockSandbox.CreateFileFunc = func(ctx context.Context, identifier sandboxEntity.SandboxIdentifier, opt *sandboxService.CreateFileOpt) (*sandboxService.CreateFileResponse, error) {
				return nil, errors.New("create error")
			}

			// 执行测试
			err := agentRunService.CreateFile(ctx, &service.CreateFileOpt{
				UserID:         123,
				ConversationID: "conv123",
				Path:           "test.txt",
				Content:        []byte("file content"),
			})

			// 验证结果
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "create error")
		})
	})
}

func TestAgentRunServiceImpl_ReadFile(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// 创建模拟对象
	mockAgentRunDAO := dal.NewMockAgentRunDAO(ctrl)
	mockSandbox := &mockSandboxManagerService{}
	localCache := NewLocalCache()

	// 创建 AgentSandboxConfig
	sandboxAPIConfig := &tcc.GenericConfig[config.AgentSandboxConfig]{}

	// 创建被测试的服务
	agentRunService := NewAgentRunService(mockAgentRunDAO, mockSandbox, localCache, sandboxAPIConfig)

	mockey.PatchConvey("Test ReadFile", t, func() {
		ctx := context.Background()

		mockey.MockGeneric((*tcc.GenericConfig[config.AgentSandboxConfig]).GetPointer).Return(&config.AgentSandboxConfig{
			DataAnalysis: &config.AgentSandboxAPIConfig{
				NumLimit:  5,
				AliveTime: 3600,
			},
		}).Build()
		mockey.PatchConvey("When read succeeds", func() {
			// 设置 mockSandbox 行为
			mockSandbox.ReadFileFunc = func(ctx context.Context, identifier sandboxEntity.SandboxIdentifier, opt *sandboxService.ReadFileOpt) (*sandboxService.ReadFileResponse, error) {
				return &sandboxService.ReadFileResponse{
					Path:        opt.Path,
					Content:     []byte("file content"),
					ContentSize: 12,
					FileSize:    12,
					LineCount:   1,
					TotalLines:  1,
				}, nil
			}

			// 执行测试
			resp, err := agentRunService.ReadFile(ctx, &service.ReadFileOpt{
				UserID:         123,
				ConversationID: "conv123",
				Path:           "test.txt",
				Offset:         0,
				Limit:          100,
			})

			// 验证结果
			So(err, ShouldBeNil)
			So(resp, ShouldNotBeNil)
			So(string(resp.Content), ShouldEqual, "file content")
			So(resp.LineCount, ShouldEqual, 1)
		})

		mockey.PatchConvey("When read fails", func() {
			// 设置 mockSandbox 行为
			mockSandbox.ReadFileFunc = func(ctx context.Context, identifier sandboxEntity.SandboxIdentifier, opt *sandboxService.ReadFileOpt) (*sandboxService.ReadFileResponse, error) {
				return nil, errors.New("read error")
			}

			// 执行测试
			resp, err := agentRunService.ReadFile(ctx, &service.ReadFileOpt{
				UserID:         123,
				ConversationID: "conv123",
				Path:           "test.txt",
				Offset:         0,
				Limit:          100,
			})

			// 验证结果
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "read error")
			So(resp, ShouldBeNil)
		})
	})
}

func TestAgentRunServiceImpl_EditFile(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// 创建模拟对象
	mockAgentRunDAO := dal.NewMockAgentRunDAO(ctrl)
	mockSandbox := &mockSandboxManagerService{}
	localCache := NewLocalCache()

	// 创建 AgentSandboxConfig
	sandboxAPIConfig := &tcc.GenericConfig[config.AgentSandboxConfig]{}

	// 创建被测试的服务
	agentRunService := NewAgentRunService(mockAgentRunDAO, mockSandbox, localCache, sandboxAPIConfig)

	mockey.PatchConvey("Test EditFile", t, func() {
		ctx := context.Background()
		mockey.MockGeneric((*tcc.GenericConfig[config.AgentSandboxConfig]).GetPointer).Return(&config.AgentSandboxConfig{
			DataAnalysis: &config.AgentSandboxAPIConfig{
				NumLimit:  5,
				AliveTime: 3600,
			},
		}).Build()

		mockey.PatchConvey("When edit succeeds", func() {
			// 设置 mockSandbox 行为
			mockSandbox.EditFileFunc = func(ctx context.Context, identifier sandboxEntity.SandboxIdentifier, opt *sandboxService.EditFileOpt) (*sandboxService.EditFileResponse, error) {
				return &sandboxService.EditFileResponse{
					Path:       opt.Path,
					FileSize:   11,
					TotalLines: 1,
				}, nil
			}

			// 执行测试
			resp, err := agentRunService.EditFile(ctx, &service.EditFileOpt{
				UserID:         123,
				ConversationID: "conv123",
				Path:           "test.txt",
				OldStr:         "old content",
				NewStr:         "new content",
			})

			// 验证结果
			So(err, ShouldBeNil)
			So(resp, ShouldNotBeNil)
			So(resp.Path, ShouldEqual, "test.txt")
			So(resp.FileSize, ShouldEqual, 11)
			So(resp.TotalLines, ShouldEqual, 1)
		})

		mockey.PatchConvey("When edit fails", func() {
			// 设置 mockSandbox 行为
			mockSandbox.EditFileFunc = func(ctx context.Context, identifier sandboxEntity.SandboxIdentifier, opt *sandboxService.EditFileOpt) (*sandboxService.EditFileResponse, error) {
				return nil, errors.New("edit error")
			}

			// 执行测试
			resp, err := agentRunService.EditFile(ctx, &service.EditFileOpt{
				UserID:         123,
				ConversationID: "conv123",
				Path:           "test.txt",
				OldStr:         "old content",
				NewStr:         "new content",
			})

			// 验证结果
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "edit error")
			So(resp, ShouldBeNil)
		})
	})
}

func TestAgentRunServiceImpl_GetSandBoxInstance(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// 创建模拟对象
	mockAgentRunDAO := dal.NewMockAgentRunDAO(ctrl)
	mockSandbox := &mockSandboxManagerService{}
	localCache := NewLocalCache()

	// 创建 AgentSandboxConfig
	sandboxAPIConfig := &tcc.GenericConfig[config.AgentSandboxConfig]{}
	// 确保 mock 在每个测试用例中都有效

	mockAgentRunDAO.EXPECT().GetByConversationID(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*entity.AgentRun{}, nil).AnyTimes()

	// 创建被测试的服务
	agentRunService := NewAgentRunService(mockAgentRunDAO, mockSandbox, localCache, sandboxAPIConfig)

	mockey.PatchConvey("Test getSandBoxInstance", t, func() {
		ctx := context.Background()
		mockey.MockGeneric((*tcc.GenericConfig[config.AgentSandboxConfig]).GetPointer).Return(&config.AgentSandboxConfig{
			DataAnalysis: &config.AgentSandboxAPIConfig{
				NumLimit:  5,
				AliveTime: 3600,
			},
		}).Build()

		mockey.MockGeneric(metrics.CodeAssistMetric.CodeAssistAgentSandboxAllocation.WithTags).Return(
			gmetrics.Store{},
		).Build()
		mockey.Mock((gmetrics.Store).Store).Return(nil).Build()

		mockey.PatchConvey("When sandbox already exists and is running", func() {
			// 设置 mockSandbox 行为
			mockSandbox.GetSandboxByIdentifierFunc = func(ctx context.Context, identifier sandboxEntity.SandboxIdentifier) (*sandboxEntity.Sandbox, error) {
				return &sandboxEntity.Sandbox{
					ID: "sandbox123",
					SandboxIdentifier: sandboxEntity.SandboxIdentifier{
						AllocationID: strconv.FormatInt(123, 10),
						SessionID:    "conv123",
						FunctionType: sandboxEntity.FunctionTypeAgent,
						SandboxType:  sandboxEntity.SandboxTypeShare,
					},
					Status: sandboxEntity.SandboxRuntimeStatusRunning,
				}, nil
			}

			// 执行测试
			sandbox, isNew, err := agentRunService.getSandBoxInstance(ctx, 123, "conv123")

			// 验证结果
			So(err, ShouldBeNil)
			So(sandbox, ShouldNotBeNil)
			So(isNew, ShouldBeFalse)
			So(sandbox.ID, ShouldEqual, "sandbox123")
			So(sandbox.Status, ShouldEqual, sandboxEntity.SandboxRuntimeStatusRunning)
		})

		mockey.PatchConvey("When sandbox does not exist and needs to be created", func() {
			// 设置 mockSandbox 行为
			mockSandbox.GetSandboxByIdentifierFunc = func(ctx context.Context, identifier sandboxEntity.SandboxIdentifier) (*sandboxEntity.Sandbox, error) {
				return nil, nil
			}
			mockSandbox.GetAllSandboxDetailByAllocationIDFunc = func(ctx context.Context, opt *sandboxService.GetAllSandboxesOpt) ([]*sandboxEntity.Sandbox, error) {
				return []*sandboxEntity.Sandbox{}, nil
			}
			mockSandbox.CreateSandboxFunc = func(ctx context.Context, opt *sandboxService.CreateSandboxOpt) (*sandboxEntity.Sandbox, error) {
				return &sandboxEntity.Sandbox{
					ID: "newsandbox123",
					SandboxIdentifier: sandboxEntity.SandboxIdentifier{
						AllocationID: strconv.FormatInt(123, 10),
						SessionID:    "conv123",
						FunctionType: sandboxEntity.FunctionTypeAgent,
						SandboxType:  sandboxEntity.SandboxTypeShare,
					},
					Status: sandboxEntity.SandboxRuntimeStatusRunning,
				}, nil
			}

			// 执行测试
			sandbox, isNew, err := agentRunService.getSandBoxInstance(ctx, 123, "conv123")

			// 验证结果
			So(err, ShouldBeNil)
			So(sandbox, ShouldNotBeNil)
			So(isNew, ShouldBeTrue)
			So(sandbox.ID, ShouldEqual, "newsandbox123")
			So(sandbox.Status, ShouldEqual, sandboxEntity.SandboxRuntimeStatusRunning)
		})

		mockey.PatchConvey("When sandbox limit is reached and needs to release one", func() {
			// 设置 mockSandbox 行为
			mockSandbox.GetSandboxByIdentifierFunc = func(ctx context.Context, identifier sandboxEntity.SandboxIdentifier) (*sandboxEntity.Sandbox, error) {
				return nil, nil
			}
			mockSandbox.GetAllSandboxDetailByAllocationIDFunc = func(ctx context.Context, opt *sandboxService.GetAllSandboxesOpt) ([]*sandboxEntity.Sandbox, error) {
				return []*sandboxEntity.Sandbox{
					{
						ID: "sandbox1",
						SandboxIdentifier: sandboxEntity.SandboxIdentifier{
							AllocationID: strconv.FormatInt(123, 10),
							SessionID:    "oldconv1",
							FunctionType: sandboxEntity.FunctionTypeAgent,
							SandboxType:  sandboxEntity.SandboxTypeShare,
						},
						Status: sandboxEntity.SandboxRuntimeStatusRunning,
					},
					{
						ID: "sandbox2",
						SandboxIdentifier: sandboxEntity.SandboxIdentifier{
							AllocationID: strconv.FormatInt(123, 10),
							SessionID:    "oldconv2",
							FunctionType: sandboxEntity.FunctionTypeAgent,
							SandboxType:  sandboxEntity.SandboxTypeShare,
						},
						Status: sandboxEntity.SandboxRuntimeStatusRunning,
					},
					{
						ID: "sandbox3",
						SandboxIdentifier: sandboxEntity.SandboxIdentifier{
							AllocationID: strconv.FormatInt(123, 10),
							SessionID:    "oldconv3",
							FunctionType: sandboxEntity.FunctionTypeAgent,
							SandboxType:  sandboxEntity.SandboxTypeShare,
						},
						Status: sandboxEntity.SandboxRuntimeStatusRunning,
					},
					{
						ID: "sandbox4",
						SandboxIdentifier: sandboxEntity.SandboxIdentifier{
							AllocationID: strconv.FormatInt(123, 10),
							SessionID:    "oldconv4",
							FunctionType: sandboxEntity.FunctionTypeAgent,
							SandboxType:  sandboxEntity.SandboxTypeShare,
						},
						Status: sandboxEntity.SandboxRuntimeStatusRunning,
					},
					{
						ID: "sandbox5",
						SandboxIdentifier: sandboxEntity.SandboxIdentifier{
							AllocationID: strconv.FormatInt(123, 10),
							SessionID:    "oldconv5",
							FunctionType: sandboxEntity.FunctionTypeAgent,
							SandboxType:  sandboxEntity.SandboxTypeShare,
						},
						Status: sandboxEntity.SandboxRuntimeStatusRunning,
					},
				}, nil
			}
			// 为所有可能的会话 ID 设置 mock 期望
			mockAgentRunDAO.EXPECT().GetByConversationID(gomock.Any(), "oldconv1", gomock.Any()).Return([]*entity.AgentRun{}, nil).AnyTimes()
			mockAgentRunDAO.EXPECT().GetByConversationID(gomock.Any(), "oldconv2", gomock.Any()).Return([]*entity.AgentRun{}, nil).AnyTimes()
			mockAgentRunDAO.EXPECT().GetByConversationID(gomock.Any(), "oldconv3", gomock.Any()).Return([]*entity.AgentRun{}, nil).AnyTimes()
			mockAgentRunDAO.EXPECT().GetByConversationID(gomock.Any(), "oldconv4", gomock.Any()).Return([]*entity.AgentRun{}, nil).AnyTimes()
			mockAgentRunDAO.EXPECT().GetByConversationID(gomock.Any(), "oldconv5", gomock.Any()).Return([]*entity.AgentRun{}, nil).AnyTimes()
			mockSandbox.ClearSandboxOrInstanceFunc = func(ctx context.Context, identifier sandboxEntity.SandboxIdentifier) error {
				return nil
			}
			mockSandbox.CreateSandboxFunc = func(ctx context.Context, opt *sandboxService.CreateSandboxOpt) (*sandboxEntity.Sandbox, error) {
				return &sandboxEntity.Sandbox{
					ID: "newsandbox123",
					SandboxIdentifier: sandboxEntity.SandboxIdentifier{
						AllocationID: strconv.FormatInt(123, 10),
						SessionID:    "conv123",
						FunctionType: sandboxEntity.FunctionTypeAgent,
						SandboxType:  sandboxEntity.SandboxTypeShare,
					},
					Status: sandboxEntity.SandboxRuntimeStatusRunning,
				}, nil
			}

			// 执行测试
			sandbox, isNew, err := agentRunService.getSandBoxInstance(ctx, 123, "conv123")

			// 验证结果
			So(err, ShouldBeNil)
			So(sandbox, ShouldNotBeNil)
			So(isNew, ShouldBeTrue)
			So(sandbox.ID, ShouldEqual, "newsandbox123")
			So(sandbox.Status, ShouldEqual, sandboxEntity.SandboxRuntimeStatusRunning)
		})

		mockey.PatchConvey("When GetSandboxByIdentifier returns error", func() {
			// 设置 mockSandbox 行为
			mockSandbox.GetSandboxByIdentifierFunc = func(ctx context.Context, identifier sandboxEntity.SandboxIdentifier) (*sandboxEntity.Sandbox, error) {
				return nil, errors.New("get sandbox error")
			}

			// 执行测试
			sandbox, isNew, err := agentRunService.getSandBoxInstance(ctx, 123, "conv123")

			// 验证结果
			So(err, ShouldNotBeNil)
			So(isNew, ShouldBeFalse)
			So(err.Error(), ShouldContainSubstring, "get sandbox error")
			So(sandbox, ShouldBeNil)
		})
	})
}

// 模拟 SandboxManagerService 接口
type mockSandboxManagerService struct {
	GetSandboxByIdentifierFunc            func(ctx context.Context, identifier sandboxEntity.SandboxIdentifier) (*sandboxEntity.Sandbox, error)
	GetAllSandboxDetailByAllocationIDFunc func(ctx context.Context, opt *sandboxService.GetAllSandboxesOpt) ([]*sandboxEntity.Sandbox, error)
	CreateSandboxFunc                     func(ctx context.Context, opt *sandboxService.CreateSandboxOpt) (*sandboxEntity.Sandbox, error)
	ClearSandboxOrInstanceFunc            func(ctx context.Context, identifier sandboxEntity.SandboxIdentifier) error
	ExecuteCodeFunc                       func(ctx context.Context, identifier sandboxEntity.SandboxIdentifier, opt *sandboxService.ExecuteOpt) (*sandboxService.ExecuteResponse, error)
	CreateFileFunc                        func(ctx context.Context, identifier sandboxEntity.SandboxIdentifier, opt *sandboxService.CreateFileOpt) (*sandboxService.CreateFileResponse, error)
	DownloadFileFunc                      func(ctx context.Context, identifier sandboxEntity.SandboxIdentifier, opt *sandboxService.DownloadFileOpt) (*sandboxService.DownloadFileResponse, error)
	ReadFileFunc                          func(ctx context.Context, identifier sandboxEntity.SandboxIdentifier, opt *sandboxService.ReadFileOpt) (*sandboxService.ReadFileResponse, error)
	EditFileFunc                          func(ctx context.Context, identifier sandboxEntity.SandboxIdentifier, opt *sandboxService.EditFileOpt) (*sandboxService.EditFileResponse, error)
	GetNeedRecycleSandboxListByTimeFunc   func(ctx context.Context, fromTime, endTime time.Time) ([]*sandboxEntity.Sandbox, error)
}

func (m *mockSandboxManagerService) GetSandboxByIdentifier(ctx context.Context, identifier sandboxEntity.SandboxIdentifier) (*sandboxEntity.Sandbox, error) {
	if m.GetSandboxByIdentifierFunc != nil {
		return m.GetSandboxByIdentifierFunc(ctx, identifier)
	}
	return nil, nil
}

func TestAgentRunServiceImpl_renewSandboxInstance(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// 创建模拟对象
	mockAgentRunDAO := dal.NewMockAgentRunDAO(ctrl)
	mockSandbox := &mockSandboxManagerService{}
	localCache := NewLocalCache()

	// 创建 AgentSandboxConfig
	sandboxAPIConfig := &tcc.GenericConfig[config.AgentSandboxConfig]{}

	// 创建被测试的服务
	agentRunService := NewAgentRunService(mockAgentRunDAO, mockSandbox, localCache, sandboxAPIConfig)

	mockey.PatchConvey("Test renewSandboxInstance", t, func() {
		ctx := context.Background()

		mockey.PatchConvey("When ExecuteCode succeeds", func() {
			// 设置 mockSandbox 行为
			mockSandbox.ExecuteCodeFunc = func(ctx context.Context, identifier sandboxEntity.SandboxIdentifier, opt *sandboxService.ExecuteOpt) (*sandboxService.ExecuteResponse, error) {
				return &sandboxService.ExecuteResponse{
					Status: "success",
				}, nil
			}

			// 执行测试
			err := agentRunService.renewSandboxInstance(ctx, 123, "conv123")

			// 验证结果
			So(err, ShouldBeNil)
		})

		mockey.PatchConvey("When ExecuteCode fails", func() {
			// 设置 mockSandbox 行为
			mockSandbox.ExecuteCodeFunc = func(ctx context.Context, identifier sandboxEntity.SandboxIdentifier, opt *sandboxService.ExecuteOpt) (*sandboxService.ExecuteResponse, error) {
				return nil, errors.New("execute error")
			}

			// 执行测试
			err := agentRunService.renewSandboxInstance(ctx, 123, "conv123")

			// 验证结果
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "execute error")
		})
	})
}

func (m *mockSandboxManagerService) GetAllSandboxDetailByAllocationID(ctx context.Context, opt *sandboxService.GetAllSandboxesOpt) ([]*sandboxEntity.Sandbox, error) {
	if m.GetAllSandboxDetailByAllocationIDFunc != nil {
		return m.GetAllSandboxDetailByAllocationIDFunc(ctx, opt)
	}
	return nil, nil
}

func (m *mockSandboxManagerService) CreateSandbox(ctx context.Context, opt *sandboxService.CreateSandboxOpt) (*sandboxEntity.Sandbox, error) {
	if m.CreateSandboxFunc != nil {
		return m.CreateSandboxFunc(ctx, opt)
	}
	return nil, nil
}

func (m *mockSandboxManagerService) ClearSandboxOrInstance(ctx context.Context, identifier sandboxEntity.SandboxIdentifier) error {
	if m.ClearSandboxOrInstanceFunc != nil {
		return m.ClearSandboxOrInstanceFunc(ctx, identifier)
	}
	return nil
}

func (m *mockSandboxManagerService) ExecuteCode(ctx context.Context, identifier sandboxEntity.SandboxIdentifier, opt *sandboxService.ExecuteOpt) (*sandboxService.ExecuteResponse, error) {
	if m.ExecuteCodeFunc != nil {
		return m.ExecuteCodeFunc(ctx, identifier, opt)
	}
	return nil, nil
}

func (m *mockSandboxManagerService) CreateFile(ctx context.Context, identifier sandboxEntity.SandboxIdentifier, opt *sandboxService.CreateFileOpt) (*sandboxService.CreateFileResponse, error) {
	if m.CreateFileFunc != nil {
		return m.CreateFileFunc(ctx, identifier, opt)
	}
	return nil, nil
}

func (m *mockSandboxManagerService) DownloadFile(ctx context.Context, identifier sandboxEntity.SandboxIdentifier, opt *sandboxService.DownloadFileOpt) (*sandboxService.DownloadFileResponse, error) {
	if m.DownloadFileFunc != nil {
		return m.DownloadFileFunc(ctx, identifier, opt)
	}
	return nil, nil
}

func (m *mockSandboxManagerService) ReadFile(ctx context.Context, identifier sandboxEntity.SandboxIdentifier, opt *sandboxService.ReadFileOpt) (*sandboxService.ReadFileResponse, error) {
	if m.ReadFileFunc != nil {
		return m.ReadFileFunc(ctx, identifier, opt)
	}
	return nil, nil
}

func (m *mockSandboxManagerService) EditFile(ctx context.Context, identifier sandboxEntity.SandboxIdentifier, opt *sandboxService.EditFileOpt) (*sandboxService.EditFileResponse, error) {
	if m.EditFileFunc != nil {
		return m.EditFileFunc(ctx, identifier, opt)
	}
	return nil, nil
}

func (m *mockSandboxManagerService) GetNeedRecycleSandboxListByTime(ctx context.Context, fromTime, endTime time.Time) ([]*sandboxEntity.Sandbox, error) {
	if m.GetNeedRecycleSandboxListByTimeFunc != nil {
		return m.GetNeedRecycleSandboxListByTimeFunc(ctx, fromTime, endTime)
	}
	return nil, nil
}
