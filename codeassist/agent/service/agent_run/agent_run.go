package agent_run

import (
	"context"
	"strconv"

	"code.byted.org/gopkg/logs/v2"
	"github.com/pkg/errors"
	poolsdk "github.com/sourcegraph/conc/pool"

	"code.byted.org/devgpt/kiwis/codeassist/agent/dal"
	"code.byted.org/devgpt/kiwis/codeassist/agent/entity"
	"code.byted.org/devgpt/kiwis/codeassist/agent/service"
	sandboxEntity "code.byted.org/devgpt/kiwis/codeassist/sandbox/entity"
	sandboxService "code.byted.org/devgpt/kiwis/codeassist/sandbox/service"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/metrics"
	"code.byted.org/devgpt/kiwis/lib/tcc"
)

var _ service.AgentRunService = &AgentRunServiceImpl{}

type AgentRunServiceImpl struct {
	agentRunDAO      dal.AgentRunDAO
	sandbox          sandboxService.SandboxManagerService
	localCache       *LocalCache
	sandboxAPIConfig *tcc.GenericConfig[config.AgentSandboxConfig]
}

func NewAgentRunService(agentRunDAO dal.AgentRunDAO, sandbox sandboxService.SandboxManagerService, localCache *LocalCache, sandboxAPIConfig *tcc.GenericConfig[config.AgentSandboxConfig]) *AgentRunServiceImpl {
	return &AgentRunServiceImpl{
		agentRunDAO:      agentRunDAO,
		sandbox:          sandbox,
		localCache:       localCache,
		sandboxAPIConfig: sandboxAPIConfig,
	}
}

func (a *AgentRunServiceImpl) CreateOrGet(ctx context.Context, opt *service.CreateOrGetOption) (*entity.AgentRun, bool, error) {
	if opt == nil || opt.TaskID == "" {
		return nil, false, errors.New("create or get option is nil")
	}

	// 查询是否存在
	agentRun, err := a.agentRunDAO.GetByTaskID(ctx, opt.TaskID)
	if err != nil {
		return nil, false, err
	}
	// 不存在保存到数据库
	if agentRun == nil {
		agentRun = &entity.AgentRun{
			TaskID:         opt.TaskID,
			UserID:         opt.UserID,
			ConversationID: opt.ConversationID,
			AgentID:        opt.AgentID,
			AgentVersion:   opt.AgentVersion,
			Status:         entity.AgentRunStatusCreated,
			AgentInput:     opt.AgentInput,
			AgentMeta:      opt.AgentMeta,
		}
		var agentRunID string
		agentRunID, err = a.agentRunDAO.Create(ctx, agentRun)
		if err != nil {
			return nil, false, err
		}
		agentRun.UUID = agentRunID
	}

	// 绑定到 sandbox
	instance, isNew, err := a.getSandBoxInstance(ctx, agentRun.UserID, agentRun.ConversationID)
	if err != nil {
		return nil, isNew, err
	}
	agentRun.WorkspaceDir = instance.GetSandboxWorkDir()

	// 更新状态为 ready
	err = a.UpdateAgentRunStatus(ctx, agentRun.UUID, entity.AgentRunStatusReady)
	if err != nil {
		return nil, isNew, err
	}
	agentRun.Status = entity.AgentRunStatusReady
	return agentRun, isNew, nil
}

func (a *AgentRunServiceImpl) GetAgentRun(ctx context.Context, agentRunID string) (*entity.AgentRun, error) {
	localRun, err := a.localCache.GetAgentRun(agentRunID)
	if localRun != nil && err == nil {
		return localRun, nil
	}

	agentRun, err := a.agentRunDAO.GetByID(ctx, agentRunID)
	if err != nil {
		return nil, err
	}
	if agentRun == nil {
		return nil, nil
	}
	if agentRun.Status == entity.AgentRunStatusRunning {
		err := a.localCache.SetAgentRun(agentRun)
		if err != nil {
			logs.V1.CtxWarn(ctx, "set agent run local cache fail: %s", err.Error())
		}
	}
	return agentRun, nil
}

func (a *AgentRunServiceImpl) UpdateAgentRunStatus(ctx context.Context, agentRunID string, status entity.AgentRunStatus) error {
	err := a.agentRunDAO.UpdateStatus(ctx, agentRunID, status)
	if err != nil {
		return err
	}

	a.localCache.DelAgentRun(agentRunID)
	return nil
}

func (a *AgentRunServiceImpl) ReleaseSandbox(ctx context.Context, userID int64, conversationID string) error {
	return a.sandbox.ClearSandboxOrInstance(ctx, sandboxEntity.SandboxIdentifier{
		AllocationID: strconv.FormatInt(userID, 10),
		SessionID:    conversationID,
		FunctionType: sandboxEntity.FunctionTypeAgent,
		SandboxType:  sandboxEntity.SandboxTypeShare,
	})
}

func (a *AgentRunServiceImpl) UploadFile(ctx context.Context, opt *service.UploadFileOption) error {
	if opt == nil {
		return errors.New("upload file option is nil")
	}
	g := poolsdk.New().WithErrors()
	for filename, content := range opt.Files {
		g.Go(func() (err error) {
			_, err = a.sandbox.CreateFile(ctx, sandboxEntity.SandboxIdentifier{
				AllocationID: strconv.FormatInt(opt.UserID, 10),
				SessionID:    opt.ConversationID,
				FunctionType: sandboxEntity.FunctionTypeAgent,
				SandboxType:  sandboxEntity.SandboxTypeShare,
			}, &sandboxService.CreateFileOpt{
				Path:    filename,
				Content: content,
			})
			return err
		})
	}
	if err := g.Wait(); err != nil {
		logs.V1.CtxWarn(ctx, "upload file to sandbox fail: %+v", err)
		return errors.WithMessage(err, "upload file fail")
	}
	return nil
}

func (a *AgentRunServiceImpl) DownloadFile(ctx context.Context, opt *service.DownloadFileOption) (map[string][]byte, error) {
	if opt == nil {
		return nil, errors.New("download file option is nil")
	}
	g := poolsdk.New().WithErrors()
	resp := make(map[string][]byte)
	for _, filename := range opt.Files {
		g.Go(func() (err error) {
			result, err := a.sandbox.DownloadFile(ctx, sandboxEntity.SandboxIdentifier{
				AllocationID: strconv.FormatInt(opt.UserID, 10),
				SessionID:    opt.ConversationID,
				FunctionType: sandboxEntity.FunctionTypeAgent,
				SandboxType:  sandboxEntity.SandboxTypeShare,
			}, &sandboxService.DownloadFileOpt{

				Path: filename,
			})
			if err != nil {
				return err
			}
			resp[result.Path] = result.Content
			return nil
		})
	}

	if err := g.Wait(); err != nil {
		logs.V1.CtxWarn(ctx, "download file from sandbox fail: %+v", err)
		return resp, errors.WithMessage(err, "download file fail")
	}

	return resp, nil
}

func (a *AgentRunServiceImpl) CreateFile(ctx context.Context, opt *service.CreateFileOpt) error {
	if opt == nil {
		return errors.New("create file option is nil")
	}
	_, err := a.sandbox.CreateFile(ctx, sandboxEntity.SandboxIdentifier{
		AllocationID: strconv.FormatInt(opt.UserID, 10),
		SessionID:    opt.ConversationID,
		FunctionType: sandboxEntity.FunctionTypeAgent,
		SandboxType:  sandboxEntity.SandboxTypeShare,
	}, &sandboxService.CreateFileOpt{
		Path:    opt.Path,
		Content: opt.Content,
	})
	return err
}

func (a *AgentRunServiceImpl) ReadFile(ctx context.Context, opt *service.ReadFileOpt) (*sandboxService.ReadFileResponse, error) {
	if opt == nil {
		return nil, errors.New("read file option is nil")
	}
	return a.sandbox.ReadFile(ctx, sandboxEntity.SandboxIdentifier{
		AllocationID: strconv.FormatInt(opt.UserID, 10),
		SessionID:    opt.ConversationID,
		FunctionType: sandboxEntity.FunctionTypeAgent,
		SandboxType:  sandboxEntity.SandboxTypeShare,
	}, &sandboxService.ReadFileOpt{
		Path:      opt.Path,
		Offset:    opt.Offset,
		LineLimit: opt.Limit,
	})
}

func (a *AgentRunServiceImpl) EditFile(ctx context.Context, opt *service.EditFileOpt) (*sandboxService.EditFileResponse, error) {
	if opt == nil {
		return nil, errors.New("edit file option is nil")
	}
	return a.sandbox.EditFile(ctx, sandboxEntity.SandboxIdentifier{
		AllocationID: strconv.FormatInt(opt.UserID, 10),
		SessionID:    opt.ConversationID,
		FunctionType: sandboxEntity.FunctionTypeAgent,
		SandboxType:  sandboxEntity.SandboxTypeShare,
	}, &sandboxService.EditFileOpt{

		Path:   opt.Path,
		OldStr: opt.OldStr,
		NewStr: opt.NewStr,
	})
}

func (a *AgentRunServiceImpl) ExecuteCode(ctx context.Context, opt *service.ExecuteCodeOpt) (*sandboxService.ExecuteResponse, error) {
	if opt == nil {
		return nil, errors.New("execute code option is nil")
	}
	resp, err := a.sandbox.ExecuteCode(ctx, sandboxEntity.SandboxIdentifier{
		AllocationID: strconv.FormatInt(opt.UserID, 10),
		SessionID:    opt.ConversationID,
		FunctionType: sandboxEntity.FunctionTypeAgent,
		SandboxType:  sandboxEntity.SandboxTypeShare,
	}, &sandboxService.ExecuteOpt{
		ExecuteDetail: &sandboxService.ExecuteDetail{
			Code:               &opt.Code,
			CopyRuntimeTimeout: opt.CopyRuntimeTimeout,
			CompileTimeout:     opt.CompileTimeout,
			RunTimeout:         opt.RunTimeout,
			Stdin:              &opt.Stdin,
			Language:           opt.Language,
			Files:              opt.Files,
			FetchFiles:         opt.FetchFiles,
			LinkPrefix:         &opt.LinkPrefix,
			SessionID:          &opt.ConversationID,
			ShareContext:       opt.ShareContext,
		},
	})
	if err != nil {
		return nil, errors.WithMessage(err, "sandbox execute code fail")
	}

	return resp, nil
}

func (a *AgentRunServiceImpl) getSandBoxInstance(ctx context.Context, userID int64, conversationID string) (*sandboxEntity.Sandbox, bool, error) {
	isNew := false
	sandbox, err := a.sandbox.GetSandboxByIdentifier(ctx, sandboxEntity.SandboxIdentifier{
		AllocationID: strconv.FormatInt(userID, 10),
		SessionID:    conversationID,
		FunctionType: sandboxEntity.FunctionTypeAgent,
		SandboxType:  sandboxEntity.SandboxTypeShare,
	})
	if err != nil {
		return nil, isNew, err
	}
	if sandbox != nil && sandbox.Status == sandboxEntity.SandboxRuntimeStatusRunning {
		e := a.renewSandboxInstance(ctx, userID, conversationID)
		if e != nil {
			logs.V1.CtxInfo(ctx, "renew sandbox instance fail, err: %v", e)
		}
		return sandbox, isNew, nil
	}

	// 获取当前用户的 sandbox 实例数; 如果达到上限，释放一个实例；如果没有符合要求的，则返回报错
	sandboxes, err := a.sandbox.GetAllSandboxDetailByAllocationID(ctx, &sandboxService.GetAllSandboxesOpt{
		AllocationID: strconv.FormatInt(userID, 10),
		Status:       sandboxEntity.SandboxRuntimeStatusRunning,
		FunctionType: sandboxEntity.FunctionTypeAgent,
		SandboxType:  sandboxEntity.SandboxTypeShare,
	})
	if err != nil {
		return nil, isNew, err
	}

	_ = metrics.CodeAssistMetric.CodeAssistAgentSandboxAllocation.WithTags(&metrics.CodeAssistAgentSandboxAllocationTag{
		UserId: userID,
	}).Store(float64(len(sandboxes)))
	if len(sandboxes) >= a.sandboxAPIConfig.GetPointer().DataAnalysis.NumLimit {
		hasRelease := false
		for _, s := range sandboxes {
			if s == nil {
				continue
			}

			var runs []*entity.AgentRun
			runs, err = a.agentRunDAO.GetByConversationID(ctx, s.SessionID, []entity.AgentRunStatus{
				entity.AgentRunStatusCreated,
				entity.AgentRunStatusReady,
				entity.AgentRunStatusRunning})

			if err != nil {
				return nil, isNew, err
			}
			if len(runs) == 0 {
				err = a.ReleaseSandbox(ctx, userID, s.SessionID)
				if err != nil {
					return nil, isNew, err
				}
				hasRelease = true
				break
			}
		}
		logs.V1.CtxInfo(ctx, "sandbox instance hit limit, need release one, release result: %t", hasRelease)
		if !hasRelease {
			return nil, isNew, errors.New("sandbox instance hit limit")
		}
	}

	// 创建新的 sandbox 实例
	sandbox, err = a.sandbox.CreateSandbox(ctx, &sandboxService.CreateSandboxOpt{
		AllocationID: strconv.FormatInt(userID, 10),
		SessionID:    conversationID,
		FunctionType: sandboxEntity.FunctionTypeAgent,
		SandboxType:  sandboxEntity.SandboxTypeShare,
		AliveTime:    &a.sandboxAPIConfig.GetPointer().DataAnalysis.AliveTime,
		Extension:    true,
	})
	if err != nil {
		return nil, isNew, err
	}
	isNew = true
	return sandbox, isNew, nil
}

// renewSandboxInstance 刷新 sandbox 实例，执行个命令就会自动续期
func (a *AgentRunServiceImpl) renewSandboxInstance(ctx context.Context, userID int64, conversationID string) error {
	_, err := a.ExecuteCode(ctx, &service.ExecuteCodeOpt{
		UserID:         userID,
		ConversationID: conversationID,
		Code:           "ls",
		Language:       "shell",
	})
	if err != nil {
		return err
	}
	return nil
}
