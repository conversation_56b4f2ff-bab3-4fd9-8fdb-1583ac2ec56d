package agent_run

import (
	"encoding/json"
	"errors"
	"testing"
	"time"

	"github.com/bytedance/mockey"
	"github.com/coocood/freecache"
	. "github.com/smartystreets/goconvey/convey"

	"code.byted.org/devgpt/kiwis/codeassist/agent/entity"
)

func TestLocalCache_GetAgentRun(t *testing.T) {
	mockey.PatchConvey("Test GetAgentRun", t, func() {
		// 创建测试数据
		agentRunID := "test-agent-run-id"
		agentRun := &entity.AgentRun{
			UUID:           agentRunID,
			TaskID:         "test-task-id",
			UserID:         123456,
			ConversationID: "test-conversation-id",
			AgentID:        "test-agent-id",
			AgentVersion:   "1.0.0",
			AgentMeta:      &entity.AgentRunMeta{Config: "test-config"},
			AgentInput:     &entity.AgentRunInput{},
			Status:         entity.AgentRunStatusRunning,
			WorkspaceDir:   "/tmp/workspace",
			CreatedAt:      time.Now(),
		}

		// 序列化测试数据
		agentRunData, _ := json.Marshal(agentRun)

		mockey.PatchConvey("When cache hit", func() {
			// 创建 LocalCache 实例
			localCache := NewLocalCache()

			// Mock freecache.Get 方法
			mockey.Mock((*freecache.Cache).Get).Return(agentRunData, nil).Build()

			// 调用被测试方法
			result, err := localCache.GetAgentRun(agentRunID)

			// 验证结果
			So(err, ShouldBeNil)
			So(result, ShouldNotBeNil)
			So(result.UUID, ShouldEqual, agentRun.UUID)
			So(result.TaskID, ShouldEqual, agentRun.TaskID)
			So(result.UserID, ShouldEqual, agentRun.UserID)
			So(result.Status, ShouldEqual, agentRun.Status)
		})

		mockey.PatchConvey("When cache miss", func() {
			// 创建 LocalCache 实例
			localCache := NewLocalCache()

			// Mock freecache.Get 方法返回错误
			mockey.Mock((*freecache.Cache).Get).Return(nil, freecache.ErrNotFound).Build()

			// 调用被测试方法
			result, err := localCache.GetAgentRun(agentRunID)

			// 验证结果
			So(err, ShouldNotBeNil)
			So(result, ShouldBeNil)
		})

		mockey.PatchConvey("When unmarshal error", func() {
			// 创建 LocalCache 实例
			localCache := NewLocalCache()

			// Mock freecache.Get 方法返回无效的 JSON 数据
			mockey.Mock((*freecache.Cache).Get).Return([]byte("invalid json"), nil).Build()

			// 调用被测试方法
			result, err := localCache.GetAgentRun(agentRunID)

			// 验证结果
			So(err, ShouldNotBeNil)
			So(result, ShouldBeNil)
		})
	})
}

func TestLocalCache_SetAgentRun(t *testing.T) {
	mockey.PatchConvey("Test SetAgentRun", t, func() {
		// 创建测试数据
		agentRunID := "test-agent-run-id"
		agentRun := &entity.AgentRun{
			UUID:           agentRunID,
			TaskID:         "test-task-id",
			UserID:         123456,
			ConversationID: "test-conversation-id",
			AgentID:        "test-agent-id",
			AgentVersion:   "1.0.0",
			AgentMeta:      &entity.AgentRunMeta{Config: "test-config"},
			AgentInput:     &entity.AgentRunInput{},
			Status:         entity.AgentRunStatusRunning,
			WorkspaceDir:   "/tmp/workspace",
			CreatedAt:      time.Now(),
		}

		mockey.PatchConvey("When marshal success", func() {
			// 创建 LocalCache 实例
			localCache := NewLocalCache()

			// Mock freecache.Set 方法
			mockey.Mock((*freecache.Cache).Set).Return(nil).Build()

			// 调用被测试方法
			err := localCache.SetAgentRun(agentRun)

			// 验证结果
			So(err, ShouldBeNil)
		})

		mockey.PatchConvey("When marshal error", func() {
			// 创建 LocalCache 实例
			localCache := NewLocalCache()

			// Mock json.Marshal 方法返回错误
			mockey.Mock(json.Marshal).Return(nil, errors.New("marshal error")).Build()

			// 调用被测试方法
			err := localCache.SetAgentRun(agentRun)

			// 验证结果
			So(err, ShouldNotBeNil)
		})
	})
}

func TestLocalCache_DelAgentRun(t *testing.T) {
	mockey.PatchConvey("Test DelAgentRun", t, func() {
		// 创建测试数据
		agentRunID := "test-agent-run-id"

		mockey.PatchConvey("When delete success", func() {

			// 创建 LocalCache 实例
			localCache := NewLocalCache()
			// Mock freecache.Del 方法返回成功
			mockey.Mock((*freecache.Cache).Del).Return(true).Build()

			// 调用被测试方法
			result := localCache.DelAgentRun(agentRunID)

			// 验证结果
			So(result, ShouldBeTrue)
		})

		mockey.PatchConvey("When delete fail", func() {
			// 创建 LocalCache 实例
			localCache := NewLocalCache()

			// Mock freecache.Del 方法返回失败
			mockey.Mock((*freecache.Cache).Del).Return(false).Build()

			// 调用被测试方法
			result := localCache.DelAgentRun(agentRunID)

			// 验证结果
			So(result, ShouldBeFalse)
		})
	})
}
