package agent_run

import (
	"encoding/json"

	"code.byted.org/gopkg/pkg/errors"
	"github.com/coocood/freecache"

	"code.byted.org/devgpt/kiwis/codeassist/agent/entity"
)

type LocalCache struct {
	agentRunCache freecache.Cache
}

func NewLocalCache() *LocalCache {
	return &LocalCache{
		agentRunCache: *freecache.NewCache(100 * 1024 * 1024),
	}
}

func (l *LocalCache) GetAgentRun(agentRunId string) (*entity.AgentRun, error) {
	key := []byte(agentRunId)
	data, err := l.agentRunCache.Get(key)
	if err != nil {
		return nil, err
	}
	var agentRun entity.AgentRun
	err = json.Unmarshal(data, &agentRun)
	if err != nil {
		return nil, err
	}
	return &agentRun, nil
}

func (l *LocalCache) SetAgentRun(instance *entity.AgentRun) error {
	key := []byte(instance.UUID)
	value, err := json.Marshal(instance)
	if err != nil {
		return errors.Wrap(err, "agent run json marshal fail")
	}
	l.agentRunCache.Set(key, value, 30*60)
	return nil
}

func (l *LocalCache) DelAgentRun(agentRunID string) bool {
	return l.agentRunCache.Del([]byte(agentRunID))
}
