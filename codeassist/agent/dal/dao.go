package dal

import (
	"context"

	"github.com/pkg/errors"

	"code.byted.org/devgpt/kiwis/codeassist/agent/entity"
)

var UpdateNothingError = errors.New("update nothing error")

//go:generate go run go.uber.org/mock/mockgen@latest -destination ../mock/dal/task_dao_impl.go -package dal code.byted.org/devgpt/kiwis/codeassist/agent/dal TaskDAO
type TaskDAO interface {
	Create(ctx context.Context, task *entity.Task) (string, error)
	GetByID(ctx context.Context, uniqueID string) (*entity.Task, error)
	UpdateStatusByID(ctx context.Context, uniqueID string, oldStatus entity.TaskStatus, newStatus entity.TaskStatus) error
	GetStatusByID(ctx context.Context, uniqueID string) (entity.TaskStatus, error)
	UpdateStageByID(ctx context.Context, uniqueID string, stage entity.TaskStage) error
	UpdateStageByMessageID(ctx context.Context, messageID string, stage entity.TaskStage) error
	UpdateStageByMessageIDAndUser(ctx context.Context, messageID string, userID string, stage entity.TaskStage) error
	GetStageByID(ctx context.Context, uniqueID string) (entity.TaskStage, error)
	FilterGivenMessageList(ctx context.Context, messageIDs []string) ([]string, error)
}

//go:generate go run go.uber.org/mock/mockgen@latest -destination ../mock/dal/agent_run_dao.go -package dal code.byted.org/devgpt/kiwis/codeassist/agent/dal AgentRunDAO
type AgentRunDAO interface {
	GetByID(ctx context.Context, uniqueID string) (*entity.AgentRun, error)
	GetByTaskID(ctx context.Context, taskID string) (*entity.AgentRun, error)
	Create(ctx context.Context, agentRun *entity.AgentRun) (string, error)
	UpdateStatus(ctx context.Context, agentRunID string, status entity.AgentRunStatus) error
	GetByConversationID(ctx context.Context, conversationID string, status []entity.AgentRunStatus) ([]*entity.AgentRun, error)
}

//go:generate go run go.uber.org/mock/mockgen@latest -destination ../mock/dal/agent_run_step_dao.go -package dal code.byted.org/devgpt/kiwis/codeassist/agent/dal AgentRunStepDAO
type AgentRunStepDAO interface {
	GetByID(ctx context.Context, uniqueID string) (*entity.AgentRunStep, error)
	GetByAgentRunID(ctx context.Context, agentRunID string, status entity.AgentRunStepStatus) ([]*entity.AgentRunStep, error)
	Create(ctx context.Context, agentRunStep *entity.AgentRunStep) (string, error)
	UpdateByID(ctx context.Context, uniqueId string, status entity.AgentRunStepStatus, metadata *entity.AgentRunStepMetaData) error
}

//go:generate go run go.uber.org/mock/mockgen@latest -destination ../mock/dal/agent_run_step_tool_call_dao.go -package dal code.byted.org/devgpt/kiwis/codeassist/agent/dal AgentRunStepToolCallDAO
type AgentRunStepToolCallDAO interface {
	GetByID(ctx context.Context, uniqueID string) (*entity.AgentRunStepToolCall, error)
	GetByAgentRunStepID(ctx context.Context, agentRunStepID string) ([]*entity.AgentRunStepToolCall, error)
	Create(ctx context.Context, agentRunStepToolCall *entity.AgentRunStepToolCall) (string, error)
	MCreate(ctx context.Context, agentRunStepToolCall ...*entity.AgentRunStepToolCall) error
}

//go:generate go run go.uber.org/mock/mockgen@latest -destination ../mock/dal/agent_run_step_message_dao.go -package dal code.byted.org/devgpt/kiwis/codeassist/agent/dal AgentRunStepMessageDAO
type AgentRunStepMessageDAO interface {
	Set(ctx context.Context, messageID string, message string) error
	Get(ctx context.Context, messageID string) (string, error)
}

//go:generate go run go.uber.org/mock/mockgen@latest -destination ../mock/dal/tool_call_observation_dao.go -package dal code.byted.org/devgpt/kiwis/codeassist/agent/dal ToolCallObservationDAO
type ToolCallObservationDAO interface {
	Set(ctx context.Context, toolCallID string, message string) error
	Get(ctx context.Context, toolCallID string) (string, error)
}
