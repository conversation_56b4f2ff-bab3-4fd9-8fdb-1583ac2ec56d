package impl

import (
	"context"
	"time"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"gorm.io/datatypes"

	"code.byted.org/devgpt/kiwis/codeassist/agent/dal"
	"code.byted.org/devgpt/kiwis/codeassist/agent/dal/po"
	"code.byted.org/devgpt/kiwis/codeassist/agent/entity"
	"code.byted.org/devgpt/kiwis/port/db"
)

var _ dal.AgentRunStepDAO = &AgentRunStepDAOImpl{}

type AgentRunStepDAOImpl struct {
	DB db.Client
}

// entity 转 po
func createAgentRunStepPO(agentRunStep *entity.AgentRunStep) *po.AgentRunStepPO {
	if agentRunStep.UUID == "" {
		agentRunStep.UUID = uuid.NewString()
	}

	agentRunStepPO := &po.AgentRunStepPO{
		UniqueID:   agentRunStep.UUID,
		AgentRunID: agentRunStep.AgentRunID,
		Actor:      agentRunStep.Actor,
		Round:      int64(agentRunStep.Round),
		Status:     int8(agentRunStep.Status),
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
		ActorIndex: int64(agentRunStep.ActorIndex),
	}

	if agentRunStep.MetaData != nil {
		agentRunStepPO.MetaData = lo.ToPtr(datatypes.NewJSONType(*agentRunStep.MetaData))
	}
	return agentRunStepPO
}

// po 转 entity
func getAgentRunStepFromPO(p *po.AgentRunStepPO) *entity.AgentRunStep {
	agentRunStep := &entity.AgentRunStep{
		UUID:       p.UniqueID,
		AgentRunID: p.AgentRunID,
		Actor:      p.Actor,
		Round:      int(p.Round),
		Status:     entity.AgentRunStepStatus(p.Status),
		ActorIndex: int(p.ActorIndex),
	}

	if p.MetaData != nil {
		agentRunStep.MetaData = lo.ToPtr(p.MetaData.Data())
	}
	return agentRunStep
}

func (d *AgentRunStepDAOImpl) GetByID(ctx context.Context, uniqueID string) (*entity.AgentRunStep, error) {
	agentRunStepPO := &po.AgentRunStepPO{}
	res := d.DB.NewRequest(ctx).Where("unique_id=?", uniqueID).First(agentRunStepPO)
	if err := res.Error; err != nil {
		if db.IsRecordNotFoundError(err) {
			return nil, nil
		}
		return nil, errors.WithMessage(err, "get agent_run_step failed")
	}
	return getAgentRunStepFromPO(agentRunStepPO), nil
}

func (d *AgentRunStepDAOImpl) GetByAgentRunID(ctx context.Context, agentRunID string, status entity.AgentRunStepStatus) ([]*entity.AgentRunStep, error) {
	agentRunStepPOs := make([]*po.AgentRunStepPO, 0)
	where := map[string]any{
		"agent_run_id": agentRunID,
	}
	if status != 0 {
		where["status"] = int8(status)
	}
	res := d.DB.NewRequest(ctx).Where(where).Find(&agentRunStepPOs)
	if err := res.Error; err != nil {
		return nil, errors.Wrap(err, "get agent_run_step failed")
	}
	return lo.Map(agentRunStepPOs, func(agentRunStepPO *po.AgentRunStepPO, _ int) *entity.AgentRunStep {
		return getAgentRunStepFromPO(agentRunStepPO)
	}), nil
}

func (d *AgentRunStepDAOImpl) Create(ctx context.Context, agentRunStep *entity.AgentRunStep) (string, error) {
	agentRunStepPO := createAgentRunStepPO(agentRunStep)
	res := d.DB.NewRequest(ctx).Create(agentRunStepPO)
	if err := res.Error; err != nil {
		return "", errors.Wrap(err, "create agent_run_step failed")
	}
	return agentRunStepPO.UniqueID, nil
}

func (d *AgentRunStepDAOImpl) UpdateByID(ctx context.Context, uniqueID string, newStatus entity.AgentRunStepStatus, metadata *entity.AgentRunStepMetaData) error {
	updateMap := map[string]any{}
	if newStatus != 0 {
		updateMap["status"] = int8(newStatus)
	}
	if metadata != nil {
		updateMap["meta_data"] = datatypes.NewJSONType(*metadata)
	}
	updateMap["updated_at"] = time.Now()
	res := d.DB.NewRequest(ctx).Model(&po.AgentRunStepPO{}).Where("unique_id =?", uniqueID).Updates(updateMap)
	if err := res.Error; err != nil {
		return errors.Wrap(err, "update agent run step status failed")
	}
	if res.RowsAffected < 1 {
		return dal.UpdateNothingError
	}
	return nil
}
