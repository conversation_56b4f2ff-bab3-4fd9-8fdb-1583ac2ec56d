package impl

import (
	"context"
	"strconv"
	"time"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"gorm.io/datatypes"

	"code.byted.org/devgpt/kiwis/codeassist/agent/dal"
	"code.byted.org/devgpt/kiwis/codeassist/agent/dal/po"
	"code.byted.org/devgpt/kiwis/codeassist/agent/entity"
	"code.byted.org/devgpt/kiwis/port/db"
)

var _ dal.AgentRunDAO = &AgentRunDAOImpl{}

type AgentRunDAOImpl struct {
	DB db.Client
}

// entity 转 po
func createAgentRunPO(agentRun *entity.AgentRun) *po.AgentRunPO {
	agentRun.UUID = uuid.NewString()

	agentRunPO := &po.AgentRunPO{
		UniqueID:       agentRun.UUID,
		TaskID:         agentRun.TaskID,
		AgentID:        agentRun.AgentID,
		AgentVersion:   agentRun.AgentVersion,
		UserID:         strconv.FormatInt(agentRun.UserID, 10),
		ConversationID: agentRun.ConversationID,
		Status:         int8(agentRun.Status),
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}

	if agentRun.AgentMeta != nil {
		agentRunPO.AgentMeta = lo.ToPtr(datatypes.NewJSONType(*agentRun.AgentMeta))
	}
	if agentRun.AgentInput != nil {
		agentRunPO.AgentInput = lo.ToPtr(datatypes.NewJSONType(*agentRun.AgentInput))
	}
	return agentRunPO
}

// po 转 entity
func getAgentRunFromPO(p *po.AgentRunPO) *entity.AgentRun {
	agentRun := &entity.AgentRun{
		UUID:           p.UniqueID,
		TaskID:         p.TaskID,
		AgentID:        p.AgentID,
		AgentVersion:   p.AgentVersion,
		Status:         entity.AgentRunStatus(p.Status),
		CreatedAt:      p.CreatedAt,
		UserID:         lo.Must(strconv.ParseInt(p.UserID, 10, 64)),
		ConversationID: p.ConversationID,
	}

	if p.AgentMeta != nil {
		agentRun.AgentMeta = lo.ToPtr(p.AgentMeta.Data())
	}
	if p.AgentInput != nil {
		agentRun.AgentInput = lo.ToPtr(p.AgentInput.Data())
	}

	return agentRun
}

func (d *AgentRunDAOImpl) GetByID(ctx context.Context, uniqueID string) (*entity.AgentRun, error) {
	agentRunPO := &po.AgentRunPO{}
	res := d.DB.NewRequest(ctx).Where("unique_id=?", uniqueID).First(agentRunPO)
	if err := res.Error; err != nil {
		if db.IsRecordNotFoundError(err) {
			return nil, nil
		}
		return nil, errors.Wrap(err, "get agent run failed")
	}
	return getAgentRunFromPO(agentRunPO), nil
}

func (d *AgentRunDAOImpl) Create(ctx context.Context, agentRun *entity.AgentRun) (string, error) {
	agentRunPO := createAgentRunPO(agentRun)
	res := d.DB.NewRequest(ctx).Create(agentRunPO)
	if err := res.Error; err != nil {
		return "", errors.Wrap(err, "create agent run failed")
	}
	return agentRunPO.UniqueID, nil
}

func (d *AgentRunDAOImpl) GetByTaskID(ctx context.Context, taskID string) (*entity.AgentRun, error) {
	agentRunPO := &po.AgentRunPO{}
	res := d.DB.NewRequest(ctx).Where("task_id=?", taskID).First(agentRunPO)
	if err := res.Error; err != nil {
		if db.IsRecordNotFoundError(err) {
			return nil, nil
		}
		return nil, errors.Wrap(err, "get agent run by task_id failed")
	}
	return getAgentRunFromPO(agentRunPO), nil
}

func (d *AgentRunDAOImpl) GetByConversationID(ctx context.Context, conversationID string, status []entity.AgentRunStatus) ([]*entity.AgentRun, error) {
	agentRunPOs := make([]*po.AgentRunPO, 0)
	res := d.DB.NewRequest(ctx).
		Where("conversation_id=?", conversationID).
		Where("status in ?", status).
		Find(&agentRunPOs)
	if err := res.Error; err != nil {
		return nil, errors.Wrap(err, "get agent_run_step failed")
	}
	return lo.Map(agentRunPOs, func(agentRunPO *po.AgentRunPO, _ int) *entity.AgentRun {
		return getAgentRunFromPO(agentRunPO)
	}), nil
}

func (d *AgentRunDAOImpl) UpdateStatus(ctx context.Context, uniqueID string, status entity.AgentRunStatus) error {
	res := d.DB.NewRequest(ctx).Model(&po.AgentRunPO{}).Where("unique_id=?", uniqueID).Update("status", int8(status))
	if err := res.Error; err != nil {
		return errors.Wrap(err, "update agent run status failed")
	}
	return nil
}
