package impl

import (
	"context"
	"time"

	"code.byted.org/devgpt/kiwis/codeassist/agent/dal"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/port/abase"
)

type ToolCallObservationDAOImpl struct {
	AbaseCli *abase.Client
	Config   *config.CodeAssistAssistantConfig
}

var _ dal.ToolCallObservationDAO = &ToolCallObservationDAOImpl{}

func (d *ToolCallObservationDAOImpl) Set(ctx context.Context, toolCallID string, message string) error {
	return d.AbaseCli.Set(ctx, toolCallID, message, expiredHour*time.Hour)
}

func (d *ToolCallObservationDAOImpl) Get(ctx context.Context, toolCallID string) (string, error) {
	return d.AbaseCli.Get(ctx, toolCallID)
}
