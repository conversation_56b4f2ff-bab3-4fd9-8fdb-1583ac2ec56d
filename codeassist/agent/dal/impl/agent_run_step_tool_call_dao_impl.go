package impl

import (
	"context"
	"time"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"github.com/samber/lo"

	"code.byted.org/devgpt/kiwis/codeassist/agent/dal"
	"code.byted.org/devgpt/kiwis/codeassist/agent/dal/po"
	"code.byted.org/devgpt/kiwis/codeassist/agent/entity"
	"code.byted.org/devgpt/kiwis/port/db"
)

var _ dal.AgentRunStepToolCallDAO = &AgentRunStepToolCallDAOImpl{}

type AgentRunStepToolCallDAOImpl struct {
	DB db.Client
}

// entity 转 po
func createAgentRunStepToolCallPO(toolCall *entity.AgentRunStepToolCall) *po.AgentRunStepToolCallPO {
	if toolCall.UUID == "" {
		toolCall.UUID = uuid.NewString()
	}
	return &po.AgentRunStepToolCallPO{
		UniqueID:       toolCall.UUID,
		AgentRunID:     toolCall.AgentRunID,
		AgentRunStepID: toolCall.AgentRunStepID,
		ToolName:       toolCall.ToolName,
		ToolIndex:      int64(toolCall.Index),
		Status:         toolCall.Status,
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}
}

// po 转 entity
func getAgentRunStepToolCallFromPO(p *po.AgentRunStepToolCallPO) *entity.AgentRunStepToolCall {
	return &entity.AgentRunStepToolCall{
		UUID:           p.UniqueID,
		AgentRunID:     p.AgentRunID,
		AgentRunStepID: p.AgentRunStepID,
		ToolName:       p.ToolName,
		Index:          int(p.ToolIndex),
		Status:         p.Status,
	}
}

func (d *AgentRunStepToolCallDAOImpl) GetByID(ctx context.Context, uniqueID string) (*entity.AgentRunStepToolCall, error) {
	poObj := &po.AgentRunStepToolCallPO{}
	res := d.DB.NewRequest(ctx).Where("unique_id=?", uniqueID).First(poObj)
	if err := res.Error; err != nil {
		if db.IsRecordNotFoundError(err) {
			return nil, nil
		}
		return nil, errors.WithMessage(err, "get agent_run_step_tool_call failed")
	}
	return getAgentRunStepToolCallFromPO(poObj), nil
}

func (d *AgentRunStepToolCallDAOImpl) GetByAgentRunStepID(ctx context.Context, agentRunStepID string) ([]*entity.AgentRunStepToolCall, error) {
	var poObjs []*po.AgentRunStepToolCallPO
	res := d.DB.NewRequest(ctx).Where("agent_run_step_id=?", agentRunStepID).Find(&poObjs)
	if err := res.Error; err != nil {
		return nil, errors.Wrap(err, "get agent_run_step_tool_call failed")
	}
	return lo.Map(poObjs, func(poObj *po.AgentRunStepToolCallPO, _ int) *entity.AgentRunStepToolCall {
		return getAgentRunStepToolCallFromPO(poObj)
	}), nil
}

func (d *AgentRunStepToolCallDAOImpl) Create(ctx context.Context, toolCall *entity.AgentRunStepToolCall) (string, error) {
	poObj := createAgentRunStepToolCallPO(toolCall)
	res := d.DB.NewRequest(ctx).Create(poObj)
	if err := res.Error; err != nil {
		return "", err
	}
	return poObj.UniqueID, nil
}

func (d *AgentRunStepToolCallDAOImpl) MCreate(ctx context.Context, toolCalls ...*entity.AgentRunStepToolCall) error {
	poObjs := lo.Map(toolCalls, func(toolCall *entity.AgentRunStepToolCall, _ int) *po.AgentRunStepToolCallPO {
		return createAgentRunStepToolCallPO(toolCall)
	})
	res := d.DB.NewRequest(ctx).Create(poObjs)
	if err := res.Error; err != nil {
		return err
	}
	return nil
}
