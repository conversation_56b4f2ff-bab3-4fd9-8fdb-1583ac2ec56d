package impl

import (
	"context"
	"time"

	"code.byted.org/devgpt/kiwis/codeassist/agent/dal"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/port/abase"
)

const expiredHour = 24 * 3

type AgentRunStepMessageDAOImpl struct {
	AbaseCli *abase.Client
	Config   *config.CodeAssistAssistantConfig
}

var _ dal.AgentRunStepMessageDAO = &AgentRunStepMessageDAOImpl{}

func (d *AgentRunStepMessageDAOImpl) Set(ctx context.Context, messageID string, message string) error {
	return d.AbaseCli.Set(ctx, messageID, message, expiredHour*time.Hour)
}

func (d *AgentRunStepMessageDAOImpl) Get(ctx context.Context, messageID string) (string, error) {
	return d.AbaseCli.Get(ctx, messageID)
}
