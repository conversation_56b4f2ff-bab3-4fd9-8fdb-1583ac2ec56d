package impl

import (
	"go.uber.org/fx"

	"code.byted.org/devgpt/kiwis/codeassist/agent/dal"
	"code.byted.org/devgpt/kiwis/lib/di"
)

var Module = fx.Options(
	fx.Provide(di.StructConstructor(new(AgentRunDAOImpl))),
	fx.Provide(di.Bind(new(AgentRunDAOImpl), new(dal.AgentRunDAO))),

	fx.Provide(di.StructConstructor(new(AgentRunStepDAOImpl))),
	fx.Provide(di.Bind(new(AgentRunStepDAOImpl), new(dal.AgentRunStepDAO))),

	fx.Provide(fx.Annotate(di.StructConstructor(new(AgentRunStepMessageDAOImpl)), fx.ParamTags(`name:"message_abase_client"`, ""))),
	fx.Provide(di.Bind(new(AgentRunStepMessageDAOImpl), new(dal.AgentRunStepMessageDAO))),

	fx.Provide(di.StructConstructor(new(AgentRunStepToolCallDAOImpl))),
	fx.Provide(di.Bind(new(AgentRunStepToolCallDAOImpl), new(dal.AgentRunStepToolCallDAO))),

	fx.Provide(fx.Annotate(di.StructConstructor(new(ToolCallObservationDAOImpl)), fx.ParamTags(`name:"observation_abase_client"`, ""))),
	fx.Provide(di.Bind(new(ToolCallObservationDAOImpl), new(dal.ToolCallObservationDAO))),

	fx.Provide(di.StructConstructor(new(TaskDAOImpl))),
	fx.Provide(di.Bind(new(TaskDAOImpl), new(dal.TaskDAO))),
)
