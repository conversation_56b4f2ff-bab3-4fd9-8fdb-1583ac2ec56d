package impl

import (
	"context"
	"time"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"github.com/samber/lo"

	"code.byted.org/devgpt/kiwis/codeassist/agent/dal"
	"code.byted.org/devgpt/kiwis/codeassist/agent/dal/po"
	"code.byted.org/devgpt/kiwis/codeassist/agent/entity"
	"code.byted.org/devgpt/kiwis/port/db"
)

var _ dal.TaskDAO = &TaskDAOImpl{}

type TaskDAOImpl struct {
	DB db.Client
}

func createTaskPO(task *entity.Task, _ int) *po.TaskPO {
	task.UUID = uuid.NewString()

	return &po.TaskPO{
		UniqueID:       task.UUID,
		UserID:         task.UserID,
		ConversationID: task.ConversationID,
		MessageID:      task.MessageID,
		Upstream:       string(task.Upstream),
		Status:         string(task.Status),
		Stage:          string(task.Stage),
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}
}

func getTaskFromPO(p *po.TaskPO) *entity.Task {
	return &entity.Task{
		UUID:           p.UniqueID,
		UserID:         p.UserID,
		ConversationID: p.ConversationID,
		MessageID:      p.MessageID,
		Upstream:       entity.TaskUpstream(p.Upstream),
		Status:         entity.TaskStatus(p.Status),
		Stage:          entity.TaskStage(p.Stage),
	}
}

func (d *TaskDAOImpl) GetByID(ctx context.Context, uniqueID string) (*entity.Task, error) {
	taskPO := &po.TaskPO{}
	res := d.DB.NewRequest(ctx).Where("unique_id=?", uniqueID).First(taskPO)
	if err := res.Error; err != nil {
		if db.IsRecordNotFoundError(err) {
			return nil, nil
		}
		return nil, errors.WithMessage(err, "get task failed")
	}
	return getTaskFromPO(taskPO), nil
}

func (d *TaskDAOImpl) Create(ctx context.Context, task *entity.Task) (string, error) {
	taskPO := createTaskPO(task, 0)

	res := d.DB.NewRequest(ctx).Create(taskPO)
	if err := res.Error; err != nil {
		return "", err
	}
	return taskPO.UniqueID, nil
}

// UpdateStatusByID 根据 uniqueID 和 oldStatus 更新为 newStatus
func (d *TaskDAOImpl) UpdateStatusByID(ctx context.Context, uniqueID string, oldStatus entity.TaskStatus, newStatus entity.TaskStatus) error {
	updateMap := map[string]any{
		"status":     string(newStatus),
		"updated_at": time.Now(),
	}
	res := d.DB.NewRequest(ctx).Model(&po.TaskPO{}).Where("unique_id=? AND status=?", uniqueID, string(oldStatus)).Updates(updateMap)
	if err := res.Error; err != nil {
		return errors.Wrap(err, "update task status failed")
	}
	if res.RowsAffected < 1 {
		return dal.UpdateNothingError
	}
	return nil
}

func (d *TaskDAOImpl) UpdateStageByMessageID(ctx context.Context, messageID string, stage entity.TaskStage) error {
	updateMap := map[string]any{
		"stage":      string(stage),
		"updated_at": time.Now(),
	}
	res := d.DB.NewRequest(ctx).Model(&po.TaskPO{}).Where("message_id=?", messageID).Updates(updateMap)
	if err := res.Error; err != nil {
		return errors.Wrap(err, "update task stage failed")
	}
	if res.RowsAffected < 1 {
		return dal.UpdateNothingError
	}
	return nil
}

// GetStatusByID 获取任务状态
func (d *TaskDAOImpl) GetStatusByID(ctx context.Context, uniqueID string) (entity.TaskStatus, error) {
	taskPO := &po.TaskPO{}
	res := d.DB.NewRequest(ctx).Where("unique_id=?", uniqueID).First(taskPO)
	if err := res.Error; err != nil {
		if db.IsRecordNotFoundError(err) {
			return "", nil
		}
		return "", errors.WithMessage(err, "get task status failed")
	}
	return entity.TaskStatus(taskPO.Status), nil
}

// UpdateStageByID 根据 uniqueID 更新 stage
func (d *TaskDAOImpl) UpdateStageByID(ctx context.Context, uniqueID string, stage entity.TaskStage) error {
	updateMap := map[string]any{
		"stage":      string(stage),
		"updated_at": time.Now(),
	}
	res := d.DB.NewRequest(ctx).Model(&po.TaskPO{}).Where("unique_id=?", uniqueID).Updates(updateMap)
	if err := res.Error; err != nil {
		return errors.Wrap(err, "update task stage failed")
	}
	if res.RowsAffected < 1 {
		return dal.UpdateNothingError
	}
	return nil
}

// GetStageByID 获取任务阶段
func (d *TaskDAOImpl) GetStageByID(ctx context.Context, uniqueID string) (entity.TaskStage, error) {
	taskPO := &po.TaskPO{}
	res := d.DB.NewRequest(ctx).Where("unique_id=?", uniqueID).First(taskPO)
	if err := res.Error; err != nil {
		if db.IsRecordNotFoundError(err) {
			return "", nil
		}
		return entity.TaskStageUnknown, errors.WithMessage(err, "get task stage failed")
	}
	return entity.TaskStage(taskPO.Stage), nil
}

func (d *TaskDAOImpl) FilterGivenMessageList(ctx context.Context, messageIDs []string) ([]string, error) {
	var taskList []*po.TaskPO
	res := d.DB.NewRequest(ctx).Where("message_id in (?)", messageIDs).Find(&taskList)
	if err := res.Error; err != nil {
		return nil, errors.WithMessage(err, "get message list failed")
	}
	return lo.Map(taskList, func(item *po.TaskPO, _ int) string {
		return item.MessageID
	}), nil
}

func (d *TaskDAOImpl) UpdateStageByMessageIDAndUser(ctx context.Context, messageID string, userID string, stage entity.TaskStage) error {
	updateMap := map[string]any{
		"stage":      string(stage),
		"updated_at": time.Now(),
	}
	res := d.DB.NewRequest(ctx).Model(&po.TaskPO{}).Where("message_id=? AND user_id=?", messageID, userID).Updates(updateMap)
	if err := res.Error; err != nil {
		return errors.Wrap(err, "update task stage by messageID and userID failed")
	}
	if res.RowsAffected < 1 {
		return dal.UpdateNothingError
	}
	return nil
}
