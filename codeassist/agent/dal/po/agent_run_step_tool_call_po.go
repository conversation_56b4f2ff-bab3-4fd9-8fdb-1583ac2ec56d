// Code generated by SQL PO generator, DO NOT EDIT.
// This src file contains PO(persistent object) struct definition for database table `agent_run_step_tool_call`.
package po

import "time"

// AgentRunStepToolCallPO is agent run step tool call entity.
type AgentRunStepToolCallPO struct {
	// ID is ID.
	ID int64 `gorm:"column:id;primaryKey"`
	// UniqueID is unique string ID, usually UUID.
	//
	// index: uniq_unique_id, priority: 1.
	//
	UniqueID string `gorm:"column:unique_id;size:64"`
	// AgentRunID is agent entity id.
	//
	// index: idx_agent_run_id, priority: 1.
	//
	AgentRunID string `gorm:"column:agent_run_id;size:64"`
	// AgentRunStepID is agent run step id.
	//
	// index: idx_agent_run_step_id, priority: 1.
	//
	AgentRunStepID string `gorm:"column:agent_run_step_id;size:64"`
	// ToolIndex is tool index in step.
	ToolIndex int64 `gorm:"column:tool_index"`
	// ToolName is tool name.
	ToolName string `gorm:"column:tool_name;size:128"`
	// Status is tool call status.
	Status string `gorm:"column:status;size:64"`
	// CreatedAt is create timestamp.
	CreatedAt time.Time `gorm:"column:created_at"`
	// UpdatedAt is last update timestamp.
	UpdatedAt time.Time `gorm:"column:updated_at"`
}

// TableName returns PO's corresponding DB table name: `agent_run_step_tool_call`.
func (*AgentRunStepToolCallPO) TableName() string {
	return "agent_run_step_tool_call"
}
