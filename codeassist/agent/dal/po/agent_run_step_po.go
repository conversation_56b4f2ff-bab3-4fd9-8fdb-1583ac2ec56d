// Code generated by SQL PO generator, DO NOT EDIT.
// This src file contains PO(persistent object) struct definition for database table `agent_run_step`.
package po

import (
	datatypes "gorm.io/datatypes"
	"time"
)

// AgentRunStepPO is agent run step entity.
type AgentRunStepPO struct {
	// ID is ID.
	ID int64 `gorm:"column:id;primaryKey"`
	// UniqueID is unique string ID, usually UUID.
	//
	// index: uniq_unique_id, priority: 1.
	//
	UniqueID string `gorm:"column:unique_id;size:64"`
	// AgentRunID is agent entity id.
	//
	// index: idx_run_id_status, priority: 1.
	//
	AgentRunID string `gorm:"column:agent_run_id;size:64"`
	// Actor is actor name.
	Actor string `gorm:"column:actor;size:128"`
	// ActorIndex is actor index.
	ActorIndex int64 `gorm:"column:actor_index"`
	// Round is round number in actor.
	Round int64 `gorm:"column:round"`
	// MetaData is agent run step meta data.
	MetaData *datatypes.JSONType[AgentRunStepMetaData] `gorm:"column:meta_data"`
	// Status is status: 1-running,2-completed.
	//
	// index: idx_run_id_status, priority: 2.
	//
	Status int8 `gorm:"column:status"`
	// CreatedAt is create timestamp.
	CreatedAt time.Time `gorm:"column:created_at"`
	// UpdatedAt is last update timestamp.
	UpdatedAt time.Time `gorm:"column:updated_at"`
}

// TableName returns PO's corresponding DB table name: `agent_run_step`.
func (*AgentRunStepPO) TableName() string {
	return "agent_run_step"
}
