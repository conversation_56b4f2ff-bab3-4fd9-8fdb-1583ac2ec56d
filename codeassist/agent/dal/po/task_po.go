// Code generated by SQL PO generator, DO NOT EDIT.
// This src file contains PO(persistent object) struct definition for database table `task`.
package po

import "time"

// TaskPO is task entity.
type TaskPO struct {
	// ID is ID.
	ID int64 `gorm:"column:id;primaryKey"`
	// UniqueID is unique string ID, usually UUID.
	//
	// index: uniq_unique_id, priority: 1.
	//
	UniqueID string `gorm:"column:unique_id;size:64"`
	// UserID is user ID.
	//
	// index: idx_user_id, priority: 1.
	//
	UserID string `gorm:"column:user_id;size:128"`
	// ConversationID is conversation id.
	//
	// index: idx_conversation_id, priority: 1.
	//
	ConversationID string `gorm:"column:conversation_id;size:128"`
	// MessageID is message id.
	//
	// index: uniq_message_id, priority: 1.
	//
	MessageID string `gorm:"column:message_id;size:128"`
	// Upstream is upstream type, e.g. doubao....
	Upstream string `gorm:"column:upstream;size:64"`
	// Status is task status.
	Status string `gorm:"column:status;size:64"`
	// Stage is stage in running.
	Stage string `gorm:"column:stage;size:64"`
	// CreatedAt is create timestamp.
	CreatedAt time.Time `gorm:"column:created_at"`
	// UpdatedAt is last update timestamp.
	UpdatedAt time.Time `gorm:"column:updated_at"`
}

// TableName returns PO's corresponding DB table name: `task`.
func (*TaskPO) TableName() string {
	return "task"
}
