// Code generated by SQL PO generator, DO NOT EDIT.
// This src file contains PO(persistent object) struct definition for database table `agent_run`.
package po

import (
	datatypes "gorm.io/datatypes"
	"time"
)

// AgentRunPO is agent run entity.
type AgentRunPO struct {
	// ID is ID.
	ID int64 `gorm:"column:id;primaryKey"`
	// UniqueID is unique string ID, usually UUID.
	//
	// index: uniq_unique_id, priority: 1.
	//
	UniqueID string `gorm:"column:unique_id;size:64"`
	// TaskID is task id.
	//
	// index: uniq_task_id, priority: 1.
	//
	TaskID string `gorm:"column:task_id;size:64"`
	// UserID is user id.
	//
	// index: idx_user_id, priority: 1.
	//
	UserID string `gorm:"column:user_id;size:64"`
	// ConversationID is conversation id.
	//
	// index: idx_conversation_id, priority: 1.
	//
	ConversationID string `gorm:"column:conversation_id;size:64"`
	// AgentID is agent ID, for specific scenarios.
	AgentID string `gorm:"column:agent_id;size:128"`
	// AgentVersion is agent version.
	AgentVersion string `gorm:"column:agent_version;size:64"`
	// AgentMeta is agent meta info.
	AgentMeta *datatypes.JSONType[AgentRunAgentMeta] `gorm:"column:agent_meta"`
	// AgentInput is agent input data.
	AgentInput *datatypes.JSONType[AgentRunAgentInput] `gorm:"column:agent_input"`
	// Status is 状态: 1-created,2-ready,3-running,4-completed,.
	Status int8 `gorm:"column:status"`
	// CreatedAt is create timestamp.
	CreatedAt time.Time `gorm:"column:created_at"`
	// UpdatedAt is last update timestamp.
	UpdatedAt time.Time `gorm:"column:updated_at"`
}

// TableName returns PO's corresponding DB table name: `agent_run`.
func (*AgentRunPO) TableName() string {
	return "agent_run"
}
