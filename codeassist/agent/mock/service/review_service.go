// Code generated by MockGen. DO NOT EDIT.
// Source: code.byted.org/devgpt/kiwis/codeassist/agent/service (interfaces: ReviewService)
//
// Generated by this command:
//
//	mockgen -destination ../mock/service/review_service.go -package service code.byted.org/devgpt/kiwis/codeassist/agent/service ReviewService
//

// Package service is a generated GoMock package.
package service

import (
	context "context"
	reflect "reflect"

	service "code.byted.org/devgpt/kiwis/codeassist/agent/service"
	review "code.byted.org/overpass/ocean_cloud_review/kitex_gen/ocean/cloud/review"
	gomock "go.uber.org/mock/gomock"
)

// MockReviewService is a mock of ReviewService interface.
type MockReviewService struct {
	ctrl     *gomock.Controller
	recorder *MockReviewServiceMockRecorder
	isgomock struct{}
}

// MockReviewServiceMockRecorder is the mock recorder for MockReviewService.
type MockReviewServiceMockRecorder struct {
	mock *MockReviewService
}

// NewMockReviewService creates a new mock instance.
func NewMockReviewService(ctrl *gomock.Controller) *MockReviewService {
	mock := &MockReviewService{ctrl: ctrl}
	mock.recorder = &MockReviewServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockReviewService) EXPECT() *MockReviewServiceMockRecorder {
	return m.recorder
}

// FilenameReview mocks base method.
func (m *MockReviewService) FilenameReview(ctx context.Context, opt *service.ReviewOption) (review.CheckResultEnum, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FilenameReview", ctx, opt)
	ret0, _ := ret[0].(review.CheckResultEnum)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FilenameReview indicates an expected call of FilenameReview.
func (mr *MockReviewServiceMockRecorder) FilenameReview(ctx, opt any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FilenameReview", reflect.TypeOf((*MockReviewService)(nil).FilenameReview), ctx, opt)
}

// FullTextReview mocks base method.
func (m *MockReviewService) FullTextReview(ctx context.Context, opt *service.ReviewOption) (review.CheckResultEnum, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FullTextReview", ctx, opt)
	ret0, _ := ret[0].(review.CheckResultEnum)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FullTextReview indicates an expected call of FullTextReview.
func (mr *MockReviewServiceMockRecorder) FullTextReview(ctx, opt any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FullTextReview", reflect.TypeOf((*MockReviewService)(nil).FullTextReview), ctx, opt)
}

// ImageReview mocks base method.
func (m *MockReviewService) ImageReview(ctx context.Context, opt *service.ReviewOption) (review.CheckResultEnum, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ImageReview", ctx, opt)
	ret0, _ := ret[0].(review.CheckResultEnum)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ImageReview indicates an expected call of ImageReview.
func (mr *MockReviewServiceMockRecorder) ImageReview(ctx, opt any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ImageReview", reflect.TypeOf((*MockReviewService)(nil).ImageReview), ctx, opt)
}

// ReportReview mocks base method.
func (m *MockReviewService) ReportReview(ctx context.Context, opt *service.ReviewOption) (review.CheckResultEnum, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReportReview", ctx, opt)
	ret0, _ := ret[0].(review.CheckResultEnum)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReportReview indicates an expected call of ReportReview.
func (mr *MockReviewServiceMockRecorder) ReportReview(ctx, opt any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReportReview", reflect.TypeOf((*MockReviewService)(nil).ReportReview), ctx, opt)
}

// TextReview mocks base method.
func (m *MockReviewService) TextReview(ctx context.Context, opt *service.ReviewOption) (review.CheckResultEnum, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TextReview", ctx, opt)
	ret0, _ := ret[0].(review.CheckResultEnum)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TextReview indicates an expected call of TextReview.
func (mr *MockReviewServiceMockRecorder) TextReview(ctx, opt any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TextReview", reflect.TypeOf((*MockReviewService)(nil).TextReview), ctx, opt)
}
