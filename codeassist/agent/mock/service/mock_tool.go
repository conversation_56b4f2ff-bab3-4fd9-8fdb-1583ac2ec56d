// Code generated by MockGen. DO NOT EDIT.
// Source: code.byted.org/devgpt/kiwis/codeassist/agent/service (interfaces: Tool)
//
// Generated by this command:
//
//	mockgen -destination ../mock/service/mock_tool.go -package service code.byted.org/devgpt/kiwis/codeassist/agent/service Tool
//

// Package service is a generated GoMock package.
package service

import (
	context "context"
	reflect "reflect"

	service "code.byted.org/devgpt/kiwis/codeassist/agent/service"
	gomock "go.uber.org/mock/gomock"
)

// MockTool is a mock of Tool interface.
type MockTool struct {
	ctrl     *gomock.Controller
	recorder *MockToolMockRecorder
	isgomock struct{}
}

// MockToolMockRecorder is the mock recorder for MockTool.
type MockToolMockRecorder struct {
	mock *MockTool
}

// NewMockTool creates a new mock instance.
func NewMockTool(ctrl *gomock.Controller) *MockTool {
	mock := &MockTool{ctrl: ctrl}
	mock.recorder = &MockToolMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTool) EXPECT() *MockToolMockRecorder {
	return m.recorder
}

// Description mocks base method.
func (m *MockTool) Description() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Description")
	ret0, _ := ret[0].(string)
	return ret0
}

// Description indicates an expected call of Description.
func (mr *MockToolMockRecorder) Description() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Description", reflect.TypeOf((*MockTool)(nil).Description))
}

// FormatResult mocks base method.
func (m *MockTool) FormatResult(result any) string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FormatResult", result)
	ret0, _ := ret[0].(string)
	return ret0
}

// FormatResult indicates an expected call of FormatResult.
func (mr *MockToolMockRecorder) FormatResult(result any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FormatResult", reflect.TypeOf((*MockTool)(nil).FormatResult), result)
}

// InputSpec mocks base method.
func (m *MockTool) InputSpec() service.Schema {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InputSpec")
	ret0, _ := ret[0].(service.Schema)
	return ret0
}

// InputSpec indicates an expected call of InputSpec.
func (mr *MockToolMockRecorder) InputSpec() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InputSpec", reflect.TypeOf((*MockTool)(nil).InputSpec))
}

// Name mocks base method.
func (m *MockTool) Name() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Name")
	ret0, _ := ret[0].(string)
	return ret0
}

// Name indicates an expected call of Name.
func (mr *MockToolMockRecorder) Name() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Name", reflect.TypeOf((*MockTool)(nil).Name))
}

// Run mocks base method.
func (m *MockTool) Run(ctx context.Context, toolContext service.ToolContext, input map[string]string) (any, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Run", ctx, toolContext, input)
	ret0, _ := ret[0].(any)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Run indicates an expected call of Run.
func (mr *MockToolMockRecorder) Run(ctx, toolContext, input any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Run", reflect.TypeOf((*MockTool)(nil).Run), ctx, toolContext, input)
}
