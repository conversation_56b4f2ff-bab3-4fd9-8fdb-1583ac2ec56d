// Code generated by MockGen. DO NOT EDIT.
// Source: code.byted.org/devgpt/kiwis/codeassist/agent/service (interfaces: ToolService)
//
// Generated by this command:
//
//	mockgen -destination ../mock/service/tool_service.go -package service code.byted.org/devgpt/kiwis/codeassist/agent/service ToolService
//

// Package service is a generated GoMock package.
package service

import (
	reflect "reflect"

	service "code.byted.org/devgpt/kiwis/codeassist/agent/service"
	gomock "go.uber.org/mock/gomock"
)

// MockToolService is a mock of ToolService interface.
type MockToolService struct {
	ctrl     *gomock.Controller
	recorder *MockToolServiceMockRecorder
	isgomock struct{}
}

// MockToolServiceMockRecorder is the mock recorder for MockToolService.
type MockToolServiceMockRecorder struct {
	mock *MockToolService
}

// NewMockToolService creates a new mock instance.
func NewMockToolService(ctrl *gomock.Controller) *MockToolService {
	mock := &MockToolService{ctrl: ctrl}
	mock.recorder = &MockToolServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockToolService) EXPECT() *MockToolServiceMockRecorder {
	return m.recorder
}

// GetTool mocks base method.
func (m *MockToolService) GetTool(name string) service.Tool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTool", name)
	ret0, _ := ret[0].(service.Tool)
	return ret0
}

// GetTool indicates an expected call of GetTool.
func (mr *MockToolServiceMockRecorder) GetTool(name any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTool", reflect.TypeOf((*MockToolService)(nil).GetTool), name)
}
