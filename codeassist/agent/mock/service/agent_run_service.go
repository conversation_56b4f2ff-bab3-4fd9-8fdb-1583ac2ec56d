// Code generated by MockGen. DO NOT EDIT.
// Source: code.byted.org/devgpt/kiwis/codeassist/agent/service (interfaces: AgentRunService)
//
// Generated by this command:
//
//	mockgen -destination ../mock/service/agent_run_service.go -package service code.byted.org/devgpt/kiwis/codeassist/agent/service AgentRunService
//

// Package service is a generated GoMock package.
package service

import (
	context "context"
	reflect "reflect"

	entity "code.byted.org/devgpt/kiwis/codeassist/agent/entity"
	service "code.byted.org/devgpt/kiwis/codeassist/agent/service"
	service0 "code.byted.org/devgpt/kiwis/codeassist/sandbox/service"
	gomock "go.uber.org/mock/gomock"
)

// MockAgentRunService is a mock of AgentRunService interface.
type MockAgentRunService struct {
	ctrl     *gomock.Controller
	recorder *MockAgentRunServiceMockRecorder
	isgomock struct{}
}

// MockAgentRunServiceMockRecorder is the mock recorder for MockAgentRunService.
type MockAgentRunServiceMockRecorder struct {
	mock *MockAgentRunService
}

// NewMockAgentRunService creates a new mock instance.
func NewMockAgentRunService(ctrl *gomock.Controller) *MockAgentRunService {
	mock := &MockAgentRunService{ctrl: ctrl}
	mock.recorder = &MockAgentRunServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAgentRunService) EXPECT() *MockAgentRunServiceMockRecorder {
	return m.recorder
}

// CreateFile mocks base method.
func (m *MockAgentRunService) CreateFile(ctx context.Context, opt *service.CreateFileOpt) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateFile", ctx, opt)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateFile indicates an expected call of CreateFile.
func (mr *MockAgentRunServiceMockRecorder) CreateFile(ctx, opt any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateFile", reflect.TypeOf((*MockAgentRunService)(nil).CreateFile), ctx, opt)
}

// CreateOrGet mocks base method.
func (m *MockAgentRunService) CreateOrGet(ctx context.Context, opt *service.CreateOrGetOption) (*entity.AgentRun, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateOrGet", ctx, opt)
	ret0, _ := ret[0].(*entity.AgentRun)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// CreateOrGet indicates an expected call of CreateOrGet.
func (mr *MockAgentRunServiceMockRecorder) CreateOrGet(ctx, opt any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateOrGet", reflect.TypeOf((*MockAgentRunService)(nil).CreateOrGet), ctx, opt)
}

// DownloadFile mocks base method.
func (m *MockAgentRunService) DownloadFile(ctx context.Context, opt *service.DownloadFileOption) (map[string][]byte, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DownloadFile", ctx, opt)
	ret0, _ := ret[0].(map[string][]byte)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DownloadFile indicates an expected call of DownloadFile.
func (mr *MockAgentRunServiceMockRecorder) DownloadFile(ctx, opt any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DownloadFile", reflect.TypeOf((*MockAgentRunService)(nil).DownloadFile), ctx, opt)
}

// EditFile mocks base method.
func (m *MockAgentRunService) EditFile(ctx context.Context, opt *service.EditFileOpt) (*service0.EditFileResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "EditFile", ctx, opt)
	ret0, _ := ret[0].(*service0.EditFileResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// EditFile indicates an expected call of EditFile.
func (mr *MockAgentRunServiceMockRecorder) EditFile(ctx, opt any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EditFile", reflect.TypeOf((*MockAgentRunService)(nil).EditFile), ctx, opt)
}

// ExecuteCode mocks base method.
func (m *MockAgentRunService) ExecuteCode(ctx context.Context, opt *service.ExecuteCodeOpt) (*service0.ExecuteResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExecuteCode", ctx, opt)
	ret0, _ := ret[0].(*service0.ExecuteResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ExecuteCode indicates an expected call of ExecuteCode.
func (mr *MockAgentRunServiceMockRecorder) ExecuteCode(ctx, opt any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExecuteCode", reflect.TypeOf((*MockAgentRunService)(nil).ExecuteCode), ctx, opt)
}

// GetAgentRun mocks base method.
func (m *MockAgentRunService) GetAgentRun(ctx context.Context, agentRunID string) (*entity.AgentRun, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAgentRun", ctx, agentRunID)
	ret0, _ := ret[0].(*entity.AgentRun)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAgentRun indicates an expected call of GetAgentRun.
func (mr *MockAgentRunServiceMockRecorder) GetAgentRun(ctx, agentRunID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAgentRun", reflect.TypeOf((*MockAgentRunService)(nil).GetAgentRun), ctx, agentRunID)
}

// ReadFile mocks base method.
func (m *MockAgentRunService) ReadFile(ctx context.Context, opt *service.ReadFileOpt) (*service0.ReadFileResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReadFile", ctx, opt)
	ret0, _ := ret[0].(*service0.ReadFileResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReadFile indicates an expected call of ReadFile.
func (mr *MockAgentRunServiceMockRecorder) ReadFile(ctx, opt any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReadFile", reflect.TypeOf((*MockAgentRunService)(nil).ReadFile), ctx, opt)
}

// ReleaseSandbox mocks base method.
func (m *MockAgentRunService) ReleaseSandbox(ctx context.Context, userID int64, conversationID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReleaseSandbox", ctx, userID, conversationID)
	ret0, _ := ret[0].(error)
	return ret0
}

// ReleaseSandbox indicates an expected call of ReleaseSandbox.
func (mr *MockAgentRunServiceMockRecorder) ReleaseSandbox(ctx, userID, conversationID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReleaseSandbox", reflect.TypeOf((*MockAgentRunService)(nil).ReleaseSandbox), ctx, userID, conversationID)
}

// UpdateAgentRunStatus mocks base method.
func (m *MockAgentRunService) UpdateAgentRunStatus(ctx context.Context, agentRunID string, status entity.AgentRunStatus) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAgentRunStatus", ctx, agentRunID, status)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateAgentRunStatus indicates an expected call of UpdateAgentRunStatus.
func (mr *MockAgentRunServiceMockRecorder) UpdateAgentRunStatus(ctx, agentRunID, status any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAgentRunStatus", reflect.TypeOf((*MockAgentRunService)(nil).UpdateAgentRunStatus), ctx, agentRunID, status)
}

// UploadFile mocks base method.
func (m *MockAgentRunService) UploadFile(ctx context.Context, opt *service.UploadFileOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UploadFile", ctx, opt)
	ret0, _ := ret[0].(error)
	return ret0
}

// UploadFile indicates an expected call of UploadFile.
func (mr *MockAgentRunServiceMockRecorder) UploadFile(ctx, opt any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UploadFile", reflect.TypeOf((*MockAgentRunService)(nil).UploadFile), ctx, opt)
}
