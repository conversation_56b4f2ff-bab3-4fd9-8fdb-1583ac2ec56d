// Code generated by MockGen. DO NOT EDIT.
// Source: code.byted.org/devgpt/kiwis/codeassist/agent/service (interfaces: MemoryService)
//
// Generated by this command:
//
//	mockgen -destination ../mock/service/memory_service.go -package service code.byted.org/devgpt/kiwis/codeassist/agent/service MemoryService
//

// Package service is a generated GoMock package.
package service

import (
	context "context"
	reflect "reflect"

	entity "code.byted.org/devgpt/kiwis/codeassist/agent/entity"
	service "code.byted.org/devgpt/kiwis/codeassist/agent/service"
	gomock "go.uber.org/mock/gomock"
)

// MockMemoryService is a mock of MemoryService interface.
type MockMemoryService struct {
	ctrl     *gomock.Controller
	recorder *MockMemoryServiceMockRecorder
	isgomock struct{}
}

// MockMemoryServiceMockRecorder is the mock recorder for MockMemoryService.
type MockMemoryServiceMockRecorder struct {
	mock *MockMemoryService
}

// NewMockMemoryService creates a new mock instance.
func NewMockMemoryService(ctrl *gomock.Controller) *MockMemoryService {
	mock := &MockMemoryService{ctrl: ctrl}
	mock.recorder = &MockMemoryServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockMemoryService) EXPECT() *MockMemoryServiceMockRecorder {
	return m.recorder
}

// AddToolCall mocks base method.
func (m *MockMemoryService) AddToolCall(ctx context.Context, toolCall *entity.AgentRunStepToolCall) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddToolCall", ctx, toolCall)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddToolCall indicates an expected call of AddToolCall.
func (mr *MockMemoryServiceMockRecorder) AddToolCall(ctx, toolCall any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddToolCall", reflect.TypeOf((*MockMemoryService)(nil).AddToolCall), ctx, toolCall)
}

// GetAgentRunSteps mocks base method.
func (m *MockMemoryService) GetAgentRunSteps(ctx context.Context, opt *service.GetAgentRunStepOption) ([]*entity.AgentRunStep, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAgentRunSteps", ctx, opt)
	ret0, _ := ret[0].([]*entity.AgentRunStep)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAgentRunSteps indicates an expected call of GetAgentRunSteps.
func (mr *MockMemoryServiceMockRecorder) GetAgentRunSteps(ctx, opt any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAgentRunSteps", reflect.TypeOf((*MockMemoryService)(nil).GetAgentRunSteps), ctx, opt)
}

// SaveAgentRunStep mocks base method.
func (m *MockMemoryService) SaveAgentRunStep(ctx context.Context, opt *service.SaveAgentRunStepOption) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveAgentRunStep", ctx, opt)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SaveAgentRunStep indicates an expected call of SaveAgentRunStep.
func (mr *MockMemoryServiceMockRecorder) SaveAgentRunStep(ctx, opt any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveAgentRunStep", reflect.TypeOf((*MockMemoryService)(nil).SaveAgentRunStep), ctx, opt)
}

// UpdateAgentRunStep mocks base method.
func (m *MockMemoryService) UpdateAgentRunStep(ctx context.Context, opt *service.UpdateAgentRunStepOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAgentRunStep", ctx, opt)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateAgentRunStep indicates an expected call of UpdateAgentRunStep.
func (mr *MockMemoryServiceMockRecorder) UpdateAgentRunStep(ctx, opt any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAgentRunStep", reflect.TypeOf((*MockMemoryService)(nil).UpdateAgentRunStep), ctx, opt)
}
