// Code generated by MockGen. DO NOT EDIT.
// Source: code.byted.org/devgpt/kiwis/codeassist/agent/service (interfaces: ArtifactsService)
//
// Generated by this command:
//
//	mockgen -destination ../mock/service/artifacts_service.go -package service code.byted.org/devgpt/kiwis/codeassist/agent/service ArtifactsService
//

// Package service is a generated GoMock package.
package service

import (
	context "context"
	reflect "reflect"

	entity "code.byted.org/devgpt/kiwis/codeassist/agent/entity"
	service "code.byted.org/devgpt/kiwis/codeassist/agent/service"
	gomock "go.uber.org/mock/gomock"
)

// MockArtifactsService is a mock of ArtifactsService interface.
type MockArtifactsService struct {
	ctrl     *gomock.Controller
	recorder *MockArtifactsServiceMockRecorder
	isgomock struct{}
}

// MockArtifactsServiceMockRecorder is the mock recorder for MockArtifactsService.
type MockArtifactsServiceMockRecorder struct {
	mock *MockArtifactsService
}

// NewMockArtifactsService creates a new mock instance.
func NewMockArtifactsService(ctrl *gomock.Controller) *MockArtifactsService {
	mock := &MockArtifactsService{ctrl: ctrl}
	mock.recorder = &MockArtifactsServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockArtifactsService) EXPECT() *MockArtifactsServiceMockRecorder {
	return m.recorder
}

// CreateDoubaoArtifact mocks base method.
func (m *MockArtifactsService) CreateDoubaoArtifact(ctx context.Context, opt *service.CreateDoubaoArtifactOption) (*entity.DoubaoArtifact, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateDoubaoArtifact", ctx, opt)
	ret0, _ := ret[0].(*entity.DoubaoArtifact)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateDoubaoArtifact indicates an expected call of CreateDoubaoArtifact.
func (mr *MockArtifactsServiceMockRecorder) CreateDoubaoArtifact(ctx, opt any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateDoubaoArtifact", reflect.TypeOf((*MockArtifactsService)(nil).CreateDoubaoArtifact), ctx, opt)
}

// DownloadFile mocks base method.
func (m *MockArtifactsService) DownloadFile(ctx context.Context, uri string, userID int64) ([]byte, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DownloadFile", ctx, uri, userID)
	ret0, _ := ret[0].([]byte)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DownloadFile indicates an expected call of DownloadFile.
func (mr *MockArtifactsServiceMockRecorder) DownloadFile(ctx, uri, userID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DownloadFile", reflect.TypeOf((*MockArtifactsService)(nil).DownloadFile), ctx, uri, userID)
}

// UploadFile mocks base method.
func (m *MockArtifactsService) UploadFile(ctx context.Context, userID int64, filename string, content []byte) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UploadFile", ctx, userID, filename, content)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UploadFile indicates an expected call of UploadFile.
func (mr *MockArtifactsServiceMockRecorder) UploadFile(ctx, userID, filename, content any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UploadFile", reflect.TypeOf((*MockArtifactsService)(nil).UploadFile), ctx, userID, filename, content)
}
