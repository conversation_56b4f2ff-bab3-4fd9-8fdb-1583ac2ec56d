// Code generated by MockGen. DO NOT EDIT.
// Source: code.byted.org/devgpt/kiwis/codeassist/agent/dal (interfaces: ToolCallObservationDAO)
//
// Generated by this command:
//
//	mockgen -destination ../mock/dal/tool_call_observation_dao.go -package dal code.byted.org/devgpt/kiwis/codeassist/agent/dal ToolCallObservationDAO
//

// Package dal is a generated GoMock package.
package dal

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockToolCallObservationDAO is a mock of ToolCallObservationDAO interface.
type MockToolCallObservationDAO struct {
	ctrl     *gomock.Controller
	recorder *MockToolCallObservationDAOMockRecorder
	isgomock struct{}
}

// MockToolCallObservationDAOMockRecorder is the mock recorder for MockToolCallObservationDAO.
type MockToolCallObservationDAOMockRecorder struct {
	mock *MockToolCallObservationDAO
}

// NewMockToolCallObservationDAO creates a new mock instance.
func NewMockToolCallObservationDAO(ctrl *gomock.Controller) *MockToolCallObservationDAO {
	mock := &MockToolCallObservationDAO{ctrl: ctrl}
	mock.recorder = &MockToolCallObservationDAOMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockToolCallObservationDAO) EXPECT() *MockToolCallObservationDAOMockRecorder {
	return m.recorder
}

// Get mocks base method.
func (m *MockToolCallObservationDAO) Get(ctx context.Context, toolCallID string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get", ctx, toolCallID)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockToolCallObservationDAOMockRecorder) Get(ctx, toolCallID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockToolCallObservationDAO)(nil).Get), ctx, toolCallID)
}

// Set mocks base method.
func (m *MockToolCallObservationDAO) Set(ctx context.Context, toolCallID, message string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Set", ctx, toolCallID, message)
	ret0, _ := ret[0].(error)
	return ret0
}

// Set indicates an expected call of Set.
func (mr *MockToolCallObservationDAOMockRecorder) Set(ctx, toolCallID, message any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Set", reflect.TypeOf((*MockToolCallObservationDAO)(nil).Set), ctx, toolCallID, message)
}
