// Code generated by MockGen. DO NOT EDIT.
// Source: code.byted.org/devgpt/kiwis/codeassist/agent/dal (interfaces: AgentRunStepToolCallDAO)
//
// Generated by this command:
//
//	mockgen -destination ../mock/dal/agent_run_step_tool_call_dao.go -package dal code.byted.org/devgpt/kiwis/codeassist/agent/dal AgentRunStepToolCallDAO
//

// Package dal is a generated GoMock package.
package dal

import (
	context "context"
	reflect "reflect"

	entity "code.byted.org/devgpt/kiwis/codeassist/agent/entity"
	gomock "go.uber.org/mock/gomock"
)

// MockAgentRunStepToolCallDAO is a mock of AgentRunStepToolCallDAO interface.
type MockAgentRunStepToolCallDAO struct {
	ctrl     *gomock.Controller
	recorder *MockAgentRunStepToolCallDAOMockRecorder
	isgomock struct{}
}

// MockAgentRunStepToolCallDAOMockRecorder is the mock recorder for MockAgentRunStepToolCallDAO.
type MockAgentRunStepToolCallDAOMockRecorder struct {
	mock *MockAgentRunStepToolCallDAO
}

// NewMockAgentRunStepToolCallDAO creates a new mock instance.
func NewMockAgentRunStepToolCallDAO(ctrl *gomock.Controller) *MockAgentRunStepToolCallDAO {
	mock := &MockAgentRunStepToolCallDAO{ctrl: ctrl}
	mock.recorder = &MockAgentRunStepToolCallDAOMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAgentRunStepToolCallDAO) EXPECT() *MockAgentRunStepToolCallDAOMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockAgentRunStepToolCallDAO) Create(ctx context.Context, agentRunStepToolCall *entity.AgentRunStepToolCall) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, agentRunStepToolCall)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockAgentRunStepToolCallDAOMockRecorder) Create(ctx, agentRunStepToolCall any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockAgentRunStepToolCallDAO)(nil).Create), ctx, agentRunStepToolCall)
}

// GetByAgentRunStepID mocks base method.
func (m *MockAgentRunStepToolCallDAO) GetByAgentRunStepID(ctx context.Context, agentRunStepID string) ([]*entity.AgentRunStepToolCall, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByAgentRunStepID", ctx, agentRunStepID)
	ret0, _ := ret[0].([]*entity.AgentRunStepToolCall)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByAgentRunStepID indicates an expected call of GetByAgentRunStepID.
func (mr *MockAgentRunStepToolCallDAOMockRecorder) GetByAgentRunStepID(ctx, agentRunStepID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByAgentRunStepID", reflect.TypeOf((*MockAgentRunStepToolCallDAO)(nil).GetByAgentRunStepID), ctx, agentRunStepID)
}

// GetByID mocks base method.
func (m *MockAgentRunStepToolCallDAO) GetByID(ctx context.Context, uniqueID string) (*entity.AgentRunStepToolCall, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByID", ctx, uniqueID)
	ret0, _ := ret[0].(*entity.AgentRunStepToolCall)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByID indicates an expected call of GetByID.
func (mr *MockAgentRunStepToolCallDAOMockRecorder) GetByID(ctx, uniqueID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByID", reflect.TypeOf((*MockAgentRunStepToolCallDAO)(nil).GetByID), ctx, uniqueID)
}

// MCreate mocks base method.
func (m *MockAgentRunStepToolCallDAO) MCreate(ctx context.Context, agentRunStepToolCall ...*entity.AgentRunStepToolCall) error {
	m.ctrl.T.Helper()
	varargs := []any{ctx}
	for _, a := range agentRunStepToolCall {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "MCreate", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// MCreate indicates an expected call of MCreate.
func (mr *MockAgentRunStepToolCallDAOMockRecorder) MCreate(ctx any, agentRunStepToolCall ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx}, agentRunStepToolCall...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MCreate", reflect.TypeOf((*MockAgentRunStepToolCallDAO)(nil).MCreate), varargs...)
}
