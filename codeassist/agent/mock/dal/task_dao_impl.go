// Code generated by MockGen. DO NOT EDIT.
// Source: code.byted.org/devgpt/kiwis/codeassist/agent/dal (interfaces: TaskDAO)
//
// Generated by this command:
//
//	mockgen -destination ../mock/dal/task_dao_impl.go -package dal code.byted.org/devgpt/kiwis/codeassist/agent/dal TaskDAO
//

// Package dal is a generated GoMock package.
package dal

import (
	context "context"
	reflect "reflect"

	entity "code.byted.org/devgpt/kiwis/codeassist/agent/entity"
	gomock "go.uber.org/mock/gomock"
)

// MockTaskDAO is a mock of TaskDAO interface.
type MockTaskDAO struct {
	ctrl     *gomock.Controller
	recorder *MockTaskDAOMockRecorder
	isgomock struct{}
}

// MockTaskDAOMockRecorder is the mock recorder for MockTaskDAO.
type MockTaskDAOMockRecorder struct {
	mock *MockTaskDAO
}

// NewMockTaskDAO creates a new mock instance.
func NewMockTaskDAO(ctrl *gomock.Controller) *MockTaskDAO {
	mock := &MockTaskDAO{ctrl: ctrl}
	mock.recorder = &MockTaskDAOMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTaskDAO) EXPECT() *MockTaskDAOMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockTaskDAO) Create(ctx context.Context, task *entity.Task) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, task)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockTaskDAOMockRecorder) Create(ctx, task any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockTaskDAO)(nil).Create), ctx, task)
}

// FilterGivenMessageList mocks base method.
func (m *MockTaskDAO) FilterGivenMessageList(ctx context.Context, messageIDs []string) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FilterGivenMessageList", ctx, messageIDs)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FilterGivenMessageList indicates an expected call of FilterGivenMessageList.
func (mr *MockTaskDAOMockRecorder) FilterGivenMessageList(ctx, messageIDs any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FilterGivenMessageList", reflect.TypeOf((*MockTaskDAO)(nil).FilterGivenMessageList), ctx, messageIDs)
}

// GetByID mocks base method.
func (m *MockTaskDAO) GetByID(ctx context.Context, uniqueID string) (*entity.Task, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByID", ctx, uniqueID)
	ret0, _ := ret[0].(*entity.Task)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByID indicates an expected call of GetByID.
func (mr *MockTaskDAOMockRecorder) GetByID(ctx, uniqueID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByID", reflect.TypeOf((*MockTaskDAO)(nil).GetByID), ctx, uniqueID)
}

// GetStageByID mocks base method.
func (m *MockTaskDAO) GetStageByID(ctx context.Context, uniqueID string) (entity.TaskStage, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStageByID", ctx, uniqueID)
	ret0, _ := ret[0].(entity.TaskStage)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStageByID indicates an expected call of GetStageByID.
func (mr *MockTaskDAOMockRecorder) GetStageByID(ctx, uniqueID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStageByID", reflect.TypeOf((*MockTaskDAO)(nil).GetStageByID), ctx, uniqueID)
}

// GetStatusByID mocks base method.
func (m *MockTaskDAO) GetStatusByID(ctx context.Context, uniqueID string) (entity.TaskStatus, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStatusByID", ctx, uniqueID)
	ret0, _ := ret[0].(entity.TaskStatus)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStatusByID indicates an expected call of GetStatusByID.
func (mr *MockTaskDAOMockRecorder) GetStatusByID(ctx, uniqueID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStatusByID", reflect.TypeOf((*MockTaskDAO)(nil).GetStatusByID), ctx, uniqueID)
}

// UpdateStageByID mocks base method.
func (m *MockTaskDAO) UpdateStageByID(ctx context.Context, uniqueID string, stage entity.TaskStage) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateStageByID", ctx, uniqueID, stage)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateStageByID indicates an expected call of UpdateStageByID.
func (mr *MockTaskDAOMockRecorder) UpdateStageByID(ctx, uniqueID, stage any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateStageByID", reflect.TypeOf((*MockTaskDAO)(nil).UpdateStageByID), ctx, uniqueID, stage)
}

// UpdateStageByMessageID mocks base method.
func (m *MockTaskDAO) UpdateStageByMessageID(ctx context.Context, messageID string, stage entity.TaskStage) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateStageByMessageID", ctx, messageID, stage)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateStageByMessageID indicates an expected call of UpdateStageByMessageID.
func (mr *MockTaskDAOMockRecorder) UpdateStageByMessageID(ctx, messageID, stage any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateStageByMessageID", reflect.TypeOf((*MockTaskDAO)(nil).UpdateStageByMessageID), ctx, messageID, stage)
}

// UpdateStageByMessageIDAndUser mocks base method.
func (m *MockTaskDAO) UpdateStageByMessageIDAndUser(ctx context.Context, messageID, userID string, stage entity.TaskStage) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateStageByMessageIDAndUser", ctx, messageID, userID, stage)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateStageByMessageIDAndUser indicates an expected call of UpdateStageByMessageIDAndUser.
func (mr *MockTaskDAOMockRecorder) UpdateStageByMessageIDAndUser(ctx, messageID, userID, stage any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateStageByMessageIDAndUser", reflect.TypeOf((*MockTaskDAO)(nil).UpdateStageByMessageIDAndUser), ctx, messageID, userID, stage)
}

// UpdateStatusByID mocks base method.
func (m *MockTaskDAO) UpdateStatusByID(ctx context.Context, uniqueID string, oldStatus, newStatus entity.TaskStatus) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateStatusByID", ctx, uniqueID, oldStatus, newStatus)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateStatusByID indicates an expected call of UpdateStatusByID.
func (mr *MockTaskDAOMockRecorder) UpdateStatusByID(ctx, uniqueID, oldStatus, newStatus any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateStatusByID", reflect.TypeOf((*MockTaskDAO)(nil).UpdateStatusByID), ctx, uniqueID, oldStatus, newStatus)
}
