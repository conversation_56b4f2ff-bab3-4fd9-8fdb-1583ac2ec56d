// Code generated by MockGen. DO NOT EDIT.
// Source: code.byted.org/devgpt/kiwis/codeassist/agent/dal (interfaces: AgentRunStepMessageDAO)
//
// Generated by this command:
//
//	mockgen -destination ../mock/dal/agent_run_step_message_dao.go -package dal code.byted.org/devgpt/kiwis/codeassist/agent/dal AgentRunStepMessageDAO
//

// Package dal is a generated GoMock package.
package dal

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockAgentRunStepMessageDAO is a mock of AgentRunStepMessageDAO interface.
type MockAgentRunStepMessageDAO struct {
	ctrl     *gomock.Controller
	recorder *MockAgentRunStepMessageDAOMockRecorder
	isgomock struct{}
}

// MockAgentRunStepMessageDAOMockRecorder is the mock recorder for MockAgentRunStepMessageDAO.
type MockAgentRunStepMessageDAOMockRecorder struct {
	mock *MockAgentRunStepMessageDAO
}

// NewMockAgentRunStepMessageDAO creates a new mock instance.
func NewMockAgentRunStepMessageDAO(ctrl *gomock.Controller) *MockAgentRunStepMessageDAO {
	mock := &MockAgentRunStepMessageDAO{ctrl: ctrl}
	mock.recorder = &MockAgentRunStepMessageDAOMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAgentRunStepMessageDAO) EXPECT() *MockAgentRunStepMessageDAOMockRecorder {
	return m.recorder
}

// Get mocks base method.
func (m *MockAgentRunStepMessageDAO) Get(ctx context.Context, messageID string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get", ctx, messageID)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockAgentRunStepMessageDAOMockRecorder) Get(ctx, messageID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockAgentRunStepMessageDAO)(nil).Get), ctx, messageID)
}

// Set mocks base method.
func (m *MockAgentRunStepMessageDAO) Set(ctx context.Context, messageID, message string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Set", ctx, messageID, message)
	ret0, _ := ret[0].(error)
	return ret0
}

// Set indicates an expected call of Set.
func (mr *MockAgentRunStepMessageDAOMockRecorder) Set(ctx, messageID, message any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Set", reflect.TypeOf((*MockAgentRunStepMessageDAO)(nil).Set), ctx, messageID, message)
}
