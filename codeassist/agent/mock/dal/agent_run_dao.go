// Code generated by MockGen. DO NOT EDIT.
// Source: code.byted.org/devgpt/kiwis/codeassist/agent/dal (interfaces: AgentRunDAO)
//
// Generated by this command:
//
//	mockgen -destination ../mock/dal/agent_run_dao.go -package dal code.byted.org/devgpt/kiwis/codeassist/agent/dal AgentRunDAO
//

// Package dal is a generated GoMock package.
package dal

import (
	context "context"
	reflect "reflect"

	entity "code.byted.org/devgpt/kiwis/codeassist/agent/entity"
	gomock "go.uber.org/mock/gomock"
)

// MockAgentRunDAO is a mock of AgentRunDAO interface.
type MockAgentRunDAO struct {
	ctrl     *gomock.Controller
	recorder *MockAgentRunDAOMockRecorder
	isgomock struct{}
}

// MockAgentRunDAOMockRecorder is the mock recorder for MockAgentRunDAO.
type MockAgentRunDAOMockRecorder struct {
	mock *MockAgentRunDAO
}

// NewMockAgentRunDAO creates a new mock instance.
func NewMockAgentRunDAO(ctrl *gomock.Controller) *MockAgentRunDAO {
	mock := &MockAgentRunDAO{ctrl: ctrl}
	mock.recorder = &MockAgentRunDAOMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAgentRunDAO) EXPECT() *MockAgentRunDAOMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockAgentRunDAO) Create(ctx context.Context, agentRun *entity.AgentRun) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, agentRun)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockAgentRunDAOMockRecorder) Create(ctx, agentRun any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockAgentRunDAO)(nil).Create), ctx, agentRun)
}

// GetByConversationID mocks base method.
func (m *MockAgentRunDAO) GetByConversationID(ctx context.Context, conversationID string, status []entity.AgentRunStatus) ([]*entity.AgentRun, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByConversationID", ctx, conversationID, status)
	ret0, _ := ret[0].([]*entity.AgentRun)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByConversationID indicates an expected call of GetByConversationID.
func (mr *MockAgentRunDAOMockRecorder) GetByConversationID(ctx, conversationID, status any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByConversationID", reflect.TypeOf((*MockAgentRunDAO)(nil).GetByConversationID), ctx, conversationID, status)
}

// GetByID mocks base method.
func (m *MockAgentRunDAO) GetByID(ctx context.Context, uniqueID string) (*entity.AgentRun, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByID", ctx, uniqueID)
	ret0, _ := ret[0].(*entity.AgentRun)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByID indicates an expected call of GetByID.
func (mr *MockAgentRunDAOMockRecorder) GetByID(ctx, uniqueID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByID", reflect.TypeOf((*MockAgentRunDAO)(nil).GetByID), ctx, uniqueID)
}

// GetByTaskID mocks base method.
func (m *MockAgentRunDAO) GetByTaskID(ctx context.Context, taskID string) (*entity.AgentRun, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByTaskID", ctx, taskID)
	ret0, _ := ret[0].(*entity.AgentRun)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByTaskID indicates an expected call of GetByTaskID.
func (mr *MockAgentRunDAOMockRecorder) GetByTaskID(ctx, taskID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByTaskID", reflect.TypeOf((*MockAgentRunDAO)(nil).GetByTaskID), ctx, taskID)
}

// UpdateStatus mocks base method.
func (m *MockAgentRunDAO) UpdateStatus(ctx context.Context, agentRunID string, status entity.AgentRunStatus) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateStatus", ctx, agentRunID, status)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateStatus indicates an expected call of UpdateStatus.
func (mr *MockAgentRunDAOMockRecorder) UpdateStatus(ctx, agentRunID, status any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateStatus", reflect.TypeOf((*MockAgentRunDAO)(nil).UpdateStatus), ctx, agentRunID, status)
}
