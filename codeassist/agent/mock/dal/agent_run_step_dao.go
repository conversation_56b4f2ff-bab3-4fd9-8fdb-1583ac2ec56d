// Code generated by MockGen. DO NOT EDIT.
// Source: code.byted.org/devgpt/kiwis/codeassist/agent/dal (interfaces: AgentRunStepDAO)
//
// Generated by this command:
//
//	mockgen -destination ../mock/dal/agent_run_step_dao.go -package dal code.byted.org/devgpt/kiwis/codeassist/agent/dal AgentRunStepDAO
//

// Package dal is a generated GoMock package.
package dal

import (
	context "context"
	reflect "reflect"

	entity "code.byted.org/devgpt/kiwis/codeassist/agent/entity"
	gomock "go.uber.org/mock/gomock"
)

// MockAgentRunStepDAO is a mock of AgentRunStepDAO interface.
type MockAgentRunStepDAO struct {
	ctrl     *gomock.Controller
	recorder *MockAgentRunStepDAOMockRecorder
	isgomock struct{}
}

// MockAgentRunStepDAOMockRecorder is the mock recorder for MockAgentRunStepDAO.
type MockAgentRunStepDAOMockRecorder struct {
	mock *MockAgentRunStepDAO
}

// NewMockAgentRunStepDAO creates a new mock instance.
func NewMockAgentRunStepDAO(ctrl *gomock.Controller) *MockAgentRunStepDAO {
	mock := &MockAgentRunStepDAO{ctrl: ctrl}
	mock.recorder = &MockAgentRunStepDAOMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAgentRunStepDAO) EXPECT() *MockAgentRunStepDAOMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockAgentRunStepDAO) Create(ctx context.Context, agentRunStep *entity.AgentRunStep) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, agentRunStep)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockAgentRunStepDAOMockRecorder) Create(ctx, agentRunStep any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockAgentRunStepDAO)(nil).Create), ctx, agentRunStep)
}

// GetByAgentRunID mocks base method.
func (m *MockAgentRunStepDAO) GetByAgentRunID(ctx context.Context, agentRunID string, status entity.AgentRunStepStatus) ([]*entity.AgentRunStep, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByAgentRunID", ctx, agentRunID, status)
	ret0, _ := ret[0].([]*entity.AgentRunStep)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByAgentRunID indicates an expected call of GetByAgentRunID.
func (mr *MockAgentRunStepDAOMockRecorder) GetByAgentRunID(ctx, agentRunID, status any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByAgentRunID", reflect.TypeOf((*MockAgentRunStepDAO)(nil).GetByAgentRunID), ctx, agentRunID, status)
}

// GetByID mocks base method.
func (m *MockAgentRunStepDAO) GetByID(ctx context.Context, uniqueID string) (*entity.AgentRunStep, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByID", ctx, uniqueID)
	ret0, _ := ret[0].(*entity.AgentRunStep)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByID indicates an expected call of GetByID.
func (mr *MockAgentRunStepDAOMockRecorder) GetByID(ctx, uniqueID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByID", reflect.TypeOf((*MockAgentRunStepDAO)(nil).GetByID), ctx, uniqueID)
}

// UpdateByID mocks base method.
func (m *MockAgentRunStepDAO) UpdateByID(ctx context.Context, uniqueId string, status entity.AgentRunStepStatus, metadata *entity.AgentRunStepMetaData) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateByID", ctx, uniqueId, status, metadata)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateByID indicates an expected call of UpdateByID.
func (mr *MockAgentRunStepDAOMockRecorder) UpdateByID(ctx, uniqueId, status, metadata any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateByID", reflect.TypeOf((*MockAgentRunStepDAO)(nil).UpdateByID), ctx, uniqueId, status, metadata)
}
