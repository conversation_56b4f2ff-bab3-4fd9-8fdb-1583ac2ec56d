package base

import (
	"context"
	"errors"
	"unicode/utf8"

	"code.byted.org/gopkg/logs/v2"
	"github.com/samber/lo"

	"code.byted.org/devgpt/kiwis/codeassist/agent/agents"
	"code.byted.org/devgpt/kiwis/codeassist/agent/entity"
	"code.byted.org/devgpt/kiwis/lib/metrics"
)

// TrimMessages 裁剪历史消息以适应token限制
func (a *AgentBaseService) TrimMessages(ctx context.Context, runContext *agents.RunContext) error {
	// 获取当前Actor的裁剪配置
	trimConfig := runContext.CurrentActor.Actor.GetTrimConfig()
	if trimConfig == nil {
		return errors.New("trim config is nil")
	}
	messages := runContext.CurrentActor.ActorMessage
	if messages == nil {
		logs.V1.CtxWarn(ctx, "actorMessages is nil, use runContext.Messages")
		messages = runContext.Messages
	}
	for {
		trimTimes := 0
		modelName := runContext.CurrentActor.Actor.GetModelConfig().ModelArch
		historyTokens := agents.EstimateTokenCount(messages.ChatHistory, modelName)
		agentTrajectoryTokens := agents.EstimateTokenCount(messages.AgentTrajectory, modelName)
		userQueryTokens := agents.EstimateTokenCount([]*entity.MessageUnit{messages.UserQuery}, modelName)
		systemPrompt := lo.Map(runContext.CurrentActor.Actor.GetSystemPrompt(ctx, runContext), func(prompt string, _ int) *entity.MessageUnit {
			return &entity.MessageUnit{
				Role:    entity.MessageUnitRoleSystem,
				Content: prompt,
			}
		})
		contextPrompt := lo.Map(runContext.CurrentActor.Actor.GetContextPrompt(ctx, runContext), func(prompt string, _ int) *entity.MessageUnit {
			return &entity.MessageUnit{
				Role:    entity.MessageUnitRoleUser,
				Content: prompt,
			}
		})
		systemPromptTokens := agents.EstimateTokenCount(systemPrompt, modelName)
		contextPromptTokens := agents.EstimateTokenCount(contextPrompt, modelName)
		totalTokens := systemPromptTokens + contextPromptTokens + historyTokens + agentTrajectoryTokens + userQueryTokens
		if trimConfig.MaxTokens == 0 || totalTokens <= trimConfig.MaxTokens {
			return nil
		}

		logs.V1.CtxInfo(ctx, "TrimMessages: totalTokens: %d, maxTokens: %d", totalTokens, trimConfig.MaxTokens)
		// 裁剪 ChatHistory
		if trimConfig.MaxHistoryTokens != 0 && historyTokens > trimConfig.MaxHistoryTokens && len(messages.ChatHistory) > 1 {
			// 找到第一轮对话的结束位置
			roundEndIndex := findFirstRoundEnd(messages.ChatHistory)
			if roundEndIndex > 0 && roundEndIndex < len(messages.ChatHistory)-1 {
				// 裁剪第一轮，但确保至少保留一条消息
				messages.ChatHistory = messages.ChatHistory[roundEndIndex:]
				trimTimes++
			}
		}

		// 裁剪 AgentTrajectory
		if trimConfig.MaxAgentTrajTokens != 0 && agentTrajectoryTokens > trimConfig.MaxAgentTrajTokens && len(messages.AgentTrajectory) > 1 {
			// 找到第一轮的结束位置（一个assistant + 后续的tool messages）
			roundEndIndex := findFirstAgentRoundEnd(messages.AgentTrajectory)
			if roundEndIndex > 0 && roundEndIndex < len(messages.AgentTrajectory)-1 {
				// 裁剪第一轮，但确保至少保留一条消息
				messages.AgentTrajectory = messages.AgentTrajectory[roundEndIndex:]
				// 在开头添加裁剪提示消息
				messages.AgentTrajectory = append([]*entity.MessageUnit{
					{
						Role:    entity.MessageUnitRoleUser,
						Content: trimConfig.AgentTrajTrimMsg,
					},
				}, messages.AgentTrajectory...)
				trimTimes++
			}
		}

		if trimTimes == 0 {
			historyTokens := agents.EstimateTokenCount(messages.ChatHistory, modelName)
			agentTrajectoryTokens := agents.EstimateTokenCount(messages.AgentTrajectory, modelName)
			totalTokens := systemPromptTokens + contextPromptTokens + historyTokens + agentTrajectoryTokens + userQueryTokens
			if trimConfig.MaxHistoryTokens != 0 && totalTokens > trimConfig.MaxHistoryTokens {
				roundEndIndex := findFirstRoundEnd(messages.ChatHistory)
				if roundEndIndex > 0 && roundEndIndex < len(messages.ChatHistory)-1 {
					messages.ChatHistory = messages.ChatHistory[:roundEndIndex]
					trimTimes++
				}
			}

			// 上报消息裁剪失败指标
			metrics.CodeAssistMetric.ReportAgentTrimFailedMetrics(runContext.AgentName, runContext.CurrentActor.Actor.GetName())

			return errors.New("trim messages error: unable to reduce token count")
		}
	}
}

// findFirstRoundEnd 找到ChatHistory中第一轮对话的结束位置
// 一轮包括：连续的user消息 + 一个assistant消息，或者只有一个assistant消息
func findFirstRoundEnd(messages []*entity.MessageUnit) int {
	if len(messages) == 0 {
		return -1
	}

	// 找到第一个assistant消息
	for i := 0; i < len(messages); i++ {
		if messages[i].Role == entity.MessageUnitRoleAssistant {
			// 返回assistant消息的下一个位置作为裁剪点
			return i + 1
		}
	}

	// 如果没有assistant消息，但有多个user消息，裁剪第一个user
	if len(messages) > 1 && messages[0].Role == entity.MessageUnitRoleUser {
		return 1
	}

	return -1
}

// findFirstAgentRoundEnd 找到AgentTrajectory中第一轮的结束位置
// 一轮包括：一个assistant消息 + 后续连续的tool消息
func findFirstAgentRoundEnd(messages []*entity.MessageUnit) int {
	if len(messages) == 0 {
		return -1
	}

	// 找到第一个assistant消息
	assistantIndex := -1
	for i := 0; i < len(messages); i++ {
		if messages[i].Role == entity.MessageUnitRoleAssistant {
			assistantIndex = i
			break
		}
	}

	if assistantIndex == -1 {
		// 没有assistant消息，无法裁剪
		return -1
	}

	// 从assistant消息后开始，找到所有连续的tool消息
	endIndex := assistantIndex + 1
	for endIndex < len(messages) && messages[endIndex].Role == entity.MessageUnitRoleTool {
		endIndex++
	}

	// 如果下一个是assistant消息，说明找到了完整的一轮
	if endIndex < len(messages) && messages[endIndex].Role == entity.MessageUnitRoleAssistant {
		return endIndex
	}

	// 如果已经到达末尾，或者遇到了其他类型的消息，返回当前位置
	return endIndex
}

func trimToolCallResult(runContext *agents.RunContext, toolCallResult string) string {
	trimConfig := runContext.CurrentActor.Actor.GetTrimConfig()
	if trimConfig.MaxToolCallLength != 0 && len(toolCallResult) > trimConfig.MaxToolCallLength {
		// 安全地截断 tool call result，避免UTF-8字符被截断
		truncatedResult := safeStringTruncate(toolCallResult, trimConfig.MaxToolCallLength)
		toolCallResult = truncatedResult + trimConfig.ToolCallTrimMsg
	}
	return toolCallResult
}

// safeStringTruncate 安全地截断字符串，确保不会破坏UTF-8字符
func safeStringTruncate(s string, maxBytes int) string {
	if len(s) <= maxBytes {
		return s
	}

	// 从maxBytes位置向前找到安全的截断点
	for i := maxBytes; i >= 0; i-- {
		if utf8.ValidString(s[:i]) {
			return s[:i]
		}
	}

	// 如果找不到有效位置，返回空字符串
	return ""
}
