package base

import (
	"context"
	"testing"

	"code.byted.org/devgpt/kiwis/codeassist/agent/agents"
)

func TestParseArtifacts(t *testing.T) {
	agent := &AgentBaseService{}
	ctx := context.Background()

	tests := []struct {
		name             string
		content          string
		wantHasArtifacts bool
		wantArtifacts    []agents.Artifact
	}{
		{
			name: "单个artifact",
			content: `
这是一些文本内容
<artifacts>人员分析系统.html</artifacts>
这是更多文本`,
			wantHasArtifacts: true,
			wantArtifacts: []agents.Artifact{
				{
					Content: "人员分析系统.html",
				},
			},
		},
		{
			name: "多个artifacts",
			content: `
第一个artifact:
<artifacts>dashboard.html</artifacts>
第二个artifact:
<artifacts>report.pdf</artifacts>`,
			wantHasArtifacts: true,
			wantArtifacts: []agents.Artifact{
				{
					Content: "dashboard.html",
				},
				{
					Content: "report.pdf",
				},
			},
		},
		{
			name: "包含多行内容的artifact",
			content: `<artifacts>
<!DOCTYPE html>
<html>
<head>
    <title>人员分析系统</title>
</head>
<body>
    <h1>欢迎使用人员分析系统</h1>
</body>
</html>
</artifacts>`,
			wantHasArtifacts: true,
			wantArtifacts: []agents.Artifact{
				{
					Content: `<!DOCTYPE html>
<html>
<head>
    <title>人员分析系统</title>
</head>
<body>
    <h1>欢迎使用人员分析系统</h1>
</body>
</html>`,
				},
			},
		},
		{
			name:             "没有artifacts",
			content:          "这是一段普通文本，没有任何artifacts",
			wantHasArtifacts: false,
			wantArtifacts:    []agents.Artifact{},
		},
		{
			name:             "空的artifacts标签",
			content:          `<artifacts></artifacts>`,
			wantHasArtifacts: true,
			wantArtifacts:    []agents.Artifact{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := agent.parseArtifacts(ctx, tt.content)
			if err != nil {
				t.Errorf("parseArtifacts() error = %v", err)
				return
			}

			if result.HasArtifacts != tt.wantHasArtifacts {
				t.Errorf("parseArtifacts() HasArtifacts = %v, want %v", result.HasArtifacts, tt.wantHasArtifacts)
			}

			if len(result.Artifacts) != len(tt.wantArtifacts) {
				t.Errorf("parseArtifacts() got %d artifacts, want %d", len(result.Artifacts), len(tt.wantArtifacts))
				return
			}

			for i, artifact := range result.Artifacts {
				if artifact.Content != tt.wantArtifacts[i].Content {
					t.Errorf("Artifact[%d] Content = %v, want %v", i, artifact.Content, tt.wantArtifacts[i].Content)
				}
			}
		})
	}
}

func TestParseContent(t *testing.T) {
	agent := &AgentBaseService{}
	ctx := context.Background()

	tests := []struct {
		name               string
		content            string
		wantHasToolCalls   bool
		wantToolCallsCount int
		wantHasArtifacts   bool
		wantArtifactsCount int
	}{
		{
			name: "同时包含工具调用和artifacts",
			content: `
我需要生成一个分析报告：

<doubao:function_calls>
<doubao:invoke name="analyze_data">
<doubao:parameter name="data_source">employees.csv</doubao:parameter>
<doubao:parameter name="analysis_type">demographic</doubao:parameter>
</doubao:invoke>
</doubao:function_calls>

生成的报告如下：

<artifacts>人员分析系统.html</artifacts>

分析完成！`,
			wantHasToolCalls:   true,
			wantToolCallsCount: 1,
			wantHasArtifacts:   true,
			wantArtifactsCount: 1,
		},
		{
			name: "只有工具调用",
			content: `<doubao:function_calls>
<doubao:invoke name="read_file">
<doubao:parameter name="path">data.txt</doubao:parameter>
</doubao:invoke>
</doubao:function_calls>`,
			wantHasToolCalls:   true,
			wantToolCallsCount: 1,
			wantHasArtifacts:   false,
			wantArtifactsCount: 0,
		},
		{
			name:               "只有artifacts",
			content:            `<artifacts>output.html</artifacts>`,
			wantHasToolCalls:   false,
			wantToolCallsCount: 0,
			wantHasArtifacts:   true,
			wantArtifactsCount: 1,
		},
		{
			name: "多个工具调用和多个artifacts",
			content: `
<doubao:function_calls>
<doubao:invoke name="func1">
<doubao:parameter name="param1">value1</doubao:parameter>
</doubao:invoke>
<doubao:invoke name="func2">
<doubao:parameter name="param2">value2</doubao:parameter>
</doubao:invoke>
</doubao:function_calls>

<artifacts>file1.html</artifacts>
<artifacts>file2.pdf</artifacts>`,
			wantHasToolCalls:   true,
			wantToolCallsCount: 2,
			wantHasArtifacts:   true,
			wantArtifactsCount: 2,
		},
		{
			name:               "都没有",
			content:            "普通文本内容",
			wantHasToolCalls:   false,
			wantToolCallsCount: 0,
			wantHasArtifacts:   false,
			wantArtifactsCount: 0,
		},
	}

	buf := agents.DeltaEventBuffer{
		InvokeIDs: []string{"test", "test2"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := agent.ParseContent(ctx, tt.content, buf)
			if err != nil {
				t.Errorf("parseContent() error = %v", err)
				return
			}

			// 检查工具调用
			if result.ToolCalls.HasToolCalls != tt.wantHasToolCalls {
				t.Errorf("parseContent() HasToolCalls = %v, want %v", result.ToolCalls.HasToolCalls, tt.wantHasToolCalls)
			}
			if len(result.ToolCalls.Invokes) != tt.wantToolCallsCount {
				t.Errorf("parseContent() got %d tool calls, want %d", len(result.ToolCalls.Invokes), tt.wantToolCallsCount)
			}

			// 检查artifacts
			if result.Artifacts.HasArtifacts != tt.wantHasArtifacts {
				t.Errorf("parseContent() HasArtifacts = %v, want %v", result.Artifacts.HasArtifacts, tt.wantHasArtifacts)
			}
			if len(result.Artifacts.Artifacts) != tt.wantArtifactsCount {
				t.Errorf("parseContent() got %d artifacts, want %d", len(result.Artifacts.Artifacts), tt.wantArtifactsCount)
			}
		})
	}
}
