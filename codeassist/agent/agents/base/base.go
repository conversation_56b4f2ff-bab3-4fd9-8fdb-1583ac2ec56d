package base

import (
	"code.byted.org/devgpt/kiwis/codeassist/agent/service"
	"code.byted.org/devgpt/kiwis/copilotstack/llm"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/port/fornax"
)

type AgentBaseService struct {
	FornaxClient    fornax.Client
	LLMService      llm.Service
	ToolService     service.ToolService
	AgentRunService service.AgentRunService
}

type ChatContext struct {
	Messages       []llm.ChatCompletionMessage
	ModelConf      *config.CodeAssistLLMConfig
	ConversationID string
	UserID         int64
	AgentID        string
}

func (a *AgentBaseService) Name() string {
	return "AgentBaseService"
}

func (a *AgentBaseService) Description() string {
	return "AgentBaseService"
}
