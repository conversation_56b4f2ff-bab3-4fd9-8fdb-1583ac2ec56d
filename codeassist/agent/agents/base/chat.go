package base

import (
	"context"

	"code.byted.org/gopkg/logs/v2"
	"github.com/samber/lo"
	"github.com/tidwall/gjson"
	"github.com/tidwall/sjson"

	authentity "code.byted.org/devgpt/kiwis/copilotstack/common/auth/entity"
	copilotstackentity "code.byted.org/devgpt/kiwis/copilotstack/entity"
	"code.byted.org/devgpt/kiwis/copilotstack/llm"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/llminfra/utils"
)

// const (
// 	ChatCompletionTimeout = 600 * time.Second
// )

func (a *AgentBaseService) ChatCompletion(ctx context.Context, chatContext ChatContext) (llm.ChatCompletionStreamResult, error) {
	modelConf := chatContext.ModelConf
	messages := chatContext.Messages

	variables := a.GetLLMFlowVariables(ctx, modelConf)

	req := llm.ChatCompletionRequest{
		Model:       modelConf.Name,
		ModelArch:   modelConf.ModelArch,
		Messages:    messages,
		MaxTokens:   lo.ToPtr(lo.Ternary(modelConf.MaxTokens > 0, modelConf.MaxTokens, 4096)),
		Temperature: lo.Ternary(modelConf.Temperature > 0, lo.ToPtr(modelConf.Temperature), nil),
		TopP:        lo.Ternary(modelConf.TopP > 0, lo.ToPtr(modelConf.TopP), nil),
		Stream:      true,
		SessionID:   chatContext.ConversationID,
		SensitiveOpt: llm.SensitiveOpt{ //不用LLMService里的敏感词检测
			DisableAntiDirt:    lo.ToPtr(true),
			DisableLLMSecurity: lo.ToPtr(true),
		},
		Account: &authentity.Account{
			ID: chatContext.UserID,
		},
		AppID:  chatContext.AgentID,
		Record: false, // 不使用 journal 记录，单独走 fornax trace 上报
		Variables: copilotstackentity.Variables{
			llm.LLMFlowGptAbParamsKey: variables,
		},
	}
	// ctx, cancel := context.WithTimeout(ctx, ChatCompletionTimeout)
	// defer cancel()
	completionResult, err := a.LLMService.ChatCompletion(ctx, req)
	if err != nil {
		logs.V1.CtxError(ctx, "ChatCompletion error: %s", err)
		return nil, err
	}
	return completionResult, nil
}

func (a *AgentBaseService) GetLLMFlowVariables(ctx context.Context, modelConf *config.CodeAssistLLMConfig) string {
	var variables string
	if modelConf == nil {
		return variables
	}
	if modelConf.MaxTokens > 0 {
		path := "graph_config.nodes.llm_node.param.parameters.max_new_tokens"
		variables = updateJSONStr(ctx, variables, path, modelConf.MaxTokens)
	}
	if modelConf.ModelDescName != "" {
		path := "graph_config.nodes.llm_node.param.model_desc.name"
		variables = updateJSONStr(ctx, variables, path, modelConf.ModelDescName)
	}
	logs.V1.CtxInfo(ctx, "getLLMFlowGptAbParams variables: %s", variables)
	return variables
}

func updateJSONStr(ctx context.Context, jsonStr string, path string, value interface{}) string {
	if utils.Empty(value) {
		return jsonStr
	}

	tempStr := jsonStr
	pathExists := gjson.Get(tempStr, path).Exists()
	// 如果path不存在，进行字段赋值。 如果存在不做替换
	if !pathExists {
		var err error
		tempStr, err = sjson.Set(tempStr, path, value)
		if err != nil {
			// 如果赋值失败，返回最开始的jsonStr
			logs.V1.CtxError(ctx, "[updateMaxNewTokens] set max_new_tokens fail: %v", err)
			return jsonStr
		}
	}
	return tempStr
}
