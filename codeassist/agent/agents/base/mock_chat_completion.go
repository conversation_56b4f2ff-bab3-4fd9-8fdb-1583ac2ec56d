package base

import (
	"bytes"
	"context"
	"io"
	"math/rand"
	"text/template"
	"time"

	"code.byted.org/devgpt/kiwis/copilotstack/llm"
)

const maxChunkSize = 5

type MetaData struct {
	ConversionID     string
	SensitiveContent string
	SensitiveReport  string
}

// MockChatCompletionStreamResult 是一个模拟的 ChatCompletionStreamResult 实现
type MockChatCompletionStreamResult struct {
	// 要返回的内容，可以是一个字符串或者多个字符串的切片
	Content []rune

	currentIndex int
	chunkCount   int
	consumed     bool // 是否已经消费完毕
}

// NewMockChatCompletionStreamResult 创建一个新的 mock 实例
func NewMockChatCompletionStreamResult(content string, metaData *MetaData) *MockChatCompletionStreamResult {
	tmpl, _ := template.New("temp").Parse(content)
	var buf bytes.Buffer
	err := tmpl.Execute(&buf, metaData)
	if err != nil {
		panic(err)
	}
	return &MockChatCompletionStreamResult{
		Content:      []rune(buf.String()),
		currentIndex: 0,
		consumed:     false,
	}
}

// NextChunk 实现 ChatCompletionStreamResult 接口
func (m *MockChatCompletionStreamResult) NextChunk(ctx context.Context) (*llm.ChatCompletionStreamResponse, error) {
	if m.consumed {
		return nil, io.EOF
	}

	if m.currentIndex >= len(m.Content) {
		m.consumed = true
		return nil, io.EOF
	}

	chunkSize := rand.Intn(maxChunkSize) + 1
	if m.currentIndex+chunkSize > len(m.Content) {
		chunkSize = len(m.Content) - m.currentIndex
	}

	content := m.Content[m.currentIndex : m.currentIndex+chunkSize]
	m.currentIndex += chunkSize
	m.chunkCount++

	result := &llm.ChatCompletionStreamResponse{
		ID:      "mock-id",
		Object:  "chat.completion.chunk",
		Created: time.Now().Unix(),
		Model:   "mock-model",
		Choices: []llm.ChatCompletionStreamChoice{
			{
				Index: 0,
				Delta: llm.ChatCompletionStreamChoiceDelta{
					Content: string(content),
					Role:    "assistant",
				},
				FinishReason: "",
			},
		},
	}

	return result, nil
}

// Aggregation 实现 ChatCompletionStreamResult 接口
func (m *MockChatCompletionStreamResult) Aggregation(ctx context.Context) (*llm.ChatCompletionResponse, error) {
	// 将所有内容聚合
	fullContent := m.Content

	return &llm.ChatCompletionResponse{
		ID:      "mock-id",
		Object:  "chat.completion",
		Created: time.Now().Unix(),
		Model:   "mock-model",
		Choices: []llm.ChatCompletionChoice{
			{
				Index: 0,
				Message: llm.ChatCompletionMessage{
					Role:    "assistant",
					Content: string(fullContent),
				},
				FinishReason: "stop",
			},
		},
		Usage: llm.TokenUsage{
			PromptTokens:     0,
			CompletionTokens: 0,
			TotalTokens:      0,
		},
	}, nil
}

// Close 实现 ChatCompletionStreamResult 接口
func (m *MockChatCompletionStreamResult) Close(ctx context.Context) error {
	m.consumed = true
	return nil
}

// Statistics 实现 ChatCompletionStreamResult 接口
func (m *MockChatCompletionStreamResult) Statistics(ctx context.Context) llm.Stats {
	return llm.Stats{
		ID:                 "mock-stats-id",
		ChunkCount:         m.chunkCount,
		FirstCountLatency:  time.Millisecond * 100,
		TotalTimeCost:      time.Millisecond * 500,
		PromptTokens:       0,
		CompletionTokens:   0,
		TotalTokens:        0,
		ContentRaw:         string(m.Content),
		ToolCalls:          []llm.ToolCall{},
		Status:             "success",
		FinishReason:       "stop",
		Error:              "",
		PromptCompletionID: 0,
	}
}

// Metadata 实现 ChatCompletionStreamResult 接口
func (m *MockChatCompletionStreamResult) Metadata(ctx context.Context) llm.Metadata {
	return llm.Metadata{
		PromptCompletionID: 12345,
		ModelName:          "mock-model",
	}
}

// MockMultiChatCompletionWithList 是一个支持多段内容的 mock ChatCompletion 方法
func MockMultiChatCompletionWithList(mockContentList []string, metaData *MetaData) []llm.ChatCompletionStreamResult {
	var results []llm.ChatCompletionStreamResult
	for _, content := range mockContentList {
		results = append(results, NewMockChatCompletionStreamResult(content, metaData))
	}
	return results
}

var TestData = []string{
	`好的，我明白了`,
	`<think>首先，我需要看一下当前目录有什么内容，然后进行趋势分析</think>准备查看当前目录。
<doubao:function_calls>
<doubao:invoke name="ls">
<doubao:parameter name="path">/mnt/{{ .ConversionID }}/</doubao:parameter>
</doubao:invoke>
</doubao:function_calls>`,
	`<think>第二步，我要创建一个文件，记录一段内容 {{ .SensitiveContent }}</think>准备创建文件。{{ .SensitiveContent }}
<doubao:function_calls>
<doubao:invoke name="create_file">
<doubao:parameter name="path">/mnt/{{ .ConversionID }}/created_file.txt</doubao:parameter>
<doubao:parameter name="file_text">
这是第一行
这是第二行
可以包含 "双引号" 或 '单引号'
也可以包含特殊符号 \n \t 等，不会被转义
</doubao:parameter>
</doubao:invoke>
</doubao:function_calls>`,
	`<think>第二点一步，我要创建一个文件，记录一段内容</think>准备创建文件。
<doubao:function_calls>
<doubao:invoke name="create_file">
<doubao:parameter name="path">/mnt/{{ .ConversionID }}/created_python.py</doubao:parameter>
<doubao:parameter name="file_text">
# 计算并输出前 N 项斐波那契数列
def fibonacci(n):
    fib_seq = []
    a, b = 0, 1
    for _ in range(n):
        fib_seq.append(a)
        a, b = b, a + b
    return fib_seq
# 输入要计算的项数
n = int(input("请输入要输出的斐波那契数列项数："))
result = fibonacci(n)
print("斐波那契数列前", n, "项为：")
print(result)
</doubao:parameter>
</doubao:invoke>
</doubao:function_calls>`,
	`<think>第三步，我要编辑文件，修改内容</think>准备编辑文件。
<doubao:function_calls>
<doubao:invoke name="edit_file">
<doubao:parameter name="path">/mnt/{{ .ConversionID }}/created_file.txt</doubao:parameter>
<doubao:parameter name="old_str">不会被转义</doubao:parameter>
<doubao:parameter name="new_str">会被转义，而且疯狂转义</doubao:parameter>
</doubao:invoke>
</doubao:function_calls>`,
	`<think>第四步，我要查看文件</think>准备查看文件。
<doubao:function_calls>
<doubao:invoke name="view_file">
<doubao:parameter name="file_path">/mnt/{{ .ConversionID }}/created_file.txt</doubao:parameter>
<doubao:parameter name="offset">0</doubao:parameter>
<doubao:parameter name="limit">3000</doubao:parameter>
</doubao:invoke>
</doubao:function_calls>`,
	`<think>第五步，我要写代码查看文件</think>准备写代码。
<doubao:function_calls>
<doubao:invoke name="data_analysis">
<doubao:parameter name="code_script"># 打开并读取指定文件内容
file_path = '/mnt/{{ .ConversionID }}/created_file.txt'
try:
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
        print(content)
except FileNotFoundError:
    print(f"文件未找到: {file_path}")
except Exception as e:
    print(f"读取文件时出错: {e}")</doubao:parameter>
</doubao:invoke>
</doubao:function_calls>`,
	`<think>第六步，我要创建一个产物文件</think>准备创建文件。
<doubao:function_calls>
<doubao:invoke name="create_file">
<doubao:parameter name="path">/mnt/{{ .ConversionID }}/index.html</doubao:parameter>
<doubao:parameter name="file_text"><!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>我的第一个网页</title>
</head>
<body>
    <h1>欢迎来到我的网页！</h1>
    <p>这是一个简单的 HTML 示例页面。</p>
	<p>还有一个{{ .SensitiveReport }}</p>
    <button onclick="alert('你点击了按钮！')">点我试试</button>
</body>
</html></doubao:parameter>
</doubao:invoke>
</doubao:function_calls>
`,
	`好了，任务已经完成了`,
	`现为你呈现一下产出内容
<artifacts>/mnt/{{ .ConversionID }}/created_file.txt</artifacts>
<artifacts>/mnt/{{ .ConversionID }}/index.html</artifacts>`,
}
