package base

import (
	"context"
	"testing"
	"unicode/utf8"

	"github.com/stretchr/testify/assert"

	"code.byted.org/devgpt/kiwis/codeassist/agent/agents"
	"code.byted.org/devgpt/kiwis/codeassist/agent/entity"
	"code.byted.org/devgpt/kiwis/lib/config"
)

func TestFindFirstRoundEnd(t *testing.T) {
	tests := []struct {
		name     string
		messages []*entity.MessageUnit
		expected int
	}{
		{
			name:     "空消息列表",
			messages: []*entity.MessageUnit{},
			expected: -1,
		},
		{
			name: "只有一个assistant消息",
			messages: []*entity.MessageUnit{
				{Role: "assistant", Content: "回答"},
			},
			expected: 1,
		},
		{
			name: "user + assistant",
			messages: []*entity.MessageUnit{
				{Role: "user", Content: "问题"},
				{Role: "assistant", Content: "回答"},
			},
			expected: 2,
		},
		{
			name: "多个user + assistant",
			messages: []*entity.MessageUnit{
				{Role: "user", Content: "问题1"},
				{Role: "user", Content: "问题2"},
				{Role: "assistant", Content: "回答"},
			},
			expected: 3,
		},
		{
			name: "多轮对话",
			messages: []*entity.MessageUnit{
				{Role: "user", Content: "问题1"},
				{Role: "assistant", Content: "回答1"},
				{Role: "user", Content: "问题2"},
				{Role: "assistant", Content: "回答2"},
			},
			expected: 2, // 第一轮结束在第二个位置
		},
		{
			name: "只有user消息",
			messages: []*entity.MessageUnit{
				{Role: "user", Content: "问题1"},
				{Role: "user", Content: "问题2"},
			},
			expected: 1, // 裁剪第一个user
		},
		{
			name: "单个user消息",
			messages: []*entity.MessageUnit{
				{Role: "user", Content: "问题"},
			},
			expected: -1, // 无法裁剪
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := findFirstRoundEnd(tt.messages)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestFindFirstAgentRoundEnd(t *testing.T) {

	tests := []struct {
		name     string
		messages []*entity.MessageUnit
		expected int
	}{
		{
			name:     "空消息列表",
			messages: []*entity.MessageUnit{},
			expected: -1,
		},
		{
			name: "只有assistant消息",
			messages: []*entity.MessageUnit{
				{Role: "assistant", Content: "分析"},
			},
			expected: 1,
		},
		{
			name: "assistant + tool",
			messages: []*entity.MessageUnit{
				{Role: "assistant", Content: "调用工具"},
				{Role: "tool", Content: "工具结果"},
			},
			expected: 2,
		},
		{
			name: "assistant + 多个tool",
			messages: []*entity.MessageUnit{
				{Role: "assistant", Content: "调用多个工具"},
				{Role: "tool", Content: "工具结果1"},
				{Role: "tool", Content: "工具结果2"},
				{Role: "tool", Content: "工具结果3"},
			},
			expected: 4,
		},
		{
			name: "完整的两轮",
			messages: []*entity.MessageUnit{
				{Role: "assistant", Content: "第一轮"},
				{Role: "tool", Content: "工具结果1"},
				{Role: "tool", Content: "工具结果2"},
				{Role: "assistant", Content: "第二轮"},
				{Role: "tool", Content: "工具结果3"},
			},
			expected: 3, // 第一轮结束在第二个assistant之前
		},
		{
			name: "没有assistant消息",
			messages: []*entity.MessageUnit{
				{Role: "tool", Content: "工具结果1"},
				{Role: "tool", Content: "工具结果2"},
			},
			expected: -1,
		},
		{
			name: "assistant后没有tool",
			messages: []*entity.MessageUnit{
				{Role: "assistant", Content: "分析"},
				{Role: "user", Content: "用户消息"},
			},
			expected: 1,
		},
		{
			name: "tool消息前有其他消息",
			messages: []*entity.MessageUnit{
				{Role: "user", Content: "用户消息"},
				{Role: "assistant", Content: "分析"},
				{Role: "tool", Content: "工具结果"},
			},
			expected: 3,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := findFirstAgentRoundEnd(tt.messages)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestTrimMessagesLogic(t *testing.T) {
	t.Run("确保至少保留一条消息", func(t *testing.T) {

		// ChatHistory 测试
		chatHistory := []*entity.MessageUnit{
			{Role: "user", Content: "问题"},
			{Role: "assistant", Content: "回答"},
		}
		roundEnd := findFirstRoundEnd(chatHistory)
		// 应该返回2，但由于长度为2，不应该裁剪（确保至少保留一条）
		assert.Equal(t, 2, roundEnd)
		// 在实际的TrimMessages中，会检查 roundEndIndex < len(messages)-1

		// AgentTrajectory 测试
		agentTraj := []*entity.MessageUnit{
			{Role: "assistant", Content: "分析"},
			{Role: "tool", Content: "结果"},
		}
		roundEnd = findFirstAgentRoundEnd(agentTraj)
		// 应该返回2，但由于长度为2，不应该裁剪（确保至少保留一条）
		assert.Equal(t, 2, roundEnd)
	})

	t.Run("多轮裁剪场景", func(t *testing.T) {

		// 多轮ChatHistory
		chatHistory := []*entity.MessageUnit{
			{Role: "user", Content: "问题1"},
			{Role: "assistant", Content: "回答1"},
			{Role: "user", Content: "问题2"},
			{Role: "assistant", Content: "回答2"},
			{Role: "user", Content: "问题3"},
			{Role: "assistant", Content: "回答3"},
		}

		// 第一次裁剪
		roundEnd := findFirstRoundEnd(chatHistory)
		assert.Equal(t, 2, roundEnd)
		// 裁剪后: [问题2, 回答2, 问题3, 回答3]

		// 第二次裁剪
		chatHistory = chatHistory[roundEnd:]
		roundEnd = findFirstRoundEnd(chatHistory)
		assert.Equal(t, 2, roundEnd)
		// 裁剪后: [问题3, 回答3]
	})

	t.Run("复杂的AgentTrajectory裁剪", func(t *testing.T) {
		agentTraj := []*entity.MessageUnit{
			{Role: "assistant", Content: "分析1"},
			{Role: "tool", Content: "结果1"},
			{Role: "tool", Content: "结果2"},
			{Role: "assistant", Content: "分析2"},
			{Role: "tool", Content: "结果3"},
			{Role: "assistant", Content: "分析3"},
		}

		// 第一次裁剪应该移除第一轮（分析1 + 结果1 + 结果2）
		roundEnd := findFirstAgentRoundEnd(agentTraj)
		assert.Equal(t, 3, roundEnd)
		// 裁剪后: [分析2, 结果3, 分析3]
	})
}

func TestSafeStringTruncate(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		maxBytes int
		expected string
	}{
		{
			name:     "空字符串",
			input:    "",
			maxBytes: 10,
			expected: "",
		},
		{
			name:     "字符串长度小于限制",
			input:    "hello",
			maxBytes: 10,
			expected: "hello",
		},
		{
			name:     "字符串长度等于限制",
			input:    "hello",
			maxBytes: 5,
			expected: "hello",
		},
		{
			name:     "纯ASCII字符截断",
			input:    "hello world",
			maxBytes: 5,
			expected: "hello",
		},
		{
			name:     "中文字符截断 - 完整字符边界",
			input:    "你好世界", // 每个中文字符3字节
			maxBytes: 6,      // 正好2个字符
			expected: "你好",
		},
		{
			name:     "中文字符截断 - 字符中间",
			input:    "你好世界",
			maxBytes: 7,    // 2个字符6字节 + 1字节（第3个字符的一部分）
			expected: "你好", // 应该截断到安全边界
		},
		{
			name:     "中英混合截断",
			input:    "hello你好world",
			maxBytes: 11, // hello(5) + 你好(6) = 11
			expected: "hello你好",
		},
		{
			name:     "中英混合截断 - 字符中间",
			input:    "hello你好world",
			maxBytes: 10,       // hello(5) + 你(3) + 好的一部分
			expected: "hello你", // 应该截断到安全边界
		},
		{
			name:     "Emoji字符截断",
			input:    "😀😁😂", // 每个emoji 4字节
			maxBytes: 8,     // 正好2个emoji
			expected: "😀😁",
		},
		{
			name:     "Emoji字符截断 - 字符中间",
			input:    "😀😁😂",
			maxBytes: 9,    // 2个emoji(8字节) + 1字节（第3个emoji的一部分）
			expected: "😀😁", // 应该截断到安全边界
		},
		{
			name:     "复杂混合内容",
			input:    "Hello世界😀Test",
			maxBytes: 13,        // Hello(5) + 世界(6) + 😀的一部分
			expected: "Hello世界", // 应该截断到安全边界
		},
		{
			name:     "单个多字节字符被截断",
			input:    "你好",
			maxBytes: 2,  // 小于一个中文字符的字节数
			expected: "", // 应该返回空字符串
		},
		{
			name:     "maxBytes为0",
			input:    "hello",
			maxBytes: 0,
			expected: "",
		},
		{
			name:     "负数maxBytes",
			input:    "hello",
			maxBytes: -1,
			expected: "",
		},
		{
			name:     "特殊UTF-8字符",
			input:    "こんにちは", // 日文，每个字符3字节，总共15字节
			maxBytes: 15,      // 正好5个字符
			expected: "こんにちは", // 完整字符串
		},
		{
			name:     "特殊UTF-8字符截断",
			input:    "こんにちは",
			maxBytes: 10,    // 3个字符(9字节) + 1字节
			expected: "こんに", // 应该截断到安全边界
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := safeStringTruncate(tt.input, tt.maxBytes)
			assert.Equal(t, tt.expected, result)

			// 验证结果是有效的UTF-8字符串
			assert.True(t, isValidUTF8(result), "结果应该是有效的UTF-8字符串")

			// 验证结果长度不超过限制（仅对非负数限制进行检查）
			if tt.maxBytes >= 0 {
				assert.LessOrEqual(t, len(result), tt.maxBytes, "结果字节长度不应超过限制")
			}
		})
	}
}

func TestTrimToolCallResult(t *testing.T) {
	// 创建一个mock的RunContext和TrimConfig
	tests := []struct {
		name              string
		toolCallResult    string
		maxToolCallLength int
		toolCallTrimMsg   string
		expectedResult    string
		expectedValidUTF8 bool
	}{
		{
			name:              "不需要截断",
			toolCallResult:    "短消息",
			maxToolCallLength: 20,
			toolCallTrimMsg:   "...[截断]",
			expectedResult:    "短消息",
			expectedValidUTF8: true,
		},
		{
			name:              "ASCII内容截断",
			toolCallResult:    "This is a very long message that needs to be truncated",
			maxToolCallLength: 20,
			toolCallTrimMsg:   "...[truncated]",
			expectedResult:    "This is a very long ...[truncated]",
			expectedValidUTF8: true,
		},
		{
			name:              "中文内容安全截断",
			toolCallResult:    "这是一个很长的工具调用结果消息需要被截断处理以避免超出限制",
			maxToolCallLength: 20,
			toolCallTrimMsg:   "...[已截断]",
			expectedResult:    "这是一个很长...[已截断]", // 应该在安全边界截断
			expectedValidUTF8: true,
		},
		{
			name:              "中英混合内容截断",
			toolCallResult:    "Hello世界This is a test message with mixed content",
			maxToolCallLength: 15,
			toolCallTrimMsg:   "...[cut]",
			expectedResult:    "Hello世界This...[cut]", // 应该在安全边界截断
			expectedValidUTF8: true,
		},
		{
			name:              "Emoji内容截断",
			toolCallResult:    "😀😁😂😃😄😅这是测试内容",
			maxToolCallLength: 20,
			toolCallTrimMsg:   "...[end]",
			expectedResult:    "😀😁😂😃😄...[end]", // 应该在安全边界截断
			expectedValidUTF8: true,
		},
		{
			name:              "配置为0不截断",
			toolCallResult:    "任何长度的消息",
			maxToolCallLength: 0,
			toolCallTrimMsg:   "...[cut]",
			expectedResult:    "任何长度的消息",
			expectedValidUTF8: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建mock的RunContext
			runContext := createMockRunContext(tt.maxToolCallLength, tt.toolCallTrimMsg)

			result := trimToolCallResult(runContext, tt.toolCallResult)
			assert.Equal(t, tt.expectedResult, result)

			// 验证结果是有效的UTF-8字符串
			if tt.expectedValidUTF8 {
				assert.True(t, isValidUTF8(result), "结果应该是有效的UTF-8字符串")
			}
		})
	}
}

// 辅助函数：验证字符串是否为有效的UTF-8
func isValidUTF8(s string) bool {
	for len(s) > 0 {
		r, size := utf8.DecodeRuneInString(s)
		if r == utf8.RuneError && size == 1 {
			return false
		}
		s = s[size:]
	}
	return true
}

// 创建mock的RunContext用于测试
func createMockRunContext(maxToolCallLength int, toolCallTrimMsg string) *agents.RunContext {
	return &agents.RunContext{
		CurrentActor: &agents.CurrentActor{
			Actor: &mockActor{
				trimConfig: &agents.TrimConfig{
					MaxToolCallLength: maxToolCallLength,
					ToolCallTrimMsg:   toolCallTrimMsg,
				},
			},
		},
	}
}

// Mock Actor 用于测试
type mockActor struct {
	trimConfig *agents.TrimConfig
}

// 实现 ActorInterface 的所有方法
func (m *mockActor) GetName() string {
	return "mockActor"
}

func (m *mockActor) IsFinished(ctx context.Context, runContext *agents.RunContext) bool {
	return false
}

func (m *mockActor) GetSystemPrompt(ctx context.Context, runContext *agents.RunContext) []string {
	return []string{}
}

func (m *mockActor) GetContextPrompt(ctx context.Context, runContext *agents.RunContext) []string {
	return []string{}
}

func (m *mockActor) GetToolList() []string {
	return []string{}
}

func (m *mockActor) GetModelConfig() *config.CodeAssistLLMConfig {
	return nil
}

func (m *mockActor) GetTrimConfig() *agents.TrimConfig {
	return m.trimConfig
}

func (m *mockActor) BeforeExecute(ctx context.Context, runContext *agents.RunContext) error {
	return nil
}

func (m *mockActor) AfterExecute(ctx context.Context, runContext *agents.RunContext) error {
	return nil
}
