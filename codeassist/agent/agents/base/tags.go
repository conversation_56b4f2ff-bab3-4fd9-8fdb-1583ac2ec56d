package base

import (
	"context"
	"errors"
	"regexp"
	"strings"
	"time"

	"github.com/google/uuid"

	"code.byted.org/devgpt/kiwis/codeassist/agent/agents"
	"code.byted.org/devgpt/kiwis/codeassist/agent/entity"
)

var (
	reThinkOpen      = regexp.MustCompile(`<think>`)
	reThinkClose     = regexp.MustCompile(`</think>`)
	reFunctionOpen   = regexp.MustCompile(`<doubao:function_calls>`)
	reFunctionClose  = regexp.MustCompile(`</doubao:function_calls>`)
	reInvokeOpen     = regexp.MustCompile(`<doubao:invoke name="([^"]+)">`)
	reInvokeClose    = regexp.MustCompile(`</doubao:invoke>`)
	reParamOpen      = regexp.MustCompile(`<doubao:parameter name="([^"]+)">`)
	reParamClose     = regexp.MustCompile(`</doubao:parameter>`)
	reArtifactsOpen  = regexp.MustCompile(`<artifacts>`)
	reArtifactsClose = regexp.MustCompile(`</artifacts>`)
)

type TagConfig struct {
	TagName      string
	Regex        *regexp.Regexp
	StateUpdater func(buffer *agents.DeltaEventBuffer, matches []string)
}

func (a *AgentBaseService) CouldBePartOfTag(content string) bool {
	if len(content) == 0 {
		return false
	}

	// 如果不是以 '<' 开头，则不可能是标签
	if content[0] != '<' {
		return false
	}

	// 定义所有可能的标签
	possibleTags := []string{
		"<think>",
		"</think>",
		"<doubao:function_calls>",
		"</doubao:function_calls>",
		"<doubao:invoke",
		"</doubao:invoke>",
		"<doubao:parameter",
		"</doubao:parameter>",
		"<artifacts>",
		"</artifacts>",
	}

	// 检查是否是完整标签或标签的前缀
	for _, tag := range possibleTags {
		// 如果内容是标签的前缀
		if strings.HasPrefix(tag, content) {
			return true
		}
		// 如果标签是内容的前缀（处理带属性的标签）
		if strings.HasPrefix(content, tag) {
			return true
		}
	}

	// 特殊处理带属性的标签
	// 检查是否匹配 invoke 或 parameter 标签的模式
	if len(content) > 1 {
		// 检查是否可能是 <doubao:invoke name="..."> 的一部分
		if strings.HasPrefix(content, "<doubao:invoke") {
			// 如果已经有完整的开始部分，检查是否有合法的属性格式
			if len(content) >= len("<doubao:invoke") {
				remaining := content[len("<doubao:invoke"):]
				// 允许空格、name属性等
				if remaining == "" || remaining[0] == ' ' || remaining[0] == '>' {
					return true
				}
			}
		}

		// 检查是否可能是 <doubao:parameter name="..."> 的一部分
		if strings.HasPrefix(content, "<doubao:parameter") {
			// 如果已经有完整的开始部分，检查是否有合法的属性格式
			if len(content) >= len("<doubao:parameter") {
				remaining := content[len("<doubao:parameter"):]
				// 允许空格、name属性等
				if remaining == "" || remaining[0] == ' ' || remaining[0] == '>' {
					return true
				}
			}
		}
	}

	return false
}

// couldBeOurTag 判断以 '<' 开头的字符串是否可能是我们需要的标签
func (a *AgentBaseService) couldBeOurTag(content string) bool {
	if !strings.HasPrefix(content, "<") {
		return false
	}

	// 检查是否可能是我们需要的标签开头
	prefixes := []string{
		"<think>", "</think>",
		"<doubao:function_calls>", "</doubao:function_calls>",
		"<doubao:invoke", "</doubao:invoke>",
		"<doubao:parameter", "</doubao:parameter>",
		"<artifacts>", "</artifacts>",
	}
	for _, prefix := range prefixes {
		if strings.HasPrefix(content, prefix) || strings.HasPrefix(prefix, content) {
			return true
		}
	}

	return false
}

func (a *AgentBaseService) GetTagHandler(content string) (bool, func(ctx context.Context, buffer agents.DeltaEventBuffer) (*entity.AgentEvent, agents.DeltaEventBuffer, error)) {
	// 使用 FindStringIndex 来确保匹配的是字符串开头
	if loc := reThinkOpen.FindStringIndex(content); loc != nil && loc[0] == 0 {
		return true, a.thinkOpenHandler
	}
	if loc := reFunctionOpen.FindStringIndex(content); loc != nil && loc[0] == 0 {
		return true, a.functionOpenHandler
	}
	if loc := reInvokeOpen.FindStringIndex(content); loc != nil && loc[0] == 0 {
		return true, a.invokeOpenHandler
	}
	if loc := reParamOpen.FindStringIndex(content); loc != nil && loc[0] == 0 {
		return true, a.parameterOpenHandler
	}
	if loc := reArtifactsOpen.FindStringIndex(content); loc != nil && loc[0] == 0 {
		return true, a.artifactsOpenHandler
	}
	if loc := reThinkClose.FindStringIndex(content); loc != nil && loc[0] == 0 {
		return true, a.thinkCloseHandler
	}
	if loc := reFunctionClose.FindStringIndex(content); loc != nil && loc[0] == 0 {
		return true, a.functionCloseHandler
	}
	if loc := reInvokeClose.FindStringIndex(content); loc != nil && loc[0] == 0 {
		return true, a.invokeCloseHandler
	}
	if loc := reParamClose.FindStringIndex(content); loc != nil && loc[0] == 0 {
		return true, a.parameterCloseHandler
	}
	if loc := reArtifactsClose.FindStringIndex(content); loc != nil && loc[0] == 0 {
		return true, a.artifactsCloseHandler
	}
	return false, nil
}

func (a *AgentBaseService) thinkOpenHandler(ctx context.Context, buffer agents.DeltaEventBuffer) (
	*entity.AgentEvent, agents.DeltaEventBuffer, error) {

	config := TagConfig{
		TagName: "think",
		Regex:   reThinkOpen,
		StateUpdater: func(buf *agents.DeltaEventBuffer, matches []string) {
			buf.InThink = true
			buf.ContentType = entity.AgentDeltaTypeThink
		},
	}

	return a.processTagOpen(ctx, buffer, config)
}

func (a *AgentBaseService) thinkCloseHandler(ctx context.Context, buffer agents.DeltaEventBuffer) (
	*entity.AgentEvent, agents.DeltaEventBuffer, error) {

	config := TagConfig{
		TagName: "think",
		Regex:   reThinkClose,
		StateUpdater: func(buf *agents.DeltaEventBuffer, matches []string) {
			buf.InThink = false
			buf.ContentType = entity.AgentDeltaType("")
		},
	}

	return a.processTagClose(ctx, buffer, config)
}

func (a *AgentBaseService) functionOpenHandler(ctx context.Context, buffer agents.DeltaEventBuffer) (
	*entity.AgentEvent, agents.DeltaEventBuffer, error) {

	config := TagConfig{
		TagName: "function",
		Regex:   reFunctionOpen,
		StateUpdater: func(buf *agents.DeltaEventBuffer, matches []string) {
			buf.InFunction = true
			buf.ContentType = entity.AgentDeltaTypeFunction
		},
	}

	return a.processTagOpen(ctx, buffer, config)
}

func (a *AgentBaseService) functionCloseHandler(ctx context.Context, buffer agents.DeltaEventBuffer) (
	*entity.AgentEvent, agents.DeltaEventBuffer, error) {

	config := TagConfig{
		TagName: "function",
		Regex:   reFunctionClose,
		StateUpdater: func(buf *agents.DeltaEventBuffer, matches []string) {
			buf.InFunction = false
			buf.ContentType = entity.AgentDeltaType("")
		},
	}

	return a.processTagClose(ctx, buffer, config)
}

func (a *AgentBaseService) parameterOpenHandler(ctx context.Context, buffer agents.DeltaEventBuffer) (
	*entity.AgentEvent, agents.DeltaEventBuffer, error) {

	config := TagConfig{
		TagName: "parameter",
		Regex:   reParamOpen,
		StateUpdater: func(buf *agents.DeltaEventBuffer, matches []string) {
			buf.InParameter = true
			if len(matches) > 1 {
				buf.ParamName = matches[1]
				buf.ContentType = entity.AgentDeltaTypeFunction
			}
		},
	}

	// 先更新 buffer 状态以获取参数名称
	matches := config.Regex.FindStringSubmatch(buffer.Content)
	if config.StateUpdater != nil {
		config.StateUpdater(&buffer, matches)
	}

	// 获取参数的 schema 信息
	tool := a.ToolService.GetTool(buffer.InvokeName)
	if tool != nil {
		schema := tool.InputSpec()
		paramSchema, ok := schema.Properties[buffer.ParamName]
		if ok {
			buffer.ParamSchema.Name = paramSchema.Name
			buffer.ParamSchema.Type = paramSchema.Type
			buffer.ParamSchema.Description = paramSchema.Description
			buffer.ParamSchema.Required = paramSchema.Required
			buffer.ParamSchema.StreamType = paramSchema.StreamType
		}
	}

	// 如果参数不是流式的，初始化 ParamContent
	if !buffer.ParamSchema.StreamType {
		buffer.ParamContent = ""
	}

	return a.processTagOpen(ctx, buffer, config)
}

func (a *AgentBaseService) parameterCloseHandler(ctx context.Context, buffer agents.DeltaEventBuffer) (
	*entity.AgentEvent, agents.DeltaEventBuffer, error) {

	// 如果参数不是流式的，需要在关闭标签时发送完整内容
	if buffer.InParameter && !buffer.ParamSchema.StreamType && buffer.ParamContent != "" {
		// Build metadata
		metadata, err := a.BufferToMetaData(ctx, buffer)
		if err != nil {
			return nil, buffer, err
		}

		// Create event with complete parameter content
		event := &entity.AgentEvent{
			EventType: entity.AgentEventTypeAgentMessageDelta,
			Timestamp: time.Now(),
			RunID:     buffer.RunID,
			AgentMessageDeltaEvent: &entity.AgentMessageDeltaEvent{
				Type:     entity.AgentDeltaTypeFunction,
				Content:  buffer.ParamContent,
				MetaData: metadata,
				Actor:    buffer.Actor,
			},
		}

		// Clear the parameter content
		buffer.ParamContent = ""

		config := TagConfig{
			TagName: "parameter",
			Regex:   reParamClose,
			StateUpdater: func(buf *agents.DeltaEventBuffer, matches []string) {
				buf.InParameter = false
				buf.ParamName = ""
			},
		}

		// Process the close tag to update buffer state
		_, buffer, err = a.processTagClose(ctx, buffer, config)
		if err != nil {
			return nil, buffer, err
		}

		return event, buffer, nil
	}

	config := TagConfig{
		TagName: "parameter",
		Regex:   reParamClose,
		StateUpdater: func(buf *agents.DeltaEventBuffer, matches []string) {
			buf.InParameter = false
			buf.ParamName = ""
		},
	}

	return a.processTagClose(ctx, buffer, config)
}

func (a *AgentBaseService) invokeOpenHandler(ctx context.Context, buffer agents.DeltaEventBuffer) (
	*entity.AgentEvent, agents.DeltaEventBuffer, error) {

	config := TagConfig{
		TagName: "invoke",
		Regex:   reInvokeOpen,
		StateUpdater: func(buf *agents.DeltaEventBuffer, matches []string) {
			buf.InInvoke = true
			if len(buf.InvokeIDs) == 0 {
				buf.InvokeIDs = []string{uuid.New().String()}
			} else {
				buf.InvokeIDs = append(buf.InvokeIDs, uuid.New().String())
			}
			if len(matches) > 1 {
				buf.InvokeName = matches[1]
				buf.ContentType = entity.AgentDeltaTypeFunction
			}
		},
	}

	return a.processTagOpen(ctx, buffer, config)
}

func (a *AgentBaseService) invokeCloseHandler(ctx context.Context, buffer agents.DeltaEventBuffer) (
	*entity.AgentEvent, agents.DeltaEventBuffer, error) {

	config := TagConfig{
		TagName: "invoke",
		Regex:   reInvokeClose,
		StateUpdater: func(buf *agents.DeltaEventBuffer, matches []string) {
			buf.InInvoke = false
			buf.InvokeName = ""
		},
	}

	return a.processTagClose(ctx, buffer, config)
}

func (a *AgentBaseService) artifactsOpenHandler(ctx context.Context, buffer agents.DeltaEventBuffer) (
	*entity.AgentEvent, agents.DeltaEventBuffer, error) {

	config := TagConfig{
		TagName: "artifacts",
		Regex:   reArtifactsOpen,
		StateUpdater: func(buf *agents.DeltaEventBuffer, matches []string) {
			buf.InArtifacts = true
		},
	}

	// 特殊处理：artifacts 标签内的内容需要被丢弃
	if !config.Regex.MatchString(buffer.Content) {
		return nil, buffer, errors.New("not artifacts open")
	}

	// 更新状态
	if config.StateUpdater != nil {
		config.StateUpdater(&buffer, nil)
	}

	// 移动指针跳过标签
	buffer.Content = buffer.Content[len(config.Regex.FindString(buffer.Content)):]

	// 查找内容直到下一个标签
	idx := 0
	for idx < len(buffer.Content) {
		if buffer.Content[idx] == '<' && a.couldBeOurTag(buffer.Content[idx:]) {
			break
		}
		idx++
	}

	// 丢弃 artifacts 标签内的内容，不生成事件
	buffer.Content = buffer.Content[idx:]

	return nil, buffer, nil
}

func (a *AgentBaseService) artifactsCloseHandler(ctx context.Context, buffer agents.DeltaEventBuffer) (
	*entity.AgentEvent, agents.DeltaEventBuffer, error) {

	config := TagConfig{
		TagName: "artifacts",
		Regex:   reArtifactsClose,
		StateUpdater: func(buf *agents.DeltaEventBuffer, matches []string) {
			buf.InArtifacts = false
			buf.ContentType = entity.AgentDeltaType("")
		},
	}

	// 特殊处理：artifacts 标签的关闭
	if !config.Regex.MatchString(buffer.Content) {
		return nil, buffer, errors.New("not artifacts close")
	}

	// 更新状态
	if config.StateUpdater != nil {
		config.StateUpdater(&buffer, nil)
	}

	// 移动指针跳过标签
	buffer.Content = buffer.Content[len(config.Regex.FindString(buffer.Content)):]

	// 查找内容直到下一个标签
	idx := 0
	for idx < len(buffer.Content) {
		if buffer.Content[idx] == '<' && a.couldBeOurTag(buffer.Content[idx:]) {
			break
		}
		idx++
	}

	// 提取内容
	content := buffer.Content[:idx]

	// 处理换行符
	if idx > 0 && buffer.Content[idx-1] == '\n' && idx != len(buffer.Content) {
		content = buffer.Content[:idx-1]
	}

	// 移动缓冲区指针
	buffer.Content = buffer.Content[idx:]

	// 如果有内容，生成普通文本事件
	if content != "" {
		// 构建元数据
		metadata, err := a.BufferToMetaData(ctx, buffer)
		if err != nil {
			return nil, buffer, err
		}

		// 创建事件
		event := &entity.AgentEvent{
			EventType: entity.AgentEventTypeAgentMessageDelta,
			Timestamp: time.Now(),
			RunID:     buffer.RunID,
			AgentMessageDeltaEvent: &entity.AgentMessageDeltaEvent{
				Type:     entity.AgentDeltaTypeInfo,
				Content:  content,
				MetaData: metadata,
				Actor:    buffer.Actor,
			},
		}

		return event, buffer, nil
	}

	return nil, buffer, nil
}

// processTagOpen handles opening tag processing with unified logic
func (a *AgentBaseService) processTagOpen(
	ctx context.Context,
	buffer agents.DeltaEventBuffer,
	config TagConfig) (*entity.AgentEvent, agents.DeltaEventBuffer, error) {

	// Check if the tag matches
	if !config.Regex.MatchString(buffer.Content) {
		return nil, buffer, errors.New("not " + config.TagName + " open")
	}

	// Extract matches if needed (for tags with attributes)
	matches := config.Regex.FindStringSubmatch(buffer.Content)

	// Update buffer state
	if config.StateUpdater != nil {
		config.StateUpdater(&buffer, matches)
	}

	// Move pointer past the tag
	buffer.Content = buffer.Content[len(config.Regex.FindString(buffer.Content)):]

	// Find content until next tag
	idx := 0
	for idx < len(buffer.Content) {
		if buffer.Content[idx] == '<' && a.couldBeOurTag(buffer.Content[idx:]) {
			break
		}
		idx++
	}

	// Extract content
	content := buffer.Content[:idx]

	// Trim newline if configured and not at end of buffer
	if idx > 0 && buffer.Content[idx-1] == '\n' && idx != len(buffer.Content) {
		content = buffer.Content[:idx-1]
	}

	// Move buffer pointer
	buffer.Content = buffer.Content[idx:]

	// 如果是参数标签且参数不是流式，需要缓存内容而不是立即发送
	if buffer.InParameter && !buffer.ParamSchema.StreamType {
		buffer.ParamContent = buffer.ParamContent + content
		return nil, buffer, nil
	}

	// Skip if content is empty and configured to do so
	if content == "" {
		return nil, buffer, nil
	}

	// Build metadata
	metadata, err := a.BufferToMetaData(ctx, buffer)
	if err != nil {
		return nil, buffer, err
	}

	// Create event
	event := &entity.AgentEvent{
		EventType: entity.AgentEventTypeAgentMessageDelta,
		Timestamp: time.Now(),
		RunID:     buffer.RunID,
		AgentMessageDeltaEvent: &entity.AgentMessageDeltaEvent{
			Type:     buffer.ContentType,
			Content:  content,
			MetaData: metadata,
			Actor:    buffer.Actor,
		},
	}

	return event, buffer, nil
}

// processTagClose handles closing tag processing with unified logic
func (a *AgentBaseService) processTagClose(
	ctx context.Context,
	buffer agents.DeltaEventBuffer,
	config TagConfig) (*entity.AgentEvent, agents.DeltaEventBuffer, error) {

	// Check if the tag matches
	if !config.Regex.MatchString(buffer.Content) {
		return nil, buffer, errors.New("not " + config.TagName + " close")
	}

	// Update buffer state
	if config.StateUpdater != nil {
		config.StateUpdater(&buffer, nil)
	}

	// Move pointer past the tag
	buffer.Content = buffer.Content[len(config.Regex.FindString(buffer.Content)):]

	// Find content until next tag
	idx := 0
	for idx < len(buffer.Content) {
		if buffer.Content[idx] == '<' && a.couldBeOurTag(buffer.Content[idx:]) {
			break
		}
		idx++
	}

	// Extract content
	content := buffer.Content[:idx]

	// Trim newline if configured and not at end of buffer
	if idx > 0 && buffer.Content[idx-1] == '\n' && idx != len(buffer.Content) {
		content = buffer.Content[:idx-1]
	}

	// Move buffer pointer
	buffer.Content = buffer.Content[idx:]

	// Skip if content is empty
	if content == "" {
		return nil, buffer, nil
	}

	// 如果在非流式参数标签内，缓存内容而不是生成事件
	if buffer.InParameter && !buffer.ParamSchema.StreamType {
		buffer.ParamContent = buffer.ParamContent + content
		return nil, buffer, nil
	}

	// Build metadata
	metadata, err := a.BufferToMetaData(ctx, buffer)
	if err != nil {
		return nil, buffer, err
	}

	// Determine delta type based on buffer state
	deltaType := buffer.ContentType
	if deltaType == entity.AgentDeltaType("") {
		deltaType = entity.AgentDeltaTypeInfo
	}

	// Create event
	event := &entity.AgentEvent{
		EventType: entity.AgentEventTypeAgentMessageDelta,
		Timestamp: time.Now(),
		RunID:     buffer.RunID,
		AgentMessageDeltaEvent: &entity.AgentMessageDeltaEvent{
			Type:     deltaType,
			Content:  content,
			MetaData: metadata,
			Actor:    buffer.Actor,
		},
	}

	return event, buffer, nil
}
