package agents

import (
	"context"
	"time"

	"code.byted.org/devgpt/kiwis/codeassist/agent/entity"
	"code.byted.org/devgpt/kiwis/lib/stream"
)

type ControlSignal int

const (
	ControlSignalStart  ControlSignal = 1
	ControlSignalStop   ControlSignal = 2
	ControlSignalPause  ControlSignal = 3
	ControlSignalResume ControlSignal = 4
)

type RunContext struct {
	TaskID         string
	AgentName      string
	AgentRunID     string
	UserID         int64
	ConversationID string
	ChunkBatchSize int
	Actors         []ActorInterface

	Round        int
	StartTime    time.Time
	CurrentActor *CurrentActor
	Messages     *entity.AgentRunHistoryMessage

	RunError      error
	ParsedContent *ParsedContent
	RunInfo       *RunInfo

	ControlChan chan ControlSignal                      // Task -> Agent
	EventChan   *stream.SendChannel[*entity.AgentEvent] // Agent -> Task
}

type CurrentActor struct {
	Actor        ActorInterface
	Index        int
	ActorMessage *entity.AgentRunHistoryMessage
}

type AgentConfig struct {
	ID      string
	Name    string
	Version string
	Actors  []ActorInterface
}

type PromptTemplate struct {
	SystemPromptTemplate  string
	ContextPromptTemplate string
}

type Agent interface {
	Name() string
	Description() string
	Run(ctx context.Context, runContext *RunContext) error

	ProcessMessages(ctx context.Context, runContext *RunContext) error
	ParseContent(ctx context.Context, content string, buffer DeltaEventBuffer) (*ParsedContent, error)
	BufferToMetaData(ctx context.Context, buffer DeltaEventBuffer) (entity.AgentMessageDeltaMetaData, error)
	TrimMessages(ctx context.Context, runContext *RunContext) error
	GenDeltaEvent(ctx context.Context, content string, buffer DeltaEventBuffer) ([]*entity.AgentEvent, DeltaEventBuffer, error)
}

type DeltaEventBuffer struct {
	Content      string
	ContentType  entity.AgentDeltaType
	InParameter  bool
	InThink      bool
	InFunction   bool
	InInvoke     bool
	InArtifacts  bool
	InvokeIDs    []string
	InvokeName   string
	ParamName    string
	ParamSchema  ParamSchema
	ParamContent string
	Actor        string
	RunID        string
}

type ParamSchema struct {
	Name        string
	Type        string
	Description string
	Required    bool
	StreamType  bool
}

// ParsedContent 表示解析后的完整内容，包含工具调用和 artifacts
type ParsedContent struct {
	ToolCalls  *ParsedToolCalls
	Artifacts  *ParsedArtifacts
	RawContent string
}

// ToolCall 表示一个工具调用
type Invoke struct {
	InvokeID     string
	FunctionName string
	Parameters   map[string]string
}

// ParsedToolCalls 表示解析后的工具调用结果
type ParsedToolCalls struct {
	HasToolCalls bool
	Invokes      []Invoke
}

// Artifact 表示一个 artifact
type Artifact struct {
	Content string
}

// ParsedArtifacts 表示解析后的 artifacts 结果
type ParsedArtifacts struct {
	HasArtifacts bool
	Artifacts    []Artifact
}

type RunInfo struct {
	TotalPromptTokens     int
	TotalCompletionTokens int
	TotalTokens           int
}
