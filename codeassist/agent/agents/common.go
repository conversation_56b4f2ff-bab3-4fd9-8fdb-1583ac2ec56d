package agents

import (
	"strings"
	"unicode/utf8"

	"code.byted.org/devgpt/kiwis/codeassist/agent/entity"
)

// 不同模型的字符与token比例
var modelCharPerToken = map[string]float64{
	// OpenAI系列模型
	"gpt-3.5-turbo": 3.3,
	"gpt-4":         3.3,
	"gpt-4-turbo":   3.3,
	"gpt-4o":        3.3,
	// 豆包系列模型
	"skylark":       3.36,
	"seed":          3.36,
	"ds":            3.36,
	"codeds":        3.36,
	"ds_sft_stream": 3.36,
	"ds_v25":        3.36,
	"fast_apply":    3.36,
	"intent":        3.36,
	// Claude系列模型
	"claude":   4.0,
	"claude-3": 4.0,
	// 默认比例
	"default": 3.36,
}

// EstimateTokenCount 估算消息的token数量
// 使用近似估算方式以提高性能，避免精确tokenizer计算的开销
func EstimateTokenCount(messages []*entity.MessageUnit, modelName string) int {
	if len(messages) == 0 {
		return 0
	}

	// 获取模型对应的字符与token比例
	charPerToken := getCharPerTokenRatio(modelName)

	totalChars := 0
	for _, msg := range messages {
		if msg.Content != "" {
			// 使用 RuneCountInString 来准确计算字符数（包括中文字符）
			totalChars += utf8.RuneCountInString(msg.Content)
		}
	}

	// 近似估算token数量
	estimatedTokens := int(float64(totalChars) / charPerToken)

	// 为每条消息添加固定的元数据开销（role等）
	// 参考OpenAI的计算方式：每条消息大约有3个token的开销
	messageOverhead := len(messages) * 3

	return estimatedTokens + messageOverhead
}

// getCharPerTokenRatio 获取指定模型的字符与token比例
func getCharPerTokenRatio(modelName string) float64 {
	modelName = strings.ToLower(modelName)

	// 精确匹配
	if ratio, exists := modelCharPerToken[modelName]; exists {
		return ratio
	}

	// 前缀匹配
	for prefix, ratio := range modelCharPerToken {
		if strings.HasPrefix(modelName, prefix) {
			return ratio
		}
	}

	// 返回默认值
	return modelCharPerToken["default"]
}
