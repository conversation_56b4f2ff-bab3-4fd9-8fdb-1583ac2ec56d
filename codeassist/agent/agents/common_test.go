package agents

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"code.byted.org/devgpt/kiwis/codeassist/agent/entity"
)

func TestEstimateTokenCount(t *testing.T) {
	tests := []struct {
		name      string
		messages  []*entity.MessageUnit
		modelName string
		expected  int // 大概的期望值范围
	}{
		{
			name:      "empty messages",
			messages:  []*entity.MessageUnit{},
			modelName: "gpt-3.5-turbo",
			expected:  0,
		},
		{
			name: "single english message",
			messages: []*entity.MessageUnit{
				{
					Role:    entity.MessageUnitRoleUser,
					Content: "Hello world",
				},
			},
			modelName: "gpt-3.5-turbo",
			expected:  6, // ~3 chars / token + 3 overhead = 6
		},
		{
			name: "single chinese message",
			messages: []*entity.MessageUnit{
				{
					Role:    entity.MessageUnitRoleUser,
					Content: "你好世界",
				},
			},
			modelName: "skylark",
			expected:  4, // ~4 chars / 3.36 + 3 overhead = 4
		},
		{
			name: "multiple messages",
			messages: []*entity.MessageUnit{
				{
					Role:    entity.MessageUnitRoleUser,
					Content: "Hello",
				},
				{
					Role:    entity.MessageUnitRoleAssistant,
					Content: "Hi there",
				},
			},
			modelName: "gpt-4",
			expected:  10, // ~13 chars / 3.3 + 6 overhead = 10
		},
		{
			name: "claude model",
			messages: []*entity.MessageUnit{
				{
					Role:    entity.MessageUnitRoleUser,
					Content: "Test message for Claude",
				},
			},
			modelName: "claude-3",
			expected:  9, // ~23 chars / 4.0 + 3 overhead = 9
		},
		{
			name: "unknown model uses default",
			messages: []*entity.MessageUnit{
				{
					Role:    entity.MessageUnitRoleUser,
					Content: "Test message",
				},
			},
			modelName: "unknown-model",
			expected:  7, // ~12 chars / 3.36 + 3 overhead = 7
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := EstimateTokenCount(tt.messages, tt.modelName)

			// 由于是近似估算，我们检查结果是否在合理范围内
			// 允许 ±2 的误差
			assert.InDelta(t, tt.expected, result, 2,
				"EstimateTokenCount(%v, %s) = %d, expected around %d",
				tt.messages, tt.modelName, result, tt.expected)
		})
	}
}

func TestGetCharPerTokenRatio(t *testing.T) {
	tests := []struct {
		modelName string
		expected  float64
	}{
		{"gpt-3.5-turbo", 3.3},
		{"GPT-4", 3.3}, // 测试大小写不敏感
		{"skylark", 3.36},
		{"skylark-pro", 3.36}, // 测试前缀匹配
		{"ds_sft_stream", 3.36},
		{"claude-3", 4.0},
		{"unknown-model", 3.36}, // 测试默认值
	}

	for _, tt := range tests {
		t.Run(tt.modelName, func(t *testing.T) {
			result := getCharPerTokenRatio(tt.modelName)
			assert.Equal(t, tt.expected, result,
				"getCharPerTokenRatio(%s) = %f, expected %f",
				tt.modelName, result, tt.expected)
		})
	}
}

func BenchmarkEstimateTokenCount(b *testing.B) {
	// 创建一个包含多条消息的测试数据
	messages := make([]*entity.MessageUnit, 10)
	for i := 0; i < 10; i++ {
		messages[i] = &entity.MessageUnit{
			Role:    entity.MessageUnitRoleUser,
			Content: "This is a test message for benchmarking the token estimation function. 这是一个用于基准测试的消息。",
		}
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		EstimateTokenCount(messages, "gpt-3.5-turbo")
	}
}
