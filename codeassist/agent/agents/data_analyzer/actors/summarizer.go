package actors

import (
	"context"
	"errors"

	"code.byted.org/gopkg/logs/v2"

	"code.byted.org/devgpt/kiwis/codeassist/agent/agents"
	"code.byted.org/devgpt/kiwis/codeassist/agent/entity"
)

const (
	MaxSummarizerRetryRounds = 3
)

// Summarizer 总结Actor，负责生成最终的分析总结
type Summarizer struct {
	agents.Actor
}

func (s *Summarizer) GetName() string {
	return agents.SummarizerActor
}

func (s *Summarizer) Description() string {
	return "Summarizer: 总结Actor，负责生成最终的分析总结"
}

func (s *Summarizer) BeforeExecute(ctx context.Context, runContext *agents.RunContext) error {
	userPromptTemplate := ""
	if len(s.UserPromptTemplate) > 0 && s.UserPromptTemplate[0] != "" {
		userPromptTemplate = s.UserPromptTemplate[0]
	} else {
		logs.V1.CtxError(ctx, "[Summarizer] UserPromptTemplate is empty")
	}
	if runContext.CurrentActor.ActorMessage != nil {
		runContext.CurrentActor.ActorMessage.AgentTrajectory = append(runContext.CurrentActor.ActorMessage.AgentTrajectory, &entity.MessageUnit{
			Role:    entity.MessageUnitRoleUser,
			Content: userPromptTemplate,
			Actor:   agents.SummarizerActor,
		})
	} else {
		return errors.New("[Summarizer] ActorMessage is empty")
	}
	return nil
}

func (s *Summarizer) IsFinished(ctx context.Context, runContext *agents.RunContext) bool {
	if runContext.Round > MaxRounds+MaxSummarizerRetryRounds {
		logs.V1.CtxError(ctx, "[Summarizer] Round is greater than %d", MaxRounds+MaxSummarizerRetryRounds)
		return true
	}

	return len(runContext.Messages.AgentTrajectory)!=0 && 
	runContext.Messages.AgentTrajectory[len(runContext.Messages.AgentTrajectory)-1].Actor == agents.SummarizerActor &&
	runContext.Messages.AgentTrajectory[len(runContext.Messages.AgentTrajectory)-1].Role == entity.MessageUnitRoleAssistant
}
