package actors

import (
	"context"
	"errors"

	"code.byted.org/devgpt/kiwis/codeassist/agent/agents"
	"code.byted.org/devgpt/kiwis/codeassist/agent/entity"
)

type Echoer struct {
	agents.Actor
}

func (e *Echoer) GetName() string {
	return agents.EchoerActor
}

func (e *Echoer) Description() string {
	return "Echoer: 回应Actor，负责回应用户的问题"
}

func (e *Echoer) IsFinished(ctx context.Context, runContext *agents.RunContext) bool {
	return runContext.Round > 1
}

func (e *Echoer) BeforeExecute(ctx context.Context, runContext *agents.RunContext) error {
	if runContext.CurrentActor.ActorMessage == nil {
		return errors.New("[Echoer] ActorMessage is empty")
	}
	if len(runContext.CurrentActor.ActorMessage.ChatHistory) > 0 {
		runContext.CurrentActor.ActorMessage.ChatHistory = []*entity.MessageUnit{}
	}
	return nil
}