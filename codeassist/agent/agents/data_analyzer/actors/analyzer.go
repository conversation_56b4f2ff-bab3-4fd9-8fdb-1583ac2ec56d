package actors

import (
	"context"
	"regexp"
	"time"

	"code.byted.org/gopkg/logs/v2"

	"code.byted.org/devgpt/kiwis/codeassist/agent/agents"
	"code.byted.org/devgpt/kiwis/codeassist/agent/entity"
)

// Analyzer	 数据分析Actor，负责分析用户输入并生成分析报告
type Analyzer struct {
	agents.Actor
}

const (
	MaxRounds  = 50
	MaxTimeout = time.Minute * 30
)

func (a *Analyzer) GetName() string {
	return agents.AnalyzerActor
}

func (a *Analyzer) BeforeExecute(ctx context.Context, runContext *agents.RunContext) error {
	// 创建新的切片，只保留非 EchoerActor 的消息
	actorMessages := runContext.CurrentActor.ActorMessage
	if actorMessages == nil {
		logs.V1.CtxWarn(ctx, "actorMessages is nil, use runContext.Messages")
		actorMessages = runContext.Messages
	}
	var filteredTrajectory []*entity.MessageUnit
	for _, msg := range actorMessages.AgentTrajectory {
		// 只保留不是 echoer 的消息
		if msg.Actor != agents.EchoerActor {
			filteredTrajectory = append(filteredTrajectory, msg)
		}
	}
	actorMessages.AgentTrajectory = filteredTrajectory
	return nil
}

// IsFinished 判断分析是否完成
func (a *Analyzer) IsFinished(ctx context.Context, runContext *agents.RunContext) bool {
	// 检查最后一条消息是否包含分析完成的标志

	if runContext.RunError != nil {
		return true
	}

	// 检查是否达到最大分析轮数
	if runContext.Round >= MaxRounds {
		return true
	}
	// 检查是否超时
	timeout := runContext.StartTime.Add(MaxTimeout)
	if timeout.Before(time.Now()) {
		return true
	}

	if runContext.CurrentActor.ActorMessage != nil && len(runContext.CurrentActor.ActorMessage.AgentTrajectory) > 2 {
		for i := len(runContext.CurrentActor.ActorMessage.AgentTrajectory) - 1; i >= 0; i-- {
			message := runContext.CurrentActor.ActorMessage.AgentTrajectory[i]
			if message.Role == entity.MessageUnitRoleAssistant {
				return !a.HasToolCall(message.Content)
			}
		}
	}
	return false
}

func (a *Analyzer) HasToolCall(content string) bool {
	// 查找所有的function_calls块
	functionCallsPattern := regexp.MustCompile(`(?s)<doubao:function_calls>(.*?)</doubao:function_calls>`)
	functionCallsMatches := functionCallsPattern.FindAllStringSubmatch(content, -1)

	return len(functionCallsMatches) > 0
}
