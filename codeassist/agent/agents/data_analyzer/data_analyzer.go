package data_analyzer

import (
	"code.byted.org/devgpt/kiwis/codeassist/agent/agents/base"
)

const (
	AgentNameDataAnalyzer = "data_analyze_agent"
)

type DataAnalysisAgent struct {
	*base.AgentBaseService
}

func NewDataAnalysisAgent(agentBaseService *base.AgentBaseService) *DataAnalysisAgent {
	return &DataAnalysisAgent{
		AgentBaseService: agentBaseService,
	}
}

func (a *DataAnalysisAgent) Name() string {
	return AgentNameDataAnalyzer
}

func (a *DataAnalysisAgent) Description() string {
	return "DataAnalyzer:xxx"
}
