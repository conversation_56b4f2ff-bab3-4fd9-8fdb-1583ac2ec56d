# Go Template 使用示例

## 概述

代码已经修改为使用 Go template 来渲染 `SystemPromptTemplate` 和 `ContextPromptTemplate`。这允许在模板中使用动态变量和逻辑。

## 模板变量

在模板中可以使用以下变量：

### 基础 Actor 类中的变量：
- `{{.Actor}}` - 当前的 Actor 实例
- `{{.RunContext}}` - 运行上下文
- `{{.Tools}}` - 工具列表

### Analyzer 类中的额外变量：
- `{{.ToolDesc}}` - 工具描述的字符串
- `{{.Tools}}` - 工具列表
- `{{.RunContext}}` - 运行上下文

## 使用示例

### 1. 简单的模板示例

```
你是一个数据分析助手。

可用的工具：
{{.ToolDesc}}

当前轮次：{{.RunContext.Round}}
```

### 2. 使用循环的模板示例

```
你是一个数据分析助手。

可用的工具列表：
{{range .Tools}}
- {{.Name}}: {{.Description}}
{{end}}

{{if gt .RunContext.Round 10}}
注意：已经执行了超过10轮，请尽快完成任务。
{{end}}
```

### 3. 使用条件判断的模板示例

```
你是一个数据分析助手。

{{if .RunContext.RunError}}
错误信息：{{.RunContext.RunError}}
请根据错误信息调整你的策略。
{{else}}
请继续执行任务。
{{end}}

工具描述：
{{.ToolDesc}}
```

### 4. 复杂的模板示例

```
系统角色：{{.Actor.Name}}

{{if .Tools}}
可用工具：
{{range $index, $tool := .Tools}}
{{$index | add 1}}. {{$tool.Name}}
   描述：{{$tool.Description}}
{{end}}
{{else}}
没有可用的工具。
{{end}}

执行状态：
- 当前轮次：{{.RunContext.Round}}/{{.MaxRounds}}
- 开始时间：{{.RunContext.StartTime.Format "2006-01-02 15:04:05"}}
{{if .RunContext.ParsedContent}}
- 已解析内容：是
{{end}}
```

## 注意事项

1. 如果模板解析失败，系统会返回原始的模板字符串，确保不会因为模板错误导致系统崩溃。

2. 模板中可以使用 Go template 的所有功能，包括：
   - 变量引用：`{{.Variable}}`
   - 条件判断：`{{if .Condition}} ... {{else}} ... {{end}}`
   - 循环：`{{range .Collection}} ... {{end}}`
   - 管道操作：`{{.Value | function}}`

3. 在 Analyzer 类中，`ToolDesc` 是预先格式化好的工具描述字符串，如果需要更灵活的控制，可以直接使用 `{{range .Tools}}` 来遍历工具列表。

## 配置示例

在配置文件中，可以这样定义模板：

```yaml
AgentPromptMap:
  data_analyzer_system_prompt: |
    你是一个专业的数据分析助手。
    
    {{if .Tools}}
    可用的分析工具：
    {{range .Tools}}
    - {{.Name}}: {{.Description}}
    {{end}}
    {{end}}
    
    请根据用户的需求进行数据分析。
```

这样就可以充分利用 Go template 的功能来创建更加灵活和动态的提示词模板。 