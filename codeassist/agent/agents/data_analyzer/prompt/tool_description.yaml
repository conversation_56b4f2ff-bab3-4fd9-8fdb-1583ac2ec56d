AgentToolMap:
  data_analyze: |
    def data_analysis(code_script: str):
        """
        数据分析工具
        当用户上传数据文件时，你可以使用该工具编写并运行一段python代码来载入、分析和处理数据

        Args:
        - code_script (str) [Required]: 需要被执行的完整python代码，用于数据载入、分析和处理

        Returns:
        - result（string）: 工具执行结果

        """
  file_edit: |
    def edit_file(new_str: str, old_str: str, path: str):
        """
        文件编辑工具
        该工具用于原文件的编辑，你需要提供文件路径`path`、待替换的字符串`old_str`、以及新的字符串`new_str`，如果未提供新字符串`new_str`，则表示删除待替换的字符串。
        * `old_str` 参数必须与原始文件中一个或多个连续行的内容完全匹配（特别注意空格和缩进！），并且会将匹配到的所有内容替换为`new_str`

        Args:
        - new_str (str) [Optional]: 选填参数，包含要替换的新字符串，如果未提供，则表示删除该字符串
        - old_str (str) [Required]: 必填参数，用于指定 `path` 文件中待替换的字符串内容。
        - path (str) [Required]: 文件的绝对路径，例如 `/mnt/workspace/file.py`

        Returns:
        - result（string）: 工具执行结果

        """
  file_view: |
    def view_file(file_path: str, offset: int, limit: int):
        """
        文件查看工具
        该工具用于查看文件内容，你需要提供对应路径 `file_path`，默认读取最多 2000 行，如果文件内容过长，请使用 `offset` 和 `limit` 参数指定查看范围
        * `file_path`: 必填参数，文件的绝对路径，例如 `/mnt/workspace/file.py`
        * `offset`: 可选参数，从第几行开始读取，默认是 0
        * `limit`: 可选参数，读取多少行，默认是 2000 行
        - 任何长度超过 2000 个字符的行都会被截断

        Args:
        - file_path (str) [Required]: 必填参数，文件的绝对路径，例如 `/mnt/workspace/file.py`
        - offset (int) [Optional]: 可选参数，从第几行开始读取，默认是 0，一般在文件内容过长时使用 默认为 0
        - limit (int) [Optional]: 可选参数，读取多少行，默认是 2000 行，一般在文件内容过长时使用 默认为 2000

        Returns:
        - result（string）: 工具执行结果

        """
  file_create: |
    def create_file(file_text: str, path: str):
        """
        文件新建工具
        该工具用于新文件的创建，你需要提供文件路径和文件内容。

        Args:
        - file_text (str) [Required]: 必填参数，用于指定待创建文件的内容。
        - path (str) [Required]: 文件的绝对路径，例如 `/mnt/workspace/file.py`

        Returns:
        - result（string）: 工具执行结果

        """
  file_list: |
    def ls(path: str, show_hidden: bool):
        """
        目录查看工具
        该工具用于查看给定目录下的文件以及文件夹，你需要提供路径 `path` 
        * `path`: 绝对路径，例如 `/mnt/workspace/`
        * `show_hidden`: 是否显示隐藏文件，默认不显示

        Args:
        - path (str) [Required]: 必填参数，目录的绝对路径，例如 `/mnt/workspace/`
        - show_hidden (bool) [Optional]: 可选参数，是否显示隐藏文件，默认不显示 默认为 False

        Returns:
        - result（string）: 工具执行结果

        """