package data_analyzer

import (
	"context"
	"fmt"
	"os"
	"strings"
	"testing"
	"time"

	"go.uber.org/mock/gomock"

	"code.byted.org/devgpt/kiwis/codeassist/agent/agents"
	"code.byted.org/devgpt/kiwis/codeassist/agent/agents/base"
	"code.byted.org/devgpt/kiwis/codeassist/agent/entity"
	mockservice "code.byted.org/devgpt/kiwis/codeassist/agent/mock/service"
)

// TestFullDataAnalysisFlowDemo 演示完整的数据分析流程和事件
func TestFullDataAnalysisFlowDemo(t *testing.T) {
	ctx := context.Background()

	// 修复空指针问题：创建mock controller和mock ToolService
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockToolService := mockservice.NewMockToolService(ctrl)
	// 设置GetTool方法返回nil（表示没有找到工具）
	mockToolService.EXPECT().GetTool(gomock.Any()).Return(nil).AnyTimes()

	// 正确初始化 DataAnalysisAgent
	baseService := &base.AgentBaseService{
		ToolService: mockToolService,
	}
	agent := &DataAnalysisAgent{
		AgentBaseService: baseService,
	}

	// 创建事件收集器
	allEvents := make([]*entity.AgentEvent, 0)

	// 模拟用户查询
	userQuery := "请分析一下我们公司过去三个月的销售数据，找出销售趋势和异常点"

	fmt.Println("=== 数据分析Agent完整流程演示 ===")
	fmt.Printf("用户查询: %s\n\n", userQuery)

	// 1. Echoer Actor - 不包含任何标签
	fmt.Println("--- 第1阶段: Echoer Actor ---")
	echoerContent := []string{
		"好的，我会帮您分析",
		"公司过去三个月的销售数据",
		"，找出销售趋势和异常点。",
		"让我开始进行详细分析。",
	}

	buffer := agents.DeltaEventBuffer{ContentType: entity.AgentDeltaTypeInfo}
	for _, chunk := range echoerContent {
		events, newBuffer, err := agent.GenDeltaEvent(ctx, chunk, buffer)
		if err != nil {
			t.Fatal(err)
		}
		buffer = newBuffer
		for _, event := range events {
			allEvents = append(allEvents, event)
			fmt.Printf("  [Delta] %s: %q\n", event.AgentMessageDeltaEvent.Type, event.AgentMessageDeltaEvent.Content)
		}
	}

	// 2. Analyzer Actor - 第一轮，包含think和工具调用
	fmt.Println("\n--- 第2阶段: Analyzer Actor (第1轮) ---")
	analyzerRound1 := []string{
		"<think>用户需要分析销售数据，",
		"我需要先获取数据，然后进行趋势分析</think>",
		"我来帮您分析销售数据。首先让我获取数据。\n",
		"<doubao:function_calls>\n<doubao:invoke name=\"get_sales_data\">",
		"<doubao:parameter name=\"period\">last_3_months</doubao:parameter>",
		"<doubao:parameter name=\"format\">json</doubao:parameter>",
		"</doubao:invoke>\n</doubao:function_calls>",
	}

	buffer = agents.DeltaEventBuffer{ContentType: entity.AgentDeltaTypeInfo}
	for _, chunk := range analyzerRound1 {
		events, newBuffer, err := agent.GenDeltaEvent(ctx, chunk, buffer)
		if err != nil {
			t.Fatal(err)
		}
		buffer = newBuffer
		for _, event := range events {
			allEvents = append(allEvents, event)
			if event.AgentMessageDeltaEvent.Type == entity.AgentDeltaTypeThink {
				fmt.Printf("  [Think] %q\n", event.AgentMessageDeltaEvent.Content)
			} else if event.AgentMessageDeltaEvent.MetaData.InvokeName != "" {
				fmt.Printf("  [Invoke] %s\n", event.AgentMessageDeltaEvent.MetaData.InvokeName)
			} else if event.AgentMessageDeltaEvent.MetaData.ParamName != "" {
				fmt.Printf("  [Param] %s = %q\n", event.AgentMessageDeltaEvent.MetaData.ParamName, event.AgentMessageDeltaEvent.Content)
			} else {
				fmt.Printf("  [Info] %q\n", event.AgentMessageDeltaEvent.Content)
			}
		}
	}

	// 模拟工具调用结果
	fmt.Println("\n  [ToolCall] get_sales_data 执行完成")
	fmt.Println("  输出: 获取到销售数据：1250条记录，总销售额250万")

	// 3. Analyzer Actor - 第二轮
	fmt.Println("\n--- 第3阶段: Analyzer Actor (第2轮) ---")
	analyzerRound2 := []string{
		"<think>获取到了销售数据，",
		"现在需要进行趋势分析和异常检测</think>",
		"数据已获取，现在进行趋势分析。\n",
		"<doubao:function_calls>\n<doubao:invoke name=\"analyze_trend\">",
		"<doubao:parameter name=\"data_id\">sales_2024_q1</doubao:parameter>",
		"<doubao:parameter name=\"method\">linear_regression</doubao:parameter>",
		"</doubao:invoke>\n</doubao:function_calls>",
	}

	buffer = agents.DeltaEventBuffer{ContentType: entity.AgentDeltaTypeInfo}
	for _, chunk := range analyzerRound2 {
		events, newBuffer, err := agent.GenDeltaEvent(ctx, chunk, buffer)
		if err != nil {
			t.Fatal(err)
		}
		buffer = newBuffer
		for _, event := range events {
			allEvents = append(allEvents, event)
			if event.AgentMessageDeltaEvent.Type == entity.AgentDeltaTypeThink {
				fmt.Printf("  [Think] %q\n", event.AgentMessageDeltaEvent.Content)
			} else if event.AgentMessageDeltaEvent.MetaData.InvokeName != "" {
				fmt.Printf("  [Invoke] %s\n", event.AgentMessageDeltaEvent.MetaData.InvokeName)
			} else if event.AgentMessageDeltaEvent.MetaData.ParamName != "" {
				fmt.Printf("  [Param] %s = %q\n", event.AgentMessageDeltaEvent.MetaData.ParamName, event.AgentMessageDeltaEvent.Content)
			} else {
				fmt.Printf("  [Info] %q\n", event.AgentMessageDeltaEvent.Content)
			}
		}
	}

	// 模拟工具调用结果
	fmt.Println("\n  [ToolCall] analyze_trend 执行完成")
	fmt.Println("  输出: 趋势分析完成：上升趋势，增长率15%，第6周存在异常")

	// 4. Analyzer Actor - 第三轮，没有工具调用
	fmt.Println("\n--- 第4阶段: Analyzer Actor (第3轮) ---")
	analyzerRound3 := []string{
		"<think>分析完成，",
		"现在总结分析结果</think>",
		"根据分析结果，我发现了以下几点：\n\n",
		"1. **销售趋势**：过去三个月整体呈上升趋势，月均增长率为15%\n",
		"2. **异常点**：在第二个月的第三周出现了异常下降，可能与市场活动有关\n",
		"3. **产品表现**：产品A销售额占比最高，达到45%\n",
		"4. **建议**：继续加强产品A的推广，同时关注异常时期的原因分析",
	}

	buffer = agents.DeltaEventBuffer{ContentType: entity.AgentDeltaTypeInfo}
	for _, chunk := range analyzerRound3 {
		events, newBuffer, err := agent.GenDeltaEvent(ctx, chunk, buffer)
		if err != nil {
			t.Fatal(err)
		}
		buffer = newBuffer
		for _, event := range events {
			allEvents = append(allEvents, event)
			if event.AgentMessageDeltaEvent.Type == entity.AgentDeltaTypeThink {
				fmt.Printf("  [Think] %q\n", event.AgentMessageDeltaEvent.Content)
			} else {
				fmt.Printf("  [Info] %q\n", event.AgentMessageDeltaEvent.Content)
			}
		}
	}

	// 5. Summarizer Actor - 包含artifacts
	fmt.Println("\n--- 第5阶段: Summarizer Actor ---")
	summarizerContent := []string{
		"基于完整的分析，我为您准备了详细的分析报告。\n\n",
		"<artifacts>\n# 销售数据分析报告\n\n",
		"## 执行摘要\n",
		"过去三个月的销售数据显示公司业绩稳步增长，",
		"月均增长率达到15%。\n\n",
		"## 关键发现\n",
		"- 整体销售趋势向好\n",
		"- 产品A表现突出\n",
		"- 第二个月第三周存在异常\n\n",
		"## 详细分析\n",
		"### 1. 销售趋势\n",
		"通过线性回归分析...\n\n",
		"### 2. 产品表现\n",
		"各产品销售占比...\n",
		"</artifacts>\n\n",
		"分析报告已生成，您可以查看详细的分析结果。",
	}

	buffer = agents.DeltaEventBuffer{ContentType: entity.AgentDeltaTypeInfo}
	artifactsContent := strings.Builder{}
	for _, chunk := range summarizerContent {
		events, newBuffer, err := agent.GenDeltaEvent(ctx, chunk, buffer)
		if err != nil {
			t.Fatal(err)
		}
		buffer = newBuffer

		// 收集artifacts内容
		if strings.Contains(chunk, "<artifacts>") || buffer.InArtifacts {
			artifactsContent.WriteString(chunk)
		}

		for _, event := range events {
			allEvents = append(allEvents, event)
			fmt.Printf("  [Info] %q\n", event.AgentMessageDeltaEvent.Content)
		}
	}

	// 显示artifacts内容（虽然在事件中被过滤了）
	fmt.Println("\n  [Artifacts] 生成了分析报告文件")

	// 6. 保存所有事件到文件
	saveEventsDemoToFile(t, allEvents)

	// 7. 统计事件
	fmt.Println("\n=== 事件统计 ===")
	thinkCount := 0
	infoCount := 0
	paramCount := 0
	for _, event := range allEvents {
		if event.EventType == entity.AgentEventTypeAgentMessageDelta {
			switch event.AgentMessageDeltaEvent.Type {
			case entity.AgentDeltaTypeThink:
				thinkCount++
			case entity.AgentDeltaTypeInfo:
				infoCount++
			}
			if event.AgentMessageDeltaEvent.MetaData.ParamName != "" {
				paramCount++
			}
		}
	}

	fmt.Printf("总事件数: %d\n", len(allEvents))
	fmt.Printf("Think事件: %d\n", thinkCount)
	fmt.Printf("Info事件: %d\n", infoCount)
	fmt.Printf("参数事件: %d\n", paramCount)
}

// saveEventsDemoToFile 将事件保存到文件
func saveEventsDemoToFile(t *testing.T, events []*entity.AgentEvent) {
	var sb strings.Builder
	sb.WriteString("=== 数据分析Agent完整流程事件记录 ===\n")
	sb.WriteString(fmt.Sprintf("记录时间: %s\n", time.Now().Format("2006-01-02 15:04:05")))
	sb.WriteString(fmt.Sprintf("事件总数: %d\n\n", len(events)))

	// Actor阶段标记
	actorStage := "Echoer"
	analyzerRound := 1

	for i, event := range events {
		// 根据内容判断当前阶段
		if i > 0 && event.AgentMessageDeltaEvent != nil {
			content := event.AgentMessageDeltaEvent.Content
			if strings.Contains(content, "分析销售数据") {
				actorStage = "Analyzer"
			} else if strings.Contains(content, "基于完整的分析") {
				actorStage = "Summarizer"
			} else if actorStage == "Analyzer" && strings.Contains(content, "数据已获取") {
				analyzerRound = 2
			} else if actorStage == "Analyzer" && strings.Contains(content, "根据分析结果") {
				analyzerRound = 3
			}
		}

		sb.WriteString(fmt.Sprintf("\n--- 事件 #%d [%s", i+1, actorStage))
		if actorStage == "Analyzer" {
			sb.WriteString(fmt.Sprintf(" Round %d", analyzerRound))
		}
		sb.WriteString("] ---\n")

		sb.WriteString(fmt.Sprintf("类型: %s\n", event.EventType))

		if event.EventType == entity.AgentEventTypeAgentMessageDelta {
			sb.WriteString("Delta事件详情:\n")
			sb.WriteString(fmt.Sprintf("  Delta类型: %s\n", event.AgentMessageDeltaEvent.Type))
			sb.WriteString(fmt.Sprintf("  内容: %q\n", event.AgentMessageDeltaEvent.Content))
			if event.AgentMessageDeltaEvent.MetaData.InvokeName != "" {
				sb.WriteString(fmt.Sprintf("  调用名称: %s\n", event.AgentMessageDeltaEvent.MetaData.InvokeName))
			}
			if event.AgentMessageDeltaEvent.MetaData.ParamName != "" {
				sb.WriteString(fmt.Sprintf("  参数名称: %s\n", event.AgentMessageDeltaEvent.MetaData.ParamName))
			}
		}
	}

	// 添加工具调用事件（模拟）
	sb.WriteString("\n\n=== 模拟的工具调用事件 ===\n")
	sb.WriteString("\n--- 工具调用 #1 ---\n")
	sb.WriteString("工具名称: get_sales_data\n")
	sb.WriteString("输入参数:\n")
	sb.WriteString("  period: last_3_months\n")
	sb.WriteString("  format: json\n")
	sb.WriteString("输出结果: 获取到销售数据：1250条记录，总销售额250万\n")

	sb.WriteString("\n--- 工具调用 #2 ---\n")
	sb.WriteString("工具名称: analyze_trend\n")
	sb.WriteString("输入参数:\n")
	sb.WriteString("  data_id: sales_2024_q1\n")
	sb.WriteString("  method: linear_regression\n")
	sb.WriteString("输出结果: 趋势分析完成：上升趋势，增长率15%，第6周存在异常\n")

	// 添加Artifacts事件（模拟）
	sb.WriteString("\n\n=== 模拟的Artifacts事件 ===\n")
	sb.WriteString("生成文件: report_1.md\n")
	sb.WriteString("文件内容:\n")
	sb.WriteString("# 销售数据分析报告\n\n")
	sb.WriteString("## 执行摘要\n")
	sb.WriteString("过去三个月的销售数据显示公司业绩稳步增长，月均增长率达到15%。\n\n")
	sb.WriteString("## 关键发现\n")
	sb.WriteString("- 整体销售趋势向好\n")
	sb.WriteString("- 产品A表现突出\n")
	sb.WriteString("- 第二个月第三周存在异常\n\n")
	sb.WriteString("## 详细分析\n")
	sb.WriteString("### 1. 销售趋势\n")
	sb.WriteString("通过线性回归分析...\n\n")
	sb.WriteString("### 2. 产品表现\n")
	sb.WriteString("各产品销售占比...\n")

	// 写入文件
	filename := "data_analysis_agent_demo_events.log"
	err := os.WriteFile(filename, []byte(sb.String()), 0644)
	if err != nil {
		t.Errorf("Failed to write file: %v", err)
		return
	}

	fmt.Printf("\n事件记录已保存到文件: %s\n", filename)
}
