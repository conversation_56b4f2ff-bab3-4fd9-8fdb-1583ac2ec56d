package agents

import (
	"bytes"
	"context"
	"strings"
	"text/template"

	"code.byted.org/gopkg/logs/v2"
	"github.com/samber/lo"

	"code.byted.org/devgpt/kiwis/lib/config"
)

const (
	EchoerActor     = "echoer"
	AnalyzerActor   = "analyzer"
	SummarizerActor = "summarizer"
)

// ActorInterface 定义了Actor的核心行为
type ActorInterface interface {
	// 获取Actor名称
	GetName() string
	// 判断Actor是否已完成任务
	IsFinished(ctx context.Context, runContext *RunContext) bool
	// 获取系统提示词
	GetSystemPrompt(ctx context.Context, runContext *RunContext) []string
	// 获取上下文提示词
	GetContextPrompt(ctx context.Context, runContext *RunContext) []string
	// 获取工具列表
	GetToolList() []string
	// 获取模型配置
	GetModelConfig() *config.CodeAssistLLMConfig
	// 获取裁剪配置
	GetTrimConfig() *TrimConfig
	// 执行前的准备工作
	BeforeExecute(ctx context.Context, runContext *RunContext) error
	// 执行后的清理工作
	AfterExecute(ctx context.Context, runContext *RunContext) error
}

// Actor 基础Actor结构体，实现了ActorInterface
type Actor struct {
	Name                  string
	TrimConfig            *TrimConfig
	SystemPromptTemplate  []string
	ContextPromptTemplate []string
	UserPromptTemplate    []string
	ToolList              []*Tool
	ModelConfig           *config.CodeAssistLLMConfig
}

type Tool struct {
	Name        string
	Description string
}

// 实现ActorInterface
func (a *Actor) GetName() string {
	return a.Name
}

func (a *Actor) IsFinished(ctx context.Context, runContext *RunContext) bool {
	// 默认实现：永不结束，由Agent控制
	return false
}

func (a *Actor) GetSystemPrompt(ctx context.Context, runContext *RunContext) []string {
	// 使用 Go template 进行模板渲染，注入动态变量

	systemPromptList := make([]string, 0, len(a.SystemPromptTemplate))
	for _, prompt := range a.SystemPromptTemplate {
		tmpl, err := template.New("systemPrompt").Parse(prompt)
		if err != nil {
			// 如果模板解析失败，返回原始模板
			logs.V1.CtxWarn(ctx, "parse system prompt template failed: %v", err)
			systemPromptList = append(systemPromptList, prompt)
			continue
		}

		if a.ToolList == nil {
			systemPromptList = append(systemPromptList, prompt)
			continue
		}
		toolDescList := lo.Map(a.ToolList, func(tool *Tool, _ int) string {
			return "Function: \n" + tool.Description
		})
		toolDesc := strings.Join(toolDescList, "\n")

		// 准备模板数据
		data := map[string]interface{}{
			"tool_descriptions": toolDesc,
		}

		// 渲染模板
		var buf bytes.Buffer
		err = tmpl.Execute(&buf, data)
		if err != nil {
			// 如果模板执行失败，返回原始模板
			logs.V1.CtxWarn(ctx, "execute system prompt template failed: %v", err)
			systemPromptList = append(systemPromptList, prompt)
			continue
		}
		systemPromptList = append(systemPromptList, buf.String())
	}
	return systemPromptList
}

func (a *Actor) GetContextPrompt(ctx context.Context, runContext *RunContext) []string {
	// 使用 Go template 进行模板渲染，注入动态变量
	contextPromptList := make([]string, 0, len(a.ContextPromptTemplate))
	actorMessages := runContext.CurrentActor.ActorMessage
	if actorMessages == nil {
		logs.V1.CtxWarn(ctx, "actorMessages is nil, use runContext.Messages")
		actorMessages = runContext.Messages
	}
	for _, prompt := range a.ContextPromptTemplate {
		tmpl, err := template.New("contextPrompt").Parse(prompt)
		if err != nil {
			// 如果模板解析失败，返回原始模板
			logs.V1.CtxWarn(ctx, "parse context prompt template failed: %v", err)
			contextPromptList = append(contextPromptList, prompt)
			continue
		}
		if actorMessages.WorkspaceInfo != nil {
			workspaceInfo := actorMessages.WorkspaceInfo
			// 如果 workspaceInfo.WorkspaceFiles 长度超过最大限制，则截取前 MaxWorkspaceFilesCount 个文件
			if a.TrimConfig.MaxWorkspaceFilesCount != 0 && len(workspaceInfo.WorkspaceFiles) > a.TrimConfig.MaxWorkspaceFilesCount {
				workspaceInfo.WorkspaceFiles = workspaceInfo.WorkspaceFiles[:a.TrimConfig.MaxWorkspaceFilesCount]
			}
			data := map[string]interface{}{
				"workspace_directory": workspaceInfo.WorkspaceDirectory,
				"user_files":          strings.Join(workspaceInfo.UserFiles, "\n"),
				"workspace_files":     strings.Join(workspaceInfo.WorkspaceFiles, "\n"),
			}
			var buf bytes.Buffer
			err = tmpl.Execute(&buf, data)
			if err != nil {
				logs.V1.CtxWarn(ctx, "execute context prompt template failed: %v", err)
				contextPromptList = append(contextPromptList, prompt)
				continue
			}
			contextPromptList = append(contextPromptList, buf.String())
		}
	}
	return contextPromptList
}

func (a *Actor) GetToolList() []string {
	return lo.Map(a.ToolList, func(tool *Tool, _ int) string {
		return tool.Name
	})
}

func (a *Actor) GetModelConfig() *config.CodeAssistLLMConfig {
	return a.ModelConfig
}

func (a *Actor) GetTrimConfig() *TrimConfig {
	return a.TrimConfig
}

func (a *Actor) BeforeExecute(ctx context.Context, runContext *RunContext) error {
	// 默认实现：不做任何事
	return nil
}

func (a *Actor) AfterExecute(ctx context.Context, runContext *RunContext) error {
	// 默认实现：不做任何事
	return nil
}

type TrimConfig struct {
	MaxTokens              int    // 最大token数
	MaxHistoryTokens       int    // 最大历史消息token数
	MaxAgentTrajTokens     int    // 最大Agent轨迹token数
	MaxWorkspaceFilesCount int    // 最大工作区文件数
	MaxToolCallLength      int    // 最大工具返回内容长度
	ToolCallTrimMsg        string // 工具调用裁剪提示
	AgentTrajTrimMsg       string // Agent轨迹裁剪提示
	HistoryTrimMsg         string // 历史消息裁剪提示
}
