package handler

import (
	"context"
	"strconv"

	"code.byted.org/gopkg/logs/v2"
	"github.com/pkg/errors"

	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/codeassist"
	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/common"
	"code.byted.org/devgpt/kiwis/codeassist/agent/service"
	"code.byted.org/devgpt/kiwis/lib/kitex"
	"code.byted.org/devgpt/kiwis/lib/util"
)

type AgentHandler struct {
	TaskService service.TaskService
}

func (h *AgentHandler) InterruptCodeAgentTask(ctx context.Context, req *codeassist.InterruptCodeAgentTaskRequest) (*codeassist.InterruptCodeAgentTaskResponse, error) {
	if req == nil || req.MessageID == 0 || req.UserID == 0 {
		return nil, kitex.NewKitexError(common.ErrorCode_ErrParamInvalid, errors.New("message id is required"))
	}
	logs.V1.CtxInfo(ctx, "interrupt code agent task, request: %s", util.MarshalStruct(req))

	err := h.TaskService.InterruptTask(ctx, strconv.FormatInt(req.MessageID, 10), strconv.FormatInt(req.UserID, 10))
	if err != nil {
		if errors.Is(err, service.ErrTaskNotFound) {
			return nil, kitex.NewKitexError(common.ErrorCode_ErrRecordNotFound, errors.New("task not found"))
		}
		return nil, kitex.NewKitexError(common.ErrorCode_ErrInternal, err)
	}

	return &codeassist.InterruptCodeAgentTaskResponse{}, nil
}

func (h *AgentHandler) RecoverFromSchedulerCallBack(ctx context.Context, req *codeassist.ExecuteJobCallbackReq) (*codeassist.ExecuteJobCallbackResp, error) {
	if req == nil {
		return nil, kitex.NewKitexError(common.ErrorCode_ErrParamInvalid, errors.New("request is nil"))
	}
	logs.V1.CtxInfo(ctx, "recover code agent task, request: %s", util.MarshalStruct(req))

	err := h.TaskService.RecoverFromSchedulerCallBack(ctx, req)
	if err != nil {
		return nil, kitex.NewKitexError(common.ErrorCode_ErrInternal, err)
	}

	return &codeassist.ExecuteJobCallbackResp{}, nil
}
