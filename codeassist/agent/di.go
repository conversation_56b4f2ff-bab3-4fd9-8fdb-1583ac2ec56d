package agent

import (
	"context"

	"code.byted.org/overpass/flowpc_chat_eventstream/kitex_gen/flowpc_chat_eventstream/eventstreamservice"
	"code.byted.org/samanthapkg/jobschedule"
	"go.uber.org/fx"

	"code.byted.org/devgpt/kiwis/codeassist/agent/agents/base"
	"code.byted.org/devgpt/kiwis/codeassist/agent/agents/data_analyzer"
	dal "code.byted.org/devgpt/kiwis/codeassist/agent/dal/impl"
	"code.byted.org/devgpt/kiwis/codeassist/agent/handler"
	agentFactory "code.byted.org/devgpt/kiwis/codeassist/agent/service/agent_factory"
	agentRunService "code.byted.org/devgpt/kiwis/codeassist/agent/service/agent_run"
	artifactsService "code.byted.org/devgpt/kiwis/codeassist/agent/service/artifacts"
	memoryService "code.byted.org/devgpt/kiwis/codeassist/agent/service/memory"
	protocolService "code.byted.org/devgpt/kiwis/codeassist/agent/service/protocol"
	reviewService "code.byted.org/devgpt/kiwis/codeassist/agent/service/review"
	taskService "code.byted.org/devgpt/kiwis/codeassist/agent/service/task"
	toolService "code.byted.org/devgpt/kiwis/codeassist/agent/service/tool"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/port/flowresource"
)

var Module = fx.Options(
	fx.Provide(NewEventStreamClient),
	fx.Provide(fx.Annotate(NewAgentFlowResourceSDKClient, fx.ResultTags(`name:"agent_flow_resource_client"`))),
	// dal
	dal.Module,

	// agent
	base.Module,
	data_analyzer.Module,

	// services
	agentFactory.Module,
	agentRunService.Module,
	artifactsService.Module,
	memoryService.Module,
	toolService.Module,
	taskService.Module,
	protocolService.Module,
	reviewService.Module,

	// handlers
	handler.Module,
	fx.Invoke(registerInit),
)

func NewEventStreamClient(config *config.CodeAssistAssistantConfig) eventstreamservice.Client {
	return eventstreamservice.MustNewClient(
		config.EventStreamClientConfig.PSM,
	)
}

func NewAgentFlowResourceSDKClient(tccConfig *config.CodeAssistTCCConfig) *flowresource.SDKClient {
	return flowresource.NewFlowResourceSDKClient(nil, tccConfig.FlowResourceConfig.GetPointer().FileFlowResourceConfig)
}

func registerInit(lc fx.Lifecycle, config *config.CodeAssistAssistantConfig) {
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			jobschedule.MustInit(config.JobSchedulerConfig.PSM)
			return nil
		},
	})
}
