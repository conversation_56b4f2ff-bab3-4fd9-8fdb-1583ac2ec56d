package entity

import "time"

type AgentEventType string // 事件类型

const (
	AgentEventTypeAgentProgress     AgentEventType = "agent_progress"
	AgentEventTypeAgentStep         AgentEventType = "agent_step"
	AgentEventTypeAgentMessageDelta AgentEventType = "agent_message_delta"
	AgentEventTypeAgentMessage      AgentEventType = "agent_message"
	AgentEventTypeAgentToolCall     AgentEventType = "agent_tool_call"
)

type AgentEvent struct {
	EventType AgentEventType `json:"event_type"` // 事件类型
	Timestamp time.Time      `json:"timestamp"`  // 时间戳
	RunID     string         `json:"run_id"`     // 一次agent运行id

	AgentProgressEvent     *AgentProgressEvent     `json:"agent_progress_event,omitempty"`      // agent执行进度事件
	AgentStepEvent         *AgentStepEvent         `json:"agent_step_event,omitempty"`          // agent执行步骤事件
	AgentMessageDeltaEvent *AgentMessageDeltaEvent `json:"agent_message_delta_event,omitempty"` // agent数据事件
	AgentMessageEvent      *AgentMessageEvent      `json:"agent_message_event,omitempty"`       // agent消息事件
	AgentToolCallEvent     *AgentToolCallEvent     `json:"agent_tool_call_event,omitempty"`     // agent tool调用事件
}

// AgentProgressEvent agent进度 事件
type AgentProgressEvent struct {
	Status       AgentStatus `json:"status"`        // 运行状态
	ErrorMessage string      `json:"error_message"` // 失败时的错误信息
}

type AgentStatus string // 运行状态

const (
	AgentStatusCreated     AgentStatus = "started"      // step创建
	AgentStatusCompleted   AgentStatus = "completed"    // 完成
	AgentStatusFailed      AgentStatus = "failed"       // 失败
	AgentStatusToolRequire AgentStatus = "tool_require" // 保留，需要tool调用
)

// AgentStepEvent agent执行步骤事件
type AgentStepEvent struct {
	Status       AgentStepStatus `json:"status"`        // step状态
	Actor        string          `json:"actor"`         // step执行者
	ActorIndex   int             `json:"actor_index"`   // 第几个执行者
	Round        int             `json:"round"`         // 标记 step 在 actor 第几轮
	ErrorMessage string          `json:"error_message"` // step失败时的错误信息
}

type AgentStepStatus string // step状态

const (
	AgentStepStatusStarted   AgentStepStatus = "started"   // step
	AgentStepStatusCompleted AgentStepStatus = "completed" // 完成
)

// AgentMessageDeltaEvent agent数据事件
type AgentMessageDeltaEvent struct {
	Actor string `json:"actor"` // actor

	Type     AgentDeltaType            `json:"type"`
	MetaData AgentMessageDeltaMetaData `json:"meta_data"`
	Content  string                    `json:"content"`
}

type AgentMessageDeltaMetaData struct {
	InvokeName InvokeName `json:"invoke_name"`
	ParamName  string     `json:"param_name"`
	InvokeID   string     `json:"invoke_id"`
}

type InvokeName string

const (
	InvokeNameDataAnalysis InvokeName = "data_analysis"
	InvokeNameEditFile     InvokeName = "edit_file"
	InvokeNameViewFile     InvokeName = "view_file"
	InvokeNameCreateFile   InvokeName = "create_file"
	InvokeNameLS           InvokeName = "ls"
)

const (
	ParamNameDataAnalysisCode = "code_script"

	ParamNameEditFilePath = "path"

	ParamNameViewFilePath = "file_path"

	ParamNameCreateFilePath    = "path"
	ParamNameCreateFileContent = "file_text"

	ParamNameLSPath = "path"
)

type AgentDeltaType string

const (
	AgentDeltaTypeThink    AgentDeltaType = "think"
	AgentDeltaTypeFunction AgentDeltaType = "function"
	AgentDeltaTypeInfo     AgentDeltaType = "info"
)

type AgentMessageEvent struct {
	ID      string `json:"id"`
	Actor   string `json:"actor"`
	Content string `json:"content"` // 原始输出
	//StructContent *AgentMessageStructContent `json:"struct_content,omitempty"` // 结构化content
	Attachments []*AgentMessageAttachment `json:"attachments,omitempty"` // 附件
}

// type AgentMessageStructContent struct {
// 	Think      string         `json:"think"`
// 	Output     string         `json:"output"`
// 	ToolName   string         `json:"tool_name"`
// 	Parameters map[string]any `json:"parameters"`
// }

// AgentToolCallEvent agent tool调用事件
type AgentToolCallEvent struct {
	InvokeID    string              `json:"invoke_id"`   // 单次执行的 id
	InvokeName  InvokeName          `json:"invoke_name"` // 工具名称
	Description string              `json:"description"` // 工具描述
	Status      AgentToolCallStatus `json:"status"`      // 工具调用状态
	Inputs      map[string]string   `json:"inputs"`      // 工具调用原始输入
	Outputs     any                 `json:"outputs"`     // 工具调用原始输出
	OutputsStr  string              `json:"outputs_str"` // 给到模型的工具输出，用于历史恢复
	Error       string              `json:"error"`       // 工具调用错误信息
}

type AgentMessageAttachment struct {
	RawContent []byte `json:"raw_content"`
	Name       string `json:"name"` // 文件名
}

type AgentToolCallStatus string

const (
	ToolCallStatusCompleted AgentToolCallStatus = "completed"
	ToolCallStatusFailed    AgentToolCallStatus = "failed"
)
