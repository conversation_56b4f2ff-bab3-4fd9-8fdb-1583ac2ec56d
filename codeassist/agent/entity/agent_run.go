package entity

import "time"

type AgentRunStatus int

const (
	AgentRunStatusCreated   AgentRunStatus = 1
	AgentRunStatusReady     AgentRunStatus = 2
	AgentRunStatusRunning   AgentRunStatus = 3
	AgentRunStatusCompleted AgentRunStatus = 4
)

func (s AgentRunStatus) String() string {
	switch s {
	case AgentRunStatusCreated:
		return "created"
	case AgentRunStatusReady:
		return "ready"
	case AgentRunStatusRunning:
		return "running"
	case AgentRunStatusCompleted:
		return "completed"
	default:
		return "unknown"
	}
}

type AgentRun struct {
	UUID           string         `json:"uuid"`
	TaskID         string         `json:"task_id"`
	UserID         int64          `json:"user_id"`
	ConversationID string         `json:"conversation"`
	AgentID        string         `json:"agent_id"`
	AgentVersion   string         `json:"agent_run_version"`
	AgentMeta      *AgentRunMeta  `json:"agent_meta"`
	AgentInput     *AgentRunInput `json:"agent_input"`
	Status         AgentRunStatus `json:"status"`
	WorkspaceDir   string         `json:"workspace_dir"`
	CreatedAt      time.Time      `json:"created_at"`
}

type AgentRunMeta struct {
	Config string `json:"config"`
}

type AgentRunInput struct {
}
