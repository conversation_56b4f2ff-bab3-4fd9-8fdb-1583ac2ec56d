package entity

type BizParam struct {
	Source     string   `json:"source,omitempty"`
	Text       string   `json:"text,omitempty"`
	Texts      []string `json:"texts,omitempty"`
	Prompt     string   `json:"prompt,omitempty"`
	Response   string   `json:"response,omitempty"`
	ImageURL   string   `json:"image_url,omitempty"`
	URI        string   `json:"uri,omitempty"`
	ImageURLs  []string `json:"image_urls,omitempty"`
	BotID      string   `json:"bot_id,omitempty"`
	ChatID     string   `json:"chat_id,omitempty"`
	BotType    string   `json:"bot_type,omitempty"`
	ModelID    string   `json:"model_id,omitempty"`
	URL        string   `json:"url,omitempty"`
	URLDecoded string   `json:"url_decoded,omitempty"`
}
