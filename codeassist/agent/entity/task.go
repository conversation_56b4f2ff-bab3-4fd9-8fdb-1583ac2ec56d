package entity

type TaskStatus string
type TaskUpstream string
type TaskStage string

const (
	TaskStatusUnknown      TaskStatus = "unknown"
	TaskStatusCreated      TaskStatus = "created"
	TaskStatusInitializing TaskStatus = "initializing"
	TaskStatusRunning      TaskStatus = "running"
	TaskStatusRecovering   TaskStatus = "recovering"
	TaskStatusSucceeded    TaskStatus = "succeeded"
	TaskStatusFailed       TaskStatus = "failed"
)

const (
	TaskUpstreamDoubao TaskUpstream = "doubao"
)

const (
	TaskStageUnknown     TaskStage = "unknown"
	TaskStageBegin       TaskStage = "begin"
	TaskStageSync        TaskStage = "sync"
	TaskStageAsync       TaskStage = "async"
	TaskStageInterrupted TaskStage = "interrupted"
)

type Task struct {
	UUID           string       `json:"uuid"`
	UserID         string       `json:"user_id"`
	ConversationID string       `json:"conversation_id"`
	MessageID      string       `json:"message_id"`
	Upstream       TaskUpstream `json:"upstream"`
	Status         TaskStatus   `json:"status"`
	Stage          TaskStage    `json:"stage"`
}
