package entity

type MessageUnitRole string

const (
	MessageUnitRoleSystem    MessageUnitRole = "system"
	MessageUnitRoleUser      MessageUnitRole = "user"
	MessageUnitRoleAssistant MessageUnitRole = "assistant"
	MessageUnitRoleTool      MessageUnitRole = "tool"
)

type MessageUnit struct {
	Role    MessageUnitRole `json:"role"`
	Content string          `json:"content"`
	Actor   string          `json:"actor"`
}

type AgentMessage struct {
	SystemPrompt    *MessageUnit
	ChatHistory     []*MessageUnit
	ContextPrompt   *MessageUnit
	UserQuery       *MessageUnit
	AgentTrajectory []*MessageUnit
}

type AgentRunHistoryMessage struct {
	ChatHistory     []*MessageUnit
	UserQuery       *MessageUnit
	AgentTrajectory []*MessageUnit
	WorkspaceInfo   *WorkspaceInfo
}

type WorkspaceInfo struct {
	WorkspaceDirectory string
	UserFiles          []string
	WorkspaceFiles     []string
}
