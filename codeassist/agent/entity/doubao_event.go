package entity

import (
	"encoding/json"

	"code.byted.org/flow/alice_protocol/chat_block"
	"code.byted.org/flow/samantha_nova/pkg/client/pb_gen/event"

	"code.byted.org/devgpt/kiwis/lib/slidingwindow"
)

type BlockType string

const (
	BlockTypeUndefined    BlockType = "undefined"
	BlockTypeText         BlockType = "text"
	BlockTypeDataAnalysis BlockType = "data_analysis"
	BlockTypeFileView     BlockType = "file_view"
	BlockTypeFileCreate   BlockType = "file_create"
	BlockTypeFileEdit     BlockType = "file_edit"
	BlockTypeFileList     BlockType = "file_list"
)

type DoubaoEventContext struct {
	UserID             int64     // User ID
	MessageID          string    // Message ID
	AnswerID           string    // Answer ID
	ConversationID     string    // Conversion ID
	TaskID             string    // Task ID
	TaskStage          TaskStage // Task stage
	AgentRunID         string    // AgentRun ID
	Actor              string    // 当前Actor，恢复时需要
	CardBlockID        string    // 记录卡片BlockID，恢复时需要
	CardBeginTimestamp int64     // 记录当前卡片启动时间
	CardHasError       bool      // 记录当前卡片是否报错
	CurEventID         int64     // 记录EventID，单调递增，每次变化都不必要持久化，可以通过flowpc_chat_eventstream.GetCurrentShot恢复

	AgentRunStepCtx *AgentRunStepContext
}

type AgentRunStepContext struct {
	AgentRunStepID   string // AgentRunStep ID
	TextBlock        *BlockContext
	ToolBlocks       map[string]*BlockContext
	InvalidInvokeIDs map[string]struct{}
}

type BlockContext struct {
	BlockID       string    // 当前BlockID
	ParentBlockID string    // 当前Block 的parentBlock
	BlockType     BlockType // 当前Block类型
	SlidingWindow *slidingwindow.SlidingWindow
	Finished      bool
}

type DoubaoEvent struct {
	BlockWrappers []*BlockWrapper
	TaskStage     TaskStage
	NeedSplitSend bool
}

func (e *DoubaoEvent) HasEvent() bool {
	if e == nil || len(e.BlockWrappers) == 0 {
		return false
	}
	return true
}

func (e *DoubaoEvent) BuildStringList() []string {
	if e == nil || len(e.BlockWrappers) == 0 {
		return nil
	}
	if !e.NeedSplitSend {
		return nil
	}

	var result []string
	for _, wrapper := range e.BlockWrappers {
		block := wrapper.BuildMessage()
		if block == nil {
			continue
		}
		blockList := &event.BlockList{
			Blocks: []*event.Message{block},
		}
		s, _ := json.Marshal(blockList)
		result = append(result, string(s))
	}

	return result
}

func (e *DoubaoEvent) BuildString() string {
	if e == nil || len(e.BlockWrappers) == 0 {
		return ""
	}
	if e.NeedSplitSend {
		return ""
	}

	var blocks []*event.Message
	for _, wrapper := range e.BlockWrappers {
		block := wrapper.BuildMessage()
		if block == nil {
			continue
		}
		blocks = append(blocks, block)
	}
	if len(blocks) == 0 {
		return ""
	}
	blockList := &event.BlockList{
		Blocks: blocks,
	}
	s, _ := json.Marshal(blockList)
	return string(s)
}

type BlockWrapper struct {
	Block        *event.Message
	BlockContent *BlockContent `json:"-"`
}

func (w *BlockWrapper) BuildMessage() *event.Message {
	if w == nil || w.Block == nil {
		return nil
	}
	blockContentStr := w.BlockContent.BuildString()
	if blockContentStr == "" {
		return nil
	}
	w.Block.Content = blockContentStr
	return w.Block
}

type BlockContent struct {
	TextBlock              *chat_block.TextBlock
	CodeAssistProcessBlock *chat_block.CodeAssistProcessBlock
	ResearchProcessBlock   *chat_block.ResearchProcessBlock
	CodeBlock              *chat_block.CodeBlock
	FileOperationBlock     *chat_block.FileOperationBlock
	ArtifactBlock          *chat_block.ArtifactBlock
	ArtifactCodeFileBlock  *chat_block.ArtifactCodeFileBlock
	FileBlock              *chat_block.FileBlock
}

func (c *BlockContent) BuildString() string {
	if c == nil {
		return ""
	}
	if c.TextBlock != nil {
		s, _ := json.Marshal(c.TextBlock)
		return string(s)
	}
	if c.CodeAssistProcessBlock != nil {
		s, _ := json.Marshal(c.CodeAssistProcessBlock)
		return string(s)
	}
	if c.ResearchProcessBlock != nil {
		s, _ := json.Marshal(c.ResearchProcessBlock)
		return string(s)
	}
	if c.CodeBlock != nil {
		s, _ := json.Marshal(c.CodeBlock)
		return string(s)
	}
	if c.FileOperationBlock != nil {
		s, _ := json.Marshal(c.FileOperationBlock)
		return string(s)
	}
	if c.ArtifactBlock != nil {
		s, _ := json.Marshal(c.ArtifactBlock)
		return string(s)
	}
	if c.ArtifactCodeFileBlock != nil {
		s, _ := json.Marshal(c.ArtifactCodeFileBlock)
		return string(s)
	}
	if c.FileBlock != nil {
		s, _ := json.Marshal(c.FileBlock)
		return string(s)
	}
	return ""
}
