package app

import (
	"context"

	"github.com/pkg/errors"
	"github.com/samber/lo"

	"code.byted.org/devgpt/kiwis/codeassist/app/entity"
	"code.byted.org/devgpt/kiwis/codeassist/app/service"
	chatentity "code.byted.org/devgpt/kiwis/codeassist/chat/entity"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/conv"
	"code.byted.org/devgpt/kiwis/lib/tcc"
	"code.byted.org/devgpt/kiwis/port/llmops"
)

var _ service.AppService = &Service{}

type Service struct {
	AppConfig    *tcc.GenericConfig[config.CodeAssistAppsConfig]
	LLMOpsClient llmops.Client
}

func (s *Service) ListApps(ctx context.Context) ([]*entity.App, error) {
	//TODO implement me
	panic("implement me")
}

// GetAppByName returns the app by name and version, it will return the default version App if version is empty.
func (s *Service) GetAppByName(ctx context.Context, name string, appABConfig map[string]any) (*entity.App, error) {
	if s.AppConfig.GetPointer() == nil || len(s.AppConfig.GetPointer().Apps) == 0 {
		return nil, errors.Errorf("app config is empty")
	}
	abConfig, err := conv.MapToStructByJSONTag[ABConfig](appABConfig)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to parse ab config")
	}
	version := abConfig.AppVersion
	if version != "" {
		appConfig, ok := lo.Find(s.AppConfig.GetPointer().Apps, func(item *config.CodeAssistAppConfig) bool {
			return item.Name == name && item.Version == version
		})
		if ok {
			app, err := s.getAppFromConfig(ctx, appConfig, &abConfig)
			if err != nil {
				return nil, err
			}
			return replaceAppConfigWithAbConfig(app, &abConfig)
		}
	}
	appConfig, ok := lo.Find(s.AppConfig.GetPointer().Apps, func(item *config.CodeAssistAppConfig) bool {
		return item.Name == name && (item.Version == "" || item.Version == service.DefaultAppVersion)
	})
	if !ok {
		return nil, service.ErrAppNotFound
	}
	app, err := s.getAppFromConfig(ctx, appConfig, &abConfig)
	if err != nil {
		return nil, err
	}
	return replaceAppConfigWithAbConfig(app, &abConfig)
}

func (s *Service) getAppFromConfig(ctx context.Context, appConfig *config.CodeAssistAppConfig, abConfig *ABConfig) (*entity.App, error) {
	if appConfig == nil {
		return nil, service.ErrAppNotFound
	}
	if len(appConfig.Handlers) == 0 {
		return nil, service.ErrHandlerNotFound
	}
	handlers := make([]*entity.HandlerConfig, 0, len(appConfig.Handlers))
	for _, handlerConfig := range appConfig.Handlers {
		handler, err := s.getHandlerFromConfig(ctx, appConfig.LLMOpsAppKey, handlerConfig, abConfig)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to get handler from config")
		}
		handlers = append(handlers, handler)
	}
	return &entity.App{
		ID:                appConfig.ID,
		Name:              appConfig.Name,
		Version:           appConfig.Version,
		Models:            appConfig.Models,
		Handlers:          handlers,
		DisableSuggest:    appConfig.DisableSuggest,
		RateLimitWindows:  appConfig.RateLimitWindows,
		RateLimitQuota:    appConfig.RateLimitQuota,
		CKGRetrieveParams: appConfig.CKGRetrieveParams,
	}, nil
}

func (s *Service) getHandlerFromConfig(
	ctx context.Context, llmOpsAppKey string, handlerConfig config.CodeAssistHandlerConfig, abConfig *ABConfig,
) (*entity.HandlerConfig, error) {
	handlerType := entity.HandlerType(handlerConfig.Type)
	switch handlerType {
	case entity.HandlerTypePipeline:
		// image generation pipeline 无需获取后续的prompt key，直接return
		if handlerConfig.ID == chatentity.PipelineHandlerImageGenerationPipeline.String() {
			return &entity.HandlerConfig{
				ID:   handlerConfig.ID,
				Type: entity.HandlerType(handlerConfig.Type),
			}, nil
		}

		var (
			answerPrompt  *entity.PromptConfig
			suggestPrompt *entity.PromptConfig
			aiFixPrompt   *entity.PromptConfig
			err           error
		)

		pipelineConfig := handlerConfig.Pipeline
		// answer prompt 配置
		var (
			answerPromptKey   = pipelineConfig.AnswerPrompt.PromptKey
			answerPromptLabel = pipelineConfig.AnswerPrompt.PromptLabel
		)
		// answer prompt 配置 从 ab 参数中获取 prompt 配置替换
		if abConfig != nil && abConfig.PromptConfig != nil {
			if abConfig.PromptConfig.AnswerPromptKey != "" {
				answerPromptKey = abConfig.PromptConfig.AnswerPromptKey
			}
			if abConfig.PromptConfig.AnswerPromptLabel != "" {
				answerPromptLabel = abConfig.PromptConfig.AnswerPromptLabel
			}
		}
		answerPrompt, err = s.getPromptConfig(ctx, llmOpsAppKey, answerPromptKey, answerPromptLabel)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to get answer prompt messages")
		}

		// suggest prompt 配置
		suggestPrompt, err = s.getPromptConfig(ctx, llmOpsAppKey,
			pipelineConfig.SuggestPrompt.PromptKey, pipelineConfig.SuggestPrompt.PromptLabel)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to get answer prompt messages")
		}

		// ai fix prompt 配置
		if pipelineConfig.AIFixPrompt != nil && pipelineConfig.AIFixPrompt.PromptKey != "" {
			aiFixPrompt, err = s.getPromptConfig(ctx, llmOpsAppKey,
				pipelineConfig.AIFixPrompt.PromptKey, pipelineConfig.AIFixPrompt.PromptLabel)
			if err != nil {
				return nil, errors.WithMessage(err, "failed to get answer prompt messages")
			}
		}

		return &entity.HandlerConfig{
			ID:   handlerConfig.ID,
			Type: handlerType,
			Pipeline: entity.PipelineConfig{
				AnswerPrompt:  answerPrompt,
				SuggestPrompt: suggestPrompt,
				AIFixPrompt:   aiFixPrompt,
			},
		}, nil
	case entity.HandlerTypeAgent:
		return &entity.HandlerConfig{
			ID:   handlerConfig.ID,
			Type: handlerType,
		}, nil
	default:
		return nil, errors.WithMessage(nil, "unknown handler type")

	}
}

func (s *Service) getPromptConfig(ctx context.Context, llmOpsAppKey, promptKey, label string) (*entity.PromptConfig, error) {
	promptRaw, err := s.LLMOpsClient.GetPromptTemplatesWithLabel(ctx, llmOpsAppKey, promptKey, label)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get prompt")
	}

	prompt, err := entity.GetPromptMessages(promptRaw.Prompt)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get prompt messages")
	}
	promptConfig := &entity.PromptConfig{
		Name:    promptRaw.Name,
		Label:   promptRaw.Label,
		Prompt:  prompt,
		Version: promptRaw.Version,
	}
	return promptConfig, nil
}
