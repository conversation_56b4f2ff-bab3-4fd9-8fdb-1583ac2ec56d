package entity

import (
	"code.byted.org/devgpt/kiwis/lib/config"
)

type App struct {
	ID                string                        `json:"id"`
	Name              string                        `json:"name"`
	Version           string                        `json:"version"`
	Models            config.CodeAssistModelsConfig `json:"models"`
	Handlers          []*HandlerConfig              `json:"handlers"`
	DisableSuggest    bool                          `json:"disable_suggest"`
	RateLimitWindows  int                           `json:"rate_limit_windows"` // 限流窗口,单位s
	RateLimitQuota    int                           `json:"rate_limit_quota"`   // 限流窗口大小
	CKGRetrieveParams *config.CKGRetrieveParams     `json:"ckg_retrieve_params,omitempty"`
}

func (a *App) GetName() string {
	return a.Name
}

// GetAppHandler 目前一个app chat只支持一种handler模式，所以直接取第一个
func (a *App) GetAppHandler() *HandlerConfig {
	if len(a.Handlers) == 0 {
		return nil
	}
	return a.Handlers[0]
}

const (
	AppNameProjectAsk        = "project_ask" // 项目问答
	AppNameDirectAsk         = "direct_ask"  // 研发自由问答
	AppNameArtifacts         = "artifacts"   // artifacts问答
	AppNameImageGeneration   = "image_generation"
	AppNameDataAnalysisAgent = "data_analyze_agent"
)
