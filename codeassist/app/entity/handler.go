package entity

import (
	"encoding/json"
	"strconv"

	"github.com/pkg/errors"

	"code.byted.org/devgpt/kiwis/port/llmops"
)

type HandlerType string

const (
	HandlerTypePipeline HandlerType = "pipeline"
	HandlerTypeAgent    HandlerType = "agent"
)

type HandlerConfig struct {
	ID       string         `json:"id"`
	Type     HandlerType    `json:"type"`
	Pipeline PipelineConfig `json:"pipeline"`
}

type PipelineConfig struct {
	AnswerPrompt  *PromptConfig `json:"answer_prompt"`
	SuggestPrompt *PromptConfig `json:"suggest_prompt"`
	AIFixPrompt   *PromptConfig `json:"ai_fix_prompt"`
}

type PromptConfig struct {
	Name    string           `json:"name"`
	Label   string           `json:"label"`
	Prompt  []*PromptMessage `json:"prompt"`
	Version string           `json:"version"`
}

type PromptMessage struct {
	Role     string       `json:"role"`
	Content  string       `json:"content"`
	MetaData *MessageMeta `json:"metadata"`
}

type MessageMeta struct {
	MaxToken           int    `json:"max_token"`
	Priority           int    `json:"priority"`
	FileCountThreshold int    `json:"file_count_threshold"` // 最大文件数量阈值，超过阈值则该 resolver 采取不同的召回策略
	Name               string `json:"name"`
}

func (m *MessageMeta) UnmarshalJSON(data []byte) error {
	type inner struct {
		MaxToken           string `json:"max_token"`
		Priority           string `json:"priority"`
		Name               string `json:"name"`
		FileCountThreshold string `json:"file_count_threshold"`
	}

	i := new(inner)
	err := json.Unmarshal(data, i)
	if err != nil {
		return err
	}

	m.Name = i.Name

	if i.MaxToken != "" {
		maxToken, err := strconv.Atoi(i.MaxToken)
		if err != nil {
			return err
		}
		m.MaxToken = maxToken
	}

	if i.Priority != "" {
		priority, err := strconv.Atoi(i.Priority)
		if err != nil {
			return err
		}
		m.Priority = priority
	}
	if i.FileCountThreshold != "" {
		fileCountThreshold, err := strconv.Atoi(i.FileCountThreshold)
		if err != nil {
			return err
		}
		m.FileCountThreshold = fileCountThreshold
	}
	return nil
}

func (c *PipelineConfig) GetPromptMessageByName(name string) *PromptMessage {
	if c == nil || c.AnswerPrompt == nil {
		return nil
	}
	for _, template := range c.AnswerPrompt.Prompt {
		if template.MetaData == nil {
			continue
		}
		if template.MetaData.Name == name {
			return template
		}
	}
	return nil
}

func GetPromptMessages(messages []*llmops.Message) ([]*PromptMessage, error) {
	promptMessages := make([]*PromptMessage, 0)
	for _, message := range messages {
		if message.MetaData == nil || *message.MetaData == "" {
			return nil, errors.New("invalid template metadata, it cannot be empty")
		}
		var metadata MessageMeta
		if err := json.Unmarshal([]byte(*message.MetaData), &metadata); err != nil {
			return nil, errors.WithMessagef(err, "failed to unmarshal template metadata: %s", *message.MetaData)
		}
		promptMessages = append(promptMessages, &PromptMessage{
			Role:    message.Role,
			Content: message.Content,
			MetaData: &MessageMeta{
				MaxToken:           metadata.MaxToken,
				Priority:           metadata.Priority,
				FileCountThreshold: metadata.FileCountThreshold,
				Name:               metadata.Name,
			},
		})
	}
	return promptMessages, nil
}
