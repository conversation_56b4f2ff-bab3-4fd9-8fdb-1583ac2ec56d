package dal

import (
	"context"
	"time"

	"code.byted.org/devgpt/kiwis/codeassist/sandbox/entity"
)

type SandboxDAO interface {
	GetAllocatedInstanceNum(ctx context.Context, allocationID string, functionType entity.FunctionType, sandboxType entity.SandboxType) (int, error)
	GetShareInstanceBySessionNum(ctx context.Context, allocationID string, sessionNum int, functionType int) (string, string, error)
	GetSandboxByIdentifier(
		ctx context.Context, sandboxIdentifier entity.SandboxIdentifier, opt *GetSandboxOption,
	) (*entity.Sandbox, error)
	GetFunctionAllocatedInstances(ctx context.Context, functionID string) ([]string, error)
	GetUserFunctionAllSandboxes(ctx context.Context, allocationID string, functionType, sandboxType, status int) ([]*entity.Sandbox, error)
	CreateSandbox(ctx context.Context, sandbox *entity.Sandbox) (*entity.Sandbox, error)
	UpdateSandboxUpdatedTime(ctx context.Context, sandboxIdentifier entity.SandboxIdentifier) error
	DeleteSandboxByID(ctx context.Context, sandboxID string) error
	DeleteInstance(ctx context.Context, instanceName string) error
	GetInstanceUnDeleteSandboxSessionIDs(ctx context.Context, instanceName string) ([]string, error)
	GetNeedRecycleSandboxListByTime(ctx context.Context, fromTime, endTime time.Time) ([]*entity.Sandbox, error)
}

type GetSandboxOption struct {
	Status *entity.SandboxRuntimeStatus
}
