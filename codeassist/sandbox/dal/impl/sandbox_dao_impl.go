package impl

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/pkg/errors"

	"code.byted.org/devgpt/kiwis/codeassist/sandbox/dal"
	"code.byted.org/devgpt/kiwis/codeassist/sandbox/dal/po"
	"code.byted.org/devgpt/kiwis/codeassist/sandbox/entity"
	"code.byted.org/devgpt/kiwis/port/db"
)

type SandboxDAOImpl struct {
	DB db.Client
}

var _ dal.SandboxDAO = &SandboxDAOImpl{}

func sandboxPOToVO(po *po.SandboxPO) *entity.Sandbox {
	if po == nil {
		return nil
	}
	return &entity.Sandbox{
		ID:        po.UniqueID,
		CreatedAt: po.CreatedAt,
		UpdatedAt: po.UpdatedAt,
		SandboxIdentifier: entity.SandboxIdentifier{
			AllocationID: po.AllocationID,
			SessionID:    po.SessionID,
			FunctionType: entity.FunctionType(po.FunctionType),
			SandboxType:  entity.SandboxType(po.SandboxType),
		},
		FunctionID:   po.FunctionID,
		InstanceName: po.InstanceName,
		Status:       entity.SandboxRuntimeStatus(po.Status),
		AliveTime:    po.AliveTime,
	}
}

func (s *SandboxDAOImpl) GetAllocatedInstanceNum(ctx context.Context, allocationID string, functionType entity.FunctionType, sandboxType entity.SandboxType) (int, error) {
	var count int64
	err := s.DB.NewRequest(ctx).
		Model(&po.SandboxPO{}).
		Select("COUNT(DISTINCT instance_name)").
		Where("allocation_id = ? AND function_type = ? AND sandbox_type = ?", allocationID, functionType, sandboxType).
		Count(&count).Error
	if err != nil {
		return 0, err
	}
	return int(count), nil
}

// GetShareInstanceBySessionNum 从共享的实例里分配一个可容纳沙盒的实例
func (s *SandboxDAOImpl) GetShareInstanceBySessionNum(ctx context.Context, allocationID string, sessionNum int, functionType int) (string, string, error) {
	var result []struct {
		FunctionID   string
		InstanceName string
		SessionCount int
	}
	res := s.DB.NewRequest(ctx).Model(&po.SandboxPO{}).
		Where("allocation_id = ?", allocationID).
		Where("sandbox_type = ?", entity.SandboxTypeShare).
		Where("function_type = ?", functionType).
		Select("function_id, instance_name, COUNT(*) AS session_count").
		Group("instance_name").
		Having("COUNT(*) < ?", sessionNum).
		Scan(&result)
	if err := res.Error; err != nil {
		if db.IsRecordNotFoundError(err) {
			return "", "", nil
		}
		return "", "", errors.WithMessage(err, "failed to get allocated instance")
	}
	if len(result) == 0 {
		return "", "", nil
	}
	return result[0].FunctionID, result[0].InstanceName, nil
}

func (s *SandboxDAOImpl) GetSandboxByIdentifier(
	ctx context.Context, sandboxIdentifier entity.SandboxIdentifier, opt *dal.GetSandboxOption,
) (*entity.Sandbox, error) {
	sandboxPO := &po.SandboxPO{}
	query := s.DB.WithWriteDB(ctx).Model(&po.SandboxPO{}).
		Where("allocation_id = ?", sandboxIdentifier.AllocationID).
		Where("function_type = ?", sandboxIdentifier.FunctionType).
		Where("session_id = ?", sandboxIdentifier.SessionID).
		Where("sandbox_type =?", sandboxIdentifier.SandboxType)
	if opt != nil {
		if opt.Status != nil {
			query.Where("status = ?", int8(entity.SandboxRuntimeStatusRunning))
		}
	}
	if err := query.First(sandboxPO).Error; err != nil {
		if db.IsRecordNotFoundError(err) {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get instance detail: %w", err)
	}
	return sandboxPOToVO(sandboxPO), nil
}

func (s *SandboxDAOImpl) GetFunctionAllocatedInstances(ctx context.Context, functionID string) ([]string, error) {
	var result []string
	res := s.DB.NewRequest(ctx).Model(&po.SandboxPO{}).
		Select("instance_name").
		Where("function_id = ?", functionID).
		Where("status = ?", int8(entity.SandboxRuntimeStatusRunning)).Find(&result)
	if err := res.Error; err != nil {
		if db.IsRecordNotFoundError(err) {
			return []string{}, nil
		}
		return nil, err
	}
	return result, nil
}

func (s *SandboxDAOImpl) GetUserFunctionAllSandboxes(ctx context.Context, allocationID string, functionType, sandboxType, status int) ([]*entity.Sandbox, error) {
	var resultsPO []*po.SandboxPO
	res := s.DB.NewRequest(ctx).Model(&po.SandboxPO{}).
		Where("allocation_id = ?", allocationID).
		Where("function_type = ?", functionType).
		Where("sandbox_type = ?", sandboxType).
		Where("status = ?", status).
		Find(&resultsPO)

	if err := res.Error; err != nil {
		if db.IsRecordNotFoundError(err) {
			return []*entity.Sandbox{}, nil
		}
		return nil, errors.WithMessage(err, "failed to get user allocated instances")
	}

	var resultsVO []*entity.Sandbox
	for _, v := range resultsPO {
		resultsVO = append(resultsVO, sandboxPOToVO(v))
	}

	return resultsVO, nil
}

func (s *SandboxDAOImpl) CreateSandbox(ctx context.Context, sandbox *entity.Sandbox) (*entity.Sandbox, error) {
	sandboxID := sandbox.ID
	if sandboxID == "" {
		sandboxID = uuid.NewString()
	}
	sandboxPO := &po.SandboxPO{
		UniqueID:     sandboxID,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
		FunctionID:   sandbox.FunctionID,
		InstanceName: sandbox.InstanceName,
		SessionID:    sandbox.SessionID,
		FunctionType: int8(sandbox.FunctionType),
		SandboxType:  int8(sandbox.SandboxType),
		AllocationID: sandbox.AllocationID,
		Status:       int8(sandbox.Status),
		AliveTime:    sandbox.AliveTime,
	}
	res := s.DB.NewRequest(ctx).Create(sandboxPO)
	if err := res.Error; err != nil {
		return nil, err
	}
	// todo @niligang createtime 会不会赋值上
	return sandboxPOToVO(sandboxPO), nil
}

func (s *SandboxDAOImpl) UpdateSandboxUpdatedTime(ctx context.Context, sandboxIdentifier entity.SandboxIdentifier) error {
	query := s.DB.NewRequest(ctx).Model(&po.SandboxPO{}).
		Where("allocation_id = ?", sandboxIdentifier.AllocationID).
		Where("function_type = ?", sandboxIdentifier.FunctionType).
		Where("session_id = ?", sandboxIdentifier.SessionID).
		Where("sandbox_type =?", sandboxIdentifier.SandboxType)
	if err := query.Update("updated_at", time.Now()).Error; err != nil {
		return fmt.Errorf("failed to update instance updated_at: %w", err)
	}
	return nil
}

func (s *SandboxDAOImpl) DeleteSandboxByID(ctx context.Context, sandboxID string) error {
	query := s.DB.NewRequest(ctx).Model(&po.SandboxPO{}).
		Where("unique_id = ?", sandboxID)
	res := query.Updates(map[string]interface{}{
		"deleted_at": time.Now().UnixNano(),
		"status":     int8(entity.SandboxRuntimeStatusStopped),
	})
	if err := res.Error; err != nil {
		return err
	}
	return nil
}

func (s *SandboxDAOImpl) DeleteInstance(ctx context.Context, instanceName string) error {
	res := s.DB.NewRequest(ctx).Model(&po.SandboxPO{}).
		Where("instance_name = ?", instanceName).Updates(map[string]interface{}{
		"deleted_at": time.Now().UnixNano(),
		"status":     int8(entity.SandboxRuntimeStatusStopped),
	})
	if err := res.Error; err != nil {
		if db.IsRecordNotFoundError(err) {
			return nil
		}
		return err
	}
	return nil
}

func (s *SandboxDAOImpl) GetInstanceUnDeleteSandboxSessionIDs(ctx context.Context, instanceName string) ([]string, error) {
	var sessionIDs []string
	err := s.DB.NewRequest(ctx).Model(&po.SandboxPO{}).
		Where("instance_name = ?", instanceName).
		Where("deleted_at = 0").
		Pluck("session_id", &sessionIDs).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get session ids for instance %s: %w", instanceName, err)
	}
	return sessionIDs, nil
}

func (s *SandboxDAOImpl) GetNeedRecycleSandboxListByTime(ctx context.Context, fromTime, endTime time.Time) ([]*entity.Sandbox, error) {
	var sandboxesPO []*po.SandboxPO
	res := s.DB.NewRequest(ctx).
		Where("status = ?", int8(entity.SandboxRuntimeStatusRunning)).
		Where("updated_at < ?", endTime).
		Where("updated_at > ?", fromTime).
		Find(&sandboxesPO)
	if err := res.Error; err != nil {
		if db.IsRecordNotFoundError(err) {
			return []*entity.Sandbox{}, nil
		}
		return nil, errors.WithMessage(err, "failed to get containers")
	}
	// PO -> VO 转换
	sandboxesVO := make([]*entity.Sandbox, 0, len(sandboxesPO))
	for _, sandboxPO := range sandboxesPO {
		sandboxesVO = append(sandboxesVO, sandboxPOToVO(sandboxPO))
	}
	return sandboxesVO, nil
}
