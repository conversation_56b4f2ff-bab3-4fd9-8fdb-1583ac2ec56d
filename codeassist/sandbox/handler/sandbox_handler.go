package handler

import (
	"context"

	"github.com/pkg/errors"
	"github.com/spf13/cast"

	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/codeassist"
	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/common"
	"code.byted.org/devgpt/kiwis/codeassist/sandbox/entity"
	sandboxservice "code.byted.org/devgpt/kiwis/codeassist/sandbox/service"
	"code.byted.org/devgpt/kiwis/lib/kitex"
	"code.byted.org/devgpt/kiwis/lib/util"
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/gopkg/logs/v2/log"
)

// ProcessDataCreateSandbox 创建处理数据的实例
func (h *SandboxHandler) ProcessDataCreateSandbox(ctx context.Context, req *codeassist.CreateSandboxRequest) (*codeassist.CreateSandboxResponse, error) {
	// 目前只允许创建functionType为TypeProcessData和TypeDeepResearch的实例
	if req.GetSandboxInentifier().FunctionType != codeassist.SandboxFunctionType_TypeProcessData &&
		req.GetSandboxInentifier().FunctionType != codeassist.SandboxFunctionType_TypeDeepResearch {
		return nil, kitex.NewKitexError(common.ErrorCode_ErrParamInvalid, errors.New("param invalid"))
	}
	logs.V1.CtxInfo(ctx, "[CreateSandbox] request: %s", util.MarshalStruct(req))
	sandboxIdentifier := req.GetSandboxInentifier()
	if sandboxIdentifier == nil {
		return nil, kitex.NewKitexError(common.ErrorCode_ErrParamInvalid, errors.New("param invalid"))
	}

	sandbox, err := h.SandboxService.CreateSandbox(ctx, &sandboxservice.CreateSandboxOpt{
		AllocationID: sandboxIdentifier.AllocationID,
		SessionID:    sandboxIdentifier.SessionID,
		FunctionType: entity.FunctionType(sandboxIdentifier.FunctionType),
		SandboxType:  entity.SandboxType(sandboxIdentifier.SandboxType),
		Extension:    false,
		AliveTime:    req.AliveTime,
	})
	if err != nil {
		log.V1.CtxError(ctx, "[CreateProcessDataInstance] create instance err, err is %+v", err)
		return &codeassist.CreateSandboxResponse{}, kitex.NewKitexError(common.ErrorCode_ErrInternal, err)
	}
	return &codeassist.CreateSandboxResponse{
		SandboxInentifier: &codeassist.SandboxInentifier{
			AllocationID: sandboxIdentifier.AllocationID,
			SessionID:    sandboxIdentifier.SessionID,
			FunctionType: codeassist.SandboxFunctionType(sandbox.FunctionType),
			SandboxType:  codeassist.SandboxType(sandbox.SandboxType),
		},
	}, nil
}

// ProcessDataReleaseSandbox 释放处理数据的实例
func (h *SandboxHandler) ProcessDataReleaseSandbox(ctx context.Context, req *codeassist.ReleaseSandboxRequest) (*codeassist.ReleaseSandboxResponse, error) {
	if req.GetSandboxIdentifier().FunctionType != codeassist.SandboxFunctionType_TypeProcessData &&
		req.GetSandboxIdentifier().FunctionType != codeassist.SandboxFunctionType_TypeDeepResearch {
		return nil, kitex.NewKitexError(common.ErrorCode_ErrParamInvalid, errors.New("param invalid"))
	}
	logs.V1.CtxInfo(ctx, "[ProcessDataReleaseSandbox] request: %s", util.MarshalStruct(req))
	sandboxIdentifier := req.GetSandboxIdentifier()
	if sandboxIdentifier == nil {
		return nil, kitex.NewKitexError(common.ErrorCode_ErrParamInvalid, errors.New("param invalid"))
	}

	err := h.SandboxService.ClearSandboxOrInstance(ctx, entity.SandboxIdentifier{
		AllocationID: sandboxIdentifier.AllocationID,
		SessionID:    sandboxIdentifier.SessionID,
		FunctionType: entity.FunctionType(sandboxIdentifier.FunctionType),
		SandboxType:  entity.SandboxType(sandboxIdentifier.SandboxType),
	})
	if err != nil {
		return nil, kitex.NewKitexError(common.ErrorCode_ErrInternal, err)
	}

	return nil, nil
}

// ProcessDataExecuteCode 执行代码
func (h *SandboxHandler) ProcessDataExecuteCode(ctx context.Context, req *codeassist.ExecuteCodeRequest) (*codeassist.ExecuteCodeResponse, error) {
	var (
		shareContext = req.ExecuteDetails.ShareContext != nil && *req.ExecuteDetails.ShareContext
	)

	logs.V1.CtxInfo(ctx, "[ProcessDataExecuteCode] request: %s", util.MarshalStruct(req))
	sandboxIdentifier := req.GetSandboxInentifier()
	if sandboxIdentifier == nil {
		return nil, kitex.NewKitexError(common.ErrorCode_ErrParamInvalid, errors.New("param invalid"))
	}

	// 先根据沙盒类型设置cwd，但如果输入不为空，以输入为准
	cwd := "/mnt"
	if sandboxIdentifier.SandboxType == codeassist.SandboxType_SandboxTypeShare {
		cwd = "/mnt/" + req.SandboxInentifier.SessionID
	}

	// 如果有指定CWD且不为空，使用指定的CWD
	if req.ExecuteDetails != nil && req.ExecuteDetails.CWD != nil && *req.ExecuteDetails.CWD != "" {
		cwd = *req.ExecuteDetails.CWD
	}

	codeRes, err := h.SandboxService.ExecuteCode(ctx, entity.SandboxIdentifier{
		AllocationID: sandboxIdentifier.AllocationID,
		SessionID:    sandboxIdentifier.SessionID,
		FunctionType: entity.FunctionType(sandboxIdentifier.FunctionType),
		SandboxType:  entity.SandboxType(sandboxIdentifier.SandboxType),
	}, &sandboxservice.ExecuteOpt{
		ExecuteDetail: &sandboxservice.ExecuteDetail{
			Code:         req.ExecuteDetails.Code,
			Language:     req.ExecuteDetails.Language,
			FetchFiles:   req.ExecuteDetails.FetchFiles,
			LinkPrefix:   &req.ExecuteDetails.LinkPrefix,
			ShareContext: shareContext,
			CWD:          &cwd,
		},
	})
	if err != nil {
		log.V1.CtxError(ctx, "[ProcessDataRunCode] run code err, err is %+v", err)
		return &codeassist.ExecuteCodeResponse{}, sandboxservice.ErrorToKiteXError(err)
	}
	var (
		compileResult []*codeassist.ExecuteCommandResult_
		runResult     []*codeassist.ExecuteCommandResult_
	)

	if len(codeRes.CompileResult) != 0 {
		for _, v := range codeRes.CompileResult {
			if v != nil {
				compileResult = append(compileResult, &codeassist.ExecuteCommandResult_{
					Status:        v.Status,
					ExecutionTime: v.ExecutionTime,
					ReturnCode:    cast.ToInt32(v.ReturnCode),
					Type:          string(v.Type),
					Content:       v.Content,
				})
			}
		}
	}

	if len(codeRes.RunResult) != 0 {
		for _, v := range codeRes.RunResult {
			if v != nil {
				runResult = append(runResult, &codeassist.ExecuteCommandResult_{
					Status:        v.Status,
					ExecutionTime: v.ExecutionTime,
					ReturnCode:    cast.ToInt32(v.ReturnCode),
					Type:          string(v.Type),
					Content:       v.Content,
				})
			}
		}
	}

	return &codeassist.ExecuteCodeResponse{
		Status:          codeRes.Status,
		Message:         codeRes.Message,
		CompileResult_:  compileResult,
		RunResult_:      runResult,
		ExecutorPodName: codeRes.ExecutorPodName,
		Files:           codeRes.Files,
		ErrorCode:       &codeRes.ErrorCode,
		ErrorMessage:    &codeRes.ErrorMessage,
		RequestID:       &codeRes.RequestID,
	}, nil
}

func (h *SandboxHandler) ProcessDataUploadFile(ctx context.Context, req *codeassist.UploadFileRequest) (*codeassist.UploadFileResponse, error) {
	logs.V1.CtxInfo(ctx, "[ProcessDataUploadFile] request: %s", util.MarshalStruct(req))
	sandboxIdentifier := req.GetSandboxInentifier()
	if sandboxIdentifier == nil {
		return nil, kitex.NewKitexError(common.ErrorCode_ErrParamInvalid, errors.New("param invalid"))
	}

	fullFilePath, err := h.SandboxService.CreateFile(ctx, entity.SandboxIdentifier{
		AllocationID: sandboxIdentifier.AllocationID,
		SessionID:    sandboxIdentifier.SessionID,
		FunctionType: entity.FunctionType(sandboxIdentifier.FunctionType),
		SandboxType:  entity.SandboxType(sandboxIdentifier.SandboxType),
	}, &sandboxservice.CreateFileOpt{
		Path:    req.FilePath,
		Content: req.FileContent,
		WorkDir: req.WorkDir,
	})
	if err != nil {
		log.V1.CtxError(ctx, "[ProcessDataUploadFiles] upload files err, err is %+v", err)
		return &codeassist.UploadFileResponse{}, sandboxservice.ErrorToKiteXError(err)
	}
	return &codeassist.UploadFileResponse{
		Path: fullFilePath.Path,
	}, nil
}

func (h *SandboxHandler) ProcessDataDownloadFile(ctx context.Context, req *codeassist.DownloadFileRequest) (*codeassist.DownloadFileResponse, error) {
	logs.V1.CtxInfo(ctx, "[ProcessDataDownloadFile] request: %s", util.MarshalStruct(req))
	sandboxIdentifier := req.GetSandboxInentifier()
	if sandboxIdentifier == nil {
		return nil, kitex.NewKitexError(common.ErrorCode_ErrParamInvalid, errors.New("param invalid"))
	}

	retFile, err := h.SandboxService.DownloadFile(ctx, entity.SandboxIdentifier{
		AllocationID: sandboxIdentifier.AllocationID,
		SessionID:    sandboxIdentifier.SessionID,
		FunctionType: entity.FunctionType(sandboxIdentifier.FunctionType),
		SandboxType:  entity.SandboxType(sandboxIdentifier.SandboxType),
	}, &sandboxservice.DownloadFileOpt{
		Path:    req.FilePath,
		WorkDir: req.WorkDir,
	})
	if err != nil {
		log.V1.CtxError(ctx, "[ProcessDataDownloadFiles] download files err, err is %+v", err)
		return &codeassist.DownloadFileResponse{}, sandboxservice.ErrorToKiteXError(err)
	}
	return &codeassist.DownloadFileResponse{
		FilePath:    retFile.Path,
		FileContent: retFile.Content,
	}, nil
}
