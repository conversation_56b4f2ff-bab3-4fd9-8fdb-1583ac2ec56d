package entity

import (
	"time"

	appentity "code.byted.org/devgpt/kiwis/codeassist/app/entity"
	chatentity "code.byted.org/devgpt/kiwis/codeassist/chat/entity"
	resolverentity "code.byted.org/devgpt/kiwis/codeassist/resolver/entity"
	resolverservice "code.byted.org/devgpt/kiwis/codeassist/resolver/service"
)

// 埋点上报的 Span.
const (
	SpanLLMChat      = "codeassist_llm_chat"
	SpanLLMContext   = "codeassist_llm_context"
	SpanLLMAnswer    = "codeassist_llm_answer"
	SpanLLMIntention = "codeassist_llm_intention"
	SpanLLMSuggest   = "codeassist_llm_suggest"

	SpanTask          = "codeassist_task"
	SpanAgent         = "codeassist_agent"
	SpanAgentRound    = "codeassist_agent_round"
	SpanAgentThink    = "codeassist_agent_think"
	SpanAgentToolCall = "codeassist_agent_tool_call"
)

// RequestMetadata 记录模型请求的元信息.
type RequestMetadata struct {
	// Model params:
	Name            string  `json:"model_name"`
	Stream          bool    `json:"stream"`
	MaxTokens       int     `json:"max_tokens"`
	Temperature     float32 `json:"temperature"`
	TopP            float32 `json:"top_p"`
	TopK            int     `json:"top_k"`
	MaxPromptTokens int     `json:"max_prompt_tokens"`

	// Response:
	Usage             *Usage        `json:"usage"`
	Latency           time.Duration `json:"latency"`
	FirstTokenLatency time.Duration `json:"first_token_latency"`
	LogID             string        `json:"log_id"`

	// GlobalContextVariables records the context variables of the request.
	GlobalContextVariables map[string]any                                   `json:"global_variables"`
	PromptTemplate         *PromptTemplate                                  `json:"prompt_template"`
	CodeCurrentEditor      *resolverservice.CodeCurrentEditorResolverOption `json:"code_current_editor"`
	CodeUserMessage        *CodeUserMessage                                 `json:"code_user_message"`
	FileEntity             *resolverservice.FileEntityResolverOption        `json:"file_entity"`
}

// Usage 模型 token 使用情况.
type Usage struct {
	PromptTokens     int            `json:"prompt_tokens"`
	CompletionTokens int            `json:"completion_tokens"`
	TotalTokens      int            `json:"total_tokens"`
	HistoryTokens    int            `json:"history_tokens"`
	ResolverTokens   map[string]int `json:"resolver_tokens"`
}

type PromptTemplate struct {
	PromptVersion string                     `json:"prompt_version"`
	Messages      []*appentity.PromptMessage `json:"messages"`
}

type CodeUserMessage struct {
	*resolverservice.CodeUserMessageResolverOption
	RepoName   string `json:"repo_name"`
	FromGitHub bool   `json:"from_github"`
	Ref        string `json:"ref"`
}

// ContextVariables 拼接 PE 之前的上下文变量.
type ContextVariables struct {
	// GlobalContextVariables records the context variables of the request.
	GlobalContextVariables map[string]any               `json:"global_variables"`
	Resolvers              []*ResolverResult            `json:"resolvers"`
	CanvasVariables        *chatentity.ContextVariables `json:"canvas_variables"`
}

type ResolverResult struct {
	ResolverID     resolverentity.ResolverID `json:"resolver_id"`
	OriginTokens   map[string]int            `json:"origin_tokens"`    // 裁剪前 resolver 占用的 token 数量
	Tokens         map[string]int            `json:"tokens"`           // 裁剪后 resolver 占用的 token 数量
	KeepEntityNum  int                       `json:"keep_entity_num"`  // 裁剪后保留的 entity 数量
	TotalEntityNum int                       `json:"total_entity_num"` // 总 entity 数量
	IsCropped      bool                      `json:"is_cropped"`       // 是否裁剪过
	Variables      map[string]any            `json:"variables"`        // resolver 输出的变量
	References     map[string]any            `json:"references"`       // resolver 输出的引用
}
