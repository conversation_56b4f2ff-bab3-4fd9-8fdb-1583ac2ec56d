package defaultcontext

import (
	"context"
	"encoding/json"
	"math/rand"
	"path"
	"strconv"
	"time"

	"github.com/pkg/errors"
	poolsdk "github.com/sourcegraph/conc/pool"

	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/knowledgebase"
	"code.byted.org/devgpt/kiwis/codeassist/common/constant"
	"code.byted.org/devgpt/kiwis/codeassist/context/dal"
	"code.byted.org/devgpt/kiwis/codeassist/context/entity"
	"code.byted.org/devgpt/kiwis/codeassist/context/pack"
	"code.byted.org/devgpt/kiwis/codeassist/context/service"
	"code.byted.org/devgpt/kiwis/lib/metrics"
	"code.byted.org/devgpt/kiwis/port/dgit"
	"code.byted.org/devgpt/kiwis/port/github"
	eventbus "code.byted.org/eventbus/client-go"
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/lang/gg/gptr"
)

const (
	GitDefaultBranch  = "HEAD"
	redisMaxRetries   = 400
	redisWaitInterval = time.Second
	redisTTL          = time.Minute * 30
)

// SniffRepositoryLink 尝试识别并解析给定的仓库链接。
//
// 该函数接收一个字符串形式的链接，并尝试识别它是否为有效的代码仓库链接。
// 目前支持的仓库类型仅为 GitHub。函数会解析链接并提取出仓库的相关信息，同时触发对仓库的索引。
//
// @Param opt: 包含待识别的仓库链接字符串的结构体。
//   - Link: 待识别的仓库链接字符串。
//   - UserID: 用户id。
//
// @Return *entity.RepositoryEntity: 如果成功识别并解析链接，返回包含仓库信息的 RepositoryEntity 对象。
// @Return error: 如果链接无效或无法识别，返回相应的错误。如果成功解析，则为 nil。
//
// 可能的错误:
//   - 如果提供的链接为空，返回 "empty link" 错误。
//   - 如果链接格式无效或不是支持的仓库类型，返回 "invalid link" 错误。
//   - 如果在解析过程中遇到其他问题，将返回相应的错误信息。
func (s *ContextServiceImpl) SniffRepositoryLink(ctx context.Context, opt service.SniffRepositoryLinkOption) (result *service.SniffRepositoryLinkResult, err error) {
	reportContextIndexStepMetricsFunc := metrics.CodeAssistMetric.ReportContextIndexStepMetrics()
	defer func() {
		var isError bool
		if err != nil {
			isError = true
		}
		reportContextIndexStepMetricsFunc(string(constant.ContextResourceTypeRepository), metrics.RepositorySniffLinkStep, isError)
	}()

	start := time.Now()
	repoSupportSizeLimitKB := s.TccConfig.RepositoryRestrictionConfig.GetValue().SizeLimitKB

	repoName, repoLink, err := s.GitHubClient.ParseGetGithubUrl(opt.Link)
	if err != nil {
		// link is not GitHub when err is not nil
		return &service.SniffRepositoryLinkResult{
			RepositoryEntity: nil,
			SupportIndex:     false,
			NotSupportReason: entity.SniffRepositoryNotSupportReasonNotGitHubLink,
			SizeLimitKB:      repoSupportSizeLimitKB,
		}, nil
	}

	// check if repo is exceeding limit in cache
	if opt.UpdateMode == entity.RepositoryUpdateModeNormal {
		if repositoryInfoCache := s.RepositoryInfoCacheService.GetRepositoryInfoCache(ctx, repoName); repositoryInfoCache != nil {
			if repositoryInfoCache.Size > repoSupportSizeLimitKB {
				return &service.SniffRepositoryLinkResult{
					RepositoryEntity: repositoryInfoCache.RepositoryInfoCacheToRepositoryEntity(),
					SupportIndex:     false,
					NotSupportReason: entity.SniffRepositoryNotSupportReasonSizeOverLimit,
					SizeLimitKB:      repoSupportSizeLimitKB,
				}, nil
			}
		}
	}

	// try to get default branch name from DGit.
	var defaultBranch string
	var githubRepo *github.RespRepo
	if defaultBranch, err = s.DGitClient.FindRepositoryDefaultBranchName(ctx, repoName); err != nil {
		// error happened, get default branch failed, get from GitHub
		logs.V1.CtxWarn(ctx, "failed to find default branch of repo name: %s with err: %v", repoName, err)

		githubRepo, err = s.getRepoInfoFromGitHub(ctx, repoName)
		if err != nil {
			return nil, err
		}
		if githubRepo == nil {
			// repo cannot found in GitHub or private repo
			return &service.SniffRepositoryLinkResult{
				RepositoryEntity: nil,
				SupportIndex:     false,
				NotSupportReason: entity.SniffRepositoryNotSupportReasonNotPublicRepository,
				SizeLimitKB:      repoSupportSizeLimitKB,
			}, nil
		}

		// no default branch
		if githubRepo.DefaultBranch != nil {
			defaultBranch = *githubRepo.DefaultBranch
		}
	} else {
		logs.V1.CtxInfo(ctx, "get default branch of repo name: %s with default branch: %s", repoName, defaultBranch)
	}

	// get repo entity from db
	var repoEntity *entity.RepositoryEntity
	repoEntity, err = s.RepositoryEntityDAO.GetByNameAndRef(ctx, repoName, defaultBranch)
	if err != nil {
		logs.V1.CtxWarn(ctx, "fail to get repository entity by name: %s, ref: %s, err: %+v", repoName, defaultBranch, err)
		return nil, err
	}

	// repo entity not exist, create it
	if repoEntity == nil {
		// get repo info from DGit, but repo not exist, still need to get repo info from GitHub
		if githubRepo == nil {
			githubRepo, err = s.getRepoInfoFromGitHub(ctx, repoName)
			if err != nil {
				return nil, err
			}
			if githubRepo == nil {
				// repo cannot found in GitHub or private repo
				return &service.SniffRepositoryLinkResult{
					RepositoryEntity: nil,
					SupportIndex:     false,
					NotSupportReason: entity.SniffRepositoryNotSupportReasonNotPublicRepository,
					SizeLimitKB:      repoSupportSizeLimitKB,
				}, nil
			}
		}

		if githubRepo.DefaultBranch != nil && *githubRepo.DefaultBranch != defaultBranch {
			logs.V1.CtxWarn(ctx, "default branch from DGit %s is not equals to GitHub %s", defaultBranch, *githubRepo.DefaultBranch)
			defaultBranch = *githubRepo.DefaultBranch
		}

		repoEntity = &entity.RepositoryEntity{
			Name:        repoName,
			Link:        repoLink,
			Ref:         defaultBranch,
			Source:      entity.ContextSourceGitHub,
			UpdateTimes: 0,
		}

		if githubRepo.Language != nil {
			repoEntity.Language = *githubRepo.Language
		}
		if githubRepo.Description != nil {
			repoEntity.Description = *githubRepo.Description
		}
		if githubRepo.Size != nil {
			repoEntity.Size = *githubRepo.Size
		}
		if githubRepo.StargazersCount != nil {
			repoEntity.StargazersCount = *githubRepo.StargazersCount
		}

		if opt.UpdateMode == entity.RepositoryUpdateModeNormal {
			repoEntity.IndexSource = entity.ContextIndexSourceFromUser
		} else {
			repoEntity.IndexSource = entity.ContextIndexSourceFromPlatformPreIndex
		}

		// try to use standard repo name
		if githubRepo.HTMLURL != nil {
			standardRepoName, standardRepoLink, parseErr := s.GitHubClient.ParseGetGithubUrl(*githubRepo.HTMLURL)
			if parseErr == nil {
				repoEntity.Name = standardRepoName
				repoEntity.Link = standardRepoLink
			} else {
				logs.V1.CtxError(ctx, "parse repo name and link from githubRepo.HTMLURL %s err %v", *githubRepo.HTMLURL, parseErr)
			}
		}

		// check if repo is exceeding limit
		if opt.UpdateMode == entity.RepositoryUpdateModeNormal && githubRepo.Size != nil && *githubRepo.Size > repoSupportSizeLimitKB {
			repositoryInfoCache := repoEntity.RepositoryEntityToRepositoryInfoCache()
			err = s.RepositoryInfoCacheService.SetRepositoryInfoCache(ctx, repoName, repositoryInfoCache)
			if err != nil {
				logs.V1.CtxError(ctx, "failed to record repo %s unsupported by exceeds limit, err: %v", repoName, err)
			}
			return &service.SniffRepositoryLinkResult{
				RepositoryEntity: repoEntity,
				SupportIndex:     false,
				NotSupportReason: entity.SniffRepositoryNotSupportReasonSizeOverLimit,
				SizeLimitKB:      repoSupportSizeLimitKB,
			}, nil
		}

		logs.V1.CtxInfo(ctx, "create repo entity name: %s with default branch: %s", repoName, defaultBranch)

		// create repo entity idempotent
		var repoID string
		repoID, err = s.RepositoryEntityDAO.CreateIgnoreDuplicate(ctx, repoEntity)
		if err != nil {
			return nil, errors.WithMessagef(err, "fail to create repository entity for repo %+v", repoEntity)
		}

		// repo is created in another sniff process, now just get it
		if repoID == "" {
			repoEntity, err = s.RepositoryEntityDAO.GetByNameAndRef(ctx, repoEntity.Name, defaultBranch)
			if err != nil {
				return nil, err
			}
			if repoEntity == nil {
				return nil, errors.New("failed to find repo by name and default branch")
			}
		}
	}

	datasetID := s.TccConfig.KnowledgeBaseDatasetConfig.GetValue().RepositoryDatasetID

	// create context and send analysis repo task
	_, err = s.CreateRepositoryContext(ctx, service.CreateRepositoryContextOption{
		CreatorID: opt.UserID,
		RepoID:    repoEntity.UniqueID,
		RepoName:  repoEntity.Name,
		RepoLink:  repoEntity.Link,
		RepoRef:   repoEntity.Ref,
		RepoSize:  repoEntity.Size,
		EntryTime: start,
		DatasetID: datasetID,
	})
	if err != nil {
		return nil, err
	}

	return &service.SniffRepositoryLinkResult{
		RepositoryEntity: repoEntity,
		SupportIndex:     true,
		NotSupportReason: entity.SniffRepositoryNotSupportReasonNoReason,
		SizeLimitKB:      repoSupportSizeLimitKB,
	}, nil
}

func (s *ContextServiceImpl) getRepoInfoFromGitHub(ctx context.Context, repoName string) (*github.RespRepo, error) {
	// if repo not exist in DGit or db, create new repo
	owner, name, err := s.splitOwnerAndNameFromRepoName(repoName) // transfer full repoName to owner/name
	if err != nil {
		logs.V1.CtxWarn(ctx, "failed to split repo name from repo name: %s with err: %v", repoName, err)
		// if full repoName is not valid, interrupt the process and return err
		return nil, service.ParamInvalidError
	}

	// if default branch is not found, try to find default branch from GitHub
	token, orgName, err := s.randomGetOrgAccessToken(ctx)
	if err != nil {
		return nil, errors.WithMessagef(err, "fail to get access token for org, org is %+v, err is %+v", orgName, err)
	}
	githubRepo, err := s.GitHubClient.GetRepository(ctx, token.GetAccessToken(), orgName, owner, name)
	if err != nil {
		if err.Error() == github.ErrCodeNotFound.MessageFormat {
			logs.V1.CtxInfo(ctx, "repo %s not found", repoName)
			return nil, nil
		}
		return nil, err
	}

	if githubRepo.Size != nil && githubRepo.HTMLURL != nil && githubRepo.DefaultBranch != nil {
		logs.V1.CtxInfo(ctx, "get repo %s info from GitHub: size %d, url %s, default branch %s", repoName, *githubRepo.Size, *githubRepo.HTMLURL, *githubRepo.DefaultBranch)
	} else {
		logs.V1.CtxWarn(ctx, "some field of repo %s info lost", repoName)
	}
	return githubRepo, nil
}

// 获取access token，用org身份访问
func (s *ContextServiceImpl) randomGetOrgAccessToken(ctx context.Context) (*AccessToken, string, error) {
	var orgs = s.OrgInstallations
	if orgs == nil || len(orgs.OrgNames) == 0 {
		return nil, "", errors.New("have no org name to get access token")
	}
	// 随机取
	orgName := orgs.OrgNames[rand.Intn(len(orgs.OrgNames))]
	installID := orgs.OrgInstallationIDs[orgName]
	tokenRes, err := s.getAndAutoRefreshTokenByInstallID(ctx, installID)
	if err != nil {
		logs.CtxWarn(ctx, "randomGetOrgAccessToken get org %s(installID:%d) access token failed, err:%+v", orgName, installID, err)
		return nil, "", err
	}
	logs.CtxInfo(ctx, "randomGetOrgAccessToken get org %s(installID:%d) access token success", orgName, installID)
	return tokenRes, orgName, nil
}

func (s *ContextServiceImpl) getAndAutoRefreshTokenByInstallID(ctx context.Context, installID int64) (tokenRes *AccessToken, err error) {
	// 当token不存在时，tokenRes为nil
	tokenRes, err = s.loadTokenFromRedis(ctx, "", installID)
	if err != nil {
		logs.CtxInfo(ctx, "getAndAutoRefreshTokenByInstallID[:2]: installID = %+v, err = %v", installID, err)
		return nil, err
	}
	expired := tokenRes.checkAccessTokenExpired()
	if expired {
		token, expireAt, err := s.GitHubClient.GetAccessTokenByInstallationID(ctx, installID)
		if err != nil {
			logs.CtxInfo(ctx, "getAndAutoRefreshTokenByInstallID[:9]: installID = %+v, err = %v", installID, err)
			return nil, err
		}
		tokenRes = &AccessToken{AccessToken: token, AccessExpiredAt: expireAt.UnixMilli(), OrgInstallToken: gptr.Of(true)}
		_, err = s.setTokenToRedis(ctx, "", installID, tokenRes, nil)
		if err != nil {
			logs.CtxInfo(ctx, "getAndAutoRefreshTokenByInstallID[:15]: installID = %+v, err = %v", installID, err)
			return nil, err
		}
	}
	tokenRes.OrgInstallToken = gptr.Of(true)
	return tokenRes, nil
}

func (s *ContextServiceImpl) CreateRepositoryContext(ctx context.Context, opt service.CreateRepositoryContextOption) (contextMeta *entity.Context, err error) {
	// add latency and throughput metrics
	reportContextIndexStepMetricsFunc := metrics.CodeAssistMetric.ReportContextIndexStepMetrics()
	defer func() {
		reportContextIndexStepMetricsFunc(string(constant.ContextResourceTypeRepository), metrics.ContextCreateStep, err != nil)
	}()

	// defer func() {
	// 	if contextMeta != nil {
	// 		// log context relation of repo, ignore err
	// 		s.logContextRelationOfRepo(ctx, contextMeta.UniqueID, contextMeta.EntityID, opt.CreatorID)
	// 	}
	// }()

	contextMeta, err = s.ContextMetaDAO.GetByEntityIDAndDatasetID(ctx, opt.RepoID, opt.DatasetID)
	if err != nil {
		return nil, err
	}

	logs.V1.CtxInfo(ctx, "contextMeta: %+v", contextMeta)

	if contextMeta != nil {
		logs.V1.CtxInfo(ctx, "context already exists, id: %s", contextMeta.UniqueID)

		if contextMeta.Status == entity.ContextStatusSuccess {
			// If repo index success, try to trigger update repo and quick return repo
			s.TriggerRepositoryUpdate(ctx, service.TriggerRepositoryUpdateOption{
				RepoID:    opt.RepoID,
				UserID:    opt.CreatorID,
				EntryTime: opt.EntryTime,
			})
		} else if contextMeta.IndexFailed() || contextMeta.ExceedMaxIndexLatency() {
			logs.V1.CtxWarn(ctx, "context analysis failed or exceed max index latency, recreate task. repo name: %v, repo id: %v, status: %v", opt.RepoName, opt.RepoID, contextMeta.Status)

			if contextMeta.Status != entity.ContextStatusJustCreated {
				err = s.ContextMetaDAO.UpdateStatus(ctx, contextMeta.UniqueID, entity.ContextStatusJustCreated, entity.ContextIndexProcessCreated)
				if err != nil && !errors.Is(err, dal.UpdateNothingError) {
					return nil, err
				}
			}
			err = s.sendRepositoryCreateTask(ctx, *contextMeta, opt)
			if err != nil {
				return nil, err
			}
		}
		return contextMeta, nil
	}

	datasourceCreateReq := &knowledgebase.CreateDatasourceRequest{
		DatasetID: opt.DatasetID,
		Name:      opt.RepoName,
	}

	datasourceCreateResp, err := s.KnowledgeBaseClient.CreateDatasource(ctx, datasourceCreateReq)
	if err != nil {
		return nil, err
	}
	datasourceID := datasourceCreateResp.Datasource.ID

	logs.V1.CtxInfo(ctx, "create repo context with datasource id %d", datasourceID)
	contextMeta = &entity.Context{
		DatasetID:    opt.DatasetID,
		DatasourceID: datasourceID,
		Name:         opt.RepoName,
		Type:         constant.ContextResourceTypeRepository,
		EntityID:     opt.RepoID,
		ResourceKey:  opt.RepoID,
		Status:       entity.ContextStatusJustCreated,
		Progress:     entity.ContextIndexProcessCreated,

		ContextExtension: &entity.ContextExtension{
			Size: int64(opt.RepoSize) * 1024,
		},
	}
	contextID, err := s.ContextMetaDAO.CreateIgnoreDuplicate(ctx, contextMeta)
	if err != nil {
		return nil, err
	}
	if contextID == "" {
		contextMeta, err = s.ContextMetaDAO.GetByEntityID(ctx, opt.RepoID)
		if err != nil {
			return nil, err
		}
		if contextMeta == nil {
			return nil, errors.New("failed to find context with id")
		}

		return contextMeta, nil
	}

	err = s.sendRepositoryCreateTask(ctx, *contextMeta, opt)
	if err != nil {
		return nil, err
	}
	return contextMeta, nil
}

func (s *ContextServiceImpl) sendRepositoryCreateTask(ctx context.Context, contextMeta entity.Context, opt service.CreateRepositoryContextOption) error {
	task := entity.AnalysisTask{
		Context:      contextMeta,
		UserID:       opt.CreatorID,
		RepoLink:     opt.RepoLink,
		RepoName:     opt.RepoName,
		TaskType:     entity.TaskTypeCreate,
		QueueingTime: time.Now(),
		EntryTime:    opt.EntryTime,
	}

	value, err := json.Marshal(task)
	if err != nil {
		return err
	}
	taskEvent := eventbus.NewProducerEventBuilder().
		WithEventName(s.Config.ContextEventBusConfig.EventName).
		WithValue(value).Build()
	return s.EventbusClient.Publish(ctx, taskEvent)
}

func repoNameKey(repoName string) string {
	return "dgit_repo_create@" + repoName
}

func (s *ContextServiceImpl) waitRepoLock(ctx context.Context, repoName string) error {
	repoKey := repoNameKey(repoName)
	for i := 0; i < redisMaxRetries; i++ {
		acquired, err := s.RedisClient.SetNX(ctx, repoKey, "1", redisTTL)
		if err != nil {
			return errors.WithMessagef(err, "could not acquire lock for %s", repoKey)
		}
		if acquired {
			return nil
		}
		if i%20 == 0 {
			logs.V1.CtxWarn(ctx, "could not acquire lock after %d s", i)
		}
		time.Sleep(redisWaitInterval)
	}
	return errors.Errorf("could not acquire lock after %d s", redisMaxRetries)
}

func (s *ContextServiceImpl) unlockRepo(ctx context.Context, repoName string) error {
	repoKey := repoNameKey(repoName)
	if err := s.RedisClient.Del(ctx, repoKey); err != nil {
		return err
	}
	return nil
}

func (s *ContextServiceImpl) createOrFetchDgitRepo(ctx context.Context, opt service.AnalysisRepoOption) error {
	exists, err := s.DGitClient.RepositoryExists(ctx, opt.RepoName)
	if err != nil {
		logs.V1.CtxError(ctx, "repository not found.err: %v", err)
		daoErr := s.ContextMetaDAO.UpdateStatus(ctx, opt.Context.UniqueID, entity.ContextStatusFailed, entity.ContextIndexProcessIndexingFailed)
		return errors.WithMessagef(err, "check repository exists failed. save err: %v", daoErr)
	}
	if !exists {
		logs.V1.CtxInfo(ctx, "repository %s not exists, need to create repo.", opt.RepoName)
		err := s.DGitClient.CreateRepositoryFromURL(ctx, opt.RepoName, opt.RepoLink)
		if err != nil {
			daoErr := s.ContextMetaDAO.UpdateStatus(ctx, opt.Context.UniqueID, entity.ContextStatusFailed, entity.ContextIndexProcessIndexingFailed)
			return errors.WithMessagef(err, "save repository status failed. save err: %v", daoErr)
		}
	} else {
		logs.V1.CtxInfo(ctx, "repository %s exists, update repo.", opt.RepoName)
		err := s.DGitClient.FetchRepositoryFromRemote(ctx, opt.RepoName, opt.RepoLink)
		if err != nil {
			logs.V1.CtxError(ctx, "failed to fetch repository from remote. err: %v", err)
		}
	}
	return nil
}

// AnalysisRepositoryContext 分析仓库上下文
// 它会检查仓库是否存在，创建必要的数据源和上下文元数据，并初始化一个分析任务。
//
// @param opt: 创建上下文的选项
//   - Context: 仓库上下文
//   - UserID: 用户id
//   - RepoName: repo全名
//   - RepoLink: repo对应的链接
//
// @Return error: 如果过程中发生错误，返回相应的错误；否则为nil
func (s *ContextServiceImpl) AnalysisRepositoryContext(ctx context.Context, opt service.AnalysisRepoOption) (err error) {
	// 检测是否再黑名单中
	if s.IsResourceKeyInBlackList(ctx, &entity.ContextIdentifier{Type: constant.ContextResourceTypeRepository, ResourceKey: opt.Context.ResourceKey}) {
		_ = s.ContextMetaDAO.UpdateStatus(ctx, opt.Context.UniqueID, entity.ContextStatusFailed, entity.ContextIndexProcessIndexingFailed)
		logs.V1.CtxWarn(ctx, "skip case in black list %v", opt)
		return nil
	}

	err = s.ContextMetaDAO.UpdateStatusWithLock(
		ctx,
		opt.Context.UniqueID,
		entity.ContextStatusJustCreated,
		entity.ContextStatusProcessing,
		entity.ContextIndexProcessIndexing,
	)
	if err != nil {
		if errors.Is(err, dal.UpdateNothingError) {
			logs.V1.CtxWarn(ctx, "repository already begin analysis: %v", err)
			return nil
		}
		logs.V1.CtxError(ctx, "repository update context status failed.err: %v", err)
		daoErr := s.ContextMetaDAO.UpdateStatus(ctx, opt.Context.UniqueID, entity.ContextStatusFailed, entity.ContextIndexProcessIndexingFailed)
		return errors.WithMessagef(err, "repository analysis get lock failed. save err: %v", daoErr)
	}

	// 检查Repo Name的合法性，同时解析出repo的仓库名，后续需要将仓库名称添加到路径头部
	_, shortName, err := s.splitOwnerAndNameFromRepoName(opt.RepoName)
	if err != nil {
		logs.V1.CtxError(ctx, "split owner and name from repo name failed. err: %v", err)
		daoErr := s.ContextMetaDAO.UpdateStatus(ctx, opt.Context.UniqueID, entity.ContextStatusFailed, entity.ContextIndexProcessIndexingFailed)
		return errors.WithMessagef(err, "illegal repo name. save err: %v", daoErr)
	}

	if err = s.createOrFetchDgitRepo(ctx, opt); err != nil {
		return errors.WithMessagef(err, "create or fetch dgit repo failed. save err: %v", err)
	}

	commitSHA, err := s.DGitClient.GetCommitIdOfRepository(ctx, opt.RepoName, GitDefaultBranch)
	if err != nil {
		daoErr := s.ContextMetaDAO.UpdateStatus(ctx, opt.Context.UniqueID, entity.ContextStatusFailed, entity.ContextIndexProcessIndexingFailed)
		return errors.WithMessagef(err, "save file status failed.err: %v", daoErr)
	}
	// commit sha为空代表空仓库，直接返回解析成功
	if commitSHA == "" {
		s.SaveContextExtra(ctx, &entity.ContextExtra{
			ContextID:   opt.Context.UniqueID,
			Type:        opt.Context.Type,
			ResourceKey: opt.Context.ResourceKey,
		})

		err = s.ContextMetaDAO.UpdateStatus(ctx, opt.Context.UniqueID, entity.ContextStatusSuccess, entity.ContextIndexProcessSuccess)
		if err != nil {
			return errors.WithMessagef(err, "update context status failed.err: %v", err)
		}
		logs.V1.CtxWarn(ctx, "repository commitSHA is empty.")
		return nil
	}
	logs.V1.CtxInfo(ctx, "repository create, newest commit SHA: %s", commitSHA)

	err = s.RepositoryEntityDAO.UpdateCommitIdByUniqueID(ctx, opt.Context.EntityID, commitSHA)
	if err != nil {
		daoErr := s.ContextMetaDAO.UpdateStatus(ctx, opt.Context.UniqueID, entity.ContextStatusFailed, entity.ContextIndexProcessIndexingFailed)
		return errors.WithMessagef(err, "update repository commit sha failed. dao err: %v", daoErr)
	}

	// step0：获取目录信息
	treeInfo, err := s.getAllRepositoryTree(ctx, opt.RepoName, commitSHA)
	if err != nil {
		daoErr := s.ContextMetaDAO.UpdateStatus(ctx, opt.Context.UniqueID, entity.ContextStatusFailed, entity.ContextIndexProcessIndexingFailed)
		return errors.WithMessagef(err, "repository download tree failed. dao err: %v", daoErr)
	}
	// 空仓库直接返回解析成功
	if treeInfo.IsTreeEmpty() {
		err = s.ContextMetaDAO.UpdateStatus(ctx, opt.Context.UniqueID, entity.ContextStatusSuccess, entity.ContextIndexProcessSuccess)
		if err != nil {
			return errors.WithMessagef(err, "update context status failed.err: %v", err)
		}
		logs.V1.CtxWarn(ctx, "repository tree is empty.")
		return nil
	}

	err = s.ContextMetaDAO.UpdateStatus(ctx, opt.Context.UniqueID, entity.ContextStatusProcessing, entity.ContextIndexProcessTreeDownloaded)
	if err != nil {
		logs.V1.CtxError(ctx, "repository update context status failed. err: %v", err)
	}
	logs.V1.CtxInfo(ctx, "repository download tree finished, repo: %v", opt.RepoName)

	directoryStats := entity.DirectoryNodes(treeInfo.DirNodes).CountDirectoryNodes()
	directoryInfo := &entity.DirectoryInfo{
		FileExtensionCountList: directoryStats.FileExtensionCountList,
		FileCount:              len(treeInfo.FirstLevelFileNodes) + len(treeInfo.RestFileNodes),
		DirectoryCount:         len(treeInfo.DirNodes),
		MaxDirectoryDepth:      directoryStats.MaxDepth,
	}

	// 并行
	analysisParallelPool := poolsdk.New().WithMaxGoroutines(2).WithErrors()
	analysisParallelPool.Go(func() error {
		// step1: 创建目录CKG实体及获取语言
		err := s.analysisTree(ctx, service.AnalysisTreeOption{
			Identifier: entity.ContextIdentifier{
				ResourceKey: opt.Context.EntityID,
				Type:        constant.ContextResourceTypeRepository,
			},
			Context:               opt.Context,
			DirNodes:              treeInfo.DirNodes,
			MaxIndexedFolderIndex: 0,
		})
		if err != nil {
			logs.V1.CtxError(ctx, "repository analysis tree failed. err: %v", err)
		}
		logs.V1.CtxInfo(ctx, "repository analysis tree finished, repo: %v", opt.RepoName)
		return err
	})

	analysisParallelPool.Go(func() error {
		// step2: 第一层文件ckg索引构建
		analysisFirstLevelFilesOption := service.AnalysisFirstLevelFilesOption{
			Context:                  opt.Context,
			FirstLevelFileNodes:      treeInfo.FirstLevelFileNodes,
			DownloadFilesConcurrency: s.FileConcurrencyConfig.GetPointer().GetDGitDownloadFilesConcurrency(),

			DownloadFileFunc: func(fileNode *entity.DirectoryNode) (string, []byte, error) {

				fileEntity, err := s.getDgitFileContentWithRetry(ctx, GetDGitFileContentOption{
					Path:        fileNode.Path,
					RepoName:    opt.RepoName,
					CommitSHA:   commitSHA,
					RetryConfig: s.TccConfig.ContextRetryConfig.GetValue().GetFileContentRetryConfig,
				})
				if err != nil {
					return "", nil, err
				}
				if fileEntity == nil {
					return "", nil, service.ResourceNotFoundError
				}
				kbFileID := path.Join(shortName, fileNode.Path)
				return kbFileID, fileEntity.Content, nil
			},
		}
		err := s.analysisFirstLevelFiles(ctx, analysisFirstLevelFilesOption)
		if err != nil {
			logs.V1.CtxError(ctx, "repository analysis first level tree failed. err: %v", err)
		}
		logs.V1.CtxInfo(ctx, "repository analysis first level tree finished, repo: %v", opt.RepoName)

		directoryStats = entity.DirectoryNodes(analysisFirstLevelFilesOption.FirstLevelFileNodes).CountDirectoryNodes()
		directoryInfo.Merge(directoryStats)

		return err
	})

	err = analysisParallelPool.Wait()
	if err != nil {
		daoErr := s.ContextMetaDAO.UpdateStatus(ctx, opt.Context.UniqueID, entity.ContextStatusFailed, entity.ContextIndexProcessIndexingFailed)
		return errors.WithMessagef(err, "repository analysis tree and first level dir failed. update status with err: %v", daoErr)
	}

	// 第一层遍历完成即任务成功，后台遍历解析后续文件
	err = s.ContextMetaDAO.UpdateStatus(ctx, opt.Context.UniqueID, entity.ContextStatusReady, entity.ContextIndexProcessSuccess)
	if err != nil {
		return errors.WithMessagef(err, "update context status failed err")
	}
	logs.V1.CtxInfo(ctx, "repository create, analysis tree and first level dir finished, repo: %v", opt.RepoName)

	// 打点
	metrics.CodeAssistMetric.ReportContextIndexReadyLatencyMetrics(constant.ContextResourceTypeRepository.String(), opt.EntryTime)

	// 剩余文件遍历分析更新
	analysisRestFilesOption := service.AnalysisRestFilesOption{
		Context:       opt.Context,
		RestFileNodes: treeInfo.RestFileNodes,

		DownloadFileFunc: func(fileNode *entity.DirectoryNode) (string, []byte, error) {
			fileEntity, err := s.getDgitFileContentWithRetry(ctx, GetDGitFileContentOption{
				Path:        fileNode.Path,
				RepoName:    opt.RepoName,
				CommitSHA:   commitSHA,
				RetryConfig: s.TccConfig.ContextRetryConfig.GetValue().GetFileContentRetryConfig,
			})
			if err != nil {
				return "", nil, errors.WithMessagef(err, "get dgit file content failed. repo:%s, path: %s", opt.RepoName, fileNode.Path)
			}
			if fileEntity == nil {
				return "", nil, service.ResourceNotFoundError
			}
			kbFileID := path.Join(shortName, fileNode.Path)
			return kbFileID, fileEntity.Content, nil
		},
	}
	s.analysisRestFiles(ctx, analysisRestFilesOption)

	directoryStats = entity.DirectoryNodes(analysisRestFilesOption.RestFileNodes).CountDirectoryNodes()
	directoryInfo.Merge(directoryStats)
	// 仓库的最大深度需要统计仓库名，所以要+1
	directoryInfo.MaxDirectoryDepth = directoryInfo.MaxDirectoryDepth + 1

	s.SaveContextExtra(ctx, &entity.ContextExtra{
		ContextID:     opt.Context.UniqueID,
		Type:          opt.Context.Type,
		ResourceKey:   opt.Context.ResourceKey,
		DirectoryInfo: directoryInfo,
	})

	if opt.Context != nil && opt.Context.ContextExtension != nil {
		// Size在CreateRepositoryContext中带过来了
		opt.Context.ContextExtension.FileNum = int64(directoryInfo.FileCount)
	}

	err = s.ContextMetaDAO.UpdateStatus(ctx, opt.Context.UniqueID, entity.ContextStatusSuccess, entity.ContextIndexProcessSuccess)
	if err != nil {
		return errors.WithMessagef(err, "update context status failed.err: %v", err)
	}
	logs.V1.CtxInfo(ctx, "repository create, analysis all files finished, repo: %v", opt.RepoName)
	return nil
}

func (s *ContextServiceImpl) getAllRepositoryTree(ctx context.Context, repoName string, commitSHA string) (contextTreeInfo *service.ContextTreeInfo, err error) {
	// add latency and throughput metrics
	reportContextIndexStepMetricsFunc := metrics.CodeAssistMetric.ReportContextIndexStepMetrics()
	defer func() {
		reportContextIndexStepMetricsFunc(string(constant.ContextResourceTypeRepository), metrics.ContextDownloadTreeStep, err != nil)
	}()

	firstLevelFileNodes := make([]*entity.DirectoryNode, 0)
	restFileNodes := make([]*entity.DirectoryNode, 0)
	dirNodes := make([]*entity.DirectoryNode, 0)

	trees, err := s.DGitClient.GetTreeEntriesOfRepositoryRecursively(ctx, repoName, commitSHA, RootPath)
	if err != nil {
		logs.V1.CtxError(ctx, "get tree from dgit failed. err: %v", err)
		return nil, err
	}

	for _, treeNode := range trees {
		if treeNode == nil {
			continue
		}

		node := &entity.DirectoryNode{
			OID:          treeNode.ObjectID,
			Name:         treeNode.Name,
			Type:         pack.DGitDirectoryTypeToDirectoryNodeType(treeNode.Type),
			Path:         treeNode.Path,
			ParentPath:   path.Dir(treeNode.Path),
			FileFetchWay: entity.FileFetchWayRepository,
		}

		if node.Type == entity.DirectoryNodeTypeDirectory {
			dirNodes = append(dirNodes, node)
		} else {
			if node.ParentPath == RootPath {
				firstLevelFileNodes = append(firstLevelFileNodes, node)
			} else {
				restFileNodes = append(restFileNodes, node)
			}
		}
	}

	logs.V1.CtxInfo(ctx, "directory first level file count: %d, rest file count: %d, dir count %d", len(firstLevelFileNodes), len(restFileNodes), len(dirNodes))
	return &service.ContextTreeInfo{
		FirstLevelFileNodes: firstLevelFileNodes,
		RestFileNodes:       restFileNodes,
		DirNodes:            dirNodes,
		MainLanguage:        "",
	}, nil
}

func (s *ContextServiceImpl) UpdateRepository(ctx context.Context, opt service.UpdateRepositoryOption) []*entity.DirectoryNode {
	// 需要将仓库名称添加到路径头部
	_, shortName, err := s.splitOwnerAndNameFromRepoName(opt.RepoName)
	if err != nil {
		logs.V1.CtxError(ctx, "split owner and name from repo name failed. err: %v", err)
		return opt.RemoteDirNodes
	}

	remoteDirNodes := s.DisabledDirectoryService.CheckDirectoryNodeDisabled(ctx,
		service.CheckDirectoryNodeDisabledOption{DirectoryNodeList: opt.RemoteDirNodes})
	deleteNodeList, updateNodeList := getKBRemoteDiffs(opt.KBDirNodes, remoteDirNodes, shortName)
	if len(deleteNodeList) == 0 && len(updateNodeList) == 0 {
		logs.V1.CtxInfo(ctx, "repo %s no file need update", opt.RepoName)
		return remoteDirNodes
	}

	deleteFilesConcurrency := s.FileConcurrencyConfig.GetPointer().GetDeleteFilesConcurrency()
	deletePool := poolsdk.New().WithMaxGoroutines(deleteFilesConcurrency)
	for _, deleteNode := range deleteNodeList {
		filePath := deleteNode.Path
		deletePool.Go(func() {
			defer func() {
				err := recover()
				if err != nil {
					logs.V1.CtxError(ctx, "recover from panic: %v", err)
				}
			}()

			file := &knowledgebase.File{
				DatasetID:    opt.Context.DatasetID,
				DatasourceID: opt.Context.DatasourceID,
				ID:           filePath,
			}
			deleteFilesReq := &knowledgebase.DeleteFilesRequest{
				DatasetID:        opt.Context.DatasetID,
				DatasourceID:     opt.Context.DatasourceID,
				Files:            []*knowledgebase.File{file},
				DeleteDatasource: false,
			}

			err = s.DeleteFilesWithRetry(ctx, deleteFilesReq,
				s.TccConfig.ContextRetryConfig.GetValue().DeleteFilesRetryConfig)
			if err != nil {
				logs.V1.CtxError(ctx, "delete files failed. file: %s, err: %v", filePath, err)
			}
		})
	}
	deletePool.Wait()

	updateFilesConcurrency := s.FileConcurrencyConfig.GetPointer().GetUpdateFilesConcurrency()
	updatePool := poolsdk.New().WithMaxGoroutines(updateFilesConcurrency)
	for _, updateNode := range updateNodeList {
		fileNode := updateNode
		updatePool.Go(func() {
			defer func() {
				err := recover()
				if err != nil {
					logs.V1.CtxError(ctx, "recover from panic: %v", err)
				}
			}()

			rawFileEntity, err := s.getDgitFileContentWithRetry(ctx, GetDGitFileContentOption{
				Path:        fileNode.Path,
				RepoName:    opt.RepoName,
				CommitSHA:   opt.TargetCommitSHA,
				RetryConfig: s.TccConfig.ContextRetryConfig.GetValue().GetFileContentRetryConfig,
			})
			if err != nil || rawFileEntity == nil {
				logs.V1.CtxError(ctx, "get repo file content failed. err: %v", err)
				return
			}
			rawFile := rawFileEntity.Content
			ckgDisabled := s.DisabledDirectoryService.CheckFileContentCKGDisabled(ctx,
				service.CheckFileContentDisabledOption{
					FilePath:    fileNode.Path,
					FileContent: rawFile,
				})
			if ckgDisabled {
				fileNode.IndexResult = entity.ContextIndexResultFilteredByCKGLimit
				return
			}

			// fileNode 从 dgit 获取需要拼接 shortname
			kbFileID := path.Join(shortName, fileNode.Path)
			file := &knowledgebase.File{
				DatasetID:    opt.Context.DatasetID,
				DatasourceID: opt.Context.DatasourceID,
				ID:           kbFileID,
				ContentHash:  fileNode.OID,
				Content:      string(rawFile),
			}
			updateFilesReq := &knowledgebase.UpdateFilesRequest{
				DatasetID:    opt.Context.DatasetID,
				DatasourceID: opt.Context.DatasourceID,
				Files:        []*knowledgebase.File{file},
				Sync:         true,
			}
			_, err = s.UpdateFilesWithRetry(ctx, updateFilesReq,
				constant.ContextResourceTypeRepository,
				constant.ContextStepUpdateFile,
			)
			if err != nil {
				logs.V1.CtxError(ctx, "UpdateRepository: update file to knowledge base failed. filePath: %s, fileSize: %d, err: %v", kbFileID, len(rawFile), err)
			}
		})
	}
	updatePool.Wait()

	logs.V1.CtxInfo(ctx, "update repo: %s complete. delete file count: %d, update file count: %d",
		opt.RepoName, len(deleteNodeList), len(updateNodeList))
	return remoteDirNodes
}

func getKBRemoteDiffs(kbNodes, remoteNodes []*entity.DirectoryNode, repoName string) (onlyInKB, onlyInRemote []*entity.DirectoryNode) {
	kbMap := make(map[string]*entity.DirectoryNode)
	remoteMap := make(map[string]*entity.DirectoryNode)

	for _, node := range kbNodes {
		key := node.OID + node.Path
		kbMap[key] = node
	}

	for _, node := range remoteNodes {
		if node.Disabled {
			continue
		}
		key := node.OID + path.Join(repoName, node.Path)
		remoteMap[key] = node
	}

	// 找出只在KB中的节点, KB 中的path是拼过shortname的
	for key, node := range kbMap {
		if _, exists := remoteMap[key]; !exists {
			onlyInKB = append(onlyInKB, node)
		}
	}

	// 找出只在Remote中的节点
	for key, node := range remoteMap {
		if _, exists := kbMap[key]; !exists {
			onlyInRemote = append(onlyInRemote, node)
		}
	}

	return onlyInKB, onlyInRemote
}

func (s *ContextServiceImpl) analyzeCommitDiffs(ctx context.Context, commitDiffs []*dgit.DiffEntity) (updateNodeList []*entity.DirectoryNode, deleteNodeList []*entity.DirectoryNode) {
	for _, diff := range commitDiffs {
		newNode := &entity.DirectoryNode{
			Name:       path.Base(diff.Path),
			Path:       diff.Path,
			ParentPath: path.Dir(diff.Path),
			Type:       entity.DirectoryNodeTypeFile,
		}

		if diff.GitChangeType == dgit.ChangeTypeDeleted {
			deleteNodeList = append(deleteNodeList, &entity.DirectoryNode{
				Name:       path.Base(diff.OldPath),
				Path:       diff.OldPath,
				ParentPath: path.Dir(diff.OldPath),
				Type:       entity.DirectoryNodeTypeFile,
			})
			continue
		}
		if diff.GitChangeType == dgit.ChangeTypeRenamed {
			renameNode := &entity.DirectoryNode{
				Name:       path.Base(diff.OldPath),
				Path:       diff.OldPath,
				ParentPath: path.Dir(diff.OldPath),
				Type:       entity.DirectoryNodeTypeFile,
			}
			deleteNodeList = append(deleteNodeList, renameNode)
		}
		disabledNode := s.DisabledDirectoryService.CheckDirectoryNodeDisabled(ctx, service.CheckDirectoryNodeDisabledOption{
			DirectoryNodeList: []*entity.DirectoryNode{newNode},
		})
		if len(disabledNode) == 0 || disabledNode[0].Disabled {
			continue
		}
		updateNodeList = append(updateNodeList, newNode)
	}
	return
}

func (s *ContextServiceImpl) TriggerRepositoryUpdate(ctx context.Context, opt service.TriggerRepositoryUpdateOption) {
	repo, err := s.RepositoryEntityDAO.GetByUniqueID(ctx, opt.RepoID)
	if err != nil || repo == nil {
		logs.V1.CtxError(ctx, "failed to find repository by unique id %s: %v", opt.RepoID, err)
		return
	}

	if repo.UpdatedAt.Add(entity.RepositoryUpdateIntervalDuration).After(time.Now()) {
		logs.V1.CtxInfo(ctx, "repository %s is updated recently, skip update", opt.RepoID)
		return
	}

	// get lock to update repository
	err = s.RepositoryEntityDAO.UpdateUpdateCountByUniqueIDWithLock(ctx, opt.RepoID, repo.UpdateTimes, repo.UpdateTimes+1)
	if err != nil {
		logs.V1.CtxWarn(ctx, "failed to get lock to update repository: %v", err)
		return
	}

	datasetID := s.TccConfig.KnowledgeBaseDatasetConfig.GetValue().RepositoryDatasetID
	contextMeta, err := s.ContextMetaDAO.GetByEntityIDAndDatasetID(ctx, opt.RepoID, datasetID)
	if err != nil || contextMeta == nil {
		logs.V1.CtxError(ctx, "failed to get repository by entity id %s and dataset id %d: %v", opt.RepoID, datasetID, err)
		return
	}

	task := entity.AnalysisTask{
		Context:      *contextMeta,
		UserID:       opt.UserID,
		RepoName:     repo.Name,
		RepoLink:     repo.Link,
		TaskType:     entity.TaskTypeUpdate,
		QueueingTime: time.Now(),
		EntryTime:    opt.EntryTime,
	}

	logs.V1.CtxInfo(ctx, "send repository updating task to MQ: %v", task)

	value, err := json.Marshal(task)
	if err != nil {
		logs.V1.CtxError(ctx, "failed to marshal task: %v", err)
		return
	}

	taskEvent := eventbus.NewProducerEventBuilder().
		WithEventName(s.Config.ContextEventBusConfig.EventName).
		WithValue(value).Build()
	err = s.EventbusClient.Publish(ctx, taskEvent)
	//err = s.TaskMQ.SendMessage(ctx, value, "")
	if err != nil {
		logs.V1.CtxError(ctx, "failed to send task to MQ: %v", err)
	}
}

func (s *ContextServiceImpl) logContextRelationOfRepo(ctx context.Context, contextUniqID string, RepoID string, UserID int64) {
	exist := s.LocalCacheService.IsUserIDRepoIDExistInCache(RepoID, UserID)
	if exist {
		logs.V1.CtxInfo(ctx, "user %d with repository %d relation already exist", UserID, RepoID)
		return
	}

	// create context relation ignore duplicate error
	_, err := s.ContextRelationDAO.CreateIgnoreDuplicate(ctx, &entity.ContextRelation{
		ContextID:    contextUniqID,
		RelatedID:    strconv.FormatInt(UserID, 10),
		RelationType: entity.ContextRelationTypeReference,
	})
	if err != nil {
		logs.V1.CtxError(ctx, "failed to create context relation: %v", err)
		return
	}

	err = s.LocalCacheService.SetUserIDRepoIDExistInCache(RepoID, UserID, 6*60*60)
	if err != nil {
		logs.V1.CtxError(ctx, "cache user %d with repository %d err %v", UserID, RepoID, err)
	}
}

func (s *ContextServiceImpl) UpdateRepositoryContext(ctx context.Context, opt service.UpdateRepositoryContextOption) (err error) {
	// 检测是否再黑名单中
	if s.IsResourceKeyInBlackList(ctx, &entity.ContextIdentifier{Type: constant.ContextResourceTypeDirectory, ResourceKey: opt.Context.ResourceKey}) {
		logs.V1.CtxWarn(ctx, "skip case in black list %v", opt)
		return nil
	}

	err = s.DGitClient.FetchRepositoryFromRemote(ctx, opt.RepoName, opt.RepoLink)
	if err != nil {
		// 拉取失败也继续更新
		logs.V1.CtxError(ctx, "failed to fetch repository from remote: %v", err)
	}
	logs.V1.CtxInfo(ctx, "repository update, fetch remote finished. repo: %v", opt.RepoName)

	repo, err := s.RepositoryEntityDAO.GetByUniqueID(ctx, opt.Context.EntityID)
	if err != nil {
		logs.V1.CtxError(ctx, "failed to find repository by unique id %s: %v", opt.Context.EntityID, err)
		return err
	}
	if repo == nil {
		logs.V1.CtxError(ctx, "repository %s not found", opt.Context.EntityID)
		return errors.New("repository not found")
	}

	newCommitSHA, err := s.DGitClient.GetCommitIdOfRepository(ctx, opt.RepoName, repo.Ref)
	if err != nil {
		logs.V1.CtxError(ctx, "failed to get commit id of repository: %v", err)
		return err
	}
	logs.V1.CtxInfo(ctx, "repository update, get commit id. old commit sha: %v, new commit sha: %v", repo.CommitSHA, newCommitSHA)

	indexedFileList, maxIndexedFolderID, err := s.GetIndexedFilesAndMaxID(ctx, opt.Context.DatasetID, opt.Context.DatasourceID)
	if err != nil {
		return errors.WithMessage(err, "failed to get file meta")
	}
	treeInfo, err := s.getAllRepositoryTree(ctx, opt.RepoName, newCommitSHA)
	if err != nil {
		logs.V1.CtxError(ctx, "failed to get tree of repository: %v", err)
		return err
	}

	err = s.analysisTree(ctx, service.AnalysisTreeOption{
		Identifier: entity.ContextIdentifier{
			ResourceKey: opt.Context.EntityID,
			Type:        constant.ContextResourceTypeRepository,
		},
		Context:               opt.Context,
		DirNodes:              treeInfo.DirNodes,
		MaxIndexedFolderIndex: maxIndexedFolderID,
	})
	if err != nil {
		logs.V1.CtxError(ctx, "failed to analysis tree: %v", err)
		return err
	}
	err = s.RepositoryEntityDAO.UpdateCommitIdByUniqueID(ctx, repo.UniqueID, newCommitSHA)
	if err != nil && !errors.Is(err, dal.UpdateNothingError) {
		logs.V1.CtxError(ctx, "failed to update repository by unique id %s: %v", repo.UniqueID, err)
		return err
	}
	logs.V1.CtxInfo(ctx, "repository update, analysis tree finished. repo: %v", opt.RepoName)

	directoryStats := entity.DirectoryNodes(treeInfo.DirNodes).CountDirectoryNodes()
	directoryInfo := &entity.DirectoryInfo{
		FileExtensionCountList: directoryStats.FileExtensionCountList,
		FileCount:              len(treeInfo.FirstLevelFileNodes) + len(treeInfo.RestFileNodes),
		DirectoryCount:         len(treeInfo.DirNodes),
		MaxDirectoryDepth:      directoryStats.MaxDepth,
	}

	remoteDirNodes := s.UpdateRepository(ctx, service.UpdateRepositoryOption{
		Context:         opt.Context,
		RepoName:        opt.RepoName,
		TargetCommitSHA: newCommitSHA,
		KBDirNodes:      indexedFileList,
		RemoteDirNodes:  append(treeInfo.FirstLevelFileNodes, treeInfo.RestFileNodes...),
	})
	directoryStats = entity.DirectoryNodes(remoteDirNodes).CountDirectoryNodes()
	directoryInfo.Merge(directoryStats)
	// 仓库的最大深度需要统计仓库名，所以要+1
	directoryInfo.MaxDirectoryDepth = directoryInfo.MaxDirectoryDepth + 1

	contextExtra, err := s.ContextExtraDAO.GetByContextID(ctx, opt.Context.UniqueID)
	if err != nil {
		logs.V1.CtxError(ctx, "failed to get context extra: %v", err)
	} else if contextExtra == nil {
		s.SaveContextExtra(ctx, &entity.ContextExtra{
			ContextID:     opt.Context.UniqueID,
			Type:          opt.Context.Type,
			ResourceKey:   opt.Context.ResourceKey,
			DirectoryInfo: directoryInfo,
		})
	} else {
		err = s.ContextExtraDAO.UpdateDirectoryInfoByContextID(ctx, opt.Context.UniqueID, directoryInfo)
		if err != nil {
			logs.V1.CtxWarn(ctx, "failed to update context extra: %v", err)
		}
	}

	logs.V1.CtxInfo(ctx, "repository update, all files finished. repo: %v", opt.RepoName)
	return nil
}

func (s *ContextServiceImpl) getRepositoryTree(ctx context.Context, identifier entity.ContextIdentifier, path string) ([]*entity.DirectoryNode, error) {
	repositoryEntity, err := s.RepositoryEntityDAO.GetByUniqueID(ctx, identifier.ResourceKey)
	if err != nil {
		return nil, err
	}
	if repositoryEntity == nil {
		return nil, service.ResourceNotFoundError
	}
	repoName, commitSHA := repositoryEntity.Name, repositoryEntity.CommitSHA

	directories, err := s.DGitClient.GetTreeEntriesOfRepository(ctx, repoName, commitSHA, path)
	if err != nil {
		return nil, err
	}
	return dGitDirectoryNodesToEntity(directories), nil
}

func dGitDirectoryNodesToEntity(nodes []*dgit.DirectoryEntity) []*entity.DirectoryNode {
	directoryNodes := make([]*entity.DirectoryNode, 0, len(nodes))
	for _, node := range nodes {
		directoryNodes = append(directoryNodes, &entity.DirectoryNode{
			OID:          node.ObjectID,
			Name:         node.Name,
			Type:         pack.DGitDirectoryTypeToDirectoryNodeType(node.Type),
			Path:         node.Path,
			ParentPath:   path.Dir(node.Path),
			FileFetchWay: entity.FileFetchWayRepository,
		})
	}

	return directoryNodes
}
