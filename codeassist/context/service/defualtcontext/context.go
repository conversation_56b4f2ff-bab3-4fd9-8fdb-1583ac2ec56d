package defaultcontext

import (
	"context"
	"encoding/base64"
	"fmt"
	"path"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"

	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/kite/kitex/client/callopt"
	"github.com/cenkalti/backoff/v4"
	"github.com/go-enry/go-enry/v2"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	poolsdk "github.com/sourcegraph/conc/pool"

	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/knowledgebase"
	knowledgebaseclient "code.byted.org/devgpt/kiwis/api/idl/kitex_gen/knowledgebase/knowledgebase"
	"code.byted.org/devgpt/kiwis/codeassist/common/constant"
	"code.byted.org/devgpt/kiwis/codeassist/context/dal"
	"code.byted.org/devgpt/kiwis/codeassist/context/entity"
	"code.byted.org/devgpt/kiwis/codeassist/context/service"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/conv"
	"code.byted.org/devgpt/kiwis/lib/metrics"
	"code.byted.org/devgpt/kiwis/lib/tcc"
	"code.byted.org/devgpt/kiwis/lib/tokenizer"
	"code.byted.org/devgpt/kiwis/port/dgit"
	"code.byted.org/devgpt/kiwis/port/eventbus"
	"code.byted.org/devgpt/kiwis/port/github"
	"code.byted.org/devgpt/kiwis/port/redis"
)

const (
	LegacyRepoDatasetID = 4
)

type ContextServiceImpl struct {
	Config                *config.CodeAssistAssistantConfig
	TccConfig             *config.CodeAssistTCCConfig
	OrgInstallations      *config.OrgInstallationCollection `optional:"true"`
	FileAnalysisConfig    *tcc.GenericConfig[config.FileAnalysisConfig]
	FileConcurrencyConfig *tcc.GenericConfig[config.CodeAssistFileConcurrencyConfig]

	DGitClient          dgit.Client
	KnowledgeBaseClient knowledgebaseclient.Client
	EventbusClient      eventbus.Client
	RedisClient         redis.Client
	GitHubClient        github.Client `optional:"true"`

	ContextMetaDAO      dal.ContextDAO
	DirectoryEntityDAO  dal.DirectoryDAO
	RepositoryEntityDAO dal.RepositoryDAO
	FileEntityDAO       dal.FileDAO
	ContextRelationDAO  dal.ContextRelationDAO
	ContextExtraDAO     dal.ContextExtraDAO

	DoubaoService              service.DoubaoService
	DisabledDirectoryService   service.DisabledDirectoryService
	LocalCacheService          service.LocalCacheServiceIntf
	DirectoryCompressService   service.DirectoryCompressService
	DesensitizationService     service.DesensitizationService
	RepositoryInfoCacheService service.RepositoryInfoCacheService
}

var repoLabels = []string{
	"repo",
	"仓库",
	"工程",
	"root",
	"根目录",
	"包",
	"package",
	"文件夹",
	"folder",
}

const (
	// MaxDirectoryCount kb解析一批最大的目录数量
	MaxDirectoryCount    = 100
	FoldersNameFormatter = "folders_%d"
	FoldersNamePattern   = `^folders_\d+$`
	FoldersNamePrefix    = "folders_"
	RootPath             = "."
)

func (s *ContextServiceImpl) GetContextDetail(ctx context.Context, opt entity.ContextIdentifier) (*service.ContextDetail, error) {
	contextDetail := &service.ContextDetail{}
	var contextMeta *entity.Context
	var entityID string
	switch opt.Type {
	case constant.ContextResourceTypeFile:
		fileEntity, err := s.FileEntityDAO.GetByURI(ctx, opt.ResourceKey)
		if err != nil {
			return nil, err
		}
		if fileEntity == nil {
			return nil, service.ResourceNotFoundError
		}
		entityID = fileEntity.UniqueID
		contextDetail.FileEntity = fileEntity
	case constant.ContextResourceTypeDirectory:
		directoryID, err := strconv.ParseInt(opt.ResourceKey, 10, 64)
		if err != nil {
			return nil, service.ParamInvalidError
		}
		directoryEntity, err := s.DirectoryEntityDAO.GetByDirectoryID(ctx, directoryID)
		if err != nil {
			return nil, err
		}
		if directoryEntity == nil {
			return nil, service.ResourceNotFoundError
		}
		entityID = directoryEntity.UniqueID
		contextDetail.DirectoryEntity = directoryEntity
	case constant.ContextResourceTypeRepository:
		repositoryEntity, err := s.RepositoryEntityDAO.GetByUniqueID(ctx, opt.ResourceKey)
		if err != nil {
			return nil, err
		}
		if repositoryEntity == nil {
			return nil, service.ResourceNotFoundError
		}

		entityID = repositoryEntity.UniqueID
		contextDetail.RepositoryEntity = repositoryEntity
	default:
		return nil, service.ParamInvalidError
	}
	contextMeta, err := s.ContextMetaDAO.GetByEntityID(ctx, entityID)
	if err != nil {
		return nil, err
	}
	if contextMeta == nil {
		return nil, errors.Errorf("context not found by entity id: %s", entityID)
	}
	contextDetail.Context = *contextMeta
	return contextDetail, nil
}

func (s *ContextServiceImpl) GetRepoContextDetail(ctx context.Context, opt entity.ContextIdentifier) (*service.ContextDetail, error) {
	var entityID string
	if opt.Type != constant.ContextResourceTypeRepository {
		return nil, service.ParamInvalidError
	}
	contextDetail := &service.ContextDetail{}
	repositoryEntity, err := s.RepositoryEntityDAO.GetByUniqueID(ctx, opt.ResourceKey)
	if err != nil {
		return nil, err
	}
	if repositoryEntity == nil {
		return nil, service.ResourceNotFoundError
	}
	contextDetail.RepositoryEntity = repositoryEntity

	var datasetID int64 = s.TccConfig.KnowledgeBaseDatasetConfig.GetValue().RepositoryDatasetID
	entityID = repositoryEntity.UniqueID

	contextMeta, err := s.ContextMetaDAO.GetByEntityIDAndDatasetID(ctx, entityID, datasetID)
	if err != nil {
		return nil, err
	}

	// if contextMeta is nil, try to get context from legacy dataset
	if contextMeta == nil {
		logs.V1.CtxInfo(ctx, "contextMeta is nil, try to get context from legacy dataset")
		contextMeta, err = s.ContextMetaDAO.GetByEntityIDAndDatasetID(ctx, entityID, LegacyRepoDatasetID)
		if err != nil {
			return nil, err
		}
	}
	contextDetail.Context = *contextMeta
	return contextDetail, nil
}

func (s *ContextServiceImpl) GetFileContent(ctx context.Context, opt service.GetFileIdentifierOption) (*service.FileContent, error) {
	var content string
	var fileSize int64
	var contentBytes []byte
	var err error

	// If size limit not provide, set default value
	sizeLimit := s.Config.ContextConfig.FileFetchSizeLimit
	if opt.SizeLimit != nil {
		sizeLimit = *opt.SizeLimit
	}
	switch opt.Identifier.Type {
	case constant.ContextResourceTypeFile:
		contentBytes, err = s.DoubaoService.DownloadFileByDoubaoURI(ctx, opt.Identifier.ResourceKey, opt.UserID)
		if err != nil {
			return nil, err
		}
		fileSize = int64(len(contentBytes))
	case constant.ContextResourceTypeDirectory:
		directoryID, err := strconv.ParseInt(opt.Identifier.ResourceKey, 10, 64)
		if err != nil || opt.Path == nil {
			return nil, service.ParamInvalidError
		}
		parentPath := path.Dir(*opt.Path)
		fileName := path.Base(*opt.Path)
		directoryNodes, err := s.DoubaoService.GetDoubaoDirectory(ctx, opt.UserID, directoryID, parentPath)
		if err != nil {
			return nil, err
		}
		for _, node := range directoryNodes {
			if node.Name != fileName {
				continue
			}
			contentBytes, err = s.DoubaoService.DownloadFileByDoubaoURI(ctx, node.URI, opt.UserID)
			if err != nil {
				return nil, err
			}
			fileSize = int64(len(contentBytes))
			break
		}
		if fileSize == 0 {
			return nil, service.ResourceNotFoundError
		}
	case constant.ContextResourceTypeRepository:
		repoEntity, err := s.RepositoryEntityDAO.GetByUniqueID(ctx, opt.Identifier.ResourceKey)
		if err != nil {
			return nil, err
		}
		if repoEntity == nil {
			return nil, service.ResourceNotFoundError
		}
		if opt.Path == nil {
			return nil, service.ParamInvalidError
		}
		fileEntity, err := s.getDgitFileContentWithRetry(ctx, GetDGitFileContentOption{
			Path:               *opt.Path,
			RepoName:           repoEntity.Name,
			CommitSHA:          repoEntity.CommitSHA,
			RetryConfig:        s.TccConfig.ContextRetryConfig.GetValue().GetFileContentRetryConfig,
			FileFetchSizeLimit: &sizeLimit,
		})
		if err != nil {
			return nil, err
		}
		if fileEntity == nil {
			return nil, service.ResourceNotFoundError
		}
		contentBytes = fileEntity.Content
		fileSize = fileEntity.Size
	default:
		return nil, service.ParamInvalidError
	}

	truncated := false
	// If content bytes size exceeds the size limit, truncate content
	if sizeLimit > 0 && int64(len(contentBytes)) > sizeLimit {
		contentBytes = contentBytes[:sizeLimit]
		truncated = true
	}
	// If file size is bigger than fetch content size, truncated is true
	if fileSize > int64(len(contentBytes)) {
		truncated = true
	}

	switch opt.Encoding {
	case entity.EncodingTypeNone:
		content = string(contentBytes)
	case entity.EncodingTypeBase64:
		content = base64.StdEncoding.EncodeToString(contentBytes)
	default:
		logs.V1.CtxError(ctx, "unsupported encoding type: %s", opt.Encoding)
		return nil, service.ParamInvalidError
	}
	return &service.FileContent{
		Content:   content,
		Size:      fileSize,
		Truncated: truncated,
	}, nil
}

func (s *ContextServiceImpl) GetDirectoryTree(ctx context.Context, opt service.DirectoryNodeIdentifier) ([]*entity.DirectoryNode, error) {
	switch opt.Identifier.Type {
	case constant.ContextResourceTypeDirectory:
		return s.getDirectoryTree(ctx, opt.Identifier, opt.UserID, *opt.Path)
	case constant.ContextResourceTypeRepository:
		return s.getRepositoryTree(ctx, opt.Identifier, *opt.Path)
	default:
		return nil, service.ParamInvalidError
	}
}

func (s *ContextServiceImpl) GetDirectoryTreeWhitDisabled(ctx context.Context, opt service.DirectoryNodeIdentifier) ([]*entity.DirectoryNode, error) {
	directoryNodes, err := s.GetDirectoryTree(ctx, opt)
	if err != nil {
		return nil, err
	}

	checkDisabledOpt := service.CheckDirectoryNodeDisabledOption{
		DirectoryNodeList: directoryNodes,
	}
	nodesWithDisabled := s.DisabledDirectoryService.CheckDirectoryNodeDisabled(ctx, checkDisabledOpt)
	return nodesWithDisabled, nil
}

func (s *ContextServiceImpl) GetDirectoryInfo(ctx context.Context, opt entity.ContextIdentifier) (*entity.DirectoryInfo, error) {
	contextMeta, err := s.ContextExtraDAO.GetByResourceKey(ctx, opt.ResourceKey)
	if err != nil {
		return nil, err
	}
	if contextMeta == nil {
		return nil, errors.Errorf("context not found by resource key: %s", opt.ResourceKey)
	}
	return contextMeta.DirectoryInfo, nil
}

func (s *ContextServiceImpl) BatchGetContextDetail(ctx context.Context, opt service.BatchContextOption) ([]*service.ContextDetail, error) {
	entityIDS := make([]string, 0)
	entityIDMap := make(map[string]interface{})
	result := make([]*service.ContextDetail, 0)
	switch opt.Type {
	case constant.ContextResourceTypeFile:
		fileEntities, err := s.FileEntityDAO.BatchGetByURIs(ctx, opt.ResourceKeyList)
		if err != nil {
			return nil, err
		}
		for _, fileEntity := range fileEntities {
			entityIDS = append(entityIDS, fileEntity.UniqueID)
			entityIDMap[fileEntity.UniqueID] = fileEntity
		}
	case constant.ContextResourceTypeDirectory:
		directoryIDs := lo.Map[string, int64](opt.ResourceKeyList, func(item string, _ int) int64 {
			directoryID, err := strconv.ParseInt(item, 10, 64)
			if err != nil {
				logs.V1.CtxError(ctx, "invalid resource key: %s", item)
				return -1
			}
			return directoryID
		})
		directoryEntities, err := s.DirectoryEntityDAO.BatchGetByDirectoryIDs(ctx, directoryIDs)
		if err != nil {
			return nil, err
		}
		for _, directoryEntity := range directoryEntities {
			entityIDS = append(entityIDS, directoryEntity.UniqueID)
			entityIDMap[directoryEntity.UniqueID] = directoryEntity
		}
	case constant.ContextResourceTypeRepository:
		repositoryEntities, err := s.RepositoryEntityDAO.BatchGetByUniqueIDs(ctx, opt.ResourceKeyList)
		if err != nil {
			return nil, err
		}
		for _, repositoryEntity := range repositoryEntities {
			entityIDS = append(entityIDS, repositoryEntity.UniqueID)
			entityIDMap[repositoryEntity.UniqueID] = repositoryEntity
		}
	}
	contexts, err := s.ContextMetaDAO.BatchGetByEntityIDs(ctx, entityIDS)
	if err != nil {
		return nil, err
	}
	if len(contexts) != len(entityIDS) {
		return nil, errors.New("contexts length not equal to entity ids length")
	}
	for _, c := range contexts {
		contextDetail := &service.ContextDetail{
			Context: *c,
		}
		switch c.Type {
		case constant.ContextResourceTypeFile:
			fileEntity, ok := entityIDMap[c.EntityID].(*entity.FileEntity)
			if !ok {
				return nil, errors.New("file entity id not found")
			}
			contextDetail.FileEntity = fileEntity
		case constant.ContextResourceTypeDirectory:
			directoryEntity, ok := entityIDMap[c.EntityID].(*entity.DirectoryEntity)
			if !ok {
				return nil, errors.New("directory entity id not found")
			}
			contextDetail.DirectoryEntity = directoryEntity
		case constant.ContextResourceTypeRepository:
			repositoryEntity, ok := entityIDMap[c.EntityID].(*entity.RepositoryEntity)
			if !ok {
				return nil, errors.New("repository entity id not found")
			}
			contextDetail.RepositoryEntity = repositoryEntity
		}
		result = append(result, contextDetail)
	}
	return result, nil
}

func (s *ContextServiceImpl) GetContextExtraByResourceKey(ctx context.Context, resourceKey string) (*entity.ContextExtra, error) {
	return s.ContextExtraDAO.GetByResourceKey(ctx, resourceKey)
}

func (s *ContextServiceImpl) GetContextDownloadURLInfo(ctx context.Context, opt service.ContextDownloadOption) (service.ContextDownloadURLInfo, error) {
	if opt.Identifier.Type == constant.ContextResourceTypeDirectory {
		URL, name, err := s.getDirectoryContextDownloadInfo(ctx, opt.Identifier, opt.UserID)
		if err != nil {
			return service.ContextDownloadURLInfo{}, err
		}
		return service.ContextDownloadURLInfo{
			URL:  URL,
			Name: name,
		}, nil
	}
	if opt.Identifier.Type == constant.ContextResourceTypeRepository {
		repoEntity, err := s.RepositoryEntityDAO.GetByUniqueID(ctx, opt.Identifier.ResourceKey)
		if err != nil || repoEntity == nil {
			return service.ContextDownloadURLInfo{}, service.ResourceNotFoundError
		}
		return service.ContextDownloadURLInfo{
			URL:  repoEntity.Link,
			Name: repoEntity.Name,
		}, nil
	}
	return service.ContextDownloadURLInfo{}, service.ParamInvalidError
}

func (s *ContextServiceImpl) GetResourceDownloadURLInfo(ctx context.Context, opt service.ResourceDownloadOption) (service.ResourceDownloadURLInfo, error) {
	switch opt.Type {
	case entity.ResourceDownloadTypeMultiFileContext:
		if len(opt.MultiFileContextIdentifiers) == 0 {
			return service.ResourceDownloadURLInfo{}, service.ParamInvalidError
		}
		containsNotFile := lo.ContainsBy[*entity.ContextIdentifier](opt.MultiFileContextIdentifiers, func(item *entity.ContextIdentifier) bool {
			return item.Type != constant.ContextResourceTypeFile
		})
		if containsNotFile {
			return service.ResourceDownloadURLInfo{}, service.ParamInvalidError
		}

		fileContexts, err := s.MGetContext(ctx, service.MGetContextStatusOption{
			Identifiers: opt.MultiFileContextIdentifiers,
		})
		if err != nil {
			return service.ResourceDownloadURLInfo{}, err
		}
		compressOpt := service.CompressDoubaoMultiFileOption{
			UserID: opt.UserID,
			CompressFileInfos: lo.Map[*entity.Context, *service.CompressFileInfo](fileContexts, func(item *entity.Context, _ int) *service.CompressFileInfo {
				return &service.CompressFileInfo{
					Name: item.Name,
					Uri:  item.ResourceKey,
				}
			}),
		}
		URI, err := s.DirectoryCompressService.CompressAndUploadMultiFile(ctx, compressOpt)
		if err != nil {
			return service.ResourceDownloadURLInfo{}, err
		}

		URL, _, err := s.DoubaoService.GetDoubaoURLByURI(ctx, URI, opt.UserID)
		if err != nil {
			return service.ResourceDownloadURLInfo{}, err
		}
		return service.ResourceDownloadURLInfo{
			URL:  URL,
			Name: "DoubaoFiles",
		}, nil
	case entity.ResourceDownloadTypeDirectoryContext:
		if opt.DirectoryContextIdentifier == nil {
			return service.ResourceDownloadURLInfo{}, service.ParamInvalidError
		}
		if opt.DirectoryContextIdentifier.Type != constant.ContextResourceTypeDirectory {
			return service.ResourceDownloadURLInfo{}, service.ParamInvalidError
		}

		URL, name, err := s.getDirectoryContextDownloadInfo(ctx, *opt.DirectoryContextIdentifier, opt.UserID)
		if err != nil {
			return service.ResourceDownloadURLInfo{}, err
		}
		return service.ResourceDownloadURLInfo{
			URL:  URL,
			Name: name,
		}, nil
	case entity.ResourceDownloadTypeArtifacts:
		if opt.ArtifactsIdentifier == nil {
			return service.ResourceDownloadURLInfo{}, service.ParamInvalidError
		}
		compressOpt := service.CompressDoubaoArtifactsOption{
			UserID:     opt.UserID,
			Identifier: opt.ArtifactsIdentifier,
		}
		URI, err := s.DirectoryCompressService.CompressAndUploadArtifacts(ctx, compressOpt)
		if err != nil {
			return service.ResourceDownloadURLInfo{}, err
		}

		URL, _, err := s.DoubaoService.GetDoubaoURLByURI(ctx, URI, opt.UserID)
		if err != nil {
			return service.ResourceDownloadURLInfo{}, err
		}
		return service.ResourceDownloadURLInfo{
			URL:  URL,
			Name: opt.ArtifactsIdentifier.Title,
		}, nil
	case entity.ResourceDownloadTypeSingleFileContext:
		if opt.SingleFileContextIdentifier == nil {
			return service.ResourceDownloadURLInfo{}, service.ParamInvalidError
		}
		if opt.SingleFileContextIdentifier.Type != constant.ContextResourceTypeFile {
			return service.ResourceDownloadURLInfo{}, service.ParamInvalidError
		}

		URL, _, err := s.DoubaoService.GetDoubaoURLByURI(ctx, opt.SingleFileContextIdentifier.ResourceKey, opt.UserID)
		if err != nil {
			return service.ResourceDownloadURLInfo{}, err
		}
		return service.ResourceDownloadURLInfo{
			URL:  URL,
			Name: "",
		}, nil
	default:
		return service.ResourceDownloadURLInfo{}, service.ParamInvalidError
	}
}

func (s *ContextServiceImpl) getDirectoryContextDownloadInfo(ctx context.Context, identifier entity.ContextIdentifier, userID int64) (string, string, error) {
	directoryID, err := strconv.ParseInt(identifier.ResourceKey, 10, 64)
	if err != nil {
		return "", "", service.ParamInvalidError
	}
	// 鉴权
	_, err = s.DoubaoService.GetDoubaoDirectory(ctx, userID, directoryID, RootPath)
	if err != nil {
		return "", "", service.AccessRejectError
	}
	directoryEntity, err := s.DirectoryEntityDAO.GetByDirectoryID(ctx, directoryID)
	if directoryEntity == nil {
		return "", "", service.ResourceNotFoundError
	}
	if err != nil {
		return "", "", err
	}

	zipURI := directoryEntity.ZipURI
	if zipURI == "" {
		zipURI, _, err = s.DirectoryCompressService.CompressAndUploadDoubaoDirectory(ctx, service.CompressDoubaoDirectoryOption{
			DirectoryID: directoryID,
			UserID:      userID,
			UniqueID:    directoryEntity.UniqueID,
			Name:        directoryEntity.Name,
		})
		if err != nil {
			return "", "", err
		}
	}

	zipURL, _, err := s.DoubaoService.GetDoubaoURLByURI(ctx, zipURI, userID)
	if err != nil {
		return "", "", err
	}
	return zipURL, directoryEntity.Name, nil
}

func (s *ContextServiceImpl) MGetContext(ctx context.Context, opt service.MGetContextStatusOption) ([]*entity.Context, error) {
	fileURIs := make([]string, 0)
	directoryIDs := make([]string, 0)
	repositoryIDs := make([]string, 0)
	for _, identifier := range opt.Identifiers {
		switch identifier.Type {
		case constant.ContextResourceTypeFile:
			fileURIs = append(fileURIs, identifier.ResourceKey)
		case constant.ContextResourceTypeDirectory:
			directoryIDs = append(directoryIDs, identifier.ResourceKey)
		case constant.ContextResourceTypeRepository:
			repositoryIDs = append(repositoryIDs, identifier.ResourceKey)
		default:
			return nil, service.ParamInvalidError
		}
	}

	contextMetas := make([]*entity.Context, 0)
	if len(fileURIs) > 0 {
		fileContexts, err := s.ContextMetaDAO.BatchGetByResourceKeys(ctx, constant.ContextResourceTypeFile, fileURIs)
		if err != nil {
			return nil, err
		}
		contextMetas = append(contextMetas, fileContexts...)
	}

	if len(directoryIDs) > 0 {
		directoryContexts, err := s.ContextMetaDAO.BatchGetByResourceKeys(ctx, constant.ContextResourceTypeDirectory, directoryIDs)
		if err != nil {
			return nil, err
		}
		contextMetas = append(contextMetas, directoryContexts...)
	}

	if len(repositoryIDs) > 0 {
		repositoryContexts, err := s.ContextMetaDAO.BatchGetByResourceKeys(ctx, constant.ContextResourceTypeRepository, repositoryIDs)
		if err != nil {
			return nil, err
		}
		datasetID := s.TccConfig.KnowledgeBaseDatasetConfig.GetValue().RepositoryDatasetID
		logs.V1.CtxInfo(ctx, "repositoryContexts: %+v, datasetID: %d", repositoryContexts, datasetID)
		for _, contextMeta := range repositoryContexts {
			if contextMeta != nil && contextMeta.DatasetID == datasetID {
				contextMetas = append(contextMetas, contextMeta)
			}
		}
	}

	return contextMetas, nil
}

func (s *ContextServiceImpl) DesensitizeContext(ctx context.Context, opt service.DesensitizeContextOption) (*service.DesensitizeContextInfo, error) {
	var identifiers []*entity.ContextIdentifier
	for _, resourceKey := range opt.ResourceKeys {
		identifiers = append(identifiers, &entity.ContextIdentifier{
			Type:        opt.Type,
			ResourceKey: resourceKey,
		})
	}
	contextMetas, err := s.MGetContext(ctx, service.MGetContextStatusOption{Identifiers: identifiers})
	if err != nil {
		return nil, errors.WithMessage(err, "get context failed")
	}
	if len(contextMetas) == 0 {
		return nil, errors.New("context not found")
	}

	// resource keys of each request must belong to one user, use first context to get user id
	firstContext := contextMetas[0]
	contextRelation, err := s.ContextRelationDAO.GetContextRelation(ctx, firstContext.UniqueID, entity.ContextRelationTypeReference)
	if err != nil {
		return nil, errors.WithMessage(err, "get context relation failed")
	}
	if contextRelation == nil {
		return nil, errors.New("context relation not found")
	}
	userID, err := strconv.ParseInt(contextRelation.RelatedID, 10, 64)
	if err != nil {
		return nil, err
	}

	logs.V1.CtxInfo(ctx, "get %d contexts, with user id %d", len(contextMetas), userID)

	var fileNodes []*entity.DirectoryNode
	switch opt.Type {
	case constant.ContextResourceTypeFile:
		nameSet := make(map[string]struct{})
		for _, contextMeta := range contextMetas {
			fileName := contextMeta.Name
			if _, exist := nameSet[contextMeta.Name]; exist {
				fileName = fmt.Sprintf("%s_%s", fileName[:strings.IndexByte(fileName, '.')], contextMeta.ResourceKey)
			} else {
				nameSet[contextMeta.Name] = struct{}{}
			}

			fileNodes = append(fileNodes, &entity.DirectoryNode{
				Name: contextMeta.Name,
				Type: entity.DirectoryNodeTypeFile,
				Path: fileName,
				URI:  contextMeta.ResourceKey,
			})
		}
	case constant.ContextResourceTypeDirectory:
		directoryID, err := strconv.ParseInt(firstContext.ResourceKey, 10, 64)
		if err != nil {
			logs.V1.CtxError(ctx, "invalid directory resource key: %s", firstContext.ResourceKey)
			return nil, err
		}
		contextTreeInfo, err := s.getAllDirectoryTree(ctx, directoryID, userID)
		if err != nil {
			return nil, errors.WithMessagef(err, "directory download tree failed")
		}
		fileNodes = append(fileNodes, contextTreeInfo.FirstLevelFileNodes...)
		fileNodes = append(fileNodes, contextTreeInfo.RestFileNodes...)
		fileNodes = append(fileNodes, contextTreeInfo.DirNodes...)
	default:
		return nil, service.ParamInvalidError
	}

	desensitizedContextID, desensitizedContextDownloadLink, err := s.DesensitizationService.Desensitize(ctx, service.DesensitizeOption{
		UserID:    userID,
		FileNodes: fileNodes,
	})
	if err != nil {
		return nil, err
	}

	return &service.DesensitizeContextInfo{
		DesensitizedContextID:           desensitizedContextID,
		DesensitizedContextDownloadLink: desensitizedContextDownloadLink,
	}, nil
}

func (s *ContextServiceImpl) analysisTree(ctx context.Context, opt service.AnalysisTreeOption) (err error) {
	// add latency and throughput metrics
	reportContextIndexStepMetricsFunc := metrics.CodeAssistMetric.ReportContextIndexStepMetrics()
	defer func() {
		reportContextIndexStepMetricsFunc(string(opt.Identifier.Type), metrics.ContextAnalysisTreeStep, err != nil)
	}()

	pathPrefix := "."
	if opt.Identifier.Type == constant.ContextResourceTypeRepository {
		// repo需要将仓库名称添加到路径头部
		_, pathPrefix, err = s.splitOwnerAndNameFromRepoName(opt.Context.Name)
		if err != nil {
			return err
		}
	}

	updateTreeConcurrency := s.FileConcurrencyConfig.GetPointer().GetUpdateTreeConcurrency()
	updateTreePool := poolsdk.New().WithMaxGoroutines(updateTreeConcurrency).WithErrors()

	pathSegments := make([]*knowledgebase.Segment, 0)
	// add root path segment
	pathSegments = append(pathSegments, &knowledgebase.Segment{
		Type: knowledgebase.FolderSegmentType,
		FolderSegment: &knowledgebase.FolderSegment{
			ID:   RootPath,
			Name: RootPath,
			Path: RootPath,
			Attributes: &knowledgebase.Attribute{
				ExternalInfos: repoLabels,
			},
		},
	})
	// folderIndex Record directory sharding index
	folderIndex := 0

	dirNodes := s.DisabledDirectoryService.CheckDirectoryNodeDisabled(ctx, service.CheckDirectoryNodeDisabledOption{
		DirectoryNodeList: opt.DirNodes,
	})

	// analysis directory nodes
	for _, dirNode := range dirNodes {
		if dirNode == nil {
			continue
		}

		if dirNode.Type != entity.DirectoryNodeTypeDirectory {
			logs.V1.CtxWarn(ctx, "params contains none directory node %s", dirNode.Path)
			continue
		}

		if dirNode.Disabled {
			continue
		}

		directoryPath := path.Join(pathPrefix, dirNode.Path)
		pathSegment := &knowledgebase.Segment{
			Type: knowledgebase.FolderSegmentType,
			FolderSegment: &knowledgebase.FolderSegment{
				ID:   directoryPath,
				Name: dirNode.Name,
				Path: directoryPath,
			},
		}
		pathSegments = append(pathSegments, pathSegment)

		if len(pathSegments) >= MaxDirectoryCount {
			batchPathSegment := pathSegments[:]
			batchIndex := folderIndex

			updateTreePool.Go(func() error {
				req := &knowledgebase.UpdateFilesRequest{
					DatasetID:    opt.Context.DatasetID,
					DatasourceID: opt.Context.DatasourceID,
					Files: []*knowledgebase.File{
						{
							ID:       fmt.Sprintf(FoldersNameFormatter, batchIndex),
							Name:     fmt.Sprintf(FoldersNameFormatter, batchIndex),
							Segments: batchPathSegment,
						},
					},
					Sync: true,
				}
				_, err = s.UpdateFilesWithRetry(ctx, req, opt.Identifier.Type, constant.ContextStepAnalysisTree)
				if err != nil {
					return errors.WithMessage(err, "update files of folders failed.")
				}
				return nil
			})

			pathSegments = make([]*knowledgebase.Segment, 0)
			folderIndex++
		}
	}

	if len(pathSegments) > 0 {
		batchPathSegment := pathSegments[:]
		batchIndex := folderIndex

		updateTreePool.Go(func() error {
			req := &knowledgebase.UpdateFilesRequest{
				DatasetID:    opt.Context.DatasetID,
				DatasourceID: opt.Context.DatasourceID,
				Files: []*knowledgebase.File{
					{
						ID:       fmt.Sprintf(FoldersNameFormatter, batchIndex),
						Name:     fmt.Sprintf(FoldersNameFormatter, batchIndex),
						Segments: batchPathSegment,
					},
				},
				Sync: true,
			}

			_, err = s.UpdateFilesWithRetry(ctx, req, opt.Identifier.Type, constant.ContextStepAnalysisTree)
			if err != nil {
				return errors.WithMessage(err, "update files of folders failed.")
			}
			return nil
		})
		folderIndex++
	}

	err = updateTreePool.Wait()
	if err != nil {
		return err
	}

	// The folder is updated in batches, and redundant batches are deleted when updated.
	if opt.Identifier.Type == constant.ContextResourceTypeRepository {
		deleteTreePool := poolsdk.New().WithMaxGoroutines(updateTreeConcurrency)

		for i := folderIndex; i <= opt.MaxIndexedFolderIndex; i++ {
			fileID := fmt.Sprintf(FoldersNameFormatter, i)

			retryConf := s.TccConfig.ContextRetryConfig.GetValue().DeleteFilesRetryConfig
			deleteTreePool.Go(func() {
				logs.V1.CtxInfo(ctx, "prepare to delete folder %s", fileID)
				deleteErr := s.DeleteFilesWithRetry(ctx, &knowledgebase.DeleteFilesRequest{
					DatasetID:        opt.Context.DatasetID,
					DatasourceID:     opt.Context.DatasourceID,
					Files:            []*knowledgebase.File{{ID: fileID}},
					DeleteDatasource: false,
				}, retryConf)
				if deleteErr != nil {
					logs.V1.CtxError(ctx, "delete kb folder failed. err: %v", opt.Identifier.ResourceKey, deleteErr)
				}
			})
		}
		deleteTreePool.Wait()
	}

	return nil
}

func (s *ContextServiceImpl) analysisFirstLevelFiles(ctx context.Context, opt service.AnalysisFirstLevelFilesOption) (err error) {
	// add latency and throughput metrics
	reportContextIndexStepMetricsFunc := metrics.CodeAssistMetric.ReportContextIndexStepMetrics()
	defer func() {
		reportContextIndexStepMetricsFunc(opt.Context.Type.String(), metrics.ContextAnalysisFirstLevelFilesStep, err != nil)
	}()

	firstLevelFileNodes := s.DisabledDirectoryService.CheckDirectoryNodeDisabled(ctx, service.CheckDirectoryNodeDisabledOption{
		DirectoryNodeList: opt.FirstLevelFileNodes,
	})

	needUpdateFiles := make([]*knowledgebase.File, 0) // CKG 更新的文件列表
	locker := sync.RWMutex{}

	downloadFilesPool := poolsdk.New().WithMaxGoroutines(opt.DownloadFilesConcurrency).WithErrors()

	// download files content
	for _, firstLevelFileNode := range firstLevelFileNodes {
		if firstLevelFileNode == nil {
			continue
		}

		if firstLevelFileNode.Type != entity.DirectoryNodeTypeFile {
			logs.V1.CtxWarn(ctx, "analysisFirstLevelFiles: params contains none file node %s", firstLevelFileNode.Path)
			continue
		}

		if firstLevelFileNode.Disabled {
			continue
		}

		fileNode := firstLevelFileNode
		downloadFilesPool.Go(func() error {
			kbFileID, rawFile, err := opt.DownloadFileFunc(fileNode)
			if err != nil {
				return err
			}

			ckgDisabled := s.DisabledDirectoryService.CheckFileContentCKGDisabled(ctx, service.CheckFileContentDisabledOption{
				FilePath:    fileNode.Path,
				FileContent: rawFile,
			})
			if ckgDisabled {
				fileNode.IndexResult = entity.ContextIndexResultFilteredByCKGLimit
				return nil
			}

			// count supported file tokens
			fileNode.Tokens = tokenizer.CountTokensByTokenizer(string(rawFile))

			locker.Lock()
			defer locker.Unlock()
			needUpdateFiles = append(needUpdateFiles, &knowledgebase.File{
				ContentHash:  fileNode.OID,
				DatasetID:    opt.Context.DatasetID,
				DatasourceID: opt.Context.DatasourceID,
				Content:      string(rawFile),
				ID:           kbFileID,
			})
			return nil
		})
	}
	err = downloadFilesPool.Wait()
	if err != nil {
		return errors.WithMessage(err, "analysisFirstLevelFiles: download files failed.")
	}

	err = s.ContextMetaDAO.UpdateStatus(ctx, opt.Context.UniqueID, entity.ContextStatusProcessing, entity.ContextIndexProcessFileDownloaded)
	if err != nil {
		logs.V1.CtxError(ctx, "update context status failed. err: %v", err)
	}
	logs.V1.CtxInfo(ctx, "analysisFirstLevelFiles: context %s with name %s download files finished. begin to update files.", opt.Context.UniqueID, opt.Context.Name)

	// knowledge update files with goroutine
	firstLevelUpdateFilesConcurrency := s.FileConcurrencyConfig.GetPointer().GetFirstLevelUpdateFilesConcurrency()
	updateFilesPool := poolsdk.New().WithMaxGoroutines(firstLevelUpdateFilesConcurrency).WithErrors()
	for _, needUpdateFile := range needUpdateFiles {
		if needUpdateFile == nil {
			continue
		}

		updateFile := needUpdateFile
		updateFilesPool.Go(func() error {
			updateFilesReq := &knowledgebase.UpdateFilesRequest{
				DatasetID:    opt.Context.DatasetID,
				DatasourceID: opt.Context.DatasourceID,
				Files:        []*knowledgebase.File{updateFile},
				Sync:         true,
			}
			_, err = s.UpdateFilesWithRetry(ctx, updateFilesReq, opt.Context.Type, constant.ContextStepFirstLevelFile)
			if err != nil {
				logs.V1.CtxError(ctx, "analysisFirstLevelFiles: update file to knowledge base failed. filePath: %s, fileSize: %d, err: %v", updateFile.ID, len(updateFile.Content), err)
				return err
			}
			return nil
		})
	}
	err = updateFilesPool.Wait()
	if err != nil {
		return errors.WithMessage(err, "analysisFirstLevelFiles: update files failed.")
	}

	return nil
}

func (s *ContextServiceImpl) analysisRestFiles(ctx context.Context, opt service.AnalysisRestFilesOption) {
	// add latency and throughput metrics
	reportContextIndexStepMetricsFunc := metrics.CodeAssistMetric.ReportContextIndexStepMetrics()
	defer func() {
		reportContextIndexStepMetricsFunc(opt.Context.Type.String(), metrics.ContextAnalysisAllFilesStep, false)
	}()

	logs.V1.CtxInfo(ctx, "analysisRestFiles: context %s with name %s analysis begin", opt.Context.UniqueID, opt.Context.Name)

	updateFilesConcurrency := s.FileConcurrencyConfig.GetPointer().GetUpdateFilesConcurrency()
	repoAnalysisPool := poolsdk.New().WithMaxGoroutines(updateFilesConcurrency)

	restFileNodes := s.DisabledDirectoryService.CheckDirectoryNodeDisabled(ctx, service.CheckDirectoryNodeDisabledOption{
		DirectoryNodeList: opt.RestFileNodes,
	})
	for _, restFileNode := range restFileNodes {
		if restFileNode == nil {
			continue
		}

		if restFileNode.Disabled {
			continue
		}

		if restFileNode.Type != entity.DirectoryNodeTypeFile {
			logs.V1.CtxWarn(ctx, "analysisRestFiles: params contains none file node %s", restFileNode.Path)
			continue
		}

		fileNode := restFileNode
		repoAnalysisPool.Go(func() {
			kbFileID, rawFile, err := opt.DownloadFileFunc(fileNode)
			if err != nil {
				logs.V1.CtxError(ctx, "analysisRestFiles: get file content of failed. filePath: %s, err: %v", fileNode.Path, err)
				return
			}

			ckgDisabled := s.DisabledDirectoryService.CheckFileContentCKGDisabled(ctx, service.CheckFileContentDisabledOption{
				FilePath:    fileNode.Path,
				FileContent: rawFile,
			})
			if ckgDisabled {
				fileNode.IndexResult = entity.ContextIndexResultFilteredByCKGLimit
				return
			}

			// count supported file tokens
			fileNode.Tokens = tokenizer.CountTokensByTokenizer(string(rawFile))

			file := &knowledgebase.File{
				DatasetID:    opt.Context.DatasetID,
				DatasourceID: opt.Context.DatasourceID,
				ID:           kbFileID,
				Content:      string(rawFile),
				ContentHash:  fileNode.OID,
			}
			updateFilesReq := &knowledgebase.UpdateFilesRequest{
				DatasetID:    opt.Context.DatasetID,
				DatasourceID: opt.Context.DatasourceID,
				Files:        []*knowledgebase.File{file},
				Sync:         true,
			}
			_, err = s.UpdateFilesWithRetry(ctx, updateFilesReq, opt.Context.Type, constant.ContextStepRestLevelFile)
			if err != nil {
				logs.V1.CtxError(ctx, "analysisRestFiles: update file to knowledge base failed. filePath: %s, fileSize: %d, err: %v", fileNode.Path, len(rawFile), err)
			}
		})
	}
	repoAnalysisPool.Wait()
	logs.V1.CtxInfo(ctx, "analysisRestFiles: context %s with name %s analysis completed", opt.Context.UniqueID, opt.Context.Name)
}

// GetFileLanguage 获取文件语言
func (s *ContextServiceImpl) GetFileLanguage(fileName string, content []byte, supportLanguageByExtension map[string]string) string {
	// 先根据后缀的配置直接进行语言分析
	fileExtension := strings.ToLower(path.Ext(fileName))
	if fileLanguage, ok := supportLanguageByExtension[fileExtension]; ok && len(fileLanguage) > 0 {
		return fileLanguage
	}
	return enry.GetLanguage(fileName, content)
}

func (s *ContextServiceImpl) SaveContextExtra(ctx context.Context, contextExtra *entity.ContextExtra) {
	id, err := s.ContextExtraDAO.CreateIgnoreDuplicate(ctx, contextExtra)
	if err != nil {
		logs.V1.CtxWarn(ctx, "SaveContextExtra: create context extra with resource key %s failed. err: %v", contextExtra.ResourceKey, err)
	} else if id == "" {
		logs.V1.CtxWarn(ctx, "SaveContextExtra: create context extra with resource key %s with duplicate key", contextExtra.ResourceKey)
	}

	logs.V1.CtxInfo(ctx, "SaveContextExtra: context extra saved with resource key %s, id %s, %+v", contextExtra.ResourceKey, id, conv.JSONString(contextExtra))
}

func (s *ContextServiceImpl) splitOwnerAndNameFromRepoName(repoName string) (string, string, error) {
	parts := strings.Split(repoName, "/")
	if len(parts) != 2 {
		return "", "", errors.New("invalid repository name")
	}
	return parts[0], parts[1], nil
}

func (s *ContextServiceImpl) UpdateFilesWithMetrics(ctx context.Context, req *knowledgebase.UpdateFilesRequest, contextResourceType string) (*knowledgebase.UpdateFilesResponse, error) {
	tags := &metrics.CodeAssistFileKBIndexTag{ResourceType: contextResourceType}
	_ = metrics.CodeAssistMetric.FileKBIndexThroughput.WithTags(tags).Add(1)
	defer metrics.TraceTimer(metrics.CodeAssistMetric.FileKBIndexLatency.WithTags(tags))()

	resp, err := s.KnowledgeBaseClient.UpdateFiles(ctx, req)
	if err != nil {
		return nil, errors.WithMessagef(err, "update file err")
	}
	return resp, nil
}

func (s *ContextServiceImpl) UpdateFilesWithRetry(
	ctx context.Context,
	req *knowledgebase.UpdateFilesRequest,
	contextResourceType constant.ContextResourceType,
	updateStep constant.ContextStep) (*knowledgebase.UpdateFilesResponse, error) {
	// 默认不重试
	updateFileRetryConf := config.CodeAssistRetryConfig{
		MaxRetries: 0,
		IntervalMS: 0,
	}
	if updateStep == constant.ContextStepAnalysisTree {
		updateFileRetryConf = s.TccConfig.ContextRetryConfig.GetValue().UpdateFoldersRetryConfig
	} else {
		switch contextResourceType {
		case constant.ContextResourceTypeRepository:
			updateFileRetryConf = s.TccConfig.ContextRetryConfig.GetValue().RepositoryUpdateFilesRetryConfig
		case constant.ContextResourceTypeDirectory:
			updateFileRetryConf = s.TccConfig.ContextRetryConfig.GetValue().DirectoryUpdateFilesRetryConfig
		case constant.ContextResourceTypeFile:
			updateFileRetryConf = s.TccConfig.ContextRetryConfig.GetValue().FileUpdateFilesRetryConfig
		}
	}

	backoffConf := backoff.NewExponentialBackOff()
	backoffConf.InitialInterval = time.Duration(updateFileRetryConf.IntervalMS) * time.Millisecond
	return backoff.RetryWithData(func() (*knowledgebase.UpdateFilesResponse, error) {
		return s.UpdateFilesWithMetrics(ctx, req, contextResourceType.String())
	}, backoff.WithMaxRetries(backoffConf, uint64(updateFileRetryConf.MaxRetries)))
}

func (s *ContextServiceImpl) DeleteFilesWithRetry(ctx context.Context, req *knowledgebase.DeleteFilesRequest,
	retryConf config.CodeAssistRetryConfig) error {
	backoffConf := backoff.NewExponentialBackOff()
	backoffConf.InitialInterval = time.Duration(retryConf.IntervalMS) * time.Millisecond
	return backoff.Retry(func() error {
		_, err := s.KnowledgeBaseClient.DeleteFiles(ctx, req)
		return err
	}, backoff.WithMaxRetries(backoffConf, uint64(retryConf.MaxRetries)))
}

var FoldersNameRegex = regexp.MustCompile(FoldersNamePattern)

func (s *ContextServiceImpl) GetIndexedFilesAndMaxID(ctx context.Context, datasetID int64, datasourceID int64) ([]*entity.DirectoryNode, int, error) {
	tags := &metrics.CodeAssistKBFileMetaFetchTag{DatasetID: datasetID}
	_ = metrics.CodeAssistMetric.KBFileMetaFetchThroughput.WithTags(tags).Add(1)
	defer metrics.TraceTimer(metrics.CodeAssistMetric.KBFileMetaFetchLatency.WithTags(tags))()

	req := &knowledgebase.GetFilesRequest{
		DatasetID:     datasetID,
		DatasourceIDs: []int64{datasourceID},
		OnlyMetadata:  true,
	}
	resp, err := s.KnowledgeBaseClient.GetFiles(ctx, req, callopt.WithRPCTimeout(time.Second*60))
	if err != nil {
		return nil, -1, errors.WithMessage(err, "get kb file meta err")
	}

	directoryNodes := make([]*entity.DirectoryNode, 0)
	foldersMaxIndex := 0
	for _, fileMeta := range resp.GetFiles() {
		fullPath := fileMeta.GetID()
		if FoldersNameRegex.MatchString(fullPath) {
			foldersIndex, err := strconv.Atoi(strings.TrimPrefix(fullPath, FoldersNamePrefix))
			if err != nil {
				logs.V1.CtxError(ctx, "parse foldersIndex from file path: %s err: %v", fullPath, err)
				continue
			}
			foldersMaxIndex = max(foldersMaxIndex, foldersIndex)
			continue
		}

		directoryNodes = append(directoryNodes, &entity.DirectoryNode{
			OID:        fileMeta.ContentHash,
			Name:       path.Base(fullPath),
			Type:       entity.DirectoryNodeTypeFile,
			Path:       fullPath,
			ParentPath: path.Dir(fullPath),
		})
	}

	return directoryNodes, foldersMaxIndex, nil
}

func (s *ContextServiceImpl) IsResourceKeyInBlackList(ctx context.Context, identifier *entity.ContextIdentifier) bool {
	var resourceKeyBlackList []string
	switch identifier.Type {
	case constant.ContextResourceTypeDirectory:
		resourceKeyBlackList = s.TccConfig.ResourceKeyBlackListConfig.GetValue().DirectoryBlackList
	case constant.ContextResourceTypeRepository:
		resourceKeyBlackList = s.TccConfig.ResourceKeyBlackListConfig.GetValue().RepositoryBlackList
	default:
		logs.V1.CtxWarn(ctx, "identifier type has no black list: %s", identifier.Type)
	}

	return lo.Contains[string](resourceKeyBlackList, identifier.ResourceKey)
}
