package entity

import (
	"time"

	"code.byted.org/devgpt/kiwis/codeassist/common/constant"
)

type ContextStatus int
type ContextSource string
type EncodingType int
type ContextIndexSource string
type ResourceDownloadType string

const (
	ContextStatusJustCreated ContextStatus = 0
	ContextStatusProcessing  ContextStatus = 1
	ContextStatusSuccess     ContextStatus = 2
	ContextStatusFailed      ContextStatus = 3
	ContextStatusReady       ContextStatus = 4

	ContextSourceDoubao ContextSource = "doubao"
	ContextSourceGitHub ContextSource = "github"

	EncodingTypeNone   EncodingType = 0
	EncodingTypeBase64 EncodingType = 1

	ContextIndexSourceFromUser             ContextIndexSource = "user"
	ContextIndexSourceFromPlatformPreIndex ContextIndexSource = "platform_pre_index"
)

const (
	ContextIndexProcessIndexingFailed = 0   // 索引失败，进度归0
	ContextIndexProcessCreated        = 10  // 实体创建完成
	ContextIndexProcessIndexing       = 15  // 索引解析中
	ContextIndexProcessTreeDownloaded = 45  // 索引目录下载完成
	ContextIndexProcessFileDownloaded = 75  // 索引文件下载完成
	ContextIndexProcessSuccess        = 100 // 索引完成
)

const (
	RepositoryUpdateIntervalDuration = 12 * time.Hour    // 代码仓库更新时间间隔
	ContextIndexMaxDuration          = 180 * time.Minute //索引最长时间间隔, 暂定180分钟
)

const (
	ResourceDownloadTypeUnknown           ResourceDownloadType = "unknown"
	ResourceDownloadTypeMultiFileContext  ResourceDownloadType = "multi_file"
	ResourceDownloadTypeDirectoryContext  ResourceDownloadType = "directory"
	ResourceDownloadTypeArtifacts         ResourceDownloadType = "artifacts"
	ResourceDownloadTypeSingleFileContext ResourceDownloadType = "single_file"
)

type Context struct {
	RecordMeta

	DatasetID    int64 `json:"dataset_id"`
	DatasourceID int64 `json:"datasource_id"`

	Name        string                       `json:"name"`
	Type        constant.ContextResourceType `json:"type"`      // 上下文实体类型
	EntityID    string                       `json:"entity_id"` // 关联的实体ID
	ResourceKey string                       `json:"resource_key"`
	Status      ContextStatus                `json:"status"`
	Progress    int                          `json:"progress"`

	Extra map[string]any `json:"extra"`

	ContextExtension *ContextExtension `json:"context_extension,omitempty"` // context扩展字段
}

type ContextExtension struct {
	FileNum int64 // 文件数量
	Size    int64 // 大小
}

// ExceedMaxIndexLatency 判断上下文索引是否超过最大时间延迟
func (c *Context) ExceedMaxIndexLatency() bool {
	if c.Status == ContextStatusJustCreated || c.Status == ContextStatusProcessing || c.Status == ContextStatusReady {
		return c.UpdatedAt.Add(ContextIndexMaxDuration).Before(time.Now())
	}
	return false
}

func (c *Context) IndexFailed() bool {
	return c.Status == ContextStatusFailed
}

// RepositoryEntity GitHub Repo 实体
type RepositoryEntity struct {
	RecordMeta

	Name            string             `json:"name"`
	Link            string             `json:"link"`
	Ref             string             `json:"ref"`
	CommitSHA       string             `json:"commit_sha"`
	Source          ContextSource      `json:"source"`
	Language        string             `json:"language"`
	Size            int                `json:"size"`
	Description     string             `json:"description,omitempty"`
	StargazersCount int64              `json:"stargazers_count"`
	IndexSource     ContextIndexSource `json:"index_source"`
	UpdateTimes     int64              `json:"update_times"`
}

func (r *RepositoryEntity) RepositoryEntityToRepositoryInfoCache() *RepositoryInfoCache {
	if r == nil {
		return nil
	}

	repositoryInfoCache := &RepositoryInfoCache{
		Name:            r.Name,
		Link:            r.Link,
		Language:        r.Language,
		Size:            r.Size,
		Description:     r.Description,
		StargazersCount: r.StargazersCount,
		Source:          ContextSourceGitHub,
	}

	return repositoryInfoCache
}

// FileEntity 文件实体
type FileEntity struct {
	RecordMeta

	Name       string        `json:"name"`
	URI        string        `json:"uri"`
	Source     ContextSource `json:"source"`
	Language   string        `json:"language"`
	TokenCount int64         `json:"token_count"` // 文件tokencount
	Size       int           `json:"size"`
}

// DirectoryEntity 文件夹实体
type DirectoryEntity struct {
	RecordMeta

	Name        string        `json:"name"`
	DirectoryID int64         `json:"directory_id"` // 豆包侧关联id
	Source      ContextSource `json:"source"`
	ZipURI      string        `json:"zip_uri"`
	Language    string        `json:"language"`
	Size        int           `json:"size"`
}

type RecordMeta struct {
	// UniqueID unique id
	UniqueID string `json:"unique_id"`
	// CreatedAt is create timestamp.
	CreatedAt time.Time `json:"created_at"`
	// UpdatedAt is last update timestamp.
	UpdatedAt time.Time `json:"updated_at"`
}

type ContextIdentifier struct {
	Type        constant.ContextResourceType `json:"type"`
	ResourceKey string                       `json:"resource_key"`
	Name        string                       `json:"name"`
}
