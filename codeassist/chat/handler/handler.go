package handler

import (
	"context"
	"encoding/json"
	"strconv"
	"strings"
	"time"

	alicectx "code.byted.org/flow/alice_util/ctx"

	fornaxtrace "code.byted.org/flowdevops/fornax_sdk/infra/ob"
	"code.byted.org/gopkg/logs/v2/log"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"github.com/spf13/cast"

	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/base"
	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/codeassist"
	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/common"
	botengine "code.byted.org/devgpt/kiwis/api/idl/kitex_gen/flow/bot/engine"
	bothook "code.byted.org/devgpt/kiwis/api/idl/kitex_gen/flow/bot/hook"
	appentity "code.byted.org/devgpt/kiwis/codeassist/app/entity"
	appservice "code.byted.org/devgpt/kiwis/codeassist/app/service"
	"code.byted.org/devgpt/kiwis/codeassist/chat/entity"
	chatentity "code.byted.org/devgpt/kiwis/codeassist/chat/entity"
	"code.byted.org/devgpt/kiwis/codeassist/chat/pack"
	"code.byted.org/devgpt/kiwis/codeassist/chat/service"
	"code.byted.org/devgpt/kiwis/codeassist/chat/service/homepage"
	"code.byted.org/devgpt/kiwis/codeassist/common/constant"
	journalentity "code.byted.org/devgpt/kiwis/codeassist/journal/entity"
	resolverentity "code.byted.org/devgpt/kiwis/codeassist/resolver/entity"
	copilotentity "code.byted.org/devgpt/kiwis/copilotstack/entity"
	"code.byted.org/devgpt/kiwis/lib/apierror"
	"code.byted.org/devgpt/kiwis/lib/kitex"
	"code.byted.org/devgpt/kiwis/lib/metrics"
	libstream "code.byted.org/devgpt/kiwis/lib/stream"
	"code.byted.org/devgpt/kiwis/lib/util"
	"code.byted.org/devgpt/kiwis/port/fornax"
	"code.byted.org/devgpt/kiwis/port/llmops"
)

const (
	firstTokenLatencyWarnLogThreshold = 2 * 1000 // 2s，超过阈值打warn日志

	verboseContent = "{\"msg_type\":\"seed_intention\",\"data\":\"{\\\"intention\\\": \\\"multi_agent\\\", \\\"detail\\\": \\\"Agent-Code\\\", \\\"is_deep_think\\\": \\\"1\\\"}\"}"
)

type ChatHandler struct {
	AppService      appservice.AppService
	ChatService     service.ChatService
	RouterService   service.RouterService
	FornaxClient    fornax.Client
	HomepageService service.HomepageService
	SearchService   service.SearchService
}

func (h *ChatHandler) Chat(req *codeassist.ChatRequest, stream codeassist.AssistantService_ChatServer) error {
	if req == nil || req.GetUserMessage() == nil {
		return kitex.NewKitexError(common.ErrorCode_ErrParamInvalid, errors.New("param invalid"))
	}
	startTime := time.Now()
	ctx := stream.Context()
	ctx = alicectx.NewCtxFromRPC(ctx)
	log.V1.CtxInfo(ctx, "[Chat] request: %s", util.MarshalStruct(req))

	span, ctx, err := h.FornaxClient.StartQuerySpan(ctx, journalentity.SpanLLMChat)
	if err != nil {
		log.V1.CtxWarn(ctx, "[Chat] start query span error: %s", err)
	}
	defer span.Finish(ctx)
	message, err := pack.GetMessageFromDTO(req.GetUserMessage())
	if err != nil {
		log.V1.CtxError(ctx, "[Chat] convert message to message error: %s", err)
		return kitex.NewKitexError(common.ErrorCode_ErrParamInvalid, errors.New("param invalid"))
	}
	historyMessages := make([]*entity.Message, 0, len(req.HistoryMessages))
	for _, msg := range req.HistoryMessages {
		m, err := pack.GetMessageFromDTO(msg)
		if err != nil {
			log.V1.CtxError(ctx, "[Chat] convert history messages to message error: %s", err)
			return kitex.NewKitexError(common.ErrorCode_ErrParamInvalid, errors.New("param invalid"))
		}
		if m.Role != copilotentity.RoleUser && m.Role != copilotentity.RoleAssistant {
			continue
		}
		historyMessages = append(historyMessages, m)
	}
	abVariables, err := pack.GetAbParamsFromDTO(req.GetAbVariables())
	if err != nil {
		log.V1.CtxError(ctx, "[Chat] failed to get ab params: %v", err)
	}

	// build chat context
	routerOpt := &service.RouterOption{
		UserID:            req.GetUserID(),
		ChatSource:        chatentity.ChatSourceCodeassistSkill,
		UserMessage:       message,
		HistoryMessages:   historyMessages, // 原始历史消息按时间正序排列
		GlobalAbVariables: abVariables,
		UseDeepThink:      req.GetUseDeepThink(),
		UseAutoCot:        pack.GetUseAutoCotSwitchStatusFromInput(req.UseAutoCot),
	}
	chatContext, err := h.RouterService.Router(ctx, routerOpt)
	if err != nil {
		log.V1.CtxError(ctx, "failed to route context err: %v", err)
		return kitex.NewKitexError(common.ErrorCode_ErrInternal, err)
	}

	var (
		chunkCount         int
		firstTokenEndedAt  *time.Time
		secondTokenEndedAt *time.Time
		resultCh           *libstream.RecvChannel[entity.ChatEvent]
	)

	defer func() { // add metrics
		h.emitChatMetrics(ctx, chatContext, startTime, firstTokenEndedAt, secondTokenEndedAt, chunkCount, err, span)
	}()

	// stream chat
	resultCh, err = h.ChatService.Chat(ctx, chatContext)
	if err != nil {
		log.V1.CtxError(ctx, "failed to do chat, err: %v", err)
		return kitex.NewKitexError(common.ErrorCode_ErrInternal, err)
	}

loop:
	for {
		select {
		case err = <-resultCh.ErrorChannel:
			if err == nil {
				continue
			}
			log.V1.CtxError(ctx, "got error while streaming chat response: %v", err)
			e := &codeassist.StreamChatResponse{
				EventType:    codeassist.ChatEventType_OUTPUT,
				EventContent: nil,
				IsFinish:     true,
				ErrorMessage: apierror.GetErrorMessage(err),
				ErrorCode:    0,
				BaseResp:     nil,
			}
			_ = stream.Send(e)
			resultCh.Close()
			continue loop
		case event, ok := <-resultCh.DataChannel:
			if !ok {
				// Finish and exit.
				log.V1.CtxInfo(ctx, "chat data channel is closed, exited")
				break loop
			}
			var publishErr error
			switch {
			case event.Output != nil:
				chunkCount++
				if chunkCount == 1 { // 第一个 chunk
					firstTokenEndedAt = lo.ToPtr(time.Now())
					firstTokenLatency := time.Since(startTime).Milliseconds()
					if firstTokenLatency > firstTokenLatencyWarnLogThreshold {
						log.V1.CtxWarn(ctx, "chat first chunk latency: %dms", firstTokenLatency)
					} else {
						log.V1.CtxInfo(ctx, "chat first chunk latency: %dms", firstTokenLatency)
					}
				}
				if chunkCount == 2 { // 第二个 chunk
					secondTokenEndedAt = lo.ToPtr(time.Now())
					log.V1.CtxInfo(ctx, "chat second chunk latency: %dms", time.Since(startTime).Milliseconds())
				}
				e := &codeassist.StreamChatResponse{
					EventType: codeassist.ChatEventType_OUTPUT,
					EventContent: &codeassist.ChatEventContent{
						Content:     event.Output.Content,
						ContentType: pack.ContentTypeToDTO(event.Output.ContentType),
						Ext:         event.Output.Ext,
						ID:          event.Output.ID,
					},
					IsFinish:     event.Output.IsFinish,
					ErrorMessage: "",
					ErrorCode:    0,
				}
				publishErr = stream.Send(e)
			case event.Suggestion != nil:
				e := &codeassist.StreamChatResponse{
					EventType: codeassist.ChatEventType_SUGGEST,
					EventContent: &codeassist.ChatEventContent{
						Content:     event.Suggestion.Content,
						ContentType: pack.ContentTypeToDTO(event.Suggestion.ContentType),
					},
					IsFinish:     event.Suggestion.IsFinish,
					ErrorMessage: "",
					ErrorCode:    0,
				}
				publishErr = stream.Send(e)
			}
			if publishErr != nil {
				// Usually, this is caused by user interrupted AI generating by click `stop generating` button.
				log.V1.CtxInfo(ctx, "failed to publish chat response to sse: %v", publishErr)
				resultCh.Close()
				break loop
			}
		}
	}
	log.V1.CtxInfo(ctx, "finishing streaming chat response, total chunk latency: %dms", time.Since(startTime).Milliseconds())
	return nil
}

func (h *ChatHandler) PromptsRender(ctx context.Context, req *codeassist.PromptsRenderRequest) (*codeassist.PromptsRenderResponse, error) {
	log.V1.CtxInfo(ctx, "[PromptsRender] request: %s", util.MarshalStruct(req))
	messages := lo.Map(req.GetMessages(), func(message *codeassist.PromptMessage, _ int) *llmops.Message {
		return &llmops.Message{
			Role:     message.GetRole(),
			Content:  message.GetContent(),
			MetaData: lo.ToPtr(message.GetMetaData()),
		}
	})
	promptMessages, err := appentity.GetPromptMessages(messages)
	if err != nil {
		log.V1.CtxError(ctx, "failed to get prompt messages, err: %v", err)
		return nil, errors.WithMessage(err, "failed to get prompt messages")
	}
	renderContext := &chatentity.RenderContext{
		PromptTemplate: &appentity.PromptConfig{
			Name:   "PromptsRender",
			Prompt: promptMessages,
		},
	}
	variables := &chatentity.RenderVariables{}
	/* 输入的参数规格
	{
		"user_message": {
			"role": "user",
			"content": "xxxxx",
			"context_blocks": [
				{
					"type": "",
					"file_block": {
						"identifiers": [
							{
								"type": "",
								"resource_key": ""
							}
						]
					},
					"directory_block": {
						"identifier": {
							"type": "",
							"resource_key": ""
						}
					},
					"repo_block": {
						"identifier": {
							"type": "",
							"resource_key": ""
						}
					},
					"model_gen_block": {
						"content": "",
						"language": ""
					}
				}
			]
			"context_variables": {
				"language": "",
				"command_name": "",
				"source_type": "",
				"source_key": "",
				"select_file_path": "",
				"select_file_uri": "",
				"select_content": "",
				"file_content": "",
				"select_range": {
					"start_line": 0,
					"end_line": 999
				},
				"visible_range": {
					"start_line": 0,
					"end_line": 999
				},
				"cursor_line": 0
			}
		},
		"history_messages": [
			{
				"role": "user",
				"content": "xxxxx",
				"context_blocks": [] // 同user_message
			},
			{
				"role": "assistant",
				"content": "xxxxx"
			}
		],
		"resolve_context":[
			{
				"resolver_id":"code_current_editor_resolver",
				"variables": {
					"code_current_editor": {}
				},
				"references": {
					"references": []
				}
			},
			{
				"resolver_id":"code_user_message_resolver",
				"variables": {
					"code_user_message": []
				},
				"references": {
					"references": []
				}
			}
		]
	}
	*/
	log.V1.CtxInfo(ctx, "variables: %s", req.GetVariables())
	variablesMap := map[string]any{}
	err = json.Unmarshal([]byte(req.GetVariables()), &variablesMap)
	if err != nil {
		log.V1.CtxError(ctx, "failed to unmarshal variables, err: %v", err)
		return nil, errors.WithMessage(err, "unmarshal variables failed")
	}
	if len(variablesMap) > 0 {
		// 需要把user_message、history_messages、resolve_context 三个key拿出来做反序列化
		// user_message 是必须的
		user_message := &chatentity.Message{}
		if userMessage, ok := variablesMap["user_message"]; ok {
			if err := json.Unmarshal([]byte(userMessage.(string)), user_message); err != nil {
				log.V1.CtxError(ctx, "failed to unmarshal user_message, err: %v", err)
				return nil, errors.WithMessage(err, "unmarshal user_message failed")
			}
			log.V1.CtxInfo(ctx, "user_message: %v", user_message)
			variables.UserMessage = user_message
		} else {
			log.V1.CtxError(ctx, "user_message is required")
			return nil, errors.New("user_message is required")
		}
		// history_messages 是可选的
		history_messages := make([]*chatentity.Message, 0)
		if historyMessages, ok := variablesMap["history_messages"]; ok {
			if err := json.Unmarshal([]byte(historyMessages.(string)), &history_messages); err != nil {
				log.V1.CtxError(ctx, "failed to unmarshal history_messages, err: %v", err)
				return nil, errors.WithMessage(err, "unmarshal history_messages failed")
			}
			log.V1.CtxInfo(ctx, "history_messages: %v", history_messages)
			variables.HistoryMessages = history_messages
		}
		// resolve_context 是可选的
		resolve_context := make([]*resolverentity.ResolvedContext, 0)
		if resolveContext, ok := variablesMap["resolve_context"]; ok {
			if err := json.Unmarshal([]byte(resolveContext.(string)), &resolve_context); err != nil {
				log.V1.CtxError(ctx, "failed to unmarshal resolve_context, err: %v", err)
				return nil, errors.WithMessage(err, "unmarshal resolve_context failed")
			}
			log.V1.CtxInfo(ctx, "resolve_context: %v", resolve_context)
			variables.ResolveContext = resolve_context
		}
	} else {
		log.V1.CtxError(ctx, "variables is required")
		return nil, errors.New("variables is required")
	}

	renderContext.Variables = variables

	result, err := h.ChatService.PromptsRender(ctx, renderContext)
	if err != nil {
		log.V1.CtxError(ctx, "failed to do prompts render, err: %v", err)
		return nil, err
	}
	resp := &codeassist.PromptsRenderResponse{
		Messages: lo.Map(result.PromptMessages, func(message *llmops.Message, _ int) *codeassist.PromptMessage {
			return &codeassist.PromptMessage{
				Role:     message.Role,
				Content:  message.Content,
				MetaData: message.MetaData,
			}
		}),
	}
	return resp, nil
}

func (h *ChatHandler) E2EPromptsRender(ctx context.Context, req *codeassist.E2EPromptsRenderRequest) (*codeassist.E2EPromptsRenderResponse, error) {
	log.V1.CtxInfo(ctx, "[E2EPromptsRender] request: %s", util.MarshalStruct(req))
	messages := lo.Map(req.GetMessages(), func(message *codeassist.PromptMessage, _ int) *llmops.Message {
		return &llmops.Message{
			Role:     message.GetRole(),
			Content:  message.GetContent(),
			MetaData: lo.ToPtr(message.GetMetaData()),
		}
	})
	promptMessages, err := appentity.GetPromptMessages(messages)
	if err != nil {
		log.V1.CtxError(ctx, "failed to get prompt messages, err: %v", err)
		return nil, errors.WithMessage(err, "failed to get prompt messages")
	}
	// init LLMOps render context
	renderContext := &chatentity.RenderContext{
		PromptTemplate: &appentity.PromptConfig{
			Name:   "PromptsRender",
			Prompt: promptMessages,
		},
	}

	// unmarshal case info
	var caseInfo codeassist.CaseInfo
	err = json.Unmarshal([]byte(req.CaseInfo), &caseInfo)
	if err != nil {
		log.V1.CtxError(ctx, "failed to unmarshal case info, err: %v, caseInfo is %+v", err, req.CaseInfo)
		return nil, errors.Wrap(err, "unmarshal case info failed")
	}

	var embeddingTopN, embeddingRecallNums, rerankRecallNums int
	if len(caseInfo.EmbeddingTopN) > 0 {
		embeddingTopN, err = strconv.Atoi(caseInfo.EmbeddingTopN)
		if err != nil {
			log.V1.CtxError(ctx, "failed to parse embedding_top_n, err: %v", err)
			return nil, errors.Wrap(err, "failed to parse embedding_top_n")
		}
	}
	if len(caseInfo.EmbeddingRecallNums) > 0 {
		embeddingRecallNums, err = strconv.Atoi(caseInfo.EmbeddingRecallNums)
		if err != nil {
			log.V1.CtxError(ctx, "failed to parse embedding_recall_nums, err: %v", err)
			return nil, errors.Wrap(err, "failed to parse embedding_recall_nums")
		}
	}
	if len(caseInfo.RerankRecallNums) > 0 {
		rerankRecallNums, err = strconv.Atoi(caseInfo.RerankRecallNums)
		if err != nil {
			log.V1.CtxError(ctx, "failed to parse rerank_recall_nums, err: %v", err)
			return nil, errors.Wrap(err, "failed to parse rerank_recall_nums")
		}
	}

	ckgRetrieveParams := &entity.CKGRetrieveParams{
		EmbeddingTopN:       embeddingTopN,
		EmbeddingRecallNums: embeddingRecallNums,
		RerankRecallNums:    rerankRecallNums,
	}
	renderContext.Variables = &chatentity.RenderVariables{
		CKGRetrieveParams: ckgRetrieveParams,
	}

	// init blocks
	blocks := make([]*codeassist.ContextBlock, 0)
	sourceType := ""
	resourceKey := caseInfo.ResourceKey
	// 从case info中读取tag，判断为「仓库」时，添加repo block，并给resource id赋值
	switch caseInfo.Tags {
	case "仓库":
		blocks = append(blocks, &codeassist.ContextBlock{
			Type: codeassist.ContextBlockType_REPO_BLOCK,
			RepoBlock: &codeassist.RepoBlock{
				Identifier: &codeassist.ContextIdentifier{
					Type:        codeassist.ResourceType_Repository,
					ResourceKey: caseInfo.ResourceKey,
				},
			},
		})
		sourceType = "repo"
	case "单文件":
		blocks = append(blocks, &codeassist.ContextBlock{
			Type: codeassist.ContextBlockType_FILE_BLOCK,
			FileBlock: &codeassist.FileBlock{
				Identifiers: []*codeassist.ContextIdentifier{
					{
						Type:        codeassist.ResourceType_File,
						ResourceKey: caseInfo.ResourceKey,
					},
				},
			},
		})
		sourceType = "file"
	case "多文件":
		// 多文件要求resource key使用逗号隔开
		tempResourceKey := strings.Split(caseInfo.ResourceKey, ",")
		blocks = lo.Map(tempResourceKey, func(resource string, _ int) *codeassist.ContextBlock {
			return &codeassist.ContextBlock{
				Type: codeassist.ContextBlockType_FILE_BLOCK,
				FileBlock: &codeassist.FileBlock{
					Identifiers: []*codeassist.ContextIdentifier{
						{
							Type:        codeassist.ResourceType_File,
							ResourceKey: resource,
						},
					},
				},
			}
		})
		sourceType = "file"
		resourceKey = caseInfo.SelectFileUri
	case "文件夹":
		blocks = append(blocks, &codeassist.ContextBlock{
			Type: codeassist.ContextBlockType_DIRECTORY_BLOCK,
			DirectoryBlock: &codeassist.DirectoryBlock{
				Identifier: &codeassist.ContextIdentifier{
					Type:        codeassist.ResourceType_Directory,
					ResourceKey: caseInfo.ResourceKey,
				},
			},
		})
		sourceType = "directory"
	case "代码块":
		blocks = append(blocks, &codeassist.ContextBlock{
			Type: codeassist.ContextBlockType_MODELGEN_BLOCK,
			ModelGenBlock: &codeassist.ModelGenBlock{
				Content:  caseInfo.ReferenceContent,
				Language: caseInfo.ReferenceLanguage,
			},
		})
	}

	var selectRange, visibleRange entity.Range
	if cast.ToInt(caseInfo.SelectCodeStartLine) != 0 || cast.ToInt(caseInfo.SelectCodeEndLine) != 0 {
		selectRange.StartLine = cast.ToInt(caseInfo.SelectCodeStartLine)
		selectRange.EndLine = cast.ToInt(caseInfo.SelectCodeEndLine)
	}
	if cast.ToInt(caseInfo.VisibleCodeStartLine) != 0 || cast.ToInt(caseInfo.VisibleCodeEndLine) != 0 {
		visibleRange.StartLine = cast.ToInt(caseInfo.VisibleCodeStartLine)
		visibleRange.EndLine = cast.ToInt(caseInfo.VisibleCodeEndLine)
	}
	commandName := "default"
	if len(caseInfo.CommandName) > 0 {
		commandName = caseInfo.CommandName
	}
	appName := appentity.AppNameProjectAsk
	switch caseInfo.AppName {
	case "项目问答":
		appName = appentity.AppNameProjectAsk
	case "自由问答":
		appName = appentity.AppNameDirectAsk
	}
	app, err := h.AppService.GetAppByName(ctx, appName, nil)
	if err != nil {
		log.V1.CtxError(ctx, "failed to get app `%s`, err: %v", appName, err)
		return nil, errors.WithMessagef(err, "failed to get app `%s`", appName)
	}

	// init context variables, file_content and cursor_line is not used
	variables, err := json.Marshal(entity.ContextVariables{
		Language:       caseInfo.Language,
		CommandName:    commandName,
		SourceType:     sourceType,
		SourceKey:      resourceKey,
		SelectFilePath: caseInfo.FilePath,
		SelectFileURI:  caseInfo.SelectFileUri,
		SelectContent:  caseInfo.SelectCodeContent,
		SelectRange:    &selectRange,
		VisibleRange:   &visibleRange,
		FileContent:    caseInfo.FileContent,
	})

	if err != nil {
		log.V1.CtxError(ctx, "failed to marshal context variables, error: %s", err)
		return nil, errors.New("marshal context variables failed")
	}

	tempVariable := string(variables)
	userMessage := codeassist.Message{
		Role:             codeassist.ChatMessageRole_USER,
		Content:          caseInfo.Query,
		ContextBlocks:    blocks,
		ContextVariables: &tempVariable,
		SectionID:        nil,
	}

	message, err := pack.GetMessageFromDTO(&userMessage)
	if err != nil {
		log.V1.CtxError(ctx, "[Chat] convert message to message error: %s", err)
		return nil, errors.New("UserMessage param invalid")
	}

	historyMessages := make([]*chatentity.Message, 0)
	if len(caseInfo.ChatHistory) > 0 {
		if err := json.Unmarshal([]byte(caseInfo.ChatHistory), &historyMessages); err != nil {
			log.V1.CtxError(ctx, "failed to unmarshal history_messages, err: %v", err)
			return nil, errors.WithMessage(err, "unmarshal history_messages failed")
		}
		log.V1.CtxInfo(ctx, "history_messages: %v", historyMessages)
	}

	var userID int64
	userID = 1541952456893803 // 默认为 maoyu@bytedance的UID，需要通过UID获取上传的文件信息，后续视情况从外部传入
	if cast.ToInt64(caseInfo.UserId) != 0 {
		userID = cast.ToInt64(caseInfo.UserId)
	}
	// build chat context without history
	chatContext := &chatentity.ChatContext{
		UserID:          userID,
		ChatSource:      chatentity.ChatSourceCodeassistSkill,
		UserMessage:     message,
		HistoryMessages: historyMessages,
		App:             app,
		IsOffline:       true,
	}

	resultCh, err := h.ChatService.OfflineChat(ctx, chatContext, renderContext)
	if err != nil {
		log.V1.CtxError(ctx, "failed to do offline chat, err: %v", err)
		return nil, err
	}

	extra := make(map[string]any)
	msg := make([]*llmops.Message, 0)

loop:
	for {
		select {
		case err = <-resultCh.ErrorChannel:
			if err == nil {
				continue
			}
			log.V1.CtxError(ctx, "got error while streaming chat response: %v", err)
			resultCh.Close()
			continue loop
		case event, ok := <-resultCh.DataChannel:
			if !ok {
				// Finish and exit.
				log.V1.CtxInfo(ctx, "chat data channel is closed, exited")
				break loop
			}
			switch {
			case len(event.ResolverContent) > 0:
				for k, v := range event.ResolverContent {
					if _, ok := extra[k]; ok {
						log.V1.CtxError(ctx, "failed to merge resolver content to extra, conflict key: %s", k)
						continue
					}
					extra[k] = v
				}
			case len(event.PromptContent) != 0 || len(event.ResolverTokenUsage) > 0 || len(event.FinalResolverContent) > 0:
				for _, v := range event.PromptContent {
					msg = append(msg, &llmops.Message{
						Role:    v.Role,
						Content: v.Content,
					})
				}

				promptTokenUsageMap := lo.Associate[*chatentity.ResolverTokenUsage, string, any](
					event.ResolverTokenUsage,
					func(item *chatentity.ResolverTokenUsage) (string, any) {
						return item.Name, item
					},
				)
				extra["resolver_token_usage"] = promptTokenUsageMap

				for k, v := range event.FinalResolverContent {
					switch k {
					case resolverentity.CodeUserMessageVariableKey, resolverentity.CodeCurrentEditorVariableKey:
						extra["final_"+k] = v
					}
				}
			}
		}
	}

	extraStr, err := json.Marshal(extra)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to marshal extra in offline progress")
	}

	resp := &codeassist.E2EPromptsRenderResponse{
		Messages: lo.Map(msg, func(message *llmops.Message, _ int) *codeassist.PromptMessage {
			return &codeassist.PromptMessage{
				Role:     message.Role,
				Content:  message.Content,
				MetaData: message.MetaData,
			}
		}),
		Extra:    string(extraStr),
		BaseResp: nil,
	}
	return resp, nil
}

func (h *ChatHandler) FlowStream(req *bothook.FlowHookRequest, stream codeassist.HookService_FlowStreamServer) error {
	startTime := time.Now()
	ctx := stream.Context()
	log.V1.CtxInfo(ctx, "[FlowStream] request: %s", util.MarshalStruct(req))
	if req == nil || req.GetBotContext() == nil {
		return kitex.NewKitexError(common.ErrorCode_ErrParamInvalid, errors.New("param invalid"))
	}
	span, ctx, err := h.FornaxClient.StartQuerySpan(ctx, "FlowStream")
	if err != nil {
		log.V1.CtxWarn(ctx, "[FlowStream] start query span error: %s", err)
	}
	defer span.Finish(ctx)

	botContext := req.GetBotContext()
	botID := botContext.GetBotId()
	ext := req.GetBotContext().GetSceneContext()

	abVariables, err := pack.GetAbParamsFromBotDTO(req.GetBotContext().SceneContext)
	if err != nil {
		log.V1.CtxError(ctx, "[FlowStream] failed to get ab params from bot: %v", err)
	}
	// build chat context
	routerOpt := &service.RouterOption{
		UserID:      util.FromString(botContext.GetConnectorUid()),
		ChatSource:  chatentity.ChatSourceAgentCode,
		UserMessage: pack.GetMessageFromBotDTO(botContext.GetMessage()),
		HistoryMessages: lo.FilterMap(botContext.GetChatContext(), func(messageDto *botengine.Message, _ int) (*entity.Message, bool) {
			if messageDto == nil {
				return nil, false
			}
			if messageDto.GetRole() != botengine.MessageRoleUser && messageDto.GetRole() != botengine.MessageRoleAssistant {
				return nil, false
			}
			message := pack.GetMessageFromBotDTO(messageDto)
			if message == nil {
				return nil, false
			}
			return message, true
		}), // 原始历史消息按时间正序排列
		GlobalAbVariables: abVariables,
		UseDeepThink:      pack.GetDeepThinkSwitchStatusFromBotEXT(ext),
		UseAutoCot:        pack.GetUseAutoCotSwitchStatusFromBotEXT(ext),
	}
	chatContext, err := h.RouterService.Router(ctx, routerOpt)
	if err != nil {
		log.V1.CtxError(ctx, "failed to route context err: %v", err)
		return kitex.NewKitexError(common.ErrorCode_ErrInternal, err)
	}
	var (
		chunkCount         int
		firstTokenEndedAt  *time.Time
		secondTokenEndedAt *time.Time
		resultCh           *libstream.RecvChannel[entity.ChatEvent]
	)

	defer func() { // add metrics
		h.emitChatMetrics(ctx, chatContext, startTime, firstTokenEndedAt, secondTokenEndedAt, chunkCount, err, span)
	}()

	// stream chat
	resultCh, err = h.ChatService.Chat(ctx, chatContext)
	if err != nil {
		log.V1.CtxError(ctx, "[FlowStream] failed to do chat, bot_id: %v, err: %v", botID, err)
		return kitex.NewKitexError(common.ErrorCode_ErrInternal, err)
	}

loop:
	for {
		select {
		case err = <-resultCh.ErrorChannel:
			if err == nil {
				continue
			}
			log.V1.CtxError(ctx, "[FlowStream] got error while streaming chat response: %v", err)
			_ = stream.Send(&bothook.StreamPacket{
				ContentType: int64(bothook.PacketContentType_Answer),
				IsFinish:    false,
				BaseResp: &base.BaseResp{
					StatusMessage: apierror.GetErrorMessage(err),
					StatusCode:    1, // has error
				},
			})
			resultCh.Close()
			continue loop
		case event, ok := <-resultCh.DataChannel:
			if !ok {
				// Finish and exit.
				_ = stream.Send(&bothook.StreamPacket{
					ContentType: int64(bothook.PacketContentType_Answer),
					Content:     "",
					IsFinish:    true,
				})
				log.V1.CtxInfo(ctx, "[FlowStream] chat data channel is closed, exited")
				break loop
			}
			var publishErr error
			switch {
			case event.Output != nil:
				chunkCount++
				if chunkCount == 1 { // 第一个 chunk
					firstTokenEndedAt = lo.ToPtr(time.Now())
					firstTokenLatency := time.Since(startTime).Milliseconds()
					if firstTokenLatency > firstTokenLatencyWarnLogThreshold {
						log.V1.CtxWarn(ctx, "[FlowStream] chat first chunk latency: %dms", firstTokenLatency)
					} else {
						log.V1.CtxInfo(ctx, "[FlowStream] chat first chunk latency: %dms", firstTokenLatency)
					}
				}
				if chunkCount == 2 { // 第二个 chunk
					secondTokenEndedAt = lo.ToPtr(time.Now())
					log.V1.CtxInfo(ctx, "[FlowStream] chat second chunk latency: %dms", time.Since(startTime).Milliseconds())
				}
				publishErr = stream.Send(&bothook.StreamPacket{
					ContentType: int64(bothook.PacketContentType_Answer),
					Content:     event.Output.Content,
					IsFinish:    false,
				})
			}
			if publishErr != nil {
				// Usually, this is caused by user interrupted AI generating by click `stop generating` button.
				log.V1.CtxInfo(ctx, "[FlowStream] failed to publish chat response to sse: %v", publishErr)
				resultCh.Close()
				break loop
			}
		}
	}
	log.V1.CtxInfo(ctx, "[FlowStream] finishing streaming chat response")

	if err != nil {
		log.V1.CtxError(ctx, "[FlowStream] flow streaming chat error. bot_id: %v, err: %v", botID, err)
		return kitex.NewKitexError(common.ErrorCode_ErrInternal, err)
	}
	return nil
}

// FlowHook implements the AssistantServiceImpl interface. return thrift stream rpc hook for coze bot call rpc method.
// Docs: https://bytedance.larkoffice.com/docx/IR5idikMeo0Uvgx7FezcL1Rwnue
func (h *ChatHandler) FlowHook(ctx context.Context, req *bothook.FlowHookRequest) (r *bothook.FlowHookResponse, err error) {
	if req == nil {
		log.V1.CtxError(ctx, "request is nil")
		return nil, kitex.NewKitexError(common.ErrorCode_ErrParamInvalid, err)
	}
	resp := &bothook.FlowHookResponse{
		FlowType: bothook.FlowType_Stream,
		StreamFlow: &bothook.StreamFlow{
			StreamServiceType: bothook.StreamServiceType_ThriftStreaming,
			Uri:               constant.CozeBotFlowHookURI,
		},
	}
	return resp, nil
}

func (h *ChatHandler) emitChatMetrics(
	ctx context.Context, chatContext *chatentity.ChatContext, startTime time.Time,
	firstTokenEndedAt, secondTokenEndedAt *time.Time, chunkCount int, err error, span fornaxtrace.QuerySpan,
) {
	if chatContext == nil || chatContext.App == nil {
		return
	}
	var (
		appName     = chatContext.App.Name
		appVersion  = chatContext.App.Version
		commandName = ""
	)
	if chatContext.UserMessage != nil && chatContext.UserMessage.ContextVariables != nil {
		commandName = chatContext.UserMessage.ContextVariables.CommandName
	}
	tag := &metrics.CodeAssistChatTag{
		AppName:     appName,
		AppVersion:  appVersion,
		ChatSource:  chatContext.GetChatSource().String(),
		IsLogin:     strconv.FormatBool(chatContext.IsLogin()),
		IsError:     strconv.FormatBool(err != nil),
		CommandName: commandName,
		HitVLM:      chatContext.HitVLM,
		CotMode:     int(chatContext.UseAutoCot),
	}
	spanTags := make(map[string]any)
	spanTags["app_name"] = appName
	spanTags["app_version"] = appVersion
	spanTags["chat_source"] = chatContext.GetChatSource().String()
	// add latency metrics
	if firstTokenEndedAt != nil {
		firstTokenLatency := lo.FromPtr(firstTokenEndedAt).Sub(startTime).Milliseconds()
		lastTokenLatency := time.Since(lo.FromPtr(firstTokenEndedAt)).Milliseconds()
		_ = metrics.CodeAssistMetric.ChatFirstTokenLatency.WithTags(tag).Observe(float64(firstTokenLatency))
		_ = metrics.CodeAssistMetric.ChatLastTokenLatency.WithTags(tag).Observe(float64(lastTokenLatency))
		spanTags["first_token_latency"] = firstTokenLatency
		spanTags["last_token_latency"] = lastTokenLatency
		if chunkCount > 0 {
			avgTokenLatency := time.Since(lo.FromPtr(firstTokenEndedAt)).Milliseconds() / int64(chunkCount)
			_ = metrics.CodeAssistMetric.ChatAvgTokenLatency.WithTags(tag).Observe(float64(avgTokenLatency))
			spanTags["avg_token_latency"] = avgTokenLatency
		}
		totalTokenLatency := time.Since(startTime).Milliseconds()
		_ = metrics.CodeAssistMetric.ChatTotalTokenLatency.WithTags(tag).Observe(float64(totalTokenLatency))
		spanTags["total_token_latency"] = totalTokenLatency
	}
	// add second chunk latency metrics
	if secondTokenEndedAt != nil {
		secondTokenLatency := lo.FromPtr(secondTokenEndedAt).Sub(startTime).Milliseconds()
		_ = metrics.CodeAssistMetric.ChatSecondTokenLatency.WithTags(tag).Observe(float64(secondTokenLatency))
		spanTags["second_token_latency"] = secondTokenLatency
	}
	if err != nil {
		spanTags["status"] = "fail"
	} else {
		spanTags["status"] = "success"
	}
	// add throughput metrics
	_ = metrics.CodeAssistMetric.ChatThroughput.WithTags(tag).Add(1)

	// add span traces
	span.SetTag(ctx, spanTags)
}

func (h *ChatHandler) HomePage(ctx context.Context, req *codeassist.HomePageRequest) (*codeassist.HomePageResponse, error) {
	abVariables, err := pack.GetAbParamsFromDTO(req.GetAbVariables())
	if err != nil {
		log.V1.CtxError(ctx, "[HomePage] failed to get ab params: %v", err)
	}

	homePageStr := h.HomepageService.GetHomepageConfig(ctx, abVariables)
	// 兜底获取本地配置
	if homePageStr == "" {
		homePageStr = homepage.Templates
	}

	promptTemplateStr := h.HomepageService.GetPromptTemplate(ctx, req.GetLanguage(), abVariables)
	// 兜底获取本地配置
	if promptTemplateStr == "" {
		promptTemplateStr = homepage.PromptTemplatesDefault
	}
	return &codeassist.HomePageResponse{
		HomePageContent:       homePageStr,
		PromptTemplateContent: promptTemplateStr,
	}, nil
}
