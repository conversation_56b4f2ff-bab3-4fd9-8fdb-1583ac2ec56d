package pack

import (
	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/codeassist"
	chatentity "code.byted.org/devgpt/kiwis/codeassist/chat/entity"
)

func ContentTypeToDTO(contentType chatentity.ChatContentType) codeassist.ChatContentType {
	switch contentType {
	case chatentity.ChatContentTypeText:
		return codeassist.ChatContentType_TXT
	case chatentity.ChatContentTypeImage:
		return codeassist.ChatContentType_Image
	case chatentity.ChatContentTypeBlockText:
		return codeassist.ChatContentType_BlockText
	case chatentity.ChatContentTypeBlockResearchProcessCard:
		return codeassist.ChatContentType_BlockResearchProcessCard
	case chatentity.ChatContentTypeBlockCode:
		return codeassist.ChatContentType_BlockCode
	case chatentity.ChatContentTypeBlockResearchWebpage:
		return codeassist.ChatContentType_BlockResearchWebpage
	case chatentity.ChatContentTypeBlockFileOperation:
		return codeassist.ChatContentType_BlockFileOperation
	case chatentity.ChatContentTypeBlockFile:
		return codeassist.ChatContentType_BlockFile
	case chatentity.ChatContentTypeBlockArtifactCodeFile:
		return codeassist.ChatContentType_BlockArtifactCodeFile
	case chatentity.ChatContentTypeBlockCodeAssistProcess:
		return codeassist.ChatContentType_BlockCodeAssistProcess
	case chatentity.ChatContentTypeBlockArtifact:
		return codeassist.ChatContentType_BlockArtifact
	}
	return 0
}
