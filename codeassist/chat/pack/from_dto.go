package pack

import (
	"encoding/json"
	"strconv"

	"github.com/samber/lo"

	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/codeassist"
	botengine "code.byted.org/devgpt/kiwis/api/idl/kitex_gen/flow/bot/engine"
	"code.byted.org/devgpt/kiwis/codeassist/chat/entity"
	"code.byted.org/devgpt/kiwis/codeassist/common/constant"
	commonpack "code.byted.org/devgpt/kiwis/codeassist/common/pack"
	contextentity "code.byted.org/devgpt/kiwis/codeassist/context/entity"
	copilotentity "code.byted.org/devgpt/kiwis/copilotstack/entity"
	"code.byted.org/devgpt/kiwis/lib/conv"
	"code.byted.org/devgpt/kiwis/lib/util"
)

func GetMessageFromDTO(dto *codeassist.Message) (*entity.Message, error) {
	if dto == nil {
		return nil, nil
	}
	message := &entity.Message{
		Role:    getChatMessageRoleFromDTO(dto.Role),
		Content: dto.Content,
		ContextBlocks: lo.Map(dto.ContextBlocks, func(item *codeassist.ContextBlock, _ int) *entity.ContextBlock {
			return getContextBlockFromDTO(item)
		}),
	}
	if dto.ContextVariables != nil {
		var variables = new(entity.ContextVariables)
		err := json.Unmarshal([]byte(*dto.ContextVariables), &variables)
		if err != nil {
			return nil, err
		}
		message.ContextVariables = variables
	}
	if dto.SectionID != nil {
		message.SessionID = *dto.SectionID
	}
	if dto.BizInfo != nil {
		message.MessageID = util.ToString(lo.FromPtr(dto.BizInfo.MessageID))
		message.ConversationID = util.ToString(lo.FromPtr(dto.BizInfo.ConversationID))
		message.AnswerID = util.ToString(lo.FromPtr(dto.BizInfo.AnswerID))
		//message.SessionID = util.ToString(lo.FromPtr(dto.BizInfo.SectionID))  // TODO(dingbo.2020): use this section id
	}
	return message, nil
}

func getContextBlockFromDTO(dto *codeassist.ContextBlock) *entity.ContextBlock {
	if dto == nil {
		return nil
	}
	return &entity.ContextBlock{
		Type:           getContextBlockTypeFromDTO(dto.Type),
		FileBlock:      getFileBlockFromDTO(dto.FileBlock),
		DirectoryBlock: getDirectoryBlockFromDTO(dto.DirectoryBlock),
		RepoBlock:      getRepoBlockFromDTO(dto.RepoBlock),
		ModelGenBlock:  getModelGenBlockFromDTO(dto.ModelGenBlock),
		ImageBlock:     getImageBlockFromDTO(dto.ImageBlock),
	}
}

func getImageBlockFromDTO(dto *codeassist.ImageBlock) *entity.ImageBlock {
	if dto == nil {
		return nil
	}
	return &entity.ImageBlock{
		Image: getImageFromDTO(dto.Image),
	}
}

func getImageFromDTO(dto *codeassist.Image) *entity.Image {
	if dto == nil {
		return nil
	}
	return &entity.Image{
		URL:  dto.URL,
		URI:  dto.URI,
		Name: dto.Name,
		MD5:  dto.MD5,
	}
}

func getChatMessageRoleFromDTO(dto codeassist.ChatMessageRole) string {
	switch dto {
	case codeassist.ChatMessageRole_SYSTEM:
		return copilotentity.RoleSystem
	case codeassist.ChatMessageRole_USER:
		return copilotentity.RoleUser
	case codeassist.ChatMessageRole_ASSISTANT:
		return copilotentity.RoleAssistant
	default:
		return ""
	}
}

func getContextBlockTypeFromDTO(dto codeassist.ContextBlockType) constant.ContextBlockType {
	switch dto {
	case codeassist.ContextBlockType_FILE_BLOCK:
		return constant.ContextBlockTypeFileBlock
	case codeassist.ContextBlockType_DIRECTORY_BLOCK:
		return constant.ContextBlockTypeDirectoryBlock
	case codeassist.ContextBlockType_REPO_BLOCK:
		return constant.ContextBlockTypeRepoBlock
	case codeassist.ContextBlockType_MODELGEN_BLOCK:
		return constant.ContextBlockTypeModelgenBlock
	case codeassist.ContextBlockType_IMAGE_BLOCK:
		return constant.ContextBlockTypeImageBlock
	default:
		return ""
	}
}

func getFileBlockFromDTO(dto *codeassist.FileBlock) *entity.FileBlock {
	if dto == nil {
		return nil
	}
	return &entity.FileBlock{
		Identifiers: lo.Map(dto.Identifiers, func(item *codeassist.ContextIdentifier, _ int) *contextentity.ContextIdentifier {
			return getContextIdentifierFromDTO(item)
		}),
	}
}

func getDirectoryBlockFromDTO(dto *codeassist.DirectoryBlock) *entity.DirectoryBlock {
	if dto == nil {
		return nil
	}
	return &entity.DirectoryBlock{
		Identifier: getContextIdentifierFromDTO(dto.Identifier),
	}
}

func getRepoBlockFromDTO(dto *codeassist.RepoBlock) *entity.RepoBlock {
	if dto == nil {
		return nil
	}
	return &entity.RepoBlock{
		Identifier: getContextIdentifierFromDTO(dto.Identifier),
	}
}

func getModelGenBlockFromDTO(dto *codeassist.ModelGenBlock) *entity.ModelGenBlock {
	if dto == nil {
		return nil
	}
	return &entity.ModelGenBlock{
		Content:  dto.Content,
		Language: dto.Language,
	}
}

func getContextIdentifierFromDTO(dto *codeassist.ContextIdentifier) *contextentity.ContextIdentifier {
	if dto == nil {
		return nil
	}
	contextIdentifier := &contextentity.ContextIdentifier{
		Type:        commonpack.GetResourceTypeFromDTO(dto.Type),
		ResourceKey: dto.ResourceKey,
	}
	if dto.Name != nil {
		contextIdentifier.Name = *dto.Name
	}
	return contextIdentifier
}

func GetMessageFromBotDTO(dto *botengine.Message) *entity.Message {
	if dto == nil {
		return nil
	}
	message := &entity.Message{
		Role:    getChatMessageRoleFromBotDTO(dto.Role),
		Content: dto.Content,
	}
	if dto.BizInfo != nil && dto.BizInfo.GetSectionId() != 0 {
		message.SessionID = strconv.FormatInt(dto.BizInfo.GetSectionId(), 10)
	}
	return message
}

func getChatMessageRoleFromBotDTO(dto string) string {
	switch dto {
	case botengine.MessageRoleSystem:
		return copilotentity.RoleSystem
	case botengine.MessageRoleUser:
		return copilotentity.RoleUser
	case botengine.MessageRoleAssistant:
		return copilotentity.RoleAssistant
	default:
		return ""
	}
}

func GetAbParamsFromDTO(dto string) (abVariables map[string]any, err error) {
	if dto == "" {
		return
	}
	var variables map[string]any
	if err = json.Unmarshal([]byte(dto), &variables); err != nil {
		return
	}
	abVariablesStr := conv.DefaultAny[string](variables[entity.FlowSamanthaAgentParam])
	if abVariablesStr != "" { // such as "{\"marscode_config\":{\"app_version\":\"ab_test_v1\"}}"
		err = json.Unmarshal([]byte(abVariablesStr), &abVariables)
		if err != nil {
			return
		}
		abVariables = conv.DefaultAny[map[string]any](abVariables["marscode_config"])
	}
	return
}

func GetAbParamsFromBotDTO(dto map[string]string) (abVariables map[string]any, err error) {
	if len(dto) == 0 {
		return
	}
	abVariablesStr := dto[entity.FlowSamanthaAgentParam]
	if abVariablesStr != "" { // such as "{\"marscode_config\":{\"app_version\":\"ab_test_v1\"}}"
		if err = json.Unmarshal([]byte(abVariablesStr), &abVariables); err != nil {
			return
		}
		abVariables = conv.DefaultAny[map[string]any](abVariables["marscode_config"])
	}
	return
}

func GetDeepThinkSwitchStatusFromBotEXT(ext map[string]string) bool {
	if len(ext) == 0 {
		return false
	}
	if useDeepThink, ok := ext[entity.FlowSamanthaForceDeepThinking]; ok {
		if useDeepThink == "true" { // "FLOW:samantha:force_deep_thinking":"true/false"
			return true
		}
	}

	return false
}

func GetUseAutoCotSwitchStatusFromInput(useAutoCot *codeassist.CotMode) entity.CotMode {
	if useAutoCot == nil {
		return entity.CotModeClose
	}

	switch *useAutoCot {
	case codeassist.CotMode_CotModeClose:
		return entity.CotModeClose
	case codeassist.CotMode_CotModeAuto:
		return entity.CotModeAuto
	case codeassist.CotMode_CotModeOpen:
		return entity.CotModeOpen
	default:
		return entity.CotModeClose
	}
}

func GetUseAutoCotSwitchStatusFromBotEXT(ext map[string]string) entity.CotMode {
	if len(ext) == 0 {
		return entity.CotModeClose
	}
	if UseAutoCot, ok := ext[entity.FlowSamanthaUseAutoCot]; ok {
		if val, err := strconv.Atoi(UseAutoCot); err == nil {
			// 只接受 1、2、3 这三个值
			switch val {
			case 1:
				return entity.CotModeClose
			case 2:
				return entity.CotModeAuto
			case 3:
				return entity.CotModeOpen
			}
		}
	}
	return entity.CotModeClose
}
