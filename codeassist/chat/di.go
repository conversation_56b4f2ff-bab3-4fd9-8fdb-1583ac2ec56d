package chat

import (
	"go.uber.org/fx"

	"code.byted.org/devgpt/kiwis/codeassist/chat/dal/impl"
	chathandler "code.byted.org/devgpt/kiwis/codeassist/chat/handler"
	chatservice "code.byted.org/devgpt/kiwis/codeassist/chat/service/chat"
	homepageservice "code.byted.org/devgpt/kiwis/codeassist/chat/service/homepage"
	pipelineservice "code.byted.org/devgpt/kiwis/codeassist/chat/service/pipeline"
	routerservice "code.byted.org/devgpt/kiwis/codeassist/chat/service/router"
	searchservice "code.byted.org/devgpt/kiwis/codeassist/chat/service/search"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/port/flowresource"
	"code.byted.org/devgpt/kiwis/port/toutiaoalgo"
	"code.byted.org/devgpt/kiwis/port/toutiaosearch"
)

var Module = fx.Options(
	chatservice.Module,
	pipelineservice.Module,
	homepageservice.Module,
	routerservice.Module,
	searchservice.Module,
	chathandler.Module,
	impl.Module,
	fx.Provide(NewToutiaoSearchRpcClient),
	fx.Provide(NewToutiaoAlgoRpcClient),
	fx.Provide(fx.Annotate(NewFlowResourceSDKClient, fx.ResultTags(`name:"flow_resource_client"`))),
)

func NewToutiaoSearchRpcClient(tccConfig *config.CodeAssistTCCConfig) *toutiaosearch.RPCClient {
	return toutiaosearch.NewToutiaoSearchRpcClient(nil, tccConfig.ToutiaoSearchPluginConfig.GetPointer())
}

func NewToutiaoAlgoRpcClient(tccConfig *config.CodeAssistTCCConfig) *toutiaoalgo.RPCClient {
	return toutiaoalgo.NewToutiaoAlgoRpcClient(nil, tccConfig.ToutiaoAlgoConfig.GetPointer())
}

func NewFlowResourceSDKClient(tccConfig *config.CodeAssistTCCConfig) *flowresource.SDKClient {
	return flowresource.NewFlowResourceSDKClient(nil, tccConfig.FlowResourceConfig.GetPointer().ImageFlowResourceConfig)
}
