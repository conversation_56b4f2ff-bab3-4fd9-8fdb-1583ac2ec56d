package entity

import (
	"time"

	"github.com/samber/lo"

	"code.byted.org/devgpt/kiwis/codeassist/common/constant"
	contextentity "code.byted.org/devgpt/kiwis/codeassist/context/entity"
	copilotstackentity "code.byted.org/devgpt/kiwis/copilotstack/entity"
	"code.byted.org/devgpt/kiwis/port/llmops"
)

type Message struct {
	Role             string            `json:"role"`              // 角色
	Content          string            `json:"content"`           // 消息内容
	ContextBlocks    []*ContextBlock   `json:"context_blocks"`    // 引用块
	ContextVariables *ContextVariables `json:"context_variables"` // Canvas 变量
	SessionID        string            `json:"session_id"`        // 会话 ID，清除历史消息时会更新，不同于 Conversation ID
	ConversationID   string            `json:"conversation_id"`
	MessageID        string            `json:"message_id"`
	AnswerID         string            `json:"answer_id"`
}

type ContextVariables struct {
	Language             string                   `json:"language"`
	CommandName          string                   `json:"command_name"` // explain_code/fix_code/default/ai_fix/ai_edit
	SourceType           string                   `json:"source_type"`  // file/repo/directory/artifacts/modelgen
	SourceKey            string                   `json:"source_key"`
	SelectFilePath       string                   `json:"select_file_path"`
	SelectFileURI        string                   `json:"select_file_uri"`
	SelectContent        string                   `json:"select_content"`
	FileContent          string                   `json:"file_content"`
	SelectRange          *Range                   `json:"select_range"`
	VisibleRange         *Range                   `json:"visible_range"`
	CursorLine           int                      `json:"cursor_line"`
	Artifacts            *Artifacts               `json:"artifacts"`
	ArtifactTemplateID   string                   `json:"artifact_template_id"`
	RecommendationKey    string                   `json:"recommendation_key"`
	ReferenceCodeSnippet string                   `json:"reference_code_snippet"`   // 编辑时传入的代码片段
	ImageGenerationInfo  *ImageGenerationInfoPart `json:"image_generation_content"` // 图片生成的入参
	ModelGen             *ModelGen                `json:"model_gen"`
}

type ImageGenerationInfoPart struct {
	Content string `json:"content"` // 用户输入的提示词
	Scene   int    `json:"scene"`   // 图片生成的场景
}

type Artifacts struct {
	Identifier      string `json:"identifier"`
	Version         string `json:"version"`
	Type            string `json:"type"`             // 产物类型 webview/react/general_code
	Title           string `json:"title"`            // 产物标题
	ResourceID      string `json:"resource_id"`      // 产物资源 ID
	ResourceVersion string `json:"resource_version"` // 产物资源版本
	ErrorMessage    string `json:"error_message"`    // 运行错误信息
}

type ModelGen struct {
	ErrMessage string `json:"error_message"` // 非artifacts的运行错误信息
}

// GetSourceIdentifier 获取可视区或划词对应的上下文唯一标识
func (c *ContextVariables) GetSourceIdentifier() contextentity.ContextIdentifier {
	var t constant.ContextResourceType
	switch c.SourceType {
	case "file":
		t = constant.ContextResourceTypeFile
	case "repo":
		t = constant.ContextResourceTypeRepository
	case "directory":
		t = constant.ContextResourceTypeDirectory
	case "artifacts":
		t = constant.ContextResourceTypeArtifacts
	}
	return contextentity.ContextIdentifier{
		Type:        t,
		ResourceKey: c.SourceKey,
	}
}

const (
	ExplainCodeCommandName = "explain_code"
	FixCodeCommandName     = "fix_code"
	AskDoubaoCommandName   = "default"
	AIFixCommandName       = "ai_fix"
	AIEditCommandName      = "ai_edit"
)

const (
	ModelGenSourceType = "modelgen"
)

const (
	ChatCotModeVariableCot   = "cot"   // cot
	ChatCotModeVariableNoCot = "nocot" // 非cot
)

type Range struct {
	StartLine int `json:"start_line"`
	EndLine   int `json:"end_line"`
}

type ContextBlock struct {
	Type           constant.ContextBlockType `json:"type"`
	FileBlock      *FileBlock                `json:"file_block"`
	DirectoryBlock *DirectoryBlock           `json:"directory_block"`
	RepoBlock      *RepoBlock                `json:"repo_block"`
	ModelGenBlock  *ModelGenBlock            `json:"model_gen_block"`
	ImageBlock     *ImageBlock               `json:"image"`
}

func (c *ContextBlock) GetContextIdentifiers() []*contextentity.ContextIdentifier {
	if c == nil {
		return nil
	}
	switch {
	case c.FileBlock != nil:
		return c.FileBlock.Identifiers
	case c.DirectoryBlock != nil:
		return []*contextentity.ContextIdentifier{c.DirectoryBlock.Identifier}
	case c.RepoBlock != nil:
		return []*contextentity.ContextIdentifier{c.RepoBlock.Identifier}
	default:
		return nil
	}
}

type ContextType string

const (
	SingleFileContext ContextType = "single_file_context"
	MultiFileContext  ContextType = "multi_file_context"
	DirectoryContext  ContextType = "directory_context"
	RepositoryContext ContextType = "repository_context"
	UnknownContext    ContextType = "unknown_context"
)

type ContextBlocks []*ContextBlock

func (c ContextBlocks) GetContextType() ContextType {
	fileContextCount := 0
	for _, block := range c {
		if block.RepoBlock != nil {
			return RepositoryContext
		}
		if block.DirectoryBlock != nil {
			return DirectoryContext
		}
		if block.FileBlock != nil {
			fileContextCount++
		}
	}
	if fileContextCount > 1 {
		return MultiFileContext
	} else if fileContextCount > 0 {
		return SingleFileContext
	}
	return UnknownContext
}

func (c ContextBlocks) HasImage() bool {
	return lo.SomeBy(c, func(block *ContextBlock) bool {
		if block == nil || block.ImageBlock == nil || block.ImageBlock.Image == nil {
			return false
		}
		return true
	})
}

func (c ContextBlocks) GetIdentifiers() []*contextentity.ContextIdentifier {
	identifiers := make([]*contextentity.ContextIdentifier, 0)
	for _, block := range c {
		for _, identifier := range block.GetContextIdentifiers() {
			if identifier == nil {
				continue
			}
			identifiers = append(identifiers, identifier)
		}
	}
	return identifiers
}

func (c ContextBlocks) GetFileIdentifiers() []*contextentity.ContextIdentifier {
	identifiers := make([]*contextentity.ContextIdentifier, 0)
	for _, block := range c {
		if block.FileBlock != nil && block.FileBlock.Identifiers != nil {
			for _, identifier := range block.FileBlock.Identifiers {
				if identifier == nil {
					continue
				}
				identifiers = append(identifiers, identifier)
			}
		}
	}
	return identifiers
}

type FileBlock struct {
	Identifiers []*contextentity.ContextIdentifier `json:"identifiers"`
}

type DirectoryBlock struct {
	Identifier *contextentity.ContextIdentifier `json:"identifier"`
}

type RepoBlock struct {
	Identifier *contextentity.ContextIdentifier `json:"identifier"`
}

type ModelGenBlock struct {
	Content  string `json:"content"`
	Language string `json:"language"`
}

type ImageBlock struct {
	Image *Image `json:"image"`
}

func (b *ImageBlock) GetURL() string {
	if b != nil && b.Image != nil && b.Image.URL != nil {
		return lo.FromPtr(b.Image.URL)
	}
	return ""
}

type Image struct {
	URL  *string `json:"url"`
	URI  *string `json:"uri"`
	Name *string `json:"name"`
	MD5  *string `json:"md5"`
}

type ChatEvent struct {
	Output               *ChatEventContent             // 模型回复
	Suggestion           *ChatEventContent             // 问题建议
	ResolverContent      map[string]any                // 离线评测时，resolver中召回的内容
	ResolverTokenUsage   []*ResolverTokenUsage         // 离线测评时，记录每个resolver的token裁剪情况
	PromptContent        []*copilotstackentity.Message // 离线评测时，prompt裁剪后的结果
	FinalResolverContent map[string]any                // 离线评测时，resolver召回内容裁剪后的结果
}

type ChatEventType string

const (
	ChatEventTypeOutput ChatEventType = "output"
)

type ChatEventContent struct {
	Content     string            `json:"content"`
	ContentType ChatContentType   `json:"content_type"`
	Ext         map[string]string `json:"ext"`
	ID          string            `json:"id"`
	IsFinish    bool              `json:"is_finish"` // 流式生成是否结束
}

type ChatContentType string

const (
	ChatContentTypeText                     ChatContentType = "txt"
	ChatContentTypeImage                    ChatContentType = "image"
	ChatContentTypeBlockText                ChatContentType = "block_txt"
	ChatContentTypeBlockResearchProcessCard ChatContentType = "block_research_process_card"
	ChatContentTypeBlockCode                ChatContentType = "block_code"
	ChatContentTypeBlockResearchWebpage     ChatContentType = "block_research_webpage"
	ChatContentTypeBlockFileOperation       ChatContentType = "block_file_operation"
	ChatContentTypeBlockFile                ChatContentType = "block_file"
	ChatContentTypeBlockArtifactCodeFile    ChatContentType = "block_artifact_code_file"
	ChatContentTypeBlockCodeAssistProcess   ChatContentType = "block_code_assist_process"
	ChatContentTypeBlockArtifact            ChatContentType = "block_artifact"
)

type ResolverTokenUsage struct {
	Name         string `json:"name"`          // Resolver名
	Tokens       int    `json:"tokens"`        // 裁剪后的 token 数
	OriginTokens int    `json:"origin_tokens"` // 原始的 token 数
	IsCropped    bool   `json:"is_cropped"`    // 是否被裁剪
}

// PipelineHandler 处理器
type PipelineHandler string

func (p PipelineHandler) String() string {
	return string(p)
}

var (
	PipelineHandlerProjectAskPipeline      PipelineHandler = "project_ask_pipeline" // 项目问答
	PipelineHandlerDirectAskPipeline       PipelineHandler = "direct_ask_pipeline"  // 研发自由问答
	PipelineHandlerArtifactsPipeline       PipelineHandler = "artifacts_pipeline"   // Artifacts
	PipelineHandlerIntentionPipeline       PipelineHandler = "intention_pipeline"   // 意图识别
	PipelineHandlerImageGenerationPipeline PipelineHandler = "image_generation_pipeline"
)

type AgentHandler string

func (a AgentHandler) String() string {
	return string(a)
}

var (
	AgentHandlerDataAnalysisAgent AgentHandler = "data_analysis_agent"
)

type IntentionType string

var (
	IntentionDirectAsk   IntentionType = "direct_ask"
	IntentionArtifactAsk IntentionType = "artifact_ask"
)

const (
	IntentionDirectAnswer   = "b"
	IntentionArtifactAnswer = "a"
)

type PromptsRenderResult struct {
	PromptMessages []*llmops.Message
}

// 生图相关
const (
	MaxImageGenerationGoroutines     = 5
	ImageGenerationRetryInitInterval = 300 * time.Millisecond
	ImageGenerationRetryMaxTimes     = 3

	MinGenerateImageNumber     = 1
	DefaultGenerateImageNumber = 3
	MaxGenerateImageNumber     = 6

	GenerateImageWidth  = 1152
	GenerateImageHeight = 864

	ImageFormat = "jpeg"
)

type GenerateImagesOption struct {
	UserID    int64
	MessageID int64
	Query     string
	Number    *int64
}

type GenerateImagesResult struct {
	ImageDatas []GenerateImageData `json:"image_data_list"`
}
type GenerateImageData struct {
	ImageThumb GenerateImageInfo `json:"image_thumb"`
	ImageOri   GenerateImageInfo `json:"image_ori"`
	ImageRaw   GenerateImageInfo `json:"image_raw"`
	Status     CodeImageStatus   `json:"status"`
}

type GenerateImageInfo struct {
	URL          string `json:"url"`
	Width        int64  `json:"width"`
	Height       int64  `json:"height"`
	ThumbnailURL string `json:"thumbnail_url"`
	Format       string `json:"format"`
}

type CodeImageStatus int

const (
	CodeImageStatusUnknown    CodeImageStatus = 0
	CodeImageStatusGenerating CodeImageStatus = 1
	CodeImageStatusSucceed    CodeImageStatus = 2
	CodeImageStatusBanned     CodeImageStatus = 3
	CodeImageStatusFailed     CodeImageStatus = 4
)
