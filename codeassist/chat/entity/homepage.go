package entity

import (
	"code.byted.org/devgpt/kiwis/lib/config"
	"github.com/samber/lo"
)

const (
	LanguageZH                    = "zh"
	LanguageEN                    = "en"
	RecommendationKeyDataAnalysis = "web_ai_coding_prompt_data_analysis"
)

type Homepage struct {
	Recommendations            Recommendations             `json:"recommendations"`
	CodebaseTemplates          CodebaseTemplates           `json:"codebase_templates"`
	ArtifactsTemplates         *ArtifactsTemplates         `json:"artifacts_templates,omitempty"`
	ArtifactsCategoryTemplates *ArtifactsCategoryTemplates `json:"artifacts_category_templates,omitempty"`
}

type Recommendations struct {
	RecommendationItems []RecommendationItem `json:"recommendation_items"`
}

type RecommendationItem struct {
	ItemType int    `json:"item_type"`
	Key      string `json:"key"`
	Name     string `json:"name"`
	IconType string `json:"icon_type"`
}

type CodebaseTemplates struct {
	CodebaseItems []CodebaseItem `json:"codebase_items"`
}

type CodebaseItem struct {
	ItemType            int    `json:"item_type"`
	Query               string `json:"query"`
	IconURL             string `json:"icon_url"`
	UseCache            bool   `json:"use_cache"`
	Answer              string `json:"answer"`
	CodebaseURL         string `json:"codebase_url"`
	CodebaseName        string `json:"codebase_name"`
	CodebaseID          string `json:"codebase_id"`
	CodebaseBranchName  string `json:"codebase_branch_name"`
	CodebaseDescription string `json:"codebase_descirption"` // don't change the current situation online
}
type ArtifactsTemplates struct {
	ArtifactsItems []ArtifactItem `json:"artifacts_items"`
}

type ArtifactsCategoryTemplates struct {
	ArtifactsCategoryItems []ArtifactsCategoryItem `json:"artifacts_category_items"`
}

type ArtifactsCategoryItem struct {
	ArtifactsCategoryID   int            `json:"artifacts_category_id"`
	ArtifactsCategoryName string         `json:"artifacts_category_name"`
	ArtifactsItems        []ArtifactItem `json:"artifacts_items"`
}

type ArtifactItem struct {
	ArtifactsImageURL     string `json:"artifacts_image_url"`
	ArtifactsImageTitle   string `json:"artifacts_image_title"`
	ArtifactsID           string `json:"artifacts_id"`
	HomePageItemType      int    `json:"home_page_item_type"`
	UseCache              bool   `json:"use_cache"`
	Prompt                string `json:"prompt"`
	AdvertisingArticleUrl string `json:"advertising_article_url"`
}

type PromptTemplates struct {
	PromptList []*PromptData `json:"prompt_list"`
	Language   string        `json:"language"`
}

type PromptData struct {
	Template     string            `json:"template"`
	Key          string            `json:"key"`
	Name         string            `json:"name"`
	IconType     string            `json:"icon_type"`
	VariableList []*PromptVariable `json:"variable_list"`
}

type PromptVariable struct {
	Key          string            `json:"key"`
	Placeholder  string            `json:"placeholder"`
	VariableType string            `json:"variableType"`
	Options      []*VariableOption `json:"options"`
}

type VariableOption struct {
	Label string `json:"label"`
	Value string `json:"value"`
}

func ConfigPromptTemplatesToEntity(conf []*config.CodeassistPromptTemplate, lang string) PromptTemplates {
	if conf == nil || len(conf) == 0 {
		return PromptTemplates{}
	}
	return PromptTemplates{
		Language: lang,
		PromptList: lo.Map(conf, func(item *config.CodeassistPromptTemplate, _ int) *PromptData {
			if item == nil {
				return nil
			}
			return &PromptData{
				Template: item.Template,
				Key:      item.Key,
				Name:     item.Name,
				IconType: item.IconType,
				VariableList: lo.Map(item.VariableList, func(item *config.PromptVariable, _ int) *PromptVariable {
					if item == nil {
						return nil
					}
					return &PromptVariable{
						Key:          item.Key,
						Placeholder:  item.Placeholder,
						VariableType: item.VariableType,
						Options: lo.Map(item.Options, func(op *config.VariableOption, _ int) *VariableOption {
							if op == nil {
								return nil
							}
							return &VariableOption{
								Label: op.Label,
								Value: op.Value,
							}
						}),
					}
				}),
			}
		}),
	}
}
