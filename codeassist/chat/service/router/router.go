package router

import (
	"context"
	"strings"

	"code.byted.org/devgpt/kiwis/codeassist/common/constant"

	"code.byted.org/gopkg/logs/v2/log"
	"github.com/pkg/errors"

	appentity "code.byted.org/devgpt/kiwis/codeassist/app/entity"
	intentionentity "code.byted.org/devgpt/kiwis/codeassist/app/entity"
	appservice "code.byted.org/devgpt/kiwis/codeassist/app/service"
	intentionservice "code.byted.org/devgpt/kiwis/codeassist/app/service"
	"code.byted.org/devgpt/kiwis/codeassist/chat/entity"
	"code.byted.org/devgpt/kiwis/codeassist/chat/service"
	"code.byted.org/devgpt/kiwis/lib/conv"
	"code.byted.org/devgpt/kiwis/lib/metrics"
	"code.byted.org/devgpt/kiwis/lib/ratelimiter"
	"code.byted.org/devgpt/kiwis/port/redis"
)

const RateLimiterKey = "rate_limiter:codeassist_intention"

type Service struct {
	AppService       appservice.AppService
	IntentionService intentionservice.IntentionService
	PipelineService  service.PipelineService
	RateLimiter      *ratelimiter.RateLimiterV2
}

func NewRouterService(
	appService appservice.AppService, intentionService intentionservice.IntentionService, pipelineService service.PipelineService, redisCli *redis.BytedRedis,
) service.RouterService {
	return &Service{
		AppService:       appService,
		IntentionService: intentionService,
		PipelineService:  pipelineService,
		RateLimiter:      ratelimiter.NewRateLimiterV2(redisCli),
	}
}

func (s *Service) Router(ctx context.Context, opt *service.RouterOption) (*entity.ChatContext, error) {
	appName := s.routeToAppName(ctx, opt)

	appABConfig := conv.DefaultAny[map[string]any](opt.GlobalAbVariables[appName])
	app, err := s.AppService.GetAppByName(ctx, appName, appABConfig)
	if err != nil {
		log.V1.CtxError(ctx, "failed to get app `%s`, err: %v", appName, err)
		return nil, errors.WithMessage(err, "failed to get app")
	}

	hitVlm := s.hitVLM(opt)
	cotMode := s.getCotMode(opt)

	chatContext := &entity.ChatContext{
		UserID:          opt.UserID,
		ChatSource:      opt.ChatSource,
		UserMessage:     opt.UserMessage,
		HistoryMessages: opt.HistoryMessages,
		AbVariables:     appABConfig,
		App:             app,     // app 配置
		HitVLM:          hitVlm,  // 命中vlm
		UseAutoCot:      cotMode, // cot开关状态
	}
	log.V1.CtxInfo(ctx, "router result. app name: %s, hit vlm: %v, cot mode: %v", appName, chatContext.HitVLM,
		chatContext.UseAutoCot)
	return chatContext, nil
}

// getCotMode todo 判断是否命中auto cot，推全后去掉openCot字段的判断
func (s *Service) getCotMode(opt *service.RouterOption) entity.CotMode {
	// todo ai edit链路暂时不走deep think和vlm链路，后续视情况删掉
	if opt.UserMessage != nil && opt.UserMessage.ContextVariables != nil &&
		opt.UserMessage.ContextVariables.CommandName == entity.AIEditCommandName && opt.UserMessage.ContextVariables.ReferenceCodeSnippet != "" {
		return entity.CotModeClose
	}

	openCot, ok := opt.GlobalAbVariables["open_auto_cot"]
	if !ok {
		return entity.CotModeClose
	}
	if val, _ := openCot.(bool); !val {
		return entity.CotModeClose
	}

	return opt.UseAutoCot
}

// hitVLM 本轮 or 历史轮对话内有图片，即命中vlm
func (s *Service) hitVLM(opt *service.RouterOption) bool {
	// 未开启实验时直接返回false，todo: 推全后删除
	openVLM, ok := opt.GlobalAbVariables["open_vlm"]
	if !ok {
		return false
	}
	if val, _ := openVLM.(bool); !val {
		return false
	}

	// todo ai edit链路暂时不走deep think和vlm链路，后续视情况删掉
	if opt.UserMessage != nil && opt.UserMessage.ContextVariables != nil &&
		opt.UserMessage.ContextVariables.CommandName == entity.AIEditCommandName && opt.UserMessage.ContextVariables.ReferenceCodeSnippet != "" {
		return false
	}

	if opt.UserMessage != nil && len(opt.UserMessage.ContextBlocks) > 0 {
		if entity.ContextBlocks(opt.UserMessage.ContextBlocks).HasImage() {
			return true
		}
	}
	if opt.HistoryMessages != nil {
		for _, historyMessage := range opt.HistoryMessages {
			if historyMessage == nil || len(historyMessage.ContextBlocks) == 0 {
				continue
			}
			if entity.ContextBlocks(historyMessage.ContextBlocks).HasImage() {
				return true
			}
		}
	}
	return false
}

// routeToAppName 通过规则/意图识别，确定app name，如未满足任何规则，默认返回自由问答的app name
func (s *Service) routeToAppName(ctx context.Context, opt *service.RouterOption) string {
	if s.isImageGeneration(opt.UserMessage) {
		return appentity.AppNameImageGeneration
	}

	if s.isDataAnalysis(ctx, opt) {
		log.V1.CtxInfo(ctx, "hit data analysis app")
		return appentity.AppNameDataAnalysisAgent
	}

	if s.isProjectAsk(opt.UserMessage) {
		return appentity.AppNameProjectAsk
	}

	// 未开启实验时直接返回自由问答app name，todo: 推全后删除
	openIntention, ok := opt.GlobalAbVariables["open_intention"]
	if !ok {
		return appentity.AppNameDirectAsk
	}
	if val, _ := openIntention.(bool); !val {
		return appentity.AppNameDirectAsk
	}

	// 规则判断
	contextVariables := opt.UserMessage.ContextVariables
	if contextVariables != nil {
		// 1. ai fix 走 artifacts
		if contextVariables.CommandName == entity.AIFixCommandName {
			if contextVariables.Artifacts != nil {
				return appentity.AppNameArtifacts
			}
			if contextVariables.SourceType == entity.ModelGenSourceType && contextVariables.ModelGen != nil {
				return appentity.AppNameDirectAsk
			}
		}
		// 2. ai edit 走artifacts链路
		if contextVariables.CommandName == entity.AIEditCommandName && contextVariables.ReferenceCodeSnippet != "" {
			return appentity.AppNameArtifacts
		}
		// 3. 首页推荐走 artifacts
		if contextVariables.ArtifactTemplateID != "" {
			return appentity.AppNameArtifacts
		}
	}

	// 通过意图识别模型判断
	intentionABVariables := conv.DefaultAny[map[string]any](opt.GlobalAbVariables[intentionentity.IntentionName])
	intention, err := s.IntentionService.Get(ctx, intentionABVariables)
	if err != nil {
		log.V1.CtxError(ctx, "failed to get app `intention_ask`, err: %v", err)
		return appentity.AppNameDirectAsk
	}

	intentionChatContext := &entity.ChatContext{
		UserID:          opt.UserID,
		ChatSource:      opt.ChatSource,
		UserMessage:     opt.UserMessage,
		HistoryMessages: opt.HistoryMessages,
		AbVariables:     intentionABVariables,
		Intention:       intention,
	}
	intentionType := s.intentionAsk(ctx, intentionChatContext)
	switch intentionType {
	case entity.IntentionArtifactAsk:
		return appentity.AppNameArtifacts
	default:
		return appentity.AppNameDirectAsk
	}
}

func (s *Service) isProjectAsk(userMessage *entity.Message) bool {
	if userMessage != nil {
		for _, block := range userMessage.ContextBlocks {
			if block.DirectoryBlock != nil || block.FileBlock != nil || block.RepoBlock != nil {
				return true
			}
		}
	}
	return false
}

func (s *Service) isImageGeneration(userMessage *entity.Message) bool {
	if userMessage != nil && userMessage.ContextVariables != nil && userMessage.ContextVariables.ImageGenerationInfo != nil {
		return true
	}
	return false
}

// isDataAnalysis 临时的判断方案，后续会走统一流程
func (s *Service) isDataAnalysis(ctx context.Context, opt *service.RouterOption) bool {
	if opt.ChatSource != entity.ChatSourceCodeassistSkill {
		return false
	}

	if !isHitAgentABTest(opt.GlobalAbVariables) {
		return false
	}

	userMessage := opt.UserMessage

	if userMessage == nil {
		log.V1.CtxError(ctx, "user message is nil %v", userMessage)
		return false
	}

	if len(userMessage.ContextBlocks) == 0 {
		return false
	}

	for _, block := range userMessage.ContextBlocks {
		if block.Type != constant.ContextBlockTypeFileBlock {
			return false
		}
		if block.FileBlock == nil || block.FileBlock.Identifiers == nil {
			log.V1.CtxError(ctx, "file block is nil %v", userMessage)
			return false
		}
		// 判断所有 identerfier Name 是不是以 .csv,.xlsx,.xls 结尾，如果是，返回true
		for _, identifier := range block.FileBlock.Identifiers {
			if !(strings.HasSuffix(identifier.Name, ".csv") ||
				strings.HasSuffix(identifier.Name, ".xlsx") ||
				strings.HasSuffix(identifier.Name, ".xls")) {
				return false
			}
		}
	}
	return true
}

func isHitAgentABTest(abVariables map[string]any) bool {
	if abVariables == nil {
		return false
	}
	abTestInterface, ok := abVariables["open_web_ai_coding_prompt_data_analysis"]
	if !ok {
		return false
	}
	abTest, _ := abTestInterface.(bool)
	return abTest
}

func (s *Service) intentionAsk(ctx context.Context, chatContext *entity.ChatContext) entity.IntentionType {
	// check rate limit
	intention := chatContext.Intention
	allowed := true // 默认限流通过
	defer func() {
		tag := &metrics.CodeAssistRateLimitTag{
			LimitKey: RateLimiterKey,
			Result:   allowed,
		}
		_ = metrics.CodeAssistMetric.RateLimitThroughput.WithTags(tag).Add(1)
	}()

	if intention.RateLimitWindows > 0 && intention.RateLimitQuota > 0 { // 如果限流的配置有值，check限流逻辑
		allowed = s.RateLimiter.Check(ctx, RateLimiterKey, intention.RateLimitQuota, intention.RateLimitWindows)
		log.V1.CtxInfo(ctx, "intention rate limit. windows: %v, quota: %v, allowed: %v", intention.RateLimitWindows, intention.RateLimitQuota, allowed)
	}
	if !allowed {
		return entity.IntentionDirectAsk
	}

	pipelineResult, err := s.PipelineService.RunPipeline(ctx, chatContext, intention.Handler, nil)
	if err != nil {
		log.V1.CtxError(ctx, "failed to run pipeline. err: %v", err)
		return entity.IntentionDirectAsk
	}

	switch pipelineResult.AnswerContent {
	case entity.IntentionArtifactAnswer:
		return entity.IntentionArtifactAsk
	case entity.IntentionDirectAnswer:
		return entity.IntentionDirectAsk
	default:
		log.V1.CtxWarn(ctx, "intention answer is illegal: %s", pipelineResult.AnswerContent)
		return entity.IntentionDirectAsk
	}
}
