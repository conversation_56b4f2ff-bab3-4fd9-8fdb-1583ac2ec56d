package chat

import (
	"context"
	"fmt"
	"runtime/debug"

	alicectx "code.byted.org/flow/alice_util/ctx"
	"code.byted.org/gopkg/logs/v2/log"
	"github.com/pkg/errors"
	"github.com/samber/lo"

	agentservice "code.byted.org/devgpt/kiwis/codeassist/agent/service"
	appentity "code.byted.org/devgpt/kiwis/codeassist/app/entity"
	appservice "code.byted.org/devgpt/kiwis/codeassist/app/service"
	"code.byted.org/devgpt/kiwis/codeassist/chat/entity"
	"code.byted.org/devgpt/kiwis/codeassist/chat/service"
	pipelinebase "code.byted.org/devgpt/kiwis/codeassist/chat/service/pipeline/base"
	journalentity "code.byted.org/devgpt/kiwis/codeassist/journal/entity"
	copilotstackentity "code.byted.org/devgpt/kiwis/copilotstack/entity"
	"code.byted.org/devgpt/kiwis/copilotstack/llm"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/metrics"
	"code.byted.org/devgpt/kiwis/lib/ratelimiter"
	"code.byted.org/devgpt/kiwis/lib/stream"
	"code.byted.org/devgpt/kiwis/port/fornax"
	"code.byted.org/devgpt/kiwis/port/llmops"
	"code.byted.org/devgpt/kiwis/port/redis"
)

type Service struct {
	AppService      appservice.AppService
	PipelineService service.PipelineService
	LLMService      llm.Service
	RateLimiter     *ratelimiter.RateLimiterV2
	TaskService     agentservice.TaskService
	FornaxClient    fornax.Client
}

func NewChatService(llmService llm.Service, appService appservice.AppService, pipelineService service.PipelineService, redisCli *redis.BytedRedis, fornaxClient fornax.Client, taskService agentservice.TaskService) service.ChatService {
	return &Service{
		LLMService:      llmService,
		AppService:      appService,
		PipelineService: pipelineService,
		RateLimiter:     ratelimiter.NewRateLimiterV2(redisCli),
		TaskService:     taskService,
		FornaxClient:    fornaxClient,
	}
}

func (s *Service) Chat(ctx context.Context, chatContext *entity.ChatContext) (*stream.RecvChannel[entity.ChatEvent], error) {
	var (
		app     = chatContext.App
		appName = app.Name
	)
	// check rate limit
	allowed := true // 默认限流通过
	rateLimiterKey := fmt.Sprintf("rate_limiter:codeassist_chat_app_%s", appName)

	defer func() {
		tag := &metrics.CodeAssistRateLimitTag{
			LimitKey: rateLimiterKey,
			Result:   allowed,
		}
		_ = metrics.CodeAssistMetric.RateLimitThroughput.WithTags(tag).Add(1)
	}()

	if app.RateLimitWindows > 0 && app.RateLimitQuota > 0 { // 如果限流的配置有值，check限流逻辑
		allowed = s.RateLimiter.Check(ctx, rateLimiterKey, app.RateLimitQuota, app.RateLimitWindows)
		log.V1.CtxInfo(ctx, "chat app rate limit. key: %v, windows: %v, quota: %v, allowed: %v", rateLimiterKey, app.RateLimitWindows, app.RateLimitQuota, allowed)
	}
	if !allowed {
		return nil, service.ErrChatRateLimit
	}

	return s.runChat(ctx, chatContext)
}

func (s *Service) runChat(ctx context.Context, chatContext *entity.ChatContext) (*stream.RecvChannel[entity.ChatEvent], error) {
	if chatContext == nil || chatContext.App == nil {
		return nil, errors.New("chat context app is nil")
	}
	handlerConfig := chatContext.App.GetAppHandler()
	if handlerConfig == nil {
		return nil, errors.New("chat app handler not found")
	}

	send, recv := stream.NewChannel[entity.ChatEvent](20)

	switch handlerConfig.Type {
	case appentity.HandlerTypePipeline: // pipeline 类型的处理流程，可往下扩展其他模式处理
		go func() {
			defer func() {
				if e := recover(); e != nil {
					log.V1.CtxError(ctx, "panicked while running pipeline: %+v, %s", e, string(debug.Stack()))
					send.PublishError(errors.Errorf("panicked while running pipeline: %+v", e), false)
				}
				send.Close()
			}()
			// Check if the user cancelled the request.
			if ctx.Err() != nil {
				log.V1.CtxInfo(ctx, "got context error: %v", ctx.Err())
				send.PublishError(ctx.Err(), false)
				return
			}

			_, err := s.PipelineService.RunPipeline(ctx, chatContext, handlerConfig, send)
			if err != nil {
				log.V1.CtxError(ctx, "failed to run pipeline. err: %v", err)
				send.PublishError(errors.WithMessage(err, "failed to run pipeline"), false)
			}
		}()
	case appentity.HandlerTypeAgent:
		if ctx.Err() != nil {
			log.V1.CtxInfo(ctx, "got context error: %v", ctx.Err())
			send.PublishError(ctx.Err(), false)
			return recv, nil
		}

		task, err := s.TaskService.NewTask(ctx, chatContext)
		if err != nil {
			log.V1.CtxError(ctx, "failed to create task. err: %v", err)
			send.PublishError(errors.WithMessage(err, "failed to create task"), false)
			return recv, nil
		}

		span, _, err := s.FornaxClient.StartQuerySpan(ctx, journalentity.SpanLLMContext)
		if err != nil {
			log.V1.CtxWarn(ctx, "failed to start prompt span: %v", err)
		}
		pipelineOpt := &service.PipelineOption{
			ChatContext: chatContext,
		}
		pipelinebase.PackContextVariables(pipelineOpt, nil, nil, make(map[string]any))
		span.SetTag(ctx, map[string]interface{}{
			"context_variables": pipelineOpt.ContextVariables,
		})
		span.Finish(ctx)

		err = s.TaskService.RunWithScheduler(ctx, agentservice.RunTaskOption{
			AgentName:       chatContext.App.Name,
			AgentVersion:    chatContext.App.Version,
			TaskID:          task,
			UserID:          chatContext.UserID,
			BizID:           alicectx.GetBizID(ctx),
			TenantID:        alicectx.GetTenantID(ctx),
			UserMessage:     chatContext.UserMessage,
			HistoryMessages: chatContext.HistoryMessages,
			Send:            send,
			NeedRecover:     false,
		})
		if err != nil {
			log.V1.CtxError(ctx, "failed to run task. err: %v", err)
			send.PublishError(errors.WithMessage(err, "failed to run task"), false)
		}
	default:
		return nil, errors.New("unsupported app handler type")
	}

	return recv, nil
}

func (s *Service) PromptsRender(ctx context.Context, r *entity.RenderContext) (*entity.PromptsRenderResult, error) {
	if r == nil {
		return nil, errors.New("render context is nil")
	}
	// 先默认写一个
	app, err := s.AppService.GetAppByName(ctx, appentity.AppNameProjectAsk, nil)
	if err != nil {
		log.V1.CtxError(ctx, "failed to get app, err: %v", err)
		return nil, errors.WithMessage(err, "failed to get app")
	}
	chatContext := &entity.ChatContext{
		HistoryMessages: make([]*entity.Message, 0),
		UserMessage:     &entity.Message{},
		App:             app,
		IsOffline:       true,
	}
	handlerConfig := app.GetAppHandler()

	messages, err := s.PipelineService.PromptPipeline(ctx, chatContext, r, handlerConfig)
	if err != nil {
		log.V1.CtxError(ctx, "failed to render prompt, err: %v", err)
		return nil, errors.WithMessage(err, "failed to render prompt")
	}
	return &entity.PromptsRenderResult{
		PromptMessages: lo.Map(messages, func(m *copilotstackentity.Message, _ int) *llmops.Message {
			return &llmops.Message{
				Role:    m.Role,
				Content: m.Content,
			}
		}),
	}, nil
}

// OfflineChat offline chat
func (s *Service) OfflineChat(ctx context.Context, chatContext *entity.ChatContext, r *entity.RenderContext) (*stream.RecvChannel[entity.ChatEvent], error) {
	if chatContext == nil || chatContext.App == nil {
		return nil, errors.New("chat context is nil")
	}

	// set is offline
	chatContext.IsOffline = true

	// replace handler answer prompt
	if r.PromptTemplate != nil && chatContext.App.GetAppHandler() != nil {
		chatContext.App.GetAppHandler().Pipeline.AnswerPrompt = r.PromptTemplate
	}

	// replace ckg retrieve params
	if r.Variables != nil && r.Variables.CKGRetrieveParams != nil {
		chatContext.App.CKGRetrieveParams = &config.CKGRetrieveParams{
			EmbeddingTopN:       r.Variables.CKGRetrieveParams.EmbeddingTopN,
			EmbeddingRecallNums: r.Variables.CKGRetrieveParams.EmbeddingRecallNums,
			RerankRecallNums:    r.Variables.CKGRetrieveParams.RerankRecallNums,
		}
	}
	return s.runChat(ctx, chatContext)
}
