package image_generation_pipeline

import (
	"context"

	"github.com/pkg/errors"

	"code.byted.org/devgpt/kiwis/codeassist/chat/entity"
	"code.byted.org/devgpt/kiwis/codeassist/chat/service"
	pipelinebase "code.byted.org/devgpt/kiwis/codeassist/chat/service/pipeline/base"
	copilotstackentity "code.byted.org/devgpt/kiwis/copilotstack/entity"
	"code.byted.org/devgpt/kiwis/lib/stream"
	"code.byted.org/devgpt/kiwis/port/flowresource"
	"code.byted.org/gopkg/logs/v2/log"
)

var _ service.Pipeline = &Pipeline{}

type Pipeline struct {
	*pipelinebase.PipelineBase
	FlowResourceClient *flowresource.SDKClient `name:"flow_resource_client"`
}

func (p *Pipeline) GetName() entity.PipelineHandler {
	return entity.PipelineHandlerImageGenerationPipeline
}

func (p *Pipeline) Run(ctx context.Context, pipelineOpt *service.PipelineOption, send *stream.SendChannel[entity.ChatEvent],
) (*service.PipelineResult, error) {
	if pipelineOpt == nil || pipelineOpt.App == nil {
		return nil, errors.New("pipeline option is illegal")
	}

	answerContent, err := p.answer(ctx, pipelineOpt, send)
	if err != nil {
		log.V1.CtxError(ctx, "failed to llm answer: %v", err)
		return nil, errors.WithMessage(err, "failed to chatCompletion answer")
	}

	if !pipelineOpt.NeedSuggest() {
		return &service.PipelineResult{
			AnswerContent: answerContent,
		}, nil
	}

	result := &service.PipelineResult{
		AnswerContent: answerContent,
	}
	return result, nil
}

func (p *Pipeline) Render(ctx context.Context, r *entity.RenderContext, pipelineOpt *service.PipelineOption) ([]*copilotstackentity.Message, error) {
	return []*copilotstackentity.Message{}, nil
}
