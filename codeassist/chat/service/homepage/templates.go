package homepage

var Templates = `
{
  "recommendations": {
    "recommendation_items": [{
      "item_type": 6,
      "key": "web_ai_coding_prompt_md2html",
      "name": "文本秒变网页",
      "icon_type": "md2html"
    }, {
      "item_type": 4,
      "key": "web_ai_coding_prompt_repo_interpretation",
      "name": "开源仓库解读",
      "icon_type": "github"
    },
    {
        "item_type": 6,
        "key": "web_ai_coding_prompt_data_analysis",
        "name": "数据智能可视化",
        "icon_type": "data_analysis"
    },
     {
      "item_type": 6,
      "key": "web_ai_coding_prompt_title_explain",
      "name": "代码解释",
      "icon_type": "explain"
    },
      {
        "item_type": 6,
        "key": "web_ai_coding_prompt_content_fix",
        "name": "代码修复",
        "icon_type": "fix"
      },
      {
        "item_type": 6,
        "key": "web_ai_coding_prompt_content_translate",
        "name": "代码翻译",
        "icon_type": "translate"
      },
      {
        "item_type": 6,
        "key": "web_ai_coding_prompt_content_practice",
        "name": "编程解题",
        "icon_type": "practice"
      },
      {
        "item_type": 6,
        "key": "web_ai_coding_prompt_content_knowledge",
        "name": "知识问答",
        "icon_type": "knowledge"
      },
      {
        "item_type": 6,
        "key": "web_ai_coding_prompt_content_sql",
        "name": "SQL 生成",
        "icon_type": "sql"
      }
    ]
  },
  "codebase_templates": {
    "codebase_items": [{
      "codebase_name": "GoogleChrome/lighthouse",
      "codebase_descirption": "lighthouse: 网络应用的质量改进工具",
      "codebase_url": "https://github.com/GoogleChrome/lighthouse",
      "codebase_branch_name": "master",
      "codebase_id": "5c75210c-3574-4547-a77b-4b7408ca1dfe",
      "item_type": 4,
      "query": "",
      "icon_url": "",
      "use_cache": true,
      "answer": ""
    },
      {
        "codebase_name": "biaochenxuying/blog",
        "codebase_descirption": "blog: JS 数据结构与算法之美",
        "codebase_url": "https://github.com/biaochenxuying/blog",
        "codebase_branch_name": "master",
        "codebase_id": "3f728d85-f950-4ade-90b6-fc598dca8363",
        "item_type": 4,
        "query": "",
        "icon_url": "",
        "use_cache": true,
        "answer": ""
      },
      {
        "codebase_name": "weilanwl/coloruicss",
        "codebase_descirption": "coloruicss: 专注视觉的小程序组件库",
        "codebase_url": "https://github.com/weilanwl/coloruicss",
        "codebase_branch_name": "master",
        "codebase_id": "d2b95696-290a-4a98-a3c4-140b3f4bfe1c",
        "item_type": 4,
        "query": "",
        "icon_url": "",
        "use_cache": true,
        "answer": ""
      },
      {
        "codebase_name": "nslogx/Gitter",
        "codebase_descirption": "Gitter: 颜值最高的微信小程序客户端",
        "codebase_url": "https://github.com/huangjianke/Gitter",
        "codebase_branch_name": "master",
        "codebase_id": "59c0179b-66a7-4294-835f-002fc146f904",
        "item_type": 4,
        "query": "",
        "icon_url": "",
        "use_cache": true,
        "answer": ""
      },
      {
        "codebase_name": "TheAlgorithms/Python",
        "codebase_descirption": "Python: 算法的 Python 实现",
        "codebase_url": "https://github.com/TheAlgorithms/Python",
        "codebase_branch_name": "master",
        "codebase_id": "91209f29-27c8-4e9c-b7f2-3ff65e12541a",
        "item_type": 4,
        "query": "",
        "icon_url": "",
        "use_cache": true,
        "answer": ""
      },
      {
        "codebase_name": "kitao/pyxel",
        "codebase_descirption": "pyxel: Python 复古游戏开发环境",
        "codebase_url": "https://github.com/kitao/pyxel",
        "codebase_branch_name": "master",
        "codebase_id": "747b0e7c-4541-42cd-83ce-d673b6294fd6",
        "item_type": 4,
        "query": "",
        "icon_url": "",
        "use_cache": true,
        "answer": ""
      },
      {
        "codebase_name": "kaishengtai/neuralart",
        "codebase_descirption": "neuralart: 艺术风格的神经算法的实现",
        "codebase_url": "https://github.com/kaishengtai/neuralart",
        "codebase_branch_name": "master",
        "codebase_id": "28e63e62-5ba3-402a-961a-fa9a064b2cb0",
        "item_type": 4,
        "query": "",
        "icon_url": "",
        "use_cache": true,
        "answer": ""
      },
      {
        "codebase_name": "abseil/abseil-cpp",
        "codebase_descirption": "abseil-cpp: Google内部 C++ 公共库",
        "codebase_url": "https://github.com/abseil/abseil-cpp",
        "codebase_branch_name": "master",
        "codebase_id": "025b5e3c-01c9-4e21-8098-95e13eca608e",
        "item_type": 4,
        "query": "",
        "icon_url": "",
        "use_cache": true,
        "answer": ""
      },
      {
        "codebase_name": "blackhole89/notekit",
        "codebase_descirption": "notekit: 支持手绘笔记的 Markdown 编辑器",
        "codebase_url": "https://github.com/blackhole89/notekit",
        "codebase_branch_name": "master",
        "codebase_id": "beb18041-52fd-4b30-ad18-a13b439aad61",
        "item_type": 4,
        "query": "",
        "icon_url": "",
        "use_cache": true,
        "answer": ""
      },
      {
        "codebase_name": "yuesong-feng/30dayMakeCppServer",
        "codebase_descirption": "30dayMakeCppServer: 从零编写C++ 服务器教程",
        "codebase_url": "https://github.com/yuesong-feng/30dayMakeCppServer",
        "codebase_branch_name": "master",
        "codebase_id": "5e8dde82-089d-4402-a3bc-6014a3920f2c",
        "item_type": 4,
        "query": "",
        "icon_url": "",
        "use_cache": true,
        "answer": ""
      },
      {
        "codebase_name": "williamfiset/Algorithms",
        "codebase_descirption": "Algorithms: 用 Java 实现的算法和数据结构",
        "codebase_url": "https://github.com/williamfiset/Algorithms",
        "codebase_branch_name": "master",
        "codebase_id": "d046e911-1204-4519-88e9-e088a8fedf91",
        "item_type": 4,
        "query": "",
        "icon_url": "",
        "use_cache": true,
        "answer": ""
      },
      {
        "codebase_name": "jeecgboot/JimuReport",
        "codebase_descirption": "JimuReport: 在线制作报表工具",
        "codebase_url": "https://github.com/jeecgboot/JimuReport",
        "codebase_branch_name": "master",
        "codebase_id": "af4ccf68-6c99-43df-aec7-6241097dc421",
        "item_type": 4,
        "query": "",
        "icon_url": "",
        "use_cache": true,
        "answer": ""
      },
      {
        "codebase_name": "shuzijun/leetcode-editor",
        "codebase_descirption": "leetcode-editor: LeetCode IDE 刷题插件",
        "codebase_url": "https://github.com/shuzijun/leetcode-editor",
        "codebase_branch_name": "master",
        "codebase_id": "f59f6977-7ee3-4902-91a3-e2d9ef4de7a4",
        "item_type": 4,
        "query": "",
        "icon_url": "",
        "use_cache": true,
        "answer": ""
      },
      {
        "codebase_name": "coobird/thumbnailator",
        "codebase_descirption": "thumbnailator: Java 缩略图生成库",
        "codebase_url": "https://github.com/coobird/thumbnailator",
        "codebase_branch_name": "master",
        "codebase_id": "dc60720a-51b1-498d-bc0d-28295a4aac1d",
        "item_type": 4,
        "query": "",
        "icon_url": "",
        "use_cache": true,
        "answer": ""
      },
      {
        "codebase_name": "deepseek-ai/FlashMLA",
        "codebase_descirption": "deepseek: 用于 Hopper GPU 的高效 MLA 内核",
        "codebase_url": "https://github.com/deepseek-ai/FlashMLA",
        "codebase_branch_name": "main",
        "codebase_id": "d2100ef5-9634-4a68-9d9a-23bf06e5b640",
        "item_type": 4,
        "query": "",
        "icon_url": "",
        "use_cache": true,
        "answer": ""
      },
      {
        "codebase_name": "deepseek-ai/DeepEP",
        "codebase_descirption": "deepseek：面向 MoE 和 EP 设计的通信库",
        "codebase_url": "https://github.com/deepseek-ai/DeepEP",
        "codebase_branch_name": "main",
        "codebase_id": "337e8d53-17aa-427f-b316-4baacdfac940",
        "item_type": 4,
        "query": "",
        "icon_url": "",
        "use_cache": true,
        "answer": ""
      },
      {
        "codebase_name": "deepseek-ai/DeepGEMM",
        "codebase_descirption": "deepseek：用于 FP8 GEMM 的轻量级 CUDA 库",
        "codebase_url": "https://github.com/deepseek-ai/DeepGEMM",
        "codebase_branch_name": "main",
        "codebase_id": "a7cf3ba4-ced1-4736-9df4-bfb52347d068",
        "item_type": 4,
        "query": "",
        "icon_url": "",
        "use_cache": true,
        "answer": ""
      },
      {
        "codebase_name": "deepseek-ai/DualPipe",
        "codebase_descirption": "DualPipe：Deepseek 双向调度消除GPU等待方法",
        "codebase_url": "https://github.com/deepseek-ai/DualPipe",
        "codebase_branch_name": "main",
        "codebase_id": "8af844e5-ad30-447d-912b-5b444fa30b7b",
        "item_type": 4,
        "query": "",
        "icon_url": "",
        "use_cache": true,
        "answer": ""
      },
      {
        "codebase_name": "deepseek-ai/EPLB",
        "codebase_descirption": "EPLB：EP间并行动态负载均衡算法",
        "codebase_url": "https://github.com/deepseek-ai/eplb",
        "codebase_branch_name": "main",
        "codebase_id": "cdcbeb50-3f81-4b55-89b0-ed4f23f990ee",
        "item_type": 4,
        "query": "",
        "icon_url": "",
        "use_cache": true,
        "answer": ""
      },
      {
        "codebase_name": "deepseek-ai/profile-data",
        "codebase_descirption": "profile-data：DeepSeek 训练和推理框架的性能分析",
        "codebase_url": "https://github.com/deepseek-ai/profile-data",
        "codebase_branch_name": "main",
        "codebase_id": "850beab9-4daa-4e00-9350-ee9f06f672e1",
        "item_type": 4,
        "query": "",
        "icon_url": "",
        "use_cache": true,
        "answer": ""
      },
      {
        "codebase_name": "deepseek-ai/DeepSeek-R1",
        "codebase_descirption": "DeepSeek-R1：DeepSeek 开源的推理模型",
        "codebase_url": "https://github.com/deepseek-ai/DeepSeek-R1",
        "codebase_branch_name": "main",
        "codebase_id": "eb8db78d-b237-4263-9cee-6a422bfed8df",
        "item_type": 4,
        "query": "",
        "icon_url": "",
        "use_cache": true,
        "answer": ""
      },
      {
        "codebase_name": "deepseek-ai/DeepSeek-V3",
        "codebase_descirption": "DeepSeek-V3：DeepSeek 开源的混合专家模型",
        "codebase_url": "https://github.com/deepseek-ai/DeepSeek-V3",
        "codebase_branch_name": "main",
        "codebase_id": "9fd21676-5211-4883-97d8-27c00c3a691f",
        "item_type": 4,
        "query": "",
        "icon_url": "",
        "use_cache": true,
        "answer": ""
      }
    ]
  },
  "artifacts_templates": {
    "artifacts_items": [{
      "artifacts_image_url": "https://p3-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_generation/2dc68ae111e522873b3f0ae4cc0a3506_1741837756088472880.png~tplv-a9rns2rl98-image.png?rk3s=25bff839&x-expires=1773373756&x-signature=1zgMZvtJZEVrzZD9nJphPMxofvE%3D",
      "artifacts_image_title": "测测你的 MBTI",
      "artifacts_id": "202503062114250B4CB2C574835D05F466",
      "item_type": 5,
      "use_cache": false,
      "prompt": "需求：用单文件HTML生成一个MBTI的答题测试页面\n\n功能要求：\n1.界面设计采用响应式布局，确保在不同设备上都能良好展示。\n2.页面展示测试的标题，题目数量不少于10道，都是选择题的样式，一个页面展示一道题。选项展示具体内容\n3.选择选项后，自动进入下一题。最后一道选择题选择完毕后展示测试结果\n4.每一种测试结果给出对应的趣味提示，要求提示包含emoji。结果页面持续展示满屏烟花效果。\n5.测试结果页面展示再试一次的按钮，点击后清空历史选择重新开始答题。\n6.顶部有进度条显示测试进度，展示进度文字（2/12），最终结果页不展示答题进度。\n\n视觉设计要求：\n1.采用马卡龙色系搭配低饱和度渐变背景\n2.题目卡片固定大小，尺寸稍微大一些，增加微透明感和柔和阴影\n3.选项按钮使用圆角设计，选中时有按压弹性动画\n4.标题采用柔和的文字阴影效果，使用圆润的手写字体（Comic Sans MS + 华文彩云）\n5.结果框使用双色渐变背景+大圆角设计，正文使用易读的非衬线字体（微软雅黑 + 幼圆）\n6.烟花效果使用使用粉色、黄色、蓝色多层渐变色叠加\n7.所有交互元素都带有缓动动画\n8.采用拟物化设计语言（卡片、阴影等）\n9.进度条宽度和弹窗保持一致，进度条颜色使用低饱和度颜色\n10.进度文字展示在卡片内题目的顶部，居中展示，字号是13px\n\n技术实现要求：\n1.所有动画仅使用CSS transition实现\n2.烟花效果通过简单的scale动画模拟\n3.配色方案使用CSS变量统一管理\n4.采用flex布局实现响应式\n5.所有元素使用border-radius柔化边缘"
    },
      {
        "artifacts_image_url": "https://p3-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_generation/334d07666f7542b777fc000223ee94fd_1741837704061696882.png~tplv-a9rns2rl98-image.png?rk3s=25bff839&x-expires=1773373704&x-signature=ZCAKvPJKq3TI%2Bc5saKzjv3TxFeg%3D",
        "artifacts_image_title": "设计师个人作品集",
        "artifacts_id": "2025030421130164A7150D19197DC939C0",
        "item_type": 5,
        "use_cache": false,
        "prompt": "需求：创建一个设计师个人作品集网站，使用单文件HTML生成。该网站的主要定位是展示平面设计作品与接单服务\n\n功能逻辑要求：\n1. 网站头图：https://p9-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_generation/eccdf8c573aadd06e328d8851ab0c00f_1741676810548865596.jpeg~tplv-a9rns2rl98-image.jpeg?rk3s=25bff839&x-expires=1773212810&x-signature=2IFyOmdqAXYpcj9n%2F8KfWze%2BW6c%3D\n2. 作品分类展示区（品牌/海报/UI）\n作品图片1:https://p3-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_generation/2988c3e31fe68b55e5c789859e778505_1741676608255000828.jpeg~tplv-a9rns2rl98-image.jpeg?rk3s=25bff839&x-expires=1773212608&x-signature=BGNQKzQHjCUWuP3358I1AuG8AgM%3D\n作品图片2:https://p3-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_generation/a3ffaf4dff67c7e58c4fc885643bc615_1741676772797022643.jpeg~tplv-a9rns2rl98-image.jpeg?rk3s=25bff839&x-expires=1773212772&x-signature=c6ffRcNU11wWMqNXsms%2F8KDqGBE%3D\n作品图片3:https://p9-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_generation/b5667ae1754a29f6e069cfd812244d33_1741676780503652328.jpeg~tplv-a9rns2rl98-image.jpeg?rk3s=25bff839&x-expires=1773212780&x-signature=fiTrHsN2qhuOaAzayGVAfUfWkhg%3D\n作品图片4:https://p3-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_generation/43e6290f18ee5bc4903d76f9717a52b6_1741676793075906318.jpeg~tplv-a9rns2rl98-image.jpeg?rk3s=25bff839&x-expires=1773212793&x-signature=VwCh7DJ6ZqoVBY1rtsp%2FDEpxoZM%3D\n作品图片5:https://p3-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_generation/2da7068d0eccfbd948b49573a898497a_1741676796261828122.jpeg~tplv-a9rns2rl98-image.jpeg?rk3s=25bff839&x-expires=1773212796&x-signature=uIIiTkeWo8GWGoUoK4Qy4qxBunU%3D\n作品图片6:https://p3-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_generation/6983a7f631da8bd703f55446e97fb67b_1741676814574567326.jpeg~tplv-a9rns2rl98-image.jpeg?rk3s=25bff839&x-expires=1773212814&x-signature=NAgTrIWKVBnWYFyzSO17BZZM3%2FY%3D\n作品图片7:https://p9-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_generation/29b4f83ce72c120a37ab9c761b0db6d2_1741676819325501108.jpeg~tplv-a9rns2rl98-image.jpeg?rk3s=25bff839&x-expires=1773212819&x-signature=EsIuTJFNCHlwr9DV80%2BwpxyuqhQ%3D\n作品图片8:https://p3-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_generation/ef992168f008e0c1a1438e57aeeffaa3_1741676842016969033.jpeg~tplv-a9rns2rl98-image.jpeg?rk3s=25bff839&x-expires=1773212842&x-signature=Mu%2BocIGzO62zBluIFGLH1n6yRmw%3D\n作品图片9:https://p9-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_generation/0a1d13c8aa3e40b71330cf360695d22f_1741676843812404689.jpeg~tplv-a9rns2rl98-image.jpeg?rk3s=25bff839&x-expires=1773212843&x-signature=%2BzjzO2%2FxNBSr2qX0Av2dX6nBD7E%3D\n作品图片10:https://p3-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_generation/b1f5de2f3e308e5b9d67ddbd194f510b_1741676845622130502.jpeg~tplv-a9rns2rl98-image.jpeg?rk3s=25bff839&x-expires=1773212845&x-signature=TyicEtBcta4LyPlrM8t%2Be1bZTso%3D\n3. 联系表单（姓名/邮箱/需求描述）\n4. 社交媒体跳转链接\n\n设计美观度要求：\n5. 背景使用暗黑色系渐变叠加半透明遮罩，交互状态颜色变化使用HSB色相偏移。\n6. 主标题使用SF Pro系统字体，正文使用圆体组合。\n7. 卡片系统使用三层圆角+动态阴影层级，一行展示3张卡片，卡片间保持一定间距。卡片悬停时的缓动曲线动画，分类按钮点击时的颜色过渡动画\n8. 导航按钮使用胶囊造型+边框过渡动画。\n9. 社交媒体图标15度旋转交互，表单聚焦时的呼吸灯效果。\n\n技术实现要求：\n10. 使用CSS Grid瀑布流布局，所有间距采用15px基准单位\n11. 移动端优先的响应式断点\n12. 图片采用object-fit自适应裁剪\n13. 纯CSS实现所有视觉效果，无JavaScript依赖\n14. 系统字体跨平台兼容"
      },
      {
        "artifacts_image_url": "https://p9-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_generation/a1220ce66bcb1e69c0bc3f0c3470593a_1741840167095385618.png~tplv-a9rns2rl98-image.png?rk3s=25bff839&x-expires=1773376167&x-signature=CGZ4sKRICEjXFEbpPv5fWGE%2Bi40%3D",
        "artifacts_image_title": "气泡魔法世界",
        "artifacts_id": "202503032128338447E31D9C1BD4613034",
        "item_type": 5,
        "use_cache": false,
        "prompt": "需求：使用单文件HTML创建一个带有浮动气泡背景的网页，具有以下特点：\n\n功能逻辑要求：\n1. 页面约50个随机分布的气泡\n2. 气泡应有不同大小(5-25px)和半透明随机颜色\n3. 气泡应缓慢移动并有淡入淡出效果\n4. 页面中央显示网页的标题，文字应该有动画效果\n5. 添加一个带渐变和悬停效果的按钮，点击按钮后，在页面随机位置弹跳展示文案 “好运泡泡+N”，每次点击累计+1。\n6. 背景使用从蓝色到紫色的渐变"
      },
      {
        "artifacts_image_url": "https://p3-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_generation/284a11793388c52deb54d6d7debf0b5d_1741837641885514829.png~tplv-a9rns2rl98-image.png?rk3s=25bff839&x-expires=1773373641&x-signature=DmwdBtbdC4%2B1HVE7zZTba%2BSzjio%3D",
        "artifacts_image_title": "猫猫版 2048",
        "artifacts_id": "20250306173210AE32D539FFDA0C6A10E2",
        "item_type": 5,
        "use_cache": false,
        "prompt": "需求：用单文件HTML生成一个2048游戏\n功能要求：\n1. 界面设计采用响应式布局，确保在不同设备上都能良好展示。主体使用低饱和度的可爱彩色，样式美观协调。\n2.主要模块包含：标题、最高分/得分、游戏卡片、游戏规则\n3. 用以下图片替代原来2048中的数字，图片一定要正常展示：\n- 数字2：https://p9-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_generation/df099f209335f8b25e1d4ab9ce2867ff_1741677321668230149.png~tplv-a9rns2rl98-image.png?rk3s=25bff839&x-expires=1773213321&x-signature=reNM%2F907NNX7Rc%2BE4FflYbWaRKo%3D\n- 数字4：https://p9-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_generation/322b9826909d14458d22e0931fd06aac_1741677330491084517.png~tplv-a9rns2rl98-image.png?rk3s=25bff839&x-expires=1773213330&x-signature=a1ptVuhTOwjrpRZSdnL%2FUKYV9MY%3D\n- 数字8：https://p3-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_generation/1a563d103b7496a5b9eb365ff9ea58bd_1741677409407029394.png~tplv-a9rns2rl98-image.png?rk3s=25bff839&x-expires=1773213409&x-signature=L0drmZ0N1Z2%2BjUD9g9gcP0Nn7TM%3D\n- 数字16：https://p3-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_generation/f51aebc9e6d5eee19fc026e3f1070185_1741677412756659826.png~tplv-a9rns2rl98-image.png?rk3s=25bff839&x-expires=1773213412&x-signature=%2F%2FMLyi7m%2Bu%2Fo%2F%2FTQeYqmUWeDtLQ%3D\n- 数字32：https://p3-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_generation/8693822e52091fd6896366ae4e922bc2_1741677429462075923.png~tplv-a9rns2rl98-image.png?rk3s=25bff839&x-expires=1773213429&x-signature=tLOJO0F5DKTLStoKnja6l2fr%2FOg%3D\n- 数字64：https://p3-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_generation/5fbb02d79c0f16f69ec86ea7a83d7e36_1741677431738410576.png~tplv-a9rns2rl98-image.png?rk3s=25bff839&x-expires=1773213431&x-signature=Ry8L%2FkXszOCFFHkMeIdKm6Ktg%2FQ%3D\n- 数字128：https://p3-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_generation/f2c91103fc53286b5d481390c12d665f_1741677444595987213.png~tplv-a9rns2rl98-image.png?rk3s=25bff839&x-expires=1773213444&x-signature=X5AHPsnNLXS25hlANqlvTtpSQmc%3D\n- 数字256：https://p9-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_generation/9f4e790544e519f8c110d2c73e7b03bf_1741677446066399364.png~tplv-a9rns2rl98-image.png?rk3s=25bff839&x-expires=1773213446&x-signature=DmV9R3QrONwWz%2F4fUnoceUjiU8E%3D\n- 数字512：https://p3-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_generation/8f72db8a79741930894920bc8faced83_1741677458934448994.png~tplv-a9rns2rl98-image.png?rk3s=25bff839&x-expires=1773213458&x-signature=YxMhmlrT%2FY7brkyal9u5oaifd3g%3D\n- 数字1024: https://p3-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_generation/f565fa57b977fa953208f7a2b4edd839_1741677459756646895.png~tplv-a9rns2rl98-image.png?rk3s=25bff839&x-expires=1773213459&x-signature=r0bBAR7T7NVF3dyu1aGI%2FlWfR4w%3D\n- 数字2048：https://p3-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_generation/dbb80e7d3948b8af02664915eeee38d2_1741677502512862779.png~tplv-a9rns2rl98-image.png?rk3s=25bff839&x-expires=1773213502&x-signature=Bv%2FEdGFqr%2Bq47a8g1D4KiU9XYqQ%3D\n4. 通过键盘的方向键控制图片的移动和合并。\n5. 合并到2048后，提示：“恭喜你已经合并了一只宇宙无敌最可爱的猫咪”\n视觉要求：\n1. 页面背景颜色是#faf8ef\n2. 标题文字颜色和游戏规则的颜色都是#776e65，游戏规则的字号 14px\n3. 标题与最高分、得分排在一行，标题左对齐，高分、得分右对齐\n4. 游戏规则说明放在游戏卡片下面，左对齐游戏卡片"
      },
      {
        "artifacts_image_url": "https://p3-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_generation/e4246615666f6683049f999f624fb624_1741837730112614461.png~tplv-a9rns2rl98-image.png?rk3s=25bff839&x-expires=1773373730&x-signature=qTq992%2Bk6MStI8G%2BdLRAZl2qfS8%3D",
        "artifacts_image_title": "记忆力测试",
        "artifacts_id": "2025030616374222EC592597ADDA68049C",
        "item_type": 5,
        "use_cache": false,
        "prompt": "需求：生成一个HTML文件实现一个记忆力测试游戏。\n页面元素及视觉设计：\n1、背景：采用深色渐变背景（靛蓝色到紫色到深灰色）\n2、标题区：页面上方区域显示记忆力测试游戏，使用低饱和渐变文字效果（从浅紫色到浅粉色到浅靛蓝色），参考颜色#色值a5b4fc，字体使用微软雅黑\n3、计时区：标题下显示倒计时，默认为60秒，增加时间单位秒，增加类似tag的视觉效果，字号16px\n4、游戏区：页面中间为游戏主体，主体内按4X4划分为16张卡片，卡片尺寸110*110px，卡片分为四组，有正反面，每组正面分别展示一张照片链接：\n  照片一：https://p9-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_generation/13f9ea0cccf7d214a1164e8ef4d1350d_1741773629464734225.png~tplv-a9rns2rl98-image.png?rk3s=25bff839&x-expires=1773309629&x-signature=hFSc3eF0B5%2BxrXgmaqPcuvJ%2FcMw%3D\n  照片二：https://p9-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_generation/ea4ffebe6e55e0cd24f52f8174c736a0_1741773635605132415.png~tplv-a9rns2rl98-image.png?rk3s=25bff839&x-expires=**********&x-signature=A%2BurWIkTZt7D4AxokkGrYqLB%2BBw%3D\n  照片三：https://p3-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_generation/3884d96c52f4307d8ee35164b6917acb_1741773641327011703.png~tplv-a9rns2rl98-image.png?rk3s=25bff839&x-expires=**********&x-signature=wM7m1%2Fw1JzsNINhS%2BiSOLVme%2FIA%3D\n  照片四：https://p3-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_generation/077c37e8eae14fa5054881ed2706769f_1741773642128909041.png~tplv-a9rns2rl98-image.png?rk3s=25bff839&x-expires=**********&x-signature=r29Ds2hd5Kd3ZGW0R7iI8Q3eFIw%3D\n  卡片背面：填充与背景相近的紫色\n5、按钮区：游戏区下方默认显示开始游戏按钮，用户点击切换即重新开始按钮，按钮宽度尺寸240px。\n6、每张卡片使用圆角设计，稍微带一点描边光泽效果，卡片增加鼠标 hover 交互\n8、结果展示：文字字号是20px\n\n游戏逻辑：\n1. 初始状态：\n  - 计时区：默认60秒\n  - 游戏区：默认展示全部16张卡片，默认背面朝上，点击卡片即开始游戏\n  - 按钮区：默认展示开始游戏，点击按钮或者点击任一卡片，均可开始游戏\n2. 游戏状态：\n  - 计时区：开始倒计时，当倒计时清零，进入结果展示-失败状态\n  - 游戏区：先全部卡片正面朝上展示1秒，后全部翻转回背面，若用户翻出的两张卡片为同一张照片，集匹配成功，卡片保持正面朝上，全部16张卡片正面朝上游戏结束，进入结果展示状态-成功状态。若用户翻出的两张卡片为不同的照片，匹配失败，两张卡片同时翻回反面，支持用户再次点击\n  - 按钮区：展示重新开始状态，当用户点击回到初始状态\n3. 结果展示-失败状态\n  - 计时区：维持清零\n  - 游戏区：不再支持点击，下方显示用柔和艺术字体显示闯关失败提醒\n  - 按钮区：展示重新开始状态，用户点击进入游戏状态\n4. 结果展示-成功状态\n  - 计时区：维持不变\n  - 游戏区：不再支持点击，下方显示用柔和艺术字体显示闯关成功提醒\n  - 按钮区：展示重新开始状态，用户点击整个页面刷新为初始状态"
      },
      {
        "artifacts_image_url": "https://p9-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_generation/bc6a0734c22417d81363e7bc2d34eaab_1741837707326073369.png~tplv-a9rns2rl98-image.png?rk3s=25bff839&x-expires=1773373707&x-signature=yVDnshpMUjV27EDu6QDniMvUf78%3D",
        "artifacts_image_title": "生日贺卡",
        "artifacts_id": "20250303214658209867F59F7E1F6438ED",
        "item_type": 5,
        "use_cache": false,
        "prompt": "需求：使用单文件HTML创建互动生日贺卡：\n\n视觉美观度要求：\n1. 背景是逐渐从粉红色到金色的径向渐变\n2. 页面居中显示「Happy Birthday!」白色文字，字体使用Google Fonts的Dancing Script手写体\n3. 字体效果采用文字阴影+0.5秒缩放脉冲动画\n4. 页面随机飘落的爱心、气球、星星、蛋糕等类型的emoji\n5. 页面显示AI生成的随机的祝福语，祝福语使用幼圆字体，每次点击屏幕，更换新的祝福语\n6. 祝福语下方居中展示图片：https://p9-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_generation/6a30c75a0b0b77cb595a1b3362796d3b_1741678103707880520.png~tplv-a9rns2rl98-image.png?rk3s=25bff839&x-expires=1773214103&x-signature=5jO6t3cI6T8Ksk9oeiKTkDnP%2BOY%3D\n7. 鼠标移动过的位置出现蛋糕emoji的掉落"
      },
      {
        "artifacts_image_url": "https://p3-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_generation/c85f7fd2f2b7f445d967eccb6c927be5_1741837673826643713.png~tplv-a9rns2rl98-image.png?rk3s=25bff839&x-expires=1773373673&x-signature=oHok29pgsuGvuhXeBxpgvJHlR6w%3D",
        "artifacts_image_title": "BMI 计算器",
        "artifacts_id": "202503051629544C5C0C4066F2E20F9A17",
        "item_type": 5,
        "use_cache": false,
        "prompt": "需求：使用单文件的html生成一个BMI计算器\n功能要求：\n1. 界面设计采用响应式布局，确保在不同设备上都能良好展示，背景色偏浅的蓝紫渐变，样式美观协调。\n2. 页面展示输入框，标题是你的身高(cm)、你的体重(kg)，卡片稍微大一点，输入框与卡片边框间距保持一致。\n3. 用户点击计算按钮后，页面展示计算结果，需要包含具体BMI数值、体重分类(偏瘦/正常/超重/肥胖)\n4. 每一种体重分类给出以下提示，这些提示要用满屏动态弹幕的形式，从屏幕右侧飘到左侧。注意是满屏弹幕，弹幕间距合适，有一定速度差，来增加趣味性。弹幕文案用白色字体，低饱和彩色背景。\n（1）偏瘦：风一吹，你怕是要像风筝一样飘走咯！\n（2）正常：嘿，你这身材，老天爷赏饭吃的 “刚刚好” 呀！\n（3）超重：再胖点，你能去给熊猫当替身咯！\n（4）肥胖：你不是胖，你是可爱到膨胀啦\n视觉要求：\n5. 采用柔和偏蓝紫的渐变色按钮和阴影效果\n6. 输入框添加流畅的聚焦动画\n7. 响应式卡片式布局\n8. 弹幕使用圆角气泡样式和随机色相背景\n9. 结果展示区域使用强调色突出显示，可视化重点信息及数字\n10. 添加微交互动画（按钮悬停效果）\n11. 智能间距管理和字体层次\n12. 使用现代CSS变量维护配色方案"
      },
      {
        "artifacts_image_url": "https://p3-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_generation/6d8c8902eb0b91756a74fbdbdefb2c89_1741847260718573806.png~tplv-a9rns2rl98-image.png?rk3s=25bff839&x-expires=1773383260&x-signature=gXMaX%2FAY8xnix3jwUwIyRx1IzIk%3D",
        "artifacts_image_title": "做一份个人简历",
        "artifacts_id": "20250305192141698939FB0588461EB6FA",
        "item_type": 5,
        "use_cache": false,
        "prompt": "需求：用单文件html生成一份个人简历网页。 \n功能要求：\n1. 简历整个简历采用单一卡片布局，具有精致的质感和悬浮效果示，简历内容区采用白色，页面背景用高级的浅灰色，与页面背景保留合适上下左右间距，卡片内容占页面比例75%，整体样式美观协调。\n- 内容需包含个人信息、自我介绍、教育背景、工作经历、专业技能、项目经验等完整板块，内容尽可能丰富，视觉上强调名字与联系人信息，头像与名字、联系人信息左右布局，专业技能需要可视化表达\n2. 联系信息包含电话、邮箱、微信、GitHub地址、知乎地址等，联系信息字的颜色默认是高级的深灰色，点击可跳转。\n3. 自我介绍采用手写字体，自我介绍模块前面增加竖线\n4. 名字下面增加工作职位，比如全栈工程师\n5. 专业技能：用进度条、雷达图展示专业技能，进度条与雷达图的内容包含前端技能、后端技术、数据库、人工智能等，进度条上文字左对齐，样式美观协调，雷达图与进度条左右布局，雷达图尺寸大小与进度条保持一致。\n6. 请把进度条的展示拆分到技术语言维度\n7. 交互方面，期望加入平滑的页面滚动效果，在重要内容处设计渐入动画。\n8. 简历的教育背景、工作经历、项目经验，期望增加更多、更丰富、更个性的更真实内容描述，期望内容可以结构化表达呈现，比如使用一些数字序号或者无序序号。\n9. 可以用“王小明”作为示例，展示具体的简历内容，以下是王小明的照片，你可以使用它作为头像，头像布局展示有设计感，采用正圆的形式，头像尺寸宽是200像素，高也是200像素，增加鼠标悬停交互：https://p3-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_generation/459c82913030032351c934812b7ddd09_1741676356514132792.png~tplv-a9rns2rl98-image.png?rk3s=25bff839&x-expires=1773212356&x-signature=OvpjH6KB8ArCF44r9%2FHy91n%2BKCQ%3D\n视觉设计：\n- 颜色系统：使用低饱和度配色方案，通过CSS变量统一管理\n- 动态阴影：卡片悬停时产生自然的悬浮效果\n- 微交互：联系方式有链接的悬停颜色变化，增加图标\n- 精致排版：合理的行高、间距和字体、字号颜色层次，描述字体是浅灰色，行高更紧凑一些，适当增加一些浅色的分割线，去掉底部的分割线\n- 质感效果：经典纸张卡片的质感"
      },
      {
        "artifacts_image_url": "https://p9-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_generation/7c30c3c66ca641ddbd6dba5d85879f85_1741837679560423715.png~tplv-a9rns2rl98-image.png?rk3s=25bff839&x-expires=1773373679&x-signature=HynUZkRdoV%2FYmdJu7M2Hou8k9sM%3D",
        "artifacts_image_title": "职场人已读乱回",
        "artifacts_id": "20250304111503CCB71A19E0E21E489D4D",
        "item_type": 5,
        "use_cache": false,
        "prompt": "使用单文件HTML创建一个职场主题的接消息游戏，要求：\n1. 动态渐变背景：深灰渐变（#2D2F33 → #1A1A1A）\n2. 标题样式：「职场已读乱回」使用故障艺术字体效果（文字抖动+颜色分离），并添加像素风边框装饰\n3. 掉落消息：\n  - 从画面上方会随机快速掉落10种不同的「打工人的敷衍回复」\n  - 消息使用不同颜色气泡（不同渐变色+图标）包裹：\n    白领蓝：#6C8EB4 → #B4C7E7\n    糊弄橙：#F4B183 → #F8C471\n    卷王红：#FF6666 → #FF9999 \n4. 承接区域：\n  - 承接区域始终位于页面底部，可以通过鼠标或键盘可以控制承接区域的移动\n  - 控制承接区域移动时，能够敏捷的改变位置\n  - 承接区域玻璃质感背景（backdrop-filter），移动时产生拖尾残影效果\n5. 动态反馈：\n  - 成功接住时： 屏幕中间从中心到四周绽放金色粒子特效，页面弹出提示「功德+N」N展示累积值\n6. 技术实现：\n  - 使用CSS变量管理主题色\n  - 碰撞检测通过getBoundingClientRect实现\n  - 所有动画使用requestAnimationFrame\n  - 移动端优化：禁用默认触摸行为\n  - 内置性能模式（自动降低特效质量）\n7. 美术资源：\n  - 所有图标用SVG符号内联定义\n  - 音效通过振荡器生成（无外部文件），声音频率柔和，不要过于刺耳\n  - 粒子效果用纯CSS实现\n  - 禁用外部资源加载"
      },
      {
        "artifacts_image_url": "https://p3-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_generation/42b92eb1ae5c26dbe214d96adf040450_1741837722130331791.png~tplv-a9rns2rl98-image.png?rk3s=25bff839&x-expires=1773373722&x-signature=ZRtubGrQWG2rjFYdgTdGB4iZcHc%3D",
        "artifacts_image_title": "销售数据看板",
        "artifacts_id": "20250306195221DC5396123C5CB8006620",
        "item_type": 5,
        "use_cache": false,
        "prompt": "使用单文件HTML生成以下销售数据看板网页\n页面布局描述\n1. 整体布局\n- 页面采用左右分栏布局，左侧为导航栏，右侧为主内容区域\n- 整体背景色为浅蓝色渐变(#F5F9FF)，内容区域使用白色卡片配合轻微阴影效果\n- 页面宽度自适应，支持响应式设计\n\n2. 左侧导航栏\n- 宽度约占页面的15-20%，顶部有品牌标识\"BoltKit\"，配有蓝色闪电图标，使用深色字体\n- 导航菜单项包括：实时看板(当前选中)、订单、产品、文件夹、客户管理等，菜单项使用图标+文字组合，图标位于文字左侧\n- 选中的菜单项背景为深蓝色(#2563EB)或深灰色(#4B5563)，菜单项字体为中等大小(14-16px)，颜色为深灰色(#333333)，菜单项图标使用线性图标，颜色与文字一致\n- 底部有额外的设置类菜单项，如个人设置、系统设置等\n\n3. 右侧主内容区域\n- 顶部有标题\"实时看板\"，使用黑色粗体字，字号约24-28px，标题下方可能有欢迎文本或页面描述，使用灰色(#666666)常规字体\n- 右上角有全局搜索栏和通知图标，内容区域分为多个部分：指标卡片区、数据图表区、ECharts图表区\n\n4. 指标卡片区\n- 包含三到四个指标卡片，每行排列，间距均匀，每个卡片有圆角和轻微阴影效果\n- 卡片内部结构：左侧指标名称和数值，右侧百分比变化和迷你图表\n- 指标卡片详情：\n  - 活跃用户(5,653)，增长+22.42%(绿色)，迷你柱状图\n  - 新用户(1,650)，增长+15.44%(绿色)，迷你柱状图\n  - 总访问量(5,420)，下降-10.24%(红色)，迷你柱状图\n  - 迷你柱状图使用ECharts\n\n5. ECharts图表实现要点\n- 分为多个卡片式区块，每块区域中有一个图表，四宫格排列\n- 图表类型包括：\n  - 柱状图：展示流量来源或销售数据，带有百分比标签\n  - 饼图：展示用户年龄分布(0-18岁、19-30岁、30-40岁、其他)\n  - 线形图：展示时间趋势数据\n  - 水平条形图：展示社交媒体表现(Facebook、Instagram、Twitter等)\n\n6. 字体和颜色系统\n- 主要字体：无衬线字体(如Helvetica或Inter)\n- 品牌主色：蓝色(#2563EB)\n- 标题颜色：黑色(#000000)或深灰色(#333333)\n- 正文颜色：中灰色(#666666)\n- 指标数值：黑色粗体(#000000)\n- 正向变化百分比：绿色(#4CAF50或#10B981)\n- 负向变化百分比：红色(#F44336或#EF4444)\n- 图表颜色：主要使用蓝色系列，辅以黄色、绿色等作为对比\n\n功能交互逻辑要求\n7. 导航交互\n- 点击导航菜单项可切换到相应页面\n- 当前选中的菜单项保持高亮状态\n- 顶部搜索栏支持全局搜索功能\n\n8. 指标卡片交互\n- 卡片支持悬停效果，悬停时轻微提升阴影深度\n- 点击卡片可跳转到相应详情页面\n- 百分比变化根据数值自动显示颜色(正值为绿色，负值为红色)\n- 迷你图表展示该指标的历史趋势\n\n9. 图表交互功能\n- 所有图表支持数据筛选功能(时间范围、数据维度等)\n- 鼠标悬停在图表元素上时，显示详细数据tooltip\n- 柱状图支持点击查看详情功能\n- 环形图支持点击切换不同分类的详细数据\n\n10. 响应式设计逻辑\n- 大屏幕(>1200px)：完整显示所有元素，左侧导航栏展开\n- 中等屏幕(768px-1200px)：指标卡片适当缩小，图表自适应缩放\n- 小屏幕(<768px)：\n  - 导航栏默认折叠，可通过按钮展开\n  - 指标卡片改为每行一个，垂直排列\n  - 图表垂直排列，宽度100%"
      },
      {
        "artifacts_image_url": "https://p9-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_generation/f6263a3040e027421d49b3892a073160_1741837780114300671.png~tplv-a9rns2rl98-image.png?rk3s=25bff839&x-expires=1773373780&x-signature=2nLgunyVUVQdvmjF%2FLPzbr3vPXA%3D",
        "artifacts_image_title": "SaaS 平台产品官网",
        "artifacts_id": "20250307160020809565B07ED59C4F9075",
        "item_type": 5,
        "use_cache": false,
        "prompt": "用单HTML文件生成平台产品官网\n1. 页面整体布局与设计\n- 页面采用现代、简洁的设计风格。\n- 主色调为白色，辅以黑色和浅紫色（按钮）。\n- 使用无衬线字体，提供清晰易读的视觉体验。\n- 页面内容居中显示，两侧留白，提供舒适的视觉空间。\n\n2. 顶部导航栏\n- 固定在页面顶部。\n- 左侧显示 Logo：\"Lovely\"（黑色圆形背景）。\n- 中间显示导航链接：\"功能\", \"定价\", \"关于我们\"。\n- 右侧显示操作按钮：\n  - \"登录\"（文本链接）。\n  - \"免费试用\"（浅紫色背景按钮）。\n\n3. 英雄区（Hero Section）\n- 大标题：\"构建您的下一个 SaaS 平台\"（大字号，黑色）。\n- 副标题：一段描述性文字（灰色，较小字号）。\n- 操作按钮：\n  - \"立即开始\"（黑色背景按钮）。\n  - \"了解更多\"（文本链接）。\n- 背景图：https://p3-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_generation/698c6b92200ce778c6d504711ddad14a_1741676559693567649.jpeg~tplv-a9rns2rl98-image.jpeg?rk3s=25bff839&x-expires=1773212559&x-signature=qGR6OZo4EXnztqMSLEtyOP8gHf4%3D\n\n4. 信任标识区\n- 标题：\"受您熟知的热门初创公司信赖\"（灰色，小字号）。\n- 下方展示三个logo。\n  - logo1:https://p9-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_generation/de0657782dc01c396884b7c85532bcb9_1741676433461770969.jpeg~tplv-a9rns2rl98-image.jpeg?rk3s=25bff839&x-expires=1773212433&x-signature=J7fV4VQyU8nKx1yuBx3h6RZaZEs%3D\n  - logo2:https://p3-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_generation/82f7e943302c25dcd2668412559bbfc2_1741676521967071416.jpeg~tplv-a9rns2rl98-image.jpeg?rk3s=25bff839&x-expires=1773212522&x-signature=OUM6LPQy0VbclS1JIy1bHiuVKtw%3D\n  - logo3:https://p9-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_generation/54864213467edcaf9f485721042361ba_1741676537082617644.jpeg~tplv-a9rns2rl98-image.jpeg?rk3s=25bff839&x-expires=1773212537&x-signature=ha9ZXKn4bWuNPpp4OaLfA66nGAk%3D\n\n5. 核心功能区\n- 标题：\"提升您的工作流程\"（大字号，黑色）。\n- 副标题：描述性文字（灰色，较小字号）。\n- 采用两栏布局：\n（1）左侧：\n  - 模块标题：\"智能任务管理\"\n  - 描述文字\n  - 功能列表（带勾选图标）：\n    - 智能任务分配\n    - 实时协作\n    - 数据安全与隐私\n  - 操作按钮：\n    - \"开始 14 天试用\"（黑色背景按钮）\n    - \"联系销售\"（文本链接）\n（2）右侧：一个图片占位符。\n  - 图片资源可使用 [https://p3-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_generation/cd9623c20d45bbc713cc4963d1497878_1741676569049154135.jpeg~tplv-a9rns2rl98-image.jpeg?rk3s=25bff839&x-expires=1773212569&x-signature=a68ifjODqgC8EclaUNWWyYlWZx4%3D](https://p3-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_generation/cd9623c20d45bbc713cc4963d1497878_1741676569049154135.jpeg~tplv-a9rns2rl98-image.jpeg?rk3s=25bff839&x-expires=1773212569&x-signature=a68ifjODqgC8EclaUNWWyYlWZx4%3D)\n  - 内容之间使用内边距和外边距进行分隔。\n（1）右侧：\n  -  模块标题：\"高级分析\"\n  - 描述文字\n  - 功能列表（带勾选图标）：\n    - 智能受众细分\n    - 预测性能\n    - 实时可视化\n  - 操作按钮：\n    -  \"开始 14 天试用\"（黑色背景按钮）\n    -  \"联系销售\"（文本链接）\n（2）左侧：一个图片占位符。\n  - 图片资源可使用 [https://p9-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_generation/5247eb0d8bf1f68ec2604b4b93b91073_1741676579771426625.jpeg~tplv-a9rns2rl98-image.jpeg?rk3s=25bff839&x-expires=1773212579&x-signature=Ez9PgFO50n3nkXJKOxYWC7zZmTQ%3D](https://p9-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_generation/5247eb0d8bf1f68ec2604b4b93b91073_1741676579771426625.jpeg~tplv-a9rns2rl98-image.jpeg?rk3s=25bff839&x-expires=1773212579&x-signature=Ez9PgFO50n3nkXJKOxYWC7zZmTQ%3D)\n  - 内容之间使用内边距和外边距进行分隔。\n\n6. 页脚\n- Logo 和描述：\"Lovely\"（黑色圆形背景），\"面向企业的现代 SaaS 平台\"。\n- \"产品\" 链接：功能、定价、文档\n- \"公司\" 链接：关于我们、博客、职业机会\n- \"法律\" 链接：隐私、条款、许可证\n- 版权信息：\"© 2024 Lovely. 保留所有权利。\"\n- 社交媒体链接：Twitter, GitHub, Discord.\n\n7. 页面交互逻辑\n- 导航栏链接：点击后平滑滚动到页面对应锚点（如 \"功能\" 滚动到核心功能区）。\n- 按钮：\n  - \"免费试用\"、\"立即开始\"：通常链接到注册或试用页面。\n  - \"开始 14 天试用\"：链接到试用申请页面。\n  - \"联系销售\"：链接到联系销售团队的页面。\n- 鼠标悬停效果：\n  - 按钮：改变背景颜色或添加阴影，提供视觉反馈。\n  - 链接：改变文本颜色或添加下划线。\n\n8. 响应式设计要求\n- 小屏幕（手机）：\n  - 导航栏：Logo 缩小，导航链接隐藏到汉堡菜单中，操作按钮垂直排列。\n  - 英雄区：标题和副标题堆叠显示，按钮垂直排列。\n  - 核心功能区：两栏布局改为单列布局，图片显示在文本上方或下方。\n  - 页脚：各部分垂直堆叠。\n- 大屏幕（桌面）：\n  - 所有元素保持原始设计，根据屏幕宽度调整间距和留白。\n\n9. 字体和颜色\n- 主要字体：无衬线字体（例如，Inter, Open Sans, Roboto 等）。\n- 标题字体：可以使用略微不同的无衬线字体，或保持一致。\n- 颜色：\n  - 主文本：#000000（黑色）\n  - 副文本/描述：#666666（深灰色）\n  - 按钮（主要）：#000000（黑色）\n  - 按钮（次要）：#E0E0FF（浅紫色）\n  - 链接：#000000（黑色，悬停时可变为浅紫色）\n\n10. 技术实现注意事项\n- 使用 HTML5 语义化标签（如 <header>, <nav>, <main>, <article>, <footer> 等）。\n- 使用 CSS Flexbox 或 Grid 进行布局。\n- 使用媒体查询（Media Queries）实现响应式设计。\n- 使用 JavaScript 实现交互效果（如平滑滚动、汉堡菜单等）。\n- 图片占位符可以使用 <img src=\"\" alt=\"\"> 或背景图片实现。\n- 使用 CSS 变量来定义颜色值，提高代码的可维护性。"
      },
      {
        "artifacts_image_url": "https://p9-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_generation/6bdd0a22b0b956c8ee20fe81848dd316_1741837657634499920.png~tplv-a9rns2rl98-image.png?rk3s=25bff839&x-expires=**********&x-signature=LNt1o9QTvPaKxAbhby6TzYdPn7s%3D",
        "artifacts_image_title": "APP 产品官网",
        "artifacts_id": "20250311135451BBD06D21235BF8C9102F",
        "item_type": 5,
        "use_cache": false,
        "prompt": "使用一个HTML文件生成以下网页：\n1. 页面布局描述\n- 顶部导航栏：\n  - 左侧：BankApp 标志\n  - 右侧：功能、定价、关于、登录、立即开始 按钮 (额外添加css: align-item:center)\n- 主内容区域：\n  - 标题：更改您使用的银行账户\n  - 小标题：体验重新构想的银行业务。简单、安全，专为现代世界设计。\n  - 两个按钮：立即开始、了解更多\n- 图片：可正确显示的英文数据看板图片，图片链接（https://p3-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_generation/f79cee5b8986d56b9d67eb754d2c0fb0_1741677298377196362.jpeg~tplv-a9rns2rl98-image.jpeg?rk3s=25bff839&x-expires=**********&x-signature=x99wKGT1vj9papUAhuUZoG8LJFY%3D）\n- 功能模块标题：为您设计的功能\n- 功能模块：\n  - 安全交易：银行级加密保障所有交易\n  - 实时更新：即时获取账户活动通知\n  - 智能储蓄：AI驱动的洞察助您节省更多\n- 常见问题区域：\n  - 标题：常见问题解答\n  - 问题列表：我的数据有多安全？、费用是多少？、如何开始使用？\n- 底部区域：\n  - 号召性用语：准备好开始了吗？加入数千名已经转换银行的满意客户。\n  - 按钮：创建账户\n  - 底部导航：互相之间加上适当的间距（50px左右），样式美观\n    - 产品：功能、定价、安全\n    - 公司：关于我们、职业发展、联系我们\n    - 资源：文档、博客、帮助中心\n    - 法律：隐私、条款、Cookies\n  - 社交媒体链接：Twitter、LinkedIn\n  - 版权信息：© 2024 BankApp 版权所有\n\n2. 字体类型和色号：\n- 字体：无衬线字体（如 Arial 或 Helvetica）\n- 背景色：#F5F5F5\n- 文字颜色：#333333\n- 按钮背景色：#007BFF\n- 按钮文字颜色：#FFFFFF\n- 登录按钮颜色：#F5F5F5\n- 登录按钮文字颜色：#000\n- 立即开始按钮颜色: #000\n- 立即开始按钮文字颜色 #FFFFFF\n- 创建账户按钮颜色(#007BFF)\n- 创建账户按钮文字颜色#000\n- 链接颜色：#007BFF\n- 边框：border-radius: 8px\n\n3. 页面功能交互逻辑：\n- 顶部导航栏：\n  - 添加浅色背景色增强区域区分度\n  - 点击 功能、定价、关于 跳转页面，如果无法跳转，请添加提示信息，至少要有点击反馈\n  - 点击 登录 打开登录弹窗，至少要有点击反馈\n  - 点击 立即开始 滚动至创建账户按钮\n- 主内容区域：\n  - 点击 立即开始 滚动至创建账户按钮\n  - 点击 了解更多 跳转至功能详情页，如果无法跳转，请添加提示信息，至少要有点击反馈\n- 常见问题区域：\n  - 点击问题展开答案，至少要有点击反馈\n- 底部区域：\n  - 添加背景色增强区域区分度，\n  - 点击 创建账户 打开注册表单，至少要有点击反馈\n  - 点击底部导航链接跳转页面，至少要有点击反馈\n  - 社交媒体链接在新标签页打开，至少要有点击反馈\n\n4. 响应式设计实现逻辑：\n- 手机端：\n  - 顶部导航栏显示汉堡菜单\n  - 主内容按钮和功能模块垂直排列\n  - 常见问题垂直布局\n  - 底部导航链接垂直排列\n- 桌面端：保持原始布局和间距"
      }
    ]
  }
}
`

var PromptTemplatesDefault = `
{
	"language": "zh",
	"prompt_list": [{
			"key": "web_ai_coding_prompt_md2html",
			"name": "文本秒变网页",
			"icon_type": "md2html",
			"template": "  1. 分析以下内容，并将其转化为清晰美观的可视化网页，使用{s_style_select}：{s_paste_content}",
			"variable_list": [{
					"key": "s_style_select",
					"placeholder": "风格选择",
					"variable_type": "radio",
					"options": [{
							"value": "自由发挥风格",
							"label": "自由发挥风格"
						},
						{
							"value": "极简主义风格",
							"label": "极简主义风格"
						},
						{
							"value": "现代扁平化风格",
							"label": "现代扁平化风格"
						},
						{
							"value": "Brutalism 风格",
							"label": "Brutalism 风格"
						},
						{
							"value": "新波普艺术风格",
							"label": "新波普艺术风格"
						},
						{
							"value": "简约高级灰风格",
							"label": "简约高级灰风格"
						},
						{
							"value": "商务简报风格",
							"label": "商务简报风格"
						},
						{
							"value": "暗黑科技风格",
							"label": "暗黑科技风格"
						},
						{
							"value": "儿童童话风格",
							"label": "儿童童话风格"
						},
						{
							"value": "优雅复古风格",
							"label": "优雅复古风格"
						},
						{
							"value": "清新自然风格",
							"label": "清新自然风格"
						},
						{
							"value": "水彩艺术风格",
							"label": "水彩艺术风格"
						},
						{
							"value": "手绘风格",
							"label": "手绘风格"
						}
					]

				},
				{
					"key": "s_paste_content",
					"placeholder": "粘贴内容详情，如财报数据、旅游攻略、新闻资讯、论文总结等",
					"variable_type": "input"
				}
			]
		},
		{
			"key": "web_ai_coding_prompt_title_explain",
			"name": "代码解释",
			"icon_type": "explain",
			"template": "解释这段代码【粘贴代码】，要求：\n1. 简要总结代码的功能和使用场景\n2. 按照实现逻辑，拆分代码的核心模块，每个模块进行逐一解释\n3. 尽量通过比喻、类比等表达方式，帮助我理解代码中的抽象概念或复杂逻辑",
			"variable_list": []
		},
		{
			"key": "web_ai_coding_prompt_content_fix",
			"name": "代码修复",
			"icon_type": "fix",
			"template": "修复这段代码【粘贴代码】，要求：\n1. 对代码进行错误分析，指出错误点、错误原因以及修复思路\n2. 给出修复后的代码，并用注释标出主要的改动处，方便我理解改动逻辑",
			"variable_list": []
		},
		{
			"key": "web_ai_coding_prompt_content_translate",
			"name": "代码翻译",
			"icon_type": "translate",
			"template": "将【粘贴代码】转换为【目标语言】，要求： \n1. 代码功能保持一致，语法符合目标语言习惯 \n2. 给出转换后的代码和调用示例",
			"variable_list": []
		},
		{
			"key": "web_ai_coding_prompt_content_practice",
			"name": "编程解题",
			"icon_type": "practice",
			"template": "分析以下题目，并给出对应的答案解析【粘贴题目】，要求：\n1. 分析题目，给出简要的解题思路\n2. 提供最终答案或示例代码\n3. 必要时对答案进行解释或补充",
			"variable_list": []
		},
		{
			"key": "web_ai_coding_prompt_content_knowledge",
			"name": "知识问答",
			"icon_type": "knowledge",
			"template": "回答下述编程知识或问题【描述问题】，要求： \n1. 尽量通过比喻、类比等表达方式，帮助我理解代码中的抽象概念，说出问题的核心和本质\n2. 如有必要可以提供对应的代码示例和说明\n3. 如有必要可以指出新手容易误解的点",
			"variable_list": []
		},
		{
			"key": "web_ai_coding_prompt_content_sql",
			"name": "SQL 生成",
			"icon_type": "sql",
			"template": "你是一个专业的数据库管理员(DBA)，你的主要任务是根据以下信息生成准确的 SQL 语句\n1. 数据库类型：【输入内容】\n2. 表结构：【输入内容】\n3. 需求描述：【输入内容】",
			"variable_list": []
		},
    	{
			"key": "web_ai_coding_prompt_data_analysis",
			"name": "数据分析",
			"icon_type": "data_analysis",
			"template": "你是一个专业的数据分析师",
			"variable_list": []
		},
	]
}
`
