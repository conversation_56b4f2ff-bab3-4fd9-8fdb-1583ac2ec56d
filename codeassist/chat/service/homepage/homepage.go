package homepage

import (
	"context"
	"encoding/json"
	"math/rand"
	"strings"
	"time"

	"code.byted.org/gopkg/logs/v2"
	"github.com/samber/lo"

	"code.byted.org/devgpt/kiwis/codeassist/chat/entity"
	"code.byted.org/devgpt/kiwis/codeassist/chat/service"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/tcc"
)

const codebaseReturnCount = 5

type Impl struct {
	HomepageTccConfig *tcc.GenericConfig[config.HomepageConfig]
	PromptTccConfig   *tcc.GenericConfig[config.CodeassistPromptTemplatesConfig]
}

var _ service.HomepageService = &Impl{}

func (s *Impl) GetHomepageConfig(ctx context.Context, abVariables map[string]any) string {
	homepageConfig := s.HomepageTccConfig.GetPointer()
	if homepageConfig == nil {
		logs.V1.CtxWarn(ctx, "[GetHomepageConfig] not get tcc homepage config, use default config")
		return ""
	}

	result := &entity.Homepage{
		Recommendations: entity.Recommendations{
			RecommendationItems: lo.Map(homepageConfig.Recommendations.RecommendationItems, func(item config.RecommendationItem, _ int) entity.RecommendationItem {
				return entity.RecommendationItem{
					ItemType: item.ItemType,
					Key:      item.Key,
					Name:     item.Name,
					IconType: item.IconType,
				}
			}),
		},
		CodebaseTemplates: randomSelectCodebaseItems(homepageConfig.CodebaseTemplates),
	}

	var openArtifactCategory bool
	openTemplateCategoryABInterface, ok := abVariables["artifact_template_category"]
	if ok {
		if val, _ := openTemplateCategoryABInterface.(bool); val {
			openArtifactCategory = true
		}
	}
	logs.V1.CtxInfo(ctx, "[GetHomepageConfig] hit status: %v, abParams: %v", openArtifactCategory, abVariables)
	if openArtifactCategory {
		result.ArtifactsCategoryTemplates = lo.ToPtr(entity.ArtifactsCategoryTemplates{
			ArtifactsCategoryItems: lo.Map(homepageConfig.ArtifactsCategoryTemplates.ArtifactsCategories,
				func(category config.ArtifactCategory, _ int) entity.ArtifactsCategoryItem {
					return entity.ArtifactsCategoryItem{
						ArtifactsCategoryID:   category.CategoryID,
						ArtifactsCategoryName: category.CategoryName,
						ArtifactsItems: lo.Map(category.ArtifactItems, func(item config.ArtifactItem, _ int) entity.ArtifactItem {
							return entity.ArtifactItem{
								ArtifactsImageURL:     item.ArtifactsImageURL,
								ArtifactsImageTitle:   item.ArtifactsImageTitle,
								ArtifactsID:           item.ArtifactsID,
								HomePageItemType:      item.HomePageItemType,
								UseCache:              item.UseCache,
								Prompt:                item.Prompt,
								AdvertisingArticleUrl: item.AdvertisingArticleUrl,
							}
						}),
					}
				}),
		},
		)
	} else {
		result.ArtifactsTemplates = lo.ToPtr(entity.ArtifactsTemplates{
			ArtifactsItems: lo.Map(homepageConfig.ArtifactsTemplates.ArtifactsItems,
				func(item config.ArtifactItem, _ int) entity.ArtifactItem {
					return entity.ArtifactItem{
						ArtifactsImageURL:     item.ArtifactsImageURL,
						ArtifactsImageTitle:   item.ArtifactsImageTitle,
						ArtifactsID:           item.ArtifactsID,
						HomePageItemType:      item.HomePageItemType,
						UseCache:              item.UseCache,
						Prompt:                item.Prompt,
						AdvertisingArticleUrl: item.AdvertisingArticleUrl,
					}
				}),
		})
	}
	openDataAnalysis := isHitAbTest(abVariables, "open_web_ai_coding_prompt_data_analysis")
	if !openDataAnalysis {
		result.Recommendations.RecommendationItems = lo.Filter(result.Recommendations.RecommendationItems, func(item entity.RecommendationItem, _ int) bool {
			return item.Key != entity.RecommendationKeyDataAnalysis
		})
	}

	resultBytes, err := json.Marshal(result)
	if err != nil {
		logs.V1.CtxError(ctx, "[GetHomepageConfig] marshal json fail, err: %v", err)
		return ""
	}
	return string(resultBytes)
}

func randomSelectCodebaseItems(codebaseTemplates config.CodebaseTemplates) entity.CodebaseTemplates {
	rng := rand.New(rand.NewSource(time.Now().UnixNano()))
	items := codebaseTemplates.CodebaseItems

	// 原始数据不足，全部映射
	if len(items) <= codebaseReturnCount {
		return entity.CodebaseTemplates{
			CodebaseItems: lo.Map(items, func(item config.CodebaseItem, _ int) entity.CodebaseItem {
				return entity.CodebaseItem{
					ItemType:            item.ItemType,
					Query:               item.Query,
					IconURL:             item.IconURL,
					UseCache:            item.UseCache,
					Answer:              item.Answer,
					CodebaseURL:         item.CodebaseURL,
					CodebaseName:        item.CodebaseName,
					CodebaseID:          item.CodebaseID,
					CodebaseBranchName:  item.CodebaseBranchName,
					CodebaseDescription: item.CodebaseDescription,
				}
			}),
		}
	}

	rng.Shuffle(len(items), func(i, j int) {
		items[i], items[j] = items[j], items[i]
	})
	selectedItems := items[:codebaseReturnCount]

	return entity.CodebaseTemplates{
		CodebaseItems: lo.Map(selectedItems, func(item config.CodebaseItem, _ int) entity.CodebaseItem {
			return entity.CodebaseItem{
				ItemType:            item.ItemType,
				Query:               item.Query,
				IconURL:             item.IconURL,
				UseCache:            item.UseCache,
				Answer:              item.Answer,
				CodebaseURL:         item.CodebaseURL,
				CodebaseName:        item.CodebaseName,
				CodebaseID:          item.CodebaseID,
				CodebaseBranchName:  item.CodebaseBranchName,
				CodebaseDescription: item.CodebaseDescription,
			}
		}),
	}
}

func (s *Impl) GetPromptTemplate(ctx context.Context, language string, abVariables map[string]any) string {
	promptTemplates := s.PromptTccConfig.GetPointer()
	if promptTemplates == nil {
		// 本地配置兜底
		logs.V1.CtxWarn(ctx, "not get tcc homepage config, use default config")
		return ""
	}
	result := s.getPromptTemplatesByLang(ctx, *promptTemplates, language)
	resultBytes, err := json.Marshal(result)
	if err != nil {
		return ""
	}
	return string(resultBytes)
}

func (s *Impl) getPromptTemplatesByLang(
	ctx context.Context, promptTemplates config.CodeassistPromptTemplatesConfig, lang string,
) entity.PromptTemplates {
	if lang == "" {
		return entity.ConfigPromptTemplatesToEntity(promptTemplates.ZH, entity.LanguageZH)
	}
	switch {
	case strings.ToLower(lang) == entity.LanguageZH:
		return entity.ConfigPromptTemplatesToEntity(promptTemplates.ZH, entity.LanguageZH)
	case strings.ToLower(lang) == entity.LanguageEN:
		return entity.ConfigPromptTemplatesToEntity(promptTemplates.EN, entity.LanguageEN)
	default:
		logs.V1.CtxWarn(ctx, "invalid codebase template language: %s", lang)
		return entity.ConfigPromptTemplatesToEntity(promptTemplates.ZH, entity.LanguageZH)
	}
}

func isHitAbTest(abVariables map[string]any, key string) bool {
	if abVariables == nil {
		return false
	}
	abTestInterface, ok := abVariables[key]
	if !ok {
		return false
	}
	abTest, _ := abTestInterface.(bool)
	return abTest
}
