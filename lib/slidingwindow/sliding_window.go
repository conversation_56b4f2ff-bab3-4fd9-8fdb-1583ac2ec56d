package slidingwindow

type SlidingWindow struct {
	window      []byte
	size        int
	addedSize   int
	triggerSize int
}

func NewSlidingWindow(size int, triggerSize int) *SlidingWindow {
	if size < triggerSize {
		panic("size must be greater than triggerSize")
	}
	return &SlidingWindow{
		window:      make([]byte, size),
		size:        size,
		addedSize:   0,
		triggerSize: triggerSize,
	}
}

func (s *SlidingWindow) Add(b []byte) ([]byte, bool) {
	s.window = append(s.window, b...)
	if len(s.window) > s.size {
		s.window = s.window[len(s.window)-s.size:]
	}

	s.addedSize += len(b)
	if s.addedSize >= s.triggerSize {
		s.addedSize = 0
		return s.window, true
	}
	return s.window, false
}

func (s *SlidingWindow) AddString(str string) (string, bool) {
	content, ok := s.Add([]byte(str))
	return string(content), ok
}

func (s *SlidingWindow) GetWindow() []byte {
	return s.window
}

func (s *SlidingWindow) GetWindowString() string {
	return string(s.window)
}
