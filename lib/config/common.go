package config

type LarkBotConfig struct {
	AppID     string `yaml:"AppID" env:"LARK_APP_ID"` // TODO(caoyunxiang): load secret config from TCC.
	AppSecret string `yaml:"AppSecret" env:"LARK_APP_SECRET"`
}

// RDSConfig is the general bytedance mysql DB config.
type RDSConfig struct {
	PSM                     string  `yaml:"PSM"`
	DBName                  string  `yaml:"DBName"`
	LogLevel                *string `yaml:"LogLevel,omitempty"`
	WithReadReplicas        *bool   `yaml:"WithReadReplicas,omitempty"`
	ClientFoundRows         *bool   `yaml:"ClientFoundRows,omitempty"` // 当设置为true，更新操作返回匹配的纪录数而不是实际的修改数；当设置为false时，如果更新内容一样，返回的RawsAffected为0
	WithSecurityScanSupport bool    `yaml:"WithSecurityScanSupport"`   // 是否启用安全扫描支持 (https://bytedance.larkoffice.com/wiki/XQlXwoCdui1XjmkTdLLcny3Gn0f)
}

// TCCConfig is the general bytedance tcc config.
type TCCConfig struct {
	PSM string `yaml:"PSM"`
}

// TOSConfig is the general bytecloud TOS config.
type TOSConfig struct {
	Bucket    string `yaml:"bucket"`
	AccessKey string `yaml:"access_key"`
	SecretKey string `yaml:"secret_key"`
}

type KaniConfig struct {
	Namespace       string `yaml:"Namespace"`
	BaseURL         string `yaml:"BaseURL"`
	Location        string `yaml:"Location"`
	ProtegoBaseURL  string `yaml:"ProtegoBaseURL"`
	WorkflowBaseURL string `yaml:"WorkflowBaseURL"`
}

type RocketMQConfig struct {
	Cluster       string `yaml:"Cluster"`
	Topic         string `yaml:"Topic"`
	ConsumerGroup string `yaml:"ConsumerGroup"`
	// ConsumeTags indicates which events with the given tags will be consumed.
	ConsumeTags string `yaml:"ConsumeTags"`
	// WorkerCount indicates how many workers will be used to consume messages.
	// For rocketmq, the default value is 10.
	WorkerCount int `yaml:"WorkerCount"`
	// SwimlaneV1, boe env name to subscribe and publish
	FeatureEnv           string `yaml:"FeatureEnv,omitempty"`
	SwimlaneV2           *bool  `yaml:"SwimlaneV2,omitempty"`
	ConsumeFromWhere     *int   `yaml:"ConsumeFromWhere,omitempty"` // 0-Latest, 1-First
	ConsumeTimeoutMillis *int64 `yaml:"ConsumeTimeoutMillis,omitempty"`
	Broadcast            *bool  `yaml:"Broadcast,omitempty"`
	DisablePPE           *bool  `yaml:"DisablePPE,omitempty"`
}

type KafkaConfig struct {
	Cluster string `yaml:"Cluster"`
	Topic   string `yaml:"Topic"`
}

type ByteCloudConfig struct {
	BaseURL string `yaml:"BaseURL"`
	Token   string `yaml:"Token"`
}

type AuthConfig struct {
	// EnabledAuthOrigins is a list of allowed origins for authenticating user,
	// such as byte_cloud, codebase, cloud_ide.
	// If empty, all origins are allowed.
	// Used to control which user identities we trust.
	// E.g. we only accepts requests of users authenticated by ByteCloud JWT.
	EnabledAuthOrigins []string `yaml:"EnabledAuthOrigins"`
	// EnableSignCodebaseJWT indicates if sign codebase user JWT automatically if available.
	EnableSignCodebaseJWT bool `yaml:"EnableSignCodebaseJWT"`
	// SignCodebaseJWTScopes indicates which scopes will be used to sign codebase user JWT(E.g. "repos.contents:fetch").
	SignCodebaseJWTScopes []string `yaml:"SignCodebaseJWTScopes"`
	// SignNextCodeJWTScopes  indicates which scopes will be used to sign next codebase user JWT(E.g. "repo:read").
	SignNextCodeJWTScopes []string `yaml:"SignNextCodeJWTScopes"`
	// Whether to get codebase user details(E.g. department)
	IncludeCodebaseUserDetails bool `yaml:"IncludeCodebaseUserDetails"`

	// Whether to validate cloud ide user jwt by requesting cloud ide account platform API.
	EnableValidateCloudIDEJWT bool `yaml:"EnableValidateCloudIDEJWT"`
	// CloudIDE tenant ids.
	EnableValidateCloudIDEJWTTenantIDs []string `yaml:"EnableValidateCloudIDEJWTTenantIDs"`
	// Disabled user authentication for these APIs(E.g. kitex methods).
	DisabledAuthenticationMethods []string `yaml:"DisabledAuthenticationMethods"`
	// EnableBothAuthAndNoAuthMethods indicates whether to enable both authenticated and unauthenticated requests.
	EnableBothAuthAndNoAuthMethods []string `yaml:"EnableBothAuthAndNoAuthMethods"`
}

type UserGrowthActivityConfig struct {
	// Whether to support the UG activity
	EnableActivity bool `yaml:"EnableActivity"`
}

type LaplaceConfig struct {
	Name      string `yaml:"Name"`
	PSM       string `yaml:"PSM"`
	Cluster   string `yaml:"Cluster"`
	IDC       string `yaml:"IDC"`
	ModelName string `yaml:"ModelName"`
	AbServer  int16  `yaml:"AbServer"`
}

type KnowledgeBaseConfig struct {
	PSM string `yaml:"PSM"`
}

type FaasConfig struct {
	FunctionID string `yaml:"FunctionID"`
	Token      string `yaml:"Token"`
}

// LarkAppConfig is lark app config
type LarkAppConfig struct {
	AppID              string `yaml:"AppID"`
	OpenID             string `yaml:"OpenID"`
	AppSecret          string `yaml:"AppSecret"`
	DevGPTServiceToken string `yaml:"DevGPTServiceToken"`
	RedirectURI        string `yaml:"RedirectURI"`
	Scope              string `yaml:"Scope"`
}

// VocoderConfig is vecorder config
type VocoderConfig struct {
	BaseURL string `yaml:"BaseURL"`
	User    string `yaml:"User"`
}

type CORSConfig struct {
	AllowOrigins        map[string]bool `yaml:"allow_origins"`
	AllowedOriginsRegex []string        `yaml:"allowed_origins_regex"`
}

// ModelType .
type ModelType = string

const (
	ModelTypeAzure            ModelType = "azure"             // Azure compatible model APIs.
	ModelTypeOpenAI           ModelType = "openai"            // OpenAI compatible model APIs.
	ModelTypeSeed             ModelType = "seed"              // Seed series.
	ModelTypeMaaS             ModelType = "maas"              // MaaS V1.
	ModelTypeMaaSV3           ModelType = "maas_v3"           // MaaS V3.
	ModelTypeCoze             ModelType = "coze"              // Model provided by coze.
	ModelTypeBytedAGI         ModelType = "bytedagi"          // Model provided by bytedagi.
	ModelTypeEcho             ModelType = "echo"              // A test dumb model just replies with last msg.
	ModelTypeDeepSeekHTTP     ModelType = "deepseek_http"     // Access model using http.
	ModelTypeOpenAICompatible ModelType = "openai_compatible" // Access model via openai compatible APIs.
	ModelTypeCodeKG           ModelType = "codekg"            // CodeKG model.
	ModelTypeIES              ModelType = "ies"               // ies model.
	ModelTypeFornax           ModelType = "fornax"            // Access model by Fornax platform.
	ModelTypeLLMFlow          ModelType = "llmflow"           // Access model by LLMFlow platform.
)

type ModelAuthConfig struct {
	Type ModelType `yaml:"Type"`
	// Account available models.
	Models []string `yaml:"Models"`
	// AK.
	AccessKey string `yaml:"AccessKey"`
	// SK.
	SecretKey string `yaml:"SecretKey"`
	// Endpoint, usually for SFT/LoRA models.
	Endpoint string `yaml:"Endpoint"`
	// This account is exclusive for these APPs.
	AllowedAppIDs []string `yaml:"AllowedAppIDs"`
	// API Key
	APIKey string `yaml:"APIKey"`

	// For maas.
	Host   string `yaml:"Host"`
	Region string `yaml:"Region"`
	// The unit is second, and it currently only used for maas and DeepSeek and Fornax.
	Timeout int `yaml:"Timeout"`

	// For http
	ModelURL map[string]string `yaml:"ModelURL"`

	// Mapping the alias model name to the real model name.
	ModelNameAlias map[string]string `yaml:"ModelNameAlias"`

	// is internal model
	Internal bool `yaml:"Internal"`
}

type ModelDispatchConfig struct {
	Models []*ModelAuthConfig `yaml:"Models"`
}

type EventCollectorTokenConfig struct {
	Token string `json:"token"`
}

type ToolAgentConfig struct {
	BaseURL string `yaml:"BaseURL"`
}

type IdeCopilotConfig struct {
	BaseURL string `yaml:"BaseURL"`
}

type AgentLLMConfig struct {
	ModelName   string  `yaml:"model_name"`
	MaxTokens   int     `yaml:"max_tokens"`
	Temperature float32 `yaml:"temperature"`
	Stream      bool    `yaml:"stream"`
	JSONFormat  bool    `yaml:"json_format"`
}

type MultiAgentLLMConfig struct {
	AgentLLMConfigs map[string]*AgentLLMConfig `yaml:"agent_llm_configs"`
}

type DGitConfig struct {
	GrpcAddr    string `yaml:"GrpcAddr"`
	GrpcToken   string `yaml:"GrpcToken"`
	GrpcPSM     string `yaml:"GrpcPSM"`
	StorageName string `yaml:"StorageName"`
}

type AMapConfig struct {
	CustomHost  string `yaml:"CustomHost"`
	OverseaHost string `yaml:"OverseaHost"`
	APIHost     string `yaml:"APIHost"`
	AK          string `yaml:"AK"`
	SK          string `yaml:"SK"`
}

type StarlingConfig struct {
	ProjectID int64 `yaml:"ProjectID"`
	SpaceID   int64 `yaml:"SourceSpaceID"`
}

type ToutiaoSearchPluginConfig struct {
	BizId        string `yaml:"BizId"`
	TrafficGroup string `yaml:"TrafficGroup"`
	TrafficId    string `yaml:"TrafficId"`
	AK           string `yaml:"AK"`
}

type ToutiaoAlgoConfig struct {
	ReqKey       string `yaml:"req_key"`
	AppKey       string `yaml:"app_key"`
	AppSecret    string `yaml:"app_secret"`
	ModelVersion string `yaml:"model_version"`
}

type FlowResourceConfig struct {
	TenantID   int64  `yaml:"tenant_id"`
	TenantName string `yaml:"tenant_name"`
	SceneID    int64  `yaml:"scene_id"`
	SceneName  string `yaml:"scene_name"`
}
