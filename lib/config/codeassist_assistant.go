package config

type CodeAssistAssistantConfig struct {
	ContextConfig                ContextConfig                `yaml:"ContextConfig"`
	DBConfig                     RDSConfig                    `yaml:"DBConfig"`
	TCCConfig                    TCCConfig                    `yaml:"TCCConfig"`
	DoubaoTCCConfig              DoubaoTCCConfig              `yaml:"DoubaoTCCConfig"`
	KnowledgeBaseConfig          KnowledgeBaseConfig          `yaml:"KnowledgeBaseConfig"`
	LLMOpsConfig                 LLMOpsConfig                 `yaml:"LLMOpsConfig"`
	ContextEventBusConfig        ContextEventBusConfig        `yaml:"ContextEventBusConfig"`
	SandboxRecycleEventBusConfig SandboxRecycleEventBusConfig `yaml:"SandboxRecycleEventBusConfig"`
	DoubaoStreamClientConfig     DoubaoStreamClientConfig     `yaml:"DoubaoStreamClientConfig"`
	EventStreamClientConfig      EventStreamClientConfig      `yaml:"EventStreamClientConfig"`
	JobSchedulerConfig           JobSchedulerConfig           `yaml:"JobSchedulerConfig"`
	RedisConfig                  RedisConfig                  `yaml:"Redis"`
	MetricsEventMQConfig         RocketMQConfig               `yaml:"MetricsEventMQConfig"`
	SandboxConfig                SandboxConfig                `yaml:"SandboxConfig"`
	CodeAssistAgentConfig        CodeAssistAgentConfig        `yaml:"CodeAssistAgentConfig"`
	DKMSConfig                   DKMSConfig                   `yaml:"DKMSConfig"`
}

type DoubaoTCCConfig struct {
	PSM string `yaml:"PSM"`
}

type ContextConfig struct {
	FileFetchSizeLimit int64 `yaml:"FileFetchSizeLimit"`
}

type ContextTaskMQConfig struct {
	Enabled  bool            `yaml:"Enabled"`
	MQConfig *RocketMQConfig `yaml:"MQConfig"`
}

type ContextEventBusConfig struct {
	EventName     string `yaml:"EventName"`
	ConsumerGroup string `yaml:"ConsumerGroup"`
	ConsumerPSM   string `yaml:"ConsumerPSM"`
}

type SandboxRecycleEventBusConfig struct {
	EventName     string `yaml:"EventName"`
	ConsumerGroup string `yaml:"ConsumerGroup"`
	ConsumerPSM   string `yaml:"ConsumerPSM"`
}

type ContextAbaseConfig struct {
	PSM            string `yaml:"PSM"`
	ReadTimeoutMS  int64  `yaml:"ReadTimeoutMS"`
	DialTimeoutMS  int64  `yaml:"DialTimeoutMS"`
	WriteTimeoutMS int64  `yaml:"WriteTimeoutMS"`
}

type DoubaoStreamClientConfig struct {
	PSM              string `yaml:"PSM"`
	StreamWindowSize uint32 `yaml:"StreamWindowSize"`
}

type EventStreamClientConfig struct {
	PSM string `yaml:"PSM"`
}

type JobSchedulerConfig struct {
	PSM string `yaml:"PSM"`
}

type OrgInstallationCollection struct {
	OrgNames           []string
	OrgInstallationIDs map[string]int64 // key: orgName, val: installation id
}

type SandboxConfig struct {
	BaseURL         string `yaml:"BaseURL"`
	TransportDomain string `yaml:"TransportDomain"`
}

type CodeAssistAgentConfig struct {
	MessageAbaseConfig CodeAssistAbaseConfig `yaml:"MessageAbaseConfig"`
	ToolAbaseConfig    CodeAssistAbaseConfig `yaml:"ToolAbaseConfig"`
}

// MessageAbaseConfig is the config for Abase.
type CodeAssistAbaseConfig struct {
	PSM   string `yaml:"PSM"`
	Table string `yaml:"Table"`
}
