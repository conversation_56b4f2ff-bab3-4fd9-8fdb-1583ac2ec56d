package config

import libtcc "code.byted.org/devgpt/kiwis/lib/tcc"

type MemoryTCCConfig struct {
	RatelimitConfig      *libtcc.GenericConfig[MemoryRatelimitConfig]    `tcc:"key:ratelimit_config;format:yaml;space:default"`
	AzureEmbeddingConfig *libtcc.GenericConfig[AzureEmbeddingToken]      `tcc:"key:azure_embedding_token;format:yaml;space:default"`
	ModelAuthConfig      *libtcc.GenericConfig[ModelDispatchConfig]      `tcc:"key:model_auth_config;format:yaml;space:default"`
	CodebaseServiceJWT   *libtcc.GenericConfig[CodebaseServiceJWTConfig] `tcc:"key:codebase_service_jwt;format:string;space:default"`
	ByteCloudAuthJwt     *libtcc.GenericConfig[ByteCloudAuthJwtConfig]   `tcc:"key:byte_cloud_auth_jwt;format:string;space:default"`
	ArkBearer            *libtcc.GenericConfig[ArkBearer]                `tcc:"key:ark_bearer;format:string;space:default"`
}

type MemoryRatelimitConfig struct {
	// Embedding 失败的请求数量限制
	EmbeddingErrorRatelimitRequests int `yaml:"EmbeddingErrorRatelimitRequests"`
	// 计算的时间周期，单位秒
	RatelimitResetTime int `yaml:"RatelimitResetTime"`
}

type ByteCloudAuthJwtConfig string

type ArkBearer string
