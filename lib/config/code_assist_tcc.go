package config

import "code.byted.org/devgpt/kiwis/lib/tcc"

type CodeAssistTCCConfig struct {
	// app
	AppRegisterConfig *tcc.GenericConfig[CodeAssistAppsConfig] `tcc:"key:app_register_config;format:yaml;space:default"`
	// intention
	IntentionConfig *tcc.GenericConfig[CodeAssistIntentionConfig] `tcc:"key:intention_config;format:yaml;space:default"`
	// fornax
	FornaxConfig *tcc.GenericConfig[FornaxConfig] `tcc:"key:fornax_config;format:yaml;space:default"`
	// knowledge base config
	KnowledgeBaseDatasetConfig *tcc.GenericConfig[KnowledgeBaseDatasetConfig] `tcc:"key:knowledge_base_dataset_config;format:yaml;space:default"`
	// model
	ModelAuthConfig *tcc.GenericConfig[ModelDispatchConfig] `tcc:"key:model_auth_config;format:yaml;space:default"`
	// dgit
	DGitConfig *tcc.GenericConfig[DGitConfig] `tcc:"key:dgit_config;format:yaml;space:default"`
	// github token
	GithubConfig *tcc.GenericConfig[GithubConfig] `tcc:"key:github_config;format:yaml;space:default"`
	// desensitize tos config
	DesensitizeTOSConfig *tcc.GenericConfig[TOSConfig] `tcc:"key:desensitize_tos_config;format:yaml;space:default"`
	// context
	DisabledDirectoryConfig     *tcc.GenericConfig[DisabledDirectoryConfig]         `tcc:"key:disable_directory_config;format:yaml;space:default"`
	FileAnalysisConfig          *tcc.GenericConfig[FileAnalysisConfig]              `tcc:"key:file_analysis_config;format:yaml;space:default"`
	RepositoryRestrictionConfig *tcc.GenericConfig[RepositoryRestrictionConfig]     `tcc:"key:repository_restriction_config;format:yaml;space:default"`
	FileConcurrencyConfig       *tcc.GenericConfig[CodeAssistFileConcurrencyConfig] `tcc:"key:file_concurrency_config;format:yaml;space:default"`
	EventBusConfig              *tcc.GenericConfig[EventBusConfig]                  `tcc:"key:eventbus_config;format:yaml;space:default"`
	ContextRetryConfig          *tcc.GenericConfig[ContextRetryConfig]              `tcc:"key:context_try_config;format:yaml;space:default"`
	ResourceKeyBlackListConfig  *tcc.GenericConfig[ResourceKeyBlackListConfig]      `tcc:"key:resource_key_black_list;format:yaml;space:default"`
	// sandbox
	PythonExecuteRuleConfig      *tcc.GenericConfig[PythonExecuteRuleConfig]         `tcc:"key:python_execute_rule_config;format:yaml;space:default"`
	SandboxFaaSFunctions         *tcc.GenericConfig[FaaSFunctionsConfig]             `tcc:"key:sandbox_faas_functions;format:yaml;space:default"`
	SandboxFaaSFunctionsNew      *tcc.GenericConfig[FaaSFunctionsConfigNew]          `tcc:"key:sandbox_faas_functions_new;format:yaml;space:default"`
	SandboxAPIConfig             *tcc.GenericConfig[SandboxAPIConfig]                `tcc:"key:sandbox_api_config;format:yaml;space:default"`
	SandboxAuthConfig            *tcc.GenericConfig[SandboxAuthConfig]               `tcc:"key:sandbox_auth_config;format:yaml;space:default"`
	SandboxAllocationLimitConfig *tcc.GenericConfig[SandboxAllocationNumLimitConfig] `tcc:"key:sandbox_allocation_limit_config;format:yaml;space:default"`
	// homepage
	HomepageConfig        *tcc.GenericConfig[HomepageConfig]                  `tcc:"key:homepage_config;format:json;space:default"`
	PromptTemplatesConfig *tcc.GenericConfig[CodeassistPromptTemplatesConfig] `tcc:"key:prompt_templates;format:json;space:default"`
	// search plugin config
	ToutiaoSearchPluginConfig *tcc.GenericConfig[ToutiaoSearchPluginConfig] `tcc:"key:toutiao_search_plugin_config;format:yaml;space:default"`
	ToutiaoAlgoConfig         *tcc.GenericConfig[ToutiaoAlgoConfig]         `tcc:"key:toutiao_algo_config;format:yaml;space:default"`
	// resource
	FlowResourceConfig *tcc.GenericConfig[CodeassistFlowResourceConfig] `tcc:"key:multi_flow_resource_config;format:yaml;space:default"`
	// agent
	AgentsBuildConfig  *tcc.GenericConfig[CodeassistAgentsBuildConfig] `tcc:"key:agents_build_config;format:yaml;space:default"`
	AgentPromptConfig  *tcc.GenericConfig[CodeassistAgentPromptConfig] `tcc:"key:agent_prompt_config;format:yaml;space:default"`
	AgentToolConfig    *tcc.GenericConfig[CodeassistAgentToolConfig]   `tcc:"key:agent_tool_config;format:yaml;space:default"`
	AgentSandboxConfig *tcc.GenericConfig[AgentSandboxConfig]          `tcc:"key:agent_sandbox_config;format:yaml;space:default"`
}

type CodeAssistIntentionConfig struct {
	ID               string                           `yaml:"ID"`
	Name             string                           `yaml:"Name"`
	LLMOpsAppKey     string                           `yaml:"LLMOpsAppKey"`
	Model            CodeAssistLLMConfig              `yaml:"Model"`
	Handler          CodeAssistIntentionHandlerConfig `yaml:"Handler"`
	RateLimitWindows int                              `yaml:"RateLimitWindows"` // 限流窗口,单位s
	RateLimitQuota   int                              `yaml:"RateLimitQuota"`   // 限流窗口大小
}

type CodeAssistIntentionHandlerConfig struct {
	ID       string                            `yaml:"ID"`
	Type     string                            `yaml:"Type"`
	Pipeline CodeAssistIntentionPipelineConfig `yaml:"Pipeline"`
}

type CodeAssistIntentionPipelineConfig struct {
	Prompt CodeAssistPromptConfig `yaml:"Prompt"`
}

type CodeAssistAppsConfig struct {
	Apps []*CodeAssistAppConfig `yaml:"Apps"`
}

type CodeAssistAppConfig struct {
	ID                string                    `yaml:"ID"`
	Name              string                    `yaml:"Name"`
	Version           string                    `yaml:"Version"`
	LLMOpsAppKey      string                    `yaml:"LLMOpsAppKey"`
	Models            CodeAssistModelsConfig    `yaml:"Models"`
	Handlers          []CodeAssistHandlerConfig `yaml:"Handlers"`
	DisableSuggest    bool                      `yaml:"DisableSuggest"`
	RateLimitWindows  int                       `yaml:"RateLimitWindows"` // 限流窗口,单位s
	RateLimitQuota    int                       `yaml:"RateLimitQuota"`   // 限流窗口大小
	CKGRetrieveParams *CKGRetrieveParams        `yaml:"CKGRetrieveParams"`
}

type CKGRetrieveParams struct {
	EmbeddingTopN       int `yaml:"EmbeddingTopN"`
	EmbeddingRecallNums int `yaml:"EmbeddingRecallNums"`
	RerankRecallNums    int `yaml:"RerankRecallNums"`
}

func (c *CKGRetrieveParams) IsValid() bool {
	return c != nil && c.EmbeddingTopN > 0 && c.EmbeddingRecallNums > 0 && c.RerankRecallNums > 0
}

type CodeAssistHandlerConfig struct {
	ID       string                   `yaml:"ID"`
	Type     string                   `yaml:"Type"`
	Pipeline CodeAssistPipelineConfig `yaml:"Pipeline"`
}

type CodeAssistPipelineConfig struct {
	AnswerPrompt  CodeAssistPromptConfig  `yaml:"AnswerPrompt"`
	SuggestPrompt CodeAssistPromptConfig  `yaml:"SuggestPrompt"`
	AIFixPrompt   *CodeAssistPromptConfig `yaml:"AIFixPrompt"`
}

type CodeAssistPromptConfig struct {
	PromptKey   string `yaml:"PromptKey"`
	PromptLabel string `yaml:"PromptLabel"`
}

type CodeAssistModelsConfig struct {
	AnswerModel             CodeAssistLLMConfig `yaml:"AnswerModel"`
	DeepThinkAnswerModel    CodeAssistLLMConfig `yaml:"DeepThinkAnswerModel"`
	VLMAnswerModel          CodeAssistLLMConfig `yaml:"VLMAnswerModel"`
	DeepThinkVLMAnswerModel CodeAssistLLMConfig `yaml:"DeepThinkVLMAnswerModel"`
	SuggestModel            CodeAssistLLMConfig `yaml:"SuggestModel"`
}

type CodeAssistLLMConfig struct {
	Name        string  `yaml:"Name"`
	Temperature float32 `yaml:"Temperature"`
	// MaxTokens is the max token of model output which will be used in model request.
	// For example: in intent detect, the max token is 10.
	// in chat, the max token is 2000.
	MaxTokens int `yaml:"MaxTokens"`
	// PromptMaxTokens is the max token length for prompt which will be used to crop prompt.
	// seed_moe_stream: 4k
	// gpt-35-turbo-16k: 14k
	// gpt-4-0613: 6k
	PromptMaxTokens int      `yaml:"PromptMaxTokens"`
	TopP            float32  `yaml:"TopP"`
	TopK            int      `yaml:"TopK"`
	EnabledModels   []string `yaml:"EnabledModels"`
	// chat模型流式输出停止标志字段
	Stop []string `yaml:"Stop"`
	// 排序模型阈值字段
	Threshold *float32 `yaml:"Threshold"`
	// FallbackModels is the model names which will be retried if the first model is failed(currently only rate limit error will be retried).
	// You must ensure the parameters(such as max_tokens, prompt_max_tokens) are compatible for all fallback models,
	// or the retrying will be failed or not work as expected.
	// And the prompts will be constructed base on the main model not the fallback models.
	// Use after reviewed and tested carefully.
	FallbackModels []string `yaml:"FallbackModels"`
	// llmflow 模型 model arch & desc name
	ModelArch     string `yaml:"ModelArch"`
	ModelDescName string `yaml:"ModelDescName"`
	// llmflow 模型ab参数 key值
	GptAbParamsKey string `yaml:"GptAbParamsKey"`
	// HistoryMessagesLimit the max number of history
	HistoryMessagesLimit int `yaml:"HistoryMessagesLimit"`
	// CloseDynamicToken DynamicToken降级开关
	CloseDynamicToken bool `yaml:"CloseDynamicToken"`
}

type DisabledDirectoryConfig struct {
	// 前端渲染disable配置
	FileWhiteList       []string `yaml:"FileWhiteList"`
	FileSuffixWhiteList []string `yaml:"FileSuffixWhiteList"`
	DirectoryBlockList  []string `yaml:"DirectoryBlockList"`
	// 文件CKG disable配置
	CKGFileMaxSize int `yaml:"CKGFileMaxSize"`
	// 代码类型文件CKG disable配置
	CKGFileMaxLineNum  int `yaml:"CKGFileMaxLineNum"`
	CKGFileMaxLineSize int `yaml:"CKGFileMaxLineSize"`
}

type FileAnalysisConfig struct {
	SupportLanguageByExtension map[string]string `yaml:"SupportLanguageByExtension"`
}

type KnowledgeBaseDatasetConfig struct {
	FileDatasetID       int64 `yaml:"FileDatasetID"`
	DirectoryDatasetID  int64 `yaml:"DirectoryDatasetID"`
	RepositoryDatasetID int64 `yaml:"RepositoryDatasetID"`
}

type RepositoryRestrictionConfig struct {
	SizeLimitKB int `yaml:"SizeLimitKB"`
}

type FornaxConfig struct {
	AccessKey string `yaml:"AccessKey"`
	SecretKey string `yaml:"SecretKey"`
	Timeout   int    `yaml:"Timeout"`
}

type EventBusConfig struct {
	Timeout int `yaml:"Timeout"`
}

type PythonExecuteRuleConfig struct {
	WhitelistedPackages             []string            `yaml:"WhitelistedPackages"`
	ProhibitedNormalOperations      map[string][]string `yaml:"ProhibitedNormalOperations"`
	ProhibitedInteractionOperations map[string][]string `yaml:"ProhibitedInteractionOperations"`
	ProhibitedSecurityOperations    map[string][]string `yaml:"ProhibitedSecurityOperations"`
	PipAliasMap                     map[string]string   `yaml:"PipAliasMap"`
}

type CodeAssistFileConcurrencyConfig struct {
	FirstLevelUpdateFilesConcurrency       int `yaml:"FirstLevelUpdateFilesConcurrency"`       // 目录的第一层文件调用知识库UpdateFiles接口 并发控制
	UpdateTreeConcurrency                  int `yaml:"UpdateTreeConcurrency"`                  // 上传目录调用知识库UpdateFiles接口 并发控制
	UpdateFilesConcurrency                 int `yaml:"UpdateFilesConcurrency"`                 // 调用知识库UpdateFiles接口 并发控制
	DeleteFilesConcurrency                 int `yaml:"DeleteFilesConcurrency"`                 // 知识库DeleteFiles接口 并发控制
	DGitDownloadFilesConcurrency           int `yaml:"DGitDownloadFilesConcurrency"`           // dgit下载文件内容 并发控制
	DoubaoDownloadFilesConcurrency         int `yaml:"DoubaoDownloadFilesConcurrency"`         // doubao下载文件内容 并发控制
	DoubaoDownloadTreeConcurrency          int `yaml:"DoubaoDownloadTreeConcurrency"`          // doubao下载目录内容 并发控制
	DesensitizeConcurrency                 int `yaml:"DesensitizeConcurrency"`                 // 脱敏并发度
	UploadFilesToAgentWorkspaceConcurrency int `yaml:"UploadFilesToAgentWorkspaceConcurrency"` // 上传文件到agent workspace中的并发度
}

func (c *CodeAssistFileConcurrencyConfig) getConcurrency(tccValue int, maxConcurrency int) int {
	var finalConcurrency int

	if tccValue >= maxConcurrency {
		finalConcurrency = maxConcurrency
	} else {
		finalConcurrency = tccValue
	}

	if finalConcurrency < 1 { // 保障并发最小为1
		finalConcurrency = 1
	}
	return finalConcurrency
}

func (c *CodeAssistFileConcurrencyConfig) GetFirstLevelUpdateFilesConcurrency() int {
	return c.getConcurrency(c.FirstLevelUpdateFilesConcurrency, 50)
}

func (c *CodeAssistFileConcurrencyConfig) GetUpdateTreeConcurrency() int {
	return c.getConcurrency(c.UpdateTreeConcurrency, 50)
}

func (c *CodeAssistFileConcurrencyConfig) GetUpdateFilesConcurrency() int {
	return c.getConcurrency(c.UpdateFilesConcurrency, 50)
}

func (c *CodeAssistFileConcurrencyConfig) GetDeleteFilesConcurrency() int {
	return c.getConcurrency(c.DeleteFilesConcurrency, 50)
}

func (c *CodeAssistFileConcurrencyConfig) GetDGitDownloadFilesConcurrency() int {
	return c.getConcurrency(c.DGitDownloadFilesConcurrency, 50)
}

func (c *CodeAssistFileConcurrencyConfig) GetDoubaoDownloadFilesConcurrency() int {
	return c.getConcurrency(c.DoubaoDownloadFilesConcurrency, 50)
}

func (c *CodeAssistFileConcurrencyConfig) GetDoubaoDownloadTreeConcurrency() int {
	return c.getConcurrency(c.DoubaoDownloadTreeConcurrency, 50)
}

func (c *CodeAssistFileConcurrencyConfig) GetDesensitizeConcurrency() int {
	return c.getConcurrency(c.DesensitizeConcurrency, 30)
}

func (c *CodeAssistFileConcurrencyConfig) GetUploadFilesToAgentWorkspaceConcurrency() int {
	return c.getConcurrency(c.UploadFilesToAgentWorkspaceConcurrency, 50)
}

type ContextRetryConfig struct {
	FileUpdateFilesRetryConfig       CodeAssistRetryConfig `yaml:"FileUpdateFilesRetryConfig"`
	DirectoryUpdateFilesRetryConfig  CodeAssistRetryConfig `yaml:"DirectoryUpdateFilesRetryConfig"`
	RepositoryUpdateFilesRetryConfig CodeAssistRetryConfig `yaml:"RepositoryUpdateFilesRetryConfig"`
	UpdateFoldersRetryConfig         CodeAssistRetryConfig `yaml:"UpdateFoldersRetryConfig"`

	DeleteFilesRetryConfig    CodeAssistRetryConfig `yaml:"DeleteFilesRetryConfig"`
	GetFileContentRetryConfig CodeAssistRetryConfig `yaml:"GetFileContentRetryConfig"`
	ImageXDownloadRetryConfig CodeAssistRetryConfig `yaml:"ImageXDownloadRetryConfig"` // 有主备，双倍重试
}

type ResourceKeyBlackListConfig struct {
	DirectoryBlackList  []string `yaml:"DirectoryBlackList"`
	RepositoryBlackList []string `yaml:"RepositoryBlackList"`
}

type CodeAssistRetryConfig struct {
	MaxRetries int   `yaml:"MaxRetries"`
	IntervalMS int64 `yaml:"IntervalMS"`
}

type SandboxAuthConfig struct {
	// 旧沙盒配置
	TenantID                   string `yaml:"tenant_id"`
	SandboxJwtToken            string `yaml:"sandbox_jwt_token"`
	SandboxEffectivePercentage int    `yaml:"sandbox_effective_percentage"`
	// 新沙盒配置
	CommonSandboxAuthKey string `yaml:"common_sandbox_auth_key"`
	// VeFaaS配置
	VeFaaSAccessKeyID     string `yaml:"vefaas_access_key_id"`
	VeFaaSSecretAccessKey string `yaml:"vefaas_secret_access_key"`
}

type FaaSFunctionsConfig struct {
	FaaSFunctions []FaaSFunction `yaml:"FaaSFunctions"`
}

type FaaSFunctionsConfigNew struct {
	FaaSFunctions []FaaSFunction `yaml:"FaaSFunctions"`
}

type SandboxAllocationNumLimitConfig struct {
	// 沙盒分配数量限制
	SandboxAllocationNumLimit map[int]AllocationNumLimitItem `yaml:"sandbox_allocation_num_limit"`
}

type AllocationNumLimitItem struct {
	FunctionName                      string `yaml:"function_name"`
	SingleInstanceMaxSessionNum       int    `yaml:"single_instance_max_session_num"`
	SingleUserMaxShareInstanceNum     int    `yaml:"single_user_max_share_instance_num"`
	SingleUserMaxExclusiveInstanceNum int    `yaml:"single_user_max_exclusive_instance_num"`
}

type FaaSFunction struct {
	Name             string   `yaml:"Name"`
	FunctionType     int      `yaml:"FunctionType"`
	SupportLanguages []string `yaml:"SupportLanguages"`
	FunctionIDs      []string `yaml:"FunctionIDs"`
}

type SandboxAPIConfig struct {
	// 旧沙盒配置
	SandboxEffectivePercentage        int `yaml:"sandbox_effective_percentage"`
	SandboxManagerEffectivePercentage int `yaml:"sandbox_manager_effective_percentage"`

	// 新沙盒配置
	CommonEffectivePercentage  int    `yaml:"common_effective_percentage"`
	CommonTotalInstancesNumber int    `yaml:"common_total_instances_number"`
	CommonSandboxDomainName    string `yaml:"common_sandbox_domain_name"`

	// VeFaaS 配置
	VeFaaSDomainName               string `yaml:"vefaas_domain_name"`
	VeFaaSRegion                   string `yaml:"vefaas_region"`
	VeFaaSService                  string `yaml:"vefaas_service"`
	ListFunctionInstancesVersion   string `yaml:"list_function_instances_version"`
	GetReleaseStatusVersion        string `yaml:"get_release_status_version"`
	MigrateFunctionInstanceVersion string `yaml:"migrate_function_instance_version"`
}
type HomepageConfig struct {
	Recommendations            Recommendations            `json:"recommendations"`
	CodebaseTemplates          CodebaseTemplates          `json:"codebase_templates"`
	ArtifactsTemplates         ArtifactsTemplates         `json:"artifacts_templates"`
	ArtifactsCategoryTemplates ArtifactsCategoryTemplates `json:"artifacts_category_templates"`
}

type Recommendations struct {
	RecommendationItems []RecommendationItem `json:"recommendation_items"`
}

type RecommendationItem struct {
	ItemType int    `json:"item_type"`
	Key      string `json:"key"`
	Name     string `json:"name"`
	IconType string `json:"icon_type"`
}

type CodebaseTemplates struct {
	CodebaseItems []CodebaseItem `json:"codebase_items"`
}

type CodebaseItem struct {
	ItemType            int    `json:"item_type"`
	Query               string `json:"query"`
	IconURL             string `json:"icon_url"`
	UseCache            bool   `json:"use_cache"`
	Answer              string `json:"answer"`
	CodebaseURL         string `json:"codebase_url"`
	CodebaseName        string `json:"codebase_name"`
	CodebaseID          string `json:"codebase_id"`
	CodebaseBranchName  string `json:"codebase_branch_name"`
	CodebaseDescription string `json:"codebase_descirption"` // don't change the current situation online.
}

type ArtifactsCategoryTemplates struct {
	ArtifactsCategories []ArtifactCategory `json:"artifacts_categories"`
}

type ArtifactCategory struct {
	CategoryID    int            `json:"category_id"`
	CategoryName  string         `json:"category_name"`
	ArtifactItems []ArtifactItem `json:"artifact_items"`
}

type ArtifactsTemplates struct {
	ArtifactsItems []ArtifactItem `json:"artifacts_items"`
}

type ArtifactItem struct {
	ArtifactsImageURL     string `json:"artifacts_image_url"`
	ArtifactsImageTitle   string `json:"artifacts_image_title"`
	ArtifactsID           string `json:"artifacts_id"`
	HomePageItemType      int    `json:"home_page_item_type"`
	UseCache              bool   `json:"use_cache"`
	Prompt                string `json:"prompt"`
	AdvertisingArticleUrl string `json:"advertising_article_url"`
}

type CodeassistPromptTemplatesConfig struct {
	ZH []*CodeassistPromptTemplate `json:"zh"`
	EN []*CodeassistPromptTemplate `json:"en"`
}

type CodeassistPromptTemplate struct {
	Template     string            `json:"template"`
	Key          string            `json:"key"`
	Name         string            `json:"name"`
	IconType     string            `json:"icon_type"`
	VariableList []*PromptVariable `json:"variable_list"`
}

type PromptVariable struct {
	Key          string            `json:"key"`
	Placeholder  string            `json:"placeholder"`
	VariableType string            `json:"variable_type"`
	Options      []*VariableOption `json:"options"`
}

type VariableOption struct {
	Label string `json:"label"`
	Value string `json:"value"`
}

type CodeassistFlowResourceConfig struct {
	ImageFlowResourceConfig *FlowResourceConfig `yaml:"ImageFlowResourceConfig"`
	FileFlowResourceConfig  *FlowResourceConfig `yaml:"FileFlowResourceConfig"`
}

type CodeassistAgentsBuildConfig struct {
	Agents []*CodeassistAgentBuildConfig `yaml:"Agents"`
}

type CodeassistAgentBuildConfig struct {
	ID             string                   `yaml:"ID"`
	Name           string                   `yaml:"Name"`
	Version        string                   `yaml:"Version"`
	ChunkBatchSize int                      `yaml:"ChunkBatchSize"`
	Actors         []*CodeassistActorConfig `yaml:"Actors"`
}

type CodeassistActorConfig struct {
	Name         string                    `yaml:"Name"`
	TrimConfig   CodeassistTrimConfig      `yaml:"TrimConfig"`
	PromptKey    CodeassistPromptKeyConfig `yaml:"PromptKeyConfig"`
	ToolNameList []string                  `yaml:"ToolNameList"`
	Model        CodeAssistLLMConfig       `yaml:"Model"`
}

type CodeassistTrimConfig struct {
	MaxTokens              int    `yaml:"MaxTokens"`
	MaxHistoryTokens       int    `yaml:"MaxHistoryTokens"`
	MaxAgentTrajTokens     int    `yaml:"MaxAgentTrajTokens"`
	MaxWorkspaceFilesCount int    `yaml:"MaxWorkspaceFilesCount"`
	MaxToolCallLength      int    `yaml:"MaxToolCallLength"`
	ToolCallTrimMsg        string `yaml:"ToolCallTrimMsg"`
	AgentTrajTrimMsg       string `yaml:"AgentTrajTrimMsg"`
	HistoryTrimMsg         string `yaml:"HistoryTrimMsg"`
}

type CodeassistPromptKeyConfig struct {
	SystemPromptKeys  []string `yaml:"SystemPromptKeys"`
	ContextPromptKeys []string `yaml:"ContextPromptKeys"`
	UserPromptKeys    []string `yaml:"UserPromptKeys"`
}

type CodeassistAgentPromptConfig struct {
	AgentPromptMap map[string]string `yaml:"AgentPromptMap"`
}

type CodeassistAgentToolConfig struct {
	AgentToolMap map[string]string `yaml:"AgentToolMap"`
}

type AgentSandboxConfig struct {
	DataAnalysis *AgentSandboxAPIConfig `yaml:"data_analysis"`
}

type AgentSandboxAPIConfig struct {
	NumLimit  int   `yaml:"sandbox_num_limit"`
	AliveTime int64 `yaml:"sandbox_alive_time"`
}
