package defaultcodeaiservice

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"math"
	"math/rand"
	"net/http"
	"path/filepath"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logid"
	"code.byted.org/gopkg/logs/v2/log"
	redis2 "code.byted.org/kv/redis-v6"
	"code.byted.org/middleware/hertz/byted/consts"
	"github.com/go-enry/go-enry/v2"
	"github.com/gobwas/glob"
	"github.com/google/uuid"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"gopkg.in/yaml.v3"
	"gorm.io/gorm"

	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/llmstack"
	"code.byted.org/devgpt/kiwis/apps/codeai/dal"
	"code.byted.org/devgpt/kiwis/apps/codeai/entity"
	"code.byted.org/devgpt/kiwis/apps/codeai/service"
	chatservice "code.byted.org/devgpt/kiwis/bitscopilot/chat/service"
	codecopilotentity "code.byted.org/devgpt/kiwis/bitscopilot/codecopilot/entity"
	codecopilotservice "code.byted.org/devgpt/kiwis/bitscopilot/codecopilot/service"
	authentity "code.byted.org/devgpt/kiwis/copilotstack/common/auth/entity"
	skillentity "code.byted.org/devgpt/kiwis/copilotstack/entity/skill"
	"code.byted.org/devgpt/kiwis/lib/apierror"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/hertz"
	"code.byted.org/devgpt/kiwis/lib/mapstructure"
	"code.byted.org/devgpt/kiwis/lib/tcc"
	"code.byted.org/devgpt/kiwis/port/codebase"
	"code.byted.org/devgpt/kiwis/port/llmreviewer"
	redis "code.byted.org/devgpt/kiwis/port/redis"
	"code.byted.org/devgpt/kiwis/search/tools"
)

var _ service.CodebaseAgent = &CodebaseAgent{}

const (
	DefaultAIFileReviewRequestKey     = "codebase-ai-file-review"
	DefaultAIFileReviewInstanceQuota  = 15
	DefaultSimilarCommentsThreshold   = 0.9
	FixSimilarCommentsThreshold       = 0.82
	DefaultMRWalkThroughInstanceQuota = 15
	DefaultCRCopilotFunctionID        = "review_cr_copilot_file"
	DefaultAIReplyFunctionID          = "reply_comment"
	DefaultAIFileReviewFunctionID     = "review_code_batch_file"
	DefaultMRWalkThroughFeature       = "mr_walkthrough"
	DefaultAppName                    = "code_ai"
	DefaultFileExclusionReason        = "source_code_exclusion"

	DefaultProcessAIReviewChatIntervalMilli    = 5000
	DefaultProcessPublishIntervalMilli         = 5000
	DefaultProcessGenCommentsIntervalMilli     = 5000
	DefaultProcessTimeoutIntervalMilli         = 5000
	DefaultProcessAIReviewerIntervalMilli      = 5000
	DefaultProcessWalkThroughChatIntervalMilli = 5000

	// to avoid overwhelming
	DefaultFileCntLimitPerRequest           = 100
	DefaultLineCntLimitPerRequestPerRequest = 5000
	DefaultTaskCountLimitPerRequest         = 500

	// single file
	DefaultMaxLineAdded = 3000

	// time limit for ready -> responded
	DefaultNotStartedTimeoutDurationMin = 8
	// time limit for responded -> drafted/published etc.
	DefaultNotFinishedTimeoutDurationMin = 20

	DefaultMaxQueryPage = 80
	DefaultPerPageLimit = 20
	DefaultMaxEndColumn = 512

	// DefaultReviewLocalIntervalMilli is max duration to control the interval of generating comments.
	DefaultReviewLocalIntervalMilli = 12000
	// DefaultReviewOverallIntervalMilli is min duration.
	DefaultReviewOverallIntervalMilli = 400

	FilterPatternOutputNo  = "no"
	GoodPatternOutputLGTM  = "lgtm"
	FilterFunctionID       = "filter_review_code"
	MergeReviewsFunctionID = "merge_code_reviews"
	BitsAIUserName         = "BitsAI"
	EmbeddingModel         = "Doubao-Embedding"

	// AIReviewPublishTTL indicate the task key with session in cache and should not be processed twice, this value can
	// be very large
	AIReviewPublishTTL        = DefaultNotFinishedTimeoutDurationMin * 2 * time.Minute
	AIReviewTaskTTL           = 5 * time.Minute
	AutoTriggeredDurPermitTTL = 24 * time.Hour

	CusLogPrefix        = "[Customized_Recall]"
	CusFunctionIDPrefix = "customized_"

	FreeStylePrefix           = "[FreeStyle_Recall]"
	FreeStyleFunctionIDPrefix = "freestyle_"

	AIFixFunctionID = "fix_code"
	AIFixAppID      = "8634d2bf-12ae-470e-ac1b-882014c9ebf0"

	// MaxPastCommentsLen indicate the limited count of inputs that aligns with downstream LLM embedding call
	MaxPastCommentsLen = 256

	NoReviewComment      = "AI 未发现异常 (结论仅供参考)."
	NoReviewFiltered     = "AI 未参与评审，详见[评审范围](" + FilterRuleURL + ")"
	NoReviewNotSupported = "通用链路暂未支持该语言 - AI 未参与评审."

	// FilterRuleURL indicates the url to explain filter rules
	FilterRuleURL = "https://bytedance.sg.larkoffice.com/docx/QJYbdefo8omHd7xhO0El4SOkgvf"
)

type CodebaseAgent struct {
	DAO                           dal.AsyncTaskDAO
	CodebaseCli                   codebase.Client
	RedisClient                   redis.Client
	ChatService                   chatservice.ChatService
	CodeCopilotService            codecopilotservice.CodeCopilotService
	LLMReviewerClient             llmreviewer.Client
	ReviewsSettings               service.ReviewsSettings
	BitsAISecret                  string
	BitsAIAppID                   *tcc.GenericConfig[config.BitsAIAppConfig]
	FeatureConfig                 *tcc.GenericConfig[config.CodeCopilotFeatureConfig]
	SelectiveFunctionAccessConfig *tcc.GenericConfig[config.SelectiveFunctionAccessConfig]
	CodeAIConfig                  *tcc.GenericConfig[config.BitsCodeAIConfig]
	StrictSensitiveWords          *tcc.GenericConfig[config.StrictSensitiveWords]
	FreeStyleRulesConfig          *tcc.GenericConfig[config.CodeAIReviewFreeStyleRules]
	CodeAIBPMSecret               string
}

func NewCodebaseAgent(DAO dal.AsyncTaskDAO, CodebaseCli codebase.Client, RedisClient redis.Client,
	ChatService chatservice.ChatService, CodeCopilotService codecopilotservice.CodeCopilotService,
	llmReviewerClient llmreviewer.Client, ReviewsSettings service.ReviewsSettings, BitsAISecret *tcc.GenericConfig[config.CodebaseBitsAIJWTConfig],
	BitsAIAppID *tcc.GenericConfig[config.BitsAIAppConfig],
	FeatureConfig *tcc.GenericConfig[config.CodeCopilotFeatureConfig],
	SelectiveFunctionAccessConfig *tcc.GenericConfig[config.SelectiveFunctionAccessConfig],
	CodeAIConfig *tcc.GenericConfig[config.BitsCodeAIConfig],
	StrictSensitiveWords *tcc.GenericConfig[config.StrictSensitiveWords],
	FreeStyleRulesConfig *tcc.GenericConfig[config.CodeAIReviewFreeStyleRules]) (*CodebaseAgent, error) {
	s := &CodebaseAgent{
		DAO:                           DAO,
		CodebaseCli:                   CodebaseCli,
		RedisClient:                   RedisClient,
		ChatService:                   ChatService,
		CodeCopilotService:            CodeCopilotService,
		LLMReviewerClient:             llmReviewerClient,
		ReviewsSettings:               ReviewsSettings,
		BitsAISecret:                  string(lo.FromPtr(BitsAISecret.GetPointer())),
		BitsAIAppID:                   BitsAIAppID,
		FeatureConfig:                 FeatureConfig,
		SelectiveFunctionAccessConfig: SelectiveFunctionAccessConfig,
		CodeAIConfig:                  CodeAIConfig,
		StrictSensitiveWords:          StrictSensitiveWords,
		FreeStyleRulesConfig:          FreeStyleRulesConfig,
	}

	// make sure these assignments are done before cron job created
	if s.CodeAIConfig.GetPointer() == nil || s.CodeAIConfig.GetValue().AppName == "" {
		// simply use app_name to check if read failed or not
		codeAIConfig := config.BitsCodeAIConfig{
			AppName: DefaultAppName,
			GlobalENV: config.GlobalEnvConfig{
				MaxQueryPage:                        DefaultMaxQueryPage,
				PerPageLimit:                        DefaultPerPageLimit,
				CRCopilotFunctionID:                 DefaultCRCopilotFunctionID,
				AIFileReviewFunctionID:              DefaultAIFileReviewFunctionID,
				MRWalkThroughFeature:                DefaultMRWalkThroughFeature,
				ProcessAIReviewChatIntervalMilli:    DefaultProcessAIReviewChatIntervalMilli,
				ProcessPublishIntervalMilli:         DefaultProcessPublishIntervalMilli,
				ProcessGenCommentsIntervalMilli:     DefaultProcessGenCommentsIntervalMilli,
				ProcessTimeoutIntervalMilli:         DefaultProcessTimeoutIntervalMilli,
				ProcessWalkThroughChatInternalMilli: DefaultProcessWalkThroughChatIntervalMilli,
				ProcessAIReviewerIntervalMilli:      DefaultProcessAIReviewerIntervalMilli,
				MaxEndColumn:                        DefaultMaxEndColumn,
				FileCountLimit:                      DefaultFileCntLimitPerRequest,
				LineCountLimit:                      DefaultLineCntLimitPerRequestPerRequest,
				TaskCountLimit:                      DefaultTaskCountLimitPerRequest,
				AIFileReviewInstanceQuota:           DefaultAIFileReviewInstanceQuota,
				MRWalkThroughInstanceQuota:          DefaultMRWalkThroughInstanceQuota,
				EnableSimilarCommentsDetect:         false,
				AIFileReviewLocalIntervalMilli:      DefaultReviewLocalIntervalMilli,
				AIFileReviewOverallIntervalMilli:    DefaultReviewOverallIntervalMilli,
				NotStartedTimeoutDurationMin:        DefaultNotStartedTimeoutDurationMin,
				NotFinishedTimeoutDurationMin:       DefaultNotFinishedTimeoutDurationMin,
			},
			CodeReview: config.CodeReviewConfig{
				Exclusions: []config.ExclusionsConfig{
					{
						StringID: "default_exclusions",
						Patterns: codecopilotentity.SkippedFiles,
					},
				},
				SimilarCommentsThreshold: DefaultSimilarCommentsThreshold,
			},
		}
		s.CodeAIConfig, _ = tcc.NewConfig[config.BitsCodeAIConfig](codeAIConfig, "", "", "", tcc.ConfigFormatYAML)
	}

	// startAIReviewComments generate ai review comments upon llm response.
	// status responded -> drafted
	if err := s.startAIReviewComments(); err != nil {
		return nil, errors.WithMessage(err, "failed to start cron job to create comments")
	}

	// startAIReviewLLMChat send LLM chat in sequential to avoid llm service overload.
	// status ready -> thinking -> responded
	if err := s.startAIReviewLLMChat(); err != nil {
		return nil, errors.WithMessage(err, "failed to start cron job to send LLM chats")
	}

	// startAIReviewPublish publish generated draft comments.
	if err := s.startAIReviewPublish(); err != nil {
		return nil, errors.WithMessage(err, "failed to start cron job to publish drafted comments")
	}

	if err := s.startMRWalkThroughLLMChat(); err != nil {
		return nil, errors.WithMessage(err, "failed to start cron job for MR walkthrough llm chats")
	}

	if err := s.cronjobBatchClearTimeoutTask(); err != nil {
		return nil, errors.WithMessage(err, "failed to start timeout chat task cron job")
	}

	if err := s.cronjobAIReviewer(); err != nil {
		return nil, errors.WithMessage(err, "failed to start cron job to handle ai reviewer")
	}

	return s, nil
}

var (
	// ErrParseLLMResponseFailed indicate an incorrect format of llm response.
	ErrParseLLMResponseFailed = errors.New("service Error: parse LLM response failed, please try again, or contact codebase assistant/oncall")
	// ErrChatLLMFailed indicates failed chat with llm.
	ErrChatLLMFailed = errors.New("service Error: Chat with LLM failed, please try again")
	// ErrLLMReturnEmpty indicate a successful chat but empty llm response.
	ErrLLMReturnEmpty = errors.New("service Error: LLM returns empty string, please try again")
	// ErrInvalidParameters indicate wrong params provided, should check with client-end.
	ErrInvalidParameters = errors.New("task has invalid params, please contact codebase assistant/oncall")
	// ErrInternalError indicate a defect in server, should check with server-end.
	ErrInternalError = errors.New("internal error, please contact codebase assistant/oncall")
	// ErrCreateComment indicate failure to generate a review comment
	ErrCreateComment = errors.New("internal error of creating review comment, please contact codebase assistant/oncall")
	// ErrFeatureForbidden indicate a request is beyond beta release
	ErrFeatureForbidden = errors.New("forbid feature for repo")
	// ErrTaskKeyNotFound indicate a request for a task key is not exist
	ErrTaskKeyNotFound = errors.New("task key is not exist")
	// ErrAppIDNotFound indicate retrieving an app id failed.
	ErrAppIDNotFound = errors.New("app id not found")
	// ErrCheckCommentSimilarityBlocked indicate there are similar comments published in this change or drafted.
	ErrCheckCommentSimilarityBlocked = errors.New("similar comment(s) existed")
	// ErrEmptyMR indicate an unnecessary request
	ErrEmptyMR = errors.New("empty mr")
	// ErrOutdatedMetadata indicate metadata in db is outdated
	ErrOutdatedMetadata = errors.New("outdated metadata")
	// ErrRepeatAutoReview indicate repeated requests which should be blocked
	ErrRepeatAutoReview = errors.New("repeat patchset review")

	// NoErrUserCancel indicate a user active cancel
	NoErrUserCancel = "user cancelled."

	NoErrFilteredFile     = "file is filtered according to valid_code_rules"
	NoErrReviewComment    = "Good case! No review comment from AI."
	NoErrDupTaskShortTime = "Same code segment has been triggerred to be reviewed in a short time."
)

func GetUniqueTaskKey(source, target string) string {
	return source + target
}

func (c *CodebaseAgent) GetTaskInfo(ctx context.Context, taskID uint64) (*entity.AsyncTask, error) {
	// get gen comment task may be extended to generally use scenarios?
	po, err := c.DAO.GetAsyncTask(ctx, taskID)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get agent task")
	}
	return po, nil
}

type commentToThreadOptions struct {
	RepoName string `json:"repo_name" mapstructure:"repo_name"`
	ChangeID int64  `json:"change_id" mapstructure:"change_id"`
	ThreadID int64  `json:"thread_id" mapstructure:"thread_id"`
}

func (c *CodebaseAgent) CreateAIReplyTask(ctx context.Context, params entity.Parameters, commentUser string) (*entity.AsyncTask, error) {
	appIDConfig := c.BitsAIAppID.GetPointer()
	if appIDConfig == nil {
		return nil, ErrAppIDNotFound
	}
	appConfig, exist := c.BitsAIAppID.GetValue()[c.CodeAIConfig.GetValue().AppName]
	if !exist {
		return nil, ErrAppIDNotFound
	}
	// 1. extract a valid comment
	po, err := c.DAO.CreateAsyncTask(ctx, dal.CreateAsyncTaskOption{
		// Create task with init status: llm processing/think
		TaskType: entity.TaskTypeCodebaseAIReply,
		Params:   &params,
		User:     commentUser,
		AppID:    appConfig.AppID,
		Status:   entity.StatusCodebaseAIReplyReady.String(),
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to create ai reply task")
	}

	// 2. start LLM chat
	intentContext := skillentity.Context{
		Variables: params.Params,
	}
	llmResponse, err := c.ChatService.Invoke(ctx, chatservice.InvokeOption{
		AppID:      appConfig.AppID,
		FunctionID: DefaultAIReplyFunctionID,
		Parameters: params.Params,
		Context:    &intentContext,
		Stream:     false,
		Account: &authentity.Account{
			Username:          "BitsAI",
			CodebaseBitsAIJWT: c.BitsAISecret,
		},
	})
	if err != nil {
		log.V1.CtxError(ctx, "invoke service failed: %v", err)
		c.failAIReplyTask(ctx, po.ID, lo.ToPtr(ErrChatLLMFailed.Error()))
		return nil, err
	}

	// 3. store llm response and update params
	result, exist := llmResponse.Outputs["result"]
	if !exist {
		log.V1.CtxError(ctx, "failed to get result key of llm response outputs")
		c.failAIReplyTask(ctx, po.ID, lo.ToPtr(ErrParseLLMResponseFailed.Error()))
		return nil, err
	}
	_, err = c.UpdateStatusUponLLMRespond(ctx, service.UpdateTaskOption{
		TaskID:      po.ID,
		LLMResponse: lo.ToPtr(result.(string)),
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to update status upon LLM respond")
		// do not break the function path.
	}

	// 4. Reply the comment
	opt, err := getOptionFromVars[commentToThreadOptions](params.Params)
	if err != nil {
		log.V1.CtxWarn(ctx, "failed to get options from comment params, err = %v", err)
		return nil, err
	}
	_, err = c.CodebaseCli.CreateComment(ctx, opt.RepoName, opt.ChangeID, codebase.CreateCommentsOption{
		Content:  result.(string),
		ThreadID: lo.ToPtr(opt.ThreadID),
	}, codebase.WithBitsAIServiceJWT(c.BitsAISecret))
	if err != nil {
		log.V1.CtxWarn(ctx, "failed to create comment of ai reply, err = %v", err)
	}

	return po, nil
}

// llmResponseReviews for merge process
type llmResponseReviewsForMerge struct {
	Reviews []ReviewForMerge `json:"reviews" mapstructure:"reviews"`
}

// llmResponseReviews
type llmResponseReviews struct {
	Reviews       []Review `json:"reviews,omitempty"`
	MergedReviews []Review `json:"merged_reviews,omitempty"`
}

// Line returned by LLM can either be string or int.
type Line struct {
	IntVal    int
	StringVal string
	IsString  bool
}

// UnmarshalJSON should cover raw data either is string or int.
func (l *Line) UnmarshalJSON(data []byte) error {
	if err := json.Unmarshal(data, &l.IntVal); err == nil {
		l.IsString = false
		return nil
	}

	if err := json.Unmarshal(data, &l.StringVal); err == nil {
		l.IsString = true
		return nil
	}

	return fmt.Errorf("unable to unmarshal line")
}

func (l Line) MarshalJSON() ([]byte, error) {
	// only save line as int
	if l.IsString {
		intVal, err := strconv.Atoi(l.StringVal)
		if err != nil {
			return json.Marshal(l.StringVal)
		}
		return json.Marshal(intVal)
	}
	return json.Marshal(l.IntVal)
}

func (l *Line) getValue() int32 {
	if l.IsString {
		lineInt16, err := strconv.ParseUint(l.StringVal, 10, 16)
		if err != nil {
			return -1
		}
		return int32(lineInt16)
	}
	return int32(l.IntVal)
}

type ReviewForMerge struct {
	Version     string `json:"version" mapstructure:"version"`
	Line        string `json:"line" mapstructure:"line"`
	Issue       string `json:"issue" mapstructure:"issue"`
	IssueType   string `json:"issue_type" mapstructure:"issue_type"`
	Severity    string `json:"severity" mapstructure:"severity"`
	Suggestions string `json:"suggestions" mapstructure:"suggestions"`
	Label       string `json:"label" mapstructure:"label"`
}

type Review struct {
	Version        string  `json:"version"`
	Line           Line    `json:"line"`
	StartLine      *Line   `json:"start_line,omitempty"`
	EndLine        *Line   `json:"end_line,omitempty"`
	Issue          string  `json:"issue"`
	IssueType      string  `json:"issue_type"`
	Severity       string  `json:"severity"`
	Explain        string  `json:"explain"`
	Suggestions    string  `json:"suggestions"`
	ChatPromptID   *int64  `json:"chat_prompt_id,omitempty"`
	FilterPromptID *int64  `json:"filter_prompt_id,omitempty"`
	Label          *string `json:"label,omitempty"`
	IsFiltered     *bool   `json:"is_filtered,omitempty"`
	IsTypeBlocked  *bool   `json:"is_type_blocked,omitempty"`
	IsSensBlocked  *bool   `json:"is_sens_blocked,omitempty"`
}

func setKVForMergeReviews(ctx context.Context, rList []Review, variables map[string]any) {
	const keyReviews = "ai_review"
	if variables == nil {
		log.V1.CtxWarn(ctx, "variables is null")
	} else {
		newReviews := llmResponseReviewsForMerge{
			Reviews: make([]ReviewForMerge, 0),
		}
		for _, r := range rList {
			newReviews.Reviews = append(newReviews.Reviews, ReviewForMerge{
				Version:     r.Version,
				Line:        strconv.Itoa(int(r.Line.getValue())),
				Issue:       r.Issue,
				IssueType:   r.IssueType,
				Severity:    r.Severity,
				Suggestions: r.Suggestions,
				Label:       lo.FromPtr(r.Label),
			})
		}
		reviewByte, err := json.Marshal(newReviews)
		if err != nil {
			log.V1.CtxError(ctx, "failed to set vars from options for review preparing for merge: %v", err)
		}
		variables[keyReviews] = string(reviewByte)
	}
}

func setKVForFilter(ctx context.Context, r Review, variables map[string]any) {
	const (
		KeyComment = "comment"
		keyLine    = "line_num"
	)
	if variables == nil {
		log.V1.CtxWarn(ctx, "variables is null")
	} else {
		variables[KeyComment] = r.Issue
		if r.Suggestions != "" {
			variables[KeyComment] = r.Issue + ". " + r.Suggestions
		}
		variables[keyLine] = r.Line.getValue()
	}
}

func duplicateContext(intentContext skillentity.Context) skillentity.Context {
	// append comment and line info for filter.
	localIntentContext := skillentity.Context{
		Input:          intentContext.Input,
		Variables:      make(map[string]any),
		IntentPrompts:  intentContext.IntentPrompts,
		ConversationID: intentContext.ConversationID,
		MessageID:      intentContext.MessageID,
	}
	localIntentContext.Variables = deepCopyMap(intentContext.Variables)

	return localIntentContext
}

func deepCopyMap(originalMap map[string]any) map[string]any {
	// only used for non-pointer sub member copy
	newMap := make(map[string]any)
	for k, v := range originalMap {
		newMap[k] = v
	}
	return newMap
}

func duplicateParams(parameters map[string]any) map[string]any {
	return deepCopyMap(parameters)
}

func (c *CodebaseAgent) CreatePatchsetReview(ctx context.Context,
	opt service.CreatePatchsetReviewOption) ([]*entity.AsyncTask, *entity.AsyncTask, error) {
	const (
		BasePatchNum        = -1
		zhWorkCountrySimply = "CHN"
		zhWorkCountry       = "Chinese"
		zhLang              = "zh"
		enLang              = "en"
	)
	// create session ID for patchset review
	sessionID := uuid.NewString()
	logID := tools.GetLogID(ctx)
	log.V1.CtxInfo(ctx, "logid value is %s", logID)
	tasks := make([]*entity.AsyncTask, 0)

	if opt.SourceRevision == "" {
		return nil, nil, errors.New("source revision is invalid")
	}

	var (
		rightPatchsetNum                 int
		leftPatchsetNum                  int
		fromRangeBaseSHA, toRangeBaseSHA string
		// isMQTriggered indicates the review happens automatically on patchset n-1 vs n.
		isMQTriggered             = opt.IsMQTriggered != nil && *opt.IsMQTriggered
		LLMReviewerSupportedLangs = c.CodeAIConfig.GetValue().CodeReview.LLMReviewerSupportedLangs
		FreeStyleSupportedLangs   = c.FreeStyleRulesConfig.GetValue().AccessLangs
		LineCount                 = c.CodeAIConfig.GetValue().GlobalENV.LineCountLimit
	)

	change, err := c.CodebaseCli.GetChangeDetail(ctx, opt.RepoName, int64(opt.ChangeID), false,
		codebaseWithJWT(opt.Account))
	if err != nil {
		return nil, nil, err
	}
	if change == nil {
		log.V1.CtxError(ctx, "change is nil")
		return nil, nil, errors.WithMessage(errors.New("change is nil"), "failed to get change detail")
	}

	author := lo.FromPtr(change.Author.Username)
	if author == "ci_bop_admin" {
		// for other service account, hardcode for now
		// align author with the latest committer (real person)
		changeCommits, err := c.CodebaseCli.GetChangeCurrentCommits(ctx, opt.RepoName, int64(opt.ChangeID), codebaseWithJWT(opt.Account))
		if err != nil {
			return nil, nil, err
		}
		if len(changeCommits.CurrentCommits) != 0 && changeCommits.CurrentCommits[0].Author != nil {
			author = lo.FromPtr(changeCommits.CurrentCommits[0].Author.Name)
			log.V1.CtxInfo(ctx, "align author as real committer %s", changeCommits.CurrentCommits[0].Author.Name)
		}
	}
	// check blocked users
	blockUsernameList := c.CodeAIConfig.GetValue().CodeReview.BlockUsernameList
	if lo.Contains(blockUsernameList, author) ||
		(opt.Account != nil && lo.Contains(blockUsernameList, opt.Account.Username)) {
		return nil, nil, errors.New("MR author has applied to mute Code AI Review")
	}
	// 0. if either patchset number is null, we calculate this by fetching change details and compare with src and tar rev.
	// also check if target revision is passed as empty, for example triggered by vecode event.
	if opt.RightPatchsetNumber == nil || opt.LeftPatchsetNumber == nil || opt.TargetRevision == "" {
		if len(change.PatchSets) <= 0 {
			err = errors.WithMessage(&apierror.ErrParamInvalid, "MR change is empty")
			log.V1.CtxError(ctx, "failed to get change detail: %v", err)
			return nil, nil, err
		}

		// confirm right(source) revision commit
		for _, rev := range change.PatchSets {
			if rev.SHA == opt.SourceRevision {
				rightPatchsetNum = rev.Number
				toRangeBaseSHA = rev.BaseSHA
				if opt.TargetRevision == "" {
					opt.TargetRevision = rev.BaseSHA
					leftPatchsetNum = BasePatchNum
				}
			}
		}
		if rightPatchsetNum <= 0 {
			err = errors.WithMessage(&apierror.ErrParamInvalid, "source revision(right patch) is not correct")
			log.V1.CtxError(ctx, "failed to get source revision, opt Source Revision is %s, err is %v",
				opt.SourceRevision, err)
			return nil, nil, err
		}

		if opt.FunctionID == c.CodeAIConfig.GetValue().GlobalENV.AIFileReviewFunctionID {
			if !isMQTriggered {
				for i, rev := range change.PatchSets {
					if rev.BaseSHA == opt.TargetRevision {
						// base commit
						leftPatchsetNum = BasePatchNum
						break
					} else {
						if rev.SHA == opt.TargetRevision {
							leftPatchsetNum = i + 1
						}
					}
				}
				if leftPatchsetNum == 0 && len(change.PatchSets) != 0 && change.PatchSets[0] != nil {
					// if not found, should check from client-end. Force correct target commit as base commit.
					log.V1.CtxWarn(ctx, "client-end passed target sha not found in change patch sets, correct as base.")
					leftPatchsetNum = -1
					opt.TargetRevision = change.PatchSets[0].BaseSHA
				}
			}
		}

		if isMQTriggered || opt.FunctionID == c.CodeAIConfig.GetValue().GlobalENV.CRCopilotFunctionID {
			// for CR copilot, or an automatically triggered of code-ai review, always compare patchset n vs n-1
			leftPatchsetNum = rightPatchsetNum - 1
			if leftPatchsetNum <= 0 {
				leftPatchsetNum = BasePatchNum
				opt.TargetRevision = change.PatchSets[0].BaseSHA
			} else if leftPatchsetNum-1 < len(change.PatchSets) {
				// since leftPatchsetNum > 0 (at least 1, so leftPatchsetNum - 1 >= 0)
				opt.TargetRevision = change.PatchSets[leftPatchsetNum-1].SHA
				fromRangeBaseSHA = change.PatchSets[leftPatchsetNum-1].BaseSHA
			} else {
				err = errors.WithMessage(&apierror.ErrParamInvalid, "revisions are not valid")
				log.V1.CtxError(ctx, "failed to get source revision: %v", err)
				return nil, nil, err
			}
		}

	} else {
		rightPatchsetNum = *opt.RightPatchsetNumber
		leftPatchsetNum = *opt.LeftPatchsetNumber
	}

	// recheck if params are all valid
	if opt.SourceRevision == "" || opt.TargetRevision == "" {
		return nil, nil, errors.New("source revision or target revision is invalid after pre-process")
	}

	// 1. process file context (including inserted lines etc.) according to revised revisions
	fileContextList := make([]service.FileContext, 0)
	if isMQTriggered || opt.FunctionID == c.CodeAIConfig.GetValue().GlobalENV.CRCopilotFunctionID {
		var (
			changedFiles []*codebase.ChangedFile
			err          error
		)
		// get file list
		if fromRangeBaseSHA != "" && toRangeBaseSHA != "" {
			changedFiles, err = c.CodebaseCli.GetRangeDiffFileList(ctx, codebase.GetRangeDiffFileListOption{
				RepoName:           opt.RepoName,
				FromRangeBaseSHA:   fromRangeBaseSHA,
				FromRangeSourceSHA: opt.TargetRevision,
				ToRangeBaseSHA:     toRangeBaseSHA,
				ToRangeSourceSHA:   opt.SourceRevision,
			}, codebaseWithJWT(opt.Account))
			if err != nil {
				log.V1.CtxError(ctx, "failed to get range file list when creating patchset review")
				return nil, nil, err
			}
		} else {
			changedFiles, err = c.CodebaseCli.GetDiffFileList(ctx, opt.RepoName, opt.TargetRevision,
				opt.SourceRevision, false, codebaseWithJWT(opt.Account))
			if err != nil {
				log.V1.CtxError(ctx, "failed to get file list when creating patchset review")
				return nil, nil, err
			}
		}

		for _, f := range changedFiles {
			fileContextList = append(fileContextList, service.FileContext{
				Path:          f.Path,
				LinesInserted: f.LinesInserted,
				Binary:        f.Binary,
				ChangeType:    f.ChangeType,
			})
		}
	} else if opt.FunctionID == c.CodeAIConfig.GetValue().GlobalENV.AIFileReviewFunctionID {
		// get full info of file context
		changedFiles, err := c.CodebaseCli.GetDiffFileList(ctx, opt.RepoName, opt.TargetRevision,
			opt.SourceRevision, false, codebaseWithJWT(opt.Account))
		if err != nil {
			log.V1.CtxError(ctx, "failed to get file list when creating patchset review")
			return nil, nil, err
		}

		files := changedFiles
		if len(opt.FileContext) != 0 {
			files = nil
			for _, f := range opt.FileContext {
				for _, cf := range changedFiles {
					if cf.Path == f.Path {
						files = append(files, cf)
						break
					}
				}
			}
		}

		for _, cf := range files {
			fileContextList = append(fileContextList, service.FileContext{
				Path:          cf.Path,
				LinesInserted: cf.LinesInserted,
				Binary:        cf.Binary,
				ChangeType:    cf.ChangeType,
			})
		}
	}

	// 2. check exclusion and reserve ready-to-review files in a new list
	var reservedFileContextList []service.FileContext

	// enhanced locale applies on patchset review.
	locale := opt.Locale
	if opt.Account.Locale != "" {
		log.V1.CtxInfo(ctx, "enhanced local prediction for user %s, locale = %s",
			opt.Account.Username, opt.Account.Locale)
		locale = opt.Account.Locale
	}

	// check if repo local is set
	RepoLocaleEnForced := c.CodeAIConfig.GetValue().CodeReview.RepoLocaleEN
	if len(RepoLocaleEnForced) != 0 {
		if lo.Contains(RepoLocaleEnForced, opt.RepoName) {
			locale = enLang
		}
	}

	taskKey := GetUniqueTaskKey(opt.SourceRevision, opt.TargetRevision)
	// create an AI Reviewer Task with "added" status
	// 1. create task
	excludedPath := make([]string, 0)
	unsupportedPath := make([]string, 0)
	var skippedTask *entity.AsyncTask
	for _, fc := range fileContextList {
		// if lang not supported, and no customized knowledge defined, skip this file and put in the skipped list
		lang, _ := enry.GetLanguageByExtension(fc.Path)
		supportedLangs := llmreviewer.SupportedLanguages
		if len(LLMReviewerSupportedLangs) != 0 {
			supportedLangs = LLMReviewerSupportedLangs
		}

		reviewsRules, err := c.ReviewsSettings.GetAllCachedRulesByRepo(ctx, opt.RepoName, DefaultRevision)
		if err != nil {
			log.V1.CtxError(ctx, CusLogPrefix+"failed to get repo rules in creating stage, err = %v", err)
		}

		if !lo.Contains(supportedLangs, lang) &&
			(!lo.Contains(FreeStyleSupportedLangs, "ALL") && !lo.Contains(FreeStyleSupportedLangs, lang)) &&
			len(reviewsRules.RepoReviewRules) == 0 && len(reviewsRules.PathReviewRules) == 0 {
			unsupportedPath = append(unsupportedPath, fc.Path+", "+
				"reason: "+"not current supported lang and no customized review rules defined")
			continue
		}

		// dispatch by hunk-wise
		// a. get pre- and post- lines group. Note: the revision are already SHA right now, due to pre-processing above.
		exclude, reason := c.shouldExclude(ctx, fc)
		if exclude {
			// filter out src code.
			excludedPath = append(excludedPath, fc.Path+", reason: "+reason)
		} else {
			// further check if duplicated file to review has been created recently
			isDuplicated := c.getDuplicateOrSet(ctx, fc.Path, taskKey)
			if isDuplicated {
				excludedPath = append(excludedPath, fc.Path+", reason: "+NoErrDupTaskShortTime)
			} else {
				reservedFileContextList = append(reservedFileContextList, fc)
			}
		}
	}
	if len(excludedPath) > 0 || len(unsupportedPath) > 0 {
		toSkipAll := append(excludedPath, unsupportedPath...)
		param := service.AIFileReviewInvokeParams{
			RepoName:         opt.RepoName,
			ChangeID:         opt.ChangeID,
			SourceRev:        opt.SourceRevision,
			TargetRev:        opt.TargetRevision,
			SourceBaseCommit: toRangeBaseSHA,
			TargetBaseCommit: fromRangeBaseSHA,
			RightPatchsetNum: rightPatchsetNum,
			LeftPatchsetNum:  leftPatchsetNum,
			Path:             strings.Join(toSkipAll, "\n"),
			Locale:           locale,
		}
		paramStr, err := setVarsFromOption(param)
		if err != nil {
			log.V1.CtxError(ctx, "failed to config task review params from patchset review: %v", err)
			return nil, nil, err
		}

		skippedTask, err = c.DAO.CreateAsyncTask(ctx, dal.CreateAsyncTaskOption{
			Params: &entity.Parameters{
				Params:     paramStr,
				SessionID:  sessionID,
				FunctionID: opt.FunctionID,
			},
			AppID:    opt.AppID,
			TaskType: entity.TaskTypeCodebaseAIFileReview,
			Status:   entity.StatusCodebaseAIFileReviewFinished.String(),
			// skipped tasks will not impact users, so ignore if username is true user or BitsAI app
			User:    opt.Account.Username,
			TaskKey: lo.ToPtr(taskKey),
			Message: lo.ToPtr(NoErrFilteredFile + ":see matched rule in params.path"),
		})
		if err != nil {
			return nil, nil, errors.WithMessage(err, "failed to create ai file review task for skipped file.")
		}
	}

	// 2. for each reserved file
	fileCountLimit := c.CodeAIConfig.GetValue().GlobalENV.FileCountLimit
	createdCnt := 0
	totalAddedLineCnt := 0
	for _, fc := range reservedFileContextList {
		lang, _ := enry.GetLanguageByExtension(fc.Path)
		useFunction := llmreviewer.FunctionName

		// dispatch by hunk-wise
		// a. get pre- and post- lines group. Note: the revision are already SHA right now, due to pre-processing above.
		preCodeMeta, postCodeMeta, err := c.CodeCopilotService.GetDiffFileWithLineNumber(ctx,
			codecopilotentity.GetDiffFileWithLineNumberOptions{
				RepoName:    opt.RepoName,
				FromSHA:     opt.TargetRevision,
				FromBaseSHA: fromRangeBaseSHA,
				ToSHA:       opt.SourceRevision,
				ToBaseSHA:   toRangeBaseSHA,
				Path:        fc.Path,
			}, &authentity.Account{
				Username:          "BitsAI",
				CodebaseBitsAIJWT: c.BitsAISecret,
			})
		if err != nil {
			// we cannot handle the critical bug when comment beyond changed lines, so stop and won't create.
			log.V1.CtxError(ctx, "failed to get impacted lines for filter, err = %v", err)
			continue
		}

		hunkNum := len(postCodeMeta.HunkSplitImpactLines)
		breakFromLineCntLimit := false
		for i := 0; i < hunkNum && i < len(preCodeMeta.HunkSplitImpactLines); i++ {
			if len(postCodeMeta.HunkSplitImpactLines[i]) == 0 {
				// means no new/added lines in this hunk, just skip.
				log.V1.CtxInfo(ctx, "no post impacted lines for file %s, repo %s, skip",
					fc.Path, opt.RepoName)
				continue
			}
			param := service.AIFileReviewInvokeParams{
				RepoName:         opt.RepoName,
				ChangeID:         opt.ChangeID,
				SourceRev:        opt.SourceRevision,
				TargetRev:        opt.TargetRevision,
				SourceBaseCommit: toRangeBaseSHA,
				TargetBaseCommit: fromRangeBaseSHA,
				RightPatchsetNum: rightPatchsetNum,
				LeftPatchsetNum:  leftPatchsetNum,
				PreImpactLines:   preCodeMeta.HunkSplitImpactLines[i],
				PostImpactLines:  postCodeMeta.HunkSplitImpactLines[i],
				Path:             fc.Path,
				Locale:           locale,
				Language:         lang,
			}

			curAddedLineCnt := len(postCodeMeta.HunkSplitImpactLines[i])
			param.UseLLMReviewer = lo.ToPtr(true)

			// need another GetDiffFileWithLineNumber call to get pre- and post- diff content
			newPreCodeMeta, newPostCodeMeta, err := c.CodeCopilotService.GetDiffFileWithLineNumber(ctx,
				codecopilotentity.GetDiffFileWithLineNumberOptions{
					RepoName:        opt.RepoName,
					FromSHA:         opt.TargetRevision,
					FromBaseSHA:     fromRangeBaseSHA,
					ToSHA:           opt.SourceRevision,
					ToBaseSHA:       toRangeBaseSHA,
					Path:            fc.Path,
					PreImpactLines:  preCodeMeta.HunkSplitImpactLines[i],
					PostImpactLines: postCodeMeta.HunkSplitImpactLines[i],
				}, &authentity.Account{
					Username:          "BitsAI",
					CodebaseBitsAIJWT: c.BitsAISecret,
				})
			if err != nil {
				// we cannot handle the critical bug when comment beyond changed lines, so stop and won't create.
				log.V1.CtxError(ctx, "failed to get impacted lines for filter, err = %v", err)
				continue
			}
			resolvedCodeDiff := fmt.Sprintf("```Old Version Code:%s\n%s\n```\n\n```New Version Code:%s\n%s\n```\n",
				lang, newPreCodeMeta.Content, lang, newPostCodeMeta.Content)
			param.DiffPatchCode = lo.ToPtr(resolvedCodeDiff)

			// always save mr author as user
			param.Username = lo.ToPtr(author)
			if opt.Account != nil {
				param.Triggerer = lo.ToPtr(opt.Account.Username)
				if opt.Account.Username != author {
					// triggered user is not author, we need to reset locale align to author
					authorInfo, err := c.CodebaseCli.BatchGetEmployees(ctx,
						[]string{author}, codebase.WithBitsAIServiceJWT(c.BitsAISecret))
					if err != nil {
						log.V1.CtxError(ctx, "failed to get mr author info: %v", err)
						// continue to create task, pay attention to locale settings in successive code logic
					}
					// en as locale by default
					param.Locale = enLang
					if authorInfo != nil && len(authorInfo.Employees) != 0 &&
						authorInfo.Employees[0].WorkCountryEnName != "" {
						workCountry := authorInfo.Employees[0].WorkCountryEnName
						if workCountry == zhWorkCountrySimply || strings.Contains(workCountry, zhWorkCountry) {
							param.Locale = zhLang
						}
					}
					if c.CodeAIConfig.GetValue().CodeReview.UserLocaleZH != nil {
						if lo.Contains(c.CodeAIConfig.GetValue().CodeReview.UserLocaleZH, author) {
							param.Locale = zhLang
						}
					}
					// log align status
					log.V1.CtxInfo(ctx, "align locale with mr author: %s, locale: %s",
						author, param.Locale)
				}
			}
			if isMQTriggered {
				param.IsAutoTriggered = lo.ToPtr(true)
			}

			paramStr, err := setVarsFromOption(param)
			if err != nil {
				log.V1.CtxError(ctx, "failed to config task review params from patchset review: %v", err)
				return nil, nil, err
			}

			reviewTask, err := c.CreateAIFileReviewTask(ctx, service.CreateAIFileReviewTaskOption{
				Params: entity.Parameters{
					Params:     paramStr,
					SessionID:  sessionID,
					FunctionID: useFunction,
					LogID:      logID,
				},
				SessionID:  lo.ToPtr(sessionID),
				AppID:      opt.AppID,
				FunctionID: useFunction,
				Account:    opt.Account,
				TaskKey:    lo.ToPtr(taskKey),
			})
			log.V1.CtxInfo(ctx, "created ai review task for file %s, repo %s",
				fc.Path, opt.RepoName)
			if err != nil {
				log.V1.CtxError(ctx, "failed to create review task from patchset review session: %v", err)
				return nil, nil, err
			}
			tasks = append(tasks, reviewTask)
			totalAddedLineCnt += curAddedLineCnt
			if LineCount > 0 && totalAddedLineCnt >= LineCount {
				log.V1.CtxInfo(ctx, "reach max added line count %d, stop creating tasks",
					LineCount)
				breakFromLineCntLimit = true
				break
			}
		}
		createdCnt++
		if createdCnt >= fileCountLimit {
			log.V1.CtxInfo(ctx, "reach file count limit %d, stop creating tasks", fileCountLimit)
			break
		}
		if breakFromLineCntLimit {
			break
		}
	}

	if len(tasks) > 0 {
		// 3. create AI reviewer after all file-review tasks created (in case cronjob retrieves only part of them)
		task, err := c.CreateAIReviewerTask(ctx, service.CreateAIReviewerOption{
			RepoName:       opt.RepoName,
			ChangeID:       opt.ChangeID,
			SourceRevision: opt.SourceRevision,
			TargetRevision: opt.TargetRevision,
			IsMQTriggered:  lo.ToPtr(isMQTriggered),
			Creator:        opt.Account.Username,
			Locale:         locale,
		})
		if err != nil {
			log.V1.CtxError(ctx, "failed to create intent base for file review")
			return nil, nil, err
		}

		log.V1.CtxInfo(ctx, "created AI Reviewer Task with ID %d, task_key %s", task.ID, task.TaskKey)
	} else {
		// 4. won't create AI reviewer upon successfully created skipped-tasks. publish directly.
		var commentContent string
		if len(unsupportedPath) > 0 {
			commentContent = NoReviewNotSupported
		} else if len(excludedPath) > 0 {
			commentContent = NoReviewFiltered
		}
		createReviewOptions := codebase.CreateReviewsOptions{
			Content: commentContent,
			Event:   "comment",
			Sha:     opt.SourceRevision,
		}
		err = c.CodebaseCli.CreateReviews(ctx, opt.RepoName,
			int64(opt.ChangeID),
			createReviewOptions, codebase.WithBitsAIServiceJWT(c.BitsAISecret))
		if err != nil {
			log.V1.CtxError(ctx, "failed to created reviews for ai reviewer: %v", err)
			return nil, nil, err
		}
	}

	return tasks, skippedTask, nil
}

func (c *CodebaseAgent) getDuplicateOrSet(ctx context.Context, filePath, taskKey string) bool {
	var AIReviewTaskCreatedKey = "ai_review_task_created" + "_" + filePath + "_" + taskKey
	const AIReviewCreatedTaskTTL = time.Second * 30
	// check redis and set if not exist
	_, err := c.RedisClient.Get(ctx, AIReviewTaskCreatedKey)
	if err != nil {
		log.V1.CtxInfo(ctx, "unable to get last review timestamp, err = %v", err)
		// if not exist, set the key with value of current time and lifecycle as 10 mins
		if err := c.RedisClient.Set(ctx, AIReviewTaskCreatedKey,
			strconv.FormatInt(time.Now().UnixMilli(), 10), AIReviewCreatedTaskTTL); err != nil {
			log.V1.CtxError(ctx, "failed to set last review timestamp, err = %v", err)
			// return false regarding as non-duplicated
		}
		return false
	}
	return true
}

func (c *CodebaseAgent) GetPatchsetReviewStatus(ctx context.Context,
	opt service.PatchsetReviewStatusOption) ([]entity.PatchsetReviewStatusResponse, error) {
	taskKey := GetUniqueTaskKey(opt.SourceRevision, opt.TargetRevision)
	taskPOs, err := c.DAO.ListAsyncTasks(ctx, dal.ListAsyncTaskOption{
		TaskType: lo.ToPtr(entity.TaskTypeCodebaseAIFileReview),
		TaskKey:  lo.ToPtr(taskKey),
		Desc:     lo.ToPtr(true),
	})
	if err != nil {
		log.V1.CtxError(ctx, "list tasks with same taskKey: %s failed", taskKey)
		return nil, err
	}

	if len(taskPOs) == 0 {
		log.V1.CtxWarn(ctx, "task key not found: %s", taskKey)
		return nil, nil
	}

	if taskPOs[0].Params == nil || taskPOs[0].Params.Params == nil {
		return nil, errors.New("params of latest session is invalid")
	}

	// since list by desc order, parse the first session id as the latest session.
	sessionID := taskPOs[0].Params.SessionID

	status := make([]entity.PatchsetReviewStatusResponse, 0)
	for _, po := range taskPOs {
		var (
			path         string
			commentCount int
			changeID     uint64
			functionID   string
		)

		if po.Params.SessionID != sessionID {
			// previous sessions, just break since it's descending order.
			break
		}

		functionID = po.Params.FunctionID

		if po.Params.Params != nil {
			params, err := getOptionFromVars[service.AIFileReviewInvokeParams](po.Params.Params)
			if err != nil {
				log.V1.CtxWarn(ctx, "failed to get options from ai file review params, err = %v", err)
			}
			changeID = params.ChangeID
		}

		if po.Metadata != nil {
			m, ok := po.Metadata[entity.TaskTypeCodebaseAIFileReview.String()]
			if !ok || m == nil {
				log.V1.CtxInfo(ctx, "failed to get metadata, try to parse entire metadata")
				m = po.Metadata
			}
			mMap := m.(map[string]any)
			metadata, err := getOptionFromVars[AIFileReviewMetadata](mMap)
			if err != nil {
				log.V1.CtxWarn(ctx, "failed to get options from metadata, err = %v", err)
				continue
			}
			path = metadata.FilePath
			commentCount = metadata.CommentCount
		}

		status = append(status, entity.PatchsetReviewStatusResponse{
			TaskID:       po.ID,
			FunctionID:   functionID,
			ChangeID:     changeID,
			SessionID:    sessionID,
			Path:         path,
			CommentCount: commentCount,
			Status:       po.Status,
		})
	}
	return status, nil
}

func (c *CodebaseAgent) DeletePatchsetReview(ctx context.Context, opt service.PatchsetReviewStatusOption) error {
	taskKey := GetUniqueTaskKey(opt.SourceRevision, opt.TargetRevision)
	taskPOs, err := c.DAO.ListAsyncTasks(ctx, dal.ListAsyncTaskOption{
		TaskType: lo.ToPtr(entity.TaskTypeCodebaseAIFileReview),
		TaskKey:  lo.ToPtr(taskKey),
		Desc:     lo.ToPtr(true),
	})
	if err != nil {
		log.V1.CtxError(ctx, "list tasks with same taskKey: %s failed", taskKey)
		return err
	}

	if len(taskPOs) == 0 {
		log.V1.CtxWarn(ctx, "task key not found: %s", taskKey)
		return nil
	}

	if taskPOs[0].Params == nil || taskPOs[0].Params.Params == nil {
		return errors.New("params of latest session is invalid")
	}

	// since list by desc order, parse the first session id as the latest session.
	sessionID := taskPOs[0].Params.SessionID
	for _, po := range taskPOs {
		if po.Params.SessionID != sessionID {
			// previous sessions, just break since it's descending order.
			break
		}
		if po.Status == entity.StatusCodebaseAIFileReviewCancelled.String() ||
			po.Status == entity.StatusCodebaseAIFileReviewFinished.String() {
			continue
		}
		// set status as cancelled
		_, err = c.updateStatus(ctx, po.ID,
			entity.StatusCodebaseAIFileReviewCancelled.String(), lo.ToPtr(NoErrUserCancel), nil)
		if err != nil {
			log.V1.CtxError(ctx, "failed to update cancelled task, err = %v", err)
		}
	}
	return nil
}

func (c *CodebaseAgent) CreateAIFileReviewTask(ctx context.Context,
	opt service.CreateAIFileReviewTaskOption) (*entity.AsyncTask, error) {
	var sessionID = uuid.NewString()
	if opt.SessionID != nil {
		sessionID = *opt.SessionID
	}

	// Since user's account needs directly for chat service, we directly process here
	// 1. pre-check intent and params
	if opt.Params.Params == nil || len(opt.Params.Params) == 0 {
		return nil, errors.New("no params provided for ai file review task")
	}

	param, err := getOptionFromVars[service.AIFileReviewInvokeParams](opt.Params.Params)
	if err != nil {
		log.V1.CtxError(ctx, "failed to get ai file review params, err = %v", err)
		return nil, err
	}

	// 2. Create task first
	metadata, err := SerializeMetadata(ctx, AIFileReviewMetadata{
		FilePath:          param.Path,
		CommentCount:      0,
		ThreadIDs:         nil,
		FilteredThreadIDs: nil,
	})
	if err != nil {
		log.V1.CtxError(ctx, "serial metadata failed, %v", err)
		metadata = nil
	}
	// replace user if specifically set, eg. auto triggered in Params
	var username string
	if param.Triggerer != nil && lo.FromPtr(param.Triggerer) != BitsAIUserName {
		username = lo.FromPtr(param.Triggerer)
	} else {
		username = lo.FromPtr(param.Username)
	}
	po, err := c.DAO.CreateAsyncTask(ctx, dal.CreateAsyncTaskOption{
		// Create task with init status: llm ready
		TaskType: entity.TaskTypeCodebaseAIFileReview,
		Params: &entity.Parameters{
			Params:     opt.Params.Params,
			SessionID:  sessionID,
			FunctionID: opt.FunctionID,
			LogID:      opt.Params.LogID,
		},
		User:     username,
		AppID:    opt.AppID,
		Status:   entity.StatusV2CodebaseAIFileReviewReady.String(),
		TaskKey:  opt.TaskKey,
		Metadata: metadata,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to create ai file review task")
	}

	return po, nil
}

func (c *CodebaseAgent) UpdateStatusUponLLMRespond(ctx context.Context,
	opt service.UpdateTaskOption) (*entity.AsyncTask, error) {
	var status string

	taskPO, err := c.GetTaskInfo(ctx, opt.TaskID)
	if err != nil {
		return nil, errors.WithMessage(err, "fail to get task PO")
	}
	if opt.LLMResponse != nil {
		switch taskPO.TaskType {
		case entity.TaskTypeCodebaseAIReply.String():
			status = entity.StatusCodebaseAIReplyRespond.String()
		case entity.TaskTypeCodebaseAIFileReview.String():
			// default as responded
			if opt.Status != nil && lo.FromPtr(opt.Status) != "" {
				// mid-status
				status = lo.FromPtr(opt.Status)
			} else {
				status = entity.StatusV2CodebaseAIFileReviewResponded.String()
			}
		default:
			return nil, errors.New("unknown type of task PO")
		}
	}

	if taskPO.Status == entity.StatusCodebaseAIFileReviewCancelled.String() {
		// active cancel by user
		return nil, nil
	}

	po, err := c.DAO.UpdateAsyncStatus(ctx, dal.UpdateAsyncTaskStatusOption{
		ID:          opt.TaskID,
		Status:      &status,
		Message:     opt.Message,
		LLMResponse: opt.LLMResponse,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to update codebase agent task")
	}
	return po, nil
}

func (c *CodebaseAgent) updateStatus(ctx context.Context, taskID uint64, status string, errMsg *string, metadata map[string]any) (*entity.AsyncTask, error) {
	po, err := c.DAO.UpdateAsyncStatus(ctx, dal.UpdateAsyncTaskStatusOption{
		ID:       taskID,
		Status:   &status,
		Message:  errMsg,
		Metadata: metadata,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to update codebase agent task status")
	}
	return po, nil
}

func (c *CodebaseAgent) CancelTask(ctx context.Context, taskID uint64, message *string) error {
	// set as cencel and delete
	var status string
	taskPO, err := c.GetTaskInfo(ctx, taskID)
	if err != nil {
		return errors.WithMessage(err, "fail to get task PO")
	}
	switch taskPO.TaskType {
	case entity.TaskTypeCodebaseAIReply.String():
		status = entity.StatusCodebaseAIReplyCancelled.String()
	case entity.TaskTypeCodebaseAIFileReview.String():
		status = entity.StatusCodebaseAIFileReviewCancelled.String()
	default:
		return errors.New("unknown type of task PO")
	}

	_, err = c.DAO.UpdateAsyncStatus(ctx, dal.UpdateAsyncTaskStatusOption{
		ID:      taskID,
		Status:  &status,
		Message: message,
	})
	if err != nil {
		log.V1.CtxError(ctx, "set status of cancel to task id %d failed:%v. Continue.", taskID, err)
		// continue
	}

	deleted, err := c.DAO.DeleteAsyncTask(ctx, taskID)
	if err != nil {
		return err
	}

	if deleted != 1 {
		return errors.New("no task is deleted, maybe a mismatched id is given")
	}

	return nil
}

const (
	AIFileReviewCommentFormatEN = `📝 **Review Type: {{ .issue_type }}**.
{{ .issue }}

❇️ **Review Suggestions**：
<details ><summary>View details</summary>

{{ .suggestions }}
</details>
`
	AIFileReviewCommentFormatZH = `📝 **评审类型：{{ .issue_type }}**.
{{ .issue }}

❇️ **评审建议**：
<details ><summary>View details</summary>

{{ .suggestions }}
</details>
`
	AIFileReviewCommentFormatENWithOpen = `📝 **Review Type: {{ .issue_type }}**.
{{ .issue }}

❇️ **Review Suggestions**：
<details open><summary>View details</summary>

{{ .suggestions }}
</details>
`
	AIFileReviewCommentFormatZHWithOpen = `📝 **评审类型：{{ .issue_type }}**.
{{ .issue }}

❇️ **评审建议**：
<details open><summary>View details</summary>

{{ .suggestions }}
</details>
`
	AIFileReviewCommentFormatENSimple = `📝 **Review Type: {{ .issue_type }}**.
{{ .issue }}
`
	AIFileReviewCommentFormatZHSimple = `📝 **评审类型：{{ .issue_type }}**.
{{ .issue }}
`

	AIFileReviewCommentFormatENWithoutType = `{{ .issue }}

❇️ **Review Suggestions**：
<details ><summary>View details</summary>

{{ .suggestions }}
</details>
`
	AIFileReviewCommentFormatZHWithoutType = `{{ .issue }}

❇️ **评审建议**：
<details ><summary>View details</summary>

{{ .suggestions }}
</details>
`
	AIFileReviewCommentFormatENWithOpenWithoutType = `{{ .issue }}

❇️ **Review Suggestions**：
<details open><summary>View details</summary>

{{ .suggestions }}
</details>
`
	AIFileReviewCommentFormatZHWithOpenWithoutType = `{{ .issue }}

❇️ **评审建议**：
<details open><summary>View details</summary>

{{ .suggestions }}
</details>
`
	AIFileReviewCommentFormatENSimpleWithoutType = `{{ .issue }}
`
	AIFileReviewCommentFormatZHSimpleWithoutType = `{{ .issue }}
`
	SuggestionSep = "❇️"
)

func renderSuggestion(text string) string {
	const minMatchCount = 3
	re := regexp.MustCompile("(?s)```(\\w+)\\n(.*?)```")
	result := re.ReplaceAllStringFunc(text, func(match string) string {
		subMatches := re.FindStringSubmatch(match)
		if len(subMatches) < minMatchCount {
			return match // return the original match if there are not enough sub-matches
		}
		language := strings.ToLower(subMatches[1])
		code := subMatches[2]
		code = encodeCode(code)
		return fmt.Sprintf(`<pre><code class="language-%s">%s</code></pre>`, language, code)
	})
	return result
}

func encodeCode(text string) string {
	// replace < to &lt; and replace > to &gt;
	result := strings.ReplaceAll(text, "<", "&lt;")
	result = strings.ReplaceAll(result, ">", "&gt;")
	return result
}

func (c *CodebaseAgent) renderComment(r Review, locale string, fixCode string, fixExplain string, isCustom bool) (string, string) {
	const (
		localeEN = "en"
		localeZH = "zh"
	)

	templatesFull := map[string]string{
		localeEN: AIFileReviewCommentFormatEN,
		localeZH: AIFileReviewCommentFormatZH,
	}
	if len(c.CodeAIConfig.GetValue().CodeReview.SuggestionOpenRules) != 0 {
		suggestionRules := c.CodeAIConfig.GetValue().CodeReview.SuggestionOpenRules
		if lo.Contains(suggestionRules, r.IssueType) {
			templatesFull = map[string]string{
				localeEN: AIFileReviewCommentFormatENWithOpen,
				localeZH: AIFileReviewCommentFormatZHWithOpen,
			}
		}
	}

	templatesSimple := map[string]string{
		localeEN: AIFileReviewCommentFormatENSimple,
		localeZH: AIFileReviewCommentFormatZHSimple,
	}

	templates := templatesFull
	if r.Suggestions == "" {
		templates = templatesSimple
	}

	var comment string
	t, ok := templates[locale]
	if !ok {
		// use en as default
		t = templates[localeEN]
	}
	comment = t
	// commentOnly is only used for checking similarity, use either locale
	commentOnly := templatesSimple[localeEN]

	// this filter comment notice will only be used when filtered comment is reserved from previous pipeline
	// and will be exposed to users
	backupFilteredCommentNotice := "⚠This comment was automatically filtered by AI " +
		"and immediately marked as resolved for reference only.\n\n"
	if r.IsFiltered != nil && *r.IsFiltered {
		comment = comment + "\n" + backupFilteredCommentNotice
	}

	// remain comment from LLM response, but present with readable user-friendly issue.
	issue := r.IssueType
	if len(c.CodeAIConfig.GetValue().CodeReview.RulesMappingIssue) != 0 {
		rulesMappingIssue := c.CodeAIConfig.GetValue().CodeReview.RulesMappingIssue
		for _, rule := range rulesMappingIssue {
			if lo.Contains(rule.Rules, r.IssueType) {
				issue = rule.Issue
				if locale == localeEN && rule.IssueEN != "" {
					// if en desc is valid and not empty, check if locale is en, use en desc
					issue = rule.IssueEN
				}
				break
			}
		}
	}

	// Replace placeholders with actual values
	comment = strings.Replace(comment, "{{ .issue_type }}", issue, -1)
	comment = strings.Replace(comment, "{{ .line }}", fmt.Sprintln(r.Line.getValue()), -1)
	// issueContent := encodeCode(r.Issue)
	comment = strings.Replace(comment, "{{ .issue }}", r.Issue, -1)
	suggestion := ""
	// 先渲染老版的suggestion
	if r.Suggestions != "" {
		suggestion = renderSuggestion(r.Suggestions)
	}
	// 如果是自定义规则，把fix相关内容拼在后面（如果有的话）
	if isCustom {
		fixExplain = fmt.Sprintf("%s\n%s", suggestion, fixExplain)
	}
	// 如果有fix的code，渲染diff格式的代码
	if fixCode != "" {
		suggestion = fmt.Sprintf("%s\n```suggestion\n%s\n```", fixExplain, fixCode)
	}
	comment = strings.Replace(comment, "{{ .suggestions }}", suggestion, -1)

	// Render comment only
	commentOnly = strings.Replace(commentOnly, "{{ .issue_type }}", issue, -1)
	commentOnly = strings.Replace(commentOnly, "{{ .line }}", fmt.Sprintln(r.Line.getValue()), -1)
	commentOnly = strings.Replace(commentOnly, "{{ .issue }}", r.Issue, -1)

	return commentOnly, comment
}

type AIFileReviewResponse struct {
	PromptID     int64  `json:"prompt_id"`
	FunctionID   string `json:"function_id"`
	ErrorMessage string `json:"error_message,omitempty"`
}

type AIFileReviewMetadata struct {
	FilePath           string                     `json:"file_path" mapstructure:"file_path"`
	CommentCount       int                        `json:"comment_count" mapstructure:"comment_count"`
	ThreadIDs          []int64                    `json:"thread_ids" mapstructure:"thread_ids"`
	FilteredThreadIDs  []int64                    `json:"filtered_thread_ids" mapstructure:"filtered_thread_ids"`
	ReviewResponses    []AIFileReviewResponse     `json:"review_responses,omitempty" mapstructure:"review_responses"`
	PromptID           *int64                     `json:"prompt_id,omitempty" mapstructure:"prompt_id"`
	PromptIDCustomized *int64                     `json:"prompt_id_customized,omitempty" mapstructure:"prompt_id_customized"`
	MergePromptID      *int64                     `json:"merge_prompt_id,omitempty" mapstructure:"merge_prompt_id"`
	StepLatencyList    []entity.StepLatencyRecord `json:"step_latency_list,omitempty" mapstructure:"step_latency_list"`
}

type MRWalkthroughMetadata struct {
	PromptID        uint64                     `json:"prompt_id" mapstructure:"prompt_id"`
	MRWalkthrough   MRWalkthrough              `json:"mr_walkthrough" mapstructure:"mr_walkthrough"`
	StepLatencyList []entity.StepLatencyRecord `json:"step_latency_list,omitempty" mapstructure:"step_latency_list"`
}

func SerializeMetadata(ctx context.Context, v AIFileReviewMetadata) (map[string]any, error) {
	jsonBytes, err := json.Marshal(v)
	if err != nil {
		log.V1.CtxError(ctx, "Error marshaling metadata to JSON: err = %v", err)
		return nil, err
	}

	// Parse the JSON into a map[string]any.
	var fileReviewMetadata map[string]any
	err = json.Unmarshal(jsonBytes, &fileReviewMetadata)
	if err != nil {
		log.V1.CtxError(ctx, "Error unmarshalling JSON to map: err = %v", err)
		return nil, err
	}

	return fileReviewMetadata, nil
}

func SerializeMRWalkThroughMetadata(ctx context.Context, v MRWalkthroughMetadata) (map[string]any, error) {
	jsonBytes, err := json.Marshal(v)
	if err != nil {
		log.V1.CtxError(ctx, "Error marshaling metadata to JSON: err = %v", err)
		return nil, err
	}

	// Parse the JSON into a map[string]any.
	var mrWalkThrough map[string]any
	err = json.Unmarshal(jsonBytes, &mrWalkThrough)
	if err != nil {
		log.V1.CtxError(ctx, "Error unmarshalling JSON to map: err = %v", err)
		return nil, err
	}

	return mrWalkThrough, nil
}

func packMetadata(path string, res []AIFileReviewResponse) AIFileReviewMetadata {
	return AIFileReviewMetadata{
		FilePath:          path,
		CommentCount:      0,
		ThreadIDs:         nil,
		FilteredThreadIDs: nil,
		ReviewResponses:   res,
	}
}

func (c *CodebaseAgent) getPreRecallPerTask(ctx context.Context, po *entity.AsyncTask, localParams map[string]any,
	intentContext skillentity.Context, cusRulesOrigin *string) (*llmResponseReviews, map[string]any, *AIFileReviewMetadata) {
	startTime := time.Now()
	var (
		preMergedReviews = llmResponseReviews{}
	)

	if po.Params == nil || po.Params.Params == nil {
		log.V1.CtxWarn(ctx, "task has no params")
		return nil, nil, nil
	}

	params, err := getOptionFromVars[service.AIFileReviewInvokeParams](po.Params.Params)
	if err != nil {
		log.V1.CtxWarn(ctx, "failed to get context variables, err = %v", err)
		return nil, nil, nil
	}

	if cusRulesOrigin == nil {
		// not customized or freestyled
		lang := params.Language
		supportedLang := c.CodeAIConfig.GetValue().CodeReview.LLMReviewerSupportedLangs
		if len(supportedLang) == 0 {
			supportedLang = llmreviewer.SupportedLanguages
		}
		if !lo.Contains(supportedLang, lang) {
			log.V1.CtxInfo(ctx, "unsupported language for general recall: %s", lang)
			return &preMergedReviews, nil, nil
		}
	}

	reviewResponses := make([]AIFileReviewResponse, 0)
	defaultMetadata := packMetadata(params.Path, reviewResponses)
	defaultMetadata.StepLatencyList = make([]entity.StepLatencyRecord, 0)
	response := AIFileReviewResponse{}
	log.V1.CtxInfo(ctx, "start to invoke llm reviewer")
	llmResponse, err := c.ChatService.Invoke(ctx,
		chatservice.InvokeOption{
			AppID:      po.AppID,
			FunctionID: po.Params.FunctionID,
			Parameters: localParams,
			Context:    &intentContext,
			Stream:     false,
			Account: &authentity.Account{
				Username:          "BitsAI",
				CodebaseBitsAIJWT: c.BitsAISecret,
			},
			SessionID: po.Params.SessionID,
		})
	if err != nil {
		log.V1.CtxError(ctx, "invoke service failed: %v", err)
		response.ErrorMessage = ErrChatLLMFailed.Error() + "Internal Err:" + err.Error()
		reviewResponses = append(reviewResponses, response)
		metadata := packMetadata(params.Path, reviewResponses)
		return nil, nil, &metadata
	}

	var stepID string
	if cusRulesOrigin != nil {
		switch *cusRulesOrigin {
		case "customized":
			stepID = CusLogPrefix + "invoke_llm_review"
		case "freestyle":
			stepID = FreeStylePrefix + "invoke_llm_review"
		default:
			stepID = "invoke_llm_review"
		}
	} else {
		stepID = "invoke_llm_review"
	}

	defaultMetadata.StepLatencyList = append(defaultMetadata.StepLatencyList, entity.StepLatencyRecord{
		StepID:  stepID,
		TaskID:  po.ID,
		Latency: time.Since(startTime).Milliseconds(),
	})

	// update variables for re-use in later invoke(filter etc.)
	updatedVariables := duplicateParams(intentContext.Variables)

	if llmResponse == nil || llmResponse.Outputs == nil {
		log.V1.CtxError(ctx, "invoke service failed due to empty llmResponse or output")
		response.ErrorMessage = ErrChatLLMFailed.Error() +
			".Internal Err: empty llmResponse or output"
		reviewResponses = append(reviewResponses, response)
		defaultMetadata.FilePath = params.Path
		defaultMetadata.ReviewResponses = reviewResponses
		return nil, nil, &defaultMetadata
	}

	chatPromptID := conv.Int64Default(llmResponse.Outputs["prompt_completion_id"], -1)
	// update the metadata - add chat prompt ID corresponding to model id
	response.PromptID = chatPromptID
	if cusRulesOrigin == nil {
		// 处理 nil 指针的情况
		response.FunctionID = po.Params.FunctionID
	} else {
		// 根据指针指向的字符串内容进行比较
		switch *cusRulesOrigin {
		case "customized":
			response.FunctionID = CusFunctionIDPrefix + po.Params.FunctionID
		case "freestyle":
			response.FunctionID = FreeStyleFunctionIDPrefix + po.Params.FunctionID
		default:
			response.FunctionID = po.Params.FunctionID
		}
	}

	log.V1.CtxInfo(ctx, fmt.Sprintln("result returned: ", llmResponse.Outputs["result"]))
	// extract the result
	result, exist := llmResponse.Outputs["result"]
	if !exist {
		log.V1.CtxError(ctx, "failed to get result key of llm response outputs")
		response.ErrorMessage = ErrParseLLMResponseFailed.Error()
		reviewResponses = append(reviewResponses, response)
		defaultMetadata.FilePath = params.Path
		defaultMetadata.ReviewResponses = reviewResponses
		return nil, nil, &defaultMetadata
	}

	reviewComments := result.(string)
	if reviewComments != "" {
		text := strings.ReplaceAll(reviewComments, "：", ":")
		if strings.Contains(text, "None") || strings.Contains(strings.ToLower(text), GoodPatternOutputLGTM) ||
			(!strings.Contains(text, "{") && strings.Contains(text, "代码")) {
			// rough judgement on llm response if good case by check log
			response.ErrorMessage = NoErrReviewComment
			reviewResponses = append(reviewResponses, response)
			defaultMetadata.FilePath = params.Path
			defaultMetadata.ReviewResponses = reviewResponses
			return &preMergedReviews, nil, &defaultMetadata
		}

		var formattedReviews llmResponseReviews
		err = json.Unmarshal([]byte(reviewComments), &formattedReviews)
		if err != nil {
			log.V1.CtxError(ctx, fmt.Sprintf("failed to parse llm response: %v, data = %s",
				err, reviewComments))
			response.ErrorMessage = ErrParseLLMResponseFailed.Error()
			reviewResponses = append(reviewResponses, response)
			defaultMetadata.FilePath = params.Path
			defaultMetadata.ReviewResponses = reviewResponses
			return nil, nil, &defaultMetadata
		}

		if len(formattedReviews.Reviews) == 0 {
			// same as responded with good result as "None"
			response.ErrorMessage = NoErrReviewComment
			reviewResponses = append(reviewResponses, response)
			defaultMetadata.FilePath = params.Path
			defaultMetadata.ReviewResponses = reviewResponses
			return &preMergedReviews, nil, &defaultMetadata
		}

		// save chat prompt id upon successfully parsed review structure
		for i, r := range formattedReviews.Reviews {
			// check if access type list matched
			lang := params.Language
			if lang == "" {
				lang, _ = enry.GetLanguageByExtension(params.Path)
			}

			if cusRulesOrigin == nil {
				// check access list if it's neither customized nor freestyle
				// only when safe is true which means only one lang(not first) returned
				accessConfig := c.CodeAIConfig.GetValue().CodeReview.AccessIssueTypeListByLang
				if len(accessConfig) != 0 {
					typeList := make([]string, 0)
					for _, cfg := range accessConfig {
						if strings.ToLower(lang) == cfg.Lang ||
							cfg.Lang == "all" {
							typeList = append(typeList, cfg.IssueTypes...)
						}
					}
					// check type is in config access type list
					if len(typeList) > 0 && !lo.Contains(typeList, r.IssueType) {
						formattedReviews.Reviews[i].IsTypeBlocked = lo.ToPtr(true)
						log.V1.CtxInfo(ctx,
							"type is not in access type list, type = %s, repo = %s, prompt_id = %d",
							r.IssueType, params.Path, chatPromptID)
					}
				}

				// check sensitive
				sensWordsConfig := c.StrictSensitiveWords.GetPointer()
				if sensWordsConfig != nil && len(c.StrictSensitiveWords.GetValue()) > 0 {
					sensWordList := strings.Split(string(c.StrictSensitiveWords.GetValue()), "\n")
					// check if current r contains sens word, set isSensFiltered to true is yes
					for _, word := range sensWordList {
						if strings.Contains(r.Issue, word) {
							formattedReviews.Reviews[i].IsSensBlocked = lo.ToPtr(true)
							log.V1.CtxWarn(ctx,
								"sensitive word found, word = %s, repo = %s, prompt_id = %d",
								word, params.Path, chatPromptID)
							break
						}
					}
				}
			}

			formattedReviews.Reviews[i].ChatPromptID = lo.ToPtr(chatPromptID)
			if cusRulesOrigin == nil {
				// 处理 nil 指针的情况
				formattedReviews.Reviews[i].Label = lo.ToPtr(po.Params.FunctionID)
			} else {
				// 根据指针指向的字符串内容进行比较
				switch *cusRulesOrigin {
				case "customized":
					formattedReviews.Reviews[i].Label = lo.ToPtr(CusFunctionIDPrefix + po.Params.FunctionID)
				case "freestyle":
					formattedReviews.Reviews[i].Label = lo.ToPtr(FreeStyleFunctionIDPrefix + po.Params.FunctionID)
				default:
					formattedReviews.Reviews[i].Label = lo.ToPtr(po.Params.FunctionID)
				}
			}
		}

		// valid formatted review, save review and corresponding metadata
		reviewResponses = append(reviewResponses, response)
		preMergedReviews.Reviews = append(preMergedReviews.Reviews, formattedReviews.Reviews...)
	}

	defaultMetadata.FilePath = params.Path
	defaultMetadata.ReviewResponses = reviewResponses

	return &preMergedReviews, updatedVariables, &defaultMetadata
}

func (c *CodebaseAgent) getMergedReviewPerTask(ctx context.Context, po *entity.AsyncTask, localParams map[string]any,
	intentContext skillentity.Context, preMergedReviews *llmResponseReviews,
	rMetadata *AIFileReviewMetadata) (*llmResponseReviews, *AIFileReviewMetadata, error) {
	startTime := time.Now()

	if preMergedReviews == nil {
		c.failAIFileReviewTask(ctx, po.ID, "",
			lo.ToPtr(ErrInvalidParameters.Error()), nil)
		log.V1.CtxError(ctx, "no valid pre-merged reviews to be processed")
		return nil, rMetadata, ErrInvalidParameters
	}

	if po.Params == nil || po.Params.Params == nil {
		c.failAIFileReviewTask(ctx, po.ID, "",
			lo.ToPtr(ErrInvalidParameters.Error()), nil)
		log.V1.CtxWarn(ctx, "task has no params")
		return nil, rMetadata, ErrInvalidParameters
	}

	// update ctx
	ctx = context.WithValue(ctx, consts.LOGIDKEY, po.Params.LogID)

	params, err := getOptionFromVars[service.AIFileReviewInvokeParams](po.Params.Params)
	if err != nil {
		c.failAIFileReviewTask(ctx, po.ID, params.Path,
			lo.ToPtr(ErrInvalidParameters.Error()), nil)
		log.V1.CtxWarn(ctx, "failed to get context variables, err = %v", err)
		return nil, rMetadata, err
	}

	metadata, err := SerializeMetadata(ctx, lo.FromPtr(rMetadata))
	if err != nil {
		c.failAIFileReviewTask(ctx, po.ID, params.Path,
			lo.ToPtr(ErrInvalidParameters.Error()), nil)
		log.V1.CtxError(ctx, "serial metadata failed, %v", err)
		return nil, rMetadata, err
	}

	// 3.4 set vars and merge parallel recall
	unblockedReviews := make([]Review, 0)
	for _, r := range preMergedReviews.Reviews {
		if !(r.IsTypeBlocked != nil && lo.FromPtr(r.IsTypeBlocked)) &&
			!(r.IsSensBlocked != nil && lo.FromPtr(r.IsSensBlocked)) {
			// if is_type_blocked is assigned and true, then it is blocked, otherwise append it for next
			// same as sens
			unblockedReviews = append(unblockedReviews, r)
		}
	}
	setKVForMergeReviews(ctx, unblockedReviews, intentContext.Variables)

	var mergedReviews llmResponseReviews
	if len(unblockedReviews) > 1 {
		// need merge if reviews are more than 1
		llmResponse, err := c.ChatService.Invoke(ctx,
			chatservice.InvokeOption{
				AppID:      po.AppID,
				FunctionID: MergeReviewsFunctionID,
				Parameters: localParams,
				Context:    &intentContext,
				Stream:     false,
				Account: &authentity.Account{
					Username:          "BitsAI",
					CodebaseBitsAIJWT: c.BitsAISecret,
				},
				SessionID: po.Params.SessionID,
			})
		if err != nil {
			log.V1.CtxError(ctx, "invoke merge reviews failed: %v", err)
			c.failAIFileReviewTask(ctx, po.ID, params.Path, lo.ToPtr(ErrChatLLMFailed.Error()+
				".Internal Err:"+err.Error()), metadata)
			return nil, rMetadata, ErrChatLLMFailed
		}

		// update step latency record by append new value to existing
		rMetadata.StepLatencyList = append(rMetadata.StepLatencyList, entity.StepLatencyRecord{
			StepID:  "invoke_merge_review",
			TaskID:  po.ID,
			Latency: time.Since(startTime).Milliseconds(),
		})

		if llmResponse == nil || llmResponse.Outputs == nil {
			log.V1.CtxError(ctx, "invoke merge review failed due to empty llmResponse or output")
			c.failAIFileReviewTask(ctx, po.ID, params.Path, lo.ToPtr(ErrChatLLMFailed.Error()+
				".Internal Err: empty llmResponse or output of merged reviews"), metadata)
			return nil, rMetadata, ErrChatLLMFailed
		}

		chatPromptID := conv.Int64Default(llmResponse.Outputs["prompt_completion_id"], -1)
		// update the metadata - add merge prompt ID corresponding to model id
		rMetadata.MergePromptID = lo.ToPtr(chatPromptID)
		metadata, err = SerializeMetadata(ctx, lo.FromPtr(rMetadata))
		if err != nil {
			c.failAIFileReviewTask(ctx, po.ID, params.Path,
				lo.ToPtr(ErrInvalidParameters.Error()), nil)
			log.V1.CtxError(ctx, "serial metadata failed, %v", err)
			return nil, rMetadata, err
		}

		// extract the result
		result, exist := llmResponse.Outputs["result"]
		if !exist {
			log.V1.CtxError(ctx, "failed to get result key of llm response outputs of merged reviews")
			c.failAIFileReviewTask(ctx, po.ID, params.Path,
				lo.ToPtr(ErrParseLLMResponseFailed.Error()), metadata)
			return nil, rMetadata, ErrParseLLMResponseFailed
		}

		reviewComments := result.(string)
		if reviewComments != "" {
			text := strings.ReplaceAll(reviewComments, "：", ":")
			if strings.Contains(text, "None") ||
				(!strings.Contains(text, "{") && strings.Contains(text, "代码")) {
				// rough judgement on llm response if good case by check log
				c.finishAIFileReviewTask(ctx, po.ID, lo.ToPtr(NoErrReviewComment), metadata)
				return nil, rMetadata, nil
			}

			// 3.5 pre-check again if llm response is with correct format.
			err = json.Unmarshal([]byte(reviewComments), &mergedReviews)
			if err != nil {
				log.V1.CtxError(ctx, fmt.Sprintf("failed to parse llm response: %v, data = %s",
					err, reviewComments))
				c.failAIFileReviewTask(ctx, po.ID, params.Path,
					lo.ToPtr(ErrParseLLMResponseFailed.Error()), metadata)
				return nil, rMetadata, err
			}
		}

		// 3.6 de-duplicate by line
		var dedupReviews []Review
		if len(mergedReviews.Reviews) > 1 {
			dedupReviews = make([]Review, 0)
			reviewsByLines := make(map[int32]Review)
			for _, mr := range mergedReviews.Reviews {
				if _, lineExist := reviewsByLines[mr.Line.getValue()]; lineExist && mr.Version == "new" {
					curIssue := reviewsByLines[mr.Line.getValue()].Issue
					curSug := reviewsByLines[mr.Line.getValue()].Suggestions
					reviewsByLines[mr.Line.getValue()] = Review{
						Line: Line{
							IntVal:   int(mr.Line.getValue()),
							IsString: false,
						},
						Version:     mr.Version,
						Issue:       mr.Issue + "\n" + curIssue,
						Suggestions: mr.Suggestions + "\n" + curSug,
					}
				} else if !lineExist {
					reviewsByLines[mr.Line.getValue()] = mr
				}
			}

			for _, mr := range reviewsByLines {
				dedupReviews = append(dedupReviews, mr)
			}
		} else {
			dedupReviews = mergedReviews.Reviews
		}

		// save mergedReviews apart from original reviews
		mergedReviews.Reviews = preMergedReviews.Reviews
		mergedReviews.MergedReviews = dedupReviews
	} else {
		// no need merge, direct assign original unblocked reviews to merged review list
		mergedReviews = llmResponseReviews{
			Reviews:       preMergedReviews.Reviews,
			MergedReviews: unblockedReviews,
		}
	}
	return &mergedReviews, rMetadata, nil
}

func (c *CodebaseAgent) filteredReviewPerTask(ctx context.Context, po *entity.AsyncTask, localParams map[string]any,
	intentContext skillentity.Context, mergedReviews *llmResponseReviews,
	rMetadata *AIFileReviewMetadata) ([]Review, error) {
	startTime := time.Now()
	if mergedReviews == nil {
		c.failAIFileReviewTask(ctx, po.ID, "",
			lo.ToPtr(ErrInvalidParameters.Error()), nil)
		log.V1.CtxError(ctx, "no valid pre-merged reviews to be processed")
		return nil, ErrInvalidParameters
	}

	if po.Params == nil || po.Params.Params == nil {
		c.failAIFileReviewTask(ctx, po.ID, "",
			lo.ToPtr(ErrInvalidParameters.Error()), nil)
		log.V1.CtxWarn(ctx, "task has no params")
		return nil, ErrInvalidParameters
	}

	// update ctx
	ctx = context.WithValue(ctx, "K_LOGID", po.Params.LogID)

	params, err := getOptionFromVars[service.AIFileReviewInvokeParams](po.Params.Params)
	if err != nil {
		c.failAIFileReviewTask(ctx, po.ID, params.Path,
			lo.ToPtr(ErrInvalidParameters.Error()), nil)
		log.V1.CtxWarn(ctx, "failed to get context variables, err = %v", err)
		return nil, err
	}

	metadata, err := SerializeMetadata(ctx, lo.FromPtr(rMetadata))
	if err != nil {
		c.failAIFileReviewTask(ctx, po.ID, params.Path,
			lo.ToPtr(ErrInvalidParameters.Error()), nil)
		log.V1.CtxError(ctx, "serial metadata failed, %v", err)
		return nil, err
	}

	if len(mergedReviews.MergedReviews) == 0 {
		// same as responded with good result as "None"
		c.finishAIFileReviewTask(ctx, po.ID, lo.ToPtr(NoErrReviewComment), metadata)
		return nil, nil
	}

	// 4.1 dispatch filter request for each comment.
	postProcessedReviews := make([]Review, 0)
	var (
		wgFilter sync.WaitGroup
		muFilter sync.Mutex
	)

	for i, r := range mergedReviews.MergedReviews {
		if strings.HasPrefix(lo.FromPtr(r.Label), CusFunctionIDPrefix) {
			// if reviews is based on customized review rules, then append, won't be filtered, and continue
			r.IsFiltered = lo.ToPtr(false)
			postProcessedReviews = append(postProcessedReviews, r)
			log.V1.CtxInfo(ctx, "customized review from ID %d, index %d is skipped filtering process", po.ID, i)
			continue
		}
		if strings.Contains(strings.ToLower(r.Version), "old") {
			log.V1.CtxInfo(ctx, "ai file review filtered out review comment for old code")
			continue
		}
		i := i
		r := r
		wgFilter.Add(1)
		go func(ctx context.Context, r Review, intentContext skillentity.Context, parameters map[string]any, curIdx int) {
			defer wgFilter.Done()
			defer func() {
				if err := recover(); err != nil {
					// log the error and possibly handle it, rather than aborting the program
					log.V1.CtxInfo(ctx, "recovered from panic")
				}
			}()
			// append comment and line info for filter.
			filterContext := duplicateContext(intentContext)
			filterParams := duplicateParams(parameters)
			setKVForFilter(ctx, r, filterContext.Variables)

			log.V1.CtxInfo(ctx, "session ID %s with ppe value: %s", po.Params.SessionID, ctx.Value("x-use-ppe"))
			filterResponse, err := c.ChatService.Invoke(ctx, chatservice.InvokeOption{
				AppID:      po.AppID,
				FunctionID: FilterFunctionID,
				Parameters: filterParams,
				Context:    &filterContext,
				Stream:     false,
				Account: &authentity.Account{
					Username:          "BitsAI",
					CodebaseBitsAIJWT: c.BitsAISecret,
				},
				SessionID: po.Params.SessionID,
			})
			if err != nil {
				log.V1.CtxError(ctx, "filter invoke failed: %v", err)
				return
			}

			rMetadata.StepLatencyList = append(rMetadata.StepLatencyList, entity.StepLatencyRecord{
				TaskID:  po.ID,
				StepID:  "invoke_filter_#_" + strconv.Itoa(curIdx),
				Latency: time.Since(startTime).Milliseconds(),
			})

			r.FilterPromptID = lo.ToPtr(conv.Int64Default(filterResponse.Outputs["prompt_completion_id"], -1))
			filterResult, ok := filterResponse.Outputs["result"]
			if ok {
				if !strings.Contains(strings.ToLower(filterResult.(string)), FilterPatternOutputNo) {
					r.IsFiltered = lo.ToPtr(false)
				} else {
					r.IsFiltered = lo.ToPtr(true)
				}
				muFilter.Lock() // Lock the mutex before accessing the slice
				postProcessedReviews = append(postProcessedReviews, r)
				muFilter.Unlock()
			}
		}(ctx, r, intentContext, localParams, i)
	}

	wgFilter.Wait()

	if rMetadata != nil {
		err = c.UpdateMetadata(ctx, po.ID, *rMetadata)
		if err != nil {
			log.V1.CtxError(ctx, "failed to update metadata, err = %v", err)
		}
	}

	return postProcessedReviews, nil
}

func matchesAnyPattern(path string, patterns []glob.Glob) bool {
	for _, pattern := range patterns {
		if pattern.Match(path) {
			return true
		}
	}
	return false
}

func hasAccess(path string, patterns []string) bool {
	var accessPatterns, blockPatterns []glob.Glob

	for _, pattern := range patterns {
		if pattern[0] == '!' {
			blockPatterns = append(blockPatterns, glob.MustCompile(pattern[1:]))
		} else {
			accessPatterns = append(accessPatterns, glob.MustCompile(pattern))
		}
	}

	if matchesAnyPattern(path, blockPatterns) {
		return false
	}
	if matchesAnyPattern(path, accessPatterns) {
		return true
	}

	return false
}

func (c *CodebaseAgent) getFreeStyleSettings(ctx context.Context, repoName string, lang string, localParams map[string]any) (map[string]any, bool) {
	if c.FreeStyleRulesConfig != nil {
		freeStyleConfig := c.FreeStyleRulesConfig.GetPointer()
		if !freeStyleConfig.Enable {
			log.V1.CtxInfo(ctx, CusLogPrefix+"free style rules not enabled")
			return nil, false
		}
		if freeStyleConfig != nil {
			repoAccess := false
			langAccess := false
			if lo.Contains(freeStyleConfig.AccessRepoNames, repoName) {
				repoAccess = true
				log.V1.CtxInfo(ctx, CusLogPrefix+"free style rules for repo %s", repoName)
			} else if lo.Contains(freeStyleConfig.AccessLangs, "ALL") ||
				lo.Contains(freeStyleConfig.AccessLangs, lang) {
				langAccess = true
				log.V1.CtxInfo(ctx, CusLogPrefix+"free style rules for lang %s, repo %s", lang, repoName)
			}
			if repoAccess || langAccess {
				freeStyledRules := make([]llmreviewer.CustomizedRule, 0)
				for _, rulesByLang := range freeStyleConfig.RulesConfig {
					if rulesByLang.LangID == lang || rulesByLang.LangID == "all" {
						log.V1.CtxInfo(ctx, CusLogPrefix+"free style rules for repo %s, lang %s", repoName, lang)
						for _, rule := range rulesByLang.Rules {
							freeStyledRules = append(freeStyledRules, llmreviewer.CustomizedRule{
								RuleName:      rule.RuleName,
								DescriptionZH: rule.DescriptionZH,
								PE:            rule.PE,
								COT:           rule.COT,
							})
						}
					}
				}
				if len(freeStyledRules) > 0 {
					customizedLocalParams := duplicateParams(localParams)
					customizedLocalParams[llmreviewer.KeyCustomizedReviews] = freeStyledRules
					customizedLocalParams[llmreviewer.KeyCustomizedSource] = "freestyle"
					return customizedLocalParams, true
				}
			}
		}
	}
	return nil, false
}

func (c *CodebaseAgent) getRepoReviewsSettings(ctx context.Context, repoName, path, lang string, localParams map[string]any) (map[string]any, bool) {
	// check if there are reviews rules settings for this repo
	reviewsRules, err := c.ReviewsSettings.GetAllCachedRulesByRepo(ctx, repoName, DefaultRevision)
	if err != nil {
		log.V1.CtxError(ctx, CusLogPrefix+"failed to get repo rules, err = %v", err)
		return nil, false
	}
	// if rules set, call getPreRecallPerTask for extra once in parallel in advance after settings rules in
	// context
	if len(reviewsRules.RepoReviewRules) == 0 && len(reviewsRules.PathReviewRules) == 0 {
		log.V1.CtxInfo(ctx, CusLogPrefix+"no rules set for repo %s", repoName)
		return nil, false
	}

	log.V1.CtxInfo(ctx, CusLogPrefix+"start to invoke llm reviewer with customized rules")

	// check rules setting for overall repo scope:
	customizedRules := make([]llmreviewer.CustomizedRule, 0)
	for _, rule := range reviewsRules.RepoReviewRules {
		customizedRules = append(customizedRules, llmreviewer.CustomizedRule{
			RuleName:       rule.Title,
			RuleID:         rule.Title,
			TitleZH:        rule.Title,
			DescriptionZH:  rule.Description,
			CodingLanguage: lang,
			Level:          rule.Level,
			ExamplesGood:   strings.Join(rule.PositiveExamples, "\n"),
			ExamplesBad:    strings.Join(rule.NegativeExamples, "\n"),
			PE: fmt.Sprintf("规范简介:%s\n正例:\n%s\n反例:\n%s",
				rule.RuleDefine, rule.GoodCase, rule.BadCase),
			COT: rule.ChainOfThought,
		})
	}

	// check rules setting matched with input path
	for _, pathAppliedRules := range reviewsRules.PathReviewRules {
		if hasAccess(path, pathAppliedRules.GlobPath) {
			for _, r := range pathAppliedRules.Rules {
				customizedRules = append(customizedRules, llmreviewer.CustomizedRule{
					RuleName:       r.Title,
					RuleID:         r.Title,
					TitleZH:        r.Title,
					DescriptionZH:  r.Description,
					CodingLanguage: lang,
					Level:          r.Level,
					ExamplesGood:   strings.Join(r.PositiveExamples, "\n"),
					ExamplesBad:    strings.Join(r.NegativeExamples, "\n"),
					PE: fmt.Sprintf("规范简介:%s\n正例:\n%s\n反例:\n%s",
						r.RuleDefine, r.GoodCase, r.BadCase),
					COT: r.ChainOfThought,
				})
			}
		}
	}

	customizedLocalParams := duplicateParams(localParams)
	customizedLocalParams[llmreviewer.KeyCustomizedReviews] = customizedRules
	customizedLocalParams[llmreviewer.KeyCustomizedSource] = "customized"
	return customizedLocalParams, true
}

func mergeMetadata(a *AIFileReviewMetadata, b *AIFileReviewMetadata) *AIFileReviewMetadata {
	if a == nil {
		if b != nil {
			return b
		}
	}
	if b == nil {
		if a != nil {
			return a
		}
	}
	mergedMetadata := AIFileReviewMetadata{
		CommentCount:       a.CommentCount + b.CommentCount,
		ThreadIDs:          append(a.ThreadIDs, b.ThreadIDs...),
		ReviewResponses:    append(a.ReviewResponses, b.ReviewResponses...),
		PromptID:           a.PromptID,
		PromptIDCustomized: b.PromptID,
		StepLatencyList:    append(a.StepLatencyList, b.StepLatencyList...),
	}
	return &mergedMetadata
}

func mergeVariables(general map[string]any, custom map[string]any) map[string]any {
	if general == nil {
		if custom != nil {
			return custom
		}
	}
	if custom == nil {
		if general != nil {
			return general
		}
	}
	mergedVariables := make(map[string]any)
	for k, v := range custom {
		mergedVariables[k] = v
	}
	// general can override custom since it is main path
	for k, v := range general {
		mergedVariables[k] = v
	}
	return mergedVariables
}

func (c *CodebaseAgent) startLLMReviewPipeline(ctx context.Context, chatTaskListPOs []*entity.AsyncTask) {
	// identify task and parse params
	var (
		wg sync.WaitGroup
	)
	for _, po := range chatTaskListPOs {
		if po == nil {
			continue
		}
		wg.Add(1)

		po := po
		go func(po *entity.AsyncTask) {
			defer wg.Done()
			if po.Params == nil || po.Params.Params == nil {
				log.V1.CtxWarn(ctx, "task has no params")
				c.failAIFileReviewTask(ctx, po.ID, "",
					lo.ToPtr(ErrInvalidParameters.Error()), nil)
				return
			}

			if po.AppID == "" || po.Params.SessionID == "" {
				log.V1.CtxWarn(ctx, "task's appID or functionID or sessionID not set")
				c.failAIFileReviewTask(ctx, po.ID, "",
					lo.ToPtr(ErrInvalidParameters.Error()), nil)
				return
			}

			// 3.1 confirm in advance if params correct.
			params, err := getOptionFromVars[service.AIFileReviewInvokeParams](po.Params.Params)
			if err != nil {
				log.V1.CtxWarn(ctx, "failed to get context variables, err = %v", err)
				c.failAIFileReviewTask(ctx, po.ID, params.Path,
					lo.ToPtr(ErrInvalidParameters.Error()), nil)
				return
			}

			if params == nil {
				log.V1.CtxWarn(ctx, "failed to get context variables, params is nil")
				c.failAIFileReviewTask(ctx, po.ID, "",
					lo.ToPtr(ErrInvalidParameters.Error()), nil)
				return
			}

			// create new and update ctx
			subCtx := context.WithValue(ctx, "K_LOGID", po.Params.LogID)
			log.V1.CtxInfo(ctx, "updated context with logID in AI Review Recall")

			// 3.2 check if file is in review-scope.
			exclude, reason := c.shouldExclude(subCtx, service.FileContext{
				Path: params.Path,
			})
			if exclude {
				// filter out src code.
				c.finishAIFileReviewTask(subCtx, po.ID, lo.ToPtr(NoErrFilteredFile+":"+reason), nil)
				return
			}

			// 3.3 getPreRecall
			// write current po taskID to context, for downstream service acknowledge the relations
			localParams := duplicateParams(po.Params.Params)
			localParams[llmreviewer.KeyTaskID] = strconv.FormatUint(po.ID, 10)
			intentContext := skillentity.Context{
				Variables: localParams,
			}

			// all programming lang will end on using one-stand llm reviewer
			// check customized review
			var (
				cusReviews   *llmResponseReviews
				cusMetadata  *AIFileReviewMetadata
				cusVariables map[string]any
			)
			localParamsCustomized, needed :=
				c.getRepoReviewsSettings(subCtx, params.RepoName, params.Path, params.Language, localParams)
			wgCustomized := sync.WaitGroup{}
			// 3.4 set vars for customized and call in parallel
			if needed {
				log.V1.CtxInfo(ctx, CusLogPrefix+"start to invoke customized review for repo %s", params.RepoName)
				wgCustomized.Add(1)
				go func() {
					defer wgCustomized.Done()
					// recover
					defer func() {
						if err := recover(); err != nil {
							log.V1.CtxError(subCtx, CusLogPrefix+"panic in customized review, err = %v", err)
						}
					}()

					customizedIntentContext := skillentity.Context{
						Variables: localParamsCustomized,
					}
					// use customized review and no need to override params
					cusReviews, cusVariables, cusMetadata =
						c.getPreRecallPerTask(subCtx, po, localParamsCustomized, customizedIntentContext, lo.ToPtr("customized"))
					if cusReviews == nil {
						log.V1.CtxError(subCtx, CusLogPrefix+"failed to get customized review")
					}
				}()
			}

			// freestyle
			var (
				FSReviews   *llmResponseReviews
				FSMetadata  *AIFileReviewMetadata
				FSVariables map[string]any
			)
			localParamsFreeStyle, needed :=
				c.getFreeStyleSettings(subCtx, params.RepoName, params.Language, localParams)
			if needed {
				log.V1.CtxInfo(ctx, FreeStylePrefix+"start to invoke freestyle review for repo %s", params.RepoName)
				wgCustomized.Add(1)
				go func() {
					defer wgCustomized.Done()
					// recover
					defer func() {
						if err := recover(); err != nil {
							log.V1.CtxError(subCtx, FreeStylePrefix+"panic in freestyle review, err = %v", err)
						}
					}()
					freeStyleIntentContext := skillentity.Context{
						Variables: localParamsFreeStyle,
					}
					// use customized review and no need to override params
					FSReviews, FSVariables, FSMetadata =
						c.getPreRecallPerTask(subCtx, po, localParamsFreeStyle, freeStyleIntentContext, lo.ToPtr("freestyle"))
					if FSReviews == nil {
						log.V1.CtxError(subCtx, CusLogPrefix+"failed to get customized review")
					}
				}()
			}

			preMergedReviews, updatedVariables, rMetadata :=
				c.getPreRecallPerTask(subCtx, po, localParams, intentContext, nil)
			wgCustomized.Wait()

			if preMergedReviews == nil {
				log.V1.CtxError(subCtx, CusLogPrefix+"failed to get pre recall")
				metadata, err := SerializeMetadata(subCtx, lo.FromPtr(rMetadata))
				if err != nil {
					c.failAIFileReviewTask(subCtx, po.ID, params.Path,
						lo.ToPtr(ErrInvalidParameters.Error()), nil)
					log.V1.CtxError(subCtx, CusLogPrefix+"serial metadata failed, %v", err)
					return
				}
				c.failAIFileReviewTask(subCtx, po.ID, params.Path,
					lo.ToPtr(ErrInvalidParameters.Error()), metadata)
				return
			}

			// merge customized review with general pre recall
			if cusReviews != nil && len(cusReviews.Reviews) > 0 {
				log.V1.CtxInfo(ctx, CusLogPrefix+"customized review found, merge with pre recall, reviews = %v",
					cusReviews.Reviews)
				preMergedReviews.Reviews = append(preMergedReviews.Reviews,
					cusReviews.Reviews...)
			} else if cusReviews != nil && len(cusReviews.Reviews) == 0 {
				log.V1.CtxInfo(ctx, CusLogPrefix+"no customized review found, use pre recall")
			}
			if cusMetadata != nil {
				rMetadata = mergeMetadata(rMetadata, cusMetadata)
			}
			if cusVariables != nil {
				updatedVariables = mergeVariables(updatedVariables, cusVariables)
			}

			// merge freestyle review with general pre recall
			if FSReviews != nil && len(FSReviews.Reviews) > 0 {
				log.V1.CtxInfo(ctx, FreeStylePrefix+"freestyle review found, merge with pre recall, reviews = %v",
					FSReviews.Reviews)
				preMergedReviews.Reviews = append(preMergedReviews.Reviews,
					FSReviews.Reviews...)
			} else if FSReviews != nil && len(FSReviews.Reviews) == 0 {
				log.V1.CtxInfo(ctx, FreeStylePrefix+"no freestyle review found, use pre recall")
			}
			if FSMetadata != nil {
				rMetadata = mergeMetadata(rMetadata, FSMetadata)
			}
			if FSVariables != nil {
				updatedVariables = mergeVariables(updatedVariables, FSVariables)
			}

			// 3.4 set vars and merge parallel recall
			intentContext.Variables = duplicateParams(updatedVariables)
			mergedReviews, rMetadata, err := c.getMergedReviewPerTask(subCtx, po, updatedVariables, intentContext, preMergedReviews, rMetadata)
			if err != nil {
				log.V1.CtxError(subCtx, "get merged review failed, %v", err)
				// all DAO update is handled inside sub-function
				return
			}

			// update response upon processing merged reviews finished
			reviewCommentsByte, err := json.Marshal(mergedReviews)
			if err != nil {
				log.V1.CtxError(subCtx, "marshal merged reviews failed: %v", err)
				c.failAIFileReviewTask(subCtx, po.ID, params.Path,
					lo.ToPtr(ErrParseLLMResponseFailed.Error()), nil)
				return
			}
			log.V1.CtxInfo(subCtx, "update review comments into merged review comments")
			reviewComments := string(reviewCommentsByte)
			_, err = c.UpdateStatusUponLLMRespond(subCtx, service.UpdateTaskOption{
				TaskID:      po.ID,
				LLMResponse: lo.ToPtr(reviewComments),
				Status:      lo.ToPtr(entity.StatusV2CodebaseAIFileReviewThinking.String()),
			})
			if err != nil {
				log.V1.CtxError(subCtx, "failed to update status upon LLM respond")
				c.failAIFileReviewTask(subCtx, po.ID, params.Path,
					lo.ToPtr(ErrInternalError.Error()), nil)
				return
			}

			// 4 process merged reviews to be filtered
			postProcessedReviews, err := c.filteredReviewPerTask(subCtx, po, updatedVariables, intentContext, mergedReviews, rMetadata)
			if err != nil {
				log.V1.CtxError(subCtx, "filter reviews failed, %v", err)
				c.failAIFileReviewTask(subCtx, po.ID, params.Path,
					lo.ToPtr(ErrInternalError.Error()), nil)
				return
			}
			if len(postProcessedReviews) == 0 {
				log.V1.CtxInfo(subCtx, "filtered with no reviews left")
				return
			}
			reviewCommentsByte, err = json.Marshal(llmResponseReviews{
				Reviews:       mergedReviews.Reviews,
				MergedReviews: postProcessedReviews,
			})
			if err != nil {
				log.V1.CtxError(subCtx, "marshal filtered comments failed: %v", err)
				c.failAIFileReviewTask(subCtx, po.ID, params.Path,
					lo.ToPtr(ErrParseLLMResponseFailed.Error()), nil)
				return
			}
			log.V1.CtxInfo(subCtx, "update review comments into filtered comments")
			reviewComments = string(reviewCommentsByte)

			// 5. store llm response and update params
			_, err = c.UpdateStatusUponLLMRespond(subCtx, service.UpdateTaskOption{
				TaskID:      po.ID,
				LLMResponse: lo.ToPtr(reviewComments),
			})
			if err != nil {
				log.V1.CtxError(subCtx, "failed to update status upon LLM respond")
				c.failAIFileReviewTask(subCtx, po.ID, params.Path,
					lo.ToPtr(ErrInternalError.Error()), nil)
				return
			}
		}(po)
	}
	wg.Wait()
}

func (c *CodebaseAgent) startAIReviewLLMChat() error {
	const (
		LLMChattedTaskValue = "chatted"
	)
	var (
		strReady                    = entity.StatusV2CodebaseAIFileReviewReady.String()
		TaskTimeOutDur              = DefaultNotStartedTimeoutDurationMin * time.Minute
		processAIReviewChatInterval = DefaultProcessAIReviewChatIntervalMilli * time.Millisecond
	)

	if c.CodeAIConfig.GetValue().GlobalENV.NotStartedTimeoutDurationMin != 0 {
		TaskTimeOutDur = time.Duration(c.CodeAIConfig.GetValue().GlobalENV.NotStartedTimeoutDurationMin) * time.Minute
	}
	if c.CodeAIConfig.GetValue().GlobalENV.ProcessAIReviewChatIntervalMilli != 0 {
		processAIReviewChatInterval =
			time.Duration(c.CodeAIConfig.GetValue().GlobalENV.ProcessAIReviewChatIntervalMilli) * time.Millisecond
	}

	go func() {
		ticker := time.NewTicker(processAIReviewChatInterval)
		ctx := context.Background()
		defer ticker.Stop()
		defer func() {
			if err := recover(); err != nil {
				// log the error and possibly handle it, rather than aborting the program
				log.V1.CtxError(ctx, "recovered startAIReviewLLMChat from panic")
			}
		}()
		for range ticker.C {
			// ----- AI File Review Case
			chatTaskListPOs := make([]*entity.AsyncTask, 0)
			for page := 1; page <= c.CodeAIConfig.GetValue().GlobalENV.MaxQueryPage; page++ {
				listedPOs, err := c.DAO.ListAsyncTasks(ctx, dal.ListAsyncTaskOption{
					Status:       lo.ToPtr(strReady),
					Count:        c.CodeAIConfig.GetValue().GlobalENV.PerPageLimit,
					Offset:       (page - 1) * c.CodeAIConfig.GetValue().GlobalENV.PerPageLimit,
					SinceMinutes: lo.ToPtr(TaskTimeOutDur),
				})
				if err != nil {
					log.V1.CtxError(ctx, "list tasks with status: %s failed", strReady)
					continue
				}

				// 2. update status one-by-one and only process those affected.
				for _, po := range listedPOs {
					if po.Status != strReady {
						continue
					}
					// check if po.ID task has started
					sID := strconv.FormatUint(po.ID, 10) + strReady
					taskValue, err := c.RedisClient.Get(ctx, sID)
					if err != nil {
						if errors.Is(err, redis2.Nil) {
							// not found, and ignore potential error to avoid break the main pipeline.
							log.V1.CtxInfo(ctx, "not found for key %s: %v", sID, err)
							err2 := c.RedisClient.Set(ctx, sID, LLMChattedTaskValue, AIReviewTaskTTL)
							if err2 != nil {
								log.V1.CtxError(ctx, "failed to set sID %s chat status to redis: %v", sID, err)
								continue
							}
						} else {
							log.V1.CtxError(ctx, "failed to get sID %s chat status from redis: %v", sID, err)
							continue
						}
					} else {
						if taskValue == LLMChattedTaskValue {
							// created llm chat task.
							log.V1.CtxInfo(ctx, "duplicated task for sID: %s", sID)
							continue
						}
					}
					toChatTask, err := c.DAO.UpdateAsyncStatus(ctx, dal.UpdateAsyncTaskStatusOption{
						ID:     po.ID,
						Status: lo.ToPtr(entity.StatusV2CodebaseAIFileReviewThinking.String()),
					})
					if err != nil {
						// not affected, recheck status to avoid a "thinking" forever status
						if toChatTask != nil && toChatTask.Status == entity.StatusV2CodebaseAIFileReviewReady.String() {
							// not changed, and won't execute llm chat.
							continue
						}
					}
					chatTaskListPOs = append(chatTaskListPOs, toChatTask)
					if len(chatTaskListPOs) >= c.CodeAIConfig.GetValue().GlobalENV.AIFileReviewInstanceQuota {
						log.V1.CtxInfo(ctx, "reach to ai review instance max processing task, break")
						break
					}
				}
			}

			if len(chatTaskListPOs) != 0 {
				log.V1.CtxInfo(ctx, fmt.Sprintf("process %d tasks of ai file review ready", len(chatTaskListPOs)))
			}

			// start LLM review pipeline - pre, merge and filter
			c.startLLMReviewPipeline(ctx, chatTaskListPOs)
		}
	}()
	return nil
}

func (c *CodebaseAgent) startAIReviewComments() error {
	const (
		MaxEndColumn     = 512
		CreatedTaskValue = "created"

		ToDraft = true
	)

	var (
		ProcessGenCommentsInterval = DefaultProcessGenCommentsIntervalMilli * time.Millisecond
		TaskTimeOutDur             = DefaultNotFinishedTimeoutDurationMin * time.Minute
	)

	if c.CodeAIConfig.GetValue().GlobalENV.ProcessGenCommentsIntervalMilli != 0 {
		ProcessGenCommentsInterval = time.Duration(c.CodeAIConfig.GetValue().GlobalENV.ProcessGenCommentsIntervalMilli) * time.Millisecond
	}

	if c.CodeAIConfig.GetValue().GlobalENV.NotFinishedTimeoutDurationMin != 0 {
		TaskTimeOutDur = time.Duration(c.CodeAIConfig.GetValue().GlobalENV.NotFinishedTimeoutDurationMin) * time.Minute
	}

	go func() {
		ticker := time.NewTicker(ProcessGenCommentsInterval)
		ctx := context.Background()
		ctx = context.WithValue(ctx, "K_LOGID", logid.GenLogID())
		defer ticker.Stop()
		defer func() {
			if err := recover(); err != nil {
				// log the error and possibly handle it, rather than aborting the program
				log.V1.CtxError(ctx, "recovered startAIReviewLLMChat from panic")
			}
		}()

		for range ticker.C {
			// ----- AI File Review Case
			genTaskListPOs := make([]*entity.AsyncTask, 0)
			for page := 1; page <= c.CodeAIConfig.GetValue().GlobalENV.MaxQueryPage; page++ {
				offset := (page - 1) * c.CodeAIConfig.GetValue().GlobalENV.PerPageLimit
				limit := c.CodeAIConfig.GetValue().GlobalENV.PerPageLimit
				listedPOs, err := c.DAO.ListAsyncTasks(ctx,
					dal.ListAsyncTaskOption{
						Status:       lo.ToPtr(entity.StatusV2CodebaseAIFileReviewResponded.String()),
						Offset:       offset,
						Count:        limit,
						SinceMinutes: lo.ToPtr(TaskTimeOutDur),
					})

				if err != nil {
					log.V1.CtxError(ctx, "list tasks with status: %s failed",
						entity.StatusV2CodebaseAIFileReviewResponded.String())
					continue
				}

				// 2. update status one-by-one and only process those affected.
				for _, po := range listedPOs {
					// check if po.ID task has started
					sID := "comment" + strconv.FormatUint(po.ID, 10)
					taskValue, err := c.RedisClient.Get(ctx, sID)
					if err != nil {
						if errors.Is(err, redis2.Nil) {
							// not found, and ignore potential error to avoid break the main pipeline.
							err2 := c.RedisClient.Set(ctx, sID, CreatedTaskValue, AIReviewTaskTTL)
							if err2 != nil {
								log.V1.CtxError(ctx, "set %s comment in redis failed, err = %v", sID, err2)
								continue
							}
						} else {
							log.V1.CtxError(ctx, "get %s comment status in redis failed, err = %v", sID, err)
							continue
						}
					} else {
						if taskValue == CreatedTaskValue {
							// created gen comment task.
							continue
						}
					}
					toGenTask, err := c.DAO.UpdateAsyncStatus(ctx, dal.UpdateAsyncTaskStatusOption{
						ID:     po.ID,
						Status: lo.ToPtr(entity.StatusCodebaseAIFileReviewGenerating.String()),
					})
					if err != nil {
						log.V1.CtxError(ctx, "update task generating failed, %v", err)
						continue
					} else {
						genTaskListPOs = append(genTaskListPOs, toGenTask)
					}
				}
			}
			if len(genTaskListPOs) != 0 {
				log.V1.CtxInfo(ctx, fmt.Sprintf("process %d tasks of ai file review responded\n", len(genTaskListPOs)))
			}

			// 3. identify task and parse params
			for _, po := range genTaskListPOs {
				if po.Params == nil || po.Params.Params == nil {
					log.V1.CtxWarn(ctx, "task has no params")
					c.failAIFileReviewTask(ctx, po.ID, "",
						lo.ToPtr(ErrInvalidParameters.Error()), nil)
					continue
				}

				opt, err := getOptionFromVars[service.AIFileReviewInvokeParams](po.Params.Params)
				if err != nil {
					log.V1.CtxWarn(ctx, "failed to get context variables, err = %v", err)
					continue
				}

				if po.LLMResponse == "" {
					log.V1.CtxWarn(ctx, fmt.Sprintln("llm response empty string, po = ", *po))
					c.failAIFileReviewTask(ctx, po.ID, opt.Path,
						lo.ToPtr(ErrLLMReturnEmpty.Error()), nil)
					continue
				}

				text := strings.ReplaceAll(po.LLMResponse, "：", ":")
				processedCnt := 0
				excludedCnt := 0
				filteredCnt := 0
				similarFilteredCnt := 0

				threadIDs := make([]int64, 0)
				filteredThreadIDs := make([]int64, 0)
				var response llmResponseReviews
				err = json.Unmarshal([]byte(text), &response)
				if err != nil {
					log.V1.CtxWarn(ctx, fmt.Sprintf("parse llm response failed: %v, data = %s", err, text))
					c.failAIFileReviewTask(ctx, po.ID, opt.Path,
						lo.ToPtr(ErrParseLLMResponseFailed.Error()), nil)
					continue
				}
				if len(response.MergedReviews) == 0 {
					log.V1.CtxInfo(ctx, "empty review set, skip this task id = %d", po.ID)
					continue
				}

				for _, r := range response.MergedReviews {
					if r.IsFiltered != nil && *r.IsFiltered {
						filteredCnt++
						continue
					}

					// 4. Call codebase comment generate API
					var (
						side        = "head"
						PatchSetNum = 0
					)
					var leftPatchSetNum *int
					if opt.LeftPatchsetNum < 0 {
						side = "base"
					}

					line := r.Line.getValue()
					if line == -1 {
						log.V1.CtxError(ctx, "failed to parse review line number")
						continue
					}

					switch r.Version {
					case "old":
						// ignore old part
						excludedCnt++
					case "new":
						// right side:
						if !lo.Contains(opt.PostImpactLines, int(line)) {
							log.V1.CtxWarn(ctx, "AI File Review comment beyond postcode change scope")
							excludedCnt++
							continue
						}
						if opt.LeftPatchsetNum <= 0 {
							// zero as left patchset number is not permitted, consider as base and treat same as -1
							// if base, no leftPatchsetNum(nil), patchset_num = right_patchset_num
							leftPatchSetNum = nil
						} else {
							leftPatchSetNum = lo.ToPtr(opt.LeftPatchsetNum)
						}
						side = "head"
						PatchSetNum = opt.RightPatchsetNum
					default:
						log.V1.CtxError(ctx, "change marker not recognized")
						continue
					}

					realStartLine := line
					realEndLine := line
					if r.StartLine != nil && r.EndLine != nil {
						startLine := r.StartLine.getValue()
						endLine := r.EndLine.getValue()
						if startLine != endLine && startLine < endLine && startLine >= 0 && endLine >= 0 {
							// log this change
							realStartLine = startLine
							realEndLine = endLine
						}
					}
					log.V1.CtxInfo(ctx, "AI File Review comment with line range, realStartLine = %v, realEndLine = %v", realStartLine, realEndLine)

					commentOnly, AIReviewComment := c.renderComment(r, opt.Locale, "", "", false)
					fixCode, fixExplain, start, end := c.aiFixComment(ctx, r, *opt)
					// cr全部接入fix,ca规则使用纯suggestion
					originLine := r.Line.getValue()
					if len(fixCode) > 0 && start <= int(originLine) && end >= int(originLine) && end-start <= 5 {
						commentOnly, AIReviewComment = c.renderComment(r, opt.Locale, fixCode, fixExplain, !lo.Contains(c.CodeAIConfig.GetValue().AIFix.EnableRules, r.IssueType))
						realStartLine = int32(start)
						realEndLine = int32(end)
						log.V1.CtxInfo(ctx, "cr_ai_fix_comment %s, start %d, end %d", AIReviewComment, start, end)
					}

					if AIReviewComment == "" {
						log.V1.CtxError(ctx, "failed to render comment")
						continue
					}
					// 3.3 check similarity with past comments without suggestions
					if c.CodeAIConfig.GetValue().GlobalENV.EnableSimilarCommentsDetect &&
						!strings.HasPrefix(lo.FromPtr(r.Label), CusFunctionIDPrefix) {
						log.V1.CtxInfo(ctx, "similar comment check enabled for non-customized request")
						if c.SimilarCommentExists(ctx, opt.RepoName, int64(opt.ChangeID), commentOnly, &CommentLocationOption{
							path:  opt.Path,
							start: realStartLine,
							end:   realEndLine,
						}, nil) {
							log.V1.CtxInfo(ctx,
								"similar comment exists, skip this task id = %d, "+
									"threshold = %f", po.ID,
								c.CodeAIConfig.GetValue().CodeReview.SimilarCommentsThreshold)
							similarFilteredCnt++
							continue
						}
					}

					// 4. create comment
					codebaseComment := codebase.CreateCommentsOption{
						Content:          AIReviewComment,
						LeftSHA:          &opt.TargetRev,
						RightSHA:         &opt.SourceRev,
						PatchSetNum:      PatchSetNum,
						LeftPatchSetNum:  leftPatchSetNum,
						RightPatchSetNum: lo.ToPtr(opt.RightPatchsetNum),
						Side:             side,
						Path:             opt.Path,
						StartColumn:      0,
						EndColumn:        MaxEndColumn,
						StartLine:        realStartLine,
						EndLine:          realEndLine,
						Draft:            ToDraft,
					}

					comment, err := c.CodebaseCli.CreateComment(
						ctx, opt.RepoName, int64(opt.ChangeID),
						codebaseComment, codebase.WithBitsAIServiceJWT(c.BitsAISecret))
					if err != nil {
						log.V1.CtxError(ctx, "failed to create review comment , err = %v", err)
						continue
					}
					// update metadata with thread_id for publish draft comments
					threadIDs = append(threadIDs, comment.ThreadID)

					// update to final status
					processedCnt += 1
				}
				if processedCnt > 0 {
					metadata, _ := SerializeMetadata(ctx, AIFileReviewMetadata{
						FilePath:          opt.Path,
						CommentCount:      processedCnt,
						ThreadIDs:         threadIDs,
						FilteredThreadIDs: filteredThreadIDs,
					})
					if ToDraft {
						// update status as drafted
						c.draftAIFileReviewTask(ctx, po.ID,
							lo.ToPtr(fmt.Sprintf("AI added %d comments. %d filtered, %d similar.",
								processedCnt, filteredCnt, similarFilteredCnt)), metadata)
					} else {
						// directly set to finished
						c.finishAIFileReviewTask(ctx, po.ID,
							lo.ToPtr(fmt.Sprintf("AI added %d comments. %d filtered, %d similar.",
								processedCnt, filteredCnt, similarFilteredCnt)), metadata)
					}
				} else {
					metadata, _ := SerializeMetadata(ctx, AIFileReviewMetadata{
						FilePath:          opt.Path,
						CommentCount:      processedCnt,
						ThreadIDs:         threadIDs,
						FilteredThreadIDs: filteredThreadIDs,
					})
					// excludedCnt = 0
					if excludedCnt != 0 && excludedCnt == len(response.MergedReviews) {
						// all comments are excluded.
						c.finishAIFileReviewTask(ctx, po.ID, lo.ToPtr(NoErrReviewComment), metadata)
					} else {
						if similarFilteredCnt == len(response.MergedReviews) {
							// all comments are filtered because of similarity
							c.failAIFileReviewTask(ctx, po.ID, opt.Path,
								lo.ToPtr(ErrCheckCommentSimilarityBlocked.Error()), metadata)
						} else if filteredCnt == len(response.MergedReviews) {
							// all comments are filtered because of post llm scan
							c.finishAIFileReviewTask(ctx, po.ID, lo.ToPtr(NoErrReviewComment), metadata)
						} else if similarFilteredCnt+filteredCnt == len(response.MergedReviews) {
							// partly filtered accordingly, but still no processed.
							c.finishAIFileReviewTask(ctx, po.ID, lo.ToPtr(
								NoErrReviewComment+fmt.Sprintf("\n%d filtered, %d similared.",
									filteredCnt, similarFilteredCnt)), metadata)
						} else {
							// if any failed, after all there's no processed, consider it as a failed case.
							c.failAIFileReviewTask(ctx, po.ID, opt.Path,
								lo.ToPtr(ErrCreateComment.Error()), metadata)
						}
					}
				}
			}
		}
	}()
	return nil
}

func (c *CodebaseAgent) aiFixComment(ctx context.Context, mergedReview Review, params service.AIFileReviewInvokeParams) (string, string, int, int) {
	fixRes, err := c.CreateAIFix(ctx, service.AiFixOption{
		AppID:        AIFixAppID,
		FunctionID:   AIFixFunctionID,
		ErrorMessage: mergedReview.Issue,
		Locale:       &params.Locale,
		Path:         params.Path,
		Range: service.Range{
			Start: service.RangeNode{
				Line:      int64(mergedReview.Line.getValue()),
				Character: 0,
			},
			End: service.RangeNode{
				Line:      int64(mergedReview.Line.getValue()),
				Character: 512,
			},
		},
		Revision: params.SourceRev,
		RuleName: mergedReview.IssueType,
		RepoName: params.RepoName,
		Account: &authentity.Account{
			Username:          "BitsAI",
			CodebaseBitsAIJWT: c.BitsAISecret,
		},
		SourceSHA: params.SourceRev,
		TargetSHA: params.TargetRev,
	})
	if err != nil {
		log.V1.CtxError(ctx, "aiFixPerTask Create Failed: %v", err)
		return "", "", 0, 0
	}
	if fixRes != nil && fixRes.FixCode != "" {
		rangeList := strings.Split(fixRes.FixRange, ":")
		start := 0
		end := 0
		if len(rangeList) == 2 {
			startLine, err := strconv.ParseInt(rangeList[0], 10, 64)
			if err == nil {
				start = int(startLine)
			}
			endLine, err := strconv.ParseInt(rangeList[1], 10, 64)
			if err == nil {
				end = int(endLine)
			}
		}
		if start == 0 {
			return "", "", 0, 0
		}
		return fixRes.FixCode, fixRes.FixExplain, start, end
	}
	return "", "", 0, 0
}

type CommentLocationOption struct {
	path      string
	start     int32
	end       int32
	threshold *float64
}

func (c *CodebaseAgent) SimilarCommentExists(ctx context.Context, repoName string,
	changeID int64, comment string, locOpt *CommentLocationOption, fixLocOpt *CommentLocationOption) bool {
	commentsAll, err := c.CodebaseCli.ListComments(ctx, repoName, changeID, codebase.ListCommentsOption{},
		codebase.WithBitsAIServiceJWT(c.BitsAISecret))
	const MinLineDistance = 3
	if err != nil {
		log.V1.CtxError(ctx, "[Similar Comments Checking] failed to list comments, err = %v", err)
		return false
	}

	// filter out outdated comments
	comments := make([]*codebase.Comment, 0)
	for _, cmt := range commentsAll {
		if cmt.Outdated {
			continue
		}

		// 判断diff块之间的距离大于5行
		if fixLocOpt != nil && cmt.Path == fixLocOpt.path && (fixLocOpt.end >= (cmt.StartLine - 5)) && (fixLocOpt.start <= (cmt.EndLine + 5)) {
			log.V1.CtxInfo(ctx, "[Similar Comments Checking]"+
				"similar comment with line distance found, past: content = %s, start = %d, end = %d, "+
				"current: content = %s, start = %d, end = %d",
				cmt.Content, cmt.StartLine, cmt.EndLine, comment, fixLocOpt.start, fixLocOpt.end)
			return true
		}

		// if location of past comment is very near to current comment, return true
		if locOpt != nil &&
			(math.Abs(float64(cmt.StartLine-locOpt.start)) < MinLineDistance ||
				math.Abs(float64(cmt.EndLine-locOpt.end)) < MinLineDistance) && cmt.Path == locOpt.path {
			// log content and location
			log.V1.CtxInfo(ctx, "[Similar Comments Checking]"+
				"similar comment with line distance found, past: content = %s, start = %d, end = %d, "+
				"current: content = %s, start = %d, end = %d",
				cmt.Content, cmt.StartLine, cmt.EndLine, comment, locOpt.start, locOpt.end)
			return true
		}
		comments = append(comments, cmt)
	}

	commentContents := make([]string, 0)
	lo.ForEach(comments, func(comment *codebase.Comment, index int) {
		commentSegments := strings.SplitN(comment.Content, SuggestionSep, 2)
		if len(commentSegments) <= 0 {
			commentContents = append(commentContents, comment.Content)
		} else {
			commentOnly := strings.TrimSuffix(commentSegments[0], "\n")
			commentContents = append(commentContents, commentOnly)
		}
	})

	// log comparing of processing comments(record count) and input comment
	log.V1.CtxInfo(ctx, "[Similar Comments Checking] comparing comments count = %d, input comment = %s",
		len(commentContents), comment)

	if len(commentContents) > 0 {
		pastCommentsVectors := make([][]float64, 0, len(commentContents))
		var currentVector []float64
		var mu sync.Mutex
		var wg sync.WaitGroup
		var errOccurred bool

		// 处理 currentVector (新评论)
		wg.Add(1)
		go func() {
			defer func() {
				if r := recover(); r != nil {
					log.V1.CtxError(ctx, "[Similar Comments Checking] panic in goroutine: %v", r)
					errOccurred = true
				}
			}()
			defer wg.Done()

			embeddings, err := c.ChatService.Embeddings(ctx, llmstack.EmbeddingsRequest{
				Model:  EmbeddingModel,
				Inputs: []string{comment},
			}, &authentity.Account{
				Username:          "BitsAI",
				CodebaseBitsAIJWT: c.BitsAISecret,
			})
			if err != nil {
				log.V1.CtxError(ctx, "[Similar Comments Checking] failed to get current comment embedding, error = %v", err)
				errOccurred = true
				return
			}
			if embeddings == nil || len(embeddings.Data) == 0 {
				log.V1.CtxWarn(ctx, "[Similar Comments Checking] returns empty for current comment")
				errOccurred = true
				return
			}

			mu.Lock()
			currentVector = embeddings.Data[0].Embedding
			mu.Unlock()
		}()

		// 分批处理 pastComments
		for i := 0; i < len(commentContents); i += MaxPastCommentsLen {
			end := i + MaxPastCommentsLen
			if end > len(commentContents) {
				end = len(commentContents)
			}
			batch := commentContents[i:end]

			wg.Add(1)
			go func(startIdx int, batchComments []string) {
				defer func() {
					if r := recover(); r != nil {
						log.V1.CtxError(ctx, "[Similar Comments Checking] panic in goroutine: %v", r)
						errOccurred = true
					}
				}()
				defer wg.Done()

				embeddings, err := c.ChatService.Embeddings(ctx, llmstack.EmbeddingsRequest{
					Model:  EmbeddingModel,
					Inputs: batchComments,
				}, &authentity.Account{
					Username:          "BitsAI",
					CodebaseBitsAIJWT: c.BitsAISecret,
				})
				if err != nil {
					log.V1.CtxError(ctx, "[Similar Comments Checking] failed for batch %d-%d, error = %v",
						startIdx, startIdx+len(batchComments), err)
					errOccurred = true
					return
				}
				if embeddings == nil || len(embeddings.Data) == 0 {
					log.V1.CtxWarn(ctx, "[Similar Comments Checking] returns empty for batch %d-%d",
						startIdx, startIdx+len(batchComments))
					return
				}

				mu.Lock()
				for _, data := range embeddings.Data {
					pastCommentsVectors = append(pastCommentsVectors, data.Embedding)
				}
				mu.Unlock()
			}(i, batch)
		}

		wg.Wait()

		if errOccurred || currentVector == nil || len(pastCommentsVectors) == 0 {
			return false
		}

		// log success with vectors and model
		log.V1.CtxInfo(ctx, "[Similar Comments Checking] success, model = %s, processed vectors count = %d",
			EmbeddingModel, len(pastCommentsVectors))

		// calculate similarity by calling isSimilar
		threshold := DefaultSimilarCommentsThreshold
		if fixLocOpt != nil {
			threshold = lo.FromPtrOr(fixLocOpt.threshold, DefaultSimilarCommentsThreshold)
		}
		similarIdx, checkRes := isSimilar(ctx, pastCommentsVectors, currentVector, threshold)
		// if checkRes == true, log comment by similarIdx
		if checkRes && similarIdx < len(commentContents) {
			log.V1.CtxInfo(ctx, "[Similar Comments Checking] similar comment exists, "+
				"past comment = %s, comment = %s",
				commentContents[similarIdx], comment)
		}
		return checkRes
	}
	return false
}

func isSimilar(ctx context.Context, pastComments [][]float64, comment []float64, threshold float64) (int, bool) {
	// use cosine calculation to check if comment is similar to any item in the pastComments
	// write calculation directly instead of using a function
	cosineSimilarity := func(a, b []float64) float64 {
		var dotProduct, normA, normB float64
		for i := range a {
			dotProduct += a[i] * b[i]
			normA += a[i] * a[i]
			normB += b[i] * b[i]
		}
		return dotProduct / (math.Sqrt(normA) * math.Sqrt(normB))
	}

	for i, pastComment := range pastComments {
		sim := cosineSimilarity(pastComment, comment)

		if sim >= threshold {
			log.V1.CtxInfo(ctx, "[Similar Comments Checking] similarity = %f, threshold = %f", sim, threshold)
			return i, true
		}
	}
	return -1, false
}

func (c *CodebaseAgent) startAIReviewPublish() error {
	const PublishedTaskValue = "published"

	var (
		processPublishInterval = DefaultProcessPublishIntervalMilli * time.Millisecond
		TaskTimeOutDur         = DefaultNotFinishedTimeoutDurationMin * time.Minute

		strDrafted   = entity.StatusCodebaseAIFileReviewDrafted.String()
		strFinished  = entity.StatusCodebaseAIFileReviewFinished.String()
		strCancelled = entity.StatusCodebaseAIFileReviewCancelled.String()
	)

	if c.CodeAIConfig.GetValue().GlobalENV.ProcessPublishIntervalMilli != 0 {
		processPublishInterval = time.Duration(c.CodeAIConfig.GetValue().GlobalENV.ProcessPublishIntervalMilli) * time.Millisecond
	}

	if c.CodeAIConfig.GetValue().GlobalENV.NotFinishedTimeoutDurationMin != 0 {
		TaskTimeOutDur = time.Duration(c.CodeAIConfig.GetValue().GlobalENV.NotFinishedTimeoutDurationMin) * time.Minute
	}

	go func() {
		ticker := time.NewTicker(processPublishInterval)
		defer ticker.Stop()
		for range ticker.C {
			logID := logid.GenLogID()
			ctx := context.WithValue(context.Background(), "K_LOGID", logID)
			// ----- AI File Review Case
			taskPOs := make([]*entity.AsyncTask, 0)
			// list all task with drafted comment
			for page := 1; page <= c.CodeAIConfig.GetValue().GlobalENV.MaxQueryPage; page++ {
				listedPOs, err := c.DAO.ListAsyncTasks(ctx, dal.ListAsyncTaskOption{
					Status:       lo.ToPtr(strDrafted),
					Desc:         lo.ToPtr(true),
					Count:        c.CodeAIConfig.GetValue().GlobalENV.PerPageLimit,
					Offset:       (page - 1) * c.CodeAIConfig.GetValue().GlobalENV.PerPageLimit,
					SinceMinutes: lo.ToPtr(TaskTimeOutDur),
				})
				if err != nil {
					log.V1.CtxError(ctx, "list tasks with status: %s failed", strDrafted)
					continue
				}
				taskPOs = append(taskPOs, listedPOs...)
			}

			if len(taskPOs) != 0 {
				log.V1.CtxInfo(ctx, fmt.Sprintf("fetch %d tasks of ai file review drafted\n", len(taskPOs)))
			}

			// correspond task key with current session
			allTasksMapByKey := make(map[string]string)
			logTaskKeyString := ""
			for _, task := range taskPOs {
				if task.Params == nil || task.Params.Params == nil {
					continue
				}
				taskKey := task.TaskKey

				// get all tasks by task_key, check if taskKey has saved set in map
				if _, ok := allTasksMapByKey[taskKey]; ok {
					continue
				}
				allTasksMapByKey[taskKey] = task.Params.SessionID
				// simply record which session and corresponded task key
				logTaskKeyString += taskKey + "@" + task.Params.SessionID + ", "
			}

			log.V1.CtxInfo(ctx, "to process all task key in this instance are: %s", logTaskKeyString)

			// 2. process by task_key, and skip those where not fully drafted.
			logTaskKeyProcessed := ""
			for key := range allTasksMapByKey {
				toPublish := true
				allTasks, err := c.DAO.ListAsyncTasks(ctx, dal.ListAsyncTaskOption{
					TaskKey:  lo.ToPtr(key),
					TaskType: lo.ToPtr(entity.TaskTypeCodebaseAIFileReview),
					Desc:     lo.ToPtr(true),
				})
				if err != nil {
					log.V1.CtxError(ctx, "list tasks with task_key: %s failed", key)
					continue
				}

				// keep the latest tasks with key and having same session_id
				if len(allTasks) == 0 || allTasks[0].Params == nil {
					continue
				}

				latestSession := allTasks[0].Params.SessionID
				logTaskKeyProcessed += key + "@" + latestSession + ", "
				allLatestTasks := make([]*entity.AsyncTask, 0)
				for _, task := range allTasks {
					if task.Params != nil && task.Params.SessionID == latestSession {
						allLatestTasks = append(allLatestTasks, task)
					} else {
						break
					}
				}

				if len(allLatestTasks) == 0 {
					// log error and continue to avoid breaking the pipeline
					log.V1.CtxError(ctx, "no tasks with task_key: %s", key)
					continue
				}

				var (
					draftCommentCnt = 0
				)
				for _, task := range allLatestTasks {
					if task.Status == strDrafted {
						draftCommentCnt++
					} else {
						if task.Status != strFinished && task.Status != strCancelled {
							// means it shouldn't be published (ready/thinking/responded/generating status) or there's no draft.
							toPublish = false
							break
						}
					}
				}

				if !toPublish {
					continue
				}

				// all tasks with task_key is ready to be published, cache the key WITH SESSION to avoid duplications.
				sID := "ai_code_review_publish_" + key + "@" + latestSession
				taskValue, err := c.RedisClient.Get(ctx, sID)
				if err != nil {
					if errors.Is(err, redis2.Nil) {
						// not found, and ignore potential error to avoid break the main pipeline.
						// set ttl same as process interval.
						log.V1.CtxInfo(ctx, "not found sID %s publish comments from redis, err = %v", sID, err)
						err2 := c.RedisClient.Set(ctx, sID, PublishedTaskValue, AIReviewPublishTTL)
						if err2 != nil {
							log.V1.CtxError(ctx, "failed to set sID %s status to redis, err = %v", sID, err2)
							continue
						}
					} else {
						log.V1.CtxError(ctx, "failed to get sID %s status from redis, err = %v", sID, err)
						continue
					}
				} else {
					if taskValue == PublishedTaskValue {
						// other instance may have processed this session.
						continue
					}
				}

				var (
					repoName            string
					changeID            uint64
					createReviewOptions codebase.CreateReviewsOptions
				)
				if draftCommentCnt > 0 {
					// publish by change id, take 1st of draftedTaskGroup
					if allLatestTasks[0].Params == nil || allLatestTasks[0].Params.Params == nil {
						continue
					}
					taskParams, err := getOptionFromVars[service.AIFileReviewInvokeParams](allLatestTasks[0].Params.Params)
					if err != nil {
						log.V1.CtxWarn(ctx, "failed to get context variables from draftedTaskGroup[0], err = %v", err)
					}
					repoName = taskParams.RepoName
					changeID = taskParams.ChangeID
					createReviewOptions = codebase.CreateReviewsOptions{
						Event: "comment",
						Sha:   taskParams.SourceRev,
					}
				}

				// publish by changeID; message and metadata has been recorded for each task.
				err = c.CodebaseCli.CreateReviews(ctx, repoName,
					int64(changeID),
					createReviewOptions, codebase.WithBitsAIServiceJWT(c.BitsAISecret))
				if err != nil {
					log.V1.CtxError(ctx, "failed to created reviews: %v", err)
					continue
				}
				for _, t := range allLatestTasks {
					if t.Status == strDrafted {
						c.finishAIFileReviewTask(ctx, t.ID, lo.ToPtr(t.Message), t.Metadata)
					}
				}
			}

			log.V1.CtxInfo(ctx, "processed task key is: %s", logTaskKeyProcessed)
		}
	}()
	return nil
}

func getOptionFromVars[T any](vars map[string]any) (*T, error) {
	opt := new(T)
	if err := mapstructure.Decode(vars, &opt); err != nil {
		return nil, err
	}
	return opt, nil
}

func setVarsFromOption[T any](s T) (map[string]any, error) {
	var result map[string]any
	err := mapstructure.Decode(s, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (c *CodebaseAgent) draftAIFileReviewTask(
	ctx context.Context, taskID uint64, msg *string, metadata map[string]any) {
	_, err := c.updateStatus(ctx, taskID, entity.StatusCodebaseAIFileReviewDrafted.String(), msg, metadata)
	if err != nil {
		log.V1.CtxError(ctx, "failed to update task as drafted, taskID = %d, err = %v", taskID, err)
	}
}

func (c *CodebaseAgent) finishAIFileReviewTask(
	ctx context.Context, taskID uint64, msg *string, metadata map[string]any) {
	var err error

	curTaskPO, err := c.GetTaskInfo(ctx, taskID)
	if err != nil {
		log.V1.CtxError(ctx, "failed to get task info, taskID = %d, err = %v", taskID, err)
		return
	}

	if metadata != nil && curTaskPO != nil && curTaskPO.Metadata != nil {
		// save original assignments
		m, ok := curTaskPO.Metadata[entity.TaskTypeCodebaseAIFileReview.String()]
		if !ok || m == nil {
			log.V1.CtxInfo(ctx, "failed to get metadata, try to parse entire metadata")
			m = metadata
		}
		mMap := m.(map[string]any)
		for k, v := range mMap {
			if _, exist := metadata[k]; !exist {
				metadata[k] = v
			}
		}
	}

	_, err = c.updateStatus(ctx, taskID, entity.StatusCodebaseAIFileReviewFinished.String(), msg, metadata)
	if err != nil {
		log.V1.CtxError(ctx, "failed to update task as finished, taskID = %d, err = %v", taskID, err)
	}
}

func (c *CodebaseAgent) failAIFileReviewTask(ctx context.Context, taskID uint64, filepath string, msg *string, metadata map[string]any) {
	var err error

	if metadata == nil {
		metadata, err = SerializeMetadata(ctx, AIFileReviewMetadata{
			FilePath:          filepath,
			CommentCount:      0,
			ThreadIDs:         nil,
			FilteredThreadIDs: nil,
		})
		if err != nil {
			log.V1.CtxError(ctx, "serial metadata failed, %v", err)
			metadata = nil
		}
	}

	_, err = c.updateStatus(ctx, taskID, entity.StatusCodebaseAIFileReviewCancelled.String(), msg, metadata)
	if err != nil {
		log.V1.CtxError(ctx, "failed to update task as cancelled, taskID = %d, err = %v", taskID, err)
	}
}

func (c *CodebaseAgent) UpdateMetadata(ctx context.Context, taskID uint64, rmetadata AIFileReviewMetadata) error {
	metadata, err := SerializeMetadata(ctx, rmetadata)
	if err != nil {
		log.V1.CtxError(ctx, "serial metadata failed, %v", err)
		metadata = nil
	}

	_, err = c.DAO.UpdateAsyncStatus(ctx, dal.UpdateAsyncTaskStatusOption{
		ID:       taskID,
		Metadata: metadata,
	})
	if err != nil {
		return errors.WithMessage(err, "failed to update metadata")
	}
	return nil
}

func (c *CodebaseAgent) finishAIReplyTask(ctx context.Context, taskID uint64, msg *string) {
	_, err := c.updateStatus(ctx, taskID, entity.StatusCodebaseAIReplyFinished.String(), msg, nil)
	if err != nil {
		log.V1.CtxError(ctx, "failed to update task as finished, taskID = %d, err = %v", taskID, err)
	}
}

func (c *CodebaseAgent) failAIReplyTask(ctx context.Context, taskID uint64, msg *string) {
	_, err := c.updateStatus(ctx, taskID, entity.StatusCodebaseAIReplyCancelled.String(), msg, nil)
	if err != nil {
		log.V1.CtxError(ctx, "failed to update task as cancelled, taskID = %d, err = %v", taskID, err)
	}
}

func (c *CodebaseAgent) failMRWalkThroughTask(ctx context.Context, taskID uint64, msg *string) {
	_, err := c.updateStatus(ctx, taskID, entity.StatusCodebaseMRWalkThroughCancelled.String(), msg, nil)
	if err != nil {
		log.V1.CtxError(ctx, "failed to update task as cancelled, taskID = %d, err = %v", taskID, err)
	}
}

func (c *CodebaseAgent) CreateAIFix(ctx context.Context, opt service.AiFixOption) (*service.AiFixRes, error) {
	logID := tools.GetLogID(ctx)
	log.V1.CtxInfo(ctx, "logid for ai fix is %s", logID)

	parameters := make(map[string]any)
	parameters["path"] = opt.Path
	parameters["revision"] = opt.Revision
	parameters["repo_name"] = opt.RepoName
	parameters["range"] = map[string]any{
		"start": map[string]any{
			"line":      opt.Range.Start.Line,
			"character": opt.Range.Start.Character,
		},
		"end": map[string]any{
			"line":      opt.Range.End.Line,
			"character": opt.Range.End.Character,
		},
	}
	parameters["locale"] = lo.FromPtrOr(opt.Locale, "zh")
	parameters["rule_name"] = opt.RuleName
	parameters["error_message"] = opt.ErrorMessage
	parameters["change_id"] = opt.ChangeID
	parameters["source_revision"] = opt.SourceSHA
	parameters["target_revision"] = opt.TargetSHA

	log.V1.CtxInfo(ctx, fmt.Sprintf("CreateAIFix param: %v", parameters))
	fixResponse, err := c.ChatService.Invoke(ctx,
		chatservice.InvokeOption{
			AppID:      opt.AppID,
			FunctionID: opt.FunctionID,
			Parameters: parameters,
			Context: &skillentity.Context{
				Input:     "",
				Variables: parameters,
			},
			Stream: true,
			Account: &authentity.Account{
				Username:          "BitsAI",
				CodebaseBitsAIJWT: c.BitsAISecret,
			},
			ReturnPrompt: opt.NeedPrompt,
		})
	if err != nil {
		log.V1.CtxError(ctx, "filter invoke failed: %v", err)
		return nil, err
	}
	var res service.AiFixRes

	resultsString := c.loopResult(ctx, opt, fixResponse, &res)
	if resultsString == "" {
		return c.CutPromptAndRetry(ctx, opt, parameters)
	}

	return c.processingResult(ctx, opt, res, resultsString)
}

func (c *CodebaseAgent) CutPromptAndRetry(ctx context.Context, opt service.AiFixOption, parameters map[string]any) (*service.AiFixRes, error) {
	parameters["need_cut"] = true

	fixResponse, err := c.ChatService.Invoke(ctx,
		chatservice.InvokeOption{
			AppID:      opt.AppID,
			FunctionID: opt.FunctionID,
			Parameters: parameters,
			Context: &skillentity.Context{
				Input:     "",
				Variables: parameters,
			},
			Stream: true,
			Account: &authentity.Account{
				Username:          "BitsAI",
				CodebaseBitsAIJWT: c.BitsAISecret,
			},
			ReturnPrompt: opt.NeedPrompt,
		})
	if err != nil {
		log.V1.CtxError(ctx, "filter invoke failed: %v", err)
		return nil, err
	}
	var res service.AiFixRes

	resultsString := c.loopResult(ctx, opt, fixResponse, &res)
	if resultsString == "" {
		return &res, nil
	}

	return c.processingResult(ctx, opt, res, resultsString)
}

func (c *CodebaseAgent) loopResult(ctx context.Context, opt service.AiFixOption, fixResponse *chatservice.InvokeResult, res *service.AiFixRes) string {
	resultsString := ""
loop:
	for {
		select {
		case err, ok := <-fixResponse.StreamChannel.ErrorChannel:
			if !ok {
				break loop
			}
			log.V1.CtxError(ctx, "got error while streaming invoke response: %v", err)
			fixResponse.StreamChannel.Close()
			break loop
		case event, ok := <-fixResponse.StreamChannel.DataChannel:
			if !ok {
				log.V1.CtxError(ctx, "invoke data channel is closed, exited")
				break loop
			}
			switch {
			case event.ResultChunk != nil:
				result, exist := event.ResultChunk["result"]
				if !exist {
					log.V1.CtxError(ctx, "failed to get result key of llm streaming response")
					// continue
				}
				resultStr, _ := result.(string)
				resultsString = resultsString + resultStr
				promptID, exist := event.ResultChunk["prompt_completion_id"]
				if exist {
					res.FixPromptCompletionId = promptID.(int64)
				}
			case event.Prompts != nil:
				if opt.NeedPrompt != nil && *opt.NeedPrompt {
					res.FixPrompt = *event.Prompts
				}
			case event.Ranges != nil:
				res.FixRange = *event.Ranges
			}

		}
	}
	return resultsString
}

func (c *CodebaseAgent) processingResult(ctx context.Context, opt service.AiFixOption, res service.AiFixRes, results string) (*service.AiFixRes, error) {
	log.V1.CtxInfo(ctx, "processingResult results %s", results)

	originContent, err := c.CodebaseCli.GetBlobByPath(ctx, opt.RepoName, opt.Revision, opt.Path, codebaseWithJWT(&authentity.Account{
		Username:          "BitsAI",
		CodebaseBitsAIJWT: c.BitsAISecret,
	}))
	if err != nil {
		log.V1.CtxError(ctx, "failed to get file content for file %s, err: %v", opt.Path, err)
		return &res, err
	}
	content, err := base64.StdEncoding.DecodeString(originContent.Content)
	if err != nil {
		log.V1.CtxError(ctx, "Error decoding base64 content: %v", err)
		return &res, err
	}
	fixCodeArr := strings.Split(string(content), "\n")

	re := regexp.MustCompile(`<issue_content>(.*?)</issue_content>`)
	matches := re.FindStringSubmatch(results)
	if len(matches) == 2 {
		// 提取并打印匹配的内容
		res.IssueExplain = matches[1]
	}
	re = regexp.MustCompile(`<code_fix_explain>(.*?)</code_fix_explain>`)
	matches = re.FindStringSubmatch(results)
	if len(matches) == 2 {
		// 提取并打印匹配的内容
		res.FixExplain = matches[1]
	}
	/**
	<<<<<<< SEARCH
		xxx
		if mapStatus != Status.Success {
		xxx
	=======
		xxxxxxx
		if mapStatus != Status.Success {
		xxxx
	>>>>>>> REPLACE
	*/

	resultsLines := strings.Split(results, "\n")
	var codeBlock []string
	isFlag := false
	var block []string
	for _, line := range resultsLines {
		if strings.Contains(line, "<<<<<<< SEARCH") {
			isFlag = true
			continue
		}
		if strings.Contains(line, ">>>>>>> REPLACE") {
			isFlag = false
			codeBlock = append(codeBlock, strings.Join(block, "\n"))
			block = []string{}
			continue
		}
		if isFlag {
			block = append(block, line)
		}
	}

	log.V1.CtxInfo(ctx, "codeBlock %v", codeBlock)
	if len(codeBlock) > 0 {
		for _, blockString := range codeBlock {
			// 提取<<<<<<< SEARCH和>>>>>>> REPLACE之间的内容
			searchAndReplace := blockString
			parts := strings.Split(searchAndReplace, "=======")
			if len(parts) != 2 {
				continue
			}
			// 先去掉前后的换行符
			searchChunk := strings.Trim(parts[0], "\n")
			log.V1.CtxInfo(ctx, "searchChunk %s", searchChunk)
			replaceChunk := strings.Trim(parts[1], "\n")
			log.V1.CtxInfo(ctx, "replaceChunk %s", replaceChunk)

			// 拆出每一行的内容并且去掉前后空格
			search := strings.Split(searchChunk, "\n")
			if len(search) == 0 {
				continue
			}
			// 拿到第一行开始搜索
			firstLine := strings.TrimSpace(search[0])
			log.V1.CtxInfo(ctx, "fixCodeArr %v", fixCodeArr)

			indentation := ""
			exist := false
			searchStartLine := -1
			for index, line := range fixCodeArr {
				// 如果搜到了，先抽取这一行原始行的缩进，然后记录开始行
				if strings.Contains(line, firstLine) {
					searchStartLine = index
					indentationRe := regexp.MustCompile(`^ *`)
					indentation = indentationRe.FindString(strings.ReplaceAll(line, "\t", "    "))
					exist = true
					// 查询每一行都能搜索到
					for i, searchLine := range search {
						// 如果越界了或者行内容不对
						if index+i >= len(fixCodeArr) || strings.TrimSpace(fixCodeArr[index+i]) != strings.TrimSpace(searchLine) {
							exist = false
							break
						}
					}
					if exist {
						break
					}
				}
			}
			if !exist {
				continue
			}
			// 替换
			resultCodeArr := []string{}
			replace := strings.Split(replaceChunk, "\n")
			for index, line := range replace {
				// 第一行沿用老代码的缩进
				if index == 0 {
					resultCodeArr = append(resultCodeArr, indentation+strings.TrimSpace(line))
					continue
				}
				checkLine := strings.ReplaceAll(line, "\t", "    ")
				laseLine := strings.ReplaceAll(replace[index-1], "\t", "    ")
				// 计算每一个变更行与前一行缩进差,加上上一行的缩进长度
				indentationCount := len(checkLine) - len(strings.TrimSpace(checkLine)) - (len(laseLine) - len(strings.TrimSpace(laseLine))) + len(indentation)
				if indentationCount < 0 {
					indentationCount = 0
				}
				indentation = strings.Repeat(" ", indentationCount)
				resultCodeArr = append(resultCodeArr, indentation+strings.TrimSpace(line))
			}
			log.V1.CtxInfo(ctx, "fixCodeArr 2 %v", fixCodeArr)

			prefix := fixCodeArr[0:searchStartLine]
			suffix := fixCodeArr[searchStartLine+len(search):]
			fixCodeArr = append(prefix, strings.Join(resultCodeArr, "\n"))
			fixCodeArr = append(fixCodeArr, suffix...)
			str := strings.Join(fixCodeArr, "\n")
			fixCodeArr = strings.Split(str, "\n")
			log.V1.CtxInfo(ctx, "fixCodeArr 3 %v", fixCodeArr)
		}
	}

	fixCode := strings.Join(fixCodeArr, "\n")

	log.V1.CtxInfo(ctx, "fixCode is %s", fixCode)
	if len(c.CodeAIConfig.GetValue().DevGPTHost) > 0 && strings.Contains(results, "```go") {
		client, err := hertz.NewClient(c.CodeAIConfig.GetValue().DevGPTHost, hertz.NewHTTPClientOption{
			Timeout:    time.Second * 30,
			Debug:      false,
			DisableLog: false,
		})
		if err != nil {
			return nil, err
		}
		var resp fmtResponse
		_, err = client.DoJSONReq(ctx, http.MethodPost, "/data_check/api/v1/code_format", hertz.ReqOption{
			ExpectedCode: http.StatusOK,
			Body: map[string]string{
				"content": fmt.Sprintf("```go\n%s\n```", fixCode),
			},
			Result: &resp,
			Notes:  "go_fmt",
		})
		if err == nil && resp.Status == "success" {
			fixCodeContent := resp.Content
			re = regexp.MustCompile("```go(.*?)```")
			matches = re.FindStringSubmatch(fixCodeContent)
			if len(matches) > 0 {
				fixCode = strings.Trim(matches[1], "\n")
			}
		}
	}

	log.V1.CtxInfo(ctx, "fmt_fixCode is %s", fixCode)
	diffCode, diffStart, diffEnd := calcFixContent(string(content), fixCode)
	log.V1.CtxInfo(ctx, fmt.Sprintf("diffCode: %s", diffCode))
	log.V1.CtxInfo(ctx, fmt.Sprintf("diffStart: %d", diffStart))
	log.V1.CtxInfo(ctx, fmt.Sprintf("diffEnd: %d", diffEnd))
	res.FixCode = diffCode
	res.FixRange = fmt.Sprintf("%d:%d", diffStart, diffEnd)

	if opt.ChangeID != nil && diffStart > 0 {
		go func() {
			defer func() {
				if err := recover(); err != nil {
					log.V1.CtxError(ctx, "panic in ai fix, err = %v", err)
				}
			}()
			r := rand.New(rand.NewSource(time.Now().UnixNano()))
			// 生成一个随机数,10秒之间
			randSleep := time.Duration(r.Intn(10)) * time.Second
			log.V1.CtxInfo(ctx, fmt.Sprintf("Sleeping: %d", randSleep))
			time.Sleep(randSleep)
			// 3.2 extra filter by BE, checking if content is similar to existing comments
			c.createComment(ctx, res, opt, diffCode, diffStart, diffEnd)
		}()
	}

	return &res, nil
}

func (c *CodebaseAgent) createComment(ctx context.Context, res service.AiFixRes, opt service.AiFixOption, fixRealCode string, start int, end int) {
	commentOnly, AIReviewComment := c.renderCommentByUsingSuggestion(Review{
		Issue:       res.IssueExplain,
		IssueType:   opt.RuleName,
		Suggestions: fixRealCode,
	}, lo.FromPtrOr(opt.Locale, "zh"), res.FixExplain, (end-start > 10) || (len(strings.Split(fixRealCode, "\n")) > 10))
	if AIReviewComment == "" {
		log.V1.CtxError(ctx, "failed to render comment")
		return
	}

	if end-start > 10 {
		log.V1.CtxInfo(ctx, "comment is too long, skip comment")
		return
	}

	log.V1.CtxInfo(ctx, "similar comment check enabled for ai fix request")
	// similar comment check won't pass location info, which is only for CR only comment
	if c.SimilarCommentExists(ctx, opt.RepoName, *opt.ChangeID, commentOnly, nil, &CommentLocationOption{
		path:      opt.Path,
		start:     int32(start),
		end:       int32(end),
		threshold: lo.ToPtr(FixSimilarCommentsThreshold),
	}) {
		log.V1.CtxInfo(ctx,
			"similar comment exists, skip this fix comment %s", commentOnly)
		return
	}
	// 4. create comment
	codebaseComment := codebase.CreateCommentsOption{
		Content:     AIReviewComment,
		LeftSHA:     &opt.TargetSHA,
		RightSHA:    &opt.SourceSHA,
		Side:        "head",
		Path:        opt.Path,
		StartColumn: 0,
		EndColumn:   512,
		Draft:       false,
		PatchSetNum: lo.FromPtrOr(opt.PatchSetNum, 1),
		StartLine:   int32(start),
		EndLine:     int32(end),
	}

	log.V1.CtxInfo(ctx, fmt.Sprintf("comment_resquest_body: %v", codebaseComment))
	_, err := c.CodebaseCli.CreateComment(
		ctx, opt.RepoName, *opt.ChangeID,
		codebaseComment, codebase.WithBitsAIServiceJWT(c.BitsAISecret))
	if err != nil {
		log.V1.CtxError(ctx, "failed to create review comment , err = %v", err)
		return
	}
}

type fmtResponse struct {
	Content string `json:"content"`
	Status  string `json:"status"`
}

func (c *CodebaseAgent) renderCommentByUsingSuggestion(r Review, locale string, explain string, isClose bool) (string, string) {
	const (
		localeEN = "en"
		localeZH = "zh"
	)
	// remain comment from LLM response, but present with readable user-friendly issue.
	issue := ""
	if len(c.CodeAIConfig.GetValue().CodeReview.RulesMappingIssue) != 0 {
		rulesMappingIssue := c.CodeAIConfig.GetValue().CodeReview.RulesMappingIssue
		for _, rule := range rulesMappingIssue {
			if lo.Contains(rule.Rules, r.IssueType) {
				issue = rule.Issue
				if locale == localeEN && rule.IssueEN != "" {
					// if en desc is valid and not empty, check if locale is en, use en desc
					issue = rule.IssueEN
				}
				break
			}
		}
	}

	templatesFull := map[string]string{
		localeEN: AIFileReviewCommentFormatEN,
		localeZH: AIFileReviewCommentFormatZH,
	}
	templatesSimple := map[string]string{
		localeEN: AIFileReviewCommentFormatENSimple,
		localeZH: AIFileReviewCommentFormatZHSimple,
	}
	if len(issue) == 0 {
		templatesFull = map[string]string{
			localeEN: AIFileReviewCommentFormatENWithoutType,
			localeZH: AIFileReviewCommentFormatZHWithoutType,
		}
		templatesSimple = map[string]string{
			localeEN: AIFileReviewCommentFormatENSimpleWithoutType,
			localeZH: AIFileReviewCommentFormatZHSimpleWithoutType,
		}
	}
	if !isClose && len(c.CodeAIConfig.GetValue().CodeReview.SuggestionOpenRules) != 0 {
		suggestionRules := c.CodeAIConfig.GetValue().CodeReview.SuggestionOpenRules
		if lo.Contains(suggestionRules, r.IssueType) {
			templatesFull = map[string]string{
				localeEN: AIFileReviewCommentFormatENWithOpen,
				localeZH: AIFileReviewCommentFormatZHWithOpen,
			}
			if len(issue) == 0 {
				templatesFull = map[string]string{
					localeEN: AIFileReviewCommentFormatENWithOpenWithoutType,
					localeZH: AIFileReviewCommentFormatZHWithOpenWithoutType,
				}
			}
		}
	}

	templates := templatesFull
	var comment string
	t, ok := templates[locale]
	if !ok {
		// use en as default
		t = templates[localeEN]
	}
	comment = t
	// commentOnly is only used for checking similarity, use either locale
	commentOnly := templatesSimple[localeEN]

	// this filter comment notice will only be used when filtered comment is reserved from previous pipeline
	// and will be exposed to users
	backupFilteredCommentNotice := "⚠This comment was automatically filtered by AI " +
		"and immediately marked as resolved for reference only.\n\n"
	if r.IsFiltered != nil && *r.IsFiltered {
		comment = comment + "\n" + backupFilteredCommentNotice
	}

	// Replace placeholders with actual values
	comment = strings.Replace(comment, "{{ .issue_type }}", issue, -1)
	// issueContent := encodeCode(r.Issue)
	comment = strings.Replace(comment, "{{ .issue }}", r.Issue, -1)
	comment = strings.Replace(comment, "{{ .suggestions }}", fmt.Sprintf("%s\n```suggestion\n%s\n```", explain, r.Suggestions), -1)

	// Render comment only
	commentOnly = strings.Replace(commentOnly, "{{ .issue_type }}", issue, -1)
	commentOnly = strings.Replace(commentOnly, "{{ .issue }}", r.Issue, -1)

	return commentOnly, comment
}

func processingFixContentAndRange(str string) (string, string, string) {
	realStr := strings.Split(str, "\n")
	fixCode := ""
	issueExplain := ""
	fixExplain := ""

	for i := 0; i < len(realStr); i++ {
		if realStr[i] == "issue_content: |" {
			issueExplain = strings.TrimSpace(realStr[i+1])
			continue
		}
		if realStr[i] == "fixed_code_content: |" {
			fixCode = strings.Join(realStr[i+1:], "\n")
			continue
		}
		if realStr[i] == "code_fix_explain: |" {
			fixExplain = strings.TrimSpace(realStr[i+1])
			continue
		}
	}
	return fixCode, issueExplain, fixExplain
}

func calcFixContent(originCode string, fixContent string) (string, int, int) {
	isTab := false
	startLine := 0
	originArr := strings.Split(originCode, "\n")
	fixArr := strings.Split(fixContent, "\n")
	for index, line := range originArr {
		if index >= len(fixArr) {
			break
		}
		isTab = isTab || strings.Contains(line, "\t")
		if strings.TrimSpace(line) != strings.TrimSpace(fixArr[index]) {
			startLine = index
			break
		}
	}
	endLine := len(originArr) - 1
	endDiffLine := len(fixArr) - 1
	for index, line := range originArr {
		if endLine <= startLine {
			endLine = startLine
			break
		}
		if index >= len(fixArr) {
			break
		}
		isTab = isTab || strings.Contains(line, "\t")
		if strings.TrimSpace(originArr[endLine]) != strings.TrimSpace(fixArr[endDiffLine]) {
			break
		}
		endLine--
		endDiffLine--
	}
	if endDiffLine <= startLine {
		endDiffLine = startLine
	}
	fmtArr := fixArr[startLine : endDiffLine+1]
	if isTab {
		fmtArr = lo.Map(fmtArr, func(item string, _ int) string {
			blankCount := len(item) - len(strings.TrimLeft(item, " "))
			blank := strings.Repeat(" ", blankCount)
			return strings.ReplaceAll(blank, "    ", "\t") + strings.TrimLeft(item, " ")
		})
	}

	if startLine == 0 {
		return "", 0, 0
	}
	return strings.Join(fmtArr, "\n"), startLine + 1, endLine + 1
}

// CreateMRWalkThrough create an async task for mr walkthrough function.
func (c *CodebaseAgent) CreateMRWalkThrough(ctx context.Context, opt service.MRWalkThroughTaskOption) (*entity.AsyncTask, error) {
	var sessionID = uuid.NewString()

	// fixme(John): create zh walkthrough in beta release.
	locale := "zh"
	logID := tools.GetLogID(ctx)
	log.V1.CtxInfo(ctx, "logid for walkthrough is %s", logID)
	var (
		appID             string
		bitsAIAppIDConfig = c.BitsAIAppID.GetPointer()
	)
	if bitsAIAppIDConfig == nil {
		return nil, ErrAppIDNotFound
	}
	if opt.AppID == nil || *opt.AppID == "" {
		appConfig, ok := c.BitsAIAppID.GetValue()[c.CodeAIConfig.GetValue().AppName]
		if !ok {
			return nil, ErrAppIDNotFound
		}
		appID = appConfig.AppID
	} else {
		appID = *opt.AppID
	}

	triggerer := opt.Account.Username
	if opt.Account.Username == BitsAIUserName {
		change, err := c.CodebaseCli.GetChangeDetail(ctx, opt.RepoName, int64(opt.ChangeID), false,
			codebaseWithJWT(opt.Account))
		if err != nil {
			return nil, err
		}
		if change != nil && change.Author.Username != nil && lo.FromPtr(change.Author.Username) != "" {
			triggerer = lo.FromPtr(change.Author.Username)
		}
	}

	// 1. create in rds and save params
	param := service.MRWalkThroughInvokeParams{
		RepoName:  opt.RepoName,
		ChangeID:  opt.ChangeID,
		SourceRev: opt.SourceRevision,
		TargetRev: opt.TargetRevision,
		Locale:    locale,
	}
	paramStr, err := setVarsFromOption(param)
	if err != nil {
		log.V1.CtxError(ctx, "failed to config task review params from patchset review: %v", err)
		return nil, err
	}

	taskKey := GetUniqueTaskKey(opt.SourceRevision, opt.TargetRevision)

	po, err := c.DAO.CreateAsyncTask(ctx, dal.CreateAsyncTaskOption{
		TaskType: entity.TaskTypeCodebaseMRWalkThrough,
		Params: &entity.Parameters{
			Params:     paramStr,
			SessionID:  sessionID,
			FunctionID: opt.FunctionID,
			LogID:      logID,
		},
		User:     triggerer,
		AppID:    appID,
		Status:   entity.StatusCodebaseMRWalkThroughReady.String(),
		TaskKey:  lo.ToPtr(taskKey),
		Metadata: nil,
	})
	if err != nil {
		log.V1.CtxError(ctx, "create MR Walkthrough task failed")
		return nil, err
	}

	log.V1.CtxInfo(ctx, "MR walkthrough task created, task id = %d", po.ID)
	if opt.TargetRevision == opt.SourceRevision {
		// save po but return success for upstream(manually trigger or from codebase service)
		return nil, ErrEmptyMR
	}

	return po, nil
}

func (c *CodebaseAgent) startMRWalkThroughLLMChat() error {
	const (
		processWalkThroughChatInterval = time.Second * 5
		LLMChattedTaskValue            = "walk-chatted"
	)
	var strReady = entity.StatusCodebaseMRWalkThroughReady.String()

	go func() {
		ticker := time.NewTicker(processWalkThroughChatInterval)
		ctx := context.Background()
		defer ticker.Stop()
		for range ticker.C {
			// ----- AI MR WalkThrough Case
			// Fetch the earliest.
			taskPOs := make([]*entity.AsyncTask, 0)
			for page := 1; page <= c.CodeAIConfig.GetValue().GlobalENV.MaxQueryPage; page++ {
				listedPOs, err := c.DAO.ListAsyncTasks(ctx, dal.ListAsyncTaskOption{
					Status: lo.ToPtr(strReady),
					Count:  c.CodeAIConfig.GetValue().GlobalENV.PerPageLimit,
					Offset: (page - 1) * c.CodeAIConfig.GetValue().GlobalENV.PerPageLimit,
				})
				if err != nil {
					log.V1.CtxError(ctx, "list tasks with status: %s failed", strReady)
					continue
				}
				taskPOs = append(taskPOs, listedPOs...)
			}

			if len(taskPOs) != 0 {
				log.V1.CtxInfo(ctx, fmt.Sprintf("%d tasks to interact w/ llm", len(taskPOs)))
			}

			chatTaskListPOs := make([]*entity.AsyncTask, 0)
			// 2. update status one-by-one and only process those affected.
			for _, po := range taskPOs {
				if po.Status != strReady {
					continue
				}
				// check if po.ID task has started
				sID := strconv.FormatUint(po.ID, 10) + strReady
				taskValue, err := c.RedisClient.Get(ctx, sID)
				if err != nil {
					// not found, and ignore potential error to avoid break the main pipeline.
					log.V1.CtxInfo(ctx, "get redis value for key %s failed: %v", sID, err)
					_ = c.RedisClient.Set(ctx, sID, LLMChattedTaskValue, time.Second*60)
				} else {
					if taskValue == LLMChattedTaskValue {
						// created llm chat task.
						log.V1.CtxInfo(ctx, "duplicated task for sID: %s", sID)
						continue
					}
				}
				toChatTask, err := c.DAO.UpdateAsyncStatus(ctx, dal.UpdateAsyncTaskStatusOption{
					ID:     po.ID,
					Status: lo.ToPtr(entity.StatusCodebaseMRWalkThroughThinking.String()),
				})
				if err != nil {
					log.V1.CtxError(ctx, "failed to update mr walkthrough task from ready to thinking")
					// not affected, recheck status to avoid a "thinking" forever status
					if toChatTask != nil && toChatTask.Status == strReady {
						// not changed, and won't execute llm chat.
						continue
					}
				}
				chatTaskListPOs = append(chatTaskListPOs, toChatTask)
				if len(chatTaskListPOs) >= c.CodeAIConfig.GetValue().GlobalENV.MRWalkThroughInstanceQuota {
					log.V1.CtxInfo(ctx, "reach to mr walkthrough instance max processing task, break")
					break
				}
			}

			// 3. identify task and parse params
			var wg sync.WaitGroup
			for _, po := range chatTaskListPOs {
				if po == nil {
					continue
				}

				po := po
				wg.Add(1)
				go func(po *entity.AsyncTask) {
					defer wg.Done()
					if po.Params == nil || po.Params.Params == nil {
						log.V1.CtxWarn(ctx, "task has no params")
						c.failMRWalkThroughTask(ctx, po.ID, lo.ToPtr(ErrInvalidParameters.Error()))
						return
					}

					if po.AppID == "" || po.Params.SessionID == "" {
						log.V1.CtxWarn(ctx, "task's appID or functionID or sessionID not set")
						c.failMRWalkThroughTask(ctx, po.ID, lo.ToPtr(ErrInvalidParameters.Error()))
						return
					}

					// 3.1 confirm in advance if params correct.
					params, err := getOptionFromVars[service.MRWalkThroughInvokeParams](po.Params.Params)
					if err != nil {
						log.V1.CtxWarn(ctx, "failed to get context variables, err = %v", err)
						c.failMRWalkThroughTask(ctx, po.ID, lo.ToPtr(ErrInvalidParameters.Error()))
						return
					}

					if params == nil {
						log.V1.CtxWarn(ctx, "failed to get context variables for walkthrough, params is nil")
						c.failMRWalkThroughTask(ctx, po.ID, lo.ToPtr(ErrInvalidParameters.Error()))
						return
					}

					// update ctx
					subCtx := context.WithValue(ctx, "K_LOGID", po.Params.LogID)

					localParams := duplicateParams(po.Params.Params)
					intentContext := skillentity.Context{
						Variables: localParams,
					}

					start := time.Now()
					// 3.2 llm chat
					llmResponse, err := c.ChatService.Invoke(subCtx,
						chatservice.InvokeOption{
							AppID:      po.AppID,
							FunctionID: po.Params.FunctionID,
							Parameters: localParams,
							Context:    &intentContext,
							Stream:     true,
							Account: &authentity.Account{
								Username:          "BitsAI",
								CodebaseBitsAIJWT: c.BitsAISecret,
							},
							SessionID: po.Params.SessionID,
						})
					if err != nil {
						log.V1.CtxError(subCtx, "invoke service failed: %v", err)
						c.failMRWalkThroughTask(ctx, po.ID, lo.ToPtr(ErrChatLLMFailed.Error()+
							".Internal Err:"+err.Error()))
						return
					}

					// streaming
					var results []string
					var promptID uint64
					firstChunk := true
				loop:
					for {
						select {
						case err, ok := <-llmResponse.StreamChannel.ErrorChannel:
							if !ok {
								break loop
							}
							log.V1.CtxError(subCtx, "got error while streaming invoke response: %v", err)
							llmResponse.StreamChannel.Close()
							break loop
						case event, ok := <-llmResponse.StreamChannel.DataChannel:
							if !ok {
								log.V1.CtxError(subCtx, "invoke data channel is closed, exited")
								break loop
							}
							switch {
							case event.Progress != nil:
								results = append(results, *event.Progress)
							case event.ResultChunk != nil:
								if firstChunk {
									log.V1.CtxError(subCtx, "chat handler first token latency: %dms", time.Since(start).Milliseconds())
									firstChunk = false
								}
								result, exist := event.ResultChunk["result"]
								if !exist {
									log.V1.CtxError(subCtx, "failed to get result key of llm streaming response")
									// continue
								}
								resultStr, _ := result.(string)
								results = append(results, resultStr)
								promptID = conv.Uint64Default(event.ResultChunk["prompt_completion_id"], 0)
							}
						}
					}

					log.V1.CtxInfo(subCtx, "finishing streaming invoke response for mr walkthrough")

					// 3.3 save response and metadata
					walkThrough := strings.Join(results, "")
					if walkThrough == "" {
						log.V1.CtxError(subCtx, "failed to get result key of mr walkthrough llm response outputs")
						c.failMRWalkThroughTask(subCtx, po.ID, lo.ToPtr(ErrParseLLMResponseFailed.Error()))
						return
					}

					llmStepLatency := entity.StepLatencyRecord{
						StepID:  "llm-chat",
						TaskID:  po.ID,
						Latency: time.Since(start).Milliseconds(),
					}

					_, err = c.DAO.UpdateAsyncStatus(ctx, dal.UpdateAsyncTaskStatusOption{
						ID:          po.ID,
						Status:      lo.ToPtr(entity.StatusCodebaseMRWalkThroughResponded.String()),
						LLMResponse: lo.ToPtr(walkThrough),
					})
					if err != nil {
						log.V1.CtxError(subCtx, "update mr walkthrough llm response outputs failed")
						c.failMRWalkThroughTask(subCtx, po.ID, lo.ToPtr(ErrInternalError.Error()))
						return
					}

					// 4.save parsed file walkthrough in metadata
					walkThroughJSON, err := LoadYAML(walkThrough)
					if err != nil {
						log.V1.CtxError(subCtx, "load yaml failed: %v", err)
						c.failMRWalkThroughTask(subCtx, po.ID, lo.ToPtr(ErrInternalError.Error()))
						return
					}

					for i, f := range walkThroughJSON.PRFiles {
						if f.Filename == "" {
							return
						}

						// trim filename if it ends with \n
						f.Filename = strings.TrimSuffix(f.Filename, "\n")

						preCodeMeta, postCodeMeta, err := c.CodeCopilotService.GetDiffFileWithLineNumber(ctx, codecopilotentity.GetDiffFileWithLineNumberOptions{
							RepoName: params.RepoName,
							FromSHA:  params.TargetRev,
							ToSHA:    params.SourceRev,
							Path:     f.Filename,
						}, &authentity.Account{
							Username:          "BitsAI",
							CodebaseBitsAIJWT: c.BitsAISecret,
						})
						if err != nil {
							// we cannot handle the critical bug when comment beyond changed lines, so stop and won't create.
							log.V1.CtxError(subCtx, "failed to get impacted lines for mr walkthrough, err = %v", err)

							// don't break the pipeline
							walkThroughJSON.PRFiles[i].Added = 0
							walkThroughJSON.PRFiles[i].Removed = 0
						} else {
							walkThroughJSON.PRFiles[i].Added = len(postCodeMeta.ImpactLines)
							walkThroughJSON.PRFiles[i].Removed = len(preCodeMeta.ImpactLines)
						}
						if cf, exist := c.CodeAIConfig.GetValue().MRWalkThrough.TypeWList[f.ChangeType]; exist {
							walkThroughJSON.PRFiles[i].TypeRank = cf.Weight
						}
					}
					// 4.1 sort pr files
					sort.Slice(walkThroughJSON.PRFiles, func(i, j int) bool {
						return walkThroughJSON.PRFiles[i].TypeRank < walkThroughJSON.PRFiles[j].TypeRank &&
							// make sure default value 0 indicate un-typed comes last
							walkThroughJSON.PRFiles[i].TypeRank != 0
					})

					metadata, err := SerializeMRWalkThroughMetadata(subCtx, MRWalkthroughMetadata{
						PromptID:      promptID,
						MRWalkthrough: walkThroughJSON,
						StepLatencyList: []entity.StepLatencyRecord{
							llmStepLatency,
						},
					})
					if err != nil {
						log.V1.CtxError(subCtx, "serial metadata failed, %v", err)
						return
					}

					_, err = c.DAO.UpdateAsyncStatus(subCtx, dal.UpdateAsyncTaskStatusOption{
						ID:       po.ID,
						Status:   lo.ToPtr(entity.StatusCodebaseMRWalkThroughFinished.String()),
						Metadata: metadata,
					})
					if err != nil {
						log.V1.CtxError(subCtx, "update mr walkthrough markdown failed")
						c.failMRWalkThroughTask(ctx, po.ID, lo.ToPtr(ErrInternalError.Error()))
						return
					}
				}(po)
			}
			wg.Wait()
		}
	}()
	return nil
}

// GetWalkThroughStatus return task recorded.
func (c *CodebaseAgent) GetWalkThroughStatus(ctx context.Context, opt service.MRWalkThroughStatusOption) (*entity.MRWalkthroughStatusResponse, error) {
	if opt.TargetRevision == opt.SourceRevision {
		return nil, ErrEmptyMR
	}

	taskKey := GetUniqueTaskKey(opt.SourceRevision, opt.TargetRevision)
	taskPOs, err := c.DAO.ListAsyncTasks(ctx, dal.ListAsyncTaskOption{
		TaskType: lo.ToPtr(entity.TaskTypeCodebaseMRWalkThrough),
		TaskKey:  lo.ToPtr(taskKey),
		Desc:     lo.ToPtr(true),
	})
	if err != nil {
		log.V1.CtxError(ctx, "list tasks with same taskKey: %s failed", taskKey)
		return nil, err
	}

	if len(taskPOs) == 0 {
		log.V1.CtxWarn(ctx, "task key not found: %s", taskKey)
		return nil, nil
	}

	if taskPOs[0].Params == nil || taskPOs[0].Params.Params == nil {
		return nil, errors.New("params of latest session is invalid")
	}

	// since list by desc order, parse the first session id as the latest session.
	sessionID := taskPOs[0].Params.SessionID

	po := taskPOs[0]
	var (
		repoID     string
		changeID   uint64
		functionID string
		promptID   uint64
		prFiles    []entity.PRFile
		targetRev  string
		sourceRev  string
	)

	functionID = po.Params.FunctionID

	if po.Params.Params != nil {
		params, err := getOptionFromVars[service.MRWalkThroughInvokeParams](po.Params.Params)
		if err != nil {
			log.V1.CtxWarn(ctx, "failed to get options from ai file review params, err = %v", err)
		}
		changeID = params.ChangeID
		repoID = params.RepoName
		targetRev = params.TargetRev
		sourceRev = params.SourceRev
	}

	if po.Metadata != nil {
		m, ok := po.Metadata[entity.TaskTypeCodebaseMRWalkThrough.String()]
		if !ok || m == nil {
			log.V1.CtxInfo(ctx, "failed to get walkthrough metadata, try to parse entire metadata")
			m = po.Metadata
		}
		mMap := m.(map[string]any)
		metadata, err := getOptionFromVars[MRWalkthroughMetadata](mMap)
		if err != nil {
			log.V1.CtxError(ctx, "failed to get options from metadata, err = %v", err)
			return nil, err
		}
		promptID = metadata.PromptID
		prFiles = metadata.MRWalkthrough.PRFiles
	}

	return &entity.MRWalkthroughStatusResponse{
		TaskID:             po.ID,
		FunctionID:         functionID,
		RepoID:             repoID,
		ChangeID:           changeID,
		PromptCompletionID: promptID,
		SessionID:          sessionID,
		WalkThrough:        prFiles,
		TargetRevision:     targetRev,
		SourceRevision:     sourceRev,
		Message:            po.Message,
		Status:             po.Status,
		CreatedAt:          po.CreatedAt.UnixMilli(),
	}, nil
}

func (c *CodebaseAgent) cronjobBatchClearTimeoutTask() error {
	var (
		ProcessTimeOutInterval = DefaultProcessTimeoutIntervalMilli * time.Millisecond
		TaskTimeOutDur         = DefaultNotFinishedTimeoutDurationMin * time.Minute
		maxQueryPage           = c.CodeAIConfig.GetValue().GlobalENV.PerPageLimit * 10
	)

	if c.CodeAIConfig.GetValue().GlobalENV.ProcessTimeoutIntervalMilli != 0 {
		ProcessTimeOutInterval =
			time.Duration(c.CodeAIConfig.GetValue().GlobalENV.ProcessTimeoutIntervalMilli) * time.Millisecond
	}

	if c.CodeAIConfig.GetValue().GlobalENV.NotFinishedTimeoutDurationMin != 0 {
		TaskTimeOutDur =
			time.Duration(c.CodeAIConfig.GetValue().GlobalENV.NotFinishedTimeoutDurationMin) * time.Minute
	}

	endStatusList := []string{
		entity.StatusCodebaseAIFileReviewFinished.String(),
		entity.StatusCodebaseAIFileReviewCancelled.String(),
		entity.StatusCodebaseMRWalkThroughFinished.String(),
		entity.StatusCodebaseMRWalkThroughCancelled.String(),
	}

	go func() {
		ticker := time.NewTicker(ProcessTimeOutInterval)
		ctx := context.Background()
		defer ticker.Stop()
		for range ticker.C {
			// time-out tasks that over time
			for page := 1; page <= maxQueryPage; page++ {
				tasks, err := c.DAO.ListAsyncTasks(ctx, dal.ListAsyncTaskOption{
					TaskType:     lo.ToPtr(entity.TaskTypeCodebaseAIFileReview),
					Count:        c.CodeAIConfig.GetValue().GlobalENV.PerPageLimit,
					Offset:       (page - 1) * c.CodeAIConfig.GetValue().GlobalENV.PerPageLimit,
					SinceMinutes: lo.ToPtr(TaskTimeOutDur),
				})
				if err != nil {
					log.V1.CtxError(ctx, "list task with status thinking failed: %v", err)
					continue
				}

				tasksWalkThrough, err := c.DAO.ListAsyncTasks(ctx, dal.ListAsyncTaskOption{
					TaskType:     lo.ToPtr(entity.TaskTypeCodebaseMRWalkThrough),
					Count:        c.CodeAIConfig.GetValue().GlobalENV.PerPageLimit,
					Offset:       (page - 1) * c.CodeAIConfig.GetValue().GlobalENV.PerPageLimit,
					SinceMinutes: lo.ToPtr(TaskTimeOutDur),
				})
				if err != nil {
					log.V1.CtxError(ctx, "list task with status thinking failed: %v", err)
					continue
				}

				tasks = append(tasks, tasksWalkThrough...)

				for _, task := range tasks {
					// check status
					if lo.Contains(endStatusList, task.Status) {
						// belong to one of end status, then continue.
						continue
					}

					if time.Since(task.CreatedAt) < TaskTimeOutDur {
						continue
					}

					if task.TaskType == entity.TaskTypeCodebaseAIFileReview.String() {
						// 2024-12-11, timeout only for task having drafted comment to be published
						c.failAIFileReviewTask(ctx, task.ID, "", lo.ToPtr("timeout"), nil)
					}
					if task.TaskType == entity.TaskTypeCodebaseMRWalkThrough.String() {
						c.failMRWalkThroughTask(ctx, task.ID, lo.ToPtr("timeout"))
					}
				}
			}
		}
	}()
	return nil
}

type ResMRWalkthrough struct {
	PRFiles []entity.ResPRFile `yaml:"pr_files" json:"pr_files" mapstructure:"pr_files"`
}
type MRWalkthrough struct {
	PRFiles []entity.PRFile `yaml:"pr_files" json:"pr_files" mapstructure:"pr_files"`
}

func tryFixYAML(responseText string) (bool, map[string]interface{}) {
	keys := []string{"relevant line:", "suggestion content:", "relevant file:", "existing code:", "improved code:"}
	responseTextLines := strings.Split(responseText, "\n")

	// First fallback: Convert 'relevant line: ...' to 'relevant line: |-\n ...'
	responseTextLinesCopy := make([]string, len(responseTextLines))
	copy(responseTextLinesCopy, responseTextLines)

	for i := 0; i < len(responseTextLinesCopy); i++ {
		for _, key := range keys {
			if strings.Contains(responseTextLinesCopy[i], key) && !strings.Contains(responseTextLinesCopy[i], "|-") {
				responseTextLinesCopy[i] = strings.Replace(responseTextLinesCopy[i], key, fmt.Sprintf("%s |-\n        ", key), 1)
			}
		}
	}

	data := make(map[string]interface{})
	if err := yaml.Unmarshal([]byte(strings.Join(responseTextLinesCopy, "\n")), &data); err == nil {
		return true, data
	}

	// Second fallback: try to extract only range from first ```yaml to ````
	snippetPattern := "```(yaml)?[\\s\\S]*?```"
	snippetRegex := regexp.MustCompile(snippetPattern)
	snippet := snippetRegex.FindString(responseText)
	if snippet != "" {
		snippetText := strings.TrimPrefix(snippet, "```yaml")
		snippetText = strings.TrimSuffix(snippetText, "```")
		if err := yaml.Unmarshal([]byte(snippetText), &data); err == nil {
			return true, data
		}
	}

	// Third fallback: try to remove leading and trailing curly brackets
	responseTextCopy := strings.TrimSuffix(strings.TrimPrefix(strings.TrimSpace(responseText), "{"), "}")
	if err := yaml.Unmarshal([]byte(responseTextCopy), &data); err == nil {
		return true, data
	}

	// Fourth fallback: try to remove last lines one by one
	for i := 1; i < len(responseTextLines); i++ {
		responseTextLinesTmp := strings.Join(responseTextLines[:len(responseTextLines)-i], "\n")
		if err := yaml.Unmarshal([]byte(responseTextLinesTmp), &data); err == nil {
			return true, data
		}
	}

	return false, nil
}
func LoadYAML(responseText string) (MRWalkthrough, error) {
	// Check if input is empty
	if responseText == "" {
		return MRWalkthrough{}, errors.New("input string is empty")
	}

	// Trim whitespace
	responseText = strings.TrimSpace(responseText)

	// Remove leading '```yaml' and trailing '```'
	responseText = strings.TrimPrefix(responseText, "```yaml")
	responseText = strings.TrimSuffix(responseText, "```")

	var resData ResMRWalkthrough
	err := yaml.Unmarshal([]byte(responseText), &resData)
	if err != nil {
		// Try to fix the YAML
		flag, fixedData := tryFixYAML(responseText)
		if flag {
			// Convert map[string]interface{} to MRWalkthrough
			resData, err = convertMapToMRWalkthrough(fixedData)
			if err != nil {
				return MRWalkthrough{}, err
			}
		} else {
			return MRWalkthrough{}, err
		}
	}

	data := MRWalkthrough{
		PRFiles: make([]entity.PRFile, 0),
	}
	for _, f := range resData.PRFiles {
		data.PRFiles = append(data.PRFiles, entity.PRFile{
			Filename:       f.Filename,
			ChangesSummary: f.ChangePoints,
			ChangesTitle:   f.HighLevelSummary,
			ChangeType:     f.ChangeType,
			Added:          f.Added,
			Removed:        f.Removed,
		})
	}

	return data, nil
}

func convertMapToMRWalkthrough(data map[string]interface{}) (ResMRWalkthrough, error) {
	// Convert map to JSON
	jsonData, err := json.Marshal(data)
	if err != nil {
		return ResMRWalkthrough{}, err
	}

	// Unmarshal JSON to MRWalkthrough
	var walkthrough ResMRWalkthrough
	err = json.Unmarshal(jsonData, &walkthrough)
	if err != nil {
		return ResMRWalkthrough{}, err
	}

	return walkthrough, nil
}

func (c *CodebaseAgent) applySelectiveFunc(ctx context.Context, appID string, repoName string) string {
	if appID == "" || repoName == "" {
		return ""
	}
	var useFunction string

	// apply selective model config from id min to max, apply all that matches.
	var configList []config.AccessFunctionConfig
	accessConfig := c.SelectiveFunctionAccessConfig.GetPointer()
	if accessConfig != nil {
		for _, v := range c.SelectiveFunctionAccessConfig.GetValue() {
			configList = append(configList, v)
		}
		sort.Slice(configList, func(i, j int) bool {
			return configList[i].ID < configList[j].ID
		})
	}

	for _, v := range configList {
		if v.App == appID {
			apply := false
			// found selective model config for this app
			// check if functions applies
			if lo.Contains(v.Reponames, repoName) {
				apply = true
			} else if lo.ContainsBy(v.RepoGroupnames,
				func(item string) bool {
					// check if group name in list is prefix of repo
					return strings.HasPrefix(repoName, item+"/")
				}) {
				apply = true
			}
			if apply {
				useFunction = v.CodeFunction
			}
		}
	}
	if useFunction != "" {
		log.V1.CtxInfo(ctx, "for repo %s, use function %s", repoName, useFunction)
	}
	return useFunction
}

func (c *CodebaseAgent) shouldExclude(ctx context.Context, fc service.FileContext) (bool, string) {
	var ( // fixme(John): put in config
		roleMapPermit = map[string]string{
			".js":     "JavaScript",
			".jsx":    "JavaScript React",
			".ts":     "TypeScript",
			".tsx":    "TypeScript React",
			".r":      "R",
			".vue":    "Vue",
			".go":     "Go",
			".py":     "Python",
			".rs":     "Rust",
			".rust":   "Rust",
			".cpp":    "C++",
			".cs":     "C#",
			".java":   "Java",
			".h":      "C / C++",
			".cc":     "C / C++",
			".c":      "C",
			".kt":     "Kotlin",
			".swift":  "Swift",
			".m":      "Object-C",
			".mm":     "Object-C / C++",
			".dart":   "Dart",
			".thrift": "Thrift IDL",
			".proto":  "Protocol Buffers",
			".md":     "Markdown", // 添加小写的 Markdown 文件映射
			".MD":     "Markdown", // 添加大写的 Markdown 文件映射
			".yml":    "YAML",
			".yaml":   "YAML",
		}
	)
	if _, ok := roleMapPermit[filepath.Ext(fc.Path)]; !ok {
		return true, DefaultFileExclusionReason
	}

	// filter by pattern.
	patterns := c.CodeAIConfig.GetValue().CodeReview.Exclusions
	toSkip := false
	skipReason := ""
	for _, pattern := range patterns {
		if strings.HasSuffix(pattern.StringID, "regexp") {
			for _, regexpPattern := range pattern.Patterns {
				match, err := regexp.MatchString(regexpPattern, fc.Path)
				if err == nil && match {
					toSkip = true
					skipReason = pattern.StringID
					break
				}
			}
		} else {
			for _, patternStr := range pattern.Patterns {
				if strings.Contains(fc.Path, patternStr) {
					toSkip = true
					skipReason = pattern.StringID
					break
				}
			}
		}
		if toSkip {
			return true, skipReason
		}
	}

	// check by inserted lines
	if fc.LinesInserted != 0 {
		maxLineCntByExt := c.CodeAIConfig.GetValue().CodeReview.ExclusionsByMaxLineCnt
		ext := filepath.Ext(fc.Path)
		if maxLineAdded, exist := maxLineCntByExt[ext]; exist {
			if maxLineAdded < fc.LinesInserted {
				// too many lines
				return true, fmt.Sprintf("new inserted line %d cnt exceeded %d of file ext %s.",
					fc.LinesInserted, maxLineAdded, ext)
			}
		} else {
			// check normal file size limitation
			maxLineAdded, exist = maxLineCntByExt["new_added_line"]
			if !exist {
				maxLineAdded = DefaultMaxLineAdded
			}
			if maxLineAdded < fc.LinesInserted {
				// too many lines
				return true, fmt.Sprintf("new inserted line %d cnt exceeded %d",
					fc.LinesInserted, maxLineAdded)
			}
		}
	}

	if c.CodeAIConfig.GetValue().CodeReview.ExclusionsByRenameEnable {
		if fc.ChangeType == codebase.ChangeTypeRenamed {
			return true, "rename or move file matched, skip"
		}
	}

	return toSkip, skipReason
}

func (c *CodebaseAgent) CreateMRWalkthroughByMQEvent(ctx context.Context, opt service.MRWalkThroughTaskOption) (*entity.AsyncTask, error) {
	return c.CreateMRWalkThrough(ctx, service.MRWalkThroughTaskOption{
		FunctionID:     DefaultMRWalkThroughFeature,
		ChangeID:       opt.ChangeID,
		RepoName:       opt.RepoName,
		TargetRevision: opt.TargetRevision,
		SourceRevision: opt.SourceRevision,
		Account: &authentity.Account{
			Username:          "BitsAI",
			CodebaseBitsAIJWT: c.BitsAISecret,
		},
		// fixme(John): remove this after beta release
		Locale: "zh",
	})
}

func (c *CodebaseAgent) CreateBatchCodeReviewByBitsAI(ctx context.Context, opt service.CreatePatchsetReviewOption) ([]*entity.AsyncTask, *entity.AsyncTask, error) {
	var (
		appID         string
		BitsAIAccount = &authentity.Account{
			Username:          "BitsAI",
			CodebaseBitsAIJWT: c.BitsAISecret,
		}
		bitsAIAppIDConfig = c.BitsAIAppID.GetPointer()
	)
	if bitsAIAppIDConfig == nil {
		return nil, nil, ErrAppIDNotFound
	}
	if opt.AppID == "" {
		appConfig, ok := c.BitsAIAppID.GetValue()[c.CodeAIConfig.GetValue().AppName]
		if !ok {
			return nil, nil, ErrAppIDNotFound
		}
		appID = appConfig.AppID
	} else {
		appID = opt.AppID
	}

	if opt.TargetRevision == "" && opt.SourceRevision == "" {
		// get change detail and sort out the latest branch as target branch
		changeDetail, err := c.CodebaseCli.GetChangeDetail(ctx, opt.RepoName, int64(opt.ChangeID), false, codebaseWithJWT(BitsAIAccount))
		if err != nil {
			log.V1.CtxError(ctx, "failed to get change detail for repo %s, err: %v", opt.RepoName, err)
			return nil, nil, err
		}
		if changeDetail == nil || len(changeDetail.PatchSets) == 0 {
			log.V1.CtxError(ctx, "change detail is empty for repo %s", opt.RepoName)
			return nil, nil, errors.New("change detail is empty")
		}
		// assign SourceRevision to the latest commit sha
		sourcePatchSet := changeDetail.PatchSets[len(changeDetail.PatchSets)-1]
		opt.SourceRevision = sourcePatchSet.SHA
		// assign TargetRevision to the base commit sha
		opt.TargetRevision = changeDetail.PatchSets[0].BaseSHA
	}

	// if by MQ triggered automatically, tasks should be only created once.
	if opt.IsMQTriggered != nil && lo.FromPtr(opt.IsMQTriggered) {
		// check if task_key has been created in rds
		taskKey := GetUniqueTaskKey(opt.SourceRevision, opt.TargetRevision)
		POs, err := c.DAO.ListAsyncTasks(ctx, dal.ListAsyncTaskOption{
			TaskType:     lo.ToPtr(entity.TaskTypeCodebaseAIFileReview),
			TaskKey:      lo.ToPtr(taskKey),
			Count:        1,
			Desc:         lo.ToPtr(true),
			SinceMinutes: lo.ToPtr(AutoTriggeredDurPermitTTL),
		})
		if len(POs) > 0 {
			// tasks exist
			log.V1.CtxWarn(ctx, "block automatically mq triggered patchset-review for repo %s, change id %d,"+
				"source revision %s, target revision %s", opt.RepoName, opt.ChangeID, opt.SourceRevision, opt.TargetRevision)
			return nil, nil, ErrRepeatAutoReview
		}

		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			// failed
			log.V1.CtxError(ctx, "list tasks to check if mq triggered tasks are repeated failed")
			return nil, nil, err
		}
	}

	const CRCopilotPattern = "template: cr-copilot"

	// check if cr copilot is already enabled by checking yaml file in .codebase/pipeline/*.yaml
	// get all files and filter in the target dir, get content and check if has cr_copilot pattern
	files, err := c.CodebaseCli.GetAllFiles(ctx, opt.RepoName, opt.TargetRevision, codebaseWithJWT(BitsAIAccount))
	if err != nil {
		// don't break the process if failed to get files
		log.V1.CtxError(ctx, " failed to get all files for repo %s, err: %v", opt.RepoName, err)
	}
	if len(files) != 0 {
		for _, f := range files {
			if strings.HasPrefix(f.Path, ".codebase/pipelines/") {
				if !strings.HasSuffix(f.Name, ".yaml") && !strings.HasSuffix(f.Name, ".yml") {
					continue
				}
				if !strings.Contains(f.Name, "code_review") {
					continue
				}
				fileContent, err := c.CodebaseCli.GetBlobByPath(ctx, opt.RepoName, opt.TargetRevision, f.Path, codebaseWithJWT(BitsAIAccount))
				if err != nil {
					log.V1.CtxError(ctx, "failed to get file content for file %s, err: %v", f.Path, err)
					continue
				}
				if fileContent != nil && strings.Contains(string(fileContent.ContentRaw), CRCopilotPattern) {
					log.V1.CtxInfo(ctx, "cr copilot is already enabled for repo %s, skip", opt.RepoName)
					return nil, nil, errors.New("cr copilot is already enabled")
				}
			}
		}
	}
	return c.CreatePatchsetReview(ctx, service.CreatePatchsetReviewOption{
		AppID:          appID,
		FunctionID:     DefaultAIFileReviewFunctionID,
		ChangeID:       opt.ChangeID,
		RepoName:       opt.RepoName,
		TargetRevision: opt.TargetRevision,
		SourceRevision: opt.SourceRevision,
		Account:        BitsAIAccount,
		IsMQTriggered:  opt.IsMQTriggered,
	})
}

func (c *CodebaseAgent) CreateAIReviewerTask(ctx context.Context, opt service.CreateAIReviewerOption) (*entity.AsyncTask, error) {
	taskKey := GetUniqueTaskKey(opt.SourceRevision, opt.TargetRevision)
	logID, ok := logid.GetLogIDFromCtx(ctx)
	if !ok {
		logID = logid.GenLogID()
	}
	// save a map[string]any params mapping opt as json string and pass in create job
	extraParams := service.AIReviewerParameters{
		AppID:          lo.ToPtr(opt.AppID),
		RepoName:       opt.RepoName,
		TargetRevision: opt.TargetRevision,
		SourceRevision: opt.SourceRevision,
		ChangeID:       opt.ChangeID,
		Locale:         opt.Locale,
	}
	jsonBytes, err := json.Marshal(extraParams)
	if err != nil {
		log.V1.CtxError(ctx, "Error marshaling AI Reviewer params to JSON: err = %v", err)
		return nil, err
	}

	// Parse the JSON into a map[string]any.
	var AIReviewerParams map[string]any
	err = json.Unmarshal(jsonBytes, &AIReviewerParams)
	if err != nil {
		log.V1.CtxError(ctx, "Error unmarshalling JSON to map: err = %v", err)
		return nil, err
	}
	parameters := entity.Parameters{
		SessionID:  opt.SessionID,
		Params:     AIReviewerParams,
		FunctionID: opt.FunctionID,
		LogID:      logID,
	}
	if err != nil {
		log.V1.CtxError(ctx, "failed to marshal params, err: %v", err)
		return nil, err
	}
	po, err := c.DAO.CreateAsyncTask(ctx, dal.CreateAsyncTaskOption{
		TaskType: entity.TaskTypeCodebaseAIReviewer,
		Params:   lo.ToPtr(parameters),
		Status:   entity.StatusCodebaseAIReviewerAdded.String(),
		TaskKey:  lo.ToPtr(taskKey),
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to create async task for AI Reviewer, err: %v", err)
		return nil, err
	}
	if po == nil {
		log.V1.CtxError(ctx, "create AI Reviewer returns nil po")
		return nil, errors.New("failed to create async task")
	}

	return po, nil
}

func (c *CodebaseAgent) cronjobAIReviewer() error {
	var (
		ProcessTimeOutInterval = DefaultProcessTimeoutIntervalMilli * time.Millisecond
		NotStartedTimeOutDur   = DefaultNotFinishedTimeoutDurationMin * time.Minute
		maxQueryPage           = c.CodeAIConfig.GetValue().GlobalENV.PerPageLimit
	)

	if c.CodeAIConfig.GetValue().GlobalENV.ProcessTimeoutIntervalMilli != 0 {
		ProcessTimeOutInterval =
			time.Duration(c.CodeAIConfig.GetValue().GlobalENV.ProcessTimeoutIntervalMilli) * time.Millisecond
	}

	if c.CodeAIConfig.GetValue().GlobalENV.NotFinishedTimeoutDurationMin != 0 {
		NotStartedTimeOutDur =
			time.Duration(c.CodeAIConfig.GetValue().GlobalENV.NotFinishedTimeoutDurationMin) * time.Minute
	}

	go func() {
		ticker := time.NewTicker(ProcessTimeOutInterval)
		ctx := context.Background()
		defer ticker.Stop()
		defer func() {
			if err := recover(); err != nil {
				// log the error and possibly handle it, rather than aborting the program
				log.V1.CtxError(ctx, "recovered cronjobAIReviewer from panic")
			}
		}()
		for range ticker.C {
			// get all ai reviewer tasks if status is added
			for page := 1; page <= maxQueryPage; page++ {
				AIReviewerTasks, err := c.DAO.ListAsyncTasks(ctx, dal.ListAsyncTaskOption{
					TaskType:     lo.ToPtr(entity.TaskTypeCodebaseAIReviewer),
					Status:       lo.ToPtr(entity.StatusCodebaseAIReviewerAdded.String()),
					Count:        c.CodeAIConfig.GetValue().GlobalENV.PerPageLimit,
					Offset:       (page - 1) * c.CodeAIConfig.GetValue().GlobalENV.PerPageLimit,
					SinceMinutes: lo.ToPtr(NotStartedTimeOutDur),
				})
				if err != nil {
					log.V1.CtxError(ctx, "failed to list async tasks for AI Reviewer, err: %v", err)
					continue
				}

				allTaskKeyMap := make(map[string]*entity.AsyncTask)
				for _, task := range AIReviewerTasks {
					if v, exist := allTaskKeyMap[task.TaskKey]; exist {
						// compare and keep the latest one
						if task.CreatedAt.After(v.CreatedAt) {
							allTaskKeyMap[task.TaskKey] = task
						}
					} else {
						allTaskKeyMap[task.TaskKey] = task
					}
				}

				for key := range allTaskKeyMap {
					// processing this key -- redis
					// check if po.ID task has started
					sID := "processing_reviewer_" + entity.StatusCodebaseAIReviewerAdded.String() + "_" + key
					_, err := c.RedisClient.Get(ctx, sID)
					if err != nil {
						if errors.Is(err, redis2.Nil) {
							// not found, and ignore potential error to avoid break the main pipeline.
							log.V1.CtxInfo(ctx, "get redis value for key %s not found: %v", sID, err)
							_ = c.RedisClient.Set(ctx, sID, true, time.Second*60)
						} else {
							// ignore potential error to avoid break the main pipeline.
							log.V1.CtxInfo(ctx, "get redis value for key %s failed: %v", sID, err)
						}
					} else {
						// found, skip this key
						log.V1.CtxInfo(ctx, "key %s is processing, skip", key)
						continue
					}

					// get all ai file review tasks
					AIFileReviewTasks, err := c.DAO.ListAsyncTasks(ctx, dal.ListAsyncTaskOption{
						TaskType: lo.ToPtr(entity.TaskTypeCodebaseAIFileReview),
						TaskKey:  lo.ToPtr(key),
						Desc:     lo.ToPtr(true),
					})
					if err != nil {
						log.V1.CtxError(ctx, "failed to list async tasks for AI File Review, err: %v", err)
						continue
					}
					if len(AIFileReviewTasks) == 0 {
						log.V1.CtxError(ctx, "no AI File Review tasks for task key %s", key)
						continue
					}
					if AIFileReviewTasks[0].Params == nil {
						log.V1.CtxError(ctx, "no params for task key %s", key)
						continue
					}
					// only keep the tasks with latest session
					sessionID := AIFileReviewTasks[0].Params.SessionID
					createdTime := AIFileReviewTasks[0].CreatedAt
					latestTasks := make([]*entity.AsyncTask, 0)
					for _, t := range AIFileReviewTasks {
						if t.Params != nil && t.Params.SessionID == sessionID {
							latestTasks = append(latestTasks, t)
						}
					}

					if len(latestTasks) == 0 {
						log.V1.CtxError(ctx, "no latest tasks for task key %s", key)
						continue
					}

					// check if timeout
					// createdTime is not exactly same with AI Reviewer's Task create time, but we still keep using
					// NotStartedTimeOutDur. No big deal, the actual timeout will be more tolerant.
					isTimeOut := time.Since(createdTime) > NotStartedTimeOutDur

					finishWithComment := false
					if !isTimeOut {
						// check if any task have finished and with thread_id(commented) then skip, otherwise, publish LGTM
						unfinished := false
						for _, t := range latestTasks {
							if t.Status == entity.StatusCodebaseAIFileReviewFinished.String() {
								// parse t.Metadata
								if t.Metadata != nil {
									metadata, err := getOptionFromVars[AIFileReviewMetadata](t.Metadata)
									if err != nil {
										log.V1.CtxWarn(ctx, "failed to get options from metadata, err = %v", err)
										continue
									}
									if len(metadata.ThreadIDs) > 0 {
										// finish and commented
										finishWithComment = true
										break
									}
								}
							} else if t.Status != entity.StatusCodebaseAIFileReviewCancelled.String() {
								unfinished = true
								break
							}
						}
						if finishWithComment || unfinished {
							// already commented or unfinished, just ignore
							continue
						}
					}

					// process accordingly with changing status to commented first
					_, err = c.DAO.UpdateAsyncStatus(ctx, dal.UpdateAsyncTaskStatusOption{
						ID:     allTaskKeyMap[key].ID,
						Status: lo.ToPtr(entity.StatusCodebaseAIReviewerCommented.String()),
					})
					if err != nil {
						log.V1.CtxError(ctx, "failed to update async task status, err: %v", err)
						continue
					}

					if isTimeOut || !finishWithComment {
						// publish comments with no valid reviews
						taskParams, err := getOptionFromVars[service.AIFileReviewInvokeParams](latestTasks[0].Params.Params)
						if err != nil {
							log.V1.CtxWarn(ctx, "failed to get context variables from draftedTaskGroup[0], err = %v", err)
						}
						repoName := taskParams.RepoName
						changeID := taskParams.ChangeID
						goodPatternEmoj := []string{"😎", "😌", "🤓", "🥳", "😇", "😆", "😄", "👍", "🉑", "🥇", "🆗"}
						createReviewOptions := codebase.CreateReviewsOptions{
							Content: NoReviewComment + goodPatternEmoj[rand.Intn(len(goodPatternEmoj))],
							Event:   "comment",
							Sha:     taskParams.SourceRev,
						}
						err = c.CodebaseCli.CreateReviews(ctx, repoName,
							int64(changeID),
							createReviewOptions, codebase.WithBitsAIServiceJWT(c.BitsAISecret))
						if err != nil {
							log.V1.CtxError(ctx, "failed to created reviews for ai reviewer: %v", err)
							continue
						}
					}
				}
			}
		}
	}()
	return nil
}

func codebaseWithJWT(account *authentity.Account) func(option *hertz.ReqOption) {
	if account.Username == "BitsAI" {
		return codebase.WithBitsAIServiceJWT(account.CodebaseBitsAIJWT)
	}
	return codebase.WithUserJWT(account.CodebaseUserJWT)
}
