// Code generated by thriftgo (0.4.1). DO NOT EDIT.

package nextagent

import (
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/common"
	"fmt"
)

const (
	ArtifactTypeFile = "file"

	ArtifactTypeCode = "code"

	ArtifactTypeLink = "link"

	ArtifactTypeImage = "image"

	ArtifactTypeLogs = "logs"

	ArtifactTypeResult = "result"

	ArtifactStatusCreating = "creating"

	ArtifactStatusDraft = "draft"

	ArtifactStatusCompleted = "completed"

	ArtifactSourceUser = "user"

	ArtifactSourceAgent = "agent"
)

type ArtifactType = string

type ArtifactStatus = string

type ArtifactSource = string

type Artifact struct {
	ID        string               `thrift:"ID,1,required" json:"id"`
	Type      ArtifactType         `thrift:"Type,2,required" json:"type"`
	Status    ArtifactStatus       `thrift:"Status,3,required" json:"status"`
	Source    ArtifactSource       `thrift:"Source,4,required" json:"source"`
	Key       string               `thrift:"Key,5,required" json:"key"`
	Version   int32                `thrift:"Version,6,required" json:"version"`
	FileMetas []*FileMeta          `thrift:"FileMetas,7,required" json:"file_metas"`
	CreatedAt string               `thrift:"CreatedAt,8,required" json:"created_at"`
	Metadata  common.JsonVariables `thrift:"Metadata,9,required" json:"metadata"`
}

func NewArtifact() *Artifact {
	return &Artifact{}
}

func (p *Artifact) InitDefault() {
}

func (p *Artifact) GetID() (v string) {
	return p.ID
}

func (p *Artifact) GetType() (v ArtifactType) {
	return p.Type
}

func (p *Artifact) GetStatus() (v ArtifactStatus) {
	return p.Status
}

func (p *Artifact) GetSource() (v ArtifactSource) {
	return p.Source
}

func (p *Artifact) GetKey() (v string) {
	return p.Key
}

func (p *Artifact) GetVersion() (v int32) {
	return p.Version
}

func (p *Artifact) GetFileMetas() (v []*FileMeta) {
	return p.FileMetas
}

func (p *Artifact) GetCreatedAt() (v string) {
	return p.CreatedAt
}

func (p *Artifact) GetMetadata() (v common.JsonVariables) {
	return p.Metadata
}

func (p *Artifact) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Artifact(%+v)", *p)
}

type FileMeta struct {
	Name      string `thrift:"Name,1,required" json:"name"`
	Size      int64  `thrift:"Size,2,required" json:"size"`
	Content   string `thrift:"Content,3,required" json:"content"`
	LarkToken string `thrift:"LarkToken,4,required" json:"lark_token"`
	ImageXURI string `thrift:"ImageXURI,5,required" json:"imagex_uri"`
	// 目前一个 Artifact 可能会包含不同的业务类型的文件, 以该类型为准
	Type ArtifactType `thrift:"Type,6,required" json:"type"`
	// 子类型，如果是 Code 对应不同的语言类型，其他则是文件名后缀；如果是Link，则对应的是 link 类型（参考 ArtifactLinkSubType）
	SubType         string       `thrift:"SubType,7,required" json:"sub_type"`
	LinkContent     *LinkContent `thrift:"LinkContent,8,optional" json:"link_content,omitempty"`
	PatchFileResult []*FilePatch `thrift:"PatchFileResult,9,optional" json:"patch_file_result,omitempty"`
}

func NewFileMeta() *FileMeta {
	return &FileMeta{}
}

func (p *FileMeta) InitDefault() {
}

func (p *FileMeta) GetName() (v string) {
	return p.Name
}

func (p *FileMeta) GetSize() (v int64) {
	return p.Size
}

func (p *FileMeta) GetContent() (v string) {
	return p.Content
}

func (p *FileMeta) GetLarkToken() (v string) {
	return p.LarkToken
}

func (p *FileMeta) GetImageXURI() (v string) {
	return p.ImageXURI
}

func (p *FileMeta) GetType() (v ArtifactType) {
	return p.Type
}

func (p *FileMeta) GetSubType() (v string) {
	return p.SubType
}

var FileMeta_LinkContent_DEFAULT *LinkContent

func (p *FileMeta) GetLinkContent() (v *LinkContent) {
	if !p.IsSetLinkContent() {
		return FileMeta_LinkContent_DEFAULT
	}
	return p.LinkContent
}

var FileMeta_PatchFileResult_DEFAULT []*FilePatch

func (p *FileMeta) GetPatchFileResult() (v []*FilePatch) {
	if !p.IsSetPatchFileResult() {
		return FileMeta_PatchFileResult_DEFAULT
	}
	return p.PatchFileResult
}

func (p *FileMeta) IsSetLinkContent() bool {
	return p.LinkContent != nil
}

func (p *FileMeta) IsSetPatchFileResult() bool {
	return p.PatchFileResult != nil
}

func (p *FileMeta) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FileMeta(%+v)", *p)
}

type LinkContent struct {
	URL         string `thrift:"URL,1" json:"url"`
	Title       string `thrift:"Title,2" json:"title"`
	Description string `thrift:"Description,3" json:"description"`
}

func NewLinkContent() *LinkContent {
	return &LinkContent{}
}

func (p *LinkContent) InitDefault() {
}

func (p *LinkContent) GetURL() (v string) {
	return p.URL
}

func (p *LinkContent) GetTitle() (v string) {
	return p.Title
}

func (p *LinkContent) GetDescription() (v string) {
	return p.Description
}

func (p *LinkContent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("LinkContent(%+v)", *p)
}

type CreateArtifactRequest struct {
	SessionID string `thrift:"SessionID,1,required" json:"session_id"`
	// 目前对前端支持 file, code, image 类型
	Type     ArtifactType         `thrift:"Type,2,required" json:"type"`
	Key      string               `thrift:"Key,3,required" json:"key"`
	Metadata common.JsonVariables `thrift:"Metadata,4,required" json:"metadata"`
}

func NewCreateArtifactRequest() *CreateArtifactRequest {
	return &CreateArtifactRequest{}
}

func (p *CreateArtifactRequest) InitDefault() {
}

func (p *CreateArtifactRequest) GetSessionID() (v string) {
	return p.SessionID
}

func (p *CreateArtifactRequest) GetType() (v ArtifactType) {
	return p.Type
}

func (p *CreateArtifactRequest) GetKey() (v string) {
	return p.Key
}

func (p *CreateArtifactRequest) GetMetadata() (v common.JsonVariables) {
	return p.Metadata
}

func (p *CreateArtifactRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateArtifactRequest(%+v)", *p)
}

type CreateArtifactResponse struct {
	Artifact *Artifact `thrift:"Artifact,1,required" json:"artifact"`
}

func NewCreateArtifactResponse() *CreateArtifactResponse {
	return &CreateArtifactResponse{}
}

func (p *CreateArtifactResponse) InitDefault() {
}

var CreateArtifactResponse_Artifact_DEFAULT *Artifact

func (p *CreateArtifactResponse) GetArtifact() (v *Artifact) {
	if !p.IsSetArtifact() {
		return CreateArtifactResponse_Artifact_DEFAULT
	}
	return p.Artifact
}

func (p *CreateArtifactResponse) IsSetArtifact() bool {
	return p.Artifact != nil
}

func (p *CreateArtifactResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateArtifactResponse(%+v)", *p)
}

type GetArtifactRequest struct {
	ID string `thrift:"ID,1,required" json:"id,required" path:"artifact_id,required"`
}

func NewGetArtifactRequest() *GetArtifactRequest {
	return &GetArtifactRequest{}
}

func (p *GetArtifactRequest) InitDefault() {
}

func (p *GetArtifactRequest) GetID() (v string) {
	return p.ID
}

func (p *GetArtifactRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetArtifactRequest(%+v)", *p)
}

type GetArtifactResponse struct {
	Artifact *Artifact `thrift:"Artifact,1,required" json:"artifact"`
}

func NewGetArtifactResponse() *GetArtifactResponse {
	return &GetArtifactResponse{}
}

func (p *GetArtifactResponse) InitDefault() {
}

var GetArtifactResponse_Artifact_DEFAULT *Artifact

func (p *GetArtifactResponse) GetArtifact() (v *Artifact) {
	if !p.IsSetArtifact() {
		return GetArtifactResponse_Artifact_DEFAULT
	}
	return p.Artifact
}

func (p *GetArtifactResponse) IsSetArtifact() bool {
	return p.Artifact != nil
}

func (p *GetArtifactResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetArtifactResponse(%+v)", *p)
}

type UpdateArtifactRequest struct {
	ID        string                `thrift:"ID,1,required" json:"id,required" path:"artifact_id,required"`
	Status    *ArtifactStatus       `thrift:"Status,2,optional" json:"status"`
	Metadata  *common.JsonVariables `thrift:"Metadata,3,optional" json:"metadata"`
	SessionID *string               `thrift:"SessionID,4,optional" json:"session_id"`
}

func NewUpdateArtifactRequest() *UpdateArtifactRequest {
	return &UpdateArtifactRequest{}
}

func (p *UpdateArtifactRequest) InitDefault() {
}

func (p *UpdateArtifactRequest) GetID() (v string) {
	return p.ID
}

var UpdateArtifactRequest_Status_DEFAULT ArtifactStatus

func (p *UpdateArtifactRequest) GetStatus() (v ArtifactStatus) {
	if !p.IsSetStatus() {
		return UpdateArtifactRequest_Status_DEFAULT
	}
	return *p.Status
}

var UpdateArtifactRequest_Metadata_DEFAULT common.JsonVariables

func (p *UpdateArtifactRequest) GetMetadata() (v common.JsonVariables) {
	if !p.IsSetMetadata() {
		return UpdateArtifactRequest_Metadata_DEFAULT
	}
	return *p.Metadata
}

var UpdateArtifactRequest_SessionID_DEFAULT string

func (p *UpdateArtifactRequest) GetSessionID() (v string) {
	if !p.IsSetSessionID() {
		return UpdateArtifactRequest_SessionID_DEFAULT
	}
	return *p.SessionID
}

func (p *UpdateArtifactRequest) IsSetStatus() bool {
	return p.Status != nil
}

func (p *UpdateArtifactRequest) IsSetMetadata() bool {
	return p.Metadata != nil
}

func (p *UpdateArtifactRequest) IsSetSessionID() bool {
	return p.SessionID != nil
}

func (p *UpdateArtifactRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateArtifactRequest(%+v)", *p)
}

type UpdateArtifactResponse struct {
	Artifact *Artifact `thrift:"Artifact,1,required" json:"artifact"`
}

func NewUpdateArtifactResponse() *UpdateArtifactResponse {
	return &UpdateArtifactResponse{}
}

func (p *UpdateArtifactResponse) InitDefault() {
}

var UpdateArtifactResponse_Artifact_DEFAULT *Artifact

func (p *UpdateArtifactResponse) GetArtifact() (v *Artifact) {
	if !p.IsSetArtifact() {
		return UpdateArtifactResponse_Artifact_DEFAULT
	}
	return p.Artifact
}

func (p *UpdateArtifactResponse) IsSetArtifact() bool {
	return p.Artifact != nil
}

func (p *UpdateArtifactResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateArtifactResponse(%+v)", *p)
}

// multipart/form-data file upload
type UploadArtifactRequest struct {
	ID      string `thrift:"ID,1,required" json:"id,required" path:"artifact_id,required"`
	Path    string `thrift:"Path,2,required" form:"path,required" json:"path,required"`
	Content []byte `thrift:"Content,3,required" form:"content,required" json:"content,required"`
}

func NewUploadArtifactRequest() *UploadArtifactRequest {
	return &UploadArtifactRequest{}
}

func (p *UploadArtifactRequest) InitDefault() {
}

func (p *UploadArtifactRequest) GetID() (v string) {
	return p.ID
}

func (p *UploadArtifactRequest) GetPath() (v string) {
	return p.Path
}

func (p *UploadArtifactRequest) GetContent() (v []byte) {
	return p.Content
}

func (p *UploadArtifactRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UploadArtifactRequest(%+v)", *p)
}

type UploadArtifactStreamRequest struct {
	ID   string `thrift:"ID,1,required" json:"id,required" path:"artifact_id,required"`
	Path string `thrift:"Path,2,required" json:"path,required" query:"path,required"`
	Size int64  `thrift:"Size,3,required" json:"size,required" query:"size,required"`
}

func NewUploadArtifactStreamRequest() *UploadArtifactStreamRequest {
	return &UploadArtifactStreamRequest{}
}

func (p *UploadArtifactStreamRequest) InitDefault() {
}

func (p *UploadArtifactStreamRequest) GetID() (v string) {
	return p.ID
}

func (p *UploadArtifactStreamRequest) GetPath() (v string) {
	return p.Path
}

func (p *UploadArtifactStreamRequest) GetSize() (v int64) {
	return p.Size
}

func (p *UploadArtifactStreamRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UploadArtifactStreamRequest(%+v)", *p)
}

type UploadArtifactResponse struct {
	Artifact *Artifact `thrift:"Artifact,1,required" json:"artifact"`
}

func NewUploadArtifactResponse() *UploadArtifactResponse {
	return &UploadArtifactResponse{}
}

func (p *UploadArtifactResponse) InitDefault() {
}

var UploadArtifactResponse_Artifact_DEFAULT *Artifact

func (p *UploadArtifactResponse) GetArtifact() (v *Artifact) {
	if !p.IsSetArtifact() {
		return UploadArtifactResponse_Artifact_DEFAULT
	}
	return p.Artifact
}

func (p *UploadArtifactResponse) IsSetArtifact() bool {
	return p.Artifact != nil
}

func (p *UploadArtifactResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UploadArtifactResponse(%+v)", *p)
}

type RetrieveArtifactFilesRequest struct {
	ID      string   `thrift:"ID,1,required" json:"id,required" path:"artifact_id,required"`
	Files   []string `thrift:"Files,2,required" json:"files"`
	Preview *bool    `thrift:"preview,3,optional" json:"preview"`
}

func NewRetrieveArtifactFilesRequest() *RetrieveArtifactFilesRequest {
	return &RetrieveArtifactFilesRequest{}
}

func (p *RetrieveArtifactFilesRequest) InitDefault() {
}

func (p *RetrieveArtifactFilesRequest) GetID() (v string) {
	return p.ID
}

func (p *RetrieveArtifactFilesRequest) GetFiles() (v []string) {
	return p.Files
}

var RetrieveArtifactFilesRequest_Preview_DEFAULT bool

func (p *RetrieveArtifactFilesRequest) GetPreview() (v bool) {
	if !p.IsSetPreview() {
		return RetrieveArtifactFilesRequest_Preview_DEFAULT
	}
	return *p.Preview
}

func (p *RetrieveArtifactFilesRequest) IsSetPreview() bool {
	return p.Preview != nil
}

func (p *RetrieveArtifactFilesRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RetrieveArtifactFilesRequest(%+v)", *p)
}

type RetrieveArtifactFilesResponse struct {
	Files []*FileMeta `thrift:"Files,1,required" json:"files"`
}

func NewRetrieveArtifactFilesResponse() *RetrieveArtifactFilesResponse {
	return &RetrieveArtifactFilesResponse{}
}

func (p *RetrieveArtifactFilesResponse) InitDefault() {
}

func (p *RetrieveArtifactFilesResponse) GetFiles() (v []*FileMeta) {
	return p.Files
}

func (p *RetrieveArtifactFilesResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RetrieveArtifactFilesResponse(%+v)", *p)
}

type DownloadArtifactFileRequest struct {
	ID   string `thrift:"ID,1,required" json:"id,required" path:"artifact_id,required"`
	Path string `thrift:"Path,2,required" json:"path,required" path:"path,required"`
	// 是否返回原始文件内容，默认会替换文件内部分内容，如 ::cite 标签
	Raw *bool `thrift:"Raw,3,optional" json:"raw,omitempty" query:"raw"`
	// 流式返回
	Stream *bool `thrift:"Stream,4,optional" json:"stream,omitempty" query:"stream"`
}

func NewDownloadArtifactFileRequest() *DownloadArtifactFileRequest {
	return &DownloadArtifactFileRequest{}
}

func (p *DownloadArtifactFileRequest) InitDefault() {
}

func (p *DownloadArtifactFileRequest) GetID() (v string) {
	return p.ID
}

func (p *DownloadArtifactFileRequest) GetPath() (v string) {
	return p.Path
}

var DownloadArtifactFileRequest_Raw_DEFAULT bool

func (p *DownloadArtifactFileRequest) GetRaw() (v bool) {
	if !p.IsSetRaw() {
		return DownloadArtifactFileRequest_Raw_DEFAULT
	}
	return *p.Raw
}

var DownloadArtifactFileRequest_Stream_DEFAULT bool

func (p *DownloadArtifactFileRequest) GetStream() (v bool) {
	if !p.IsSetStream() {
		return DownloadArtifactFileRequest_Stream_DEFAULT
	}
	return *p.Stream
}

func (p *DownloadArtifactFileRequest) IsSetRaw() bool {
	return p.Raw != nil
}

func (p *DownloadArtifactFileRequest) IsSetStream() bool {
	return p.Stream != nil
}

func (p *DownloadArtifactFileRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DownloadArtifactFileRequest(%+v)", *p)
}

type ArtifactFiles struct {
	ID    string   `thrift:"ID,1,required" json:"artifact_id"`
	Paths []string `thrift:"Paths,2,required" json:"paths"`
}

func NewArtifactFiles() *ArtifactFiles {
	return &ArtifactFiles{}
}

func (p *ArtifactFiles) InitDefault() {
}

func (p *ArtifactFiles) GetID() (v string) {
	return p.ID
}

func (p *ArtifactFiles) GetPaths() (v []string) {
	return p.Paths
}

func (p *ArtifactFiles) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ArtifactFiles(%+v)", *p)
}

type DownloadArtifactBatchRequest struct {
	Artifacts []*ArtifactFiles `thrift:"Artifacts,1,required" json:"artifacts"`
	SessionID *string          `thrift:"SessionID,2,optional" json:"session_id"`
	ReplayID  *string          `thrift:"ReplayID,3,optional" json:"replay_id"`
}

func NewDownloadArtifactBatchRequest() *DownloadArtifactBatchRequest {
	return &DownloadArtifactBatchRequest{}
}

func (p *DownloadArtifactBatchRequest) InitDefault() {
}

func (p *DownloadArtifactBatchRequest) GetArtifacts() (v []*ArtifactFiles) {
	return p.Artifacts
}

var DownloadArtifactBatchRequest_SessionID_DEFAULT string

func (p *DownloadArtifactBatchRequest) GetSessionID() (v string) {
	if !p.IsSetSessionID() {
		return DownloadArtifactBatchRequest_SessionID_DEFAULT
	}
	return *p.SessionID
}

var DownloadArtifactBatchRequest_ReplayID_DEFAULT string

func (p *DownloadArtifactBatchRequest) GetReplayID() (v string) {
	if !p.IsSetReplayID() {
		return DownloadArtifactBatchRequest_ReplayID_DEFAULT
	}
	return *p.ReplayID
}

func (p *DownloadArtifactBatchRequest) IsSetSessionID() bool {
	return p.SessionID != nil
}

func (p *DownloadArtifactBatchRequest) IsSetReplayID() bool {
	return p.ReplayID != nil
}

func (p *DownloadArtifactBatchRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DownloadArtifactBatchRequest(%+v)", *p)
}

type ListArtifactsRequest struct {
	SessionID *string `thrift:"SessionID,1,optional" json:"session_id,omitempty" query:"session_id"`
	ReplayID  *string `thrift:"ReplayID,2,optional" json:"replay_id,omitempty" query:"replay_id"`
	Display   *bool   `thrift:"display,3,optional" json:"display,omitempty" query:"display"`
}

func NewListArtifactsRequest() *ListArtifactsRequest {
	return &ListArtifactsRequest{}
}

func (p *ListArtifactsRequest) InitDefault() {
}

var ListArtifactsRequest_SessionID_DEFAULT string

func (p *ListArtifactsRequest) GetSessionID() (v string) {
	if !p.IsSetSessionID() {
		return ListArtifactsRequest_SessionID_DEFAULT
	}
	return *p.SessionID
}

var ListArtifactsRequest_ReplayID_DEFAULT string

func (p *ListArtifactsRequest) GetReplayID() (v string) {
	if !p.IsSetReplayID() {
		return ListArtifactsRequest_ReplayID_DEFAULT
	}
	return *p.ReplayID
}

var ListArtifactsRequest_Display_DEFAULT bool

func (p *ListArtifactsRequest) GetDisplay() (v bool) {
	if !p.IsSetDisplay() {
		return ListArtifactsRequest_Display_DEFAULT
	}
	return *p.Display
}

func (p *ListArtifactsRequest) IsSetSessionID() bool {
	return p.SessionID != nil
}

func (p *ListArtifactsRequest) IsSetReplayID() bool {
	return p.ReplayID != nil
}

func (p *ListArtifactsRequest) IsSetDisplay() bool {
	return p.Display != nil
}

func (p *ListArtifactsRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListArtifactsRequest(%+v)", *p)
}

type ListArtifactsResponse struct {
	Artifacts []*Artifact `thrift:"Artifacts,1,required" json:"artifacts"`
}

func NewListArtifactsResponse() *ListArtifactsResponse {
	return &ListArtifactsResponse{}
}

func (p *ListArtifactsResponse) InitDefault() {
}

func (p *ListArtifactsResponse) GetArtifacts() (v []*Artifact) {
	return p.Artifacts
}

func (p *ListArtifactsResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListArtifactsResponse(%+v)", *p)
}

type UpdateArtifactFileRequest struct {
	ID   string `thrift:"ID,1,required" json:"id,required" path:"artifact_id,required"`
	Path string `thrift:"Path,2,required" json:"path,required" path:"path,required"`
	// UploadLark 上传 Lark 会自动给 Session Owner 授权
	UploadLark *bool `thrift:"UploadLark,3,optional" json:"upload_lark"`
	// GrantPermission 会给该 Artifact 所属 Session 的 Owner 授权
	GrantPermission *bool `thrift:"GrantPermission,4,optional" json:"grant_permission"`
}

func NewUpdateArtifactFileRequest() *UpdateArtifactFileRequest {
	return &UpdateArtifactFileRequest{}
}

func (p *UpdateArtifactFileRequest) InitDefault() {
}

func (p *UpdateArtifactFileRequest) GetID() (v string) {
	return p.ID
}

func (p *UpdateArtifactFileRequest) GetPath() (v string) {
	return p.Path
}

var UpdateArtifactFileRequest_UploadLark_DEFAULT bool

func (p *UpdateArtifactFileRequest) GetUploadLark() (v bool) {
	if !p.IsSetUploadLark() {
		return UpdateArtifactFileRequest_UploadLark_DEFAULT
	}
	return *p.UploadLark
}

var UpdateArtifactFileRequest_GrantPermission_DEFAULT bool

func (p *UpdateArtifactFileRequest) GetGrantPermission() (v bool) {
	if !p.IsSetGrantPermission() {
		return UpdateArtifactFileRequest_GrantPermission_DEFAULT
	}
	return *p.GrantPermission
}

func (p *UpdateArtifactFileRequest) IsSetUploadLark() bool {
	return p.UploadLark != nil
}

func (p *UpdateArtifactFileRequest) IsSetGrantPermission() bool {
	return p.GrantPermission != nil
}

func (p *UpdateArtifactFileRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateArtifactFileRequest(%+v)", *p)
}

type UpdateArtifactFileResponse struct {
	Artifact *Artifact `thrift:"Artifact,1,required" json:"artifact"`
}

func NewUpdateArtifactFileResponse() *UpdateArtifactFileResponse {
	return &UpdateArtifactFileResponse{}
}

func (p *UpdateArtifactFileResponse) InitDefault() {
}

var UpdateArtifactFileResponse_Artifact_DEFAULT *Artifact

func (p *UpdateArtifactFileResponse) GetArtifact() (v *Artifact) {
	if !p.IsSetArtifact() {
		return UpdateArtifactFileResponse_Artifact_DEFAULT
	}
	return p.Artifact
}

func (p *UpdateArtifactFileResponse) IsSetArtifact() bool {
	return p.Artifact != nil
}

func (p *UpdateArtifactFileResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateArtifactFileResponse(%+v)", *p)
}
