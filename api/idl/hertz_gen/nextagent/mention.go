// Code generated by thriftgo (0.4.2). DO NOT EDIT.

package nextagent

import (
	"fmt"
)

const (
	MentionTypeUnknown = "unknown"

	MentionTypeKnowledgeBase = "knowledgebase"

	MentionTypeRepository = "repository"

	MentionTypeRepositoryBranch = "repository_branch"

	MentionTypeRepositoryDir = "repository_dir"

	MentionTypeRepositoryFile = "repository_file"

	MentionTypeAttachment = "attachment"

	MentionOrderByUnknown = "unknown"

	MentionOrderByHeat = "heat"
)

type MentionType = string

type MentionOrderBy = string

type Mention struct {
	ID                string             `thrift:"ID,1,required" json:"id"`
	Type              MentionType        `thrift:"Type,2,required" json:"type"`
	CodebaseMention   *CodebaseMention   `thrift:"CodebaseMention,3,optional" json:"codebase_mention"`
	KnowledgeMention  *KnowledgeMention  `thrift:"KnowledgeMention,4,optional" json:"knowledge_mention"`
	AttachmentMention *AttachmentMention `thrift:"AttachmentMention,5,optional" json:"attachment_mention"`
}

func NewMention() *Mention {
	return &Mention{}
}

func (p *Mention) InitDefault() {
}

func (p *Mention) GetID() (v string) {
	return p.ID
}

func (p *Mention) GetType() (v MentionType) {
	return p.Type
}

var Mention_CodebaseMention_DEFAULT *CodebaseMention

func (p *Mention) GetCodebaseMention() (v *CodebaseMention) {
	if !p.IsSetCodebaseMention() {
		return Mention_CodebaseMention_DEFAULT
	}
	return p.CodebaseMention
}

var Mention_KnowledgeMention_DEFAULT *KnowledgeMention

func (p *Mention) GetKnowledgeMention() (v *KnowledgeMention) {
	if !p.IsSetKnowledgeMention() {
		return Mention_KnowledgeMention_DEFAULT
	}
	return p.KnowledgeMention
}

var Mention_AttachmentMention_DEFAULT *AttachmentMention

func (p *Mention) GetAttachmentMention() (v *AttachmentMention) {
	if !p.IsSetAttachmentMention() {
		return Mention_AttachmentMention_DEFAULT
	}
	return p.AttachmentMention
}

func (p *Mention) IsSetCodebaseMention() bool {
	return p.CodebaseMention != nil
}

func (p *Mention) IsSetKnowledgeMention() bool {
	return p.KnowledgeMention != nil
}

func (p *Mention) IsSetAttachmentMention() bool {
	return p.AttachmentMention != nil
}

func (p *Mention) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Mention(%+v)", *p)
}

type CodebaseMention struct {
	RepoName  string  `thrift:"RepoName,1,required" json:"repo_name"`
	Branch    *string `thrift:"Branch,2,optional" json:"branch"`
	Tag       *string `thrift:"Tag,3,optional" json:"tag"`
	FilePath  *string `thrift:"FilePath,4,optional" json:"file_path"`
	Directory *string `thrift:"Directory,5,optional" json:"directory"`
}

func NewCodebaseMention() *CodebaseMention {
	return &CodebaseMention{}
}

func (p *CodebaseMention) InitDefault() {
}

func (p *CodebaseMention) GetRepoName() (v string) {
	return p.RepoName
}

var CodebaseMention_Branch_DEFAULT string

func (p *CodebaseMention) GetBranch() (v string) {
	if !p.IsSetBranch() {
		return CodebaseMention_Branch_DEFAULT
	}
	return *p.Branch
}

var CodebaseMention_Tag_DEFAULT string

func (p *CodebaseMention) GetTag() (v string) {
	if !p.IsSetTag() {
		return CodebaseMention_Tag_DEFAULT
	}
	return *p.Tag
}

var CodebaseMention_FilePath_DEFAULT string

func (p *CodebaseMention) GetFilePath() (v string) {
	if !p.IsSetFilePath() {
		return CodebaseMention_FilePath_DEFAULT
	}
	return *p.FilePath
}

var CodebaseMention_Directory_DEFAULT string

func (p *CodebaseMention) GetDirectory() (v string) {
	if !p.IsSetDirectory() {
		return CodebaseMention_Directory_DEFAULT
	}
	return *p.Directory
}

func (p *CodebaseMention) IsSetBranch() bool {
	return p.Branch != nil
}

func (p *CodebaseMention) IsSetTag() bool {
	return p.Tag != nil
}

func (p *CodebaseMention) IsSetFilePath() bool {
	return p.FilePath != nil
}

func (p *CodebaseMention) IsSetDirectory() bool {
	return p.Directory != nil
}

func (p *CodebaseMention) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CodebaseMention(%+v)", *p)
}

type KnowledgeMention struct {
	KnowledgeBaseID string `thrift:"KnowledgeBaseID,1,required" json:"knowledge_base_id"`
	// 文档ID
	DocumentID string  `thrift:"DocumentID,2,required" json:"document_id"`
	Title      *string `thrift:"Title,3,optional" json:"title"`
	URL        *string `thrift:"URL,4,optional" json:"url"`
}

func NewKnowledgeMention() *KnowledgeMention {
	return &KnowledgeMention{}
}

func (p *KnowledgeMention) InitDefault() {
}

func (p *KnowledgeMention) GetKnowledgeBaseID() (v string) {
	return p.KnowledgeBaseID
}

func (p *KnowledgeMention) GetDocumentID() (v string) {
	return p.DocumentID
}

var KnowledgeMention_Title_DEFAULT string

func (p *KnowledgeMention) GetTitle() (v string) {
	if !p.IsSetTitle() {
		return KnowledgeMention_Title_DEFAULT
	}
	return *p.Title
}

var KnowledgeMention_URL_DEFAULT string

func (p *KnowledgeMention) GetURL() (v string) {
	if !p.IsSetURL() {
		return KnowledgeMention_URL_DEFAULT
	}
	return *p.URL
}

func (p *KnowledgeMention) IsSetTitle() bool {
	return p.Title != nil
}

func (p *KnowledgeMention) IsSetURL() bool {
	return p.URL != nil
}

func (p *KnowledgeMention) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("KnowledgeMention(%+v)", *p)
}

type AttachmentMention struct {
	ArtifactID string `thrift:"ArtifactID,1,required" json:"artifact_id"`
	Path       string `thrift:"Path,2,required" json:"path"`
}

func NewAttachmentMention() *AttachmentMention {
	return &AttachmentMention{}
}

func (p *AttachmentMention) InitDefault() {
}

func (p *AttachmentMention) GetArtifactID() (v string) {
	return p.ArtifactID
}

func (p *AttachmentMention) GetPath() (v string) {
	return p.Path
}

func (p *AttachmentMention) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AttachmentMention(%+v)", *p)
}

type SearchMentionsRequest struct {
	// 搜索知识库必须传
	SpaceID *string `thrift:"SpaceID,1,optional" json:"space_id,omitempty" query:"space_id"`
	// 不填的情况下，服务端返回一个默认的数据
	Type *MentionType `thrift:"type,2,optional" json:"type,omitempty" query:"type"`
	// 支持 id or path 两种
	RepositoryID     *string         `thrift:"RepositoryID,3,optional" json:"repository_id,omitempty" query:"repository_id"`
	RepositoryBranch *string         `thrift:"RepositoryBranch,4,optional" json:"repository_branch,omitempty" query:"repository_branch"`
	Query            *string         `thrift:"Query,5,optional" json:"query,omitempty" query:"query"`
	Limit            *int32          `thrift:"Limit,6,optional" json:"limit,omitempty" query:"limit"`
	NextID           *string         `thrift:"NextID,7,optional" json:"next_id,omitempty" query:"next_id"`
	OrderBy          *MentionOrderBy `thrift:"OrderBy,8,optional" json:"order_by,omitempty" query:"order_by"`
}

func NewSearchMentionsRequest() *SearchMentionsRequest {
	return &SearchMentionsRequest{}
}

func (p *SearchMentionsRequest) InitDefault() {
}

var SearchMentionsRequest_SpaceID_DEFAULT string

func (p *SearchMentionsRequest) GetSpaceID() (v string) {
	if !p.IsSetSpaceID() {
		return SearchMentionsRequest_SpaceID_DEFAULT
	}
	return *p.SpaceID
}

var SearchMentionsRequest_Type_DEFAULT MentionType

func (p *SearchMentionsRequest) GetType() (v MentionType) {
	if !p.IsSetType() {
		return SearchMentionsRequest_Type_DEFAULT
	}
	return *p.Type
}

var SearchMentionsRequest_RepositoryID_DEFAULT string

func (p *SearchMentionsRequest) GetRepositoryID() (v string) {
	if !p.IsSetRepositoryID() {
		return SearchMentionsRequest_RepositoryID_DEFAULT
	}
	return *p.RepositoryID
}

var SearchMentionsRequest_RepositoryBranch_DEFAULT string

func (p *SearchMentionsRequest) GetRepositoryBranch() (v string) {
	if !p.IsSetRepositoryBranch() {
		return SearchMentionsRequest_RepositoryBranch_DEFAULT
	}
	return *p.RepositoryBranch
}

var SearchMentionsRequest_Query_DEFAULT string

func (p *SearchMentionsRequest) GetQuery() (v string) {
	if !p.IsSetQuery() {
		return SearchMentionsRequest_Query_DEFAULT
	}
	return *p.Query
}

var SearchMentionsRequest_Limit_DEFAULT int32

func (p *SearchMentionsRequest) GetLimit() (v int32) {
	if !p.IsSetLimit() {
		return SearchMentionsRequest_Limit_DEFAULT
	}
	return *p.Limit
}

var SearchMentionsRequest_NextID_DEFAULT string

func (p *SearchMentionsRequest) GetNextID() (v string) {
	if !p.IsSetNextID() {
		return SearchMentionsRequest_NextID_DEFAULT
	}
	return *p.NextID
}

var SearchMentionsRequest_OrderBy_DEFAULT MentionOrderBy

func (p *SearchMentionsRequest) GetOrderBy() (v MentionOrderBy) {
	if !p.IsSetOrderBy() {
		return SearchMentionsRequest_OrderBy_DEFAULT
	}
	return *p.OrderBy
}

func (p *SearchMentionsRequest) IsSetSpaceID() bool {
	return p.SpaceID != nil
}

func (p *SearchMentionsRequest) IsSetType() bool {
	return p.Type != nil
}

func (p *SearchMentionsRequest) IsSetRepositoryID() bool {
	return p.RepositoryID != nil
}

func (p *SearchMentionsRequest) IsSetRepositoryBranch() bool {
	return p.RepositoryBranch != nil
}

func (p *SearchMentionsRequest) IsSetQuery() bool {
	return p.Query != nil
}

func (p *SearchMentionsRequest) IsSetLimit() bool {
	return p.Limit != nil
}

func (p *SearchMentionsRequest) IsSetNextID() bool {
	return p.NextID != nil
}

func (p *SearchMentionsRequest) IsSetOrderBy() bool {
	return p.OrderBy != nil
}

func (p *SearchMentionsRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SearchMentionsRequest(%+v)", *p)
}

type SearchMentionsResponse struct {
	Type                   MentionType              `thrift:"type,1,required" json:"type"`
	NextID                 *string                  `thrift:"NextID,2,optional" json:"next_id"`
	HasMore                *bool                    `thrift:"HasMore,3,optional" json:"has_more"`
	KnowledgeBaseDocuments []*KnowledgeBaseDocument `thrift:"KnowledgeBaseDocuments,4,optional" json:"knowledgebase_documents"`
	Repositories           []*Repository            `thrift:"Repositories,5,optional" json:"repositories"`
	Branches               []*Branch                `thrift:"Branches,6,optional" json:"branches"`
	Dirs                   []*Dir                   `thrift:"Dirs,7,optional" json:"dirs"`
	Files                  []*File                  `thrift:"Files,8,optional" json:"files"`
}

func NewSearchMentionsResponse() *SearchMentionsResponse {
	return &SearchMentionsResponse{}
}

func (p *SearchMentionsResponse) InitDefault() {
}

func (p *SearchMentionsResponse) GetType() (v MentionType) {
	return p.Type
}

var SearchMentionsResponse_NextID_DEFAULT string

func (p *SearchMentionsResponse) GetNextID() (v string) {
	if !p.IsSetNextID() {
		return SearchMentionsResponse_NextID_DEFAULT
	}
	return *p.NextID
}

var SearchMentionsResponse_HasMore_DEFAULT bool

func (p *SearchMentionsResponse) GetHasMore() (v bool) {
	if !p.IsSetHasMore() {
		return SearchMentionsResponse_HasMore_DEFAULT
	}
	return *p.HasMore
}

var SearchMentionsResponse_KnowledgeBaseDocuments_DEFAULT []*KnowledgeBaseDocument

func (p *SearchMentionsResponse) GetKnowledgeBaseDocuments() (v []*KnowledgeBaseDocument) {
	if !p.IsSetKnowledgeBaseDocuments() {
		return SearchMentionsResponse_KnowledgeBaseDocuments_DEFAULT
	}
	return p.KnowledgeBaseDocuments
}

var SearchMentionsResponse_Repositories_DEFAULT []*Repository

func (p *SearchMentionsResponse) GetRepositories() (v []*Repository) {
	if !p.IsSetRepositories() {
		return SearchMentionsResponse_Repositories_DEFAULT
	}
	return p.Repositories
}

var SearchMentionsResponse_Branches_DEFAULT []*Branch

func (p *SearchMentionsResponse) GetBranches() (v []*Branch) {
	if !p.IsSetBranches() {
		return SearchMentionsResponse_Branches_DEFAULT
	}
	return p.Branches
}

var SearchMentionsResponse_Dirs_DEFAULT []*Dir

func (p *SearchMentionsResponse) GetDirs() (v []*Dir) {
	if !p.IsSetDirs() {
		return SearchMentionsResponse_Dirs_DEFAULT
	}
	return p.Dirs
}

var SearchMentionsResponse_Files_DEFAULT []*File

func (p *SearchMentionsResponse) GetFiles() (v []*File) {
	if !p.IsSetFiles() {
		return SearchMentionsResponse_Files_DEFAULT
	}
	return p.Files
}

func (p *SearchMentionsResponse) IsSetNextID() bool {
	return p.NextID != nil
}

func (p *SearchMentionsResponse) IsSetHasMore() bool {
	return p.HasMore != nil
}

func (p *SearchMentionsResponse) IsSetKnowledgeBaseDocuments() bool {
	return p.KnowledgeBaseDocuments != nil
}

func (p *SearchMentionsResponse) IsSetRepositories() bool {
	return p.Repositories != nil
}

func (p *SearchMentionsResponse) IsSetBranches() bool {
	return p.Branches != nil
}

func (p *SearchMentionsResponse) IsSetDirs() bool {
	return p.Dirs != nil
}

func (p *SearchMentionsResponse) IsSetFiles() bool {
	return p.Files != nil
}

func (p *SearchMentionsResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SearchMentionsResponse(%+v)", *p)
}

type Repository struct {
	ID   int64  `thrift:"ID,1,required" json:"id"`
	Name string `thrift:"Name,2,required" json:"name"`
}

func NewRepository() *Repository {
	return &Repository{}
}

func (p *Repository) InitDefault() {
}

func (p *Repository) GetID() (v int64) {
	return p.ID
}

func (p *Repository) GetName() (v string) {
	return p.Name
}

func (p *Repository) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Repository(%+v)", *p)
}

type Branch struct {
	Name      string `thrift:"Name,1,required" json:"name"`
	CommitSha string `thrift:"CommitSha,2,required" json:"commit_sha"`
}

func NewBranch() *Branch {
	return &Branch{}
}

func (p *Branch) InitDefault() {
}

func (p *Branch) GetName() (v string) {
	return p.Name
}

func (p *Branch) GetCommitSha() (v string) {
	return p.CommitSha
}

func (p *Branch) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Branch(%+v)", *p)
}

type Dir struct {
	Path string `thrift:"Path,1,required" json:"path"`
}

func NewDir() *Dir {
	return &Dir{}
}

func (p *Dir) InitDefault() {
}

func (p *Dir) GetPath() (v string) {
	return p.Path
}

func (p *Dir) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Dir(%+v)", *p)
}

type File struct {
	Path string `thrift:"Path,1,required" json:"path"`
}

func NewFile() *File {
	return &File{}
}

func (p *File) InitDefault() {
}

func (p *File) GetPath() (v string) {
	return p.Path
}

func (p *File) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("File(%+v)", *p)
}
