// Code generated by thriftgo (0.4.1). DO NOT EDIT.

package nextagent

import (
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/common"
	"database/sql"
	"database/sql/driver"
	"fmt"
)

const (
	// DS = 'DataStream'
	EventNameMessageCreate = "session.message.create"

	EventNameProgressNotice = "session.progress_notice"

	EventNamePlanUpdate = "session.plan.update"

	EventNameStepUpdate = "session.step.update"

	EventNameUseTool = "session.action.use_tool"

	EventNameToolCallRequired = "session.action.tool_call_required"

	EventNameToolCallConfirmed = "session.action.tool_call_confirmed"

	EventNameReference = "session.reference"

	EventNameSessionCompleted = "session.completed"

	EventNameDone = "done"

	EventNameError = "error"

	ArtifactLinkSubTypeLarkDoc = "lark_doc"

	ArtifactLinkSubTypeLarkSheet = "lark_sheet"

	ArtifactLinkSubTypeDeployment = "deployment"

	Preparing = "preparing"

	Recognizing = "recognizing"

	Thinking = "thinking"

	Executing = "executing"

	ReThinking = "rethinking"

	WaitForReplay = "waiting_for_replay"

	WaitForNext = "waiting_for_next"

	Sleeping = "sleeping"

	Waking = "waking"

	ToolCallRequiredTypeAsk = "ask"

	ToolCallRequiredTypeTakeControl = "take_control"
)

type DoneStatus int64

const (
	DoneStatus_Success DoneStatus = 0
	DoneStatus_Failed  DoneStatus = 1
)

func (p DoneStatus) String() string {
	switch p {
	case DoneStatus_Success:
		return "Success"
	case DoneStatus_Failed:
		return "Failed"
	}
	return "<UNSET>"
}

func DoneStatusFromString(s string) (DoneStatus, error) {
	switch s {
	case "Success":
		return DoneStatus_Success, nil
	case "Failed":
		return DoneStatus_Failed, nil
	}
	return DoneStatus(0), fmt.Errorf("not a valid DoneStatus string")
}

func DoneStatusPtr(v DoneStatus) *DoneStatus { return &v }
func (p *DoneStatus) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = DoneStatus(result.Int64)
	return
}

func (p *DoneStatus) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type BrowserContentType int64

const (
	BrowserContentType_BrowserTypeScreenshot BrowserContentType = 1
	BrowserContentType_BrowserTypeDeploy     BrowserContentType = 2
	BrowserContentType_BrowserTypeLogin      BrowserContentType = 3
)

func (p BrowserContentType) String() string {
	switch p {
	case BrowserContentType_BrowserTypeScreenshot:
		return "BrowserTypeScreenshot"
	case BrowserContentType_BrowserTypeDeploy:
		return "BrowserTypeDeploy"
	case BrowserContentType_BrowserTypeLogin:
		return "BrowserTypeLogin"
	}
	return "<UNSET>"
}

func BrowserContentTypeFromString(s string) (BrowserContentType, error) {
	switch s {
	case "BrowserTypeScreenshot":
		return BrowserContentType_BrowserTypeScreenshot, nil
	case "BrowserTypeDeploy":
		return BrowserContentType_BrowserTypeDeploy, nil
	case "BrowserTypeLogin":
		return BrowserContentType_BrowserTypeLogin, nil
	}
	return BrowserContentType(0), fmt.Errorf("not a valid BrowserContentType string")
}

func BrowserContentTypePtr(v BrowserContentType) *BrowserContentType { return &v }
func (p *BrowserContentType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = BrowserContentType(result.Int64)
	return
}

func (p *BrowserContentType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type LoginStatus int64

const (
	LoginStatus_LoginStatusUnknown LoginStatus = 0
	LoginStatus_LoginStatusWaiting LoginStatus = 1
	LoginStatus_LoginStatusSuccess LoginStatus = 4
	LoginStatus_LoginStatusFailed  LoginStatus = 5
)

func (p LoginStatus) String() string {
	switch p {
	case LoginStatus_LoginStatusUnknown:
		return "LoginStatusUnknown"
	case LoginStatus_LoginStatusWaiting:
		return "LoginStatusWaiting"
	case LoginStatus_LoginStatusSuccess:
		return "LoginStatusSuccess"
	case LoginStatus_LoginStatusFailed:
		return "LoginStatusFailed"
	}
	return "<UNSET>"
}

func LoginStatusFromString(s string) (LoginStatus, error) {
	switch s {
	case "LoginStatusUnknown":
		return LoginStatus_LoginStatusUnknown, nil
	case "LoginStatusWaiting":
		return LoginStatus_LoginStatusWaiting, nil
	case "LoginStatusSuccess":
		return LoginStatus_LoginStatusSuccess, nil
	case "LoginStatusFailed":
		return LoginStatus_LoginStatusFailed, nil
	}
	return LoginStatus(0), fmt.Errorf("not a valid LoginStatus string")
}

func LoginStatusPtr(v LoginStatus) *LoginStatus { return &v }
func (p *LoginStatus) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = LoginStatus(result.Int64)
	return
}

func (p *LoginStatus) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type ToolCallAction int64

const (
	ToolCallAction_Reject  ToolCallAction = 0
	ToolCallAction_Confirm ToolCallAction = 1
	ToolCallAction_Timeout ToolCallAction = 2
)

func (p ToolCallAction) String() string {
	switch p {
	case ToolCallAction_Reject:
		return "Reject"
	case ToolCallAction_Confirm:
		return "Confirm"
	case ToolCallAction_Timeout:
		return "Timeout"
	}
	return "<UNSET>"
}

func ToolCallActionFromString(s string) (ToolCallAction, error) {
	switch s {
	case "Reject":
		return ToolCallAction_Reject, nil
	case "Confirm":
		return ToolCallAction_Confirm, nil
	case "Timeout":
		return ToolCallAction_Timeout, nil
	}
	return ToolCallAction(0), fmt.Errorf("not a valid ToolCallAction string")
}

func ToolCallActionPtr(v ToolCallAction) *ToolCallAction { return &v }
func (p *ToolCallAction) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = ToolCallAction(result.Int64)
	return
}

func (p *ToolCallAction) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

// 产物文件中 link 类型的枚举范围
type ArtifactLinkSubType = string

type ToolCallRequiredType = string

type Event struct {
	Event string `thrift:"Event,1,required" json:"event"`
	Data  string `thrift:"Data,2,required" json:"data"`
}

func NewEvent() *Event {
	return &Event{}
}

func (p *Event) InitDefault() {
}

func (p *Event) GetEvent() (v string) {
	return p.Event
}

func (p *Event) GetData() (v string) {
	return p.Data
}

func (p *Event) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Event(%+v)", *p)
}

type ErrorEvent struct {
	EventID     string           `thrift:"EventID,1,required" json:"event_id"`
	SessionID   string           `thrift:"SessionID,2,required" json:"session_id"`
	ErrorCode   common.ErrorCode `thrift:"ErrorCode,3,required" json:"error_code"`
	Message     string           `thrift:"Message,4,required" json:"message"`
	Timestamp   int64            `thrift:"Timestamp,5" json:"timestamp"`
	EventOffset int64            `thrift:"EventOffset,6" json:"event_offset"`
	EventKey    string           `thrift:"EventKey,7" json:"event_key"`
}

func NewErrorEvent() *ErrorEvent {
	return &ErrorEvent{}
}

func (p *ErrorEvent) InitDefault() {
}

func (p *ErrorEvent) GetEventID() (v string) {
	return p.EventID
}

func (p *ErrorEvent) GetSessionID() (v string) {
	return p.SessionID
}

func (p *ErrorEvent) GetErrorCode() (v common.ErrorCode) {
	return p.ErrorCode
}

func (p *ErrorEvent) GetMessage() (v string) {
	return p.Message
}

func (p *ErrorEvent) GetTimestamp() (v int64) {
	return p.Timestamp
}

func (p *ErrorEvent) GetEventOffset() (v int64) {
	return p.EventOffset
}

func (p *ErrorEvent) GetEventKey() (v string) {
	return p.EventKey
}

func (p *ErrorEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ErrorEvent(%+v)", *p)
}

type DoneEvent struct {
	EventID   string            `thrift:"EventID,1,required" json:"event_id"`
	Status    DoneStatus        `thrift:"Status,2,required" json:"status"`
	Message   string            `thrift:"Message,3,required" json:"message"`
	Timestamp int64             `thrift:"Timestamp,4,required" json:"timestamp"`
	ErrorCode *common.ErrorCode `thrift:"ErrorCode,5,optional" json:"error_code"`
}

func NewDoneEvent() *DoneEvent {
	return &DoneEvent{}
}

func (p *DoneEvent) InitDefault() {
}

func (p *DoneEvent) GetEventID() (v string) {
	return p.EventID
}

func (p *DoneEvent) GetStatus() (v DoneStatus) {
	return p.Status
}

func (p *DoneEvent) GetMessage() (v string) {
	return p.Message
}

func (p *DoneEvent) GetTimestamp() (v int64) {
	return p.Timestamp
}

var DoneEvent_ErrorCode_DEFAULT common.ErrorCode

func (p *DoneEvent) GetErrorCode() (v common.ErrorCode) {
	if !p.IsSetErrorCode() {
		return DoneEvent_ErrorCode_DEFAULT
	}
	return *p.ErrorCode
}

func (p *DoneEvent) IsSetErrorCode() bool {
	return p.ErrorCode != nil
}

func (p *DoneEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DoneEvent(%+v)", *p)
}

/* 不要使用这个结构体，只用于调试 */
type EventStruct struct {
	MessageCreateEvent     *MessageCreateEvent     `thrift:"MessageCreateEvent,1,optional" json:"message_create_event"`
	PlanUpdateEvent        *PlanUpdateEvent        `thrift:"PlanUpdateEvent,2,optional" json:"plan_update_event"`
	StepUpdateEvent        *StepUpdateEvent        `thrift:"StepUpdateEvent,3,optional" json:"step_update_event"`
	UseToolEvent           *UseToolEvent           `thrift:"UseToolEvent,4,optional" json:"use_tool_event"`
	ProgressNoticeEvent    *ProgressNoticeEvent    `thrift:"ProgressNoticeEvent,5,optional" json:"progress_notice_event"`
	ToolCallRequiredEvent  *ToolCallRequiredEvent  `thrift:"ToolCallRequiredEvent,6,optional" json:"tool_call_required_event"`
	SessionCompletedEvent  *SessionCompletedEvent  `thrift:"SessionCompletedEvent,7,optional" json:"session_completed"`
	DoneEvent              *DoneEvent              `thrift:"DoneEvent,8,optional" json:"done_event"`
	ErrorEvent             *ErrorEvent             `thrift:"ErrorEvent,9,optional" json:"error_event"`
	ErrorCode              *common.ErrorCode       `thrift:"ErrorCode,10,optional" json:"error_code"`
	ReferenceEvent         *ReferenceEvent         `thrift:"ReferenceEvent,11,optional" json:"reference_event"`
	ToolCallConfirmedEvent *ToolCallConfirmedEvent `thrift:"ToolCallConfirmedEvent,12,optional" json:"tool_call_confirmed_event"`
}

func NewEventStruct() *EventStruct {
	return &EventStruct{}
}

func (p *EventStruct) InitDefault() {
}

var EventStruct_MessageCreateEvent_DEFAULT *MessageCreateEvent

func (p *EventStruct) GetMessageCreateEvent() (v *MessageCreateEvent) {
	if !p.IsSetMessageCreateEvent() {
		return EventStruct_MessageCreateEvent_DEFAULT
	}
	return p.MessageCreateEvent
}

var EventStruct_PlanUpdateEvent_DEFAULT *PlanUpdateEvent

func (p *EventStruct) GetPlanUpdateEvent() (v *PlanUpdateEvent) {
	if !p.IsSetPlanUpdateEvent() {
		return EventStruct_PlanUpdateEvent_DEFAULT
	}
	return p.PlanUpdateEvent
}

var EventStruct_StepUpdateEvent_DEFAULT *StepUpdateEvent

func (p *EventStruct) GetStepUpdateEvent() (v *StepUpdateEvent) {
	if !p.IsSetStepUpdateEvent() {
		return EventStruct_StepUpdateEvent_DEFAULT
	}
	return p.StepUpdateEvent
}

var EventStruct_UseToolEvent_DEFAULT *UseToolEvent

func (p *EventStruct) GetUseToolEvent() (v *UseToolEvent) {
	if !p.IsSetUseToolEvent() {
		return EventStruct_UseToolEvent_DEFAULT
	}
	return p.UseToolEvent
}

var EventStruct_ProgressNoticeEvent_DEFAULT *ProgressNoticeEvent

func (p *EventStruct) GetProgressNoticeEvent() (v *ProgressNoticeEvent) {
	if !p.IsSetProgressNoticeEvent() {
		return EventStruct_ProgressNoticeEvent_DEFAULT
	}
	return p.ProgressNoticeEvent
}

var EventStruct_ToolCallRequiredEvent_DEFAULT *ToolCallRequiredEvent

func (p *EventStruct) GetToolCallRequiredEvent() (v *ToolCallRequiredEvent) {
	if !p.IsSetToolCallRequiredEvent() {
		return EventStruct_ToolCallRequiredEvent_DEFAULT
	}
	return p.ToolCallRequiredEvent
}

var EventStruct_SessionCompletedEvent_DEFAULT *SessionCompletedEvent

func (p *EventStruct) GetSessionCompletedEvent() (v *SessionCompletedEvent) {
	if !p.IsSetSessionCompletedEvent() {
		return EventStruct_SessionCompletedEvent_DEFAULT
	}
	return p.SessionCompletedEvent
}

var EventStruct_DoneEvent_DEFAULT *DoneEvent

func (p *EventStruct) GetDoneEvent() (v *DoneEvent) {
	if !p.IsSetDoneEvent() {
		return EventStruct_DoneEvent_DEFAULT
	}
	return p.DoneEvent
}

var EventStruct_ErrorEvent_DEFAULT *ErrorEvent

func (p *EventStruct) GetErrorEvent() (v *ErrorEvent) {
	if !p.IsSetErrorEvent() {
		return EventStruct_ErrorEvent_DEFAULT
	}
	return p.ErrorEvent
}

var EventStruct_ErrorCode_DEFAULT common.ErrorCode

func (p *EventStruct) GetErrorCode() (v common.ErrorCode) {
	if !p.IsSetErrorCode() {
		return EventStruct_ErrorCode_DEFAULT
	}
	return *p.ErrorCode
}

var EventStruct_ReferenceEvent_DEFAULT *ReferenceEvent

func (p *EventStruct) GetReferenceEvent() (v *ReferenceEvent) {
	if !p.IsSetReferenceEvent() {
		return EventStruct_ReferenceEvent_DEFAULT
	}
	return p.ReferenceEvent
}

var EventStruct_ToolCallConfirmedEvent_DEFAULT *ToolCallConfirmedEvent

func (p *EventStruct) GetToolCallConfirmedEvent() (v *ToolCallConfirmedEvent) {
	if !p.IsSetToolCallConfirmedEvent() {
		return EventStruct_ToolCallConfirmedEvent_DEFAULT
	}
	return p.ToolCallConfirmedEvent
}

func (p *EventStruct) IsSetMessageCreateEvent() bool {
	return p.MessageCreateEvent != nil
}

func (p *EventStruct) IsSetPlanUpdateEvent() bool {
	return p.PlanUpdateEvent != nil
}

func (p *EventStruct) IsSetStepUpdateEvent() bool {
	return p.StepUpdateEvent != nil
}

func (p *EventStruct) IsSetUseToolEvent() bool {
	return p.UseToolEvent != nil
}

func (p *EventStruct) IsSetProgressNoticeEvent() bool {
	return p.ProgressNoticeEvent != nil
}

func (p *EventStruct) IsSetToolCallRequiredEvent() bool {
	return p.ToolCallRequiredEvent != nil
}

func (p *EventStruct) IsSetSessionCompletedEvent() bool {
	return p.SessionCompletedEvent != nil
}

func (p *EventStruct) IsSetDoneEvent() bool {
	return p.DoneEvent != nil
}

func (p *EventStruct) IsSetErrorEvent() bool {
	return p.ErrorEvent != nil
}

func (p *EventStruct) IsSetErrorCode() bool {
	return p.ErrorCode != nil
}

func (p *EventStruct) IsSetReferenceEvent() bool {
	return p.ReferenceEvent != nil
}

func (p *EventStruct) IsSetToolCallConfirmedEvent() bool {
	return p.ToolCallConfirmedEvent != nil
}

func (p *EventStruct) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EventStruct(%+v)", *p)
}

type ReferenceEvent struct {
	EventID     string           `thrift:"EventID,1,required" json:"event_id"`
	SessionID   string           `thrift:"SessionID,2,required" json:"session_id"`
	TaskID      string           `thrift:"TaskID,3,required" json:"task_id"`
	References  []*ReferenceItem `thrift:"References,4,required" json:"references"`
	Timestamp   int64            `thrift:"Timestamp,5" json:"timestamp"`
	EventOffset int64            `thrift:"EventOffset,6" json:"event_offset"`
	EventKey    string           `thrift:"EventKey,7" json:"event_key"`
}

func NewReferenceEvent() *ReferenceEvent {
	return &ReferenceEvent{}
}

func (p *ReferenceEvent) InitDefault() {
}

func (p *ReferenceEvent) GetEventID() (v string) {
	return p.EventID
}

func (p *ReferenceEvent) GetSessionID() (v string) {
	return p.SessionID
}

func (p *ReferenceEvent) GetTaskID() (v string) {
	return p.TaskID
}

func (p *ReferenceEvent) GetReferences() (v []*ReferenceItem) {
	return p.References
}

func (p *ReferenceEvent) GetTimestamp() (v int64) {
	return p.Timestamp
}

func (p *ReferenceEvent) GetEventOffset() (v int64) {
	return p.EventOffset
}

func (p *ReferenceEvent) GetEventKey() (v string) {
	return p.EventKey
}

func (p *ReferenceEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ReferenceEvent(%+v)", *p)
}

type ReferenceItem struct {
	ID    int32  `thrift:"ID,1,required" json:"id"`
	Title string `thrift:"Title,2,required" json:"title"`
	URI   string `thrift:"URI,3,required" json:"uri"`
}

func NewReferenceItem() *ReferenceItem {
	return &ReferenceItem{}
}

func (p *ReferenceItem) InitDefault() {
}

func (p *ReferenceItem) GetID() (v int32) {
	return p.ID
}

func (p *ReferenceItem) GetTitle() (v string) {
	return p.Title
}

func (p *ReferenceItem) GetURI() (v string) {
	return p.URI
}

func (p *ReferenceItem) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ReferenceItem(%+v)", *p)
}

type MessageCreateEvent struct {
	EventID            string              `thrift:"EventID,1,required" json:"event_id"`
	Message            *Message            `thrift:"Message,2,required" json:"message"`
	Timestamp          int64               `thrift:"Timestamp,3" json:"timestamp"`
	EventOffset        int64               `thrift:"EventOffset,4" json:"event_offset"`
	ReplyMessageID     string              `thrift:"ReplyMessageID,5" json:"reply_message_id"`
	EventKey           string              `thrift:"EventKey,6" json:"event_key"`
	GenerateVisualPage *GenerateVisualPage `thrift:"GenerateVisualPage,7,optional" json:"generate_visual_page"`
}

func NewMessageCreateEvent() *MessageCreateEvent {
	return &MessageCreateEvent{}
}

func (p *MessageCreateEvent) InitDefault() {
}

func (p *MessageCreateEvent) GetEventID() (v string) {
	return p.EventID
}

var MessageCreateEvent_Message_DEFAULT *Message

func (p *MessageCreateEvent) GetMessage() (v *Message) {
	if !p.IsSetMessage() {
		return MessageCreateEvent_Message_DEFAULT
	}
	return p.Message
}

func (p *MessageCreateEvent) GetTimestamp() (v int64) {
	return p.Timestamp
}

func (p *MessageCreateEvent) GetEventOffset() (v int64) {
	return p.EventOffset
}

func (p *MessageCreateEvent) GetReplyMessageID() (v string) {
	return p.ReplyMessageID
}

func (p *MessageCreateEvent) GetEventKey() (v string) {
	return p.EventKey
}

var MessageCreateEvent_GenerateVisualPage_DEFAULT *GenerateVisualPage

func (p *MessageCreateEvent) GetGenerateVisualPage() (v *GenerateVisualPage) {
	if !p.IsSetGenerateVisualPage() {
		return MessageCreateEvent_GenerateVisualPage_DEFAULT
	}
	return p.GenerateVisualPage
}

func (p *MessageCreateEvent) IsSetMessage() bool {
	return p.Message != nil
}

func (p *MessageCreateEvent) IsSetGenerateVisualPage() bool {
	return p.GenerateVisualPage != nil
}

func (p *MessageCreateEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MessageCreateEvent(%+v)", *p)
}

type Message struct {
	MessageID   string        `thrift:"MessageID,1,required" json:"message_id"`
	SessionID   string        `thrift:"SessionID,2,required" json:"session_id"`
	TaskID      string        `thrift:"TaskID,3,required" json:"task_id"`
	Role        string        `thrift:"Role,4,required" json:"role"`
	Content     string        `thrift:"Content,5,required" json:"content"`
	Attachments []*Attachment `thrift:"Attachments,7,optional" json:"attachments"`
	Creator     string        `thrift:"Creator,6,required" json:"creator"`
	CreatedAt   string        `thrift:"CreatedAt,8,required" json:"created_at"`
	UpdatedAt   string        `thrift:"UpdatedAt,9,required" json:"updated_at"`
	Mentions    []*Mention    `thrift:"Mentions,10,optional" json:"mentions"`
}

func NewMessage() *Message {
	return &Message{}
}

func (p *Message) InitDefault() {
}

func (p *Message) GetMessageID() (v string) {
	return p.MessageID
}

func (p *Message) GetSessionID() (v string) {
	return p.SessionID
}

func (p *Message) GetTaskID() (v string) {
	return p.TaskID
}

func (p *Message) GetRole() (v string) {
	return p.Role
}

func (p *Message) GetContent() (v string) {
	return p.Content
}

var Message_Attachments_DEFAULT []*Attachment

func (p *Message) GetAttachments() (v []*Attachment) {
	if !p.IsSetAttachments() {
		return Message_Attachments_DEFAULT
	}
	return p.Attachments
}

func (p *Message) GetCreator() (v string) {
	return p.Creator
}

func (p *Message) GetCreatedAt() (v string) {
	return p.CreatedAt
}

func (p *Message) GetUpdatedAt() (v string) {
	return p.UpdatedAt
}

var Message_Mentions_DEFAULT []*Mention

func (p *Message) GetMentions() (v []*Mention) {
	if !p.IsSetMentions() {
		return Message_Mentions_DEFAULT
	}
	return p.Mentions
}

func (p *Message) IsSetAttachments() bool {
	return p.Attachments != nil
}

func (p *Message) IsSetMentions() bool {
	return p.Mentions != nil
}

func (p *Message) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Message(%+v)", *p)
}

type GenerateVisualPage struct {
	// 显示生成可视化页面按钮
	DisplayGenerate bool `thrift:"DisplayGenerate,1,required" json:"display_generate"`
	// 点击生成可视化页面的默认 query
	GeneratePageQuery string `thrift:"GeneratePageQuery,2,required" json:"generate_page_query"`
}

func NewGenerateVisualPage() *GenerateVisualPage {
	return &GenerateVisualPage{}
}

func (p *GenerateVisualPage) InitDefault() {
}

func (p *GenerateVisualPage) GetDisplayGenerate() (v bool) {
	return p.DisplayGenerate
}

func (p *GenerateVisualPage) GetGeneratePageQuery() (v string) {
	return p.GeneratePageQuery
}

func (p *GenerateVisualPage) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GenerateVisualPage(%+v)", *p)
}

type Attachment struct {
	ID            string   `thrift:"ID,1,required" json:"id"`
	FileName      string   `thrift:"FileName,2,required" json:"file_name"`
	Path          string   `thrift:"Path,3,required" json:"path"`
	Type          string   `thrift:"Type,4,required" json:"type"`
	URL           string   `thrift:"URL,5,required" json:"url"`
	ContentType   string   `thrift:"ContentType,6,required" json:"content_type"`
	ContentLength int64    `thrift:"ContentLength,7,required" json:"content_length"`
	ParentStepIDs []string `thrift:"ParentStepIDs,8,optional" json:"parent_step_ids,omitempty"`
	LarkToken     string   `thrift:"LarkToken,9,required" json:"lark_token"`
	NeedPreview   bool     `thrift:"NeedPreview,10" json:"need_preview"`
	// 子类型，如果是 Code 对应不同的语言类型，其他则是文件名后缀；如果是Link，则对应的是 link 类型（参考 ArtifactLinkSubType）
	SubType *string `thrift:"SubType,11,optional" json:"sub_type"`
	// 是否是直接跳转的链接
	NeedJump bool `thrift:"NeedJump,12" json:"need_jump"`
}

func NewAttachment() *Attachment {
	return &Attachment{}
}

func (p *Attachment) InitDefault() {
}

func (p *Attachment) GetID() (v string) {
	return p.ID
}

func (p *Attachment) GetFileName() (v string) {
	return p.FileName
}

func (p *Attachment) GetPath() (v string) {
	return p.Path
}

func (p *Attachment) GetType() (v string) {
	return p.Type
}

func (p *Attachment) GetURL() (v string) {
	return p.URL
}

func (p *Attachment) GetContentType() (v string) {
	return p.ContentType
}

func (p *Attachment) GetContentLength() (v int64) {
	return p.ContentLength
}

var Attachment_ParentStepIDs_DEFAULT []string

func (p *Attachment) GetParentStepIDs() (v []string) {
	if !p.IsSetParentStepIDs() {
		return Attachment_ParentStepIDs_DEFAULT
	}
	return p.ParentStepIDs
}

func (p *Attachment) GetLarkToken() (v string) {
	return p.LarkToken
}

func (p *Attachment) GetNeedPreview() (v bool) {
	return p.NeedPreview
}

var Attachment_SubType_DEFAULT string

func (p *Attachment) GetSubType() (v string) {
	if !p.IsSetSubType() {
		return Attachment_SubType_DEFAULT
	}
	return *p.SubType
}

func (p *Attachment) GetNeedJump() (v bool) {
	return p.NeedJump
}

func (p *Attachment) IsSetParentStepIDs() bool {
	return p.ParentStepIDs != nil
}

func (p *Attachment) IsSetSubType() bool {
	return p.SubType != nil
}

func (p *Attachment) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Attachment(%+v)", *p)
}

type PlanUpdateEvent struct {
	EventID     string  `thrift:"EventID,1,required" json:"event_id"`
	TaskID      string  `thrift:"TaskID,2,required" json:"task_id"`
	PlanID      string  `thrift:"PlanID,3,required" json:"plan_id"`
	Steps       []*Step `thrift:"Steps,4,required" json:"steps"`
	Timestamp   int64   `thrift:"Timestamp,5" json:"timestamp"`
	EventOffset int64   `thrift:"EventOffset,6" json:"event_offset"`
	Status      string  `thrift:"Status,7,required" json:"status"`
	SessionID   string  `thrift:"SessionID,8,required" json:"session_id"`
	EventKey    string  `thrift:"EventKey,9" json:"event_key"`
}

func NewPlanUpdateEvent() *PlanUpdateEvent {
	return &PlanUpdateEvent{}
}

func (p *PlanUpdateEvent) InitDefault() {
}

func (p *PlanUpdateEvent) GetEventID() (v string) {
	return p.EventID
}

func (p *PlanUpdateEvent) GetTaskID() (v string) {
	return p.TaskID
}

func (p *PlanUpdateEvent) GetPlanID() (v string) {
	return p.PlanID
}

func (p *PlanUpdateEvent) GetSteps() (v []*Step) {
	return p.Steps
}

func (p *PlanUpdateEvent) GetTimestamp() (v int64) {
	return p.Timestamp
}

func (p *PlanUpdateEvent) GetEventOffset() (v int64) {
	return p.EventOffset
}

func (p *PlanUpdateEvent) GetStatus() (v string) {
	return p.Status
}

func (p *PlanUpdateEvent) GetSessionID() (v string) {
	return p.SessionID
}

func (p *PlanUpdateEvent) GetEventKey() (v string) {
	return p.EventKey
}

func (p *PlanUpdateEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PlanUpdateEvent(%+v)", *p)
}

type Step struct {
	StepID           string   `thrift:"StepID,1,required" json:"step_id"`
	TaskID           string   `thrift:"TaskID,2,required" json:"task_id"`
	Title            string   `thrift:"Title,3,required" json:"title"`
	Status           string   `thrift:"Status,4,required" json:"status"`
	StartTime        int64    `thrift:"StartTime,5,required" json:"start_time"`
	EndTime          int64    `thrift:"EndTime,6,required" json:"end_time"`
	ParentMessageIDs []string `thrift:"ParentMessageIDs,7,required" json:"parent_message_ids,omitempty"`
}

func NewStep() *Step {
	return &Step{}
}

func (p *Step) InitDefault() {
}

func (p *Step) GetStepID() (v string) {
	return p.StepID
}

func (p *Step) GetTaskID() (v string) {
	return p.TaskID
}

func (p *Step) GetTitle() (v string) {
	return p.Title
}

func (p *Step) GetStatus() (v string) {
	return p.Status
}

func (p *Step) GetStartTime() (v int64) {
	return p.StartTime
}

func (p *Step) GetEndTime() (v int64) {
	return p.EndTime
}

func (p *Step) GetParentMessageIDs() (v []string) {
	return p.ParentMessageIDs
}

func (p *Step) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Step(%+v)", *p)
}

type StepUpdateEvent struct {
	EventID     string `thrift:"EventID,1,required" json:"event_id"`
	StepID      string `thrift:"StepID,2,required" json:"step_id"`
	PlanID      string `thrift:"PlanID,3,required" json:"plan_id"`
	Status      string `thrift:"Status,4,required" json:"status"`
	Summary     string `thrift:"Summary,5,required" json:"summary"`
	Description string `thrift:"Description,6,required" json:"description"`
	Timestamp   int64  `thrift:"Timestamp,7" json:"timestamp"`
	EventOffset int64  `thrift:"EventOffset,8" json:"event_offset"`
	SessionID   string `thrift:"SessionID,9,required" json:"session_id"`
	EventKey    string `thrift:"EventKey,10" json:"event_key"`
}

func NewStepUpdateEvent() *StepUpdateEvent {
	return &StepUpdateEvent{}
}

func (p *StepUpdateEvent) InitDefault() {
}

func (p *StepUpdateEvent) GetEventID() (v string) {
	return p.EventID
}

func (p *StepUpdateEvent) GetStepID() (v string) {
	return p.StepID
}

func (p *StepUpdateEvent) GetPlanID() (v string) {
	return p.PlanID
}

func (p *StepUpdateEvent) GetStatus() (v string) {
	return p.Status
}

func (p *StepUpdateEvent) GetSummary() (v string) {
	return p.Summary
}

func (p *StepUpdateEvent) GetDescription() (v string) {
	return p.Description
}

func (p *StepUpdateEvent) GetTimestamp() (v int64) {
	return p.Timestamp
}

func (p *StepUpdateEvent) GetEventOffset() (v int64) {
	return p.EventOffset
}

func (p *StepUpdateEvent) GetSessionID() (v string) {
	return p.SessionID
}

func (p *StepUpdateEvent) GetEventKey() (v string) {
	return p.EventKey
}

func (p *StepUpdateEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("StepUpdateEvent(%+v)", *p)
}

type UseToolEvent struct {
	EventID     string      `thrift:"EventID,1,required" json:"event_id"`
	StepID      string      `thrift:"StepID,2,required" json:"step_id"`
	PlanID      string      `thrift:"PlanID,3,required" json:"plan_id"`
	ToolName    string      `thrift:"ToolName,4,required" json:"tool_name"`
	Summary     string      `thrift:"Summary,5,required" json:"summary"`
	Description string      `thrift:"Description,6,required" json:"description"`
	Status      string      `thrift:"Status,7,required" json:"status"`
	Terminal    *Terminal   `thrift:"Terminal,8,optional" json:"terminal,omitempty"`
	TextEditor  *TextEditor `thrift:"TextEditor,9,optional" json:"text_editor,omitempty"`
	CodeEditor  *CodeEditor `thrift:"CodeEditor,10,optional" json:"code_editor,omitempty"`
	Browser     *Browser    `thrift:"Browser,11,optional" json:"browser,omitempty"`
	SearchList  *SearchList `thrift:"SearchList,12,optional" json:"search_list,omitempty"`
	Timestamp   int64       `thrift:"Timestamp,13" json:"timestamp"`
	EventOffset int64       `thrift:"EventOffset,14" json:"event_offset"`
	SessionID   string      `thrift:"SessionID,15,required" json:"session_id"`
	AgentStepID string      `thrift:"AgentStepID,16,required" json:"agent_step_id"`
	EventKey    string      `thrift:"EventKey,17" json:"event_key"`
}

func NewUseToolEvent() *UseToolEvent {
	return &UseToolEvent{}
}

func (p *UseToolEvent) InitDefault() {
}

func (p *UseToolEvent) GetEventID() (v string) {
	return p.EventID
}

func (p *UseToolEvent) GetStepID() (v string) {
	return p.StepID
}

func (p *UseToolEvent) GetPlanID() (v string) {
	return p.PlanID
}

func (p *UseToolEvent) GetToolName() (v string) {
	return p.ToolName
}

func (p *UseToolEvent) GetSummary() (v string) {
	return p.Summary
}

func (p *UseToolEvent) GetDescription() (v string) {
	return p.Description
}

func (p *UseToolEvent) GetStatus() (v string) {
	return p.Status
}

var UseToolEvent_Terminal_DEFAULT *Terminal

func (p *UseToolEvent) GetTerminal() (v *Terminal) {
	if !p.IsSetTerminal() {
		return UseToolEvent_Terminal_DEFAULT
	}
	return p.Terminal
}

var UseToolEvent_TextEditor_DEFAULT *TextEditor

func (p *UseToolEvent) GetTextEditor() (v *TextEditor) {
	if !p.IsSetTextEditor() {
		return UseToolEvent_TextEditor_DEFAULT
	}
	return p.TextEditor
}

var UseToolEvent_CodeEditor_DEFAULT *CodeEditor

func (p *UseToolEvent) GetCodeEditor() (v *CodeEditor) {
	if !p.IsSetCodeEditor() {
		return UseToolEvent_CodeEditor_DEFAULT
	}
	return p.CodeEditor
}

var UseToolEvent_Browser_DEFAULT *Browser

func (p *UseToolEvent) GetBrowser() (v *Browser) {
	if !p.IsSetBrowser() {
		return UseToolEvent_Browser_DEFAULT
	}
	return p.Browser
}

var UseToolEvent_SearchList_DEFAULT *SearchList

func (p *UseToolEvent) GetSearchList() (v *SearchList) {
	if !p.IsSetSearchList() {
		return UseToolEvent_SearchList_DEFAULT
	}
	return p.SearchList
}

func (p *UseToolEvent) GetTimestamp() (v int64) {
	return p.Timestamp
}

func (p *UseToolEvent) GetEventOffset() (v int64) {
	return p.EventOffset
}

func (p *UseToolEvent) GetSessionID() (v string) {
	return p.SessionID
}

func (p *UseToolEvent) GetAgentStepID() (v string) {
	return p.AgentStepID
}

func (p *UseToolEvent) GetEventKey() (v string) {
	return p.EventKey
}

func (p *UseToolEvent) IsSetTerminal() bool {
	return p.Terminal != nil
}

func (p *UseToolEvent) IsSetTextEditor() bool {
	return p.TextEditor != nil
}

func (p *UseToolEvent) IsSetCodeEditor() bool {
	return p.CodeEditor != nil
}

func (p *UseToolEvent) IsSetBrowser() bool {
	return p.Browser != nil
}

func (p *UseToolEvent) IsSetSearchList() bool {
	return p.SearchList != nil
}

func (p *UseToolEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UseToolEvent(%+v)", *p)
}

type Terminal struct {
	Command string   `thrift:"Command,1,required" json:"command"`
	Output  []string `thrift:"Output,2,required" json:"output"`
}

func NewTerminal() *Terminal {
	return &Terminal{}
}

func (p *Terminal) InitDefault() {
}

func (p *Terminal) GetCommand() (v string) {
	return p.Command
}

func (p *Terminal) GetOutput() (v []string) {
	return p.Output
}

func (p *Terminal) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Terminal(%+v)", *p)
}

type TextEditor struct {
	CreateFile *CreateFile `thrift:"CreateFile,1" json:"create_file"`
	PatchFile  *PatchFile  `thrift:"PatchFile,2" json:"patch_file"`
	AppendFile *AppendFile `thrift:"AppendFile,3" json:"append_file"`
}

func NewTextEditor() *TextEditor {
	return &TextEditor{}
}

func (p *TextEditor) InitDefault() {
}

var TextEditor_CreateFile_DEFAULT *CreateFile

func (p *TextEditor) GetCreateFile() (v *CreateFile) {
	if !p.IsSetCreateFile() {
		return TextEditor_CreateFile_DEFAULT
	}
	return p.CreateFile
}

var TextEditor_PatchFile_DEFAULT *PatchFile

func (p *TextEditor) GetPatchFile() (v *PatchFile) {
	if !p.IsSetPatchFile() {
		return TextEditor_PatchFile_DEFAULT
	}
	return p.PatchFile
}

var TextEditor_AppendFile_DEFAULT *AppendFile

func (p *TextEditor) GetAppendFile() (v *AppendFile) {
	if !p.IsSetAppendFile() {
		return TextEditor_AppendFile_DEFAULT
	}
	return p.AppendFile
}

func (p *TextEditor) IsSetCreateFile() bool {
	return p.CreateFile != nil
}

func (p *TextEditor) IsSetPatchFile() bool {
	return p.PatchFile != nil
}

func (p *TextEditor) IsSetAppendFile() bool {
	return p.AppendFile != nil
}

func (p *TextEditor) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TextEditor(%+v)", *p)
}

type CodeEditor struct {
	CreateFile *CreateFile `thrift:"CreateFile,1" json:"create_file"`
	PatchFile  *PatchFile  `thrift:"PatchFile,2" json:"patch_file"`
	AppendFile *AppendFile `thrift:"AppendFile,3" json:"append_file"`
}

func NewCodeEditor() *CodeEditor {
	return &CodeEditor{}
}

func (p *CodeEditor) InitDefault() {
}

var CodeEditor_CreateFile_DEFAULT *CreateFile

func (p *CodeEditor) GetCreateFile() (v *CreateFile) {
	if !p.IsSetCreateFile() {
		return CodeEditor_CreateFile_DEFAULT
	}
	return p.CreateFile
}

var CodeEditor_PatchFile_DEFAULT *PatchFile

func (p *CodeEditor) GetPatchFile() (v *PatchFile) {
	if !p.IsSetPatchFile() {
		return CodeEditor_PatchFile_DEFAULT
	}
	return p.PatchFile
}

var CodeEditor_AppendFile_DEFAULT *AppendFile

func (p *CodeEditor) GetAppendFile() (v *AppendFile) {
	if !p.IsSetAppendFile() {
		return CodeEditor_AppendFile_DEFAULT
	}
	return p.AppendFile
}

func (p *CodeEditor) IsSetCreateFile() bool {
	return p.CreateFile != nil
}

func (p *CodeEditor) IsSetPatchFile() bool {
	return p.PatchFile != nil
}

func (p *CodeEditor) IsSetAppendFile() bool {
	return p.AppendFile != nil
}

func (p *CodeEditor) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CodeEditor(%+v)", *p)
}

type FileLines struct {
	StartLine int64   `thrift:"StartLine,1" json:"start_line"`
	EndLine   int64   `thrift:"EndLine,2" json:"end_line"`
	Lines     []*Line `thrift:"Lines,3" json:"lines"`
}

func NewFileLines() *FileLines {
	return &FileLines{}
}

func (p *FileLines) InitDefault() {
}

func (p *FileLines) GetStartLine() (v int64) {
	return p.StartLine
}

func (p *FileLines) GetEndLine() (v int64) {
	return p.EndLine
}

func (p *FileLines) GetLines() (v []*Line) {
	return p.Lines
}

func (p *FileLines) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FileLines(%+v)", *p)
}

type FilePatch struct {
	FilePath string `thrift:"FilePath,1" json:"file_path"`
	Patch    string `thrift:"Patch,2" json:"patch"`
}

func NewFilePatch() *FilePatch {
	return &FilePatch{}
}

func (p *FilePatch) InitDefault() {
}

func (p *FilePatch) GetFilePath() (v string) {
	return p.FilePath
}

func (p *FilePatch) GetPatch() (v string) {
	return p.Patch
}

func (p *FilePatch) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FilePatch(%+v)", *p)
}

type Line struct {
	Line    int64  `thrift:"Line,1" json:"line"`
	Content string `thrift:"Content,2" json:"content"`
}

func NewLine() *Line {
	return &Line{}
}

func (p *Line) InitDefault() {
}

func (p *Line) GetLine() (v int64) {
	return p.Line
}

func (p *Line) GetContent() (v string) {
	return p.Content
}

func (p *Line) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Line(%+v)", *p)
}

type CreateFile struct {
	// 文档名称/ Title
	FilePath string `thrift:"FilePath,1" json:"file_path"`
	// 文档内容
	Content string `thrift:"Content,2" json:"content"`
	// 如果是 Code 对应不同的语言类型，其他则是文件名后缀；如果是 Link，则对应的是 link 类型（参考 ArtifactLinkSubType）
	SubType string `thrift:"SubType,3" json:"sub_type"`
	// 文档链接，与 Content 二选一
	URL string `thrift:"URL,4" json:"url"`
}

func NewCreateFile() *CreateFile {
	return &CreateFile{}
}

func (p *CreateFile) InitDefault() {
}

func (p *CreateFile) GetFilePath() (v string) {
	return p.FilePath
}

func (p *CreateFile) GetContent() (v string) {
	return p.Content
}

func (p *CreateFile) GetSubType() (v string) {
	return p.SubType
}

func (p *CreateFile) GetURL() (v string) {
	return p.URL
}

func (p *CreateFile) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateFile(%+v)", *p)
}

type PatchFile struct {
	// 文档名称/ Title
	FilePath        string       `thrift:"FilePath,1" json:"file_path"`
	Diff            string       `thrift:"Diff,2" json:"diff"`
	PatchFileResult []*FilePatch `thrift:"PatchFileResult,3" json:"patch_file_result"`
	// 如果是 Code 对应不同的语言类型，其他则是文件名后缀；如果是 Link，则对应的是 link 类型（参考 ArtifactLinkSubType）
	SubType string `thrift:"SubType,4" json:"sub_type"`
	// 文档链接
	URL string `thrift:"URL,5" json:"url"`
}

func NewPatchFile() *PatchFile {
	return &PatchFile{}
}

func (p *PatchFile) InitDefault() {
}

func (p *PatchFile) GetFilePath() (v string) {
	return p.FilePath
}

func (p *PatchFile) GetDiff() (v string) {
	return p.Diff
}

func (p *PatchFile) GetPatchFileResult() (v []*FilePatch) {
	return p.PatchFileResult
}

func (p *PatchFile) GetSubType() (v string) {
	return p.SubType
}

func (p *PatchFile) GetURL() (v string) {
	return p.URL
}

func (p *PatchFile) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PatchFile(%+v)", *p)
}

type AppendFile struct {
	// 文档名称/ Title
	FilePath string `thrift:"FilePath,1" json:"file_path"`
	// 文档内容
	Content string `thrift:"Content,2" json:"content"`
	// 如果是 Code 对应不同的语言类型，其他则是文件名后缀；如果是 Link，则对应的是 link 类型（参考 ArtifactLinkSubType）
	SubType string `thrift:"SubType,3" json:"sub_type"`
	// 文档链接，与 Content 二选一
	URL string `thrift:"URL,4" json:"url"`
}

func NewAppendFile() *AppendFile {
	return &AppendFile{}
}

func (p *AppendFile) InitDefault() {
}

func (p *AppendFile) GetFilePath() (v string) {
	return p.FilePath
}

func (p *AppendFile) GetContent() (v string) {
	return p.Content
}

func (p *AppendFile) GetSubType() (v string) {
	return p.SubType
}

func (p *AppendFile) GetURL() (v string) {
	return p.URL
}

func (p *AppendFile) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppendFile(%+v)", *p)
}

type ReadFile struct {
	FilePath  string `thrift:"FilePath,1" json:"file_path"`
	StartLine int64  `thrift:"StartLine,2" json:"start_line"`
	EndLine   int64  `thrift:"EndLine,3" json:"end_line"`
}

func NewReadFile() *ReadFile {
	return &ReadFile{}
}

func (p *ReadFile) InitDefault() {
}

func (p *ReadFile) GetFilePath() (v string) {
	return p.FilePath
}

func (p *ReadFile) GetStartLine() (v int64) {
	return p.StartLine
}

func (p *ReadFile) GetEndLine() (v int64) {
	return p.EndLine
}

func (p *ReadFile) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ReadFile(%+v)", *p)
}

type Screenshot struct {
	ArtifactID string `thrift:"ArtifactID,1" json:"id" mapstructure:"id"`
	Path       string `thrift:"Path,2" json:"path" mapstructure:"path"`
	MimeType   string `thrift:"MimeType,3" json:"mime_type" mapstructure:"mime_type"`
}

func NewScreenshot() *Screenshot {
	return &Screenshot{}
}

func (p *Screenshot) InitDefault() {
}

func (p *Screenshot) GetArtifactID() (v string) {
	return p.ArtifactID
}

func (p *Screenshot) GetPath() (v string) {
	return p.Path
}

func (p *Screenshot) GetMimeType() (v string) {
	return p.MimeType
}

func (p *Screenshot) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Screenshot(%+v)", *p)
}

type Browser struct {
	// 截图url
	ScreenshotURL string `thrift:"ScreenshotURL,1" json:"screenshot_url"`
	// 访问页面url 或者部署的页面url
	URL         string             `thrift:"URL,2" json:"url"`
	ContentType BrowserContentType `thrift:"ContentType,3" json:"content_type"`
	// 部署id
	DeployID  string     `thrift:"DeployID,4" json:"deploy_id"`
	LoginInfo *LoginInfo `thrift:"LoginInfo,5,optional" json:"login_info"`
}

func NewBrowser() *Browser {
	return &Browser{}
}

func (p *Browser) InitDefault() {
}

func (p *Browser) GetScreenshotURL() (v string) {
	return p.ScreenshotURL
}

func (p *Browser) GetURL() (v string) {
	return p.URL
}

func (p *Browser) GetContentType() (v BrowserContentType) {
	return p.ContentType
}

func (p *Browser) GetDeployID() (v string) {
	return p.DeployID
}

var Browser_LoginInfo_DEFAULT *LoginInfo

func (p *Browser) GetLoginInfo() (v *LoginInfo) {
	if !p.IsSetLoginInfo() {
		return Browser_LoginInfo_DEFAULT
	}
	return p.LoginInfo
}

func (p *Browser) IsSetLoginInfo() bool {
	return p.LoginInfo != nil
}

func (p *Browser) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Browser(%+v)", *p)
}

type LoginInfo struct {
	LoginStatus  LoginStatus `thrift:"LoginStatus,1,required" json:"login_status"`
	LoginTimeout *int64      `thrift:"LoginTimeout,2,optional" json:"login_timeout"`
}

func NewLoginInfo() *LoginInfo {
	return &LoginInfo{}
}

func (p *LoginInfo) InitDefault() {
}

func (p *LoginInfo) GetLoginStatus() (v LoginStatus) {
	return p.LoginStatus
}

var LoginInfo_LoginTimeout_DEFAULT int64

func (p *LoginInfo) GetLoginTimeout() (v int64) {
	if !p.IsSetLoginTimeout() {
		return LoginInfo_LoginTimeout_DEFAULT
	}
	return *p.LoginTimeout
}

func (p *LoginInfo) IsSetLoginTimeout() bool {
	return p.LoginTimeout != nil
}

func (p *LoginInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("LoginInfo(%+v)", *p)
}

type SearchList struct {
	Query   string            `thrift:"Query,1,required" json:"query"`
	Results []*SearchListItem `thrift:"Results,2,required" json:"results"`
}

func NewSearchList() *SearchList {
	return &SearchList{}
}

func (p *SearchList) InitDefault() {
}

func (p *SearchList) GetQuery() (v string) {
	return p.Query
}

func (p *SearchList) GetResults() (v []*SearchListItem) {
	return p.Results
}

func (p *SearchList) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SearchList(%+v)", *p)
}

type SearchListItem struct {
	Title       string `thrift:"Title,1,required" json:"title" mapstructure:"title"`
	URL         string `thrift:"URL,2,required" json:"url" mapstructure:"url"`
	Description string `thrift:"Description,3,required" json:"description" mapstructure:"description"`
	Favicon     string `thrift:"Favicon,4,required" json:"favicon" mapstructure:"favicon"`
}

func NewSearchListItem() *SearchListItem {
	return &SearchListItem{}
}

func (p *SearchListItem) InitDefault() {
}

func (p *SearchListItem) GetTitle() (v string) {
	return p.Title
}

func (p *SearchListItem) GetURL() (v string) {
	return p.URL
}

func (p *SearchListItem) GetDescription() (v string) {
	return p.Description
}

func (p *SearchListItem) GetFavicon() (v string) {
	return p.Favicon
}

func (p *SearchListItem) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SearchListItem(%+v)", *p)
}

type ProgressNoticeEvent struct {
	EventID     string `thrift:"EventID,1,required" json:"event_id"`
	SessionID   string `thrift:"SessionID,2,required" json:"session_id"`
	Status      string `thrift:"Status,3" json:"status"`
	Timestamp   int64  `thrift:"Timestamp,4" json:"timestamp"`
	EventOffset int64  `thrift:"EventOffset,5" json:"event_offset"`
	EventKey    string `thrift:"EventKey,6" json:"event_key"`
}

func NewProgressNoticeEvent() *ProgressNoticeEvent {
	return &ProgressNoticeEvent{}
}

func (p *ProgressNoticeEvent) InitDefault() {
}

func (p *ProgressNoticeEvent) GetEventID() (v string) {
	return p.EventID
}

func (p *ProgressNoticeEvent) GetSessionID() (v string) {
	return p.SessionID
}

func (p *ProgressNoticeEvent) GetStatus() (v string) {
	return p.Status
}

func (p *ProgressNoticeEvent) GetTimestamp() (v int64) {
	return p.Timestamp
}

func (p *ProgressNoticeEvent) GetEventOffset() (v int64) {
	return p.EventOffset
}

func (p *ProgressNoticeEvent) GetEventKey() (v string) {
	return p.EventKey
}

func (p *ProgressNoticeEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ProgressNoticeEvent(%+v)", *p)
}

type ToolCallRequiredEvent struct {
	EventID           string               `thrift:"EventID,1,required" json:"event_id"`
	SessionID         string               `thrift:"SessionID,2,required" json:"session_id"`
	Tool              string               `thrift:"Tool,3" json:"tool"`
	Question          string               `thrift:"Question,4" json:"question"`
	Type              ToolCallRequiredType `thrift:"Type,5" json:"type"`
	Timestamp         int64                `thrift:"Timestamp,6" json:"timestamp"`
	EventOffset       int64                `thrift:"EventOffset,7" json:"event_offset"`
	ToolCallID        string               `thrift:"ToolCallID,8,required" json:"tool_call_id"`
	EventKey          string               `thrift:"EventKey,9" json:"event_key"`
	TakeBrowserParams *TakeBrowserParams   `thrift:"TakeBrowserParams,10,optional" json:"take_browser_params"`
	StepID            string               `thrift:"StepID,11" json:"step_id"`
}

func NewToolCallRequiredEvent() *ToolCallRequiredEvent {
	return &ToolCallRequiredEvent{}
}

func (p *ToolCallRequiredEvent) InitDefault() {
}

func (p *ToolCallRequiredEvent) GetEventID() (v string) {
	return p.EventID
}

func (p *ToolCallRequiredEvent) GetSessionID() (v string) {
	return p.SessionID
}

func (p *ToolCallRequiredEvent) GetTool() (v string) {
	return p.Tool
}

func (p *ToolCallRequiredEvent) GetQuestion() (v string) {
	return p.Question
}

func (p *ToolCallRequiredEvent) GetType() (v ToolCallRequiredType) {
	return p.Type
}

func (p *ToolCallRequiredEvent) GetTimestamp() (v int64) {
	return p.Timestamp
}

func (p *ToolCallRequiredEvent) GetEventOffset() (v int64) {
	return p.EventOffset
}

func (p *ToolCallRequiredEvent) GetToolCallID() (v string) {
	return p.ToolCallID
}

func (p *ToolCallRequiredEvent) GetEventKey() (v string) {
	return p.EventKey
}

var ToolCallRequiredEvent_TakeBrowserParams_DEFAULT *TakeBrowserParams

func (p *ToolCallRequiredEvent) GetTakeBrowserParams() (v *TakeBrowserParams) {
	if !p.IsSetTakeBrowserParams() {
		return ToolCallRequiredEvent_TakeBrowserParams_DEFAULT
	}
	return p.TakeBrowserParams
}

func (p *ToolCallRequiredEvent) GetStepID() (v string) {
	return p.StepID
}

func (p *ToolCallRequiredEvent) IsSetTakeBrowserParams() bool {
	return p.TakeBrowserParams != nil
}

func (p *ToolCallRequiredEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ToolCallRequiredEvent(%+v)", *p)
}

type TakeBrowserParams struct {
	// 接管浏览器参数
	StreamURL string `thrift:"StreamURL,1,required" json:"stream_url"`
	Reason    string `thrift:"Reason,2,required" json:"reason"`
	// 超时时间，单位秒
	Timeout int64 `thrift:"Timeout,3,required" json:"timeout"`
	// 是否需要用户确认是否保持登录状态
	AskKeepLogin *bool `thrift:"AskKeepLogin,4,optional" json:"ask_keep_login"`
}

func NewTakeBrowserParams() *TakeBrowserParams {
	return &TakeBrowserParams{}
}

func (p *TakeBrowserParams) InitDefault() {
}

func (p *TakeBrowserParams) GetStreamURL() (v string) {
	return p.StreamURL
}

func (p *TakeBrowserParams) GetReason() (v string) {
	return p.Reason
}

func (p *TakeBrowserParams) GetTimeout() (v int64) {
	return p.Timeout
}

var TakeBrowserParams_AskKeepLogin_DEFAULT bool

func (p *TakeBrowserParams) GetAskKeepLogin() (v bool) {
	if !p.IsSetAskKeepLogin() {
		return TakeBrowserParams_AskKeepLogin_DEFAULT
	}
	return *p.AskKeepLogin
}

func (p *TakeBrowserParams) IsSetAskKeepLogin() bool {
	return p.AskKeepLogin != nil
}

func (p *TakeBrowserParams) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TakeBrowserParams(%+v)", *p)
}

type ToolCallConfirmedEvent struct {
	EventID     string         `thrift:"EventID,1,required" json:"event_id"`
	SessionID   string         `thrift:"SessionID,2,required" json:"session_id"`
	ToolCallID  string         `thrift:"ToolCallID,3,required" json:"tool_call_id"`
	StepID      string         `thrift:"StepID,4,required" json:"step_id"`
	Action      ToolCallAction `thrift:"Action,5,required" json:"action"`
	Reason      string         `thrift:"Reason,6" json:"reason"`
	Timestamp   int64          `thrift:"Timestamp,7" json:"timestamp"`
	EventOffset int64          `thrift:"EventOffset,8" json:"event_offset"`
	EventKey    string         `thrift:"EventKey,9" json:"event_key"`
	Browser     *Browser       `thrift:"Browser,10,optional" json:"browser,omitempty"`
}

func NewToolCallConfirmedEvent() *ToolCallConfirmedEvent {
	return &ToolCallConfirmedEvent{}
}

func (p *ToolCallConfirmedEvent) InitDefault() {
}

func (p *ToolCallConfirmedEvent) GetEventID() (v string) {
	return p.EventID
}

func (p *ToolCallConfirmedEvent) GetSessionID() (v string) {
	return p.SessionID
}

func (p *ToolCallConfirmedEvent) GetToolCallID() (v string) {
	return p.ToolCallID
}

func (p *ToolCallConfirmedEvent) GetStepID() (v string) {
	return p.StepID
}

func (p *ToolCallConfirmedEvent) GetAction() (v ToolCallAction) {
	return p.Action
}

func (p *ToolCallConfirmedEvent) GetReason() (v string) {
	return p.Reason
}

func (p *ToolCallConfirmedEvent) GetTimestamp() (v int64) {
	return p.Timestamp
}

func (p *ToolCallConfirmedEvent) GetEventOffset() (v int64) {
	return p.EventOffset
}

func (p *ToolCallConfirmedEvent) GetEventKey() (v string) {
	return p.EventKey
}

var ToolCallConfirmedEvent_Browser_DEFAULT *Browser

func (p *ToolCallConfirmedEvent) GetBrowser() (v *Browser) {
	if !p.IsSetBrowser() {
		return ToolCallConfirmedEvent_Browser_DEFAULT
	}
	return p.Browser
}

func (p *ToolCallConfirmedEvent) IsSetBrowser() bool {
	return p.Browser != nil
}

func (p *ToolCallConfirmedEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ToolCallConfirmedEvent(%+v)", *p)
}

type SessionCompletedEvent struct {
	EventID     string `thrift:"EventID,1,required" json:"event_id"`
	SessionID   string `thrift:"SessionID,2,required" json:"session_id"`
	Status      string `thrift:"Status,3" json:"status"`
	Timestamp   int64  `thrift:"Timestamp,4" json:"timestamp"`
	EventOffset int64  `thrift:"EventOffset,5" json:"event_offset"`
	EventKey    string `thrift:"EventKey,6" json:"event_key"`
}

func NewSessionCompletedEvent() *SessionCompletedEvent {
	return &SessionCompletedEvent{}
}

func (p *SessionCompletedEvent) InitDefault() {
}

func (p *SessionCompletedEvent) GetEventID() (v string) {
	return p.EventID
}

func (p *SessionCompletedEvent) GetSessionID() (v string) {
	return p.SessionID
}

func (p *SessionCompletedEvent) GetStatus() (v string) {
	return p.Status
}

func (p *SessionCompletedEvent) GetTimestamp() (v int64) {
	return p.Timestamp
}

func (p *SessionCompletedEvent) GetEventOffset() (v int64) {
	return p.EventOffset
}

func (p *SessionCompletedEvent) GetEventKey() (v string) {
	return p.EventKey
}

func (p *SessionCompletedEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SessionCompletedEvent(%+v)", *p)
}

type SaveEventKeyRequest struct {
	Offset   int64    `thrift:"Offset,1,required" json:"offset"`
	Limit    int64    `thrift:"Limit,2,required" json:"limit"`
	EventIDs []string `thrift:"EventIDs,3,optional" json:"event_ids"`
}

func NewSaveEventKeyRequest() *SaveEventKeyRequest {
	return &SaveEventKeyRequest{}
}

func (p *SaveEventKeyRequest) InitDefault() {
}

func (p *SaveEventKeyRequest) GetOffset() (v int64) {
	return p.Offset
}

func (p *SaveEventKeyRequest) GetLimit() (v int64) {
	return p.Limit
}

var SaveEventKeyRequest_EventIDs_DEFAULT []string

func (p *SaveEventKeyRequest) GetEventIDs() (v []string) {
	if !p.IsSetEventIDs() {
		return SaveEventKeyRequest_EventIDs_DEFAULT
	}
	return p.EventIDs
}

func (p *SaveEventKeyRequest) IsSetEventIDs() bool {
	return p.EventIDs != nil
}

func (p *SaveEventKeyRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SaveEventKeyRequest(%+v)", *p)
}

type SaveEventKeyResponse struct {
	SuccessEventIDs []string `thrift:"SuccessEventIDs,1,required" json:"success_event_ids"`
	FailedEventIDs  []string `thrift:"FailedEventIDs,2,required" json:"failed_event_ids"`
}

func NewSaveEventKeyResponse() *SaveEventKeyResponse {
	return &SaveEventKeyResponse{}
}

func (p *SaveEventKeyResponse) InitDefault() {
}

func (p *SaveEventKeyResponse) GetSuccessEventIDs() (v []string) {
	return p.SuccessEventIDs
}

func (p *SaveEventKeyResponse) GetFailedEventIDs() (v []string) {
	return p.FailedEventIDs
}

func (p *SaveEventKeyResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SaveEventKeyResponse(%+v)", *p)
}
