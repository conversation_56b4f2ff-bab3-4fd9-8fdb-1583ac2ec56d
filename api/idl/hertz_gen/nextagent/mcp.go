// Code generated by thriftgo (0.4.2). DO NOT EDIT.

package nextagent

import (
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/common"
	"database/sql"
	"database/sql/driver"
	"fmt"
)

// MCP工具来源枚举
type MCPSource int64

const (
	// AIME平台
	MCPSource_AIME MCPSource = 1
	// 用户自定义
	MCPSource_UserDefine MCPSource = 2
	// 字节云平台
	MCPSource_Cloud MCPSource = 3
)

func (p MCPSource) String() string {
	switch p {
	case MCPSource_AIME:
		return "AIME"
	case MCPSource_UserDefine:
		return "UserDefine"
	case MCPSource_Cloud:
		return "Cloud"
	}
	return "<UNSET>"
}

func MCPSourceFromString(s string) (MCPSource, error) {
	switch s {
	case "AIME":
		return MCPSource_AIME, nil
	case "UserDefine":
		return MCPSource_UserDefine, nil
	case "Cloud":
		return MCPSource_Cloud, nil
	}
	return MCPSource(0), fmt.Errorf("not a valid MCPSource string")
}

func MCPSourcePtr(v MCPSource) *MCPSource { return &v }
func (p *MCPSource) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = MCPSource(result.Int64)
	return
}

func (p *MCPSource) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

// MCP工具类型枚举
type MCPType int64

const (
	// STDIO类型
	MCPType_STDIO MCPType = 1
	// SSE类型
	MCPType_SSE MCPType = 2
	// StreamableHTTP类型 (二期新增)
	MCPType_StreamableHTTP MCPType = 3
	// CloudSDK类型 (二期新增)
	MCPType_CloudSDK MCPType = 4
)

func (p MCPType) String() string {
	switch p {
	case MCPType_STDIO:
		return "STDIO"
	case MCPType_SSE:
		return "SSE"
	case MCPType_StreamableHTTP:
		return "StreamableHTTP"
	case MCPType_CloudSDK:
		return "CloudSDK"
	}
	return "<UNSET>"
}

func MCPTypeFromString(s string) (MCPType, error) {
	switch s {
	case "STDIO":
		return MCPType_STDIO, nil
	case "SSE":
		return MCPType_SSE, nil
	case "StreamableHTTP":
		return MCPType_StreamableHTTP, nil
	case "CloudSDK":
		return MCPType_CloudSDK, nil
	}
	return MCPType(0), fmt.Errorf("not a valid MCPType string")
}

func MCPTypePtr(v MCPType) *MCPType { return &v }
func (p *MCPType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = MCPType(result.Int64)
	return
}

func (p *MCPType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type MCPScope int64

const (
	// 个人
	MCPScope_Private MCPScope = 0
	// 公司内公开
	MCPScope_Public MCPScope = 1
	// 项目内公开
	MCPScope_ProjectPublic MCPScope = 2
)

func (p MCPScope) String() string {
	switch p {
	case MCPScope_Private:
		return "Private"
	case MCPScope_Public:
		return "Public"
	case MCPScope_ProjectPublic:
		return "ProjectPublic"
	}
	return "<UNSET>"
}

func MCPScopeFromString(s string) (MCPScope, error) {
	switch s {
	case "Private":
		return MCPScope_Private, nil
	case "Public":
		return MCPScope_Public, nil
	case "ProjectPublic":
		return MCPScope_ProjectPublic, nil
	}
	return MCPScope(0), fmt.Errorf("not a valid MCPScope string")
}

func MCPScopePtr(v MCPScope) *MCPScope { return &v }
func (p *MCPScope) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = MCPScope(result.Int64)
	return
}

func (p *MCPScope) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type MCPSourceTab int64

const (
	// 用户自定义MCP：个人创建+项目内公开
	MCPSourceTab_Custom MCPSourceTab = 0
	// 内置MCP：公司内公开
	MCPSourceTab_Builtin MCPSourceTab = 1
)

func (p MCPSourceTab) String() string {
	switch p {
	case MCPSourceTab_Custom:
		return "Custom"
	case MCPSourceTab_Builtin:
		return "Builtin"
	}
	return "<UNSET>"
}

func MCPSourceTabFromString(s string) (MCPSourceTab, error) {
	switch s {
	case "Custom":
		return MCPSourceTab_Custom, nil
	case "Builtin":
		return MCPSourceTab_Builtin, nil
	}
	return MCPSourceTab(0), fmt.Errorf("not a valid MCPSourceTab string")
}

func MCPSourceTabPtr(v MCPSourceTab) *MCPSourceTab { return &v }
func (p *MCPSourceTab) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = MCPSourceTab(result.Int64)
	return
}

func (p *MCPSourceTab) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

// 激活状态枚举
type ActiveStatus int64

const (
	// 激活
	ActiveStatus_ACTIVATE ActiveStatus = 1
	// 取消激活
	ActiveStatus_DEACTIVATE ActiveStatus = 2
)

func (p ActiveStatus) String() string {
	switch p {
	case ActiveStatus_ACTIVATE:
		return "ACTIVATE"
	case ActiveStatus_DEACTIVATE:
		return "DEACTIVATE"
	}
	return "<UNSET>"
}

func ActiveStatusFromString(s string) (ActiveStatus, error) {
	switch s {
	case "ACTIVATE":
		return ActiveStatus_ACTIVATE, nil
	case "DEACTIVATE":
		return ActiveStatus_DEACTIVATE, nil
	}
	return ActiveStatus(0), fmt.Errorf("not a valid ActiveStatus string")
}

func ActiveStatusPtr(v ActiveStatus) *ActiveStatus { return &v }
func (p *ActiveStatus) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = ActiveStatus(result.Int64)
	return
}

func (p *ActiveStatus) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

// MCP工具参数结构
type MCPConfig struct {
	// 命令
	Command *string `thrift:"Command,1,optional" json:"command"`
	// 参数列表
	Args []string `thrift:"Args,2,optional" json:"args"`
	// 环境变量
	Env map[string]string `thrift:"Env,3,optional" json:"env"`
	// SSE基础URL
	BaseURL *string `thrift:"BaseURL,4,optional" json:"base_url"`
	// PSM参数 (二期新增)
	PSM *string `thrift:"PSM,5,optional" json:"psm"`
}

func NewMCPConfig() *MCPConfig {
	return &MCPConfig{}
}

func (p *MCPConfig) InitDefault() {
}

var MCPConfig_Command_DEFAULT string

func (p *MCPConfig) GetCommand() (v string) {
	if !p.IsSetCommand() {
		return MCPConfig_Command_DEFAULT
	}
	return *p.Command
}

var MCPConfig_Args_DEFAULT []string

func (p *MCPConfig) GetArgs() (v []string) {
	if !p.IsSetArgs() {
		return MCPConfig_Args_DEFAULT
	}
	return p.Args
}

var MCPConfig_Env_DEFAULT map[string]string

func (p *MCPConfig) GetEnv() (v map[string]string) {
	if !p.IsSetEnv() {
		return MCPConfig_Env_DEFAULT
	}
	return p.Env
}

var MCPConfig_BaseURL_DEFAULT string

func (p *MCPConfig) GetBaseURL() (v string) {
	if !p.IsSetBaseURL() {
		return MCPConfig_BaseURL_DEFAULT
	}
	return *p.BaseURL
}

var MCPConfig_PSM_DEFAULT string

func (p *MCPConfig) GetPSM() (v string) {
	if !p.IsSetPSM() {
		return MCPConfig_PSM_DEFAULT
	}
	return *p.PSM
}

func (p *MCPConfig) IsSetCommand() bool {
	return p.Command != nil
}

func (p *MCPConfig) IsSetArgs() bool {
	return p.Args != nil
}

func (p *MCPConfig) IsSetEnv() bool {
	return p.Env != nil
}

func (p *MCPConfig) IsSetBaseURL() bool {
	return p.BaseURL != nil
}

func (p *MCPConfig) IsSetPSM() bool {
	return p.PSM != nil
}

func (p *MCPConfig) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MCPConfig(%+v)", *p)
}

// MCP (Multi-Cloud Platform) 工具定义
type MCP struct {
	// 来源不同的话，ID可能会重复
	ID          string     `thrift:"ID,1,required" json:"id"`
	Name        string     `thrift:"Name,2,required" json:"name"`
	Description string     `thrift:"Description,3,required" json:"description"`
	IconURL     string     `thrift:"IconURL,4,required" json:"icon_url"`
	Config      *MCPConfig `thrift:"Config,5,required" json:"config"`
	Creator     string     `thrift:"Creator,6,required" json:"creator"`
	Source      MCPSource  `thrift:"Source,7,required" json:"source"`
	CreatedAt   string     `thrift:"CreatedAt,8,required" json:"created_at"`
	UpdatedAt   string     `thrift:"UpdatedAt,9,required" json:"updated_at"`
	// 是否已添加到个人工具
	IsActive bool `thrift:"IsActive,10,required" json:"is_active"`
	// MCP工具类型
	Type MCPType `thrift:"Type,11,required" json:"type"`
	// 是否强制激活
	ForceActive bool `thrift:"ForceActive,12,required" json:"force_active"`
	// mcp工具英文名称
	ENName *string `thrift:"ENName,13,optional" json:"en_name"`
	// mcp 工具英文描述
	EnDescription *string `thrift:"EnDescription,14,optional" json:"en_description"`
	// 支持的SessionRole列表，为null表示支持所有Role (二期新增)
	SessionRoles []SessionRole `thrift:"SessionRoles,15,optional" json:"session_roles"`
	// 可见范围
	Scope *MCPScope `thrift:"Scope,16,optional" json:"scope"`
	// 权限列表
	Permissions []PermissionAction `thrift:"Permissions,17,optional" json:"permissions"`
}

func NewMCP() *MCP {
	return &MCP{}
}

func (p *MCP) InitDefault() {
}

func (p *MCP) GetID() (v string) {
	return p.ID
}

func (p *MCP) GetName() (v string) {
	return p.Name
}

func (p *MCP) GetDescription() (v string) {
	return p.Description
}

func (p *MCP) GetIconURL() (v string) {
	return p.IconURL
}

var MCP_Config_DEFAULT *MCPConfig

func (p *MCP) GetConfig() (v *MCPConfig) {
	if !p.IsSetConfig() {
		return MCP_Config_DEFAULT
	}
	return p.Config
}

func (p *MCP) GetCreator() (v string) {
	return p.Creator
}

func (p *MCP) GetSource() (v MCPSource) {
	return p.Source
}

func (p *MCP) GetCreatedAt() (v string) {
	return p.CreatedAt
}

func (p *MCP) GetUpdatedAt() (v string) {
	return p.UpdatedAt
}

func (p *MCP) GetIsActive() (v bool) {
	return p.IsActive
}

func (p *MCP) GetType() (v MCPType) {
	return p.Type
}

func (p *MCP) GetForceActive() (v bool) {
	return p.ForceActive
}

var MCP_ENName_DEFAULT string

func (p *MCP) GetENName() (v string) {
	if !p.IsSetENName() {
		return MCP_ENName_DEFAULT
	}
	return *p.ENName
}

var MCP_EnDescription_DEFAULT string

func (p *MCP) GetEnDescription() (v string) {
	if !p.IsSetEnDescription() {
		return MCP_EnDescription_DEFAULT
	}
	return *p.EnDescription
}

var MCP_SessionRoles_DEFAULT []SessionRole

func (p *MCP) GetSessionRoles() (v []SessionRole) {
	if !p.IsSetSessionRoles() {
		return MCP_SessionRoles_DEFAULT
	}
	return p.SessionRoles
}

var MCP_Scope_DEFAULT MCPScope

func (p *MCP) GetScope() (v MCPScope) {
	if !p.IsSetScope() {
		return MCP_Scope_DEFAULT
	}
	return *p.Scope
}

var MCP_Permissions_DEFAULT []PermissionAction

func (p *MCP) GetPermissions() (v []PermissionAction) {
	if !p.IsSetPermissions() {
		return MCP_Permissions_DEFAULT
	}
	return p.Permissions
}

func (p *MCP) IsSetConfig() bool {
	return p.Config != nil
}

func (p *MCP) IsSetENName() bool {
	return p.ENName != nil
}

func (p *MCP) IsSetEnDescription() bool {
	return p.EnDescription != nil
}

func (p *MCP) IsSetSessionRoles() bool {
	return p.SessionRoles != nil
}

func (p *MCP) IsSetScope() bool {
	return p.Scope != nil
}

func (p *MCP) IsSetPermissions() bool {
	return p.Permissions != nil
}

func (p *MCP) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MCP(%+v)", *p)
}

type MCPKey struct {
	ID     string    `thrift:"ID,1,required" json:"id"`
	Source MCPSource `thrift:"Source,2,required" json:"source"`
}

func NewMCPKey() *MCPKey {
	return &MCPKey{}
}

func (p *MCPKey) InitDefault() {
}

func (p *MCPKey) GetID() (v string) {
	return p.ID
}

func (p *MCPKey) GetSource() (v MCPSource) {
	return p.Source
}

func (p *MCPKey) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MCPKey(%+v)", *p)
}

// 创建MCP工具的请求
type CreateMCPRequest struct {
	// MCP工具名称
	Name string `thrift:"Name,1,required" json:"name"`
	// MCP工具描述
	Description string `thrift:"Description,2,required" json:"description"`
	// MCP工具图标URL
	IconURL *string `thrift:"IconURL,3,optional" json:"icon_url"`
	// MCP工具参数结构
	Config *MCPConfig `thrift:"Config,4,required" json:"config"`
	// 来源
	Source MCPSource `thrift:"Source,6,required" json:"source"`
	// MCP工具类型
	Type MCPType `thrift:"Type,7,required" json:"type"`
	// 是否强制激活
	ForceActive *bool `thrift:"ForceActive,8,optional" json:"force_active"`
	// mcp的唯一id，runtime使用需要
	UID *string `thrift:"UID,9,optional" json:"uid"`
	// mcp工具英文名称
	ENName *string `thrift:"ENName,10,optional" json:"en_name"`
	// mcp 工具英文描述
	EnDescription *string `thrift:"EnDescription,11,optional" json:"en_description"`
	// 支持的SessionRole列表 (二期新增)
	SessionRoles []SessionRole `thrift:"SessionRoles,12,optional" json:"session_roles"`
	// 空间ID
	SpaceID *string `thrift:"SpaceID,13,optional" json:"space_id"`
	// 可见范围
	Scope *MCPScope `thrift:"Scope,14,optional" json:"scope"`
}

func NewCreateMCPRequest() *CreateMCPRequest {
	return &CreateMCPRequest{}
}

func (p *CreateMCPRequest) InitDefault() {
}

func (p *CreateMCPRequest) GetName() (v string) {
	return p.Name
}

func (p *CreateMCPRequest) GetDescription() (v string) {
	return p.Description
}

var CreateMCPRequest_IconURL_DEFAULT string

func (p *CreateMCPRequest) GetIconURL() (v string) {
	if !p.IsSetIconURL() {
		return CreateMCPRequest_IconURL_DEFAULT
	}
	return *p.IconURL
}

var CreateMCPRequest_Config_DEFAULT *MCPConfig

func (p *CreateMCPRequest) GetConfig() (v *MCPConfig) {
	if !p.IsSetConfig() {
		return CreateMCPRequest_Config_DEFAULT
	}
	return p.Config
}

func (p *CreateMCPRequest) GetSource() (v MCPSource) {
	return p.Source
}

func (p *CreateMCPRequest) GetType() (v MCPType) {
	return p.Type
}

var CreateMCPRequest_ForceActive_DEFAULT bool

func (p *CreateMCPRequest) GetForceActive() (v bool) {
	if !p.IsSetForceActive() {
		return CreateMCPRequest_ForceActive_DEFAULT
	}
	return *p.ForceActive
}

var CreateMCPRequest_UID_DEFAULT string

func (p *CreateMCPRequest) GetUID() (v string) {
	if !p.IsSetUID() {
		return CreateMCPRequest_UID_DEFAULT
	}
	return *p.UID
}

var CreateMCPRequest_ENName_DEFAULT string

func (p *CreateMCPRequest) GetENName() (v string) {
	if !p.IsSetENName() {
		return CreateMCPRequest_ENName_DEFAULT
	}
	return *p.ENName
}

var CreateMCPRequest_EnDescription_DEFAULT string

func (p *CreateMCPRequest) GetEnDescription() (v string) {
	if !p.IsSetEnDescription() {
		return CreateMCPRequest_EnDescription_DEFAULT
	}
	return *p.EnDescription
}

var CreateMCPRequest_SessionRoles_DEFAULT []SessionRole

func (p *CreateMCPRequest) GetSessionRoles() (v []SessionRole) {
	if !p.IsSetSessionRoles() {
		return CreateMCPRequest_SessionRoles_DEFAULT
	}
	return p.SessionRoles
}

var CreateMCPRequest_SpaceID_DEFAULT string

func (p *CreateMCPRequest) GetSpaceID() (v string) {
	if !p.IsSetSpaceID() {
		return CreateMCPRequest_SpaceID_DEFAULT
	}
	return *p.SpaceID
}

var CreateMCPRequest_Scope_DEFAULT MCPScope

func (p *CreateMCPRequest) GetScope() (v MCPScope) {
	if !p.IsSetScope() {
		return CreateMCPRequest_Scope_DEFAULT
	}
	return *p.Scope
}

func (p *CreateMCPRequest) IsSetIconURL() bool {
	return p.IconURL != nil
}

func (p *CreateMCPRequest) IsSetConfig() bool {
	return p.Config != nil
}

func (p *CreateMCPRequest) IsSetForceActive() bool {
	return p.ForceActive != nil
}

func (p *CreateMCPRequest) IsSetUID() bool {
	return p.UID != nil
}

func (p *CreateMCPRequest) IsSetENName() bool {
	return p.ENName != nil
}

func (p *CreateMCPRequest) IsSetEnDescription() bool {
	return p.EnDescription != nil
}

func (p *CreateMCPRequest) IsSetSessionRoles() bool {
	return p.SessionRoles != nil
}

func (p *CreateMCPRequest) IsSetSpaceID() bool {
	return p.SpaceID != nil
}

func (p *CreateMCPRequest) IsSetScope() bool {
	return p.Scope != nil
}

func (p *CreateMCPRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateMCPRequest(%+v)", *p)
}

// 创建MCP工具的响应
type CreateMCPResponse struct {
	BaseResp *common.BaseResp `thrift:"BaseResp,1,required" json:"base_resp,required" `
	MCP      *MCP             `thrift:"MCP,2,required" json:"mcp"`
}

func NewCreateMCPResponse() *CreateMCPResponse {
	return &CreateMCPResponse{}
}

func (p *CreateMCPResponse) InitDefault() {
}

var CreateMCPResponse_BaseResp_DEFAULT *common.BaseResp

func (p *CreateMCPResponse) GetBaseResp() (v *common.BaseResp) {
	if !p.IsSetBaseResp() {
		return CreateMCPResponse_BaseResp_DEFAULT
	}
	return p.BaseResp
}

var CreateMCPResponse_MCP_DEFAULT *MCP

func (p *CreateMCPResponse) GetMCP() (v *MCP) {
	if !p.IsSetMCP() {
		return CreateMCPResponse_MCP_DEFAULT
	}
	return p.MCP
}

func (p *CreateMCPResponse) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *CreateMCPResponse) IsSetMCP() bool {
	return p.MCP != nil
}

func (p *CreateMCPResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateMCPResponse(%+v)", *p)
}

// 更新MCP工具的请求
type UpdateMCPRequest struct {
	// MCP工具ID
	ID string `thrift:"ID,1,required" form:"id,required" json:"id,required"`
	// 来源
	Source MCPSource `thrift:"Source,2,required" json:"source"`
	// MCP工具描述
	Description *string `thrift:"Description,3,optional" json:"description"`
	// MCP工具图标URL
	IconURL *string `thrift:"IconURL,4,optional" json:"icon_url"`
	// MCP工具参数结构
	Config *MCPConfig `thrift:"Config,5,optional" json:"config"`
	// MCP工具名称
	Name *string `thrift:"Name,6,optional" json:"name"`
	// MCP工具类型
	Type *MCPType `thrift:"Type,7,optional" json:"type"`
	// 是否强制激活
	ForceActive *bool `thrift:"ForceActive,8,optional" json:"force_active"`
	// mcp工具英文名称
	ENName *string `thrift:"ENName,9,optional" json:"en_name"`
	// mcp 工具英文描述
	EnDescription *string `thrift:"EnDescription,10,optional" json:"en_description"`
	// 支持的SessionRole列表 (二期新增)
	SessionRoles []SessionRole `thrift:"SessionRoles,11,optional" json:"session_roles"`
	// 可见范围
	Scope *MCPScope `thrift:"Scope,12,optional" json:"scope"`
}

func NewUpdateMCPRequest() *UpdateMCPRequest {
	return &UpdateMCPRequest{}
}

func (p *UpdateMCPRequest) InitDefault() {
}

func (p *UpdateMCPRequest) GetID() (v string) {
	return p.ID
}

func (p *UpdateMCPRequest) GetSource() (v MCPSource) {
	return p.Source
}

var UpdateMCPRequest_Description_DEFAULT string

func (p *UpdateMCPRequest) GetDescription() (v string) {
	if !p.IsSetDescription() {
		return UpdateMCPRequest_Description_DEFAULT
	}
	return *p.Description
}

var UpdateMCPRequest_IconURL_DEFAULT string

func (p *UpdateMCPRequest) GetIconURL() (v string) {
	if !p.IsSetIconURL() {
		return UpdateMCPRequest_IconURL_DEFAULT
	}
	return *p.IconURL
}

var UpdateMCPRequest_Config_DEFAULT *MCPConfig

func (p *UpdateMCPRequest) GetConfig() (v *MCPConfig) {
	if !p.IsSetConfig() {
		return UpdateMCPRequest_Config_DEFAULT
	}
	return p.Config
}

var UpdateMCPRequest_Name_DEFAULT string

func (p *UpdateMCPRequest) GetName() (v string) {
	if !p.IsSetName() {
		return UpdateMCPRequest_Name_DEFAULT
	}
	return *p.Name
}

var UpdateMCPRequest_Type_DEFAULT MCPType

func (p *UpdateMCPRequest) GetType() (v MCPType) {
	if !p.IsSetType() {
		return UpdateMCPRequest_Type_DEFAULT
	}
	return *p.Type
}

var UpdateMCPRequest_ForceActive_DEFAULT bool

func (p *UpdateMCPRequest) GetForceActive() (v bool) {
	if !p.IsSetForceActive() {
		return UpdateMCPRequest_ForceActive_DEFAULT
	}
	return *p.ForceActive
}

var UpdateMCPRequest_ENName_DEFAULT string

func (p *UpdateMCPRequest) GetENName() (v string) {
	if !p.IsSetENName() {
		return UpdateMCPRequest_ENName_DEFAULT
	}
	return *p.ENName
}

var UpdateMCPRequest_EnDescription_DEFAULT string

func (p *UpdateMCPRequest) GetEnDescription() (v string) {
	if !p.IsSetEnDescription() {
		return UpdateMCPRequest_EnDescription_DEFAULT
	}
	return *p.EnDescription
}

var UpdateMCPRequest_SessionRoles_DEFAULT []SessionRole

func (p *UpdateMCPRequest) GetSessionRoles() (v []SessionRole) {
	if !p.IsSetSessionRoles() {
		return UpdateMCPRequest_SessionRoles_DEFAULT
	}
	return p.SessionRoles
}

var UpdateMCPRequest_Scope_DEFAULT MCPScope

func (p *UpdateMCPRequest) GetScope() (v MCPScope) {
	if !p.IsSetScope() {
		return UpdateMCPRequest_Scope_DEFAULT
	}
	return *p.Scope
}

func (p *UpdateMCPRequest) IsSetDescription() bool {
	return p.Description != nil
}

func (p *UpdateMCPRequest) IsSetIconURL() bool {
	return p.IconURL != nil
}

func (p *UpdateMCPRequest) IsSetConfig() bool {
	return p.Config != nil
}

func (p *UpdateMCPRequest) IsSetName() bool {
	return p.Name != nil
}

func (p *UpdateMCPRequest) IsSetType() bool {
	return p.Type != nil
}

func (p *UpdateMCPRequest) IsSetForceActive() bool {
	return p.ForceActive != nil
}

func (p *UpdateMCPRequest) IsSetENName() bool {
	return p.ENName != nil
}

func (p *UpdateMCPRequest) IsSetEnDescription() bool {
	return p.EnDescription != nil
}

func (p *UpdateMCPRequest) IsSetSessionRoles() bool {
	return p.SessionRoles != nil
}

func (p *UpdateMCPRequest) IsSetScope() bool {
	return p.Scope != nil
}

func (p *UpdateMCPRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateMCPRequest(%+v)", *p)
}

// 更新MCP工具的响应
type UpdateMCPResponse struct {
	BaseResp *common.BaseResp `thrift:"BaseResp,1,required" json:"base_resp,required" `
	MCP      *MCP             `thrift:"MCP,2,required" json:"mcp"`
}

func NewUpdateMCPResponse() *UpdateMCPResponse {
	return &UpdateMCPResponse{}
}

func (p *UpdateMCPResponse) InitDefault() {
}

var UpdateMCPResponse_BaseResp_DEFAULT *common.BaseResp

func (p *UpdateMCPResponse) GetBaseResp() (v *common.BaseResp) {
	if !p.IsSetBaseResp() {
		return UpdateMCPResponse_BaseResp_DEFAULT
	}
	return p.BaseResp
}

var UpdateMCPResponse_MCP_DEFAULT *MCP

func (p *UpdateMCPResponse) GetMCP() (v *MCP) {
	if !p.IsSetMCP() {
		return UpdateMCPResponse_MCP_DEFAULT
	}
	return p.MCP
}

func (p *UpdateMCPResponse) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *UpdateMCPResponse) IsSetMCP() bool {
	return p.MCP != nil
}

func (p *UpdateMCPResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateMCPResponse(%+v)", *p)
}

// 列出MCP工具的请求
type ListMCPRequest struct {
	// 按名称搜索
	Name *string `thrift:"Name,1,optional" json:"name"`
	// 按来源列表搜索
	Sources []MCPSource `thrift:"Sources,2,optional" json:"sources"`
	// 按添加状态搜索
	IsActive *bool `thrift:"IsActive,3,optional" json:"is_active"`
	// 按类型列表搜索
	Types []MCPType `thrift:"Types,4,optional" json:"types"`
	// 按SessionRole搜索 (二期新增)
	SessionRole *SessionRole `thrift:"SessionRole,5,optional" json:"session_role"`
}

func NewListMCPRequest() *ListMCPRequest {
	return &ListMCPRequest{}
}

func (p *ListMCPRequest) InitDefault() {
}

var ListMCPRequest_Name_DEFAULT string

func (p *ListMCPRequest) GetName() (v string) {
	if !p.IsSetName() {
		return ListMCPRequest_Name_DEFAULT
	}
	return *p.Name
}

var ListMCPRequest_Sources_DEFAULT []MCPSource

func (p *ListMCPRequest) GetSources() (v []MCPSource) {
	if !p.IsSetSources() {
		return ListMCPRequest_Sources_DEFAULT
	}
	return p.Sources
}

var ListMCPRequest_IsActive_DEFAULT bool

func (p *ListMCPRequest) GetIsActive() (v bool) {
	if !p.IsSetIsActive() {
		return ListMCPRequest_IsActive_DEFAULT
	}
	return *p.IsActive
}

var ListMCPRequest_Types_DEFAULT []MCPType

func (p *ListMCPRequest) GetTypes() (v []MCPType) {
	if !p.IsSetTypes() {
		return ListMCPRequest_Types_DEFAULT
	}
	return p.Types
}

var ListMCPRequest_SessionRole_DEFAULT SessionRole

func (p *ListMCPRequest) GetSessionRole() (v SessionRole) {
	if !p.IsSetSessionRole() {
		return ListMCPRequest_SessionRole_DEFAULT
	}
	return *p.SessionRole
}

func (p *ListMCPRequest) IsSetName() bool {
	return p.Name != nil
}

func (p *ListMCPRequest) IsSetSources() bool {
	return p.Sources != nil
}

func (p *ListMCPRequest) IsSetIsActive() bool {
	return p.IsActive != nil
}

func (p *ListMCPRequest) IsSetTypes() bool {
	return p.Types != nil
}

func (p *ListMCPRequest) IsSetSessionRole() bool {
	return p.SessionRole != nil
}

func (p *ListMCPRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListMCPRequest(%+v)", *p)
}

// 列出MCP工具的响应
type ListMCPResponse struct {
	MCPs []*MCP `thrift:"MCPs,2,required" json:"mcps"`
	// 总数
	Total int32 `thrift:"Total,3,required" json:"total"`
}

func NewListMCPResponse() *ListMCPResponse {
	return &ListMCPResponse{}
}

func (p *ListMCPResponse) InitDefault() {
}

func (p *ListMCPResponse) GetMCPs() (v []*MCP) {
	return p.MCPs
}

func (p *ListMCPResponse) GetTotal() (v int32) {
	return p.Total
}

func (p *ListMCPResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListMCPResponse(%+v)", *p)
}

// 列出空间下MCP工具的请求
type ListSpaceMCPRequest struct {
	// 按名称搜索
	Name *string `thrift:"Name,1,optional" json:"name"`
	// 按来源列表搜索
	Sources []MCPSource `thrift:"Sources,2,optional" json:"sources"`
	// 按添加状态搜索
	IsActive *bool `thrift:"IsActive,3,optional" json:"is_active"`
	// 按类型列表搜索
	Types []MCPType `thrift:"Types,4,optional" json:"types"`
	// 按SessionRole搜索 (二期新增)
	SessionRole *SessionRole `thrift:"SessionRole,5,optional" json:"session_role"`
	// 空间ID
	SpaceID *string `thrift:"SpaceID,6,optional" json:"space_id"`
	// 起始ID
	NextID string `thrift:"NextID,7,required" json:"next_id"`
	// 限制数量
	Limit int64 `thrift:"Limit,8,required" json:"limit"`
	// 来源tab
	Tabs []MCPSourceTab `thrift:"Tabs,9,required" json:"tabs"`
}

func NewListSpaceMCPRequest() *ListSpaceMCPRequest {
	return &ListSpaceMCPRequest{}
}

func (p *ListSpaceMCPRequest) InitDefault() {
}

var ListSpaceMCPRequest_Name_DEFAULT string

func (p *ListSpaceMCPRequest) GetName() (v string) {
	if !p.IsSetName() {
		return ListSpaceMCPRequest_Name_DEFAULT
	}
	return *p.Name
}

var ListSpaceMCPRequest_Sources_DEFAULT []MCPSource

func (p *ListSpaceMCPRequest) GetSources() (v []MCPSource) {
	if !p.IsSetSources() {
		return ListSpaceMCPRequest_Sources_DEFAULT
	}
	return p.Sources
}

var ListSpaceMCPRequest_IsActive_DEFAULT bool

func (p *ListSpaceMCPRequest) GetIsActive() (v bool) {
	if !p.IsSetIsActive() {
		return ListSpaceMCPRequest_IsActive_DEFAULT
	}
	return *p.IsActive
}

var ListSpaceMCPRequest_Types_DEFAULT []MCPType

func (p *ListSpaceMCPRequest) GetTypes() (v []MCPType) {
	if !p.IsSetTypes() {
		return ListSpaceMCPRequest_Types_DEFAULT
	}
	return p.Types
}

var ListSpaceMCPRequest_SessionRole_DEFAULT SessionRole

func (p *ListSpaceMCPRequest) GetSessionRole() (v SessionRole) {
	if !p.IsSetSessionRole() {
		return ListSpaceMCPRequest_SessionRole_DEFAULT
	}
	return *p.SessionRole
}

var ListSpaceMCPRequest_SpaceID_DEFAULT string

func (p *ListSpaceMCPRequest) GetSpaceID() (v string) {
	if !p.IsSetSpaceID() {
		return ListSpaceMCPRequest_SpaceID_DEFAULT
	}
	return *p.SpaceID
}

func (p *ListSpaceMCPRequest) GetNextID() (v string) {
	return p.NextID
}

func (p *ListSpaceMCPRequest) GetLimit() (v int64) {
	return p.Limit
}

func (p *ListSpaceMCPRequest) GetTabs() (v []MCPSourceTab) {
	return p.Tabs
}

func (p *ListSpaceMCPRequest) IsSetName() bool {
	return p.Name != nil
}

func (p *ListSpaceMCPRequest) IsSetSources() bool {
	return p.Sources != nil
}

func (p *ListSpaceMCPRequest) IsSetIsActive() bool {
	return p.IsActive != nil
}

func (p *ListSpaceMCPRequest) IsSetTypes() bool {
	return p.Types != nil
}

func (p *ListSpaceMCPRequest) IsSetSessionRole() bool {
	return p.SessionRole != nil
}

func (p *ListSpaceMCPRequest) IsSetSpaceID() bool {
	return p.SpaceID != nil
}

func (p *ListSpaceMCPRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListSpaceMCPRequest(%+v)", *p)
}

// 列出空间下MCP工具的响应
type ListSpaceMCPResponse struct {
	MCPs []*MCP `thrift:"MCPs,1,required" json:"mcps"`
	// 是否还有更多
	HasMore bool          `thrift:"HasMore,2,required" json:"has_more"`
	NextID  string        `thrift:"NextID,3,required" json:"next_id"`
	Count   *ListMCPCount `thrift:"Count,4,required" json:"count"`
}

func NewListSpaceMCPResponse() *ListSpaceMCPResponse {
	return &ListSpaceMCPResponse{}
}

func (p *ListSpaceMCPResponse) InitDefault() {
}

func (p *ListSpaceMCPResponse) GetMCPs() (v []*MCP) {
	return p.MCPs
}

func (p *ListSpaceMCPResponse) GetHasMore() (v bool) {
	return p.HasMore
}

func (p *ListSpaceMCPResponse) GetNextID() (v string) {
	return p.NextID
}

var ListSpaceMCPResponse_Count_DEFAULT *ListMCPCount

func (p *ListSpaceMCPResponse) GetCount() (v *ListMCPCount) {
	if !p.IsSetCount() {
		return ListSpaceMCPResponse_Count_DEFAULT
	}
	return p.Count
}

func (p *ListSpaceMCPResponse) IsSetCount() bool {
	return p.Count != nil
}

func (p *ListSpaceMCPResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListSpaceMCPResponse(%+v)", *p)
}

type ListMCPCount struct {
	// 公司内公开总数
	PublicCount int64 `thrift:"PublicCount,1,required" json:"public_count"`
	// 个人创建总数
	CustomCount int64 `thrift:"CustomCount,2,required" json:"custom_count"`
	// 已激活总数
	ActivateCount int64 `thrift:"ActivateCount,3,required" json:"activate_count"`
	// 激活MCP上限
	ActivateLimit int64 `thrift:"ActivateLimit,4,required" json:"activate_limit"`
}

func NewListMCPCount() *ListMCPCount {
	return &ListMCPCount{}
}

func (p *ListMCPCount) InitDefault() {
}

func (p *ListMCPCount) GetPublicCount() (v int64) {
	return p.PublicCount
}

func (p *ListMCPCount) GetCustomCount() (v int64) {
	return p.CustomCount
}

func (p *ListMCPCount) GetActivateCount() (v int64) {
	return p.ActivateCount
}

func (p *ListMCPCount) GetActivateLimit() (v int64) {
	return p.ActivateLimit
}

func (p *ListMCPCount) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListMCPCount(%+v)", *p)
}

// 修改MCP工具激活状态的请求
type ModifyMCPActivationRequest struct {
	// MCP工具ID
	ID string `thrift:"ID,1,required" json:"id"`
	// MCP 工具来源
	Source MCPSource `thrift:"Source,2,required" json:"source"`
	// 激活状态：1-激活 2-取消激活
	Status ActiveStatus `thrift:"Status,3,required" json:"status"`
	// 空间ID
	SpaceID *string `thrift:"SpaceID,4,optional" json:"space_id"`
}

func NewModifyMCPActivationRequest() *ModifyMCPActivationRequest {
	return &ModifyMCPActivationRequest{}
}

func (p *ModifyMCPActivationRequest) InitDefault() {
}

func (p *ModifyMCPActivationRequest) GetID() (v string) {
	return p.ID
}

func (p *ModifyMCPActivationRequest) GetSource() (v MCPSource) {
	return p.Source
}

func (p *ModifyMCPActivationRequest) GetStatus() (v ActiveStatus) {
	return p.Status
}

var ModifyMCPActivationRequest_SpaceID_DEFAULT string

func (p *ModifyMCPActivationRequest) GetSpaceID() (v string) {
	if !p.IsSetSpaceID() {
		return ModifyMCPActivationRequest_SpaceID_DEFAULT
	}
	return *p.SpaceID
}

func (p *ModifyMCPActivationRequest) IsSetSpaceID() bool {
	return p.SpaceID != nil
}

func (p *ModifyMCPActivationRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyMCPActivationRequest(%+v)", *p)
}

// 修改MCP工具激活状态的响应
type ModifyMCPActivationResponse struct {
	MCP *MCP `thrift:"MCP,2,required" json:"mcp"`
}

func NewModifyMCPActivationResponse() *ModifyMCPActivationResponse {
	return &ModifyMCPActivationResponse{}
}

func (p *ModifyMCPActivationResponse) InitDefault() {
}

var ModifyMCPActivationResponse_MCP_DEFAULT *MCP

func (p *ModifyMCPActivationResponse) GetMCP() (v *MCP) {
	if !p.IsSetMCP() {
		return ModifyMCPActivationResponse_MCP_DEFAULT
	}
	return p.MCP
}

func (p *ModifyMCPActivationResponse) IsSetMCP() bool {
	return p.MCP != nil
}

func (p *ModifyMCPActivationResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyMCPActivationResponse(%+v)", *p)
}

// 验证MCP工具配置的请求
type ValidateMCPRequest struct {
	// MCP工具参数结构
	Config *MCPConfig `thrift:"Config,1,required" json:"config"`
	// 来源
	Source MCPSource `thrift:"Source,2,required" json:"source"`
	// MCP工具类型
	Type MCPType `thrift:"Type,3,required" json:"type"`
}

func NewValidateMCPRequest() *ValidateMCPRequest {
	return &ValidateMCPRequest{}
}

func (p *ValidateMCPRequest) InitDefault() {
}

var ValidateMCPRequest_Config_DEFAULT *MCPConfig

func (p *ValidateMCPRequest) GetConfig() (v *MCPConfig) {
	if !p.IsSetConfig() {
		return ValidateMCPRequest_Config_DEFAULT
	}
	return p.Config
}

func (p *ValidateMCPRequest) GetSource() (v MCPSource) {
	return p.Source
}

func (p *ValidateMCPRequest) GetType() (v MCPType) {
	return p.Type
}

func (p *ValidateMCPRequest) IsSetConfig() bool {
	return p.Config != nil
}

func (p *ValidateMCPRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ValidateMCPRequest(%+v)", *p)
}

// 验证MCP工具配置的响应
type ValidateMCPResponse struct {
	BaseResp *common.BaseResp `thrift:"BaseResp,1,required" json:"base_resp,required" `
	Valid    bool             `thrift:"Valid,2,required" json:"valid"`
}

func NewValidateMCPResponse() *ValidateMCPResponse {
	return &ValidateMCPResponse{}
}

func (p *ValidateMCPResponse) InitDefault() {
}

var ValidateMCPResponse_BaseResp_DEFAULT *common.BaseResp

func (p *ValidateMCPResponse) GetBaseResp() (v *common.BaseResp) {
	if !p.IsSetBaseResp() {
		return ValidateMCPResponse_BaseResp_DEFAULT
	}
	return p.BaseResp
}

func (p *ValidateMCPResponse) GetValid() (v bool) {
	return p.Valid
}

func (p *ValidateMCPResponse) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *ValidateMCPResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ValidateMCPResponse(%+v)", *p)
}
