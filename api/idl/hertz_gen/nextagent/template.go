// Code generated by thriftgo (0.4.2). DO NOT EDIT.

package nextagent

import (
	"database/sql"
	"database/sql/driver"
	"fmt"
)

type TemplateExperienceStatus int64

const (
	TemplateExperienceStatus_Success TemplateExperienceStatus = 0
	TemplateExperienceStatus_Failed  TemplateExperienceStatus = 1
)

func (p TemplateExperienceStatus) String() string {
	switch p {
	case TemplateExperienceStatus_Success:
		return "Success"
	case TemplateExperienceStatus_Failed:
		return "Failed"
	}
	return "<UNSET>"
}

func TemplateExperienceStatusFromString(s string) (TemplateExperienceStatus, error) {
	switch s {
	case "Success":
		return TemplateExperienceStatus_Success, nil
	case "Failed":
		return TemplateExperienceStatus_Failed, nil
	}
	return TemplateExperienceStatus(0), fmt.Errorf("not a valid TemplateExperienceStatus string")
}

func TemplateExperienceStatusPtr(v TemplateExperienceStatus) *TemplateExperienceStatus { return &v }
func (p *TemplateExperienceStatus) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = TemplateExperienceStatus(result.Int64)
	return
}

func (p *TemplateExperienceStatus) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

/* 给 Frontend 调用的接口 */
type GetTemplateRequest struct {
	TemplateID string  `thrift:"TemplateID,1,required" json:"template_id,required" path:"template_id,required"`
	SpaceID    *string `thrift:"SpaceID,2,optional" json:"space_id,omitempty" query:"space_id"`
}

func NewGetTemplateRequest() *GetTemplateRequest {
	return &GetTemplateRequest{}
}

func (p *GetTemplateRequest) InitDefault() {
}

func (p *GetTemplateRequest) GetTemplateID() (v string) {
	return p.TemplateID
}

var GetTemplateRequest_SpaceID_DEFAULT string

func (p *GetTemplateRequest) GetSpaceID() (v string) {
	if !p.IsSetSpaceID() {
		return GetTemplateRequest_SpaceID_DEFAULT
	}
	return *p.SpaceID
}

func (p *GetTemplateRequest) IsSetSpaceID() bool {
	return p.SpaceID != nil
}

func (p *GetTemplateRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetTemplateRequest(%+v)", *p)
}

type GetTemplateResponse struct {
	Template *Template `thrift:"Template,1,required" json:"template"`
	// 用户对该模板的权限
	Permissions []PermissionAction `thrift:"Permissions,2,required" json:"permissions"`
}

func NewGetTemplateResponse() *GetTemplateResponse {
	return &GetTemplateResponse{}
}

func (p *GetTemplateResponse) InitDefault() {
}

var GetTemplateResponse_Template_DEFAULT *Template

func (p *GetTemplateResponse) GetTemplate() (v *Template) {
	if !p.IsSetTemplate() {
		return GetTemplateResponse_Template_DEFAULT
	}
	return p.Template
}

func (p *GetTemplateResponse) GetPermissions() (v []PermissionAction) {
	return p.Permissions
}

func (p *GetTemplateResponse) IsSetTemplate() bool {
	return p.Template != nil
}

func (p *GetTemplateResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetTemplateResponse(%+v)", *p)
}

type CreateTemplateDraftRequest struct {
	TemplateKey *TemplateKey `thrift:"TemplateKey,1,required" json:"template_key"`
	SpaceID     *string      `thrift:"SpaceID,2,optional" json:"space_id"`
}

func NewCreateTemplateDraftRequest() *CreateTemplateDraftRequest {
	return &CreateTemplateDraftRequest{}
}

func (p *CreateTemplateDraftRequest) InitDefault() {
}

var CreateTemplateDraftRequest_TemplateKey_DEFAULT *TemplateKey

func (p *CreateTemplateDraftRequest) GetTemplateKey() (v *TemplateKey) {
	if !p.IsSetTemplateKey() {
		return CreateTemplateDraftRequest_TemplateKey_DEFAULT
	}
	return p.TemplateKey
}

var CreateTemplateDraftRequest_SpaceID_DEFAULT string

func (p *CreateTemplateDraftRequest) GetSpaceID() (v string) {
	if !p.IsSetSpaceID() {
		return CreateTemplateDraftRequest_SpaceID_DEFAULT
	}
	return *p.SpaceID
}

func (p *CreateTemplateDraftRequest) IsSetTemplateKey() bool {
	return p.TemplateKey != nil
}

func (p *CreateTemplateDraftRequest) IsSetSpaceID() bool {
	return p.SpaceID != nil
}

func (p *CreateTemplateDraftRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateTemplateDraftRequest(%+v)", *p)
}

type TemplateKey struct {
	SessionID            string `thrift:"SessionID,1,required" json:"session_id"`
	LatestEventTimestamp int64  `thrift:"LatestEventTimestamp,2,required" json:"latest_event_timestamp"`
}

func NewTemplateKey() *TemplateKey {
	return &TemplateKey{}
}

func (p *TemplateKey) InitDefault() {
}

func (p *TemplateKey) GetSessionID() (v string) {
	return p.SessionID
}

func (p *TemplateKey) GetLatestEventTimestamp() (v int64) {
	return p.LatestEventTimestamp
}

func (p *TemplateKey) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TemplateKey(%+v)", *p)
}

type CreateTemplateDraftResponse struct {
	Template *Template `thrift:"Template,1,required" json:"template"`
}

func NewCreateTemplateDraftResponse() *CreateTemplateDraftResponse {
	return &CreateTemplateDraftResponse{}
}

func (p *CreateTemplateDraftResponse) InitDefault() {
}

var CreateTemplateDraftResponse_Template_DEFAULT *Template

func (p *CreateTemplateDraftResponse) GetTemplate() (v *Template) {
	if !p.IsSetTemplate() {
		return CreateTemplateDraftResponse_Template_DEFAULT
	}
	return p.Template
}

func (p *CreateTemplateDraftResponse) IsSetTemplate() bool {
	return p.Template != nil
}

func (p *CreateTemplateDraftResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateTemplateDraftResponse(%+v)", *p)
}

// 获取模板草稿，根据 TemplateID 从指定的模板中获取最新模板草稿
type GetTemplateDraftRequest struct {
	TemplateID *string `thrift:"TemplateID,1,optional" json:"template_id,omitempty" path:"template_id"`
}

func NewGetTemplateDraftRequest() *GetTemplateDraftRequest {
	return &GetTemplateDraftRequest{}
}

func (p *GetTemplateDraftRequest) InitDefault() {
}

var GetTemplateDraftRequest_TemplateID_DEFAULT string

func (p *GetTemplateDraftRequest) GetTemplateID() (v string) {
	if !p.IsSetTemplateID() {
		return GetTemplateDraftRequest_TemplateID_DEFAULT
	}
	return *p.TemplateID
}

func (p *GetTemplateDraftRequest) IsSetTemplateID() bool {
	return p.TemplateID != nil
}

func (p *GetTemplateDraftRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetTemplateDraftRequest(%+v)", *p)
}

type GetTemplateDraftResponse struct {
	Template *Template `thrift:"Template,1,required" json:"template"`
}

func NewGetTemplateDraftResponse() *GetTemplateDraftResponse {
	return &GetTemplateDraftResponse{}
}

func (p *GetTemplateDraftResponse) InitDefault() {
}

var GetTemplateDraftResponse_Template_DEFAULT *Template

func (p *GetTemplateDraftResponse) GetTemplate() (v *Template) {
	if !p.IsSetTemplate() {
		return GetTemplateDraftResponse_Template_DEFAULT
	}
	return p.Template
}

func (p *GetTemplateDraftResponse) IsSetTemplate() bool {
	return p.Template != nil
}

func (p *GetTemplateDraftResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetTemplateDraftResponse(%+v)", *p)
}

// 创建模板，传不同的参数实现不同功能：
// 1. 传 SessionID + DraftTemplateID + Template，从指定的草稿模板和 Template 参数内容创建正式模板
// 2. 传 FromTemplateID，从指定的模板中复制新模板
type CreateTemplateRequest struct {
	SessionID       *string         `thrift:"SessionID,1,optional" json:"session_id"`
	DraftTemplateID *string         `thrift:"DraftTemplateID,2,optional" json:"draft_template_id"`
	FromTemplateID  *string         `thrift:"FromTemplateID,3,optional" json:"from_template_id"`
	Template        *ModifyTemplate `thrift:"Template,4,optional" json:"template"`
	SpaceID         *string         `thrift:"SpaceID,5,optional" json:"space_id"`
}

func NewCreateTemplateRequest() *CreateTemplateRequest {
	return &CreateTemplateRequest{}
}

func (p *CreateTemplateRequest) InitDefault() {
}

var CreateTemplateRequest_SessionID_DEFAULT string

func (p *CreateTemplateRequest) GetSessionID() (v string) {
	if !p.IsSetSessionID() {
		return CreateTemplateRequest_SessionID_DEFAULT
	}
	return *p.SessionID
}

var CreateTemplateRequest_DraftTemplateID_DEFAULT string

func (p *CreateTemplateRequest) GetDraftTemplateID() (v string) {
	if !p.IsSetDraftTemplateID() {
		return CreateTemplateRequest_DraftTemplateID_DEFAULT
	}
	return *p.DraftTemplateID
}

var CreateTemplateRequest_FromTemplateID_DEFAULT string

func (p *CreateTemplateRequest) GetFromTemplateID() (v string) {
	if !p.IsSetFromTemplateID() {
		return CreateTemplateRequest_FromTemplateID_DEFAULT
	}
	return *p.FromTemplateID
}

var CreateTemplateRequest_Template_DEFAULT *ModifyTemplate

func (p *CreateTemplateRequest) GetTemplate() (v *ModifyTemplate) {
	if !p.IsSetTemplate() {
		return CreateTemplateRequest_Template_DEFAULT
	}
	return p.Template
}

var CreateTemplateRequest_SpaceID_DEFAULT string

func (p *CreateTemplateRequest) GetSpaceID() (v string) {
	if !p.IsSetSpaceID() {
		return CreateTemplateRequest_SpaceID_DEFAULT
	}
	return *p.SpaceID
}

func (p *CreateTemplateRequest) IsSetSessionID() bool {
	return p.SessionID != nil
}

func (p *CreateTemplateRequest) IsSetDraftTemplateID() bool {
	return p.DraftTemplateID != nil
}

func (p *CreateTemplateRequest) IsSetFromTemplateID() bool {
	return p.FromTemplateID != nil
}

func (p *CreateTemplateRequest) IsSetTemplate() bool {
	return p.Template != nil
}

func (p *CreateTemplateRequest) IsSetSpaceID() bool {
	return p.SpaceID != nil
}

func (p *CreateTemplateRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateTemplateRequest(%+v)", *p)
}

type CreateTemplateResponse struct {
	Template *Template `thrift:"Template,1,required" json:"template"`
	// 用户对该模板的权限
	Permissions []PermissionAction `thrift:"Permissions,2,required" json:"permissions"`
}

func NewCreateTemplateResponse() *CreateTemplateResponse {
	return &CreateTemplateResponse{}
}

func (p *CreateTemplateResponse) InitDefault() {
}

var CreateTemplateResponse_Template_DEFAULT *Template

func (p *CreateTemplateResponse) GetTemplate() (v *Template) {
	if !p.IsSetTemplate() {
		return CreateTemplateResponse_Template_DEFAULT
	}
	return p.Template
}

func (p *CreateTemplateResponse) GetPermissions() (v []PermissionAction) {
	return p.Permissions
}

func (p *CreateTemplateResponse) IsSetTemplate() bool {
	return p.Template != nil
}

func (p *CreateTemplateResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateTemplateResponse(%+v)", *p)
}

// 更新模板，传不同的参数实现不同功能：
// 1. 传 NeedGenerateExperience + TemplateKey，将更新模板的经验
// 2. 传 Template，将更新模板的内容
type UpdateTemplateRequest struct {
	TemplateID             string          `thrift:"TemplateID,1,required" json:"template_id,required" path:"template_id,required"`
	NeedGenerateExperience *bool           `thrift:"NeedGenerateExperience,2,optional" json:"need_generate_experience"`
	TemplateKey            *TemplateKey    `thrift:"TemplateKey,3,optional" json:"template_key"`
	Template               *ModifyTemplate `thrift:"Template,4,optional" json:"template"`
}

func NewUpdateTemplateRequest() *UpdateTemplateRequest {
	return &UpdateTemplateRequest{}
}

func (p *UpdateTemplateRequest) InitDefault() {
}

func (p *UpdateTemplateRequest) GetTemplateID() (v string) {
	return p.TemplateID
}

var UpdateTemplateRequest_NeedGenerateExperience_DEFAULT bool

func (p *UpdateTemplateRequest) GetNeedGenerateExperience() (v bool) {
	if !p.IsSetNeedGenerateExperience() {
		return UpdateTemplateRequest_NeedGenerateExperience_DEFAULT
	}
	return *p.NeedGenerateExperience
}

var UpdateTemplateRequest_TemplateKey_DEFAULT *TemplateKey

func (p *UpdateTemplateRequest) GetTemplateKey() (v *TemplateKey) {
	if !p.IsSetTemplateKey() {
		return UpdateTemplateRequest_TemplateKey_DEFAULT
	}
	return p.TemplateKey
}

var UpdateTemplateRequest_Template_DEFAULT *ModifyTemplate

func (p *UpdateTemplateRequest) GetTemplate() (v *ModifyTemplate) {
	if !p.IsSetTemplate() {
		return UpdateTemplateRequest_Template_DEFAULT
	}
	return p.Template
}

func (p *UpdateTemplateRequest) IsSetNeedGenerateExperience() bool {
	return p.NeedGenerateExperience != nil
}

func (p *UpdateTemplateRequest) IsSetTemplateKey() bool {
	return p.TemplateKey != nil
}

func (p *UpdateTemplateRequest) IsSetTemplate() bool {
	return p.Template != nil
}

func (p *UpdateTemplateRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateTemplateRequest(%+v)", *p)
}

type UpdateTemplateResponse struct {
	Template *Template `thrift:"Template,1,required" json:"template"`
}

func NewUpdateTemplateResponse() *UpdateTemplateResponse {
	return &UpdateTemplateResponse{}
}

func (p *UpdateTemplateResponse) InitDefault() {
}

var UpdateTemplateResponse_Template_DEFAULT *Template

func (p *UpdateTemplateResponse) GetTemplate() (v *Template) {
	if !p.IsSetTemplate() {
		return UpdateTemplateResponse_Template_DEFAULT
	}
	return p.Template
}

func (p *UpdateTemplateResponse) IsSetTemplate() bool {
	return p.Template != nil
}

func (p *UpdateTemplateResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateTemplateResponse(%+v)", *p)
}

type DeleteTemplateRequest struct {
	TemplateID string `thrift:"TemplateID,1,required" json:"template_id,required" path:"template_id,required"`
}

func NewDeleteTemplateRequest() *DeleteTemplateRequest {
	return &DeleteTemplateRequest{}
}

func (p *DeleteTemplateRequest) InitDefault() {
}

func (p *DeleteTemplateRequest) GetTemplateID() (v string) {
	return p.TemplateID
}

func (p *DeleteTemplateRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteTemplateRequest(%+v)", *p)
}

type DeleteTemplateResponse struct {
}

func NewDeleteTemplateResponse() *DeleteTemplateResponse {
	return &DeleteTemplateResponse{}
}

func (p *DeleteTemplateResponse) InitDefault() {
}

func (p *DeleteTemplateResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteTemplateResponse(%+v)", *p)
}

/* 给 Runtime 调用的接口 */
type UpdateTemplateExperienceRequest struct {
	TemplateID string `thrift:"TemplateID,1,required" json:"template_id,required" path:"template_id,required"`
	// 粗粒度的经验
	ProgressPlan *string `thrift:"ProgressPlan,2,optional" json:"progress_plan"`
	// 细粒度的经验
	ExpSOP *string `thrift:"ExpSOP,3,optional" json:"exp_sop"`
	// 经验生成结果状态
	Status TemplateExperienceStatus `thrift:"Status,4,required" json:"status"`
	// 经验生成错误信息
	Error *string `thrift:"Error,5,optional" json:"error"`
	// 经验是否强制生效，默认会根据用户对模板的编辑操作来确定
	ForceActive *bool `thrift:"ForceActive,6,optional" json:"force_active"`
}

func NewUpdateTemplateExperienceRequest() *UpdateTemplateExperienceRequest {
	return &UpdateTemplateExperienceRequest{}
}

func (p *UpdateTemplateExperienceRequest) InitDefault() {
}

func (p *UpdateTemplateExperienceRequest) GetTemplateID() (v string) {
	return p.TemplateID
}

var UpdateTemplateExperienceRequest_ProgressPlan_DEFAULT string

func (p *UpdateTemplateExperienceRequest) GetProgressPlan() (v string) {
	if !p.IsSetProgressPlan() {
		return UpdateTemplateExperienceRequest_ProgressPlan_DEFAULT
	}
	return *p.ProgressPlan
}

var UpdateTemplateExperienceRequest_ExpSOP_DEFAULT string

func (p *UpdateTemplateExperienceRequest) GetExpSOP() (v string) {
	if !p.IsSetExpSOP() {
		return UpdateTemplateExperienceRequest_ExpSOP_DEFAULT
	}
	return *p.ExpSOP
}

func (p *UpdateTemplateExperienceRequest) GetStatus() (v TemplateExperienceStatus) {
	return p.Status
}

var UpdateTemplateExperienceRequest_Error_DEFAULT string

func (p *UpdateTemplateExperienceRequest) GetError() (v string) {
	if !p.IsSetError() {
		return UpdateTemplateExperienceRequest_Error_DEFAULT
	}
	return *p.Error
}

var UpdateTemplateExperienceRequest_ForceActive_DEFAULT bool

func (p *UpdateTemplateExperienceRequest) GetForceActive() (v bool) {
	if !p.IsSetForceActive() {
		return UpdateTemplateExperienceRequest_ForceActive_DEFAULT
	}
	return *p.ForceActive
}

func (p *UpdateTemplateExperienceRequest) IsSetProgressPlan() bool {
	return p.ProgressPlan != nil
}

func (p *UpdateTemplateExperienceRequest) IsSetExpSOP() bool {
	return p.ExpSOP != nil
}

func (p *UpdateTemplateExperienceRequest) IsSetError() bool {
	return p.Error != nil
}

func (p *UpdateTemplateExperienceRequest) IsSetForceActive() bool {
	return p.ForceActive != nil
}

func (p *UpdateTemplateExperienceRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateTemplateExperienceRequest(%+v)", *p)
}

type UpdateTemplateExperienceResponse struct {
}

func NewUpdateTemplateExperienceResponse() *UpdateTemplateExperienceResponse {
	return &UpdateTemplateExperienceResponse{}
}

func (p *UpdateTemplateExperienceResponse) InitDefault() {
}

func (p *UpdateTemplateExperienceResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateTemplateExperienceResponse(%+v)", *p)
}

type UploadTemplateExperienceFileStreamRequest struct {
	TemplateID string `thrift:"TemplateID,1,required" json:"template_id,required" path:"template_id,required"`
	Path       string `thrift:"Path,2,required" json:"path,required" query:"path,required"`
	Size       int64  `thrift:"Size,3,required" json:"size,required" query:"size,required"`
}

func NewUploadTemplateExperienceFileStreamRequest() *UploadTemplateExperienceFileStreamRequest {
	return &UploadTemplateExperienceFileStreamRequest{}
}

func (p *UploadTemplateExperienceFileStreamRequest) InitDefault() {
}

func (p *UploadTemplateExperienceFileStreamRequest) GetTemplateID() (v string) {
	return p.TemplateID
}

func (p *UploadTemplateExperienceFileStreamRequest) GetPath() (v string) {
	return p.Path
}

func (p *UploadTemplateExperienceFileStreamRequest) GetSize() (v int64) {
	return p.Size
}

func (p *UploadTemplateExperienceFileStreamRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UploadTemplateExperienceFileStreamRequest(%+v)", *p)
}

type UploadTemplateExperienceFileStreamResponse struct {
	File *TemplateFile `thrift:"File,1,required" json:"file"`
}

func NewUploadTemplateExperienceFileStreamResponse() *UploadTemplateExperienceFileStreamResponse {
	return &UploadTemplateExperienceFileStreamResponse{}
}

func (p *UploadTemplateExperienceFileStreamResponse) InitDefault() {
}

var UploadTemplateExperienceFileStreamResponse_File_DEFAULT *TemplateFile

func (p *UploadTemplateExperienceFileStreamResponse) GetFile() (v *TemplateFile) {
	if !p.IsSetFile() {
		return UploadTemplateExperienceFileStreamResponse_File_DEFAULT
	}
	return p.File
}

func (p *UploadTemplateExperienceFileStreamResponse) IsSetFile() bool {
	return p.File != nil
}

func (p *UploadTemplateExperienceFileStreamResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UploadTemplateExperienceFileStreamResponse(%+v)", *p)
}

type TemplateFile struct {
	FileID     string `thrift:"FileID,1,required" json:"file_id"`
	TemplateID string `thrift:"TemplateID,2,required" json:"template_id"`
	Name       string `thrift:"Name,3,required" json:"name"`
	Type       string `thrift:"Type,4,required" json:"type"`
}

func NewTemplateFile() *TemplateFile {
	return &TemplateFile{}
}

func (p *TemplateFile) InitDefault() {
}

func (p *TemplateFile) GetFileID() (v string) {
	return p.FileID
}

func (p *TemplateFile) GetTemplateID() (v string) {
	return p.TemplateID
}

func (p *TemplateFile) GetName() (v string) {
	return p.Name
}

func (p *TemplateFile) GetType() (v string) {
	return p.Type
}

func (p *TemplateFile) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TemplateFile(%+v)", *p)
}

type DownloadTemplateExperienceFileStreamRequest struct {
	FileID string `thrift:"FileID,1,required" json:"file_id,required" path:"file_id,required"`
	// 流式返回
	Stream *bool `thrift:"Stream,2,optional" json:"stream,omitempty" query:"stream"`
}

func NewDownloadTemplateExperienceFileStreamRequest() *DownloadTemplateExperienceFileStreamRequest {
	return &DownloadTemplateExperienceFileStreamRequest{}
}

func (p *DownloadTemplateExperienceFileStreamRequest) InitDefault() {
}

func (p *DownloadTemplateExperienceFileStreamRequest) GetFileID() (v string) {
	return p.FileID
}

var DownloadTemplateExperienceFileStreamRequest_Stream_DEFAULT bool

func (p *DownloadTemplateExperienceFileStreamRequest) GetStream() (v bool) {
	if !p.IsSetStream() {
		return DownloadTemplateExperienceFileStreamRequest_Stream_DEFAULT
	}
	return *p.Stream
}

func (p *DownloadTemplateExperienceFileStreamRequest) IsSetStream() bool {
	return p.Stream != nil
}

func (p *DownloadTemplateExperienceFileStreamRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DownloadTemplateExperienceFileStreamRequest(%+v)", *p)
}

type ListSpaceTemplatesRequest struct {
	NextID   *string           `thrift:"NextID,1,optional" json:"next_id,omitempty" query:"next_id"`
	Limit    *int64            `thrift:"Limit,2,optional" json:"limit,omitempty" query:"limit"`
	Category *TemplateCategory `thrift:"Category,3,optional" json:"category,omitempty" query:"category"`
	Search   *string           `thrift:"Search,4,optional" json:"search,omitempty" query:"search"`
	Source   *TemplateSource   `thrift:"Source,5,optional" json:"source,omitempty" query:"source"`
	Label    *TemplateLabel    `thrift:"Label,6,optional" json:"label,omitempty" query:"label"`
	SpaceID  *string           `thrift:"SpaceID,7,optional" json:"space_id,omitempty" query:"space_id"`
}

func NewListSpaceTemplatesRequest() *ListSpaceTemplatesRequest {
	return &ListSpaceTemplatesRequest{}
}

func (p *ListSpaceTemplatesRequest) InitDefault() {
}

var ListSpaceTemplatesRequest_NextID_DEFAULT string

func (p *ListSpaceTemplatesRequest) GetNextID() (v string) {
	if !p.IsSetNextID() {
		return ListSpaceTemplatesRequest_NextID_DEFAULT
	}
	return *p.NextID
}

var ListSpaceTemplatesRequest_Limit_DEFAULT int64

func (p *ListSpaceTemplatesRequest) GetLimit() (v int64) {
	if !p.IsSetLimit() {
		return ListSpaceTemplatesRequest_Limit_DEFAULT
	}
	return *p.Limit
}

var ListSpaceTemplatesRequest_Category_DEFAULT TemplateCategory

func (p *ListSpaceTemplatesRequest) GetCategory() (v TemplateCategory) {
	if !p.IsSetCategory() {
		return ListSpaceTemplatesRequest_Category_DEFAULT
	}
	return *p.Category
}

var ListSpaceTemplatesRequest_Search_DEFAULT string

func (p *ListSpaceTemplatesRequest) GetSearch() (v string) {
	if !p.IsSetSearch() {
		return ListSpaceTemplatesRequest_Search_DEFAULT
	}
	return *p.Search
}

var ListSpaceTemplatesRequest_Source_DEFAULT TemplateSource

func (p *ListSpaceTemplatesRequest) GetSource() (v TemplateSource) {
	if !p.IsSetSource() {
		return ListSpaceTemplatesRequest_Source_DEFAULT
	}
	return *p.Source
}

var ListSpaceTemplatesRequest_Label_DEFAULT TemplateLabel

func (p *ListSpaceTemplatesRequest) GetLabel() (v TemplateLabel) {
	if !p.IsSetLabel() {
		return ListSpaceTemplatesRequest_Label_DEFAULT
	}
	return *p.Label
}

var ListSpaceTemplatesRequest_SpaceID_DEFAULT string

func (p *ListSpaceTemplatesRequest) GetSpaceID() (v string) {
	if !p.IsSetSpaceID() {
		return ListSpaceTemplatesRequest_SpaceID_DEFAULT
	}
	return *p.SpaceID
}

func (p *ListSpaceTemplatesRequest) IsSetNextID() bool {
	return p.NextID != nil
}

func (p *ListSpaceTemplatesRequest) IsSetLimit() bool {
	return p.Limit != nil
}

func (p *ListSpaceTemplatesRequest) IsSetCategory() bool {
	return p.Category != nil
}

func (p *ListSpaceTemplatesRequest) IsSetSearch() bool {
	return p.Search != nil
}

func (p *ListSpaceTemplatesRequest) IsSetSource() bool {
	return p.Source != nil
}

func (p *ListSpaceTemplatesRequest) IsSetLabel() bool {
	return p.Label != nil
}

func (p *ListSpaceTemplatesRequest) IsSetSpaceID() bool {
	return p.SpaceID != nil
}

func (p *ListSpaceTemplatesRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListSpaceTemplatesRequest(%+v)", *p)
}

type ListSpaceTemplatesResponse struct {
	Templates []*Template `thrift:"Templates,1,required" json:"templates"`
	HasMore   bool        `thrift:"HasMore,2,required" json:"has_more"`
	NextID    string      `thrift:"NextID,3,required" json:"next_id"`
}

func NewListSpaceTemplatesResponse() *ListSpaceTemplatesResponse {
	return &ListSpaceTemplatesResponse{}
}

func (p *ListSpaceTemplatesResponse) InitDefault() {
}

func (p *ListSpaceTemplatesResponse) GetTemplates() (v []*Template) {
	return p.Templates
}

func (p *ListSpaceTemplatesResponse) GetHasMore() (v bool) {
	return p.HasMore
}

func (p *ListSpaceTemplatesResponse) GetNextID() (v string) {
	return p.NextID
}

func (p *ListSpaceTemplatesResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListSpaceTemplatesResponse(%+v)", *p)
}

type CountTemplatesRequest struct {
}

func NewCountTemplatesRequest() *CountTemplatesRequest {
	return &CountTemplatesRequest{}
}

func (p *CountTemplatesRequest) InitDefault() {
}

func (p *CountTemplatesRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CountTemplatesRequest(%+v)", *p)
}

type CountTemplatesResponse struct {
	// 我的模板总数，不随筛选条件变化
	MyTotal int64 `thrift:"MyTotal,1,required" json:"my_total"`
	// 收藏模板总数，不随筛选条件变化
	StarTotal int64 `thrift:"StarTotal,2,required" json:"star_total"`
	// 所有公开模板总数，不随筛选条件变化
	AllTotal int64 `thrift:"AllTotal,3,required" json:"all_total"`
}

func NewCountTemplatesResponse() *CountTemplatesResponse {
	return &CountTemplatesResponse{}
}

func (p *CountTemplatesResponse) InitDefault() {
}

func (p *CountTemplatesResponse) GetMyTotal() (v int64) {
	return p.MyTotal
}

func (p *CountTemplatesResponse) GetStarTotal() (v int64) {
	return p.StarTotal
}

func (p *CountTemplatesResponse) GetAllTotal() (v int64) {
	return p.AllTotal
}

func (p *CountTemplatesResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CountTemplatesResponse(%+v)", *p)
}

type CountSpaceTemplatesRequest struct {
	SpaceID *string `thrift:"SpaceID,1,optional" json:"space_id,omitempty" query:"space_id"`
}

func NewCountSpaceTemplatesRequest() *CountSpaceTemplatesRequest {
	return &CountSpaceTemplatesRequest{}
}

func (p *CountSpaceTemplatesRequest) InitDefault() {
}

var CountSpaceTemplatesRequest_SpaceID_DEFAULT string

func (p *CountSpaceTemplatesRequest) GetSpaceID() (v string) {
	if !p.IsSetSpaceID() {
		return CountSpaceTemplatesRequest_SpaceID_DEFAULT
	}
	return *p.SpaceID
}

func (p *CountSpaceTemplatesRequest) IsSetSpaceID() bool {
	return p.SpaceID != nil
}

func (p *CountSpaceTemplatesRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CountSpaceTemplatesRequest(%+v)", *p)
}

type CountSpaceTemplatesResponse struct {
	// 我的模板总数，不随筛选条件变化
	MyTotal int64 `thrift:"MyTotal,1,required" json:"my_total"`
	// 收藏模板总数，不随筛选条件变化
	StarTotal int64 `thrift:"StarTotal,2,required" json:"star_total"`
	// 所有公开模板总数，不随筛选条件变化
	AllTotal int64 `thrift:"AllTotal,3,required" json:"all_total"`
}

func NewCountSpaceTemplatesResponse() *CountSpaceTemplatesResponse {
	return &CountSpaceTemplatesResponse{}
}

func (p *CountSpaceTemplatesResponse) InitDefault() {
}

func (p *CountSpaceTemplatesResponse) GetMyTotal() (v int64) {
	return p.MyTotal
}

func (p *CountSpaceTemplatesResponse) GetStarTotal() (v int64) {
	return p.StarTotal
}

func (p *CountSpaceTemplatesResponse) GetAllTotal() (v int64) {
	return p.AllTotal
}

func (p *CountSpaceTemplatesResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CountSpaceTemplatesResponse(%+v)", *p)
}

type CreateTemplateStarRequest struct {
	TemplateID string  `thrift:"TemplateID,1,required" json:"template_id,required" path:"template_id,required"`
	SpaceID    *string `thrift:"SpaceID,2,optional" json:"space_id"`
}

func NewCreateTemplateStarRequest() *CreateTemplateStarRequest {
	return &CreateTemplateStarRequest{}
}

func (p *CreateTemplateStarRequest) InitDefault() {
}

func (p *CreateTemplateStarRequest) GetTemplateID() (v string) {
	return p.TemplateID
}

var CreateTemplateStarRequest_SpaceID_DEFAULT string

func (p *CreateTemplateStarRequest) GetSpaceID() (v string) {
	if !p.IsSetSpaceID() {
		return CreateTemplateStarRequest_SpaceID_DEFAULT
	}
	return *p.SpaceID
}

func (p *CreateTemplateStarRequest) IsSetSpaceID() bool {
	return p.SpaceID != nil
}

func (p *CreateTemplateStarRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateTemplateStarRequest(%+v)", *p)
}

type CreateTemplateStarResponse struct {
}

func NewCreateTemplateStarResponse() *CreateTemplateStarResponse {
	return &CreateTemplateStarResponse{}
}

func (p *CreateTemplateStarResponse) InitDefault() {
}

func (p *CreateTemplateStarResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateTemplateStarResponse(%+v)", *p)
}

type DeleteTemplateStarRequest struct {
	TemplateID string  `thrift:"TemplateID,1,required" json:"template_id,required" path:"template_id,required"`
	SpaceID    *string `thrift:"SpaceID,2,optional" json:"space_id"`
}

func NewDeleteTemplateStarRequest() *DeleteTemplateStarRequest {
	return &DeleteTemplateStarRequest{}
}

func (p *DeleteTemplateStarRequest) InitDefault() {
}

func (p *DeleteTemplateStarRequest) GetTemplateID() (v string) {
	return p.TemplateID
}

var DeleteTemplateStarRequest_SpaceID_DEFAULT string

func (p *DeleteTemplateStarRequest) GetSpaceID() (v string) {
	if !p.IsSetSpaceID() {
		return DeleteTemplateStarRequest_SpaceID_DEFAULT
	}
	return *p.SpaceID
}

func (p *DeleteTemplateStarRequest) IsSetSpaceID() bool {
	return p.SpaceID != nil
}

func (p *DeleteTemplateStarRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteTemplateStarRequest(%+v)", *p)
}

type DeleteTemplateStarResponse struct {
}

func NewDeleteTemplateStarResponse() *DeleteTemplateStarResponse {
	return &DeleteTemplateStarResponse{}
}

func (p *DeleteTemplateStarResponse) InitDefault() {
}

func (p *DeleteTemplateStarResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteTemplateStarResponse(%+v)", *p)
}

type CreateShareTemplateRequest struct {
	TemplateID string `thrift:"TemplateID,1,required" json:"template_id,required" path:"template_id,required"`
}

func NewCreateShareTemplateRequest() *CreateShareTemplateRequest {
	return &CreateShareTemplateRequest{}
}

func (p *CreateShareTemplateRequest) InitDefault() {
}

func (p *CreateShareTemplateRequest) GetTemplateID() (v string) {
	return p.TemplateID
}

func (p *CreateShareTemplateRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateShareTemplateRequest(%+v)", *p)
}

type CreateShareTemplateResponse struct {
	ShareID string `thrift:"ShareID,1,required" json:"share_id"`
}

func NewCreateShareTemplateResponse() *CreateShareTemplateResponse {
	return &CreateShareTemplateResponse{}
}

func (p *CreateShareTemplateResponse) InitDefault() {
}

func (p *CreateShareTemplateResponse) GetShareID() (v string) {
	return p.ShareID
}

func (p *CreateShareTemplateResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateShareTemplateResponse(%+v)", *p)
}

type CreateUserShareTemplateRequest struct {
	ShareID string `thrift:"ShareID,1,required" json:"share_id,required" path:"share_id,required"`
}

func NewCreateUserShareTemplateRequest() *CreateUserShareTemplateRequest {
	return &CreateUserShareTemplateRequest{}
}

func (p *CreateUserShareTemplateRequest) InitDefault() {
}

func (p *CreateUserShareTemplateRequest) GetShareID() (v string) {
	return p.ShareID
}

func (p *CreateUserShareTemplateRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateUserShareTemplateRequest(%+v)", *p)
}

type CreateUserShareTemplateResponse struct {
	Template *Template `thrift:"Template,1,required" json:"template"`
	// 用户对该模板的权限
	Permissions []PermissionAction `thrift:"Permissions,2,required" json:"permissions"`
}

func NewCreateUserShareTemplateResponse() *CreateUserShareTemplateResponse {
	return &CreateUserShareTemplateResponse{}
}

func (p *CreateUserShareTemplateResponse) InitDefault() {
}

var CreateUserShareTemplateResponse_Template_DEFAULT *Template

func (p *CreateUserShareTemplateResponse) GetTemplate() (v *Template) {
	if !p.IsSetTemplate() {
		return CreateUserShareTemplateResponse_Template_DEFAULT
	}
	return p.Template
}

func (p *CreateUserShareTemplateResponse) GetPermissions() (v []PermissionAction) {
	return p.Permissions
}

func (p *CreateUserShareTemplateResponse) IsSetTemplate() bool {
	return p.Template != nil
}

func (p *CreateUserShareTemplateResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateUserShareTemplateResponse(%+v)", *p)
}

type DeleteUserShareTemplateRequest struct {
	ShareID string `thrift:"ShareID,1,required" json:"share_id,required" path:"share_id,required"`
}

func NewDeleteUserShareTemplateRequest() *DeleteUserShareTemplateRequest {
	return &DeleteUserShareTemplateRequest{}
}

func (p *DeleteUserShareTemplateRequest) InitDefault() {
}

func (p *DeleteUserShareTemplateRequest) GetShareID() (v string) {
	return p.ShareID
}

func (p *DeleteUserShareTemplateRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteUserShareTemplateRequest(%+v)", *p)
}

type DeleteUserShareTemplateResponse struct {
}

func NewDeleteUserShareTemplateResponse() *DeleteUserShareTemplateResponse {
	return &DeleteUserShareTemplateResponse{}
}

func (p *DeleteUserShareTemplateResponse) InitDefault() {
}

func (p *DeleteUserShareTemplateResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteUserShareTemplateResponse(%+v)", *p)
}

type SaveTemplateShareFormDataRequest struct {
	FormData *TemplateFormValueDetail `thrift:"FormData,1,required" json:"form_data"`
}

func NewSaveTemplateShareFormDataRequest() *SaveTemplateShareFormDataRequest {
	return &SaveTemplateShareFormDataRequest{}
}

func (p *SaveTemplateShareFormDataRequest) InitDefault() {
}

var SaveTemplateShareFormDataRequest_FormData_DEFAULT *TemplateFormValueDetail

func (p *SaveTemplateShareFormDataRequest) GetFormData() (v *TemplateFormValueDetail) {
	if !p.IsSetFormData() {
		return SaveTemplateShareFormDataRequest_FormData_DEFAULT
	}
	return p.FormData
}

func (p *SaveTemplateShareFormDataRequest) IsSetFormData() bool {
	return p.FormData != nil
}

func (p *SaveTemplateShareFormDataRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SaveTemplateShareFormDataRequest(%+v)", *p)
}

type SaveTemplateShareFormDataResponse struct {
	ID string `thrift:"ID,1,required" json:"id"`
}

func NewSaveTemplateShareFormDataResponse() *SaveTemplateShareFormDataResponse {
	return &SaveTemplateShareFormDataResponse{}
}

func (p *SaveTemplateShareFormDataResponse) InitDefault() {
}

func (p *SaveTemplateShareFormDataResponse) GetID() (v string) {
	return p.ID
}

func (p *SaveTemplateShareFormDataResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SaveTemplateShareFormDataResponse(%+v)", *p)
}

type GetTemplateShareFormDataRequest struct {
	ID string `thrift:"ID,1,required" json:"id,required" path:"id,required"`
}

func NewGetTemplateShareFormDataRequest() *GetTemplateShareFormDataRequest {
	return &GetTemplateShareFormDataRequest{}
}

func (p *GetTemplateShareFormDataRequest) InitDefault() {
}

func (p *GetTemplateShareFormDataRequest) GetID() (v string) {
	return p.ID
}

func (p *GetTemplateShareFormDataRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetTemplateShareFormDataRequest(%+v)", *p)
}

type GetTemplateShareFormDataResponse struct {
	FormData *TemplateFormValueDetail `thrift:"FormData,1,required" json:"form_data"`
}

func NewGetTemplateShareFormDataResponse() *GetTemplateShareFormDataResponse {
	return &GetTemplateShareFormDataResponse{}
}

func (p *GetTemplateShareFormDataResponse) InitDefault() {
}

var GetTemplateShareFormDataResponse_FormData_DEFAULT *TemplateFormValueDetail

func (p *GetTemplateShareFormDataResponse) GetFormData() (v *TemplateFormValueDetail) {
	if !p.IsSetFormData() {
		return GetTemplateShareFormDataResponse_FormData_DEFAULT
	}
	return p.FormData
}

func (p *GetTemplateShareFormDataResponse) IsSetFormData() bool {
	return p.FormData != nil
}

func (p *GetTemplateShareFormDataResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetTemplateShareFormDataResponse(%+v)", *p)
}
