// Code generated by thriftgo (0.4.1). DO NOT EDIT.

package nextagent

import (
	"database/sql"
	"database/sql/driver"
	"fmt"
)

type ShowcaseCategory int64

const (
	ShowcaseCategory_Study    ShowcaseCategory = 0
	ShowcaseCategory_Code     ShowcaseCategory = 1
	ShowcaseCategory_Life     ShowcaseCategory = 2
	ShowcaseCategory_Analysis ShowcaseCategory = 3
)

func (p ShowcaseCategory) String() string {
	switch p {
	case ShowcaseCategory_Study:
		return "Study"
	case ShowcaseCategory_Code:
		return "Code"
	case ShowcaseCategory_Life:
		return "Life"
	case ShowcaseCategory_Analysis:
		return "Analysis"
	}
	return "<UNSET>"
}

func ShowcaseCategoryFromString(s string) (ShowcaseCategory, error) {
	switch s {
	case "Study":
		return ShowcaseCategory_Study, nil
	case "Code":
		return ShowcaseCategory_Code, nil
	case "Life":
		return ShowcaseCategory_Life, nil
	case "Analysis":
		return ShowcaseCategory_Analysis, nil
	}
	return ShowcaseCategory(0), fmt.<PERSON>rf("not a valid ShowcaseCategory string")
}

func ShowcaseCategoryPtr(v ShowcaseCategory) *ShowcaseCategory { return &v }
func (p *ShowcaseCategory) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = ShowcaseCategory(result.Int64)
	return
}

func (p *ShowcaseCategory) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type Showcase struct {
	ID          string `thrift:"ID,1,required" json:"id"`
	Title       string `thrift:"Title,2,required" json:"title"`
	Description string `thrift:"Description,3,required" json:"description"`
	ImageURL    string `thrift:"ImageURL,4,required" json:"image_url"`
	TotalSteps  int64  `thrift:"TotalSteps,5,required" json:"total_steps"`
	// in seconds
	Duration     int64               `thrift:"Duration,6,required" json:"duration"`
	ArtifactList []*ShowcaseArtifact `thrift:"ArtifactList,7,required" json:"artifacts"`
	Category     ShowcaseCategory    `thrift:"Category,8,required" json:"category"`
}

func NewShowcase() *Showcase {
	return &Showcase{}
}

func (p *Showcase) InitDefault() {
}

func (p *Showcase) GetID() (v string) {
	return p.ID
}

func (p *Showcase) GetTitle() (v string) {
	return p.Title
}

func (p *Showcase) GetDescription() (v string) {
	return p.Description
}

func (p *Showcase) GetImageURL() (v string) {
	return p.ImageURL
}

func (p *Showcase) GetTotalSteps() (v int64) {
	return p.TotalSteps
}

func (p *Showcase) GetDuration() (v int64) {
	return p.Duration
}

func (p *Showcase) GetArtifactList() (v []*ShowcaseArtifact) {
	return p.ArtifactList
}

func (p *Showcase) GetCategory() (v ShowcaseCategory) {
	return p.Category
}

func (p *Showcase) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Showcase(%+v)", *p)
}

type ShowcaseArtifact struct {
	Type ArtifactType `thrift:"Type,1,required" json:"type"`
	Name string       `thrift:"Name,2,required" json:"name"`
}

func NewShowcaseArtifact() *ShowcaseArtifact {
	return &ShowcaseArtifact{}
}

func (p *ShowcaseArtifact) InitDefault() {
}

func (p *ShowcaseArtifact) GetType() (v ArtifactType) {
	return p.Type
}

func (p *ShowcaseArtifact) GetName() (v string) {
	return p.Name
}

func (p *ShowcaseArtifact) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ShowcaseArtifact(%+v)", *p)
}
