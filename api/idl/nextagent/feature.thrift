namespace go nextagent
include "session.thrift"
include "activity.thrift"
include "types.thrift"

// GetCurrentActivity
struct GetGlobalFeaturesRequest {}

struct RoleFeature {
    1: required types.SessionRole Role (go.tag = "json:\"role\""),
    // 休眠后最长可唤醒的天数
    2: required i32 MaxResumeDays (go.tag = "json:\"max_resume_days\""),
    // 事件不更新后多久时间进入休眠
    3: required i32 SleepingTimeHours (go.tag = "json:\"sleeping_time_hours\""),
}

struct GetGlobalFeaturesResponse {
    1: optional activity.Activity CurrentActivity (go.tag = "json:\"current_activity\"")
    2: optional list<RoleFeature> RoleFeatures (go.tag = "json:\"role_features\"")
}

struct GetUserFeaturesRequest {
}

struct GetUserFeaturesResponse {
   1: required bool Invited (go.tag = "json:\"invited\""),
   // 没有返回该字段表示不限制
   2: optional i64 SessionLimit (go.tag = "json:\"session_limit,omitempty\"")
   3: optional i64 MessageLimit (go.tag = "json:\"message_limit,omitempty\"")
   4: optional i64 MessageWarning (go.tag = "json:\"message_warning,omitempty\"")
   5: optional list<session.SessionRoleConfig> RoleConfigs (go.tag = "json:\"role_configs,omitempty\"")
   // 新版空间灰度字段
   6: optional bool UseSpace (go.tag = "json:\"use_space,omitempty\"")
}