namespace go nextagent

include "../common.thrift"
include "types.thrift"
include "permission.thrift"

// MCP工具来源枚举
enum MCPSource {
    AIME = 1        // AIME平台
    UserDefine = 2  // 用户自定义
    Cloud = 3       // 字节云平台
}

// MCP工具类型枚举
enum MCPType {
    STDIO = 1      // STDIO类型
    SSE = 2        // SSE类型
    StreamableHTTP = 3  // StreamableHTTP类型 (二期新增)
    CloudSDK = 4   // CloudSDK类型 (二期新增)
}

enum MCPScope {
    Private = 0           // 个人
    Public = 1            // 公司内公开
    ProjectPublic = 2     // 项目内公开
}

enum MCPSourceTab {
    Custom = 0           // 用户自定义MCP：个人创建+项目内公开
    Builtin = 1          // 内置MCP：公司内公开
}

// MCP工具参数结构
struct MCPConfig {
    1: optional string Command (go.tag = "json:\"command\""),        // 命令
    2: optional list<string> Args (go.tag = "json:\"args\""),        // 参数列表
    3: optional map<string, string> Env (go.tag = "json:\"env\""),   // 环境变量
    4: optional string BaseURL (go.tag = "json:\"base_url\""),       // SSE基础URL
    5: optional string PSM (go.tag = "json:\"psm\""),               // PSM参数 (二期新增)
}

// MCP (Multi-Cloud Platform) 工具定义
struct MCP {
    // 来源不同的话，ID可能会重复
    1: required string ID (go.tag = "json:\"id\""),
    2: required string Name (go.tag = "json:\"name\""),
    3: required string Description (go.tag = "json:\"description\""),
    4: required string IconURL (go.tag = "json:\"icon_url\""),
    5: required MCPConfig Config (go.tag = "json:\"config\""),
    6: required string Creator (go.tag = "json:\"creator\""),
    7: required MCPSource Source (go.tag = "json:\"source\""),
    8: required string CreatedAt (go.tag = "json:\"created_at\""),
    9: required string UpdatedAt (go.tag = "json:\"updated_at\""),
    10: required bool IsActive (go.tag = "json:\"is_active\""),      // 是否已添加到个人工具
    11: required MCPType Type (go.tag = "json:\"type\""),           // MCP工具类型
    12: required bool ForceActive (go.tag = "json:\"force_active\""), // 是否强制激活
    13: optional string ENName (go.tag = "json:\"en_name\"") // mcp工具英文名称
    14: optional string EnDescription (go.tag = "json:\"en_description\"") // mcp 工具英文描述
    15: optional list<types.SessionRole> SessionRoles (go.tag = "json:\"session_roles\"") // 支持的SessionRole列表，为null表示支持所有Role (二期新增)
    16: optional MCPScope Scope (go.tag = "json:\"scope\"") // 可见范围
    17: optional list<permission.PermissionAction> Permissions (go.tag = "json:\"permissions\"") // 权限列表
}

struct MCPKey {
    1: required string ID (go.tag = "json:\"id\""),
    2: required MCPSource Source (go.tag = "json:\"source\""),
}

// 创建MCP工具的请求
struct CreateMCPRequest {
    1: required string Name (go.tag = "json:\"name\""),           // MCP工具名称
    2: required string Description (go.tag = "json:\"description\""), // MCP工具描述
    3: optional string IconURL (go.tag = "json:\"icon_url\""),    // MCP工具图标URL
    4: required MCPConfig Config (go.tag = "json:\"config\""),    // MCP工具参数结构
    6: required MCPSource Source (go.tag = "json:\"source\""),    // 来源
    7: required MCPType Type (go.tag = "json:\"type\""),         // MCP工具类型
    8: optional bool ForceActive (go.tag = "json:\"force_active\""), // 是否强制激活
    9: optional string UID (go.tag = "json:\"uid\"") // mcp的唯一id，runtime使用需要
    10: optional string ENName (go.tag = "json:\"en_name\"") // mcp工具英文名称
    11: optional string EnDescription (go.tag = "json:\"en_description\"") // mcp 工具英文描述
    12: optional list<types.SessionRole> SessionRoles (go.tag = "json:\"session_roles\"") // 支持的SessionRole列表 (二期新增)
    13: optional string SpaceID (go.tag = "json:\"space_id\"") // 空间ID
    14: optional MCPScope Scope (go.tag = "json:\"scope\"") // 可见范围
}

// 创建MCP工具的响应
struct CreateMCPResponse {
    1: required common.BaseResp BaseResp
    2: required MCP MCP (go.tag = "json:\"mcp\""),
}

// 更新MCP工具的请求
struct UpdateMCPRequest {
    1: required string ID (api.body = "id"),                        // MCP工具ID
    2: required MCPSource Source (go.tag = "json:\"source\""),   // 来源
    3: optional string Description (go.tag = "json:\"description\""), // MCP工具描述
    4: optional string IconURL (go.tag = "json:\"icon_url\""),   // MCP工具图标URL
    5: optional MCPConfig Config (go.tag = "json:\"config\""),   // MCP工具参数结构
    6: optional string Name (go.tag = "json:\"name\""),          // MCP工具名称
    7: optional MCPType Type (go.tag = "json:\"type\""),        // MCP工具类型
    8: optional bool ForceActive (go.tag = "json:\"force_active\""), // 是否强制激活
    9: optional string ENName (go.tag = "json:\"en_name\"") // mcp工具英文名称
    10: optional string EnDescription (go.tag = "json:\"en_description\"") // mcp 工具英文描述
    11: optional list<types.SessionRole> SessionRoles (go.tag = "json:\"session_roles\"") // 支持的SessionRole列表 (二期新增)
    12: optional MCPScope Scope (go.tag = "json:\"scope\"") // 可见范围
}

// 更新MCP工具的响应
struct UpdateMCPResponse {
    1: required common.BaseResp BaseResp
    2: required MCP MCP (go.tag = "json:\"mcp\""),
}

// 列出MCP工具的请求
struct ListMCPRequest {
    1: optional string Name (go.tag = "json:\"name\""),                // 按名称搜索
    2: optional list<MCPSource> Sources (go.tag = "json:\"sources\""), // 按来源列表搜索
    3: optional bool IsActive (go.tag = "json:\"is_active\""),        // 按添加状态搜索
    4: optional list<MCPType> Types (go.tag = "json:\"types\""),      // 按类型列表搜索
    5: optional types.SessionRole SessionRole (go.tag = "json:\"session_role\""), // 按SessionRole搜索 (二期新增)
}

// 列出MCP工具的响应
struct ListMCPResponse {
    2: required list<MCP> MCPs (go.tag = "json:\"mcps\""),
    3: required i32 Total (go.tag = "json:\"total\""),          // 总数
}

// 列出空间下MCP工具的请求
struct ListSpaceMCPRequest {
    1: optional string Name (go.tag = "json:\"name\""),                // 按名称搜索
    2: optional list<MCPSource> Sources (go.tag = "json:\"sources\""), // 按来源列表搜索
    3: optional bool IsActive (go.tag = "json:\"is_active\""),        // 按添加状态搜索
    4: optional list<MCPType> Types (go.tag = "json:\"types\""),      // 按类型列表搜索
    5: optional types.SessionRole SessionRole (go.tag = "json:\"session_role\""), // 按SessionRole搜索 (二期新增)
    6: optional string SpaceID (go.tag = "json:\"space_id\"") // 空间ID
    7: required string NextID (go.tag = "json:\"next_id\"") // 起始ID
    8: required i64 Limit (go.tag = "json:\"limit\"") // 限制数量
    9: required list<MCPSourceTab> Tabs (go.tag = "json:\"tabs\"") // 来源tab
}

// 列出空间下MCP工具的响应
struct ListSpaceMCPResponse {
    1: required list<MCP> MCPs (go.tag = "json:\"mcps\""),
    2: required bool HasMore (go.tag = "json:\"has_more\""), // 是否还有更多
    3: required string NextID (go.tag = "json:\"next_id\""),
    4: required ListMCPCount Count (go.tag = "json:\"count\""),
}

struct ListMCPCount {
    1: required i64 PublicCount (go.tag = "json:\"public_count\""), // 公司内公开总数
    2: required i64 CustomCount (go.tag = "json:\"custom_count\""), // 个人创建总数
    3: required i64 ActivateCount (go.tag = "json:\"activate_count\""), // 已激活总数
    4: required i64 ActivateLimit (go.tag = "json:\"activate_limit\""), // 激活MCP上限
}

// 激活状态枚举
enum ActiveStatus {
    ACTIVATE = 1      // 激活
    DEACTIVATE = 2    // 取消激活
}

// 修改MCP工具激活状态的请求
struct ModifyMCPActivationRequest {
    1: required string ID (go.tag = "json:\"id\""),                       // MCP工具ID
    2: required MCPSource Source (go.tag = "json:\"source\"") // MCP 工具来源
    3: required ActiveStatus Status (go.tag = "json:\"status\""), // 激活状态：1-激活 2-取消激活
    4: optional string SpaceID (go.tag = "json:\"space_id\"") // 空间ID
}

// 修改MCP工具激活状态的响应
struct ModifyMCPActivationResponse {
    2: required MCP MCP (go.tag = "json:\"mcp\""),
}

// 验证MCP工具配置的请求
struct ValidateMCPRequest {
    1: required MCPConfig Config (go.tag = "json:\"config\""),   // MCP工具参数结构
    2: required MCPSource Source (go.tag = "json:\"source\""),    // 来源
    3: required MCPType Type (go.tag = "json:\"type\""),         // MCP工具类型
}

// 验证MCP工具配置的响应
struct ValidateMCPResponse {
    1: required common.BaseResp BaseResp
    2: required bool Valid (go.tag = "json:\"valid\""),
}