namespace go nextagent
include "knowledgebase.thrift"


struct Mention {
    1: required string ID (go.tag = "json:\"id\""),
    2: required MentionType Type (go.tag = "json:\"type\""),
    3: optional CodebaseMention CodebaseMention (go.tag = "json:\"codebase_mention\""),
    4: optional KnowledgeMention KnowledgeMention (go.tag = "json:\"knowledge_mention\""),
    5: optional AttachmentMention AttachmentMention (go.tag = "json:\"attachment_mention\""),
}

struct CodebaseMention {
    1: required string RepoName (go.tag = "json:\"repo_name\""),
    2: optional string Branch (go.tag = "json:\"branch\""),
    3: optional string Tag (go.tag = "json:\"tag\""),
    4: optional string FilePath (go.tag = "json:\"file_path\""),
    5: optional string Directory (go.tag = "json:\"directory\""),
}

struct KnowledgeMention {
    1: required string KnowledgeBaseID (go.tag = "json:\"knowledge_base_id\""),
    2: required string DocumentID (go.tag = "json:\"document_id\""), // 文档ID
    3: optional string Title (go.tag = "json:\"title\""),
    4: optional string URL (go.tag = "json:\"url\""),
}
struct AttachmentMention {
    1: required string ArtifactID (go.tag = "json:\"artifact_id\""),
    2: required string Path (go.tag = "json:\"path\""),
}

typedef string MentionType
const MentionType MentionTypeUnknown = "unknown"
const MentionType MentionTypeKnowledgeBase = "knowledgebase"
const MentionType MentionTypeRepository = "repository"
const MentionType MentionTypeRepositoryBranch = "repository_branch"
const MentionType MentionTypeRepositoryDir = "repository_dir"
const MentionType MentionTypeRepositoryFile = "repository_file"
const MentionType MentionTypeAttachment = "attachment"


typedef string MentionOrderBy
const MentionOrderBy MentionOrderByUnknown = "unknown"
const MentionOrderBy MentionOrderByHeat = "heat"

struct SearchMentionsRequest {
    // 搜索知识库必须传
    1: optional string SpaceID (api.query = "space_id"),
    // 不填的情况下，服务端返回一个默认的数据
    2: optional MentionType type (api.query = "type"),
    // 支持 id or path 两种
    3: optional string RepositoryID (api.query = "repository_id"),
    4: optional string RepositoryBranch (api.query = "repository_branch"),
    5: optional string Query (api.query = "query"),
    6: optional i32 Limit (api.query = "limit"),
    7: optional string NextID (api.query = "next_id"),
    8: optional MentionOrderBy OrderBy (api.query = "order_by"),
}

struct SearchMentionsResponse {
    1: required MentionType type (go.tag = "json:\"type\""),
    2: optional string NextID (go.tag = "json:\"next_id\""),
    3: optional bool HasMore (go.tag = "json:\"has_more\""),
    4: optional list<knowledgebase.KnowledgeBaseDocument> KnowledgeBaseDocuments (go.tag = "json:\"knowledgebase_documents\""),
    5: optional list<Repository> Repositories (go.tag = "json:\"repositories\""),
    6: optional list<Branch> Branches (go.tag = "json:\"branches\""),
    7: optional list<Dir> Dirs (go.tag = "json:\"dirs\""),
    8: optional list<File> Files (go.tag = "json:\"files\""),
}

struct Repository {
    1: required i64 ID (go.tag = "json:\"id\""),
    2: required string Name  (go.tag = "json:\"name\""),
}

struct Branch {
    1: required string Name (go.tag = "json:\"name\""),
    2: required string CommitSha (go.tag = "json:\"commit_sha\""),
}

struct Dir {
    1: required string Path (go.tag = "json:\"path\""),
}

struct File {
    1: required string Path (go.tag = "json:\"path\""),
}