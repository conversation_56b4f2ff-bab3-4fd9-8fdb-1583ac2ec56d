namespace go nextagent

include "../common.thrift"
include "session.thrift"
include "permission.thrift"

/* 给 Frontend 调用的接口 */

struct GetTemplateRequest {
    1: required string TemplateID (api.path="template_id")
    2: optional string SpaceID (api.query="space_id")
}

struct GetTemplateResponse {
    1: required session.Template Template (go.tag = "json:\"template\""),
    2: required list<permission.PermissionAction> Permissions (go.tag = "json:\"permissions\""), // 用户对该模板的权限
}

struct CreateTemplateDraftRequest {
    1: required TemplateKey TemplateKey (go.tag = "json:\"template_key\""),
    2: optional string SpaceID (go.tag = "json:\"space_id\""),
}

struct TemplateKey {
    1: required string SessionID (go.tag = "json:\"session_id\""),
    2: required i64 LatestEventTimestamp (go.tag = "json:\"latest_event_timestamp\""),
}

struct CreateTemplateDraftResponse {
    1: required session.Template Template (go.tag = "json:\"template\""),
}

// 获取模板草稿，根据 TemplateID 从指定的模板中获取最新模板草稿
struct GetTemplateDraftRequest {
    1: optional string TemplateID (api.path="template_id"),
}

struct GetTemplateDraftResponse {
    1: required session.Template Template (go.tag = "json:\"template\""),
}

// 创建模板，传不同的参数实现不同功能：
// 1. 传 SessionID + DraftTemplateID + Template，从指定的草稿模板和 Template 参数内容创建正式模板
// 2. 传 FromTemplateID，从指定的模板中复制新模板
struct CreateTemplateRequest {
    1: optional string SessionID (go.tag = "json:\"session_id\""),
    2: optional string DraftTemplateID (go.tag = "json:\"draft_template_id\""),
    3: optional string FromTemplateID  (go.tag = "json:\"from_template_id\""),
    4: optional session.ModifyTemplate Template (go.tag = "json:\"template\""),
    5: optional string SpaceID (go.tag = "json:\"space_id\""),
}

struct CreateTemplateResponse {
    1: required session.Template Template (go.tag = "json:\"template\""),
    2: required list<permission.PermissionAction> Permissions (go.tag = "json:\"permissions\""), // 用户对该模板的权限
}

// 更新模板，传不同的参数实现不同功能：
// 1. 传 NeedGenerateExperience + TemplateKey，将更新模板的经验
// 2. 传 Template，将更新模板的内容
struct UpdateTemplateRequest {
    1: required string TemplateID (api.path="template_id"),
    2: optional bool NeedGenerateExperience (go.tag = "json:\"need_generate_experience\""),
    3: optional TemplateKey TemplateKey (go.tag = "json:\"template_key\""),
    4: optional session.ModifyTemplate Template (go.tag = "json:\"template\""),
}

struct UpdateTemplateResponse {
    1: required session.Template Template (go.tag = "json:\"template\""),
}

struct DeleteTemplateRequest {
    1: required string TemplateID (api.path="template_id"),
}
struct DeleteTemplateResponse {}

/* 给 Runtime 调用的接口 */

struct UpdateTemplateExperienceRequest {
    1: required string TemplateID (api.path="template_id"),
    2: optional string ProgressPlan (go.tag = "json:\"progress_plan\""), // 粗粒度的经验
    3: optional string ExpSOP (go.tag = "json:\"exp_sop\""), // 细粒度的经验
    4: required TemplateExperienceStatus Status (go.tag = "json:\"status\""), // 经验生成结果状态
    5: optional string Error (go.tag = "json:\"error\""), // 经验生成错误信息
    6: optional bool ForceActive (go.tag = "json:\"force_active\""), // 经验是否强制生效，默认会根据用户对模板的编辑操作来确定
}

enum TemplateExperienceStatus {
    Success = 0,
    Failed = 1,
}

struct UpdateTemplateExperienceResponse {}

struct UploadTemplateExperienceFileStreamRequest {
    1: required string TemplateID (api.path = "template_id"),
    2: required string Path (api.query = "path"),
    3: required i64 Size (api.query = "size"),
}
struct UploadTemplateExperienceFileStreamResponse {
    1: required TemplateFile File (go.tag = "json:\"file\""),
}

struct TemplateFile {
    1: required string FileID (go.tag = "json:\"file_id\""),
    2: required string TemplateID (go.tag = "json:\"template_id\""),
    3: required string Name (go.tag = "json:\"name\""),
    4: required string Type (go.tag = "json:\"type\""),
}

struct DownloadTemplateExperienceFileStreamRequest {
    1: required string FileID (api.path = "file_id"),
    2: optional bool Stream (api.query = "stream") // 流式返回
}

struct ListSpaceTemplatesRequest {
    1: optional string NextID (api.query = "next_id"),
    2: optional i64 Limit (api.query = "limit"),
    3: optional session.TemplateCategory Category (api.query = "category"),
    4: optional string Search (api.query = "search"),
    5: optional session.TemplateSource Source (api.query = "source"),
    6: optional session.TemplateLabel Label (api.query = "label"),
    7: optional string SpaceID (api.query = "space_id"),
}

struct ListSpaceTemplatesResponse {
    1: required list<session.Template> Templates (go.tag = "json:\"templates\""),
    2: required bool HasMore (go.tag = "json:\"has_more\""),
    3: required string NextID (go.tag = "json:\"next_id\""),
}

struct CountTemplatesRequest {}

struct CountTemplatesResponse {
    1: required i64 MyTotal (go.tag = "json:\"my_total\""), // 我的模板总数，不随筛选条件变化
    2: required i64 StarTotal (go.tag = "json:\"star_total\""), // 收藏模板总数，不随筛选条件变化
    3: required i64 AllTotal (go.tag = "json:\"all_total\""), // 所有公开模板总数，不随筛选条件变化
}

struct CountSpaceTemplatesRequest {
    1: optional string SpaceID (api.query = "space_id"),
}

struct CountSpaceTemplatesResponse {
    1: required i64 MyTotal (go.tag = "json:\"my_total\""), // 我的模板总数，不随筛选条件变化
    2: required i64 StarTotal (go.tag = "json:\"star_total\""), // 收藏模板总数，不随筛选条件变化
    3: required i64 AllTotal (go.tag = "json:\"all_total\""), // 所有公开模板总数，不随筛选条件变化
}

struct CreateTemplateStarRequest {
    1: required string TemplateID (api.path="template_id"),
    2: optional string SpaceID (go.tag = "json:\"space_id\""),
}
struct CreateTemplateStarResponse {}

struct DeleteTemplateStarRequest {
    1: required string TemplateID (api.path="template_id"),
    2: optional string SpaceID (go.tag = "json:\"space_id\""),
}
struct DeleteTemplateStarResponse {}

struct CreateShareTemplateRequest {
    1: required string TemplateID (api.path="template_id"),
}
struct CreateShareTemplateResponse {
    1: required string ShareID (go.tag = "json:\"share_id\""),
}

struct CreateUserShareTemplateRequest {
    1: required string ShareID (api.path="share_id"),
}
struct CreateUserShareTemplateResponse {
    1: required session.Template Template (go.tag = "json:\"template\""),
    2: required list<permission.PermissionAction> Permissions (go.tag = "json:\"permissions\""), // 用户对该模板的权限
}
struct DeleteUserShareTemplateRequest {
    1: required string ShareID (api.path="share_id"),
}
struct DeleteUserShareTemplateResponse {}

struct SaveTemplateShareFormDataRequest {
    1: required session.TemplateFormValueDetail FormData (go.tag = "json:\"form_data\""),
}

struct SaveTemplateShareFormDataResponse {
    1: required string ID (go.tag = "json:\"id\""),
}

struct GetTemplateShareFormDataRequest {
    1: required string ID (api.path="id"),
}
struct GetTemplateShareFormDataResponse {
    1: required session.TemplateFormValueDetail FormData (go.tag = "json:\"form_data\""),
}