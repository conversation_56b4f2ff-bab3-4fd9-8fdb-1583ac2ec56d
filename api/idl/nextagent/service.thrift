namespace go nextagent

include "session.thrift"
include "session_collection.thrift"
include "event.thrift"
include "../common.thrift"
include "artifact.thrift"
include "lark.thrift"
include "deployment.thrift"
include "replay.thrift"
include "share.thrift"
include "user.thrift"
include "trace.thrift"
include "agent.thrift"
include "prompt.thrift"
include "activity.thrift"
include "feature.thrift"
include "mcp.thrift"
include "template.thrift"
include "permission.thrift"
include "space.thrift"
include "ops.thrift"
include "knowledgebase.thrift"
include "mention.thrift"

service NextAgentService {
    replay.ListShowcaseResponse ListShowcase(1: replay.ListShowcaseRequest req) (api.get = "/api/agents/v2/showcases"),
    replay.GetReplayResponse GetReplay(1: replay.GetReplayRequest req) (api.get = "/api/agents/v2/replay/:replay_id"),
    replay.CreateReplayResponse CreateReplay(1: replay.CreateReplayRequest req) (api.post = "/api/agents/v2/replay"),
    session.GetUserRolesResponse GetUserRoles(1: session.GetUserRolesRequest req) (api.get = "/api/agents/v2/roles"),
    // session 会话相关
    session.CreateSessionResponse CreateSession(1: session.CreateSessionRequest req) (api.post = "/api/agents/v2/sessions"),
    session.ListSessionsResponse ListSessions(1: session.ListSessionsRequest req) (api.get = "/api/agents/v2/sessions"),
    session.GetSessionResponse GetSession(1: session.GetSessionRequest req) (api.get = "/api/agents/v2/sessions/:session_id"),
    session.DeleteSessionResponse DeleteSession(1: session.DeleteSessionRequest req) (api.delete = "/api/agents/v2/sessions/:session_id"),
    session.CreateMessageResponse CreateMessage(1: session.CreateMessageRequest req) (api.post = "/api/agents/v2/sessions/:session_id/message"),
    session.GetOldSessionEventsResponse GetOldSessionEvents(1: session.GetOldSessionEventsRequest req) (api.get = "/api/agents/v2/sessions/:session_id/old_events"),
    session.CheckCreateSessionResponse CheckCreateSession(1: session.CheckCreateSessionRequest req) (api.get = "/api/agents/v2/sessions/check"),
    session.UpdateSessionResponse UpdateSession(1: session.UpdateSessionRequest req) (api.patch = "/api/agents/v2/sessions/:session_id"),
    common.SSEResponse GetSessionStreamEvents(1: session.GetSessionStreamEventsRequest req) (streaming.mode="server", agw.target_streaming_protocol="sse", api.post = "/api/agents/v2/sessions/:session_id/events"),
    session.SubmitToolCallResponse SubmitToolCall(1: session.SubmitToolCallRequest req) (api.post = "/api/agents/v2/sessions/:session_id/tool_call"),
    // 获取所有未执行完成的会话
    session.ListSessionPartialResponse ListSessionPartial(1: session.ListSessionPartialRequest req) (api.get = "/api/agents/v2/sessions/partial/list"),
    mention.SearchMentionsResponse SearchMentions(1: mention.SearchMentionsRequest req) (api.get = "/api/agents/v2/mentions")
    // 会话任务采集相关
    session_collection.SessionsCollectionResponse SessionsCollection(1: session_collection.SessionsCollectionRequest req) (api.post = "/api/agents/v2/sessions/collection"),
    session_collection.ListSessionCollectionsResponse ListSessionCollections(1: session_collection.ListSessionCollectionsRequest req) (api.get = "/api/agents/v2/sessions/collection/list"),
    session_collection.FilterSessionCollectionsResponse FilterListSessionCollections(1: session_collection.FilterSessionCollectionsRequest req) (api.post = "/api/agents/v2/sessions/collection/list"),
    session_collection.RunSessionCollectionsResponse RunSessionCollections(1: session_collection.RunSessionCollectionsRequest req) (api.post = "/api/agents/v2/sessions/collection/run"),
    session_collection.SendSessionCollectionRunNotificationResponse SendSessionCollectionRunNotification (1: session_collection.SendSessionCollectionRunNotificationRequest req) (api.post = "/api/agents/v2/sessions/collection/run/notification"),
    session_collection.DownloadSessionCollectionsResponse DownloadSessionCollections(1: session_collection.DownloadSessionCollectionsRequest req) (api.post = "/api/agents/v2/sessions/collection/download"),
    session_collection.CloseSessionCollectionsResponse CloseSessionCollections(1: session_collection.CloseSessionCollectionsRequest req) (api.post = "/api/agents/v2/sessions/collection/close"),
    session_collection.ListNotificationTemplatesResponse ListNotificationTemplates(1: session_collection.ListNotificationTemplatesRequest req) (api.get = "/api/agents/v2/sessions/collection/notification_templates"),
    session_collection.SendSessionCollectionNotificationResponse SendSessionCollectionNotification (1: session_collection.SendSessionCollectionNotificationRequest req) (api.post = "/api/agents/v2/sessions/collection/notification"),
    // 快捷任务&模板相关
    session.CreateMessageWithTemplateResponse CreateMessageWithTemplate(1: session.CreateMessageWithTemplateRequest req) (api.post = "/api/agents/v2/message_with_template"),
    session.ListTemplatesResponse ListTemplates(1: session.ListTemplatesRequest req) (api.get = "/api/agents/v2/templates/list"),
    session.GetHistoryTemplateVariablesResponse GetHistoryTemplateVariables(1: session.GetHistoryTemplateVariablesRequest req) (api.get = "/api/agents/v2/templates/:template_id/history_variables"),
    session.GetSessionAgentRunResponse GetSessionAgentRun(1: session.GetSessionAgentRunRequest req) (api.get = "/api/agents/v2/sessions/:session_id/agent_run"),
    template.GetTemplateResponse GetTemplate(1: template.GetTemplateRequest req) (api.get = "/api/agents/v2/templates/:template_id"),
    template.CreateTemplateResponse CreateTemplate(1: template.CreateTemplateRequest req) (api.post = "/api/agents/v2/templates"),
    template.UpdateTemplateResponse UpdateTemplate(1: template.UpdateTemplateRequest req) (api.put = "/api/agents/v2/templates/:template_id"),
    template.DeleteTemplateResponse DeleteTemplate(1: template.DeleteTemplateRequest req) (api.delete = "/api/agents/v2/templates/:template_id"),
    template.CreateTemplateDraftResponse CreateTemplateDraft(1: template.CreateTemplateDraftRequest req) (api.post = "/api/agents/v2/templates/draft"),
    template.GetTemplateDraftResponse GetTemplateDraft(1: template.GetTemplateDraftRequest req) (api.get = "/api/agents/v2/templates/:template_id/draft"),
    template.UpdateTemplateExperienceResponse UpdateTemplateExperience(1: template.UpdateTemplateExperienceRequest req) (api.put = "/api/agents/v2/templates/:template_id/experience"),
    template.UploadTemplateExperienceFileStreamResponse UploadTemplateExperienceFileStream(1: template.UploadTemplateExperienceFileStreamRequest req) (api.post = "/api/agents/v2/templates/:template_id/upload/stream"),
    binary DownloadTemplateExperienceFile(1: template.DownloadTemplateExperienceFileStreamRequest req) (api.get = "/api/agents/v2/templates/file/:file_id/raw"),
    template.CountTemplatesResponse CountTemplates(1: template.CountTemplatesRequest req) (api.get = "/api/agents/v2/templates/count"),
    template.CreateTemplateStarResponse CreateTemplateStar(1: template.CreateTemplateStarRequest req) (api.post = "/api/agents/v2/templates/:template_id/star"),
    template.DeleteTemplateStarResponse DeleteTemplateStar(1: template.DeleteTemplateStarRequest req) (api.delete = "/api/agents/v2/templates/:template_id/star"),
    template.CreateShareTemplateResponse CreateShareTemplate(1: template.CreateShareTemplateRequest req) (api.post = "/api/agents/v2/templates/:template_id/share"),
    template.CreateUserShareTemplateResponse CreateUserShareTemplate(1: template.CreateUserShareTemplateRequest req) (api.post = "/api/agents/v2/templates/shares/:share_id/user"),
    template.DeleteUserShareTemplateResponse DeleteUserShareTemplate(1: template.DeleteUserShareTemplateRequest req) (api.delete = "/api/agents/v2/templates/shares/:share_id/user"),
    template.SaveTemplateShareFormDataResponse SaveTemplateShareFormData(1: template.SaveTemplateShareFormDataRequest req) (api.post = "/api/agents/v2/templates/shares/form_data"),
    template.GetTemplateShareFormDataResponse GetTemplateShareFormData(1: template.GetTemplateShareFormDataRequest req) (api.get = "/api/agents/v2/templates/shares/form_data/:id"),

    // artifact 相关
    artifact.CreateArtifactResponse CreateArtifact(1: artifact.CreateArtifactRequest req) (api.post = "/api/agents/v2/artifacts"),
    artifact.ListArtifactsResponse ListArtifacts(1: artifact.ListArtifactsRequest req) (api.get = "/api/agents/v2/artifacts/list"),
    artifact.GetArtifactResponse GetArtifact(1: artifact.GetArtifactRequest req) (api.get = "/api/agents/v2/artifacts/:artifact_id"),
    artifact.UpdateArtifactResponse UpdateArtifact(1: artifact.UpdateArtifactRequest req) (api.put = "/api/agents/v2/artifacts/:artifact_id"),
    artifact.UploadArtifactResponse UploadArtifact(1: artifact.UploadArtifactRequest req) (api.post = "/api/agents/v2/artifacts/:artifact_id/upload", api.serializer='muti-form'),
    artifact.UploadArtifactResponse UploadArtifactStream(1: artifact.UploadArtifactStreamRequest req) (api.post = "/api/agents/v2/artifacts/:artifact_id/upload/stream"),
    artifact.RetrieveArtifactFilesResponse RetrieveArtifactFiles(1: artifact.RetrieveArtifactFilesRequest req) (api.post = "/api/agents/v2/artifacts/:artifact_id/files"),
    binary DownloadArtifact(1: artifact.DownloadArtifactFileRequest req) (api.get = "/api/agents/v2/artifacts/:artifact_id/raw/:path"),
    binary DownloadArtifactBatch(1: artifact.DownloadArtifactBatchRequest req) (api.post = "/api/agents/v2/artifacts/download/batch"),
    artifact.UpdateArtifactFileResponse UpdateArtifactFile(1: artifact.UpdateArtifactFileRequest req) (api.put = "/api/agents/v2/artifacts/:artifact_id/files/:path"),

    // only for debug.
    event.EventStruct UnUsedStruct(1: event.EventStruct req) (api.get = "/api/agents/v2/debug"),
    event.SaveEventKeyResponse SaveEventKey(1: event.SaveEventKeyRequest req) (api.post = "/api/agents/v2/save_event_key"),

    //lark 相关
    lark.LarkAuthResponse LarkAuth(1: lark.LarkAuthRequest req) (api.get = "/api/agents/v2/lark/auth"),
    lark.CheckLarkAuthResponse CheckLarkAuth(1: lark.CheckLarkAuthRequest req) (api.get = "/api/agents/v2/lark/auth/check"),
    lark.GetLarkTicketResponse GetLarkTicket(1: lark.GetLarkTicketRequest req) (api.get = "/api/agents/v2/lark/auth/ticket"),
    lark.GetUserLarkURLResponse GetUserLarkURL(1: lark.GetUserLarkURLRequest req) (api.get = "/api/agents/v2/lark/get/lark_url"),
    lark.SendLarkReplayLinkMessageResponse SendLarkReplayLinkMessage(1: lark.SendLarkReplayLinkMessageRequest req) (api.post = "/api/agents/v2/lark/send/replay_link"),
    binary GetLarkDocxBlocks (1: lark.GetLarkDocxBlocksRequest req) (api.get = "/api/agents/v2/lark/documents/:document_id/blocks"),

    // deployment
    deployment.GetDeploymentArtifactResponse GetMainDeploymentArtifact(1: deployment.GetDeploymentArtifactRequest req) (api.get = "/api/agents/v2/deployments"),
    deployment.GetDeploymentArtifactResponse GetMDeploymentArtifact(1: deployment.GetDeploymentArtifactRequest req) (api.get = "/api/agents/v2/deployments/*path"),
    deployment.CreateDeploymentResponse CreateDeployment(1: deployment.CreateDeploymentRequest req) (api.post = "/api/agents/v2/deployments"),

    // share
    share.SharePreviewCallbackResponse ShareReplayPreviewCallback(1: share.SharePreviewCallbackRequest req) (api.post = "/api/agents/v2/share/replay/call_back"),

    // user
    user.GetUserInfoResponse GetUserInfo(1: user.GetUserInfoRequest req) (api.get = "/api/agents/v2/user/:user_name"),
    user.BindInvitationCodeResponse BindInvitationCode(1: user.BindInvitationCodeRequest req) (api.put = "/api/agents/v2/user/invitation_code/:invitation_code"),
    user.GrantAccessResponse GrantAccess(1: user.GrantAccessRequest req) (api.post = "/api/agents/v2/user/grant_access"),
    user.GetUserSettingsResponse GetUserSettings(1: user.GetUserSettingsRequest req) (api.get = "/api/agents/v2/user/settings"),
    user.UpdateUserSettingsResponse UpdateUserSettings(1: user.UpdateUserSettingsRequest req) (api.put = "/api/agents/v2/user/settings"),


    // trace
    trace.GetTraceSessionResponse GetTraceSession(1: trace.GetTraceSessionRequest req) (api.get = "/api/agents/v2/trace/session")
    common.SSEResponse GetTraceEvents(1: trace.GetTraceEventsRequest req) (streaming.mode="server", agw.target_streaming_protocol="sse", api.post = "/api/agents/v2/trace/events")
    trace.ResumeRuntimeResponse ResumeRuntime(1: trace.ResumeRuntimeRequest req) (api.post = "/api/agents/v2/trace/actions/resume")
    trace.GetTraceSessionChatResponse GetTraceSessionChat(1: trace.GetTraceSessionChatRequest req) (api.get = "/api/agents/v2/trace/session/chat")
    trace.ListSessionAgentStepResponse ListSessionAgentStep(1: trace.ListSessionAgentStepRequest req) (api.get = "/api/agents/v2/trace/session/agent_steps")
    binary DownloadSessionLog(1: trace.DownloadSessionLogRequest req) (api.get = "/api/agents/v2/trace/session/log")
    trace.SuspendRuntimeResponse SuspendRuntime(1: trace.SuspendRuntimeRequest req) (api.post = "/api/agents/v2/trace/actions/suspend")
    trace.DeleteRuntimeResponse DeleteRuntime(1: trace.DeleteRuntimeRequest req) (api.post = "/api/agents/v2/trace/actions/delete")
    trace.ListModelsResponse ListModels(1: trace.ListModelsRequest req) (api.get = "/api/agents/v2/trace/models")
    common.SSEResponse ChatStream(1: trace.ChatStreamRequest req) (streaming.mode="server", agw.target_streaming_protocol="sse", api.post = "/api/agents/v2/trace/:model/chat/completions")
    trace.ListSessionDocumentsResponse ListSessionDocuments(1: trace.ListSessionDocumentsRequest req) (api.get = "/api/agents/v2/trace/session/documents")
    trace.ConvertSessionDocumentToLarkResponse ConvertSessionDocumentToLark(1: trace.ConvertSessionDocumentToLarkRequest req) (api.post = "/api/agents/v2/trace/session/documents/convert_to_lark")
    common.SSEResponse TraceMCPEvents(1: trace.TraceMCPEventsRequest req) (streaming.mode="server", agw.target_streaming_protocol="sse", api.post = "/api/agents/v2/trace/events/mcp")

    // avtivity
    activity.GetUserActivityProgressResponse GetUserActivityProgress(1: activity.GetUserActivityProgressRequest req) (api.get = "/api/agents/v2/activity/progress"),
    activity.VerifyActivityAwardResponse VerifyActivityAward(1: activity.VerifyActivityAwardRequest req) (api.post = "/api/agents/v2/activity/verify"),

    // feature
    feature.GetUserFeaturesResponse GetUserFeatures(1: feature.GetUserFeaturesRequest req) (api.get = "/api/agents/v2/features"),
    feature.GetGlobalFeaturesResponse GetGlobalFeature(1: feature.GetGlobalFeaturesRequest req) (api.get = "/api/agents/v2/features/global"),

    // Agent Manage
    agent.CreateAgentResponse CreateAgent(1: agent.CreateAgentRequest req) (api.post = "/api/agents/v2/agent")
    agent.CreateAgentConfigResponse CreateAgentConfig(1: agent.CreateAgentConfigRequest req) (api.post = "/api/agents/v2/agent/config")
    agent.CreateAgentConfigVersionResponse CreateAgentConfigVersion(1: agent.CreateAgentConfigVersionRequest req) (api.post = "/api/agents/v2/agent/config/version")
    agent.UpdateAgentResponse UpdateAgent(1: agent.UpdateAgentRequest req) (api.put = "/api/agents/v2/agent/:agent_id")
    agent.UpdateAgentConfigResponse UpdateAgentConfig(1: agent.UpdateAgentConfigRequest req) (api.put = "/api/agents/v2/agent/config/:agent_config_id")
    agent.UpdateAgentConfigVersionResponse UpdateAgentConfigVersion(1: agent.UpdateAgentConfigVersionRequest req) (api.put = "/api/agents/v2/agent/config/version/:agent_config_version_id")
    agent.DeployAgentConfigVersionResponse DeployAgentConfigVersion(1: agent.DeployAgentConfigVersionRequest req) (api.post = "/api/agents/v2/agent/config/version/:agent_config_version_id/deploy")
    agent.DeleteAgentResponse DeleteAgent(1: agent.DeleteAgentRequest req) (api.delete = "/api/agents/v2/agent/:agent_id")
    agent.DeleteAgentConfigResponse DeleteAgentConfig(1: agent.DeleteAgentConfigRequest req)  (api.delete = "/api/agents/v2/agent/config/:agent_config_id")
    agent.GetAgentResponse GetAgent(1: agent.GetAgentRequest req) (api.get = "/api/agents/v2/agent/:agent_id")
    agent.ListAgentsResponse ListAgents(1: agent.ListAgentsRequest req) (api.get = "/api/agents/v2/agent")
    agent.GetAgentConfigResponse GetAgentConfig(1: agent.GetAgentConfigRequest req) (api.get = "/api/agents/v2/agent/config/:agent_config_id")
    agent.ListAgentConfigsResponse ListAgentConfigs(1: agent.ListAgentConfigsRequest req) (api.get = "/api/agents/v2/agent/config")
    agent.ListAgentConfigVersionsResponse ListAgentConfigVersions(1: agent.ListAgentConfigVersionsRequest req) (api.get = "/api/agents/v2/agent/config/version")
    agent.GetAgentConfigVersionResponse GetAgentConfigVersion(1: agent.GetAgentConfigVersionRequest req) (api.get = "/api/agents/v2/agent/config/version/:agent_config_version_id")
    // Prompt Manage
    prompt.CreatePromptResponse CreatePrompt(1: prompt.CreatePromptRequest req) (api.post = "/api/agents/v2/prompt")
    prompt.CreatePromptVersionResponse CreatePromptVersion(1: prompt.CreatePromptVersionRequest req) (api.post = "/api/agents/v2/prompt/version")
    prompt.UpdatePromptResponse UpdatePrompt(1: prompt.UpdatePromptRequest req) (api.put = "/api/agents/v2/prompt/:prompt_id")
    prompt.UpdatePromptVersionResponse UpdatePromptVersion(1: prompt.UpdatePromptVersionRequest req) (api.put = "/api/agents/v2/prompt/version/:prompt_version_id")
    prompt.GetPromptResponse GetPrompt(1: prompt.GetPromptRequest req) (api.get = "/api/agents/v2/prompt/:prompt_id")
    prompt.DownloadPromptVersionResponse DownloadPromptVersion(1: prompt.DownloadPromptVersionRequest req) (api.get = "/api/agents/v2/prompt/version/download")
    prompt.ListPromptVersionResponse ListPromptVersion(1: prompt.ListPromptVersionRequest req) (api.get = "/api/agents/v2/prompt/version")
    prompt.DeletePromptResponse DeletePrompt(1: prompt.DeletePromptRequest req) (api.delete = "/api/agents/v2/prompt/:prompt_id")
    prompt.ListPromptResponse ListPrompt(1: prompt.ListPromptRequest req) (api.get = "/api/agents/v2/prompt")
    prompt.GetPromptVersionResponse GetPromptVersion(1: prompt.GetPromptVersionRequest req) (api.get = "/api/agents/v2/prompt/version/:prompt_version_id")

    // MCP 相关
    mcp.CreateMCPResponse CreateMCP(1: mcp.CreateMCPRequest req) (api.post = "/api/agents/v2/mcp")
    mcp.UpdateMCPResponse UpdateMCP(1: mcp.UpdateMCPRequest req) (api.put = "/api/agents/v2/mcp/update")
    mcp.ListMCPResponse ListMCP(1: mcp.ListMCPRequest req) (api.post = "/api/agents/v2/mcp/list") // 因bam array query生成代码问题，改post
    mcp.ModifyMCPActivationResponse ModifyMCPActivation(1: mcp.ModifyMCPActivationRequest req) (api.post = "/api/agents/v2/mcp/activation")
    mcp.ValidateMCPResponse ValidateMCP(1: mcp.ValidateMCPRequest req) (api.post = "/api/agents/v2/mcp/validate")
    // 内置MCP管理相关
    mcp.CreateMCPResponse CreateBuildInMCP(1: mcp.CreateMCPRequest req) (api.post = "/api/agents/v2/build_in_mcp/create")
    mcp.UpdateMCPResponse UpdateBuildInMCP(1: mcp.UpdateMCPRequest req) (api.put = "/api/agents/v2/build_in_mcp/update")

    // 权限相关
    permission.GetUserResourcePermissionResponse GetUserResourcePermission(1: permission.GetUserResourcePermissionRequest req) (api.get = "/api/agents/v2/resource/user/permission")
    // space项目空间
    space.CreateSpaceResponse CreateSpace(1: space.CreateSpaceRequest req) (api.post = "/api/agents/v2/space"),
    space.UpdateSpaceResponse UpdateSpace(1: space.UpdateSpaceRequest req) (api.put = "/api/agents/v2/space"),
    space.GetSpaceResponse GetSpace(1: space.GetSpaceRequest req) (api.get = "/api/agents/v2/space"),
    space.DeleteSpaceResponse DeleteSpace(1: space.DeleteSpaceRequest req) (api.delete = "/api/agents/v2/space"),
    space.ListAllSpacesResponse ListAllSpaces(1: space.ListAllSpacesRequest req) (api.get = "/api/agents/v2/space/list"),
    space.AddSpaceMemberResponse AddSpaceMember(1: space.AddSpaceMemberRequest req) (api.post = "/api/agents/v2/space/members"),
    space.RemoveSpaceMemberResponse RemoveSpaceMember(1: space.RemoveSpaceMemberRequest req) (api.delete = "/api/agents/v2/space/members"),
    space.ListSpaceMembersResponse ListSpaceMembers(1: space.ListSpaceMembersRequest req) (api.get = "/api/agents/v2/space/members"),
    space.ListUserSpacesResponse ListUserSpaces(1: space.ListUserSpacesRequest req) (api.get = "/api/agents/v2/user/list/space"),

    // v3 相关接口
    session.ListSpaceSessionsResponse ListSpaceSessions(1: session.ListSpaceSessionsRequest req) (api.get = "/api/agents/v3/sessions"),
    template.ListSpaceTemplatesResponse ListSpaceTemplates(1: template.ListSpaceTemplatesRequest req) (api.get = "/api/agents/v3/templates/list"),
    template.CountSpaceTemplatesResponse CountSpaceTemplates(1: template.CountSpaceTemplatesRequest req) (api.get = "/api/agents/v3/templates/count"),
    mcp.ListSpaceMCPResponse ListSpaceMCP(1: mcp.ListSpaceMCPRequest req) (api.post = "/api/agents/v3/mcp/list") // 因bam array query生成代码问题，改post


    // 管理运维接口
    ops.OpsListTemplatesResponse OpsListTemplates(1: ops.OpsListTemplatesRequest req) (api.get = "/api/agents/v2/ops/templates"),
    ops.OpsEditTemplateResponse OpsEditTemplate(1: ops.OpsEditTemplateRequest req) (api.patch = "/api/agents/v2/ops/templates/:template_id"),

    // knowledgebase
    knowledgebase.UploadDocumentsResponse UploadDocuments(1: knowledgebase.UploadDocumentsRequest req) (api.put = "/api/agents/v2/datasets/:dataset_id/documents"),
    knowledgebase.DeleteDocumentResponse DeleteDocument(1: knowledgebase.DeleteDocumentRequest req) (api.delete = "/api/agents/v2/datasets/:dataset_id/documents/:document_id"),
    knowledgebase.ListDocumentsResponse ListDocuments(1: knowledgebase.ListDocumentsRequest req) (api.post = "/api/agents/v2/datasets/:dataset_id/documents"),
    knowledgebase.UpdateDocumentResponse UpdateDocument(1: knowledgebase.UpdateDocumentRequest req) (api.put = "/api/agents/v2/datasets/:dataset_id/documents/:document_id"),
    knowledgebase.GetDocumentResponse GetDocument(1: knowledgebase.GetDocumentRequest req) (api.get = "/api/agents/v2/datasets/:dataset_id/documents/:document_id"),
    knowledgebase.SearchLarkDocumentsResponse SearchLarkDocuments(1: knowledgebase.SearchLarkDocumentsRequest req) (api.get = "/api/agents/v2/datasets/:dataset_id/lark_documents"),
    knowledgebase.RecommendDocumentsResponse RecommendDocuments(1: knowledgebase.RecommendDocumentsRequest req) (api.post = "/api/agents/v2/datasets/:dataset_id/recommend_documents"),
    knowledgebase.CountDocumentsResponse CountDocuments(1: knowledgebase.CountDocumentsRequest req) (api.get = "/api/agents/v2/datasets/:dataset_id/documents/count"),
}
