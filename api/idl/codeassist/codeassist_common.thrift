include "../base.thrift"
include "../common.thrift"

namespace go codeassist

enum ResourceType {
    Repository = 1,  // 仓库类型上下文
    File = 2,        // 文件类型上下文
    Directory = 3,   // 文件夹类型上下文
}

struct PingRequest {
    201: optional common.HTTPRequest HTTPRequest (agw.source="not_body_struct"),
    255: optional base.Base Base,
}

struct PingResponse {
    201: optional common.ErrorCode Code (go.tag="json:\"code\"", api.body="code"),
    202: optional string Message (go.tag="json:\"message\"", api.body="message"),
    203: optional i32 HTTPCode (agw.target="http_code"),
    204: optional common.HTTPResponse HTTPResponse (agw.target="header"),
    255: optional base.BaseResp BaseResp,
}

// 上下文资源的唯一标识符
struct ContextIdentifier {
    1: required ResourceType Type,
    // repo: repo id, file: uri, directory: directory id
    2: required string ResourceKey,
    3: optional string Name,
}
