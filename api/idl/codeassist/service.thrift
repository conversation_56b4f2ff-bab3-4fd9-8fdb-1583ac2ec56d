include "../base.thrift"
include "../common.thrift"
include "agent.thrift"
include "chat.thrift"
include "codeassist_common.thrift"
include "context.thrift"
include "sandbox.thrift"
include "review.thrift"
include "search.thrift"
include "../alice/bot_hook/hook.thrift"
include "../alice/bot_hook/flow_hook.thrift"

namespace go codeassist

service AssistantService {
    // ping
    codeassist_common.PingResponse Ping(1:codeassist_common.PingRequest req)
    // chat
    chat.StreamChatResponse Chat(1: chat.ChatRequest req) (streaming.mode="server"),
    chat.PromptsRenderResponse PromptsRender(1: chat.PromptsRenderRequest req),
    chat.E2EPromptsRenderResponse E2EPromptsRender(1: chat.E2EPromptsRenderRequest req),
    chat.HomePageResponse HomePage(1: chat.HomePageRequest req),    // 首页样式
    // context
    context.SniffRepositoryLinkResponse SniffRepositoryLink(1: context.SniffRepositoryLinkRequest req),
    context.CreateContextResponse CreateContext(1: context.CreateContextRequest req),
    context.MGetContextStatusResponse MGetContextStatus(1: context.MGetContextStatusRequest req),
    context.GetContextDirectoryNodesResponse GetContextDirectoryNodes(1: context.GetContextDirectoryNodesRequest req),
    context.GetRepoFileContentResponse GetRepoFileContent(1: context.GetRepoFileContentRequest req),
    context.GetContextInfoResponse GetContextInfo(1: context.GetContextInfoRequest req),
    context.GetContextDownloadURLResponse GetContextDownloadURL(1: context.GetContextDownloadURLRequest req),
    context.GetCodeResourceDownloadURLResponse GetCodeResourceDownloadURL(1: context.GetCodeResourceDownloadURLRequest req),
    context.DesensitizeContextResponse DesensitizeContext(1: context.DesensitizeContextRequest req),
    context.BatchCheckFilePathResponse BatchCheckFilePath(1: context.BatchCheckFilePathRequest req),
    // sandbox biz rpc
    sandbox.CodeExecutableResponse CodeExecutable(1: sandbox.CodeExecutableRequest req),
    sandbox.RunCodeResponse RunCode(1: sandbox.RunCodeRequest req),
    sandbox.RunCodeResponseV2 RunCodeV2(1: sandbox.RunCodeRequestV2 req),
    sandbox.GetArtifactTemplateFileResponse GetArtifactTemplateFile(1: sandbox.GetArtifactTemplateFileRequest req);
    sandbox.GetArtifactTemplateDirResponse GetArtifactTemplateDir(1: sandbox.GetArtifactTemplateDirRequest req);
    sandbox.CompileCodeArtifactResponse CompileCodeArtifact(1: sandbox.CompileCodeArtifactRequest req);
    sandbox.GetCompileStatusResponse GetCompileStatus(1: sandbox.GetCompileStatusRequest req);
    sandbox.GetArtifactsCodeURIResponse GetArtifactsCodeURI(1: sandbox.GetArtifactsCodeURIRequest req);
    // sandbox rpc
    sandbox.CreateSandboxResponse CreateSandbox(1: sandbox.CreateSandboxRequest req);
    sandbox.ReleaseSandboxResponse ReleaseSandbox(1: sandbox.ReleaseSandboxRequest req);
    sandbox.ExecuteCodeResponse ExecuteCode(1: sandbox.ExecuteCodeRequest req);
    sandbox.UploadFileResponse UploadFile(1: sandbox.UploadFileRequest req);
    sandbox.DownloadFileResponse DownloadFile(1: sandbox.DownloadFileRequest req);
    // review
    review.ImageReviewResponse ImageReview(1: review.ImageReviewRequest req), // 图片审查
    // picture
    search.SearchImagesResponse SearchImages(1: search.SearchImagesRequest req),
    // agent
    agent.InterruptCodeAgentTaskResponse InterruptCodeAgentTask(1: agent.InterruptCodeAgentTaskRequest req),
    agent.ExecuteJobCallbackResp ExecuteJobCallback(1: agent.ExecuteJobCallbackReq req),
}

service HookService {
    // coze bot hook. docx：https://bytedance.larkoffice.com/docx/IR5idikMeo0Uvgx7FezcL1Rwnue
    hook.FlowHookResponse FlowHook(1: hook.FlowHookRequest req),
    flow_hook.StreamPacket FlowStream(1: hook.FlowHookRequest req) (streaming.mode="server"),
}
