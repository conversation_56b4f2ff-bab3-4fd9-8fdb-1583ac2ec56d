include "../base.thrift"

namespace go codeassist

struct InterruptCodeAgentTaskRequest {
    1: i64 UserID;
    2: i64 MessageID;
    3: i64 ConversationID;

    255: optional base.Base Base,
}

struct InterruptCodeAgentTaskResponse {
    255: optional base.BaseResp BaseResp,
}

// job scheduler 回调的接口
struct ExecuteJobCallbackReq {
	1: i64 InstanceID,
	2: string BizKey,
	3: string BizID,
	4: binary JobParamBytes,

	255: base.Base Base
}

struct ExecuteJobCallbackResp {
	255: base.BaseResp BaseResp
}
