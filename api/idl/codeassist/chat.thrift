include "../base.thrift"
include "../common.thrift"
include "codeassist_common.thrift"

namespace go codeassist

// 请求包
struct ChatRequest {
    1: required Message UserMessage,          // 当前用户消息
    2: list<Message> HistoryMessages,         // 历史会话消息
    3: i64 UserID,                            // 用户ID，未登录时为0
    4: optional string AbVariables,           // Ab 实验参数，序列化后的 JSON 字符串
    5: i64 AppID,                             // 应用ID
    6: optional bool UseDeepThink,            // 开启深度思考
    7: optional CotMode UseAutoCot,           // 自动深度思考开关，1 非cot 2 auto cot 3 open cot

    255: optional base.Base Base,
}

struct Message {
    1: ChatMessageRole Role,
    2: string Content,
    3: optional list<ContextBlock> ContextBlocks, // 附件引用
    4: optional string ContextVariables,  // 上下文变量，如划词、代码语言、注释风格等
    5: optional string SectionID,  // 一个对话过程公用一个 section id，清除历史对话则 section id 更新, deprecated
    6: ChatContentType ContentType,  // 消息内容类型，用于过滤消息
    7: BizInfo BizInfo, // 业务信息
}

struct BizInfo {
    1: optional i64 MessageID
    2: optional i64 ConversationID,
    3: optional i64 SectionID,
    4: optional ConversationType ConversationType,
    5: optional i64 AnswerID,
}

enum CotMode {
    CotModeClose = 1
    CotModeAuto = 2
    CotModeOpen = 3
}

enum ConversationType {
    Unknown = 0
    Single = 1
    Group = 2
}

enum ChatMessageRole {
    SYSTEM = 0,
    USER = 1,
    ASSISTANT = 2,
}

enum ContextBlockType {
    FILE_BLOCK = 1,
    DIRECTORY_BLOCK = 2,
    REPO_BLOCK = 3,
    MODELGEN_BLOCK = 4,
    IMAGE_BLOCK = 5,
}

struct ContextBlock {
    1: ContextBlockType Type,                   // 引用类型
    2: optional FileBlock FileBlock,            // 文件引用
    3: optional DirectoryBlock DirectoryBlock,  // 文件夹引用
    4: optional RepoBlock RepoBlock,            // 仓库引用
    5: optional ModelGenBlock ModelGenBlock,    // 模型生成内容引用
    6: optional ImageBlock ImageBlock // 引用的图片
}

struct Image {
    1: optional string URL
    2: optional string URI
    3: optional string Name
    4: optional string MD5
}

struct ImageBlock {
    1: Image Image
}

struct FileBlock {
    1: list<codeassist_common.ContextIdentifier> Identifiers,
}

struct DirectoryBlock {
    1: codeassist_common.ContextIdentifier Identifier,
}

struct RepoBlock {
    1: codeassist_common.ContextIdentifier Identifier,
}

struct ModelGenBlock {
    1: string Content, // 全部片段内容
    2: string Language, // 片段语言
}


// 返回包
struct StreamChatResponse {
    1: ChatEventType EventType (agw.target="sse", agw.key="event_type"),
    2: optional ChatEventContent EventContent (agw.target="sse", agw.key="event_content"),
    3: bool IsFinish (agw.target="sse", agw.key="is_finish"),
    4: string ErrorMessage (agw.target="sse", agw.key="error_message"),
    5: i32 ErrorCode (agw.target="sse", agw.key="error_code"),

    255: optional base.BaseResp BaseResp,
}

enum ChatEventType {
    DEFAULT = 0;                       // 默认
    OUTPUT = 1;                        // 模型输出
    SUGGEST = 2;                       // 建议
    Verbose = 100                      // 如果用户开启了深度思考，则在模型返回的第一个包之前插一个Verbose包给到上游
}
struct ChatEventContent {
    1: string Content                  // 消息内容
    2: ChatContentType ContentType     // 内容类型
    3: map<string,string> Ext          // 额外信息
    4: string ID                       // block 协议的 ID
}
enum ChatContentType {
    DEFAULT = 0;                       // 默认
    TXT = 1;                           // 文本
    Image = 2;
    Audio = 3;
    Video = 4;
    Link = 6;
    Music = 7;
    Tako = 8;
    File = 9;
    Card = 50;
    BotCard = 51;
    Widget = 52;
    APP = 100;

    OutputSearchResult = 200;
    OutputMultiStream = 201;
    SearchIntentionResult = 300;

    BlockText = 10000; // block Text协议
    BlockResearchProcessCard = 10001; // block 深入研究进度卡片
    BlockBanner = 10002; // block banner
    BlockHeaderText = 10003; // block 标题
    BlockSearchQuery = 10004; // block 搜索query
    BlockSearchResult= 10005; // block 搜索结果
    BlockLinkerReader = 10006; // block linker reader
    BlockVideoReader = 10007; // block 视频浏览
    BlockCode = 10008; // block code
    BlockLocalLife = 10009; // block 本地生活
    BlockGenImage= 10010; // block 画图
    BlockResearch = 10011; // block 研究报告
    BlockImage = 10012; // block image
    BlockResearchWebpage = 10013; // 研究生成的网页
    BlockResearchStatus = 10014; // block 研究进度

    BlockSupertask= 10015; // 超能模式任务
    BlockSupertaskTool=10016; // 超能模式tool执行
    BlockSupertaskProductionFile =10017; // 超能模式产物
    BlockCreationLoading = 10018; // 创作loading
    BlockFileOperation = 10019; // block 文件操作
    BlockFile = 10020; // block 文件
    BlockArtifactCodeFile = 10021 // Artifact代码文件
    BlockCodeAssistProcess = 10022 // Code Assist
    BlockArtifact = 10030 // block 文档
}

struct PromptMessage {
    1: string Role,
    2: string Content,
    3: optional string MetaData,          // llmops上的元数据
}

struct PromptsRenderRequest {
    1: required list<PromptMessage> Messages, // 当前用户消息
    2: string Variables,                      // 上下文变量

    255: optional base.Base Base,
}

struct PromptsRenderResponse {
    1: list<PromptMessage> Messages, // 渲染好要给模型的prompt message
    
    255: optional base.BaseResp BaseResp,
}

struct CaseInfo {
    1: string id,                           // 用例 ID
    2: string query,                        // 查询内容
    3: string repo_name,                    // 仓库名
    4: string tags,                         // 标签
    5: string file_path,                    // 文件路径
    6: string select_code_start_line,       // 选中代码起始行
    7: string select_code_end_line,         // 选中代码结束行
    8: string select_code_content,          // 选中代码内容
    9: string visible_code_start_line,      // 可见代码起始行
    10: string visible_code_end_line        // 可见代码结束行
    11: string resource_key,                // 资源 key
    12: string language,                    // 语言
    13: string user_id,                     // 上传文件的用户ID，optional
    14: string select_file_uri,             // 选中文件的 URI，多文件时使用，同时也是多文件中，选择文件的resource key
    15: string file_content,                // 文件内容
    16: string embedding_top_n              // ckg embedding 检索topN
    17: string embedding_recall_nums        // ckg embedding 召回结果数
    18: string rerank_recall_nums           // rerank 召回结果数
    19: string chat_history                 // 对话历史
    20: string app_name                     // App 名称
    21: string command_name                 // 指令名称
    22: string reference_content            // 引用内容，一般是模型生成的代码
    23: string reference_language           // 引用内容的语言
}

struct E2EPromptsRenderRequest {
    1: string CaseInfo,                       // 用例信息
    2: list<PromptMessage> Messages,          // 当前用户消息，后续处理中需要将PromptMessage的MetaData转换为Chat的Message结构
    3: string PromptKey,                      // prompt 部署到tcc上的key
    4: string PromptLabel,                    // prompt 部署到tcc上的label

    255: optional base.Base Base,
}

struct E2EPromptsRenderResponse {
    1: list<PromptMessage> Messages, // 渲染好要给模型的prompt message
    2: string Extra // 额外信息(code user message/final prompt等等，以map的形式序列化存入)

    255: optional base.BaseResp BaseResp,
}

// from alice/idl
enum HomePageItemType {
    None              = 0
   	CodeGenerate      = 1
   	CodeDebug         = 2
   	CodeKnowledge     = 3
   	CodeBase          = 4
   	CodeArtifacts     = 5
   	PromptTemplate    = 6
   	AdvertisingArticle    = 7          // 用于获客使用的宣传位
}

struct HomePageRequest {
    1: optional string AbVariables,           // Ab 实验参数，序列化后的 JSON 字符串
    2: optional string Language, // 用户默认语言 zh 或 en，不传默认 zh

    255: optional base.Base Base,
}

struct CodebaseItems {
    1: string CodebaseName // codebase name
    2: string CodebaseDescription // codebase description
    3: string CodebaseURL // codebase url
    4: string CodebaseBranchName // codebase branch name
    5: string CodebaseID // codebase id

    6: HomePageItemType HomePageItemType // item type
    7: string Query // query
    8: string IconURL // icon url
    9: bool UseCache // use cache
    10: string Answer // answer
}

struct ArtifactsItems {
    1: string ArtifactsImageURL // image url
    2: string ArtifactsImageDescription // image description
    3: string ArtifactsID // artifacts id

    5: HomePageItemType HomePageItemType // item type
    4: bool UseCache // use cache
}

// codebase相关内容，后续可增加其他布局信息
struct CodebaseTemplates {
    1: list<CodebaseItems> CodebaseItems, // codebase items
}

// artifacts相关内容，后续可增加其他布局信息
struct ArtifactsTemplates {
    1: list<ArtifactsItems> ArtifactsItems // artifacts items
}

struct HomePageResponse {
    1: string HomePageContent // homepage content
    2: string PromptTemplateContent // prompt template

    255: optional base.BaseResp BaseResp,
}
