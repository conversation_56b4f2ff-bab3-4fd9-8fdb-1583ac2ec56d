// Code generated by thriftgo (0.4.2). DO NOT EDIT.

package common

import (
	"bytes"
	"database/sql"
	"database/sql/driver"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

const (
	EventNameError = "error"
)

type ErrorCode int64

const (
	ErrorCode_ErrUndefined                          ErrorCode = 0
	ErrorCode_ErrNoAuth                             ErrorCode = 1001
	ErrorCode_ErrCheckAuth                          ErrorCode = 1002
	ErrorCode_ErrInternal                           ErrorCode = 2001
	ErrorCode_ErrResourceProcessing                 ErrorCode = 2002
	ErrorCode_ErrModelReqFail                       ErrorCode = 3003
	ErrorCode_ErrEmbeddingModelNotExist             ErrorCode = 3004
	ErrorCode_ErrParamInvalid                       ErrorCode = 4001
	ErrorCode_ErrExist                              ErrorCode = 4002
	ErrorCode_ErrOperating                          ErrorCode = 4003
	ErrorCode_ErrContentTooLong                     ErrorCode = 4006
	ErrorCode_ErrRateLimitExceeded                  ErrorCode = 4007
	ErrorCode_ErrQuotaExceeded                      ErrorCode = 4008
	ErrorCode_ErrLogOut                             ErrorCode = 4010
	ErrorCode_ErrAccessReject                       ErrorCode = 4011
	ErrorCode_ErrDowngrade                          ErrorCode = 4012
	ErrorCode_ErrCountryBlocked                     ErrorCode = 4013
	ErrorCode_ErrDeregister                         ErrorCode = 4014
	ErrorCode_ErrNewRiskControl                     ErrorCode = 4019
	ErrorCode_ErrRecordNotFound                     ErrorCode = 4020
	ErrorCode_ErrUsageReachLimit                    ErrorCode = 4021
	ErrorCode_ErrPromptTokensOverLimit              ErrorCode = 4022
	ErrorCode_ErrUnknownModel                       ErrorCode = 4023
	ErrorCode_ErrAgentRunLoopExceeded               ErrorCode = 5001
	ErrorCode_ErrAgentRunTimeExceeded               ErrorCode = 5002
	ErrorCode_ErrAgentRunQuotaExceeded              ErrorCode = 5003
	ErrorCode_ErrAgentRunBusy                       ErrorCode = 5004
	ErrorCode_ErrInsufficientStock                  ErrorCode = 6001
	ErrorCode_ErrBindInvitation                     ErrorCode = 6002
	ErrorCode_ErrNotEnoughPoints                    ErrorCode = 6003
	ErrorCode_ErrNotInEventTime                     ErrorCode = 6004
	ErrorCode_ErrReachedUpperLimit                  ErrorCode = 6005
	ErrorCode_ErrAccountAbnormality                 ErrorCode = 6006
	ErrorCode_ErrDecryption                         ErrorCode = 6007
	ErrorCode_ErrNotGetPrizeTime                    ErrorCode = 6008
	ErrorCode_ErrReviewResultNotReleased            ErrorCode = 6009
	ErrorCode_ErrNoQualification                    ErrorCode = 6010
	ErrorCode_ErrRedemptionLimitReached             ErrorCode = 6011
	ErrorCode_ErrSecurityCheckFail                  ErrorCode = 7001
	ErrorCode_ErrCompetetionInvalid                 ErrorCode = 7002
	ErrorCode_ErrDuplicateCompetitionRegistration   ErrorCode = 7003
	ErrorCode_ErrTeamFull                           ErrorCode = 7004
	ErrorCode_ErrDuplicateTeamRegistration          ErrorCode = 7005
	ErrorCode_ErrTeamNoAuth                         ErrorCode = 7006
	ErrorCode_ErrInvalidTeam                        ErrorCode = 7007
	ErrorCode_ErrDuplicateTeamName                  ErrorCode = 7008
	ErrorCode_ErrInvalidTeamApplication             ErrorCode = 7009
	ErrorCode_ErrInvalidTeamMember                  ErrorCode = 7010
	ErrorCode_ErrExistedEntitlement                 ErrorCode = 7011
	ErrorCode_ErrCodePreCheckFail                   ErrorCode = 7012
	ErrorCode_ErrSandboxCreateTimeExceeded          ErrorCode = 8001
	ErrorCode_ErrSandboxRunTimeExceeded             ErrorCode = 8002
	ErrorCode_ErrSandboxStillRunning                ErrorCode = 8003
	ErrorCode_ErrImageReviewUnpass                  ErrorCode = 8004
	ErrorCode_ErrImageReviewDownload                ErrorCode = 8005
	ErrorCode_ErrImageReviewImageRead               ErrorCode = 8006
	ErrorCode_ErrImageReviewUpload                  ErrorCode = 8007
	ErrorCode_ErrImageReviewGetUrl                  ErrorCode = 8008
	ErrorCode_ErrImageReviewDecode                  ErrorCode = 8009
	ErrorCode_ErrImageReviewInternal                ErrorCode = 8010
	ErrorCode_ErrTextReviewUnpass                   ErrorCode = 8011
	ErrorCode_ErrSandboxInternalError               ErrorCode = 8012
	ErrorCode_ErrTraePayRisk                        ErrorCode = 9001
	ErrorCode_ErrTraePayUnexpiredOrder              ErrorCode = 9002
	ErrorCode_ErrTraePayRegionNotAllowed            ErrorCode = 9003
	ErrorCode_ErrTraePayParamInvalid                ErrorCode = 9004
	ErrorCode_ErrTraePayEntitlementExists           ErrorCode = 9005
	ErrorCode_ErrTraePayInvalidPI                   ErrorCode = 9006
	ErrorCode_ErrNextAgentReachedUpperLimit         ErrorCode = 10001
	ErrorCode_ErrNextSessionStopped                 ErrorCode = 10002
	ErrorCode_ErrNextAgentNotAllowedUseInternalTool ErrorCode = 10003
	ErrorCode_ErrInternalFatal                      ErrorCode = 10004
	ErrorCode_ErrNextAgentReachedGlobalLimit        ErrorCode = 10005
	ErrorCode_ErrNextAgentGrantAccessEnd            ErrorCode = 10006
	ErrorCode_ErrMCPActiveLimitExceeded             ErrorCode = 10007
	ErrorCode_ErrDocumentNotExist                   ErrorCode = 10008
	ErrorCode_ErrDocumentTypeNotSupport             ErrorCode = 10009
)

func (p ErrorCode) String() string {
	switch p {
	case ErrorCode_ErrUndefined:
		return "ErrUndefined"
	case ErrorCode_ErrNoAuth:
		return "ErrNoAuth"
	case ErrorCode_ErrCheckAuth:
		return "ErrCheckAuth"
	case ErrorCode_ErrInternal:
		return "ErrInternal"
	case ErrorCode_ErrResourceProcessing:
		return "ErrResourceProcessing"
	case ErrorCode_ErrModelReqFail:
		return "ErrModelReqFail"
	case ErrorCode_ErrEmbeddingModelNotExist:
		return "ErrEmbeddingModelNotExist"
	case ErrorCode_ErrParamInvalid:
		return "ErrParamInvalid"
	case ErrorCode_ErrExist:
		return "ErrExist"
	case ErrorCode_ErrOperating:
		return "ErrOperating"
	case ErrorCode_ErrContentTooLong:
		return "ErrContentTooLong"
	case ErrorCode_ErrRateLimitExceeded:
		return "ErrRateLimitExceeded"
	case ErrorCode_ErrQuotaExceeded:
		return "ErrQuotaExceeded"
	case ErrorCode_ErrLogOut:
		return "ErrLogOut"
	case ErrorCode_ErrAccessReject:
		return "ErrAccessReject"
	case ErrorCode_ErrDowngrade:
		return "ErrDowngrade"
	case ErrorCode_ErrCountryBlocked:
		return "ErrCountryBlocked"
	case ErrorCode_ErrDeregister:
		return "ErrDeregister"
	case ErrorCode_ErrNewRiskControl:
		return "ErrNewRiskControl"
	case ErrorCode_ErrRecordNotFound:
		return "ErrRecordNotFound"
	case ErrorCode_ErrUsageReachLimit:
		return "ErrUsageReachLimit"
	case ErrorCode_ErrPromptTokensOverLimit:
		return "ErrPromptTokensOverLimit"
	case ErrorCode_ErrUnknownModel:
		return "ErrUnknownModel"
	case ErrorCode_ErrAgentRunLoopExceeded:
		return "ErrAgentRunLoopExceeded"
	case ErrorCode_ErrAgentRunTimeExceeded:
		return "ErrAgentRunTimeExceeded"
	case ErrorCode_ErrAgentRunQuotaExceeded:
		return "ErrAgentRunQuotaExceeded"
	case ErrorCode_ErrAgentRunBusy:
		return "ErrAgentRunBusy"
	case ErrorCode_ErrInsufficientStock:
		return "ErrInsufficientStock"
	case ErrorCode_ErrBindInvitation:
		return "ErrBindInvitation"
	case ErrorCode_ErrNotEnoughPoints:
		return "ErrNotEnoughPoints"
	case ErrorCode_ErrNotInEventTime:
		return "ErrNotInEventTime"
	case ErrorCode_ErrReachedUpperLimit:
		return "ErrReachedUpperLimit"
	case ErrorCode_ErrAccountAbnormality:
		return "ErrAccountAbnormality"
	case ErrorCode_ErrDecryption:
		return "ErrDecryption"
	case ErrorCode_ErrNotGetPrizeTime:
		return "ErrNotGetPrizeTime"
	case ErrorCode_ErrReviewResultNotReleased:
		return "ErrReviewResultNotReleased"
	case ErrorCode_ErrNoQualification:
		return "ErrNoQualification"
	case ErrorCode_ErrRedemptionLimitReached:
		return "ErrRedemptionLimitReached"
	case ErrorCode_ErrSecurityCheckFail:
		return "ErrSecurityCheckFail"
	case ErrorCode_ErrCompetetionInvalid:
		return "ErrCompetetionInvalid"
	case ErrorCode_ErrDuplicateCompetitionRegistration:
		return "ErrDuplicateCompetitionRegistration"
	case ErrorCode_ErrTeamFull:
		return "ErrTeamFull"
	case ErrorCode_ErrDuplicateTeamRegistration:
		return "ErrDuplicateTeamRegistration"
	case ErrorCode_ErrTeamNoAuth:
		return "ErrTeamNoAuth"
	case ErrorCode_ErrInvalidTeam:
		return "ErrInvalidTeam"
	case ErrorCode_ErrDuplicateTeamName:
		return "ErrDuplicateTeamName"
	case ErrorCode_ErrInvalidTeamApplication:
		return "ErrInvalidTeamApplication"
	case ErrorCode_ErrInvalidTeamMember:
		return "ErrInvalidTeamMember"
	case ErrorCode_ErrExistedEntitlement:
		return "ErrExistedEntitlement"
	case ErrorCode_ErrCodePreCheckFail:
		return "ErrCodePreCheckFail"
	case ErrorCode_ErrSandboxCreateTimeExceeded:
		return "ErrSandboxCreateTimeExceeded"
	case ErrorCode_ErrSandboxRunTimeExceeded:
		return "ErrSandboxRunTimeExceeded"
	case ErrorCode_ErrSandboxStillRunning:
		return "ErrSandboxStillRunning"
	case ErrorCode_ErrImageReviewUnpass:
		return "ErrImageReviewUnpass"
	case ErrorCode_ErrImageReviewDownload:
		return "ErrImageReviewDownload"
	case ErrorCode_ErrImageReviewImageRead:
		return "ErrImageReviewImageRead"
	case ErrorCode_ErrImageReviewUpload:
		return "ErrImageReviewUpload"
	case ErrorCode_ErrImageReviewGetUrl:
		return "ErrImageReviewGetUrl"
	case ErrorCode_ErrImageReviewDecode:
		return "ErrImageReviewDecode"
	case ErrorCode_ErrImageReviewInternal:
		return "ErrImageReviewInternal"
	case ErrorCode_ErrTextReviewUnpass:
		return "ErrTextReviewUnpass"
	case ErrorCode_ErrSandboxInternalError:
		return "ErrSandboxInternalError"
	case ErrorCode_ErrTraePayRisk:
		return "ErrTraePayRisk"
	case ErrorCode_ErrTraePayUnexpiredOrder:
		return "ErrTraePayUnexpiredOrder"
	case ErrorCode_ErrTraePayRegionNotAllowed:
		return "ErrTraePayRegionNotAllowed"
	case ErrorCode_ErrTraePayParamInvalid:
		return "ErrTraePayParamInvalid"
	case ErrorCode_ErrTraePayEntitlementExists:
		return "ErrTraePayEntitlementExists"
	case ErrorCode_ErrTraePayInvalidPI:
		return "ErrTraePayInvalidPI"
	case ErrorCode_ErrNextAgentReachedUpperLimit:
		return "ErrNextAgentReachedUpperLimit"
	case ErrorCode_ErrNextSessionStopped:
		return "ErrNextSessionStopped"
	case ErrorCode_ErrNextAgentNotAllowedUseInternalTool:
		return "ErrNextAgentNotAllowedUseInternalTool"
	case ErrorCode_ErrInternalFatal:
		return "ErrInternalFatal"
	case ErrorCode_ErrNextAgentReachedGlobalLimit:
		return "ErrNextAgentReachedGlobalLimit"
	case ErrorCode_ErrNextAgentGrantAccessEnd:
		return "ErrNextAgentGrantAccessEnd"
	case ErrorCode_ErrMCPActiveLimitExceeded:
		return "ErrMCPActiveLimitExceeded"
	case ErrorCode_ErrDocumentNotExist:
		return "ErrDocumentNotExist"
	case ErrorCode_ErrDocumentTypeNotSupport:
		return "ErrDocumentTypeNotSupport"
	}
	return "<UNSET>"
}

func ErrorCodeFromString(s string) (ErrorCode, error) {
	switch s {
	case "ErrUndefined":
		return ErrorCode_ErrUndefined, nil
	case "ErrNoAuth":
		return ErrorCode_ErrNoAuth, nil
	case "ErrCheckAuth":
		return ErrorCode_ErrCheckAuth, nil
	case "ErrInternal":
		return ErrorCode_ErrInternal, nil
	case "ErrResourceProcessing":
		return ErrorCode_ErrResourceProcessing, nil
	case "ErrModelReqFail":
		return ErrorCode_ErrModelReqFail, nil
	case "ErrEmbeddingModelNotExist":
		return ErrorCode_ErrEmbeddingModelNotExist, nil
	case "ErrParamInvalid":
		return ErrorCode_ErrParamInvalid, nil
	case "ErrExist":
		return ErrorCode_ErrExist, nil
	case "ErrOperating":
		return ErrorCode_ErrOperating, nil
	case "ErrContentTooLong":
		return ErrorCode_ErrContentTooLong, nil
	case "ErrRateLimitExceeded":
		return ErrorCode_ErrRateLimitExceeded, nil
	case "ErrQuotaExceeded":
		return ErrorCode_ErrQuotaExceeded, nil
	case "ErrLogOut":
		return ErrorCode_ErrLogOut, nil
	case "ErrAccessReject":
		return ErrorCode_ErrAccessReject, nil
	case "ErrDowngrade":
		return ErrorCode_ErrDowngrade, nil
	case "ErrCountryBlocked":
		return ErrorCode_ErrCountryBlocked, nil
	case "ErrDeregister":
		return ErrorCode_ErrDeregister, nil
	case "ErrNewRiskControl":
		return ErrorCode_ErrNewRiskControl, nil
	case "ErrRecordNotFound":
		return ErrorCode_ErrRecordNotFound, nil
	case "ErrUsageReachLimit":
		return ErrorCode_ErrUsageReachLimit, nil
	case "ErrPromptTokensOverLimit":
		return ErrorCode_ErrPromptTokensOverLimit, nil
	case "ErrUnknownModel":
		return ErrorCode_ErrUnknownModel, nil
	case "ErrAgentRunLoopExceeded":
		return ErrorCode_ErrAgentRunLoopExceeded, nil
	case "ErrAgentRunTimeExceeded":
		return ErrorCode_ErrAgentRunTimeExceeded, nil
	case "ErrAgentRunQuotaExceeded":
		return ErrorCode_ErrAgentRunQuotaExceeded, nil
	case "ErrAgentRunBusy":
		return ErrorCode_ErrAgentRunBusy, nil
	case "ErrInsufficientStock":
		return ErrorCode_ErrInsufficientStock, nil
	case "ErrBindInvitation":
		return ErrorCode_ErrBindInvitation, nil
	case "ErrNotEnoughPoints":
		return ErrorCode_ErrNotEnoughPoints, nil
	case "ErrNotInEventTime":
		return ErrorCode_ErrNotInEventTime, nil
	case "ErrReachedUpperLimit":
		return ErrorCode_ErrReachedUpperLimit, nil
	case "ErrAccountAbnormality":
		return ErrorCode_ErrAccountAbnormality, nil
	case "ErrDecryption":
		return ErrorCode_ErrDecryption, nil
	case "ErrNotGetPrizeTime":
		return ErrorCode_ErrNotGetPrizeTime, nil
	case "ErrReviewResultNotReleased":
		return ErrorCode_ErrReviewResultNotReleased, nil
	case "ErrNoQualification":
		return ErrorCode_ErrNoQualification, nil
	case "ErrRedemptionLimitReached":
		return ErrorCode_ErrRedemptionLimitReached, nil
	case "ErrSecurityCheckFail":
		return ErrorCode_ErrSecurityCheckFail, nil
	case "ErrCompetetionInvalid":
		return ErrorCode_ErrCompetetionInvalid, nil
	case "ErrDuplicateCompetitionRegistration":
		return ErrorCode_ErrDuplicateCompetitionRegistration, nil
	case "ErrTeamFull":
		return ErrorCode_ErrTeamFull, nil
	case "ErrDuplicateTeamRegistration":
		return ErrorCode_ErrDuplicateTeamRegistration, nil
	case "ErrTeamNoAuth":
		return ErrorCode_ErrTeamNoAuth, nil
	case "ErrInvalidTeam":
		return ErrorCode_ErrInvalidTeam, nil
	case "ErrDuplicateTeamName":
		return ErrorCode_ErrDuplicateTeamName, nil
	case "ErrInvalidTeamApplication":
		return ErrorCode_ErrInvalidTeamApplication, nil
	case "ErrInvalidTeamMember":
		return ErrorCode_ErrInvalidTeamMember, nil
	case "ErrExistedEntitlement":
		return ErrorCode_ErrExistedEntitlement, nil
	case "ErrCodePreCheckFail":
		return ErrorCode_ErrCodePreCheckFail, nil
	case "ErrSandboxCreateTimeExceeded":
		return ErrorCode_ErrSandboxCreateTimeExceeded, nil
	case "ErrSandboxRunTimeExceeded":
		return ErrorCode_ErrSandboxRunTimeExceeded, nil
	case "ErrSandboxStillRunning":
		return ErrorCode_ErrSandboxStillRunning, nil
	case "ErrImageReviewUnpass":
		return ErrorCode_ErrImageReviewUnpass, nil
	case "ErrImageReviewDownload":
		return ErrorCode_ErrImageReviewDownload, nil
	case "ErrImageReviewImageRead":
		return ErrorCode_ErrImageReviewImageRead, nil
	case "ErrImageReviewUpload":
		return ErrorCode_ErrImageReviewUpload, nil
	case "ErrImageReviewGetUrl":
		return ErrorCode_ErrImageReviewGetUrl, nil
	case "ErrImageReviewDecode":
		return ErrorCode_ErrImageReviewDecode, nil
	case "ErrImageReviewInternal":
		return ErrorCode_ErrImageReviewInternal, nil
	case "ErrTextReviewUnpass":
		return ErrorCode_ErrTextReviewUnpass, nil
	case "ErrSandboxInternalError":
		return ErrorCode_ErrSandboxInternalError, nil
	case "ErrTraePayRisk":
		return ErrorCode_ErrTraePayRisk, nil
	case "ErrTraePayUnexpiredOrder":
		return ErrorCode_ErrTraePayUnexpiredOrder, nil
	case "ErrTraePayRegionNotAllowed":
		return ErrorCode_ErrTraePayRegionNotAllowed, nil
	case "ErrTraePayParamInvalid":
		return ErrorCode_ErrTraePayParamInvalid, nil
	case "ErrTraePayEntitlementExists":
		return ErrorCode_ErrTraePayEntitlementExists, nil
	case "ErrTraePayInvalidPI":
		return ErrorCode_ErrTraePayInvalidPI, nil
	case "ErrNextAgentReachedUpperLimit":
		return ErrorCode_ErrNextAgentReachedUpperLimit, nil
	case "ErrNextSessionStopped":
		return ErrorCode_ErrNextSessionStopped, nil
	case "ErrNextAgentNotAllowedUseInternalTool":
		return ErrorCode_ErrNextAgentNotAllowedUseInternalTool, nil
	case "ErrInternalFatal":
		return ErrorCode_ErrInternalFatal, nil
	case "ErrNextAgentReachedGlobalLimit":
		return ErrorCode_ErrNextAgentReachedGlobalLimit, nil
	case "ErrNextAgentGrantAccessEnd":
		return ErrorCode_ErrNextAgentGrantAccessEnd, nil
	case "ErrMCPActiveLimitExceeded":
		return ErrorCode_ErrMCPActiveLimitExceeded, nil
	case "ErrDocumentNotExist":
		return ErrorCode_ErrDocumentNotExist, nil
	case "ErrDocumentTypeNotSupport":
		return ErrorCode_ErrDocumentTypeNotSupport, nil
	}
	return ErrorCode(0), fmt.Errorf("not a valid ErrorCode string")
}

func ErrorCodePtr(v ErrorCode) *ErrorCode { return &v }
func (p *ErrorCode) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = ErrorCode(result.Int64)
	return
}

func (p *ErrorCode) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type JsonVariables = string

type BaseResp struct {
	Code    int64  `thrift:"code,1" frugal:"1,default,i64" json:"code"`
	Message string `thrift:"message,2" frugal:"2,default,string" json:"message"`
}

func NewBaseResp() *BaseResp {
	return &BaseResp{

		Code:    0,
		Message: "",
	}
}

func (p *BaseResp) InitDefault() {
	p.Code = 0
	p.Message = ""
}

func (p *BaseResp) GetCode() (v int64) {
	return p.Code
}

func (p *BaseResp) GetMessage() (v string) {
	return p.Message
}
func (p *BaseResp) SetCode(val int64) {
	p.Code = val
}
func (p *BaseResp) SetMessage(val string) {
	p.Message = val
}

var fieldIDToName_BaseResp = map[int16]string{
	1: "code",
	2: "message",
}

func (p *BaseResp) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_BaseResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *BaseResp) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Code = _field
	return nil
}
func (p *BaseResp) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Message = _field
	return nil
}

func (p *BaseResp) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("BaseResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *BaseResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("code", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.Code); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *BaseResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("message", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Message); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *BaseResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BaseResp(%+v)", *p)

}

func (p *BaseResp) DeepEqual(ano *BaseResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Code) {
		return false
	}
	if !p.Field2DeepEqual(ano.Message) {
		return false
	}
	return true
}

func (p *BaseResp) Field1DeepEqual(src int64) bool {

	if p.Code != src {
		return false
	}
	return true
}
func (p *BaseResp) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Message, src) != 0 {
		return false
	}
	return true
}

type HTTPRequest struct {
	Authorization *string             `thrift:"Authorization,1,optional" frugal:"1,optional,string" json:"Authorization,omitempty"`
	XServiceToken *string             `thrift:"XServiceToken,2,optional" frugal:"2,optional,string" json:"XServiceToken,omitempty"`
	XJwtToken     *string             `thrift:"XJwtToken,3,optional" frugal:"3,optional,string" json:"XJwtToken,omitempty"`
	XUsername     *string             `thrift:"XUsername,4,optional" frugal:"4,optional,string" json:"XUsername,omitempty"`
	XAuthToken    *string             `thrift:"XAuthToken,5,optional" frugal:"5,optional,string" json:"XAuthToken,omitempty"`
	XIDEToken     *string             `thrift:"XIDEToken,6,optional" frugal:"6,optional,string" json:"XIDEToken,omitempty"`
	XSendTS       *string             `thrift:"XSendTS,7,optional" frugal:"7,optional,string" json:"XSendTS,omitempty"`
	XAppId        *string             `thrift:"XAppId,8,optional" frugal:"8,optional,string" json:"XAppId,omitempty"`
	Host          *string             `thrift:"Host,9,optional" frugal:"9,optional,string" json:"Host,omitempty"`
	ClientIP      *string             `thrift:"ClientIP,10,optional" frugal:"10,optional,string" json:"ClientIP,omitempty"`
	AGWRecvTs     *int64              `thrift:"AGWRecvTs,11,optional" frugal:"11,optional,i64" json:"AGWRecvTs,omitempty"`
	XAppVersion   *string             `thrift:"XAppVersion,12,optional" frugal:"12,optional,string" json:"XAppVersion,omitempty"`
	Headers       map[string][]string `thrift:"Headers,13,optional" frugal:"13,optional,map<string:list<string>>" json:"Headers,omitempty"`
	Request       []byte              `thrift:"Request,14,optional" frugal:"14,optional,binary" json:"Request,omitempty"`
	Cookie        *string             `thrift:"Cookie,15,optional" frugal:"15,optional,string" json:"Cookie,omitempty"`
	UserAgent     *string             `thrift:"UserAgent,16,optional" frugal:"16,optional,string" json:"UserAgent,omitempty"`
	Forwarded     *string             `thrift:"Forwarded,17,optional" frugal:"17,optional,string" json:"Forwarded,omitempty"`
	Referer       *string             `thrift:"Referer,18,optional" frugal:"18,optional,string" json:"Referer,omitempty"`
	WebSignRes    *string             `thrift:"WebSignRes,19,optional" frugal:"19,optional,string" json:"WebSignRes,omitempty"`
	WebMSToken    *string             `thrift:"WebMSToken,20,optional" frugal:"20,optional,string" json:"WebMSToken,omitempty"`
	TtWid         *string             `thrift:"TtWid,21,optional" frugal:"21,optional,string" json:"TtWid,omitempty"`
	FP            *string             `thrift:"FP,22,optional" frugal:"22,optional,string" json:"FP,omitempty"`
}

func NewHTTPRequest() *HTTPRequest {
	return &HTTPRequest{}
}

func (p *HTTPRequest) InitDefault() {
}

var HTTPRequest_Authorization_DEFAULT string

func (p *HTTPRequest) GetAuthorization() (v string) {
	if !p.IsSetAuthorization() {
		return HTTPRequest_Authorization_DEFAULT
	}
	return *p.Authorization
}

var HTTPRequest_XServiceToken_DEFAULT string

func (p *HTTPRequest) GetXServiceToken() (v string) {
	if !p.IsSetXServiceToken() {
		return HTTPRequest_XServiceToken_DEFAULT
	}
	return *p.XServiceToken
}

var HTTPRequest_XJwtToken_DEFAULT string

func (p *HTTPRequest) GetXJwtToken() (v string) {
	if !p.IsSetXJwtToken() {
		return HTTPRequest_XJwtToken_DEFAULT
	}
	return *p.XJwtToken
}

var HTTPRequest_XUsername_DEFAULT string

func (p *HTTPRequest) GetXUsername() (v string) {
	if !p.IsSetXUsername() {
		return HTTPRequest_XUsername_DEFAULT
	}
	return *p.XUsername
}

var HTTPRequest_XAuthToken_DEFAULT string

func (p *HTTPRequest) GetXAuthToken() (v string) {
	if !p.IsSetXAuthToken() {
		return HTTPRequest_XAuthToken_DEFAULT
	}
	return *p.XAuthToken
}

var HTTPRequest_XIDEToken_DEFAULT string

func (p *HTTPRequest) GetXIDEToken() (v string) {
	if !p.IsSetXIDEToken() {
		return HTTPRequest_XIDEToken_DEFAULT
	}
	return *p.XIDEToken
}

var HTTPRequest_XSendTS_DEFAULT string

func (p *HTTPRequest) GetXSendTS() (v string) {
	if !p.IsSetXSendTS() {
		return HTTPRequest_XSendTS_DEFAULT
	}
	return *p.XSendTS
}

var HTTPRequest_XAppId_DEFAULT string

func (p *HTTPRequest) GetXAppId() (v string) {
	if !p.IsSetXAppId() {
		return HTTPRequest_XAppId_DEFAULT
	}
	return *p.XAppId
}

var HTTPRequest_Host_DEFAULT string

func (p *HTTPRequest) GetHost() (v string) {
	if !p.IsSetHost() {
		return HTTPRequest_Host_DEFAULT
	}
	return *p.Host
}

var HTTPRequest_ClientIP_DEFAULT string

func (p *HTTPRequest) GetClientIP() (v string) {
	if !p.IsSetClientIP() {
		return HTTPRequest_ClientIP_DEFAULT
	}
	return *p.ClientIP
}

var HTTPRequest_AGWRecvTs_DEFAULT int64

func (p *HTTPRequest) GetAGWRecvTs() (v int64) {
	if !p.IsSetAGWRecvTs() {
		return HTTPRequest_AGWRecvTs_DEFAULT
	}
	return *p.AGWRecvTs
}

var HTTPRequest_XAppVersion_DEFAULT string

func (p *HTTPRequest) GetXAppVersion() (v string) {
	if !p.IsSetXAppVersion() {
		return HTTPRequest_XAppVersion_DEFAULT
	}
	return *p.XAppVersion
}

var HTTPRequest_Headers_DEFAULT map[string][]string

func (p *HTTPRequest) GetHeaders() (v map[string][]string) {
	if !p.IsSetHeaders() {
		return HTTPRequest_Headers_DEFAULT
	}
	return p.Headers
}

var HTTPRequest_Request_DEFAULT []byte

func (p *HTTPRequest) GetRequest() (v []byte) {
	if !p.IsSetRequest() {
		return HTTPRequest_Request_DEFAULT
	}
	return p.Request
}

var HTTPRequest_Cookie_DEFAULT string

func (p *HTTPRequest) GetCookie() (v string) {
	if !p.IsSetCookie() {
		return HTTPRequest_Cookie_DEFAULT
	}
	return *p.Cookie
}

var HTTPRequest_UserAgent_DEFAULT string

func (p *HTTPRequest) GetUserAgent() (v string) {
	if !p.IsSetUserAgent() {
		return HTTPRequest_UserAgent_DEFAULT
	}
	return *p.UserAgent
}

var HTTPRequest_Forwarded_DEFAULT string

func (p *HTTPRequest) GetForwarded() (v string) {
	if !p.IsSetForwarded() {
		return HTTPRequest_Forwarded_DEFAULT
	}
	return *p.Forwarded
}

var HTTPRequest_Referer_DEFAULT string

func (p *HTTPRequest) GetReferer() (v string) {
	if !p.IsSetReferer() {
		return HTTPRequest_Referer_DEFAULT
	}
	return *p.Referer
}

var HTTPRequest_WebSignRes_DEFAULT string

func (p *HTTPRequest) GetWebSignRes() (v string) {
	if !p.IsSetWebSignRes() {
		return HTTPRequest_WebSignRes_DEFAULT
	}
	return *p.WebSignRes
}

var HTTPRequest_WebMSToken_DEFAULT string

func (p *HTTPRequest) GetWebMSToken() (v string) {
	if !p.IsSetWebMSToken() {
		return HTTPRequest_WebMSToken_DEFAULT
	}
	return *p.WebMSToken
}

var HTTPRequest_TtWid_DEFAULT string

func (p *HTTPRequest) GetTtWid() (v string) {
	if !p.IsSetTtWid() {
		return HTTPRequest_TtWid_DEFAULT
	}
	return *p.TtWid
}

var HTTPRequest_FP_DEFAULT string

func (p *HTTPRequest) GetFP() (v string) {
	if !p.IsSetFP() {
		return HTTPRequest_FP_DEFAULT
	}
	return *p.FP
}
func (p *HTTPRequest) SetAuthorization(val *string) {
	p.Authorization = val
}
func (p *HTTPRequest) SetXServiceToken(val *string) {
	p.XServiceToken = val
}
func (p *HTTPRequest) SetXJwtToken(val *string) {
	p.XJwtToken = val
}
func (p *HTTPRequest) SetXUsername(val *string) {
	p.XUsername = val
}
func (p *HTTPRequest) SetXAuthToken(val *string) {
	p.XAuthToken = val
}
func (p *HTTPRequest) SetXIDEToken(val *string) {
	p.XIDEToken = val
}
func (p *HTTPRequest) SetXSendTS(val *string) {
	p.XSendTS = val
}
func (p *HTTPRequest) SetXAppId(val *string) {
	p.XAppId = val
}
func (p *HTTPRequest) SetHost(val *string) {
	p.Host = val
}
func (p *HTTPRequest) SetClientIP(val *string) {
	p.ClientIP = val
}
func (p *HTTPRequest) SetAGWRecvTs(val *int64) {
	p.AGWRecvTs = val
}
func (p *HTTPRequest) SetXAppVersion(val *string) {
	p.XAppVersion = val
}
func (p *HTTPRequest) SetHeaders(val map[string][]string) {
	p.Headers = val
}
func (p *HTTPRequest) SetRequest(val []byte) {
	p.Request = val
}
func (p *HTTPRequest) SetCookie(val *string) {
	p.Cookie = val
}
func (p *HTTPRequest) SetUserAgent(val *string) {
	p.UserAgent = val
}
func (p *HTTPRequest) SetForwarded(val *string) {
	p.Forwarded = val
}
func (p *HTTPRequest) SetReferer(val *string) {
	p.Referer = val
}
func (p *HTTPRequest) SetWebSignRes(val *string) {
	p.WebSignRes = val
}
func (p *HTTPRequest) SetWebMSToken(val *string) {
	p.WebMSToken = val
}
func (p *HTTPRequest) SetTtWid(val *string) {
	p.TtWid = val
}
func (p *HTTPRequest) SetFP(val *string) {
	p.FP = val
}

var fieldIDToName_HTTPRequest = map[int16]string{
	1:  "Authorization",
	2:  "XServiceToken",
	3:  "XJwtToken",
	4:  "XUsername",
	5:  "XAuthToken",
	6:  "XIDEToken",
	7:  "XSendTS",
	8:  "XAppId",
	9:  "Host",
	10: "ClientIP",
	11: "AGWRecvTs",
	12: "XAppVersion",
	13: "Headers",
	14: "Request",
	15: "Cookie",
	16: "UserAgent",
	17: "Forwarded",
	18: "Referer",
	19: "WebSignRes",
	20: "WebMSToken",
	21: "TtWid",
	22: "FP",
}

func (p *HTTPRequest) IsSetAuthorization() bool {
	return p.Authorization != nil
}

func (p *HTTPRequest) IsSetXServiceToken() bool {
	return p.XServiceToken != nil
}

func (p *HTTPRequest) IsSetXJwtToken() bool {
	return p.XJwtToken != nil
}

func (p *HTTPRequest) IsSetXUsername() bool {
	return p.XUsername != nil
}

func (p *HTTPRequest) IsSetXAuthToken() bool {
	return p.XAuthToken != nil
}

func (p *HTTPRequest) IsSetXIDEToken() bool {
	return p.XIDEToken != nil
}

func (p *HTTPRequest) IsSetXSendTS() bool {
	return p.XSendTS != nil
}

func (p *HTTPRequest) IsSetXAppId() bool {
	return p.XAppId != nil
}

func (p *HTTPRequest) IsSetHost() bool {
	return p.Host != nil
}

func (p *HTTPRequest) IsSetClientIP() bool {
	return p.ClientIP != nil
}

func (p *HTTPRequest) IsSetAGWRecvTs() bool {
	return p.AGWRecvTs != nil
}

func (p *HTTPRequest) IsSetXAppVersion() bool {
	return p.XAppVersion != nil
}

func (p *HTTPRequest) IsSetHeaders() bool {
	return p.Headers != nil
}

func (p *HTTPRequest) IsSetRequest() bool {
	return p.Request != nil
}

func (p *HTTPRequest) IsSetCookie() bool {
	return p.Cookie != nil
}

func (p *HTTPRequest) IsSetUserAgent() bool {
	return p.UserAgent != nil
}

func (p *HTTPRequest) IsSetForwarded() bool {
	return p.Forwarded != nil
}

func (p *HTTPRequest) IsSetReferer() bool {
	return p.Referer != nil
}

func (p *HTTPRequest) IsSetWebSignRes() bool {
	return p.WebSignRes != nil
}

func (p *HTTPRequest) IsSetWebMSToken() bool {
	return p.WebMSToken != nil
}

func (p *HTTPRequest) IsSetTtWid() bool {
	return p.TtWid != nil
}

func (p *HTTPRequest) IsSetFP() bool {
	return p.FP != nil
}

func (p *HTTPRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField14(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField15(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 16:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField16(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 17:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField17(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 18:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField18(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 19:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField19(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 20:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField20(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 21:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField21(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 22:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField22(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_HTTPRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *HTTPRequest) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Authorization = _field
	return nil
}
func (p *HTTPRequest) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.XServiceToken = _field
	return nil
}
func (p *HTTPRequest) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.XJwtToken = _field
	return nil
}
func (p *HTTPRequest) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.XUsername = _field
	return nil
}
func (p *HTTPRequest) ReadField5(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.XAuthToken = _field
	return nil
}
func (p *HTTPRequest) ReadField6(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.XIDEToken = _field
	return nil
}
func (p *HTTPRequest) ReadField7(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.XSendTS = _field
	return nil
}
func (p *HTTPRequest) ReadField8(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.XAppId = _field
	return nil
}
func (p *HTTPRequest) ReadField9(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Host = _field
	return nil
}
func (p *HTTPRequest) ReadField10(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ClientIP = _field
	return nil
}
func (p *HTTPRequest) ReadField11(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.AGWRecvTs = _field
	return nil
}
func (p *HTTPRequest) ReadField12(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.XAppVersion = _field
	return nil
}
func (p *HTTPRequest) ReadField13(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[string][]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_key = v
		}
		_, size, err := iprot.ReadListBegin()
		if err != nil {
			return err
		}
		_val := make([]string, 0, size)
		for i := 0; i < size; i++ {

			var _elem string
			if v, err := iprot.ReadString(); err != nil {
				return err
			} else {
				_elem = v
			}

			_val = append(_val, _elem)
		}
		if err := iprot.ReadListEnd(); err != nil {
			return err
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.Headers = _field
	return nil
}
func (p *HTTPRequest) ReadField14(iprot thrift.TProtocol) error {

	var _field []byte
	if v, err := iprot.ReadBinary(); err != nil {
		return err
	} else {
		_field = []byte(v)
	}
	p.Request = _field
	return nil
}
func (p *HTTPRequest) ReadField15(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Cookie = _field
	return nil
}
func (p *HTTPRequest) ReadField16(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.UserAgent = _field
	return nil
}
func (p *HTTPRequest) ReadField17(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Forwarded = _field
	return nil
}
func (p *HTTPRequest) ReadField18(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Referer = _field
	return nil
}
func (p *HTTPRequest) ReadField19(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.WebSignRes = _field
	return nil
}
func (p *HTTPRequest) ReadField20(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.WebMSToken = _field
	return nil
}
func (p *HTTPRequest) ReadField21(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.TtWid = _field
	return nil
}
func (p *HTTPRequest) ReadField22(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.FP = _field
	return nil
}

func (p *HTTPRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("HTTPRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
		if err = p.writeField14(oprot); err != nil {
			fieldId = 14
			goto WriteFieldError
		}
		if err = p.writeField15(oprot); err != nil {
			fieldId = 15
			goto WriteFieldError
		}
		if err = p.writeField16(oprot); err != nil {
			fieldId = 16
			goto WriteFieldError
		}
		if err = p.writeField17(oprot); err != nil {
			fieldId = 17
			goto WriteFieldError
		}
		if err = p.writeField18(oprot); err != nil {
			fieldId = 18
			goto WriteFieldError
		}
		if err = p.writeField19(oprot); err != nil {
			fieldId = 19
			goto WriteFieldError
		}
		if err = p.writeField20(oprot); err != nil {
			fieldId = 20
			goto WriteFieldError
		}
		if err = p.writeField21(oprot); err != nil {
			fieldId = 21
			goto WriteFieldError
		}
		if err = p.writeField22(oprot); err != nil {
			fieldId = 22
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *HTTPRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetAuthorization() {
		if err = oprot.WriteFieldBegin("Authorization", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Authorization); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *HTTPRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetXServiceToken() {
		if err = oprot.WriteFieldBegin("XServiceToken", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.XServiceToken); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *HTTPRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetXJwtToken() {
		if err = oprot.WriteFieldBegin("XJwtToken", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.XJwtToken); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *HTTPRequest) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetXUsername() {
		if err = oprot.WriteFieldBegin("XUsername", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.XUsername); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *HTTPRequest) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetXAuthToken() {
		if err = oprot.WriteFieldBegin("XAuthToken", thrift.STRING, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.XAuthToken); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *HTTPRequest) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetXIDEToken() {
		if err = oprot.WriteFieldBegin("XIDEToken", thrift.STRING, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.XIDEToken); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *HTTPRequest) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetXSendTS() {
		if err = oprot.WriteFieldBegin("XSendTS", thrift.STRING, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.XSendTS); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}
func (p *HTTPRequest) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetXAppId() {
		if err = oprot.WriteFieldBegin("XAppId", thrift.STRING, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.XAppId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}
func (p *HTTPRequest) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetHost() {
		if err = oprot.WriteFieldBegin("Host", thrift.STRING, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Host); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}
func (p *HTTPRequest) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetClientIP() {
		if err = oprot.WriteFieldBegin("ClientIP", thrift.STRING, 10); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ClientIP); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}
func (p *HTTPRequest) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetAGWRecvTs() {
		if err = oprot.WriteFieldBegin("AGWRecvTs", thrift.I64, 11); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.AGWRecvTs); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}
func (p *HTTPRequest) writeField12(oprot thrift.TProtocol) (err error) {
	if p.IsSetXAppVersion() {
		if err = oprot.WriteFieldBegin("XAppVersion", thrift.STRING, 12); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.XAppVersion); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}
func (p *HTTPRequest) writeField13(oprot thrift.TProtocol) (err error) {
	if p.IsSetHeaders() {
		if err = oprot.WriteFieldBegin("Headers", thrift.MAP, 13); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.LIST, len(p.Headers)); err != nil {
			return err
		}
		for k, v := range p.Headers {
			if err := oprot.WriteString(k); err != nil {
				return err
			}
			if err := oprot.WriteListBegin(thrift.STRING, len(v)); err != nil {
				return err
			}
			for _, v := range v {
				if err := oprot.WriteString(v); err != nil {
					return err
				}
			}
			if err := oprot.WriteListEnd(); err != nil {
				return err
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}
func (p *HTTPRequest) writeField14(oprot thrift.TProtocol) (err error) {
	if p.IsSetRequest() {
		if err = oprot.WriteFieldBegin("Request", thrift.STRING, 14); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBinary([]byte(p.Request)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 end error: ", p), err)
}
func (p *HTTPRequest) writeField15(oprot thrift.TProtocol) (err error) {
	if p.IsSetCookie() {
		if err = oprot.WriteFieldBegin("Cookie", thrift.STRING, 15); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Cookie); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 end error: ", p), err)
}
func (p *HTTPRequest) writeField16(oprot thrift.TProtocol) (err error) {
	if p.IsSetUserAgent() {
		if err = oprot.WriteFieldBegin("UserAgent", thrift.STRING, 16); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.UserAgent); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 end error: ", p), err)
}
func (p *HTTPRequest) writeField17(oprot thrift.TProtocol) (err error) {
	if p.IsSetForwarded() {
		if err = oprot.WriteFieldBegin("Forwarded", thrift.STRING, 17); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Forwarded); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 end error: ", p), err)
}
func (p *HTTPRequest) writeField18(oprot thrift.TProtocol) (err error) {
	if p.IsSetReferer() {
		if err = oprot.WriteFieldBegin("Referer", thrift.STRING, 18); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Referer); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 18 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 18 end error: ", p), err)
}
func (p *HTTPRequest) writeField19(oprot thrift.TProtocol) (err error) {
	if p.IsSetWebSignRes() {
		if err = oprot.WriteFieldBegin("WebSignRes", thrift.STRING, 19); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.WebSignRes); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 19 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 19 end error: ", p), err)
}
func (p *HTTPRequest) writeField20(oprot thrift.TProtocol) (err error) {
	if p.IsSetWebMSToken() {
		if err = oprot.WriteFieldBegin("WebMSToken", thrift.STRING, 20); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.WebMSToken); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 20 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 20 end error: ", p), err)
}
func (p *HTTPRequest) writeField21(oprot thrift.TProtocol) (err error) {
	if p.IsSetTtWid() {
		if err = oprot.WriteFieldBegin("TtWid", thrift.STRING, 21); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.TtWid); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 21 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 21 end error: ", p), err)
}
func (p *HTTPRequest) writeField22(oprot thrift.TProtocol) (err error) {
	if p.IsSetFP() {
		if err = oprot.WriteFieldBegin("FP", thrift.STRING, 22); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.FP); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 22 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 22 end error: ", p), err)
}

func (p *HTTPRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("HTTPRequest(%+v)", *p)

}

func (p *HTTPRequest) DeepEqual(ano *HTTPRequest) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Authorization) {
		return false
	}
	if !p.Field2DeepEqual(ano.XServiceToken) {
		return false
	}
	if !p.Field3DeepEqual(ano.XJwtToken) {
		return false
	}
	if !p.Field4DeepEqual(ano.XUsername) {
		return false
	}
	if !p.Field5DeepEqual(ano.XAuthToken) {
		return false
	}
	if !p.Field6DeepEqual(ano.XIDEToken) {
		return false
	}
	if !p.Field7DeepEqual(ano.XSendTS) {
		return false
	}
	if !p.Field8DeepEqual(ano.XAppId) {
		return false
	}
	if !p.Field9DeepEqual(ano.Host) {
		return false
	}
	if !p.Field10DeepEqual(ano.ClientIP) {
		return false
	}
	if !p.Field11DeepEqual(ano.AGWRecvTs) {
		return false
	}
	if !p.Field12DeepEqual(ano.XAppVersion) {
		return false
	}
	if !p.Field13DeepEqual(ano.Headers) {
		return false
	}
	if !p.Field14DeepEqual(ano.Request) {
		return false
	}
	if !p.Field15DeepEqual(ano.Cookie) {
		return false
	}
	if !p.Field16DeepEqual(ano.UserAgent) {
		return false
	}
	if !p.Field17DeepEqual(ano.Forwarded) {
		return false
	}
	if !p.Field18DeepEqual(ano.Referer) {
		return false
	}
	if !p.Field19DeepEqual(ano.WebSignRes) {
		return false
	}
	if !p.Field20DeepEqual(ano.WebMSToken) {
		return false
	}
	if !p.Field21DeepEqual(ano.TtWid) {
		return false
	}
	if !p.Field22DeepEqual(ano.FP) {
		return false
	}
	return true
}

func (p *HTTPRequest) Field1DeepEqual(src *string) bool {

	if p.Authorization == src {
		return true
	} else if p.Authorization == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Authorization, *src) != 0 {
		return false
	}
	return true
}
func (p *HTTPRequest) Field2DeepEqual(src *string) bool {

	if p.XServiceToken == src {
		return true
	} else if p.XServiceToken == nil || src == nil {
		return false
	}
	if strings.Compare(*p.XServiceToken, *src) != 0 {
		return false
	}
	return true
}
func (p *HTTPRequest) Field3DeepEqual(src *string) bool {

	if p.XJwtToken == src {
		return true
	} else if p.XJwtToken == nil || src == nil {
		return false
	}
	if strings.Compare(*p.XJwtToken, *src) != 0 {
		return false
	}
	return true
}
func (p *HTTPRequest) Field4DeepEqual(src *string) bool {

	if p.XUsername == src {
		return true
	} else if p.XUsername == nil || src == nil {
		return false
	}
	if strings.Compare(*p.XUsername, *src) != 0 {
		return false
	}
	return true
}
func (p *HTTPRequest) Field5DeepEqual(src *string) bool {

	if p.XAuthToken == src {
		return true
	} else if p.XAuthToken == nil || src == nil {
		return false
	}
	if strings.Compare(*p.XAuthToken, *src) != 0 {
		return false
	}
	return true
}
func (p *HTTPRequest) Field6DeepEqual(src *string) bool {

	if p.XIDEToken == src {
		return true
	} else if p.XIDEToken == nil || src == nil {
		return false
	}
	if strings.Compare(*p.XIDEToken, *src) != 0 {
		return false
	}
	return true
}
func (p *HTTPRequest) Field7DeepEqual(src *string) bool {

	if p.XSendTS == src {
		return true
	} else if p.XSendTS == nil || src == nil {
		return false
	}
	if strings.Compare(*p.XSendTS, *src) != 0 {
		return false
	}
	return true
}
func (p *HTTPRequest) Field8DeepEqual(src *string) bool {

	if p.XAppId == src {
		return true
	} else if p.XAppId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.XAppId, *src) != 0 {
		return false
	}
	return true
}
func (p *HTTPRequest) Field9DeepEqual(src *string) bool {

	if p.Host == src {
		return true
	} else if p.Host == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Host, *src) != 0 {
		return false
	}
	return true
}
func (p *HTTPRequest) Field10DeepEqual(src *string) bool {

	if p.ClientIP == src {
		return true
	} else if p.ClientIP == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ClientIP, *src) != 0 {
		return false
	}
	return true
}
func (p *HTTPRequest) Field11DeepEqual(src *int64) bool {

	if p.AGWRecvTs == src {
		return true
	} else if p.AGWRecvTs == nil || src == nil {
		return false
	}
	if *p.AGWRecvTs != *src {
		return false
	}
	return true
}
func (p *HTTPRequest) Field12DeepEqual(src *string) bool {

	if p.XAppVersion == src {
		return true
	} else if p.XAppVersion == nil || src == nil {
		return false
	}
	if strings.Compare(*p.XAppVersion, *src) != 0 {
		return false
	}
	return true
}
func (p *HTTPRequest) Field13DeepEqual(src map[string][]string) bool {

	if len(p.Headers) != len(src) {
		return false
	}
	for k, v := range p.Headers {
		_src := src[k]
		if len(v) != len(_src) {
			return false
		}
		for i, v := range v {
			_src1 := _src[i]
			if strings.Compare(v, _src1) != 0 {
				return false
			}
		}
	}
	return true
}
func (p *HTTPRequest) Field14DeepEqual(src []byte) bool {

	if bytes.Compare(p.Request, src) != 0 {
		return false
	}
	return true
}
func (p *HTTPRequest) Field15DeepEqual(src *string) bool {

	if p.Cookie == src {
		return true
	} else if p.Cookie == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Cookie, *src) != 0 {
		return false
	}
	return true
}
func (p *HTTPRequest) Field16DeepEqual(src *string) bool {

	if p.UserAgent == src {
		return true
	} else if p.UserAgent == nil || src == nil {
		return false
	}
	if strings.Compare(*p.UserAgent, *src) != 0 {
		return false
	}
	return true
}
func (p *HTTPRequest) Field17DeepEqual(src *string) bool {

	if p.Forwarded == src {
		return true
	} else if p.Forwarded == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Forwarded, *src) != 0 {
		return false
	}
	return true
}
func (p *HTTPRequest) Field18DeepEqual(src *string) bool {

	if p.Referer == src {
		return true
	} else if p.Referer == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Referer, *src) != 0 {
		return false
	}
	return true
}
func (p *HTTPRequest) Field19DeepEqual(src *string) bool {

	if p.WebSignRes == src {
		return true
	} else if p.WebSignRes == nil || src == nil {
		return false
	}
	if strings.Compare(*p.WebSignRes, *src) != 0 {
		return false
	}
	return true
}
func (p *HTTPRequest) Field20DeepEqual(src *string) bool {

	if p.WebMSToken == src {
		return true
	} else if p.WebMSToken == nil || src == nil {
		return false
	}
	if strings.Compare(*p.WebMSToken, *src) != 0 {
		return false
	}
	return true
}
func (p *HTTPRequest) Field21DeepEqual(src *string) bool {

	if p.TtWid == src {
		return true
	} else if p.TtWid == nil || src == nil {
		return false
	}
	if strings.Compare(*p.TtWid, *src) != 0 {
		return false
	}
	return true
}
func (p *HTTPRequest) Field22DeepEqual(src *string) bool {

	if p.FP == src {
		return true
	} else if p.FP == nil || src == nil {
		return false
	}
	if strings.Compare(*p.FP, *src) != 0 {
		return false
	}
	return true
}

type HTTPResponse struct {
	XSendTS *string `thrift:"XSendTS,1,optional" frugal:"1,optional,string" json:"XSendTS,omitempty"`
	XRecvTS *string `thrift:"XRecvTS,2,optional" frugal:"2,optional,string" json:"XRecvTS,omitempty"`
}

func NewHTTPResponse() *HTTPResponse {
	return &HTTPResponse{}
}

func (p *HTTPResponse) InitDefault() {
}

var HTTPResponse_XSendTS_DEFAULT string

func (p *HTTPResponse) GetXSendTS() (v string) {
	if !p.IsSetXSendTS() {
		return HTTPResponse_XSendTS_DEFAULT
	}
	return *p.XSendTS
}

var HTTPResponse_XRecvTS_DEFAULT string

func (p *HTTPResponse) GetXRecvTS() (v string) {
	if !p.IsSetXRecvTS() {
		return HTTPResponse_XRecvTS_DEFAULT
	}
	return *p.XRecvTS
}
func (p *HTTPResponse) SetXSendTS(val *string) {
	p.XSendTS = val
}
func (p *HTTPResponse) SetXRecvTS(val *string) {
	p.XRecvTS = val
}

var fieldIDToName_HTTPResponse = map[int16]string{
	1: "XSendTS",
	2: "XRecvTS",
}

func (p *HTTPResponse) IsSetXSendTS() bool {
	return p.XSendTS != nil
}

func (p *HTTPResponse) IsSetXRecvTS() bool {
	return p.XRecvTS != nil
}

func (p *HTTPResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_HTTPResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *HTTPResponse) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.XSendTS = _field
	return nil
}
func (p *HTTPResponse) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.XRecvTS = _field
	return nil
}

func (p *HTTPResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("HTTPResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *HTTPResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetXSendTS() {
		if err = oprot.WriteFieldBegin("XSendTS", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.XSendTS); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *HTTPResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetXRecvTS() {
		if err = oprot.WriteFieldBegin("XRecvTS", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.XRecvTS); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *HTTPResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("HTTPResponse(%+v)", *p)

}

func (p *HTTPResponse) DeepEqual(ano *HTTPResponse) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.XSendTS) {
		return false
	}
	if !p.Field2DeepEqual(ano.XRecvTS) {
		return false
	}
	return true
}

func (p *HTTPResponse) Field1DeepEqual(src *string) bool {

	if p.XSendTS == src {
		return true
	} else if p.XSendTS == nil || src == nil {
		return false
	}
	if strings.Compare(*p.XSendTS, *src) != 0 {
		return false
	}
	return true
}
func (p *HTTPResponse) Field2DeepEqual(src *string) bool {

	if p.XRecvTS == src {
		return true
	} else if p.XRecvTS == nil || src == nil {
		return false
	}
	if strings.Compare(*p.XRecvTS, *src) != 0 {
		return false
	}
	return true
}

type SSEResponse struct {
	Event        *string       `thrift:"Event,1,optional" frugal:"1,optional,string" json:"Event,omitempty"`
	Data         *string       `thrift:"Data,2,optional" frugal:"2,optional,string" json:"Data,omitempty"`
	HTTPResponse *HTTPResponse `thrift:"HTTPResponse,201,optional" frugal:"201,optional,HTTPResponse" json:"HTTPResponse,omitempty"`
}

func NewSSEResponse() *SSEResponse {
	return &SSEResponse{}
}

func (p *SSEResponse) InitDefault() {
}

var SSEResponse_Event_DEFAULT string

func (p *SSEResponse) GetEvent() (v string) {
	if !p.IsSetEvent() {
		return SSEResponse_Event_DEFAULT
	}
	return *p.Event
}

var SSEResponse_Data_DEFAULT string

func (p *SSEResponse) GetData() (v string) {
	if !p.IsSetData() {
		return SSEResponse_Data_DEFAULT
	}
	return *p.Data
}

var SSEResponse_HTTPResponse_DEFAULT *HTTPResponse

func (p *SSEResponse) GetHTTPResponse() (v *HTTPResponse) {
	if !p.IsSetHTTPResponse() {
		return SSEResponse_HTTPResponse_DEFAULT
	}
	return p.HTTPResponse
}
func (p *SSEResponse) SetEvent(val *string) {
	p.Event = val
}
func (p *SSEResponse) SetData(val *string) {
	p.Data = val
}
func (p *SSEResponse) SetHTTPResponse(val *HTTPResponse) {
	p.HTTPResponse = val
}

var fieldIDToName_SSEResponse = map[int16]string{
	1:   "Event",
	2:   "Data",
	201: "HTTPResponse",
}

func (p *SSEResponse) IsSetEvent() bool {
	return p.Event != nil
}

func (p *SSEResponse) IsSetData() bool {
	return p.Data != nil
}

func (p *SSEResponse) IsSetHTTPResponse() bool {
	return p.HTTPResponse != nil
}

func (p *SSEResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 201:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField201(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SSEResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *SSEResponse) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Event = _field
	return nil
}
func (p *SSEResponse) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Data = _field
	return nil
}
func (p *SSEResponse) ReadField201(iprot thrift.TProtocol) error {
	_field := NewHTTPResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.HTTPResponse = _field
	return nil
}

func (p *SSEResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("SSEResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField201(oprot); err != nil {
			fieldId = 201
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SSEResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetEvent() {
		if err = oprot.WriteFieldBegin("Event", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Event); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *SSEResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetData() {
		if err = oprot.WriteFieldBegin("Data", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Data); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *SSEResponse) writeField201(oprot thrift.TProtocol) (err error) {
	if p.IsSetHTTPResponse() {
		if err = oprot.WriteFieldBegin("HTTPResponse", thrift.STRUCT, 201); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.HTTPResponse.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 201 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 201 end error: ", p), err)
}

func (p *SSEResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SSEResponse(%+v)", *p)

}

func (p *SSEResponse) DeepEqual(ano *SSEResponse) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Event) {
		return false
	}
	if !p.Field2DeepEqual(ano.Data) {
		return false
	}
	if !p.Field201DeepEqual(ano.HTTPResponse) {
		return false
	}
	return true
}

func (p *SSEResponse) Field1DeepEqual(src *string) bool {

	if p.Event == src {
		return true
	} else if p.Event == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Event, *src) != 0 {
		return false
	}
	return true
}
func (p *SSEResponse) Field2DeepEqual(src *string) bool {

	if p.Data == src {
		return true
	} else if p.Data == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Data, *src) != 0 {
		return false
	}
	return true
}
func (p *SSEResponse) Field201DeepEqual(src *HTTPResponse) bool {

	if !p.HTTPResponse.DeepEqual(src) {
		return false
	}
	return true
}

type EventError struct {
	Code    int64  `thrift:"Code,1,required" frugal:"1,required,i64" json:"code,required"`
	Error   string `thrift:"Error,2,required" frugal:"2,required,string" json:"error,required"`
	Message string `thrift:"Message,3,required" frugal:"3,required,string" json:"message,required"`
}

func NewEventError() *EventError {
	return &EventError{}
}

func (p *EventError) InitDefault() {
}

func (p *EventError) GetCode() (v int64) {
	return p.Code
}

func (p *EventError) GetError() (v string) {
	return p.Error
}

func (p *EventError) GetMessage() (v string) {
	return p.Message
}
func (p *EventError) SetCode(val int64) {
	p.Code = val
}
func (p *EventError) SetError(val string) {
	p.Error = val
}
func (p *EventError) SetMessage(val string) {
	p.Message = val
}

var fieldIDToName_EventError = map[int16]string{
	1: "Code",
	2: "Error",
	3: "Message",
}

func (p *EventError) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetCode bool = false
	var issetError bool = false
	var issetMessage bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetCode = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetError = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetMessage = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetCode {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetError {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetMessage {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_EventError[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_EventError[fieldId]))
}

func (p *EventError) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Code = _field
	return nil
}
func (p *EventError) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Error = _field
	return nil
}
func (p *EventError) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Message = _field
	return nil
}

func (p *EventError) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("EventError"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *EventError) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Code", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.Code); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *EventError) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Error", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Error); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *EventError) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Message", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Message); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *EventError) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EventError(%+v)", *p)

}

func (p *EventError) DeepEqual(ano *EventError) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Code) {
		return false
	}
	if !p.Field2DeepEqual(ano.Error) {
		return false
	}
	if !p.Field3DeepEqual(ano.Message) {
		return false
	}
	return true
}

func (p *EventError) Field1DeepEqual(src int64) bool {

	if p.Code != src {
		return false
	}
	return true
}
func (p *EventError) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Error, src) != 0 {
		return false
	}
	return true
}
func (p *EventError) Field3DeepEqual(src string) bool {

	if strings.Compare(p.Message, src) != 0 {
		return false
	}
	return true
}

type Error struct {
	Code    string `thrift:"Code,1" frugal:"1,default,string" json:"Code"`
	Message string `thrift:"Message,2" frugal:"2,default,string" json:"Message"`
	Error_  string `thrift:"Error,3" frugal:"3,default,string" json:"Error"`
}

func NewError() *Error {
	return &Error{}
}

func (p *Error) InitDefault() {
}

func (p *Error) GetCode() (v string) {
	return p.Code
}

func (p *Error) GetMessage() (v string) {
	return p.Message
}

func (p *Error) GetError() (v string) {
	return p.Error_
}
func (p *Error) SetCode(val string) {
	p.Code = val
}
func (p *Error) SetMessage(val string) {
	p.Message = val
}
func (p *Error) SetError(val string) {
	p.Error_ = val
}

var fieldIDToName_Error = map[int16]string{
	1: "Code",
	2: "Message",
	3: "Error",
}

func (p *Error) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_Error[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *Error) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Code = _field
	return nil
}
func (p *Error) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Message = _field
	return nil
}
func (p *Error) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Error_ = _field
	return nil
}

func (p *Error) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("Error"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *Error) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Code", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Code); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *Error) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Message", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Message); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *Error) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Error", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Error_); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *Error) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Error(%+v)", *p)

}
func (p *Error) Error() string {
	return p.String()
}

func (p *Error) DeepEqual(ano *Error) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Code) {
		return false
	}
	if !p.Field2DeepEqual(ano.Message) {
		return false
	}
	if !p.Field3DeepEqual(ano.Error_) {
		return false
	}
	return true
}

func (p *Error) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Code, src) != 0 {
		return false
	}
	return true
}
func (p *Error) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Message, src) != 0 {
		return false
	}
	return true
}
func (p *Error) Field3DeepEqual(src string) bool {

	if strings.Compare(p.Error_, src) != 0 {
		return false
	}
	return true
}
