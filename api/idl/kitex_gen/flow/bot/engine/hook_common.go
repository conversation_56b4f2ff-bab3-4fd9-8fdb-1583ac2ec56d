// Code generated by thriftgo (0.4.2). DO NOT EDIT.

package engine

import (
	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/flow/bot/bot_schema"
	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/flow/cloud/copilot"
	"database/sql"
	"database/sql/driver"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

const (
	MessageRoleSystem = "system"

	MessageRoleUser = "user"

	MessageRoleAssistant = "assistant"

	MessageRoleTool = "tool"

	MessageRolePlaceholder = "placeholder"

	MessageRoleFunction = "function"
)

type CompositeTypeOP int64

const (
	CompositeTypeOP_None       CompositeTypeOP = 0
	CompositeTypeOP_ReplaceAll CompositeTypeOP = 1
	CompositeTypeOP_Merge      CompositeTypeOP = 2
	CompositeTypeOP_Delete     CompositeTypeOP = 3
)

func (p CompositeTypeOP) String() string {
	switch p {
	case CompositeTypeOP_None:
		return "None"
	case CompositeTypeOP_ReplaceAll:
		return "ReplaceAll"
	case CompositeTypeOP_Merge:
		return "Merge"
	case CompositeTypeOP_Delete:
		return "Delete"
	}
	return "<UNSET>"
}

func CompositeTypeOPFromString(s string) (CompositeTypeOP, error) {
	switch s {
	case "None":
		return CompositeTypeOP_None, nil
	case "ReplaceAll":
		return CompositeTypeOP_ReplaceAll, nil
	case "Merge":
		return CompositeTypeOP_Merge, nil
	case "Delete":
		return CompositeTypeOP_Delete, nil
	}
	return CompositeTypeOP(0), fmt.Errorf("not a valid CompositeTypeOP string")
}

func CompositeTypeOPPtr(v CompositeTypeOP) *CompositeTypeOP { return &v }
func (p *CompositeTypeOP) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = CompositeTypeOP(result.Int64)
	return
}

func (p *CompositeTypeOP) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type BasicTypeOP int64

const (
	BasicTypeOP_None    BasicTypeOP = 0
	BasicTypeOP_Replace BasicTypeOP = 1
	BasicTypeOP_Delete  BasicTypeOP = 2
)

func (p BasicTypeOP) String() string {
	switch p {
	case BasicTypeOP_None:
		return "None"
	case BasicTypeOP_Replace:
		return "Replace"
	case BasicTypeOP_Delete:
		return "Delete"
	}
	return "<UNSET>"
}

func BasicTypeOPFromString(s string) (BasicTypeOP, error) {
	switch s {
	case "None":
		return BasicTypeOP_None, nil
	case "Replace":
		return BasicTypeOP_Replace, nil
	case "Delete":
		return BasicTypeOP_Delete, nil
	}
	return BasicTypeOP(0), fmt.Errorf("not a valid BasicTypeOP string")
}

func BasicTypeOPPtr(v BasicTypeOP) *BasicTypeOP { return &v }
func (p *BasicTypeOP) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = BasicTypeOP(result.Int64)
	return
}

func (p *BasicTypeOP) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type BotContext struct {
	BotId        int64                  `thrift:"bot_id,1,required" frugal:"1,required,i64" json:"bot_id"`
	AgentId      *int64                 `thrift:"agent_id,2,optional" frugal:"2,optional,i64" json:"agent_id,omitempty"`
	BotVersion   int64                  `thrift:"bot_version,3" frugal:"3,default,i64" json:"bot_version"`
	ConnectorId  int64                  `thrift:"connector_id,4" frugal:"4,default,i64" json:"connector_id"`
	ConnectorUid string                 `thrift:"connector_uid,5" frugal:"5,default,string" json:"connector_uid"`
	SceneContext map[string]string      `thrift:"scene_context,6" frugal:"6,default,map<string:string>" json:"scene_context"`
	Message      *Message               `thrift:"message,7,optional" frugal:"7,optional,Message" json:"message,omitempty"`
	ChatContext  []*Message             `thrift:"chat_context,8" frugal:"8,default,list<Message>" json:"chat_context"`
	AbBotEngine  *string                `thrift:"ab_bot_engine,9,optional" frugal:"9,optional,string" json:"ab_bot_engine,omitempty"`
	AbParam      *string                `thrift:"ab_param,11,optional" frugal:"11,optional,string" json:"ab_param,omitempty"`
	AgentSchema  *bot_schema.Agent      `thrift:"agent_schema,12" frugal:"12,default,bot_schema.Agent" json:"agent_schema"`
	ContextExt   map[string]string      `thrift:"context_ext,13" frugal:"13,default,map<string:string>" json:"context_ext"`
	AuthInfo     *copilot.ToolsAuthInfo `thrift:"auth_info,14,optional" frugal:"14,optional,copilot.ToolsAuthInfo" json:"auth_info,omitempty"`
	ResumeInfo   *copilot.ResumeInfo    `thrift:"resume_info,15,optional" frugal:"15,optional,copilot.ResumeInfo" json:"resume_info,omitempty"`
}

func NewBotContext() *BotContext {
	return &BotContext{}
}

func (p *BotContext) InitDefault() {
}

func (p *BotContext) GetBotId() (v int64) {
	return p.BotId
}

var BotContext_AgentId_DEFAULT int64

func (p *BotContext) GetAgentId() (v int64) {
	if !p.IsSetAgentId() {
		return BotContext_AgentId_DEFAULT
	}
	return *p.AgentId
}

func (p *BotContext) GetBotVersion() (v int64) {
	return p.BotVersion
}

func (p *BotContext) GetConnectorId() (v int64) {
	return p.ConnectorId
}

func (p *BotContext) GetConnectorUid() (v string) {
	return p.ConnectorUid
}

func (p *BotContext) GetSceneContext() (v map[string]string) {
	return p.SceneContext
}

var BotContext_Message_DEFAULT *Message

func (p *BotContext) GetMessage() (v *Message) {
	if !p.IsSetMessage() {
		return BotContext_Message_DEFAULT
	}
	return p.Message
}

func (p *BotContext) GetChatContext() (v []*Message) {
	return p.ChatContext
}

var BotContext_AbBotEngine_DEFAULT string

func (p *BotContext) GetAbBotEngine() (v string) {
	if !p.IsSetAbBotEngine() {
		return BotContext_AbBotEngine_DEFAULT
	}
	return *p.AbBotEngine
}

var BotContext_AbParam_DEFAULT string

func (p *BotContext) GetAbParam() (v string) {
	if !p.IsSetAbParam() {
		return BotContext_AbParam_DEFAULT
	}
	return *p.AbParam
}

var BotContext_AgentSchema_DEFAULT *bot_schema.Agent

func (p *BotContext) GetAgentSchema() (v *bot_schema.Agent) {
	if !p.IsSetAgentSchema() {
		return BotContext_AgentSchema_DEFAULT
	}
	return p.AgentSchema
}

func (p *BotContext) GetContextExt() (v map[string]string) {
	return p.ContextExt
}

var BotContext_AuthInfo_DEFAULT *copilot.ToolsAuthInfo

func (p *BotContext) GetAuthInfo() (v *copilot.ToolsAuthInfo) {
	if !p.IsSetAuthInfo() {
		return BotContext_AuthInfo_DEFAULT
	}
	return p.AuthInfo
}

var BotContext_ResumeInfo_DEFAULT *copilot.ResumeInfo

func (p *BotContext) GetResumeInfo() (v *copilot.ResumeInfo) {
	if !p.IsSetResumeInfo() {
		return BotContext_ResumeInfo_DEFAULT
	}
	return p.ResumeInfo
}
func (p *BotContext) SetBotId(val int64) {
	p.BotId = val
}
func (p *BotContext) SetAgentId(val *int64) {
	p.AgentId = val
}
func (p *BotContext) SetBotVersion(val int64) {
	p.BotVersion = val
}
func (p *BotContext) SetConnectorId(val int64) {
	p.ConnectorId = val
}
func (p *BotContext) SetConnectorUid(val string) {
	p.ConnectorUid = val
}
func (p *BotContext) SetSceneContext(val map[string]string) {
	p.SceneContext = val
}
func (p *BotContext) SetMessage(val *Message) {
	p.Message = val
}
func (p *BotContext) SetChatContext(val []*Message) {
	p.ChatContext = val
}
func (p *BotContext) SetAbBotEngine(val *string) {
	p.AbBotEngine = val
}
func (p *BotContext) SetAbParam(val *string) {
	p.AbParam = val
}
func (p *BotContext) SetAgentSchema(val *bot_schema.Agent) {
	p.AgentSchema = val
}
func (p *BotContext) SetContextExt(val map[string]string) {
	p.ContextExt = val
}
func (p *BotContext) SetAuthInfo(val *copilot.ToolsAuthInfo) {
	p.AuthInfo = val
}
func (p *BotContext) SetResumeInfo(val *copilot.ResumeInfo) {
	p.ResumeInfo = val
}

var fieldIDToName_BotContext = map[int16]string{
	1:  "bot_id",
	2:  "agent_id",
	3:  "bot_version",
	4:  "connector_id",
	5:  "connector_uid",
	6:  "scene_context",
	7:  "message",
	8:  "chat_context",
	9:  "ab_bot_engine",
	11: "ab_param",
	12: "agent_schema",
	13: "context_ext",
	14: "auth_info",
	15: "resume_info",
}

func (p *BotContext) IsSetAgentId() bool {
	return p.AgentId != nil
}

func (p *BotContext) IsSetMessage() bool {
	return p.Message != nil
}

func (p *BotContext) IsSetAbBotEngine() bool {
	return p.AbBotEngine != nil
}

func (p *BotContext) IsSetAbParam() bool {
	return p.AbParam != nil
}

func (p *BotContext) IsSetAgentSchema() bool {
	return p.AgentSchema != nil
}

func (p *BotContext) IsSetAuthInfo() bool {
	return p.AuthInfo != nil
}

func (p *BotContext) IsSetResumeInfo() bool {
	return p.ResumeInfo != nil
}

func (p *BotContext) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetBotId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetBotId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 14:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField14(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 15:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField15(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetBotId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_BotContext[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_BotContext[fieldId]))
}

func (p *BotContext) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BotId = _field
	return nil
}
func (p *BotContext) ReadField2(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.AgentId = _field
	return nil
}
func (p *BotContext) ReadField3(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BotVersion = _field
	return nil
}
func (p *BotContext) ReadField4(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ConnectorId = _field
	return nil
}
func (p *BotContext) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ConnectorUid = _field
	return nil
}
func (p *BotContext) ReadField6(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_key = v
		}

		var _val string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_val = v
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.SceneContext = _field
	return nil
}
func (p *BotContext) ReadField7(iprot thrift.TProtocol) error {
	_field := NewMessage()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Message = _field
	return nil
}
func (p *BotContext) ReadField8(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*Message, 0, size)
	values := make([]Message, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ChatContext = _field
	return nil
}
func (p *BotContext) ReadField9(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.AbBotEngine = _field
	return nil
}
func (p *BotContext) ReadField11(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.AbParam = _field
	return nil
}
func (p *BotContext) ReadField12(iprot thrift.TProtocol) error {
	_field := bot_schema.NewAgent()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.AgentSchema = _field
	return nil
}
func (p *BotContext) ReadField13(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_key = v
		}

		var _val string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_val = v
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.ContextExt = _field
	return nil
}
func (p *BotContext) ReadField14(iprot thrift.TProtocol) error {
	_field := copilot.NewToolsAuthInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.AuthInfo = _field
	return nil
}
func (p *BotContext) ReadField15(iprot thrift.TProtocol) error {
	_field := copilot.NewResumeInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.ResumeInfo = _field
	return nil
}

func (p *BotContext) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("BotContext"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
		if err = p.writeField14(oprot); err != nil {
			fieldId = 14
			goto WriteFieldError
		}
		if err = p.writeField15(oprot); err != nil {
			fieldId = 15
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *BotContext) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("bot_id", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.BotId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *BotContext) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetAgentId() {
		if err = oprot.WriteFieldBegin("agent_id", thrift.I64, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.AgentId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *BotContext) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("bot_version", thrift.I64, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.BotVersion); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *BotContext) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("connector_id", thrift.I64, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.ConnectorId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *BotContext) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("connector_uid", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ConnectorUid); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *BotContext) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("scene_context", thrift.MAP, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.SceneContext)); err != nil {
		return err
	}
	for k, v := range p.SceneContext {
		if err := oprot.WriteString(k); err != nil {
			return err
		}
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteMapEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *BotContext) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetMessage() {
		if err = oprot.WriteFieldBegin("message", thrift.STRUCT, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Message.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}
func (p *BotContext) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("chat_context", thrift.LIST, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ChatContext)); err != nil {
		return err
	}
	for _, v := range p.ChatContext {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}
func (p *BotContext) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetAbBotEngine() {
		if err = oprot.WriteFieldBegin("ab_bot_engine", thrift.STRING, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.AbBotEngine); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}
func (p *BotContext) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetAbParam() {
		if err = oprot.WriteFieldBegin("ab_param", thrift.STRING, 11); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.AbParam); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}
func (p *BotContext) writeField12(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("agent_schema", thrift.STRUCT, 12); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.AgentSchema.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}
func (p *BotContext) writeField13(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("context_ext", thrift.MAP, 13); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.ContextExt)); err != nil {
		return err
	}
	for k, v := range p.ContextExt {
		if err := oprot.WriteString(k); err != nil {
			return err
		}
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteMapEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}
func (p *BotContext) writeField14(oprot thrift.TProtocol) (err error) {
	if p.IsSetAuthInfo() {
		if err = oprot.WriteFieldBegin("auth_info", thrift.STRUCT, 14); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.AuthInfo.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 end error: ", p), err)
}
func (p *BotContext) writeField15(oprot thrift.TProtocol) (err error) {
	if p.IsSetResumeInfo() {
		if err = oprot.WriteFieldBegin("resume_info", thrift.STRUCT, 15); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.ResumeInfo.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 end error: ", p), err)
}

func (p *BotContext) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BotContext(%+v)", *p)

}

func (p *BotContext) DeepEqual(ano *BotContext) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.BotId) {
		return false
	}
	if !p.Field2DeepEqual(ano.AgentId) {
		return false
	}
	if !p.Field3DeepEqual(ano.BotVersion) {
		return false
	}
	if !p.Field4DeepEqual(ano.ConnectorId) {
		return false
	}
	if !p.Field5DeepEqual(ano.ConnectorUid) {
		return false
	}
	if !p.Field6DeepEqual(ano.SceneContext) {
		return false
	}
	if !p.Field7DeepEqual(ano.Message) {
		return false
	}
	if !p.Field8DeepEqual(ano.ChatContext) {
		return false
	}
	if !p.Field9DeepEqual(ano.AbBotEngine) {
		return false
	}
	if !p.Field11DeepEqual(ano.AbParam) {
		return false
	}
	if !p.Field12DeepEqual(ano.AgentSchema) {
		return false
	}
	if !p.Field13DeepEqual(ano.ContextExt) {
		return false
	}
	if !p.Field14DeepEqual(ano.AuthInfo) {
		return false
	}
	if !p.Field15DeepEqual(ano.ResumeInfo) {
		return false
	}
	return true
}

func (p *BotContext) Field1DeepEqual(src int64) bool {

	if p.BotId != src {
		return false
	}
	return true
}
func (p *BotContext) Field2DeepEqual(src *int64) bool {

	if p.AgentId == src {
		return true
	} else if p.AgentId == nil || src == nil {
		return false
	}
	if *p.AgentId != *src {
		return false
	}
	return true
}
func (p *BotContext) Field3DeepEqual(src int64) bool {

	if p.BotVersion != src {
		return false
	}
	return true
}
func (p *BotContext) Field4DeepEqual(src int64) bool {

	if p.ConnectorId != src {
		return false
	}
	return true
}
func (p *BotContext) Field5DeepEqual(src string) bool {

	if strings.Compare(p.ConnectorUid, src) != 0 {
		return false
	}
	return true
}
func (p *BotContext) Field6DeepEqual(src map[string]string) bool {

	if len(p.SceneContext) != len(src) {
		return false
	}
	for k, v := range p.SceneContext {
		_src := src[k]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *BotContext) Field7DeepEqual(src *Message) bool {

	if !p.Message.DeepEqual(src) {
		return false
	}
	return true
}
func (p *BotContext) Field8DeepEqual(src []*Message) bool {

	if len(p.ChatContext) != len(src) {
		return false
	}
	for i, v := range p.ChatContext {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *BotContext) Field9DeepEqual(src *string) bool {

	if p.AbBotEngine == src {
		return true
	} else if p.AbBotEngine == nil || src == nil {
		return false
	}
	if strings.Compare(*p.AbBotEngine, *src) != 0 {
		return false
	}
	return true
}
func (p *BotContext) Field11DeepEqual(src *string) bool {

	if p.AbParam == src {
		return true
	} else if p.AbParam == nil || src == nil {
		return false
	}
	if strings.Compare(*p.AbParam, *src) != 0 {
		return false
	}
	return true
}
func (p *BotContext) Field12DeepEqual(src *bot_schema.Agent) bool {

	if !p.AgentSchema.DeepEqual(src) {
		return false
	}
	return true
}
func (p *BotContext) Field13DeepEqual(src map[string]string) bool {

	if len(p.ContextExt) != len(src) {
		return false
	}
	for k, v := range p.ContextExt {
		_src := src[k]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *BotContext) Field14DeepEqual(src *copilot.ToolsAuthInfo) bool {

	if !p.AuthInfo.DeepEqual(src) {
		return false
	}
	return true
}
func (p *BotContext) Field15DeepEqual(src *copilot.ResumeInfo) bool {

	if !p.ResumeInfo.DeepEqual(src) {
		return false
	}
	return true
}

type VerboseMessage struct {
	MsgType string `thrift:"msg_type,1" frugal:"1,default,string" json:"msg_type"`
	Data    string `thrift:"data,2" frugal:"2,default,string" json:"data"`
}

func NewVerboseMessage() *VerboseMessage {
	return &VerboseMessage{}
}

func (p *VerboseMessage) InitDefault() {
}

func (p *VerboseMessage) GetMsgType() (v string) {
	return p.MsgType
}

func (p *VerboseMessage) GetData() (v string) {
	return p.Data
}
func (p *VerboseMessage) SetMsgType(val string) {
	p.MsgType = val
}
func (p *VerboseMessage) SetData(val string) {
	p.Data = val
}

var fieldIDToName_VerboseMessage = map[int16]string{
	1: "msg_type",
	2: "data",
}

func (p *VerboseMessage) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_VerboseMessage[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *VerboseMessage) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.MsgType = _field
	return nil
}
func (p *VerboseMessage) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Data = _field
	return nil
}

func (p *VerboseMessage) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("VerboseMessage"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *VerboseMessage) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("msg_type", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.MsgType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *VerboseMessage) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("data", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Data); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *VerboseMessage) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("VerboseMessage(%+v)", *p)

}

func (p *VerboseMessage) DeepEqual(ano *VerboseMessage) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.MsgType) {
		return false
	}
	if !p.Field2DeepEqual(ano.Data) {
		return false
	}
	return true
}

func (p *VerboseMessage) Field1DeepEqual(src string) bool {

	if strings.Compare(p.MsgType, src) != 0 {
		return false
	}
	return true
}
func (p *VerboseMessage) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Data, src) != 0 {
		return false
	}
	return true
}

type FunctionCall struct {
	Name      string `thrift:"name,1" frugal:"1,default,string" json:"name"`
	Arguments string `thrift:"arguments,2" frugal:"2,default,string" json:"arguments"`
}

func NewFunctionCall() *FunctionCall {
	return &FunctionCall{}
}

func (p *FunctionCall) InitDefault() {
}

func (p *FunctionCall) GetName() (v string) {
	return p.Name
}

func (p *FunctionCall) GetArguments() (v string) {
	return p.Arguments
}
func (p *FunctionCall) SetName(val string) {
	p.Name = val
}
func (p *FunctionCall) SetArguments(val string) {
	p.Arguments = val
}

var fieldIDToName_FunctionCall = map[int16]string{
	1: "name",
	2: "arguments",
}

func (p *FunctionCall) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FunctionCall[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *FunctionCall) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Name = _field
	return nil
}
func (p *FunctionCall) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Arguments = _field
	return nil
}

func (p *FunctionCall) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("FunctionCall"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *FunctionCall) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("name", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Name); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *FunctionCall) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("arguments", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Arguments); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *FunctionCall) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FunctionCall(%+v)", *p)

}

func (p *FunctionCall) DeepEqual(ano *FunctionCall) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Name) {
		return false
	}
	if !p.Field2DeepEqual(ano.Arguments) {
		return false
	}
	return true
}

func (p *FunctionCall) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Name, src) != 0 {
		return false
	}
	return true
}
func (p *FunctionCall) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Arguments, src) != 0 {
		return false
	}
	return true
}

type ToolCall struct {
	Id       string        `thrift:"id,1" frugal:"1,default,string" json:"id"`
	Type     string        `thrift:"type,2" frugal:"2,default,string" json:"type"`
	Function *FunctionCall `thrift:"function,3" frugal:"3,default,FunctionCall" json:"function"`
	UniqueId string        `thrift:"unique_id,101" frugal:"101,default,string" json:"unique_id"`
}

func NewToolCall() *ToolCall {
	return &ToolCall{}
}

func (p *ToolCall) InitDefault() {
}

func (p *ToolCall) GetId() (v string) {
	return p.Id
}

func (p *ToolCall) GetType() (v string) {
	return p.Type
}

var ToolCall_Function_DEFAULT *FunctionCall

func (p *ToolCall) GetFunction() (v *FunctionCall) {
	if !p.IsSetFunction() {
		return ToolCall_Function_DEFAULT
	}
	return p.Function
}

func (p *ToolCall) GetUniqueId() (v string) {
	return p.UniqueId
}
func (p *ToolCall) SetId(val string) {
	p.Id = val
}
func (p *ToolCall) SetType(val string) {
	p.Type = val
}
func (p *ToolCall) SetFunction(val *FunctionCall) {
	p.Function = val
}
func (p *ToolCall) SetUniqueId(val string) {
	p.UniqueId = val
}

var fieldIDToName_ToolCall = map[int16]string{
	1:   "id",
	2:   "type",
	3:   "function",
	101: "unique_id",
}

func (p *ToolCall) IsSetFunction() bool {
	return p.Function != nil
}

func (p *ToolCall) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 101:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField101(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ToolCall[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ToolCall) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Id = _field
	return nil
}
func (p *ToolCall) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Type = _field
	return nil
}
func (p *ToolCall) ReadField3(iprot thrift.TProtocol) error {
	_field := NewFunctionCall()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Function = _field
	return nil
}
func (p *ToolCall) ReadField101(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UniqueId = _field
	return nil
}

func (p *ToolCall) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ToolCall"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField101(oprot); err != nil {
			fieldId = 101
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ToolCall) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("id", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Id); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ToolCall) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("type", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Type); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ToolCall) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("function", thrift.STRUCT, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Function.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *ToolCall) writeField101(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("unique_id", thrift.STRING, 101); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.UniqueId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 101 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 101 end error: ", p), err)
}

func (p *ToolCall) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ToolCall(%+v)", *p)

}

func (p *ToolCall) DeepEqual(ano *ToolCall) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Id) {
		return false
	}
	if !p.Field2DeepEqual(ano.Type) {
		return false
	}
	if !p.Field3DeepEqual(ano.Function) {
		return false
	}
	if !p.Field101DeepEqual(ano.UniqueId) {
		return false
	}
	return true
}

func (p *ToolCall) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Id, src) != 0 {
		return false
	}
	return true
}
func (p *ToolCall) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Type, src) != 0 {
		return false
	}
	return true
}
func (p *ToolCall) Field3DeepEqual(src *FunctionCall) bool {

	if !p.Function.DeepEqual(src) {
		return false
	}
	return true
}
func (p *ToolCall) Field101DeepEqual(src string) bool {

	if strings.Compare(p.UniqueId, src) != 0 {
		return false
	}
	return true
}

type BizInfo struct {
	MessageId        *int64                    `thrift:"message_id,1,optional" frugal:"1,optional,i64" json:"message_id,omitempty"`
	ConversationId   *int64                    `thrift:"conversation_id,2,optional" frugal:"2,optional,i64" json:"conversation_id,omitempty"`
	SectionId        *int64                    `thrift:"section_id,3,optional" frugal:"3,optional,i64" json:"section_id,omitempty"`
	ConversationType *copilot.ConversationType `thrift:"conversation_type,4,optional" frugal:"4,optional,ConversationType" json:"conversation_type,omitempty"`
}

func NewBizInfo() *BizInfo {
	return &BizInfo{}
}

func (p *BizInfo) InitDefault() {
}

var BizInfo_MessageId_DEFAULT int64

func (p *BizInfo) GetMessageId() (v int64) {
	if !p.IsSetMessageId() {
		return BizInfo_MessageId_DEFAULT
	}
	return *p.MessageId
}

var BizInfo_ConversationId_DEFAULT int64

func (p *BizInfo) GetConversationId() (v int64) {
	if !p.IsSetConversationId() {
		return BizInfo_ConversationId_DEFAULT
	}
	return *p.ConversationId
}

var BizInfo_SectionId_DEFAULT int64

func (p *BizInfo) GetSectionId() (v int64) {
	if !p.IsSetSectionId() {
		return BizInfo_SectionId_DEFAULT
	}
	return *p.SectionId
}

var BizInfo_ConversationType_DEFAULT copilot.ConversationType

func (p *BizInfo) GetConversationType() (v copilot.ConversationType) {
	if !p.IsSetConversationType() {
		return BizInfo_ConversationType_DEFAULT
	}
	return *p.ConversationType
}
func (p *BizInfo) SetMessageId(val *int64) {
	p.MessageId = val
}
func (p *BizInfo) SetConversationId(val *int64) {
	p.ConversationId = val
}
func (p *BizInfo) SetSectionId(val *int64) {
	p.SectionId = val
}
func (p *BizInfo) SetConversationType(val *copilot.ConversationType) {
	p.ConversationType = val
}

var fieldIDToName_BizInfo = map[int16]string{
	1: "message_id",
	2: "conversation_id",
	3: "section_id",
	4: "conversation_type",
}

func (p *BizInfo) IsSetMessageId() bool {
	return p.MessageId != nil
}

func (p *BizInfo) IsSetConversationId() bool {
	return p.ConversationId != nil
}

func (p *BizInfo) IsSetSectionId() bool {
	return p.SectionId != nil
}

func (p *BizInfo) IsSetConversationType() bool {
	return p.ConversationType != nil
}

func (p *BizInfo) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_BizInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *BizInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.MessageId = _field
	return nil
}
func (p *BizInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ConversationId = _field
	return nil
}
func (p *BizInfo) ReadField3(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.SectionId = _field
	return nil
}
func (p *BizInfo) ReadField4(iprot thrift.TProtocol) error {

	var _field *copilot.ConversationType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := copilot.ConversationType(v)
		_field = &tmp
	}
	p.ConversationType = _field
	return nil
}

func (p *BizInfo) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("BizInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *BizInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetMessageId() {
		if err = oprot.WriteFieldBegin("message_id", thrift.I64, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.MessageId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *BizInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetConversationId() {
		if err = oprot.WriteFieldBegin("conversation_id", thrift.I64, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.ConversationId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *BizInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetSectionId() {
		if err = oprot.WriteFieldBegin("section_id", thrift.I64, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.SectionId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *BizInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetConversationType() {
		if err = oprot.WriteFieldBegin("conversation_type", thrift.I32, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.ConversationType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *BizInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BizInfo(%+v)", *p)

}

func (p *BizInfo) DeepEqual(ano *BizInfo) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.MessageId) {
		return false
	}
	if !p.Field2DeepEqual(ano.ConversationId) {
		return false
	}
	if !p.Field3DeepEqual(ano.SectionId) {
		return false
	}
	if !p.Field4DeepEqual(ano.ConversationType) {
		return false
	}
	return true
}

func (p *BizInfo) Field1DeepEqual(src *int64) bool {

	if p.MessageId == src {
		return true
	} else if p.MessageId == nil || src == nil {
		return false
	}
	if *p.MessageId != *src {
		return false
	}
	return true
}
func (p *BizInfo) Field2DeepEqual(src *int64) bool {

	if p.ConversationId == src {
		return true
	} else if p.ConversationId == nil || src == nil {
		return false
	}
	if *p.ConversationId != *src {
		return false
	}
	return true
}
func (p *BizInfo) Field3DeepEqual(src *int64) bool {

	if p.SectionId == src {
		return true
	} else if p.SectionId == nil || src == nil {
		return false
	}
	if *p.SectionId != *src {
		return false
	}
	return true
}
func (p *BizInfo) Field4DeepEqual(src *copilot.ConversationType) bool {

	if p.ConversationType == src {
		return true
	} else if p.ConversationType == nil || src == nil {
		return false
	}
	if *p.ConversationType != *src {
		return false
	}
	return true
}

type Message struct {
	Role         string                `thrift:"role,1,required" frugal:"1,required,string" json:"role"`
	Content      string                `thrift:"content,2" frugal:"2,default,string" json:"content"`
	Name         string                `thrift:"name,3" frugal:"3,default,string" json:"name"`
	ToolCalls    []*ToolCall           `thrift:"tool_calls,4" frugal:"4,default,list<ToolCall>" json:"tool_calls"`
	ToolCallId   string                `thrift:"tool_call_id,5" frugal:"5,default,string" json:"tool_call_id"`
	FunctionCall *FunctionCall         `thrift:"function_call,6,optional" frugal:"6,optional,FunctionCall" json:"function_call,omitempty"`
	Location     *copilot.LocationInfo `thrift:"location,31,optional" frugal:"31,optional,copilot.LocationInfo" json:"location,omitempty"`
	Files        []*copilot.FileInfo   `thrift:"files,32,optional" frugal:"32,optional,list<copilot.FileInfo>" json:"files,omitempty"`
	Images       []*copilot.ImageInfo  `thrift:"images,33,optional" frugal:"33,optional,list<copilot.ImageInfo>" json:"images,omitempty"`
	BizInfo      *BizInfo              `thrift:"biz_info,51" frugal:"51,default,BizInfo" json:"biz_info"`
	Ext          map[string]string     `thrift:"ext,52" frugal:"52,default,map<string:string>" json:"ext"`
	UniqueId     string                `thrift:"unique_id,101" frugal:"101,default,string" json:"unique_id"`
}

func NewMessage() *Message {
	return &Message{}
}

func (p *Message) InitDefault() {
}

func (p *Message) GetRole() (v string) {
	return p.Role
}

func (p *Message) GetContent() (v string) {
	return p.Content
}

func (p *Message) GetName() (v string) {
	return p.Name
}

func (p *Message) GetToolCalls() (v []*ToolCall) {
	return p.ToolCalls
}

func (p *Message) GetToolCallId() (v string) {
	return p.ToolCallId
}

var Message_FunctionCall_DEFAULT *FunctionCall

func (p *Message) GetFunctionCall() (v *FunctionCall) {
	if !p.IsSetFunctionCall() {
		return Message_FunctionCall_DEFAULT
	}
	return p.FunctionCall
}

var Message_Location_DEFAULT *copilot.LocationInfo

func (p *Message) GetLocation() (v *copilot.LocationInfo) {
	if !p.IsSetLocation() {
		return Message_Location_DEFAULT
	}
	return p.Location
}

var Message_Files_DEFAULT []*copilot.FileInfo

func (p *Message) GetFiles() (v []*copilot.FileInfo) {
	if !p.IsSetFiles() {
		return Message_Files_DEFAULT
	}
	return p.Files
}

var Message_Images_DEFAULT []*copilot.ImageInfo

func (p *Message) GetImages() (v []*copilot.ImageInfo) {
	if !p.IsSetImages() {
		return Message_Images_DEFAULT
	}
	return p.Images
}

var Message_BizInfo_DEFAULT *BizInfo

func (p *Message) GetBizInfo() (v *BizInfo) {
	if !p.IsSetBizInfo() {
		return Message_BizInfo_DEFAULT
	}
	return p.BizInfo
}

func (p *Message) GetExt() (v map[string]string) {
	return p.Ext
}

func (p *Message) GetUniqueId() (v string) {
	return p.UniqueId
}
func (p *Message) SetRole(val string) {
	p.Role = val
}
func (p *Message) SetContent(val string) {
	p.Content = val
}
func (p *Message) SetName(val string) {
	p.Name = val
}
func (p *Message) SetToolCalls(val []*ToolCall) {
	p.ToolCalls = val
}
func (p *Message) SetToolCallId(val string) {
	p.ToolCallId = val
}
func (p *Message) SetFunctionCall(val *FunctionCall) {
	p.FunctionCall = val
}
func (p *Message) SetLocation(val *copilot.LocationInfo) {
	p.Location = val
}
func (p *Message) SetFiles(val []*copilot.FileInfo) {
	p.Files = val
}
func (p *Message) SetImages(val []*copilot.ImageInfo) {
	p.Images = val
}
func (p *Message) SetBizInfo(val *BizInfo) {
	p.BizInfo = val
}
func (p *Message) SetExt(val map[string]string) {
	p.Ext = val
}
func (p *Message) SetUniqueId(val string) {
	p.UniqueId = val
}

var fieldIDToName_Message = map[int16]string{
	1:   "role",
	2:   "content",
	3:   "name",
	4:   "tool_calls",
	5:   "tool_call_id",
	6:   "function_call",
	31:  "location",
	32:  "files",
	33:  "images",
	51:  "biz_info",
	52:  "ext",
	101: "unique_id",
}

func (p *Message) IsSetFunctionCall() bool {
	return p.FunctionCall != nil
}

func (p *Message) IsSetLocation() bool {
	return p.Location != nil
}

func (p *Message) IsSetFiles() bool {
	return p.Files != nil
}

func (p *Message) IsSetImages() bool {
	return p.Images != nil
}

func (p *Message) IsSetBizInfo() bool {
	return p.BizInfo != nil
}

func (p *Message) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetRole bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetRole = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 31:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField31(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 32:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField32(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 33:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField33(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 51:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField51(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 52:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField52(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 101:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField101(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetRole {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_Message[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_Message[fieldId]))
}

func (p *Message) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Role = _field
	return nil
}
func (p *Message) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Content = _field
	return nil
}
func (p *Message) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Name = _field
	return nil
}
func (p *Message) ReadField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*ToolCall, 0, size)
	values := make([]ToolCall, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ToolCalls = _field
	return nil
}
func (p *Message) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ToolCallId = _field
	return nil
}
func (p *Message) ReadField6(iprot thrift.TProtocol) error {
	_field := NewFunctionCall()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.FunctionCall = _field
	return nil
}
func (p *Message) ReadField31(iprot thrift.TProtocol) error {
	_field := copilot.NewLocationInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Location = _field
	return nil
}
func (p *Message) ReadField32(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*copilot.FileInfo, 0, size)
	values := make([]copilot.FileInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Files = _field
	return nil
}
func (p *Message) ReadField33(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*copilot.ImageInfo, 0, size)
	values := make([]copilot.ImageInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Images = _field
	return nil
}
func (p *Message) ReadField51(iprot thrift.TProtocol) error {
	_field := NewBizInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BizInfo = _field
	return nil
}
func (p *Message) ReadField52(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_key = v
		}

		var _val string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_val = v
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.Ext = _field
	return nil
}
func (p *Message) ReadField101(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UniqueId = _field
	return nil
}

func (p *Message) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("Message"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField31(oprot); err != nil {
			fieldId = 31
			goto WriteFieldError
		}
		if err = p.writeField32(oprot); err != nil {
			fieldId = 32
			goto WriteFieldError
		}
		if err = p.writeField33(oprot); err != nil {
			fieldId = 33
			goto WriteFieldError
		}
		if err = p.writeField51(oprot); err != nil {
			fieldId = 51
			goto WriteFieldError
		}
		if err = p.writeField52(oprot); err != nil {
			fieldId = 52
			goto WriteFieldError
		}
		if err = p.writeField101(oprot); err != nil {
			fieldId = 101
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *Message) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("role", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Role); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *Message) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("content", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Content); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *Message) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("name", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Name); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *Message) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("tool_calls", thrift.LIST, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ToolCalls)); err != nil {
		return err
	}
	for _, v := range p.ToolCalls {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *Message) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("tool_call_id", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ToolCallId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *Message) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetFunctionCall() {
		if err = oprot.WriteFieldBegin("function_call", thrift.STRUCT, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.FunctionCall.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *Message) writeField31(oprot thrift.TProtocol) (err error) {
	if p.IsSetLocation() {
		if err = oprot.WriteFieldBegin("location", thrift.STRUCT, 31); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Location.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 31 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 31 end error: ", p), err)
}
func (p *Message) writeField32(oprot thrift.TProtocol) (err error) {
	if p.IsSetFiles() {
		if err = oprot.WriteFieldBegin("files", thrift.LIST, 32); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Files)); err != nil {
			return err
		}
		for _, v := range p.Files {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 32 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 32 end error: ", p), err)
}
func (p *Message) writeField33(oprot thrift.TProtocol) (err error) {
	if p.IsSetImages() {
		if err = oprot.WriteFieldBegin("images", thrift.LIST, 33); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Images)); err != nil {
			return err
		}
		for _, v := range p.Images {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 33 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 33 end error: ", p), err)
}
func (p *Message) writeField51(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("biz_info", thrift.STRUCT, 51); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.BizInfo.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 51 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 51 end error: ", p), err)
}
func (p *Message) writeField52(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ext", thrift.MAP, 52); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.Ext)); err != nil {
		return err
	}
	for k, v := range p.Ext {
		if err := oprot.WriteString(k); err != nil {
			return err
		}
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteMapEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 52 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 52 end error: ", p), err)
}
func (p *Message) writeField101(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("unique_id", thrift.STRING, 101); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.UniqueId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 101 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 101 end error: ", p), err)
}

func (p *Message) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Message(%+v)", *p)

}

func (p *Message) DeepEqual(ano *Message) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Role) {
		return false
	}
	if !p.Field2DeepEqual(ano.Content) {
		return false
	}
	if !p.Field3DeepEqual(ano.Name) {
		return false
	}
	if !p.Field4DeepEqual(ano.ToolCalls) {
		return false
	}
	if !p.Field5DeepEqual(ano.ToolCallId) {
		return false
	}
	if !p.Field6DeepEqual(ano.FunctionCall) {
		return false
	}
	if !p.Field31DeepEqual(ano.Location) {
		return false
	}
	if !p.Field32DeepEqual(ano.Files) {
		return false
	}
	if !p.Field33DeepEqual(ano.Images) {
		return false
	}
	if !p.Field51DeepEqual(ano.BizInfo) {
		return false
	}
	if !p.Field52DeepEqual(ano.Ext) {
		return false
	}
	if !p.Field101DeepEqual(ano.UniqueId) {
		return false
	}
	return true
}

func (p *Message) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Role, src) != 0 {
		return false
	}
	return true
}
func (p *Message) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Content, src) != 0 {
		return false
	}
	return true
}
func (p *Message) Field3DeepEqual(src string) bool {

	if strings.Compare(p.Name, src) != 0 {
		return false
	}
	return true
}
func (p *Message) Field4DeepEqual(src []*ToolCall) bool {

	if len(p.ToolCalls) != len(src) {
		return false
	}
	for i, v := range p.ToolCalls {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *Message) Field5DeepEqual(src string) bool {

	if strings.Compare(p.ToolCallId, src) != 0 {
		return false
	}
	return true
}
func (p *Message) Field6DeepEqual(src *FunctionCall) bool {

	if !p.FunctionCall.DeepEqual(src) {
		return false
	}
	return true
}
func (p *Message) Field31DeepEqual(src *copilot.LocationInfo) bool {

	if !p.Location.DeepEqual(src) {
		return false
	}
	return true
}
func (p *Message) Field32DeepEqual(src []*copilot.FileInfo) bool {

	if len(p.Files) != len(src) {
		return false
	}
	for i, v := range p.Files {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *Message) Field33DeepEqual(src []*copilot.ImageInfo) bool {

	if len(p.Images) != len(src) {
		return false
	}
	for i, v := range p.Images {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *Message) Field51DeepEqual(src *BizInfo) bool {

	if !p.BizInfo.DeepEqual(src) {
		return false
	}
	return true
}
func (p *Message) Field52DeepEqual(src map[string]string) bool {

	if len(p.Ext) != len(src) {
		return false
	}
	for k, v := range p.Ext {
		_src := src[k]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *Message) Field101DeepEqual(src string) bool {

	if strings.Compare(p.UniqueId, src) != 0 {
		return false
	}
	return true
}

type Function struct {
	Name        string `thrift:"name,1" frugal:"1,default,string" json:"name"`
	Description string `thrift:"description,2" frugal:"2,default,string" json:"description"`
	Parameters  string `thrift:"parameters,3" frugal:"3,default,string" json:"parameters"`
}

func NewFunction() *Function {
	return &Function{}
}

func (p *Function) InitDefault() {
}

func (p *Function) GetName() (v string) {
	return p.Name
}

func (p *Function) GetDescription() (v string) {
	return p.Description
}

func (p *Function) GetParameters() (v string) {
	return p.Parameters
}
func (p *Function) SetName(val string) {
	p.Name = val
}
func (p *Function) SetDescription(val string) {
	p.Description = val
}
func (p *Function) SetParameters(val string) {
	p.Parameters = val
}

var fieldIDToName_Function = map[int16]string{
	1: "name",
	2: "description",
	3: "parameters",
}

func (p *Function) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_Function[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *Function) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Name = _field
	return nil
}
func (p *Function) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Description = _field
	return nil
}
func (p *Function) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Parameters = _field
	return nil
}

func (p *Function) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("Function"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *Function) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("name", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Name); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *Function) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("description", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Description); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *Function) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("parameters", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Parameters); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *Function) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Function(%+v)", *p)

}

func (p *Function) DeepEqual(ano *Function) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Name) {
		return false
	}
	if !p.Field2DeepEqual(ano.Description) {
		return false
	}
	if !p.Field3DeepEqual(ano.Parameters) {
		return false
	}
	return true
}

func (p *Function) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Name, src) != 0 {
		return false
	}
	return true
}
func (p *Function) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Description, src) != 0 {
		return false
	}
	return true
}
func (p *Function) Field3DeepEqual(src string) bool {

	if strings.Compare(p.Parameters, src) != 0 {
		return false
	}
	return true
}

type Tool struct {
	Type     string    `thrift:"type,1" frugal:"1,default,string" json:"type"`
	Function *Function `thrift:"function,2" frugal:"2,default,Function" json:"function"`
	UniqueId string    `thrift:"unique_id,101" frugal:"101,default,string" json:"unique_id"`
}

func NewTool() *Tool {
	return &Tool{}
}

func (p *Tool) InitDefault() {
}

func (p *Tool) GetType() (v string) {
	return p.Type
}

var Tool_Function_DEFAULT *Function

func (p *Tool) GetFunction() (v *Function) {
	if !p.IsSetFunction() {
		return Tool_Function_DEFAULT
	}
	return p.Function
}

func (p *Tool) GetUniqueId() (v string) {
	return p.UniqueId
}
func (p *Tool) SetType(val string) {
	p.Type = val
}
func (p *Tool) SetFunction(val *Function) {
	p.Function = val
}
func (p *Tool) SetUniqueId(val string) {
	p.UniqueId = val
}

var fieldIDToName_Tool = map[int16]string{
	1:   "type",
	2:   "function",
	101: "unique_id",
}

func (p *Tool) IsSetFunction() bool {
	return p.Function != nil
}

func (p *Tool) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 101:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField101(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_Tool[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *Tool) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Type = _field
	return nil
}
func (p *Tool) ReadField2(iprot thrift.TProtocol) error {
	_field := NewFunction()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Function = _field
	return nil
}
func (p *Tool) ReadField101(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UniqueId = _field
	return nil
}

func (p *Tool) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("Tool"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField101(oprot); err != nil {
			fieldId = 101
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *Tool) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("type", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Type); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *Tool) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("function", thrift.STRUCT, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Function.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *Tool) writeField101(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("unique_id", thrift.STRING, 101); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.UniqueId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 101 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 101 end error: ", p), err)
}

func (p *Tool) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Tool(%+v)", *p)

}

func (p *Tool) DeepEqual(ano *Tool) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Type) {
		return false
	}
	if !p.Field2DeepEqual(ano.Function) {
		return false
	}
	if !p.Field101DeepEqual(ano.UniqueId) {
		return false
	}
	return true
}

func (p *Tool) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Type, src) != 0 {
		return false
	}
	return true
}
func (p *Tool) Field2DeepEqual(src *Function) bool {

	if !p.Function.DeepEqual(src) {
		return false
	}
	return true
}
func (p *Tool) Field101DeepEqual(src string) bool {

	if strings.Compare(p.UniqueId, src) != 0 {
		return false
	}
	return true
}

type Int64OP struct {
	Op    BasicTypeOP `thrift:"op,1" frugal:"1,default,BasicTypeOP" json:"op"`
	Value *int64      `thrift:"value,2,optional" frugal:"2,optional,i64" json:"value,omitempty"`
}

func NewInt64OP() *Int64OP {
	return &Int64OP{}
}

func (p *Int64OP) InitDefault() {
}

func (p *Int64OP) GetOp() (v BasicTypeOP) {
	return p.Op
}

var Int64OP_Value_DEFAULT int64

func (p *Int64OP) GetValue() (v int64) {
	if !p.IsSetValue() {
		return Int64OP_Value_DEFAULT
	}
	return *p.Value
}
func (p *Int64OP) SetOp(val BasicTypeOP) {
	p.Op = val
}
func (p *Int64OP) SetValue(val *int64) {
	p.Value = val
}

var fieldIDToName_Int64OP = map[int16]string{
	1: "op",
	2: "value",
}

func (p *Int64OP) IsSetValue() bool {
	return p.Value != nil
}

func (p *Int64OP) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_Int64OP[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *Int64OP) ReadField1(iprot thrift.TProtocol) error {

	var _field BasicTypeOP
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = BasicTypeOP(v)
	}
	p.Op = _field
	return nil
}
func (p *Int64OP) ReadField2(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Value = _field
	return nil
}

func (p *Int64OP) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("Int64OP"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *Int64OP) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("op", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.Op)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *Int64OP) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetValue() {
		if err = oprot.WriteFieldBegin("value", thrift.I64, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.Value); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *Int64OP) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Int64OP(%+v)", *p)

}

func (p *Int64OP) DeepEqual(ano *Int64OP) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Op) {
		return false
	}
	if !p.Field2DeepEqual(ano.Value) {
		return false
	}
	return true
}

func (p *Int64OP) Field1DeepEqual(src BasicTypeOP) bool {

	if p.Op != src {
		return false
	}
	return true
}
func (p *Int64OP) Field2DeepEqual(src *int64) bool {

	if p.Value == src {
		return true
	} else if p.Value == nil || src == nil {
		return false
	}
	if *p.Value != *src {
		return false
	}
	return true
}

type Int32OP struct {
	Op    BasicTypeOP `thrift:"op,1" frugal:"1,default,BasicTypeOP" json:"op"`
	Value *int32      `thrift:"value,2,optional" frugal:"2,optional,i32" json:"value,omitempty"`
}

func NewInt32OP() *Int32OP {
	return &Int32OP{}
}

func (p *Int32OP) InitDefault() {
}

func (p *Int32OP) GetOp() (v BasicTypeOP) {
	return p.Op
}

var Int32OP_Value_DEFAULT int32

func (p *Int32OP) GetValue() (v int32) {
	if !p.IsSetValue() {
		return Int32OP_Value_DEFAULT
	}
	return *p.Value
}
func (p *Int32OP) SetOp(val BasicTypeOP) {
	p.Op = val
}
func (p *Int32OP) SetValue(val *int32) {
	p.Value = val
}

var fieldIDToName_Int32OP = map[int16]string{
	1: "op",
	2: "value",
}

func (p *Int32OP) IsSetValue() bool {
	return p.Value != nil
}

func (p *Int32OP) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_Int32OP[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *Int32OP) ReadField1(iprot thrift.TProtocol) error {

	var _field BasicTypeOP
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = BasicTypeOP(v)
	}
	p.Op = _field
	return nil
}
func (p *Int32OP) ReadField2(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Value = _field
	return nil
}

func (p *Int32OP) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("Int32OP"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *Int32OP) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("op", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.Op)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *Int32OP) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetValue() {
		if err = oprot.WriteFieldBegin("value", thrift.I32, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.Value); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *Int32OP) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Int32OP(%+v)", *p)

}

func (p *Int32OP) DeepEqual(ano *Int32OP) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Op) {
		return false
	}
	if !p.Field2DeepEqual(ano.Value) {
		return false
	}
	return true
}

func (p *Int32OP) Field1DeepEqual(src BasicTypeOP) bool {

	if p.Op != src {
		return false
	}
	return true
}
func (p *Int32OP) Field2DeepEqual(src *int32) bool {

	if p.Value == src {
		return true
	} else if p.Value == nil || src == nil {
		return false
	}
	if *p.Value != *src {
		return false
	}
	return true
}

type DoubleOP struct {
	Op    BasicTypeOP `thrift:"op,1" frugal:"1,default,BasicTypeOP" json:"op"`
	Value *float64    `thrift:"value,2,optional" frugal:"2,optional,double" json:"value,omitempty"`
}

func NewDoubleOP() *DoubleOP {
	return &DoubleOP{}
}

func (p *DoubleOP) InitDefault() {
}

func (p *DoubleOP) GetOp() (v BasicTypeOP) {
	return p.Op
}

var DoubleOP_Value_DEFAULT float64

func (p *DoubleOP) GetValue() (v float64) {
	if !p.IsSetValue() {
		return DoubleOP_Value_DEFAULT
	}
	return *p.Value
}
func (p *DoubleOP) SetOp(val BasicTypeOP) {
	p.Op = val
}
func (p *DoubleOP) SetValue(val *float64) {
	p.Value = val
}

var fieldIDToName_DoubleOP = map[int16]string{
	1: "op",
	2: "value",
}

func (p *DoubleOP) IsSetValue() bool {
	return p.Value != nil
}

func (p *DoubleOP) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.DOUBLE {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DoubleOP[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DoubleOP) ReadField1(iprot thrift.TProtocol) error {

	var _field BasicTypeOP
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = BasicTypeOP(v)
	}
	p.Op = _field
	return nil
}
func (p *DoubleOP) ReadField2(iprot thrift.TProtocol) error {

	var _field *float64
	if v, err := iprot.ReadDouble(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Value = _field
	return nil
}

func (p *DoubleOP) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("DoubleOP"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DoubleOP) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("op", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.Op)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *DoubleOP) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetValue() {
		if err = oprot.WriteFieldBegin("value", thrift.DOUBLE, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteDouble(*p.Value); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DoubleOP) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DoubleOP(%+v)", *p)

}

func (p *DoubleOP) DeepEqual(ano *DoubleOP) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Op) {
		return false
	}
	if !p.Field2DeepEqual(ano.Value) {
		return false
	}
	return true
}

func (p *DoubleOP) Field1DeepEqual(src BasicTypeOP) bool {

	if p.Op != src {
		return false
	}
	return true
}
func (p *DoubleOP) Field2DeepEqual(src *float64) bool {

	if p.Value == src {
		return true
	} else if p.Value == nil || src == nil {
		return false
	}
	if *p.Value != *src {
		return false
	}
	return true
}

type BoolOP struct {
	Op    BasicTypeOP `thrift:"op,1" frugal:"1,default,BasicTypeOP" json:"op"`
	Value *bool       `thrift:"value,2,optional" frugal:"2,optional,bool" json:"value,omitempty"`
}

func NewBoolOP() *BoolOP {
	return &BoolOP{}
}

func (p *BoolOP) InitDefault() {
}

func (p *BoolOP) GetOp() (v BasicTypeOP) {
	return p.Op
}

var BoolOP_Value_DEFAULT bool

func (p *BoolOP) GetValue() (v bool) {
	if !p.IsSetValue() {
		return BoolOP_Value_DEFAULT
	}
	return *p.Value
}
func (p *BoolOP) SetOp(val BasicTypeOP) {
	p.Op = val
}
func (p *BoolOP) SetValue(val *bool) {
	p.Value = val
}

var fieldIDToName_BoolOP = map[int16]string{
	1: "op",
	2: "value",
}

func (p *BoolOP) IsSetValue() bool {
	return p.Value != nil
}

func (p *BoolOP) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_BoolOP[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *BoolOP) ReadField1(iprot thrift.TProtocol) error {

	var _field BasicTypeOP
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = BasicTypeOP(v)
	}
	p.Op = _field
	return nil
}
func (p *BoolOP) ReadField2(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Value = _field
	return nil
}

func (p *BoolOP) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("BoolOP"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *BoolOP) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("op", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.Op)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *BoolOP) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetValue() {
		if err = oprot.WriteFieldBegin("value", thrift.BOOL, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.Value); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *BoolOP) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BoolOP(%+v)", *p)

}

func (p *BoolOP) DeepEqual(ano *BoolOP) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Op) {
		return false
	}
	if !p.Field2DeepEqual(ano.Value) {
		return false
	}
	return true
}

func (p *BoolOP) Field1DeepEqual(src BasicTypeOP) bool {

	if p.Op != src {
		return false
	}
	return true
}
func (p *BoolOP) Field2DeepEqual(src *bool) bool {

	if p.Value == src {
		return true
	} else if p.Value == nil || src == nil {
		return false
	}
	if *p.Value != *src {
		return false
	}
	return true
}

type StrOP struct {
	Op    BasicTypeOP `thrift:"op,1" frugal:"1,default,BasicTypeOP" json:"op"`
	Value *string     `thrift:"value,2,optional" frugal:"2,optional,string" json:"value,omitempty"`
}

func NewStrOP() *StrOP {
	return &StrOP{}
}

func (p *StrOP) InitDefault() {
}

func (p *StrOP) GetOp() (v BasicTypeOP) {
	return p.Op
}

var StrOP_Value_DEFAULT string

func (p *StrOP) GetValue() (v string) {
	if !p.IsSetValue() {
		return StrOP_Value_DEFAULT
	}
	return *p.Value
}
func (p *StrOP) SetOp(val BasicTypeOP) {
	p.Op = val
}
func (p *StrOP) SetValue(val *string) {
	p.Value = val
}

var fieldIDToName_StrOP = map[int16]string{
	1: "op",
	2: "value",
}

func (p *StrOP) IsSetValue() bool {
	return p.Value != nil
}

func (p *StrOP) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_StrOP[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *StrOP) ReadField1(iprot thrift.TProtocol) error {

	var _field BasicTypeOP
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = BasicTypeOP(v)
	}
	p.Op = _field
	return nil
}
func (p *StrOP) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Value = _field
	return nil
}

func (p *StrOP) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("StrOP"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *StrOP) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("op", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.Op)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *StrOP) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetValue() {
		if err = oprot.WriteFieldBegin("value", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Value); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *StrOP) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("StrOP(%+v)", *p)

}

func (p *StrOP) DeepEqual(ano *StrOP) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Op) {
		return false
	}
	if !p.Field2DeepEqual(ano.Value) {
		return false
	}
	return true
}

func (p *StrOP) Field1DeepEqual(src BasicTypeOP) bool {

	if p.Op != src {
		return false
	}
	return true
}
func (p *StrOP) Field2DeepEqual(src *string) bool {

	if p.Value == src {
		return true
	} else if p.Value == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Value, *src) != 0 {
		return false
	}
	return true
}
