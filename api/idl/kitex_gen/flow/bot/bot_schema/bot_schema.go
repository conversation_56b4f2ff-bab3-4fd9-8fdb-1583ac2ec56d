// Code generated by thriftgo (0.4.2). DO NOT EDIT.

package bot_schema

import (
	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/base"
	"database/sql"
	"database/sql/driver"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type BotMode int64

const (
	BotMode_SingleAgent  BotMode = 0
	BotMode_MultiAgent   BotMode = 1
	BotMode_WorkflowMode BotMode = 2
)

func (p BotMode) String() string {
	switch p {
	case BotMode_SingleAgent:
		return "SingleAgent"
	case BotMode_MultiAgent:
		return "MultiAgent"
	case BotMode_WorkflowMode:
		return "WorkflowMode"
	}
	return "<UNSET>"
}

func BotModeFromString(s string) (BotMode, error) {
	switch s {
	case "SingleAgent":
		return BotMode_SingleAgent, nil
	case "MultiAgent":
		return BotMode_MultiAgent, nil
	case "WorkflowMode":
		return BotMode_WorkflowMode, nil
	}
	return BotMode(0), fmt.<PERSON><PERSON>("not a valid BotMode string")
}

func BotModePtr(v BotMode) *BotMode { return &v }
func (p *BotMode) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = BotMode(result.Int64)
	return
}

func (p *BotMode) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type BotSource int64

const (
	BotSource_Doubao BotSource = 0
	BotSource_Coze   BotSource = 1
)

func (p BotSource) String() string {
	switch p {
	case BotSource_Doubao:
		return "Doubao"
	case BotSource_Coze:
		return "Coze"
	}
	return "<UNSET>"
}

func BotSourceFromString(s string) (BotSource, error) {
	switch s {
	case "Doubao":
		return BotSource_Doubao, nil
	case "Coze":
		return BotSource_Coze, nil
	}
	return BotSource(0), fmt.Errorf("not a valid BotSource string")
}

func BotSourcePtr(v BotSource) *BotSource { return &v }
func (p *BotSource) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = BotSource(result.Int64)
	return
}

func (p *BotSource) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type AgentSessionType int64

const (
	AgentSessionType_Flow AgentSessionType = 1
	AgentSessionType_Host AgentSessionType = 2
)

func (p AgentSessionType) String() string {
	switch p {
	case AgentSessionType_Flow:
		return "Flow"
	case AgentSessionType_Host:
		return "Host"
	}
	return "<UNSET>"
}

func AgentSessionTypeFromString(s string) (AgentSessionType, error) {
	switch s {
	case "Flow":
		return AgentSessionType_Flow, nil
	case "Host":
		return AgentSessionType_Host, nil
	}
	return AgentSessionType(0), fmt.Errorf("not a valid AgentSessionType string")
}

func AgentSessionTypePtr(v AgentSessionType) *AgentSessionType { return &v }
func (p *AgentSessionType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = AgentSessionType(result.Int64)
	return
}

func (p *AgentSessionType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type AgentBacktrackMode int64

const (
	AgentBacktrackMode_Current      AgentBacktrackMode = 1
	AgentBacktrackMode_Previous     AgentBacktrackMode = 2
	AgentBacktrackMode_Start        AgentBacktrackMode = 3
	AgentBacktrackMode_MostSuitable AgentBacktrackMode = 4
)

func (p AgentBacktrackMode) String() string {
	switch p {
	case AgentBacktrackMode_Current:
		return "Current"
	case AgentBacktrackMode_Previous:
		return "Previous"
	case AgentBacktrackMode_Start:
		return "Start"
	case AgentBacktrackMode_MostSuitable:
		return "MostSuitable"
	}
	return "<UNSET>"
}

func AgentBacktrackModeFromString(s string) (AgentBacktrackMode, error) {
	switch s {
	case "Current":
		return AgentBacktrackMode_Current, nil
	case "Previous":
		return AgentBacktrackMode_Previous, nil
	case "Start":
		return AgentBacktrackMode_Start, nil
	case "MostSuitable":
		return AgentBacktrackMode_MostSuitable, nil
	}
	return AgentBacktrackMode(0), fmt.Errorf("not a valid AgentBacktrackMode string")
}

func AgentBacktrackModePtr(v AgentBacktrackMode) *AgentBacktrackMode { return &v }
func (p *AgentBacktrackMode) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = AgentBacktrackMode(result.Int64)
	return
}

func (p *AgentBacktrackMode) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type AgentRecognitionMode int64

const (
	AgentRecognitionMode_FunctionCall AgentRecognitionMode = 1
	AgentRecognitionMode_Independent  AgentRecognitionMode = 2
)

func (p AgentRecognitionMode) String() string {
	switch p {
	case AgentRecognitionMode_FunctionCall:
		return "FunctionCall"
	case AgentRecognitionMode_Independent:
		return "Independent"
	}
	return "<UNSET>"
}

func AgentRecognitionModeFromString(s string) (AgentRecognitionMode, error) {
	switch s {
	case "FunctionCall":
		return AgentRecognitionMode_FunctionCall, nil
	case "Independent":
		return AgentRecognitionMode_Independent, nil
	}
	return AgentRecognitionMode(0), fmt.Errorf("not a valid AgentRecognitionMode string")
}

func AgentRecognitionModePtr(v AgentRecognitionMode) *AgentRecognitionMode { return &v }
func (p *AgentRecognitionMode) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = AgentRecognitionMode(result.Int64)
	return
}

func (p *AgentRecognitionMode) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type IndependentTiming int64

const (
	IndependentTiming_Pre        IndependentTiming = 1
	IndependentTiming_Post       IndependentTiming = 2
	IndependentTiming_PreAndPost IndependentTiming = 3
)

func (p IndependentTiming) String() string {
	switch p {
	case IndependentTiming_Pre:
		return "Pre"
	case IndependentTiming_Post:
		return "Post"
	case IndependentTiming_PreAndPost:
		return "PreAndPost"
	}
	return "<UNSET>"
}

func IndependentTimingFromString(s string) (IndependentTiming, error) {
	switch s {
	case "Pre":
		return IndependentTiming_Pre, nil
	case "Post":
		return IndependentTiming_Post, nil
	case "PreAndPost":
		return IndependentTiming_PreAndPost, nil
	}
	return IndependentTiming(0), fmt.Errorf("not a valid IndependentTiming string")
}

func IndependentTimingPtr(v IndependentTiming) *IndependentTiming { return &v }
func (p *IndependentTiming) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = IndependentTiming(result.Int64)
	return
}

func (p *IndependentTiming) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type IndependentRecognitionModelType int64

const (
	IndependentRecognitionModelType_SLM IndependentRecognitionModelType = 0
	IndependentRecognitionModelType_LLM IndependentRecognitionModelType = 1
)

func (p IndependentRecognitionModelType) String() string {
	switch p {
	case IndependentRecognitionModelType_SLM:
		return "SLM"
	case IndependentRecognitionModelType_LLM:
		return "LLM"
	}
	return "<UNSET>"
}

func IndependentRecognitionModelTypeFromString(s string) (IndependentRecognitionModelType, error) {
	switch s {
	case "SLM":
		return IndependentRecognitionModelType_SLM, nil
	case "LLM":
		return IndependentRecognitionModelType_LLM, nil
	}
	return IndependentRecognitionModelType(0), fmt.Errorf("not a valid IndependentRecognitionModelType string")
}

func IndependentRecognitionModelTypePtr(v IndependentRecognitionModelType) *IndependentRecognitionModelType {
	return &v
}
func (p *IndependentRecognitionModelType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = IndependentRecognitionModelType(result.Int64)
	return
}

func (p *IndependentRecognitionModelType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type AgentType int64

const (
	AgentType_StartAgent  AgentType = 0
	AgentType_LLMAgent    AgentType = 1
	AgentType_TaskAgent   AgentType = 2
	AgentType_GlobalAgent AgentType = 3
	AgentType_BotAgent    AgentType = 4
)

func (p AgentType) String() string {
	switch p {
	case AgentType_StartAgent:
		return "StartAgent"
	case AgentType_LLMAgent:
		return "LLMAgent"
	case AgentType_TaskAgent:
		return "TaskAgent"
	case AgentType_GlobalAgent:
		return "GlobalAgent"
	case AgentType_BotAgent:
		return "BotAgent"
	}
	return "<UNSET>"
}

func AgentTypeFromString(s string) (AgentType, error) {
	switch s {
	case "StartAgent":
		return AgentType_StartAgent, nil
	case "LLMAgent":
		return AgentType_LLMAgent, nil
	case "TaskAgent":
		return AgentType_TaskAgent, nil
	case "GlobalAgent":
		return AgentType_GlobalAgent, nil
	case "BotAgent":
		return AgentType_BotAgent, nil
	}
	return AgentType(0), fmt.Errorf("not a valid AgentType string")
}

func AgentTypePtr(v AgentType) *AgentType { return &v }
func (p *AgentType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = AgentType(result.Int64)
	return
}

func (p *AgentType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type ContextMode int64

const (
	ContextMode_Chat          ContextMode = 0
	ContextMode_FunctionCall1 ContextMode = 1
	ContextMode_FunctionCall2 ContextMode = 2
	ContextMode_FunctionCall3 ContextMode = 3
)

func (p ContextMode) String() string {
	switch p {
	case ContextMode_Chat:
		return "Chat"
	case ContextMode_FunctionCall1:
		return "FunctionCall1"
	case ContextMode_FunctionCall2:
		return "FunctionCall2"
	case ContextMode_FunctionCall3:
		return "FunctionCall3"
	}
	return "<UNSET>"
}

func ContextModeFromString(s string) (ContextMode, error) {
	switch s {
	case "Chat":
		return ContextMode_Chat, nil
	case "FunctionCall1":
		return ContextMode_FunctionCall1, nil
	case "FunctionCall2":
		return ContextMode_FunctionCall2, nil
	case "FunctionCall3":
		return ContextMode_FunctionCall3, nil
	}
	return ContextMode(0), fmt.Errorf("not a valid ContextMode string")
}

func ContextModePtr(v ContextMode) *ContextMode { return &v }
func (p *ContextMode) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = ContextMode(result.Int64)
	return
}

func (p *ContextMode) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type ResponseFormat int64

const (
	ResponseFormat_Text     ResponseFormat = 0
	ResponseFormat_Markdown ResponseFormat = 1
	ResponseFormat_JSON     ResponseFormat = 2
)

func (p ResponseFormat) String() string {
	switch p {
	case ResponseFormat_Text:
		return "Text"
	case ResponseFormat_Markdown:
		return "Markdown"
	case ResponseFormat_JSON:
		return "JSON"
	}
	return "<UNSET>"
}

func ResponseFormatFromString(s string) (ResponseFormat, error) {
	switch s {
	case "Text":
		return ResponseFormat_Text, nil
	case "Markdown":
		return ResponseFormat_Markdown, nil
	case "JSON":
		return ResponseFormat_JSON, nil
	}
	return ResponseFormat(0), fmt.Errorf("not a valid ResponseFormat string")
}

func ResponseFormatPtr(v ResponseFormat) *ResponseFormat { return &v }
func (p *ResponseFormat) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = ResponseFormat(result.Int64)
	return
}

func (p *ResponseFormat) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type KnowledgeShowSourceMode int64

const (
	KnowledgeShowSourceMode_ReplyBottom KnowledgeShowSourceMode = 0
	KnowledgeShowSourceMode_CardList    KnowledgeShowSourceMode = 1
)

func (p KnowledgeShowSourceMode) String() string {
	switch p {
	case KnowledgeShowSourceMode_ReplyBottom:
		return "ReplyBottom"
	case KnowledgeShowSourceMode_CardList:
		return "CardList"
	}
	return "<UNSET>"
}

func KnowledgeShowSourceModeFromString(s string) (KnowledgeShowSourceMode, error) {
	switch s {
	case "ReplyBottom":
		return KnowledgeShowSourceMode_ReplyBottom, nil
	case "CardList":
		return KnowledgeShowSourceMode_CardList, nil
	}
	return KnowledgeShowSourceMode(0), fmt.Errorf("not a valid KnowledgeShowSourceMode string")
}

func KnowledgeShowSourceModePtr(v KnowledgeShowSourceMode) *KnowledgeShowSourceMode { return &v }
func (p *KnowledgeShowSourceMode) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = KnowledgeShowSourceMode(result.Int64)
	return
}

func (p *KnowledgeShowSourceMode) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type KnowledgeNoRecallReplyMode int64

const (
	KnowledgeNoRecallReplyMode_Default         KnowledgeNoRecallReplyMode = 0
	KnowledgeNoRecallReplyMode_CustomizePrompt KnowledgeNoRecallReplyMode = 1
)

func (p KnowledgeNoRecallReplyMode) String() string {
	switch p {
	case KnowledgeNoRecallReplyMode_Default:
		return "Default"
	case KnowledgeNoRecallReplyMode_CustomizePrompt:
		return "CustomizePrompt"
	}
	return "<UNSET>"
}

func KnowledgeNoRecallReplyModeFromString(s string) (KnowledgeNoRecallReplyMode, error) {
	switch s {
	case "Default":
		return KnowledgeNoRecallReplyMode_Default, nil
	case "CustomizePrompt":
		return KnowledgeNoRecallReplyMode_CustomizePrompt, nil
	}
	return KnowledgeNoRecallReplyMode(0), fmt.Errorf("not a valid KnowledgeNoRecallReplyMode string")
}

func KnowledgeNoRecallReplyModePtr(v KnowledgeNoRecallReplyMode) *KnowledgeNoRecallReplyMode {
	return &v
}
func (p *KnowledgeNoRecallReplyMode) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = KnowledgeNoRecallReplyMode(result.Int64)
	return
}

func (p *KnowledgeNoRecallReplyMode) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type KnowledgeSearchMode int64

const (
	KnowledgeSearchMode_SemanticSearch KnowledgeSearchMode = 0
	KnowledgeSearchMode_HybirdSearch   KnowledgeSearchMode = 1
	KnowledgeSearchMode_FullTextSearch KnowledgeSearchMode = 20
)

func (p KnowledgeSearchMode) String() string {
	switch p {
	case KnowledgeSearchMode_SemanticSearch:
		return "SemanticSearch"
	case KnowledgeSearchMode_HybirdSearch:
		return "HybirdSearch"
	case KnowledgeSearchMode_FullTextSearch:
		return "FullTextSearch"
	}
	return "<UNSET>"
}

func KnowledgeSearchModeFromString(s string) (KnowledgeSearchMode, error) {
	switch s {
	case "SemanticSearch":
		return KnowledgeSearchMode_SemanticSearch, nil
	case "HybirdSearch":
		return KnowledgeSearchMode_HybirdSearch, nil
	case "FullTextSearch":
		return KnowledgeSearchMode_FullTextSearch, nil
	}
	return KnowledgeSearchMode(0), fmt.Errorf("not a valid KnowledgeSearchMode string")
}

func KnowledgeSearchModePtr(v KnowledgeSearchMode) *KnowledgeSearchMode { return &v }
func (p *KnowledgeSearchMode) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = KnowledgeSearchMode(result.Int64)
	return
}

func (p *KnowledgeSearchMode) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type FieldItemType int64

const (
	FieldItemType_Text    FieldItemType = 1
	FieldItemType_Number  FieldItemType = 2
	FieldItemType_Date    FieldItemType = 3
	FieldItemType_Float   FieldItemType = 4
	FieldItemType_Boolean FieldItemType = 5
)

func (p FieldItemType) String() string {
	switch p {
	case FieldItemType_Text:
		return "Text"
	case FieldItemType_Number:
		return "Number"
	case FieldItemType_Date:
		return "Date"
	case FieldItemType_Float:
		return "Float"
	case FieldItemType_Boolean:
		return "Boolean"
	}
	return "<UNSET>"
}

func FieldItemTypeFromString(s string) (FieldItemType, error) {
	switch s {
	case "Text":
		return FieldItemType_Text, nil
	case "Number":
		return FieldItemType_Number, nil
	case "Date":
		return FieldItemType_Date, nil
	case "Float":
		return FieldItemType_Float, nil
	case "Boolean":
		return FieldItemType_Boolean, nil
	}
	return FieldItemType(0), fmt.Errorf("not a valid FieldItemType string")
}

func FieldItemTypePtr(v FieldItemType) *FieldItemType { return &v }
func (p *FieldItemType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = FieldItemType(result.Int64)
	return
}

func (p *FieldItemType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type TimeCapsuleMode int64

const (
	TimeCapsuleMode_Off TimeCapsuleMode = 0
	TimeCapsuleMode_On  TimeCapsuleMode = 1
)

func (p TimeCapsuleMode) String() string {
	switch p {
	case TimeCapsuleMode_Off:
		return "Off"
	case TimeCapsuleMode_On:
		return "On"
	}
	return "<UNSET>"
}

func TimeCapsuleModeFromString(s string) (TimeCapsuleMode, error) {
	switch s {
	case "Off":
		return TimeCapsuleMode_Off, nil
	case "On":
		return TimeCapsuleMode_On, nil
	}
	return TimeCapsuleMode(0), fmt.Errorf("not a valid TimeCapsuleMode string")
}

func TimeCapsuleModePtr(v TimeCapsuleMode) *TimeCapsuleMode { return &v }
func (p *TimeCapsuleMode) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = TimeCapsuleMode(result.Int64)
	return
}

func (p *TimeCapsuleMode) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type DisablePromptCalling int64

const (
	DisablePromptCalling_Off DisablePromptCalling = 0
	DisablePromptCalling_On  DisablePromptCalling = 1
)

func (p DisablePromptCalling) String() string {
	switch p {
	case DisablePromptCalling_Off:
		return "Off"
	case DisablePromptCalling_On:
		return "On"
	}
	return "<UNSET>"
}

func DisablePromptCallingFromString(s string) (DisablePromptCalling, error) {
	switch s {
	case "Off":
		return DisablePromptCalling_Off, nil
	case "On":
		return DisablePromptCalling_On, nil
	}
	return DisablePromptCalling(0), fmt.Errorf("not a valid DisablePromptCalling string")
}

func DisablePromptCallingPtr(v DisablePromptCalling) *DisablePromptCalling { return &v }
func (p *DisablePromptCalling) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = DisablePromptCalling(result.Int64)
	return
}

func (p *DisablePromptCalling) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type FileboxInfoMode int64

const (
	FileboxInfoMode_Off FileboxInfoMode = 0
	FileboxInfoMode_On  FileboxInfoMode = 1
)

func (p FileboxInfoMode) String() string {
	switch p {
	case FileboxInfoMode_Off:
		return "Off"
	case FileboxInfoMode_On:
		return "On"
	}
	return "<UNSET>"
}

func FileboxInfoModeFromString(s string) (FileboxInfoMode, error) {
	switch s {
	case "Off":
		return FileboxInfoMode_Off, nil
	case "On":
		return FileboxInfoMode_On, nil
	}
	return FileboxInfoMode(0), fmt.Errorf("not a valid FileboxInfoMode string")
}

func FileboxInfoModePtr(v FileboxInfoMode) *FileboxInfoMode { return &v }
func (p *FileboxInfoMode) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = FileboxInfoMode(result.Int64)
	return
}

func (p *FileboxInfoMode) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type SuggestReplyMode int64

const (
	SuggestReplyMode_System  SuggestReplyMode = 0
	SuggestReplyMode_Custom  SuggestReplyMode = 1
	SuggestReplyMode_Disable SuggestReplyMode = 2
	SuggestReplyMode_OriBot  SuggestReplyMode = 3
)

func (p SuggestReplyMode) String() string {
	switch p {
	case SuggestReplyMode_System:
		return "System"
	case SuggestReplyMode_Custom:
		return "Custom"
	case SuggestReplyMode_Disable:
		return "Disable"
	case SuggestReplyMode_OriBot:
		return "OriBot"
	}
	return "<UNSET>"
}

func SuggestReplyModeFromString(s string) (SuggestReplyMode, error) {
	switch s {
	case "System":
		return SuggestReplyMode_System, nil
	case "Custom":
		return SuggestReplyMode_Custom, nil
	case "Disable":
		return SuggestReplyMode_Disable, nil
	case "OriBot":
		return SuggestReplyMode_OriBot, nil
	}
	return SuggestReplyMode(0), fmt.Errorf("not a valid SuggestReplyMode string")
}

func SuggestReplyModePtr(v SuggestReplyMode) *SuggestReplyMode { return &v }
func (p *SuggestReplyMode) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = SuggestReplyMode(result.Int64)
	return
}

func (p *SuggestReplyMode) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type VersionType int64

const (
	VersionType_Online VersionType = 0
	VersionType_Pre    VersionType = 1
)

func (p VersionType) String() string {
	switch p {
	case VersionType_Online:
		return "Online"
	case VersionType_Pre:
		return "Pre"
	}
	return "<UNSET>"
}

func VersionTypeFromString(s string) (VersionType, error) {
	switch s {
	case "Online":
		return VersionType_Online, nil
	case "Pre":
		return VersionType_Pre, nil
	}
	return VersionType(0), fmt.Errorf("not a valid VersionType string")
}

func VersionTypePtr(v VersionType) *VersionType { return &v }
func (p *VersionType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = VersionType(result.Int64)
	return
}

func (p *VersionType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type InterveneMatchType int64

const (
	InterveneMatchType_FullText InterveneMatchType = 0
	InterveneMatchType_Regex    InterveneMatchType = 1
	InterveneMatchType_Keyword  InterveneMatchType = 2
	InterveneMatchType_Location InterveneMatchType = 3
	InterveneMatchType_BizScene InterveneMatchType = 4
)

func (p InterveneMatchType) String() string {
	switch p {
	case InterveneMatchType_FullText:
		return "FullText"
	case InterveneMatchType_Regex:
		return "Regex"
	case InterveneMatchType_Keyword:
		return "Keyword"
	case InterveneMatchType_Location:
		return "Location"
	case InterveneMatchType_BizScene:
		return "BizScene"
	}
	return "<UNSET>"
}

func InterveneMatchTypeFromString(s string) (InterveneMatchType, error) {
	switch s {
	case "FullText":
		return InterveneMatchType_FullText, nil
	case "Regex":
		return InterveneMatchType_Regex, nil
	case "Keyword":
		return InterveneMatchType_Keyword, nil
	case "Location":
		return InterveneMatchType_Location, nil
	case "BizScene":
		return InterveneMatchType_BizScene, nil
	}
	return InterveneMatchType(0), fmt.Errorf("not a valid InterveneMatchType string")
}

func InterveneMatchTypePtr(v InterveneMatchType) *InterveneMatchType { return &v }
func (p *InterveneMatchType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = InterveneMatchType(result.Int64)
	return
}

func (p *InterveneMatchType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type RelationOP int64

const (
	RelationOP_Or  RelationOP = 0
	RelationOP_And RelationOP = 1
)

func (p RelationOP) String() string {
	switch p {
	case RelationOP_Or:
		return "Or"
	case RelationOP_And:
		return "And"
	}
	return "<UNSET>"
}

func RelationOPFromString(s string) (RelationOP, error) {
	switch s {
	case "Or":
		return RelationOP_Or, nil
	case "And":
		return RelationOP_And, nil
	}
	return RelationOP(0), fmt.Errorf("not a valid RelationOP string")
}

func RelationOPPtr(v RelationOP) *RelationOP { return &v }
func (p *RelationOP) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = RelationOP(result.Int64)
	return
}

func (p *RelationOP) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type InterveneActionType int64

const (
	InterveneActionType_None            InterveneActionType = 0
	InterveneActionType_JumpToAgent     InterveneActionType = 1
	InterveneActionType_DeleteAgentJump InterveneActionType = 2
	InterveneActionType_RunPlugin       InterveneActionType = 3
	InterveneActionType_DeletePlugins   InterveneActionType = 4
	InterveneActionType_RunWorkflow     InterveneActionType = 5
	InterveneActionType_DeleteWorkflows InterveneActionType = 6
)

func (p InterveneActionType) String() string {
	switch p {
	case InterveneActionType_None:
		return "None"
	case InterveneActionType_JumpToAgent:
		return "JumpToAgent"
	case InterveneActionType_DeleteAgentJump:
		return "DeleteAgentJump"
	case InterveneActionType_RunPlugin:
		return "RunPlugin"
	case InterveneActionType_DeletePlugins:
		return "DeletePlugins"
	case InterveneActionType_RunWorkflow:
		return "RunWorkflow"
	case InterveneActionType_DeleteWorkflows:
		return "DeleteWorkflows"
	}
	return "<UNSET>"
}

func InterveneActionTypeFromString(s string) (InterveneActionType, error) {
	switch s {
	case "None":
		return InterveneActionType_None, nil
	case "JumpToAgent":
		return InterveneActionType_JumpToAgent, nil
	case "DeleteAgentJump":
		return InterveneActionType_DeleteAgentJump, nil
	case "RunPlugin":
		return InterveneActionType_RunPlugin, nil
	case "DeletePlugins":
		return InterveneActionType_DeletePlugins, nil
	case "RunWorkflow":
		return InterveneActionType_RunWorkflow, nil
	case "DeleteWorkflows":
		return InterveneActionType_DeleteWorkflows, nil
	}
	return InterveneActionType(0), fmt.Errorf("not a valid InterveneActionType string")
}

func InterveneActionTypePtr(v InterveneActionType) *InterveneActionType { return &v }
func (p *InterveneActionType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = InterveneActionType(result.Int64)
	return
}

func (p *InterveneActionType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type InterveneActionScope int64

const (
	InterveneActionScope_CurrentAgent InterveneActionScope = 0
	InterveneActionScope_Global       InterveneActionScope = 1
)

func (p InterveneActionScope) String() string {
	switch p {
	case InterveneActionScope_CurrentAgent:
		return "CurrentAgent"
	case InterveneActionScope_Global:
		return "Global"
	}
	return "<UNSET>"
}

func InterveneActionScopeFromString(s string) (InterveneActionScope, error) {
	switch s {
	case "CurrentAgent":
		return InterveneActionScope_CurrentAgent, nil
	case "Global":
		return InterveneActionScope_Global, nil
	}
	return InterveneActionScope(0), fmt.Errorf("not a valid InterveneActionScope string")
}

func InterveneActionScopePtr(v InterveneActionScope) *InterveneActionScope { return &v }
func (p *InterveneActionScope) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = InterveneActionScope(result.Int64)
	return
}

func (p *InterveneActionScope) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type Bot struct {
	BotBasic          *BotBasic        `thrift:"bot_basic,1,optional" frugal:"1,optional,BotBasic" json:"bot_basic,omitempty"`
	HookInfo          *HookInfo        `thrift:"hook_info,2,optional" frugal:"2,optional,HookInfo" json:"hook_info,omitempty"`
	Model             *Model           `thrift:"model,3,optional" frugal:"3,optional,Model" json:"model,omitempty"`
	PromptInfo        *PromptInfo      `thrift:"prompt_info,4,optional" frugal:"4,optional,PromptInfo" json:"prompt_info,omitempty"`
	AbilityList       *Ability         `thrift:"ability_list,5,optional" frugal:"5,optional,Ability" json:"ability_list,omitempty"`
	AgentList         []*Agent         `thrift:"agent_list,6,optional" frugal:"6,optional,list<Agent>" json:"agent_list,omitempty"`
	InterveneInfoList []*InterveneInfo `thrift:"intervene_info_list,7,optional" frugal:"7,optional,list<InterveneInfo>" json:"intervene_info_list,omitempty"`
}

func NewBot() *Bot {
	return &Bot{}
}

func (p *Bot) InitDefault() {
}

var Bot_BotBasic_DEFAULT *BotBasic

func (p *Bot) GetBotBasic() (v *BotBasic) {
	if !p.IsSetBotBasic() {
		return Bot_BotBasic_DEFAULT
	}
	return p.BotBasic
}

var Bot_HookInfo_DEFAULT *HookInfo

func (p *Bot) GetHookInfo() (v *HookInfo) {
	if !p.IsSetHookInfo() {
		return Bot_HookInfo_DEFAULT
	}
	return p.HookInfo
}

var Bot_Model_DEFAULT *Model

func (p *Bot) GetModel() (v *Model) {
	if !p.IsSetModel() {
		return Bot_Model_DEFAULT
	}
	return p.Model
}

var Bot_PromptInfo_DEFAULT *PromptInfo

func (p *Bot) GetPromptInfo() (v *PromptInfo) {
	if !p.IsSetPromptInfo() {
		return Bot_PromptInfo_DEFAULT
	}
	return p.PromptInfo
}

var Bot_AbilityList_DEFAULT *Ability

func (p *Bot) GetAbilityList() (v *Ability) {
	if !p.IsSetAbilityList() {
		return Bot_AbilityList_DEFAULT
	}
	return p.AbilityList
}

var Bot_AgentList_DEFAULT []*Agent

func (p *Bot) GetAgentList() (v []*Agent) {
	if !p.IsSetAgentList() {
		return Bot_AgentList_DEFAULT
	}
	return p.AgentList
}

var Bot_InterveneInfoList_DEFAULT []*InterveneInfo

func (p *Bot) GetInterveneInfoList() (v []*InterveneInfo) {
	if !p.IsSetInterveneInfoList() {
		return Bot_InterveneInfoList_DEFAULT
	}
	return p.InterveneInfoList
}
func (p *Bot) SetBotBasic(val *BotBasic) {
	p.BotBasic = val
}
func (p *Bot) SetHookInfo(val *HookInfo) {
	p.HookInfo = val
}
func (p *Bot) SetModel(val *Model) {
	p.Model = val
}
func (p *Bot) SetPromptInfo(val *PromptInfo) {
	p.PromptInfo = val
}
func (p *Bot) SetAbilityList(val *Ability) {
	p.AbilityList = val
}
func (p *Bot) SetAgentList(val []*Agent) {
	p.AgentList = val
}
func (p *Bot) SetInterveneInfoList(val []*InterveneInfo) {
	p.InterveneInfoList = val
}

var fieldIDToName_Bot = map[int16]string{
	1: "bot_basic",
	2: "hook_info",
	3: "model",
	4: "prompt_info",
	5: "ability_list",
	6: "agent_list",
	7: "intervene_info_list",
}

func (p *Bot) IsSetBotBasic() bool {
	return p.BotBasic != nil
}

func (p *Bot) IsSetHookInfo() bool {
	return p.HookInfo != nil
}

func (p *Bot) IsSetModel() bool {
	return p.Model != nil
}

func (p *Bot) IsSetPromptInfo() bool {
	return p.PromptInfo != nil
}

func (p *Bot) IsSetAbilityList() bool {
	return p.AbilityList != nil
}

func (p *Bot) IsSetAgentList() bool {
	return p.AgentList != nil
}

func (p *Bot) IsSetInterveneInfoList() bool {
	return p.InterveneInfoList != nil
}

func (p *Bot) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_Bot[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *Bot) ReadField1(iprot thrift.TProtocol) error {
	_field := NewBotBasic()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BotBasic = _field
	return nil
}
func (p *Bot) ReadField2(iprot thrift.TProtocol) error {
	_field := NewHookInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.HookInfo = _field
	return nil
}
func (p *Bot) ReadField3(iprot thrift.TProtocol) error {
	_field := NewModel()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Model = _field
	return nil
}
func (p *Bot) ReadField4(iprot thrift.TProtocol) error {
	_field := NewPromptInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.PromptInfo = _field
	return nil
}
func (p *Bot) ReadField5(iprot thrift.TProtocol) error {
	_field := NewAbility()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.AbilityList = _field
	return nil
}
func (p *Bot) ReadField6(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*Agent, 0, size)
	values := make([]Agent, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.AgentList = _field
	return nil
}
func (p *Bot) ReadField7(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*InterveneInfo, 0, size)
	values := make([]InterveneInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.InterveneInfoList = _field
	return nil
}

func (p *Bot) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("Bot"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *Bot) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetBotBasic() {
		if err = oprot.WriteFieldBegin("bot_basic", thrift.STRUCT, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.BotBasic.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *Bot) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetHookInfo() {
		if err = oprot.WriteFieldBegin("hook_info", thrift.STRUCT, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.HookInfo.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *Bot) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetModel() {
		if err = oprot.WriteFieldBegin("model", thrift.STRUCT, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Model.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *Bot) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetPromptInfo() {
		if err = oprot.WriteFieldBegin("prompt_info", thrift.STRUCT, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.PromptInfo.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *Bot) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetAbilityList() {
		if err = oprot.WriteFieldBegin("ability_list", thrift.STRUCT, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.AbilityList.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *Bot) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetAgentList() {
		if err = oprot.WriteFieldBegin("agent_list", thrift.LIST, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.AgentList)); err != nil {
			return err
		}
		for _, v := range p.AgentList {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *Bot) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetInterveneInfoList() {
		if err = oprot.WriteFieldBegin("intervene_info_list", thrift.LIST, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.InterveneInfoList)); err != nil {
			return err
		}
		for _, v := range p.InterveneInfoList {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *Bot) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Bot(%+v)", *p)

}

func (p *Bot) DeepEqual(ano *Bot) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.BotBasic) {
		return false
	}
	if !p.Field2DeepEqual(ano.HookInfo) {
		return false
	}
	if !p.Field3DeepEqual(ano.Model) {
		return false
	}
	if !p.Field4DeepEqual(ano.PromptInfo) {
		return false
	}
	if !p.Field5DeepEqual(ano.AbilityList) {
		return false
	}
	if !p.Field6DeepEqual(ano.AgentList) {
		return false
	}
	if !p.Field7DeepEqual(ano.InterveneInfoList) {
		return false
	}
	return true
}

func (p *Bot) Field1DeepEqual(src *BotBasic) bool {

	if !p.BotBasic.DeepEqual(src) {
		return false
	}
	return true
}
func (p *Bot) Field2DeepEqual(src *HookInfo) bool {

	if !p.HookInfo.DeepEqual(src) {
		return false
	}
	return true
}
func (p *Bot) Field3DeepEqual(src *Model) bool {

	if !p.Model.DeepEqual(src) {
		return false
	}
	return true
}
func (p *Bot) Field4DeepEqual(src *PromptInfo) bool {

	if !p.PromptInfo.DeepEqual(src) {
		return false
	}
	return true
}
func (p *Bot) Field5DeepEqual(src *Ability) bool {

	if !p.AbilityList.DeepEqual(src) {
		return false
	}
	return true
}
func (p *Bot) Field6DeepEqual(src []*Agent) bool {

	if len(p.AgentList) != len(src) {
		return false
	}
	for i, v := range p.AgentList {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *Bot) Field7DeepEqual(src []*InterveneInfo) bool {

	if len(p.InterveneInfoList) != len(src) {
		return false
	}
	for i, v := range p.InterveneInfoList {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type BotBasic struct {
	BotId        *int64       `thrift:"bot_id,1,optional" frugal:"1,optional,i64" json:"bot_id,omitempty"`
	Version      *string      `thrift:"version,2,optional" frugal:"2,optional,string" json:"version,omitempty"`
	ConnectorId  *int64       `thrift:"connector_id,3,optional" frugal:"3,optional,i64" json:"connector_id,omitempty"`
	Name         *string      `thrift:"name,4,optional" frugal:"4,optional,string" json:"name,omitempty"`
	Description  *string      `thrift:"description,5,optional" frugal:"5,optional,string" json:"description,omitempty"`
	IconUri      *string      `thrift:"icon_uri,6,optional" frugal:"6,optional,string" json:"icon_uri,omitempty"`
	IconUrl      *string      `thrift:"icon_url,7,optional" frugal:"7,optional,string" json:"icon_url,omitempty"`
	BotMode      *BotMode     `thrift:"bot_mode,8,optional" frugal:"8,optional,BotMode" json:"bot_mode,omitempty"`
	BotSource    *BotSource   `thrift:"bot_source,9,optional" frugal:"9,optional,BotSource" json:"bot_source,omitempty"`
	CreatorId    *int64       `thrift:"creator_id,10,optional" frugal:"10,optional,i64" json:"creator_id,omitempty"`
	CreateTimeMs *int64       `thrift:"create_time_ms,11,optional" frugal:"11,optional,i64" json:"create_time_ms,omitempty"`
	UpdateTimeMs *int64       `thrift:"update_time_ms,12,optional" frugal:"12,optional,i64" json:"update_time_ms,omitempty"`
	VersionType  *VersionType `thrift:"version_type,13,optional" frugal:"13,optional,VersionType" json:"version_type,omitempty"`
}

func NewBotBasic() *BotBasic {
	return &BotBasic{}
}

func (p *BotBasic) InitDefault() {
}

var BotBasic_BotId_DEFAULT int64

func (p *BotBasic) GetBotId() (v int64) {
	if !p.IsSetBotId() {
		return BotBasic_BotId_DEFAULT
	}
	return *p.BotId
}

var BotBasic_Version_DEFAULT string

func (p *BotBasic) GetVersion() (v string) {
	if !p.IsSetVersion() {
		return BotBasic_Version_DEFAULT
	}
	return *p.Version
}

var BotBasic_ConnectorId_DEFAULT int64

func (p *BotBasic) GetConnectorId() (v int64) {
	if !p.IsSetConnectorId() {
		return BotBasic_ConnectorId_DEFAULT
	}
	return *p.ConnectorId
}

var BotBasic_Name_DEFAULT string

func (p *BotBasic) GetName() (v string) {
	if !p.IsSetName() {
		return BotBasic_Name_DEFAULT
	}
	return *p.Name
}

var BotBasic_Description_DEFAULT string

func (p *BotBasic) GetDescription() (v string) {
	if !p.IsSetDescription() {
		return BotBasic_Description_DEFAULT
	}
	return *p.Description
}

var BotBasic_IconUri_DEFAULT string

func (p *BotBasic) GetIconUri() (v string) {
	if !p.IsSetIconUri() {
		return BotBasic_IconUri_DEFAULT
	}
	return *p.IconUri
}

var BotBasic_IconUrl_DEFAULT string

func (p *BotBasic) GetIconUrl() (v string) {
	if !p.IsSetIconUrl() {
		return BotBasic_IconUrl_DEFAULT
	}
	return *p.IconUrl
}

var BotBasic_BotMode_DEFAULT BotMode

func (p *BotBasic) GetBotMode() (v BotMode) {
	if !p.IsSetBotMode() {
		return BotBasic_BotMode_DEFAULT
	}
	return *p.BotMode
}

var BotBasic_BotSource_DEFAULT BotSource

func (p *BotBasic) GetBotSource() (v BotSource) {
	if !p.IsSetBotSource() {
		return BotBasic_BotSource_DEFAULT
	}
	return *p.BotSource
}

var BotBasic_CreatorId_DEFAULT int64

func (p *BotBasic) GetCreatorId() (v int64) {
	if !p.IsSetCreatorId() {
		return BotBasic_CreatorId_DEFAULT
	}
	return *p.CreatorId
}

var BotBasic_CreateTimeMs_DEFAULT int64

func (p *BotBasic) GetCreateTimeMs() (v int64) {
	if !p.IsSetCreateTimeMs() {
		return BotBasic_CreateTimeMs_DEFAULT
	}
	return *p.CreateTimeMs
}

var BotBasic_UpdateTimeMs_DEFAULT int64

func (p *BotBasic) GetUpdateTimeMs() (v int64) {
	if !p.IsSetUpdateTimeMs() {
		return BotBasic_UpdateTimeMs_DEFAULT
	}
	return *p.UpdateTimeMs
}

var BotBasic_VersionType_DEFAULT VersionType

func (p *BotBasic) GetVersionType() (v VersionType) {
	if !p.IsSetVersionType() {
		return BotBasic_VersionType_DEFAULT
	}
	return *p.VersionType
}
func (p *BotBasic) SetBotId(val *int64) {
	p.BotId = val
}
func (p *BotBasic) SetVersion(val *string) {
	p.Version = val
}
func (p *BotBasic) SetConnectorId(val *int64) {
	p.ConnectorId = val
}
func (p *BotBasic) SetName(val *string) {
	p.Name = val
}
func (p *BotBasic) SetDescription(val *string) {
	p.Description = val
}
func (p *BotBasic) SetIconUri(val *string) {
	p.IconUri = val
}
func (p *BotBasic) SetIconUrl(val *string) {
	p.IconUrl = val
}
func (p *BotBasic) SetBotMode(val *BotMode) {
	p.BotMode = val
}
func (p *BotBasic) SetBotSource(val *BotSource) {
	p.BotSource = val
}
func (p *BotBasic) SetCreatorId(val *int64) {
	p.CreatorId = val
}
func (p *BotBasic) SetCreateTimeMs(val *int64) {
	p.CreateTimeMs = val
}
func (p *BotBasic) SetUpdateTimeMs(val *int64) {
	p.UpdateTimeMs = val
}
func (p *BotBasic) SetVersionType(val *VersionType) {
	p.VersionType = val
}

var fieldIDToName_BotBasic = map[int16]string{
	1:  "bot_id",
	2:  "version",
	3:  "connector_id",
	4:  "name",
	5:  "description",
	6:  "icon_uri",
	7:  "icon_url",
	8:  "bot_mode",
	9:  "bot_source",
	10: "creator_id",
	11: "create_time_ms",
	12: "update_time_ms",
	13: "version_type",
}

func (p *BotBasic) IsSetBotId() bool {
	return p.BotId != nil
}

func (p *BotBasic) IsSetVersion() bool {
	return p.Version != nil
}

func (p *BotBasic) IsSetConnectorId() bool {
	return p.ConnectorId != nil
}

func (p *BotBasic) IsSetName() bool {
	return p.Name != nil
}

func (p *BotBasic) IsSetDescription() bool {
	return p.Description != nil
}

func (p *BotBasic) IsSetIconUri() bool {
	return p.IconUri != nil
}

func (p *BotBasic) IsSetIconUrl() bool {
	return p.IconUrl != nil
}

func (p *BotBasic) IsSetBotMode() bool {
	return p.BotMode != nil
}

func (p *BotBasic) IsSetBotSource() bool {
	return p.BotSource != nil
}

func (p *BotBasic) IsSetCreatorId() bool {
	return p.CreatorId != nil
}

func (p *BotBasic) IsSetCreateTimeMs() bool {
	return p.CreateTimeMs != nil
}

func (p *BotBasic) IsSetUpdateTimeMs() bool {
	return p.UpdateTimeMs != nil
}

func (p *BotBasic) IsSetVersionType() bool {
	return p.VersionType != nil
}

func (p *BotBasic) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_BotBasic[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *BotBasic) ReadField1(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.BotId = _field
	return nil
}
func (p *BotBasic) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Version = _field
	return nil
}
func (p *BotBasic) ReadField3(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ConnectorId = _field
	return nil
}
func (p *BotBasic) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Name = _field
	return nil
}
func (p *BotBasic) ReadField5(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Description = _field
	return nil
}
func (p *BotBasic) ReadField6(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.IconUri = _field
	return nil
}
func (p *BotBasic) ReadField7(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.IconUrl = _field
	return nil
}
func (p *BotBasic) ReadField8(iprot thrift.TProtocol) error {

	var _field *BotMode
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := BotMode(v)
		_field = &tmp
	}
	p.BotMode = _field
	return nil
}
func (p *BotBasic) ReadField9(iprot thrift.TProtocol) error {

	var _field *BotSource
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := BotSource(v)
		_field = &tmp
	}
	p.BotSource = _field
	return nil
}
func (p *BotBasic) ReadField10(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.CreatorId = _field
	return nil
}
func (p *BotBasic) ReadField11(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.CreateTimeMs = _field
	return nil
}
func (p *BotBasic) ReadField12(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.UpdateTimeMs = _field
	return nil
}
func (p *BotBasic) ReadField13(iprot thrift.TProtocol) error {

	var _field *VersionType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := VersionType(v)
		_field = &tmp
	}
	p.VersionType = _field
	return nil
}

func (p *BotBasic) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("BotBasic"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *BotBasic) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetBotId() {
		if err = oprot.WriteFieldBegin("bot_id", thrift.I64, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.BotId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *BotBasic) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetVersion() {
		if err = oprot.WriteFieldBegin("version", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Version); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *BotBasic) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetConnectorId() {
		if err = oprot.WriteFieldBegin("connector_id", thrift.I64, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.ConnectorId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *BotBasic) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetName() {
		if err = oprot.WriteFieldBegin("name", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Name); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *BotBasic) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetDescription() {
		if err = oprot.WriteFieldBegin("description", thrift.STRING, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Description); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *BotBasic) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetIconUri() {
		if err = oprot.WriteFieldBegin("icon_uri", thrift.STRING, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.IconUri); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *BotBasic) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetIconUrl() {
		if err = oprot.WriteFieldBegin("icon_url", thrift.STRING, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.IconUrl); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}
func (p *BotBasic) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetBotMode() {
		if err = oprot.WriteFieldBegin("bot_mode", thrift.I32, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.BotMode)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}
func (p *BotBasic) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetBotSource() {
		if err = oprot.WriteFieldBegin("bot_source", thrift.I32, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.BotSource)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}
func (p *BotBasic) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetCreatorId() {
		if err = oprot.WriteFieldBegin("creator_id", thrift.I64, 10); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.CreatorId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}
func (p *BotBasic) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetCreateTimeMs() {
		if err = oprot.WriteFieldBegin("create_time_ms", thrift.I64, 11); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.CreateTimeMs); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}
func (p *BotBasic) writeField12(oprot thrift.TProtocol) (err error) {
	if p.IsSetUpdateTimeMs() {
		if err = oprot.WriteFieldBegin("update_time_ms", thrift.I64, 12); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.UpdateTimeMs); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}
func (p *BotBasic) writeField13(oprot thrift.TProtocol) (err error) {
	if p.IsSetVersionType() {
		if err = oprot.WriteFieldBegin("version_type", thrift.I32, 13); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.VersionType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}

func (p *BotBasic) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BotBasic(%+v)", *p)

}

func (p *BotBasic) DeepEqual(ano *BotBasic) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.BotId) {
		return false
	}
	if !p.Field2DeepEqual(ano.Version) {
		return false
	}
	if !p.Field3DeepEqual(ano.ConnectorId) {
		return false
	}
	if !p.Field4DeepEqual(ano.Name) {
		return false
	}
	if !p.Field5DeepEqual(ano.Description) {
		return false
	}
	if !p.Field6DeepEqual(ano.IconUri) {
		return false
	}
	if !p.Field7DeepEqual(ano.IconUrl) {
		return false
	}
	if !p.Field8DeepEqual(ano.BotMode) {
		return false
	}
	if !p.Field9DeepEqual(ano.BotSource) {
		return false
	}
	if !p.Field10DeepEqual(ano.CreatorId) {
		return false
	}
	if !p.Field11DeepEqual(ano.CreateTimeMs) {
		return false
	}
	if !p.Field12DeepEqual(ano.UpdateTimeMs) {
		return false
	}
	if !p.Field13DeepEqual(ano.VersionType) {
		return false
	}
	return true
}

func (p *BotBasic) Field1DeepEqual(src *int64) bool {

	if p.BotId == src {
		return true
	} else if p.BotId == nil || src == nil {
		return false
	}
	if *p.BotId != *src {
		return false
	}
	return true
}
func (p *BotBasic) Field2DeepEqual(src *string) bool {

	if p.Version == src {
		return true
	} else if p.Version == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Version, *src) != 0 {
		return false
	}
	return true
}
func (p *BotBasic) Field3DeepEqual(src *int64) bool {

	if p.ConnectorId == src {
		return true
	} else if p.ConnectorId == nil || src == nil {
		return false
	}
	if *p.ConnectorId != *src {
		return false
	}
	return true
}
func (p *BotBasic) Field4DeepEqual(src *string) bool {

	if p.Name == src {
		return true
	} else if p.Name == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Name, *src) != 0 {
		return false
	}
	return true
}
func (p *BotBasic) Field5DeepEqual(src *string) bool {

	if p.Description == src {
		return true
	} else if p.Description == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Description, *src) != 0 {
		return false
	}
	return true
}
func (p *BotBasic) Field6DeepEqual(src *string) bool {

	if p.IconUri == src {
		return true
	} else if p.IconUri == nil || src == nil {
		return false
	}
	if strings.Compare(*p.IconUri, *src) != 0 {
		return false
	}
	return true
}
func (p *BotBasic) Field7DeepEqual(src *string) bool {

	if p.IconUrl == src {
		return true
	} else if p.IconUrl == nil || src == nil {
		return false
	}
	if strings.Compare(*p.IconUrl, *src) != 0 {
		return false
	}
	return true
}
func (p *BotBasic) Field8DeepEqual(src *BotMode) bool {

	if p.BotMode == src {
		return true
	} else if p.BotMode == nil || src == nil {
		return false
	}
	if *p.BotMode != *src {
		return false
	}
	return true
}
func (p *BotBasic) Field9DeepEqual(src *BotSource) bool {

	if p.BotSource == src {
		return true
	} else if p.BotSource == nil || src == nil {
		return false
	}
	if *p.BotSource != *src {
		return false
	}
	return true
}
func (p *BotBasic) Field10DeepEqual(src *int64) bool {

	if p.CreatorId == src {
		return true
	} else if p.CreatorId == nil || src == nil {
		return false
	}
	if *p.CreatorId != *src {
		return false
	}
	return true
}
func (p *BotBasic) Field11DeepEqual(src *int64) bool {

	if p.CreateTimeMs == src {
		return true
	} else if p.CreateTimeMs == nil || src == nil {
		return false
	}
	if *p.CreateTimeMs != *src {
		return false
	}
	return true
}
func (p *BotBasic) Field12DeepEqual(src *int64) bool {

	if p.UpdateTimeMs == src {
		return true
	} else if p.UpdateTimeMs == nil || src == nil {
		return false
	}
	if *p.UpdateTimeMs != *src {
		return false
	}
	return true
}
func (p *BotBasic) Field13DeepEqual(src *VersionType) bool {

	if p.VersionType == src {
		return true
	} else if p.VersionType == nil || src == nil {
		return false
	}
	if *p.VersionType != *src {
		return false
	}
	return true
}

type Ability struct {
	SwitchConf    *SwitchConf    `thrift:"switch_conf,1,optional" frugal:"1,optional,SwitchConf" json:"switch_conf,omitempty"`
	PluginList    []*PluginAPI   `thrift:"plugin_list,2,optional" frugal:"2,optional,list<PluginAPI>" json:"plugin_list,omitempty"`
	WorkflowList  []*WorkflowAPI `thrift:"workflow_list,3,optional" frugal:"3,optional,list<WorkflowAPI>" json:"workflow_list,omitempty"`
	KnowledgeList []*Knowledge   `thrift:"knowledge_list,4,optional" frugal:"4,optional,list<Knowledge>" json:"knowledge_list,omitempty"`
	VariableList  []*Variable    `thrift:"variable_list,5,optional" frugal:"5,optional,list<Variable>" json:"variable_list,omitempty"`
	DatabaseList  []*Database    `thrift:"database_list,6,optional" frugal:"6,optional,list<Database>" json:"database_list,omitempty"`
	TimeCapsule   *TimeCapsule   `thrift:"time_capsule,7,optional" frugal:"7,optional,TimeCapsule" json:"time_capsule,omitempty"`
	FileBox       *FileBox       `thrift:"file_box,8,optional" frugal:"8,optional,FileBox" json:"file_box,omitempty"`
	Trigger       *Trigger       `thrift:"trigger,9,optional" frugal:"9,optional,Trigger" json:"trigger,omitempty"`
	Applet        *Applet        `thrift:"applet,10,optional" frugal:"10,optional,Applet" json:"applet,omitempty"`
	Suggest       *Suggest       `thrift:"suggest,11,optional" frugal:"11,optional,Suggest" json:"suggest,omitempty"`
	Ext           *Ext           `thrift:"ext,12,optional" frugal:"12,optional,Ext" json:"ext,omitempty"`
	LayoutInfo    *LayoutInfo    `thrift:"layout_info,13,optional" frugal:"13,optional,LayoutInfo" json:"layout_info,omitempty"`
}

func NewAbility() *Ability {
	return &Ability{}
}

func (p *Ability) InitDefault() {
}

var Ability_SwitchConf_DEFAULT *SwitchConf

func (p *Ability) GetSwitchConf() (v *SwitchConf) {
	if !p.IsSetSwitchConf() {
		return Ability_SwitchConf_DEFAULT
	}
	return p.SwitchConf
}

var Ability_PluginList_DEFAULT []*PluginAPI

func (p *Ability) GetPluginList() (v []*PluginAPI) {
	if !p.IsSetPluginList() {
		return Ability_PluginList_DEFAULT
	}
	return p.PluginList
}

var Ability_WorkflowList_DEFAULT []*WorkflowAPI

func (p *Ability) GetWorkflowList() (v []*WorkflowAPI) {
	if !p.IsSetWorkflowList() {
		return Ability_WorkflowList_DEFAULT
	}
	return p.WorkflowList
}

var Ability_KnowledgeList_DEFAULT []*Knowledge

func (p *Ability) GetKnowledgeList() (v []*Knowledge) {
	if !p.IsSetKnowledgeList() {
		return Ability_KnowledgeList_DEFAULT
	}
	return p.KnowledgeList
}

var Ability_VariableList_DEFAULT []*Variable

func (p *Ability) GetVariableList() (v []*Variable) {
	if !p.IsSetVariableList() {
		return Ability_VariableList_DEFAULT
	}
	return p.VariableList
}

var Ability_DatabaseList_DEFAULT []*Database

func (p *Ability) GetDatabaseList() (v []*Database) {
	if !p.IsSetDatabaseList() {
		return Ability_DatabaseList_DEFAULT
	}
	return p.DatabaseList
}

var Ability_TimeCapsule_DEFAULT *TimeCapsule

func (p *Ability) GetTimeCapsule() (v *TimeCapsule) {
	if !p.IsSetTimeCapsule() {
		return Ability_TimeCapsule_DEFAULT
	}
	return p.TimeCapsule
}

var Ability_FileBox_DEFAULT *FileBox

func (p *Ability) GetFileBox() (v *FileBox) {
	if !p.IsSetFileBox() {
		return Ability_FileBox_DEFAULT
	}
	return p.FileBox
}

var Ability_Trigger_DEFAULT *Trigger

func (p *Ability) GetTrigger() (v *Trigger) {
	if !p.IsSetTrigger() {
		return Ability_Trigger_DEFAULT
	}
	return p.Trigger
}

var Ability_Applet_DEFAULT *Applet

func (p *Ability) GetApplet() (v *Applet) {
	if !p.IsSetApplet() {
		return Ability_Applet_DEFAULT
	}
	return p.Applet
}

var Ability_Suggest_DEFAULT *Suggest

func (p *Ability) GetSuggest() (v *Suggest) {
	if !p.IsSetSuggest() {
		return Ability_Suggest_DEFAULT
	}
	return p.Suggest
}

var Ability_Ext_DEFAULT *Ext

func (p *Ability) GetExt() (v *Ext) {
	if !p.IsSetExt() {
		return Ability_Ext_DEFAULT
	}
	return p.Ext
}

var Ability_LayoutInfo_DEFAULT *LayoutInfo

func (p *Ability) GetLayoutInfo() (v *LayoutInfo) {
	if !p.IsSetLayoutInfo() {
		return Ability_LayoutInfo_DEFAULT
	}
	return p.LayoutInfo
}
func (p *Ability) SetSwitchConf(val *SwitchConf) {
	p.SwitchConf = val
}
func (p *Ability) SetPluginList(val []*PluginAPI) {
	p.PluginList = val
}
func (p *Ability) SetWorkflowList(val []*WorkflowAPI) {
	p.WorkflowList = val
}
func (p *Ability) SetKnowledgeList(val []*Knowledge) {
	p.KnowledgeList = val
}
func (p *Ability) SetVariableList(val []*Variable) {
	p.VariableList = val
}
func (p *Ability) SetDatabaseList(val []*Database) {
	p.DatabaseList = val
}
func (p *Ability) SetTimeCapsule(val *TimeCapsule) {
	p.TimeCapsule = val
}
func (p *Ability) SetFileBox(val *FileBox) {
	p.FileBox = val
}
func (p *Ability) SetTrigger(val *Trigger) {
	p.Trigger = val
}
func (p *Ability) SetApplet(val *Applet) {
	p.Applet = val
}
func (p *Ability) SetSuggest(val *Suggest) {
	p.Suggest = val
}
func (p *Ability) SetExt(val *Ext) {
	p.Ext = val
}
func (p *Ability) SetLayoutInfo(val *LayoutInfo) {
	p.LayoutInfo = val
}

var fieldIDToName_Ability = map[int16]string{
	1:  "switch_conf",
	2:  "plugin_list",
	3:  "workflow_list",
	4:  "knowledge_list",
	5:  "variable_list",
	6:  "database_list",
	7:  "time_capsule",
	8:  "file_box",
	9:  "trigger",
	10: "applet",
	11: "suggest",
	12: "ext",
	13: "layout_info",
}

func (p *Ability) IsSetSwitchConf() bool {
	return p.SwitchConf != nil
}

func (p *Ability) IsSetPluginList() bool {
	return p.PluginList != nil
}

func (p *Ability) IsSetWorkflowList() bool {
	return p.WorkflowList != nil
}

func (p *Ability) IsSetKnowledgeList() bool {
	return p.KnowledgeList != nil
}

func (p *Ability) IsSetVariableList() bool {
	return p.VariableList != nil
}

func (p *Ability) IsSetDatabaseList() bool {
	return p.DatabaseList != nil
}

func (p *Ability) IsSetTimeCapsule() bool {
	return p.TimeCapsule != nil
}

func (p *Ability) IsSetFileBox() bool {
	return p.FileBox != nil
}

func (p *Ability) IsSetTrigger() bool {
	return p.Trigger != nil
}

func (p *Ability) IsSetApplet() bool {
	return p.Applet != nil
}

func (p *Ability) IsSetSuggest() bool {
	return p.Suggest != nil
}

func (p *Ability) IsSetExt() bool {
	return p.Ext != nil
}

func (p *Ability) IsSetLayoutInfo() bool {
	return p.LayoutInfo != nil
}

func (p *Ability) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_Ability[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *Ability) ReadField1(iprot thrift.TProtocol) error {
	_field := NewSwitchConf()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.SwitchConf = _field
	return nil
}
func (p *Ability) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*PluginAPI, 0, size)
	values := make([]PluginAPI, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.PluginList = _field
	return nil
}
func (p *Ability) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*WorkflowAPI, 0, size)
	values := make([]WorkflowAPI, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.WorkflowList = _field
	return nil
}
func (p *Ability) ReadField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*Knowledge, 0, size)
	values := make([]Knowledge, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.KnowledgeList = _field
	return nil
}
func (p *Ability) ReadField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*Variable, 0, size)
	values := make([]Variable, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.VariableList = _field
	return nil
}
func (p *Ability) ReadField6(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*Database, 0, size)
	values := make([]Database, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.DatabaseList = _field
	return nil
}
func (p *Ability) ReadField7(iprot thrift.TProtocol) error {
	_field := NewTimeCapsule()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.TimeCapsule = _field
	return nil
}
func (p *Ability) ReadField8(iprot thrift.TProtocol) error {
	_field := NewFileBox()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.FileBox = _field
	return nil
}
func (p *Ability) ReadField9(iprot thrift.TProtocol) error {
	_field := NewTrigger()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Trigger = _field
	return nil
}
func (p *Ability) ReadField10(iprot thrift.TProtocol) error {
	_field := NewApplet()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Applet = _field
	return nil
}
func (p *Ability) ReadField11(iprot thrift.TProtocol) error {
	_field := NewSuggest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Suggest = _field
	return nil
}
func (p *Ability) ReadField12(iprot thrift.TProtocol) error {
	_field := NewExt()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Ext = _field
	return nil
}
func (p *Ability) ReadField13(iprot thrift.TProtocol) error {
	_field := NewLayoutInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.LayoutInfo = _field
	return nil
}

func (p *Ability) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("Ability"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *Ability) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetSwitchConf() {
		if err = oprot.WriteFieldBegin("switch_conf", thrift.STRUCT, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.SwitchConf.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *Ability) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetPluginList() {
		if err = oprot.WriteFieldBegin("plugin_list", thrift.LIST, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.PluginList)); err != nil {
			return err
		}
		for _, v := range p.PluginList {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *Ability) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetWorkflowList() {
		if err = oprot.WriteFieldBegin("workflow_list", thrift.LIST, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.WorkflowList)); err != nil {
			return err
		}
		for _, v := range p.WorkflowList {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *Ability) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetKnowledgeList() {
		if err = oprot.WriteFieldBegin("knowledge_list", thrift.LIST, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.KnowledgeList)); err != nil {
			return err
		}
		for _, v := range p.KnowledgeList {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *Ability) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetVariableList() {
		if err = oprot.WriteFieldBegin("variable_list", thrift.LIST, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.VariableList)); err != nil {
			return err
		}
		for _, v := range p.VariableList {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *Ability) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetDatabaseList() {
		if err = oprot.WriteFieldBegin("database_list", thrift.LIST, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.DatabaseList)); err != nil {
			return err
		}
		for _, v := range p.DatabaseList {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *Ability) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetTimeCapsule() {
		if err = oprot.WriteFieldBegin("time_capsule", thrift.STRUCT, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.TimeCapsule.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}
func (p *Ability) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetFileBox() {
		if err = oprot.WriteFieldBegin("file_box", thrift.STRUCT, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.FileBox.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}
func (p *Ability) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetTrigger() {
		if err = oprot.WriteFieldBegin("trigger", thrift.STRUCT, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Trigger.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}
func (p *Ability) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetApplet() {
		if err = oprot.WriteFieldBegin("applet", thrift.STRUCT, 10); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Applet.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}
func (p *Ability) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuggest() {
		if err = oprot.WriteFieldBegin("suggest", thrift.STRUCT, 11); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Suggest.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}
func (p *Ability) writeField12(oprot thrift.TProtocol) (err error) {
	if p.IsSetExt() {
		if err = oprot.WriteFieldBegin("ext", thrift.STRUCT, 12); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Ext.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}
func (p *Ability) writeField13(oprot thrift.TProtocol) (err error) {
	if p.IsSetLayoutInfo() {
		if err = oprot.WriteFieldBegin("layout_info", thrift.STRUCT, 13); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.LayoutInfo.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}

func (p *Ability) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Ability(%+v)", *p)

}

func (p *Ability) DeepEqual(ano *Ability) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SwitchConf) {
		return false
	}
	if !p.Field2DeepEqual(ano.PluginList) {
		return false
	}
	if !p.Field3DeepEqual(ano.WorkflowList) {
		return false
	}
	if !p.Field4DeepEqual(ano.KnowledgeList) {
		return false
	}
	if !p.Field5DeepEqual(ano.VariableList) {
		return false
	}
	if !p.Field6DeepEqual(ano.DatabaseList) {
		return false
	}
	if !p.Field7DeepEqual(ano.TimeCapsule) {
		return false
	}
	if !p.Field8DeepEqual(ano.FileBox) {
		return false
	}
	if !p.Field9DeepEqual(ano.Trigger) {
		return false
	}
	if !p.Field10DeepEqual(ano.Applet) {
		return false
	}
	if !p.Field11DeepEqual(ano.Suggest) {
		return false
	}
	if !p.Field12DeepEqual(ano.Ext) {
		return false
	}
	if !p.Field13DeepEqual(ano.LayoutInfo) {
		return false
	}
	return true
}

func (p *Ability) Field1DeepEqual(src *SwitchConf) bool {

	if !p.SwitchConf.DeepEqual(src) {
		return false
	}
	return true
}
func (p *Ability) Field2DeepEqual(src []*PluginAPI) bool {

	if len(p.PluginList) != len(src) {
		return false
	}
	for i, v := range p.PluginList {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *Ability) Field3DeepEqual(src []*WorkflowAPI) bool {

	if len(p.WorkflowList) != len(src) {
		return false
	}
	for i, v := range p.WorkflowList {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *Ability) Field4DeepEqual(src []*Knowledge) bool {

	if len(p.KnowledgeList) != len(src) {
		return false
	}
	for i, v := range p.KnowledgeList {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *Ability) Field5DeepEqual(src []*Variable) bool {

	if len(p.VariableList) != len(src) {
		return false
	}
	for i, v := range p.VariableList {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *Ability) Field6DeepEqual(src []*Database) bool {

	if len(p.DatabaseList) != len(src) {
		return false
	}
	for i, v := range p.DatabaseList {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *Ability) Field7DeepEqual(src *TimeCapsule) bool {

	if !p.TimeCapsule.DeepEqual(src) {
		return false
	}
	return true
}
func (p *Ability) Field8DeepEqual(src *FileBox) bool {

	if !p.FileBox.DeepEqual(src) {
		return false
	}
	return true
}
func (p *Ability) Field9DeepEqual(src *Trigger) bool {

	if !p.Trigger.DeepEqual(src) {
		return false
	}
	return true
}
func (p *Ability) Field10DeepEqual(src *Applet) bool {

	if !p.Applet.DeepEqual(src) {
		return false
	}
	return true
}
func (p *Ability) Field11DeepEqual(src *Suggest) bool {

	if !p.Suggest.DeepEqual(src) {
		return false
	}
	return true
}
func (p *Ability) Field12DeepEqual(src *Ext) bool {

	if !p.Ext.DeepEqual(src) {
		return false
	}
	return true
}
func (p *Ability) Field13DeepEqual(src *LayoutInfo) bool {

	if !p.LayoutInfo.DeepEqual(src) {
		return false
	}
	return true
}

type Ext struct {
	CardId []int64 `thrift:"card_id,1,optional" frugal:"1,optional,list<i64>" json:"card_id,omitempty"`
}

func NewExt() *Ext {
	return &Ext{}
}

func (p *Ext) InitDefault() {
}

var Ext_CardId_DEFAULT []int64

func (p *Ext) GetCardId() (v []int64) {
	if !p.IsSetCardId() {
		return Ext_CardId_DEFAULT
	}
	return p.CardId
}
func (p *Ext) SetCardId(val []int64) {
	p.CardId = val
}

var fieldIDToName_Ext = map[int16]string{
	1: "card_id",
}

func (p *Ext) IsSetCardId() bool {
	return p.CardId != nil
}

func (p *Ext) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_Ext[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *Ext) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]int64, 0, size)
	for i := 0; i < size; i++ {

		var _elem int64
		if v, err := iprot.ReadI64(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.CardId = _field
	return nil
}

func (p *Ext) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("Ext"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *Ext) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetCardId() {
		if err = oprot.WriteFieldBegin("card_id", thrift.LIST, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.I64, len(p.CardId)); err != nil {
			return err
		}
		for _, v := range p.CardId {
			if err := oprot.WriteI64(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *Ext) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Ext(%+v)", *p)

}

func (p *Ext) DeepEqual(ano *Ext) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.CardId) {
		return false
	}
	return true
}

func (p *Ext) Field1DeepEqual(src []int64) bool {

	if len(p.CardId) != len(src) {
		return false
	}
	for i, v := range p.CardId {
		_src := src[i]
		if v != _src {
			return false
		}
	}
	return true
}

type HookItem struct {
	Uri         *string  `thrift:"uri,1,optional" frugal:"1,optional,string" json:"uri,omitempty"`
	FilterRules []string `thrift:"filter_rules,2,optional" frugal:"2,optional,list<string>" json:"filter_rules,omitempty"`
	StrongDep   *bool    `thrift:"strong_dep,3,optional" frugal:"3,optional,bool" json:"strong_dep,omitempty"`
	TimeoutMs   *int64   `thrift:"timeout_ms,4,optional" frugal:"4,optional,i64" json:"timeout_ms,omitempty"`
}

func NewHookItem() *HookItem {
	return &HookItem{}
}

func (p *HookItem) InitDefault() {
}

var HookItem_Uri_DEFAULT string

func (p *HookItem) GetUri() (v string) {
	if !p.IsSetUri() {
		return HookItem_Uri_DEFAULT
	}
	return *p.Uri
}

var HookItem_FilterRules_DEFAULT []string

func (p *HookItem) GetFilterRules() (v []string) {
	if !p.IsSetFilterRules() {
		return HookItem_FilterRules_DEFAULT
	}
	return p.FilterRules
}

var HookItem_StrongDep_DEFAULT bool

func (p *HookItem) GetStrongDep() (v bool) {
	if !p.IsSetStrongDep() {
		return HookItem_StrongDep_DEFAULT
	}
	return *p.StrongDep
}

var HookItem_TimeoutMs_DEFAULT int64

func (p *HookItem) GetTimeoutMs() (v int64) {
	if !p.IsSetTimeoutMs() {
		return HookItem_TimeoutMs_DEFAULT
	}
	return *p.TimeoutMs
}
func (p *HookItem) SetUri(val *string) {
	p.Uri = val
}
func (p *HookItem) SetFilterRules(val []string) {
	p.FilterRules = val
}
func (p *HookItem) SetStrongDep(val *bool) {
	p.StrongDep = val
}
func (p *HookItem) SetTimeoutMs(val *int64) {
	p.TimeoutMs = val
}

var fieldIDToName_HookItem = map[int16]string{
	1: "uri",
	2: "filter_rules",
	3: "strong_dep",
	4: "timeout_ms",
}

func (p *HookItem) IsSetUri() bool {
	return p.Uri != nil
}

func (p *HookItem) IsSetFilterRules() bool {
	return p.FilterRules != nil
}

func (p *HookItem) IsSetStrongDep() bool {
	return p.StrongDep != nil
}

func (p *HookItem) IsSetTimeoutMs() bool {
	return p.TimeoutMs != nil
}

func (p *HookItem) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_HookItem[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *HookItem) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Uri = _field
	return nil
}
func (p *HookItem) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.FilterRules = _field
	return nil
}
func (p *HookItem) ReadField3(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.StrongDep = _field
	return nil
}
func (p *HookItem) ReadField4(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.TimeoutMs = _field
	return nil
}

func (p *HookItem) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("HookItem"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *HookItem) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetUri() {
		if err = oprot.WriteFieldBegin("uri", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Uri); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *HookItem) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetFilterRules() {
		if err = oprot.WriteFieldBegin("filter_rules", thrift.LIST, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.FilterRules)); err != nil {
			return err
		}
		for _, v := range p.FilterRules {
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *HookItem) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetStrongDep() {
		if err = oprot.WriteFieldBegin("strong_dep", thrift.BOOL, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.StrongDep); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *HookItem) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetTimeoutMs() {
		if err = oprot.WriteFieldBegin("timeout_ms", thrift.I64, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.TimeoutMs); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *HookItem) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("HookItem(%+v)", *p)

}

func (p *HookItem) DeepEqual(ano *HookItem) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Uri) {
		return false
	}
	if !p.Field2DeepEqual(ano.FilterRules) {
		return false
	}
	if !p.Field3DeepEqual(ano.StrongDep) {
		return false
	}
	if !p.Field4DeepEqual(ano.TimeoutMs) {
		return false
	}
	return true
}

func (p *HookItem) Field1DeepEqual(src *string) bool {

	if p.Uri == src {
		return true
	} else if p.Uri == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Uri, *src) != 0 {
		return false
	}
	return true
}
func (p *HookItem) Field2DeepEqual(src []string) bool {

	if len(p.FilterRules) != len(src) {
		return false
	}
	for i, v := range p.FilterRules {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *HookItem) Field3DeepEqual(src *bool) bool {

	if p.StrongDep == src {
		return true
	} else if p.StrongDep == nil || src == nil {
		return false
	}
	if *p.StrongDep != *src {
		return false
	}
	return true
}
func (p *HookItem) Field4DeepEqual(src *int64) bool {

	if p.TimeoutMs == src {
		return true
	} else if p.TimeoutMs == nil || src == nil {
		return false
	}
	if *p.TimeoutMs != *src {
		return false
	}
	return true
}

type Agent struct {
	AgentBasic        *AgentBasic      `thrift:"agent_basic,1,optional" frugal:"1,optional,AgentBasic" json:"agent_basic,omitempty"`
	HookInfo          *HookInfo        `thrift:"hook_info,2,optional" frugal:"2,optional,HookInfo" json:"hook_info,omitempty"`
	Model             *Model           `thrift:"model,3,optional" frugal:"3,optional,Model" json:"model,omitempty"`
	PromptInfo        *PromptInfo      `thrift:"prompt_info,4,optional" frugal:"4,optional,PromptInfo" json:"prompt_info,omitempty"`
	Ability           *Ability         `thrift:"ability,5,optional" frugal:"5,optional,Ability" json:"ability,omitempty"`
	JumpConfig        *AgentJumpConfig `thrift:"jump_config,6,optional" frugal:"6,optional,AgentJumpConfig" json:"jump_config,omitempty"`
	InterveneInfoList []*InterveneInfo `thrift:"intervene_info_list,7,optional" frugal:"7,optional,list<InterveneInfo>" json:"intervene_info_list,omitempty"`
}

func NewAgent() *Agent {
	return &Agent{}
}

func (p *Agent) InitDefault() {
}

var Agent_AgentBasic_DEFAULT *AgentBasic

func (p *Agent) GetAgentBasic() (v *AgentBasic) {
	if !p.IsSetAgentBasic() {
		return Agent_AgentBasic_DEFAULT
	}
	return p.AgentBasic
}

var Agent_HookInfo_DEFAULT *HookInfo

func (p *Agent) GetHookInfo() (v *HookInfo) {
	if !p.IsSetHookInfo() {
		return Agent_HookInfo_DEFAULT
	}
	return p.HookInfo
}

var Agent_Model_DEFAULT *Model

func (p *Agent) GetModel() (v *Model) {
	if !p.IsSetModel() {
		return Agent_Model_DEFAULT
	}
	return p.Model
}

var Agent_PromptInfo_DEFAULT *PromptInfo

func (p *Agent) GetPromptInfo() (v *PromptInfo) {
	if !p.IsSetPromptInfo() {
		return Agent_PromptInfo_DEFAULT
	}
	return p.PromptInfo
}

var Agent_Ability_DEFAULT *Ability

func (p *Agent) GetAbility() (v *Ability) {
	if !p.IsSetAbility() {
		return Agent_Ability_DEFAULT
	}
	return p.Ability
}

var Agent_JumpConfig_DEFAULT *AgentJumpConfig

func (p *Agent) GetJumpConfig() (v *AgentJumpConfig) {
	if !p.IsSetJumpConfig() {
		return Agent_JumpConfig_DEFAULT
	}
	return p.JumpConfig
}

var Agent_InterveneInfoList_DEFAULT []*InterveneInfo

func (p *Agent) GetInterveneInfoList() (v []*InterveneInfo) {
	if !p.IsSetInterveneInfoList() {
		return Agent_InterveneInfoList_DEFAULT
	}
	return p.InterveneInfoList
}
func (p *Agent) SetAgentBasic(val *AgentBasic) {
	p.AgentBasic = val
}
func (p *Agent) SetHookInfo(val *HookInfo) {
	p.HookInfo = val
}
func (p *Agent) SetModel(val *Model) {
	p.Model = val
}
func (p *Agent) SetPromptInfo(val *PromptInfo) {
	p.PromptInfo = val
}
func (p *Agent) SetAbility(val *Ability) {
	p.Ability = val
}
func (p *Agent) SetJumpConfig(val *AgentJumpConfig) {
	p.JumpConfig = val
}
func (p *Agent) SetInterveneInfoList(val []*InterveneInfo) {
	p.InterveneInfoList = val
}

var fieldIDToName_Agent = map[int16]string{
	1: "agent_basic",
	2: "hook_info",
	3: "model",
	4: "prompt_info",
	5: "ability",
	6: "jump_config",
	7: "intervene_info_list",
}

func (p *Agent) IsSetAgentBasic() bool {
	return p.AgentBasic != nil
}

func (p *Agent) IsSetHookInfo() bool {
	return p.HookInfo != nil
}

func (p *Agent) IsSetModel() bool {
	return p.Model != nil
}

func (p *Agent) IsSetPromptInfo() bool {
	return p.PromptInfo != nil
}

func (p *Agent) IsSetAbility() bool {
	return p.Ability != nil
}

func (p *Agent) IsSetJumpConfig() bool {
	return p.JumpConfig != nil
}

func (p *Agent) IsSetInterveneInfoList() bool {
	return p.InterveneInfoList != nil
}

func (p *Agent) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_Agent[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *Agent) ReadField1(iprot thrift.TProtocol) error {
	_field := NewAgentBasic()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.AgentBasic = _field
	return nil
}
func (p *Agent) ReadField2(iprot thrift.TProtocol) error {
	_field := NewHookInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.HookInfo = _field
	return nil
}
func (p *Agent) ReadField3(iprot thrift.TProtocol) error {
	_field := NewModel()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Model = _field
	return nil
}
func (p *Agent) ReadField4(iprot thrift.TProtocol) error {
	_field := NewPromptInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.PromptInfo = _field
	return nil
}
func (p *Agent) ReadField5(iprot thrift.TProtocol) error {
	_field := NewAbility()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Ability = _field
	return nil
}
func (p *Agent) ReadField6(iprot thrift.TProtocol) error {
	_field := NewAgentJumpConfig()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.JumpConfig = _field
	return nil
}
func (p *Agent) ReadField7(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*InterveneInfo, 0, size)
	values := make([]InterveneInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.InterveneInfoList = _field
	return nil
}

func (p *Agent) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("Agent"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *Agent) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetAgentBasic() {
		if err = oprot.WriteFieldBegin("agent_basic", thrift.STRUCT, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.AgentBasic.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *Agent) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetHookInfo() {
		if err = oprot.WriteFieldBegin("hook_info", thrift.STRUCT, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.HookInfo.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *Agent) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetModel() {
		if err = oprot.WriteFieldBegin("model", thrift.STRUCT, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Model.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *Agent) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetPromptInfo() {
		if err = oprot.WriteFieldBegin("prompt_info", thrift.STRUCT, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.PromptInfo.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *Agent) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetAbility() {
		if err = oprot.WriteFieldBegin("ability", thrift.STRUCT, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Ability.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *Agent) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetJumpConfig() {
		if err = oprot.WriteFieldBegin("jump_config", thrift.STRUCT, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.JumpConfig.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *Agent) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetInterveneInfoList() {
		if err = oprot.WriteFieldBegin("intervene_info_list", thrift.LIST, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.InterveneInfoList)); err != nil {
			return err
		}
		for _, v := range p.InterveneInfoList {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *Agent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Agent(%+v)", *p)

}

func (p *Agent) DeepEqual(ano *Agent) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.AgentBasic) {
		return false
	}
	if !p.Field2DeepEqual(ano.HookInfo) {
		return false
	}
	if !p.Field3DeepEqual(ano.Model) {
		return false
	}
	if !p.Field4DeepEqual(ano.PromptInfo) {
		return false
	}
	if !p.Field5DeepEqual(ano.Ability) {
		return false
	}
	if !p.Field6DeepEqual(ano.JumpConfig) {
		return false
	}
	if !p.Field7DeepEqual(ano.InterveneInfoList) {
		return false
	}
	return true
}

func (p *Agent) Field1DeepEqual(src *AgentBasic) bool {

	if !p.AgentBasic.DeepEqual(src) {
		return false
	}
	return true
}
func (p *Agent) Field2DeepEqual(src *HookInfo) bool {

	if !p.HookInfo.DeepEqual(src) {
		return false
	}
	return true
}
func (p *Agent) Field3DeepEqual(src *Model) bool {

	if !p.Model.DeepEqual(src) {
		return false
	}
	return true
}
func (p *Agent) Field4DeepEqual(src *PromptInfo) bool {

	if !p.PromptInfo.DeepEqual(src) {
		return false
	}
	return true
}
func (p *Agent) Field5DeepEqual(src *Ability) bool {

	if !p.Ability.DeepEqual(src) {
		return false
	}
	return true
}
func (p *Agent) Field6DeepEqual(src *AgentJumpConfig) bool {

	if !p.JumpConfig.DeepEqual(src) {
		return false
	}
	return true
}
func (p *Agent) Field7DeepEqual(src []*InterveneInfo) bool {

	if len(p.InterveneInfoList) != len(src) {
		return false
	}
	for i, v := range p.InterveneInfoList {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type AgentIntent struct {
	Intent      *string `thrift:"intent,1,optional" frugal:"1,optional,string" json:"intent,omitempty"`
	NextAgentId *int64  `thrift:"next_agent_id,2,optional" frugal:"2,optional,i64" json:"next_agent_id,omitempty"`
}

func NewAgentIntent() *AgentIntent {
	return &AgentIntent{}
}

func (p *AgentIntent) InitDefault() {
}

var AgentIntent_Intent_DEFAULT string

func (p *AgentIntent) GetIntent() (v string) {
	if !p.IsSetIntent() {
		return AgentIntent_Intent_DEFAULT
	}
	return *p.Intent
}

var AgentIntent_NextAgentId_DEFAULT int64

func (p *AgentIntent) GetNextAgentId() (v int64) {
	if !p.IsSetNextAgentId() {
		return AgentIntent_NextAgentId_DEFAULT
	}
	return *p.NextAgentId
}
func (p *AgentIntent) SetIntent(val *string) {
	p.Intent = val
}
func (p *AgentIntent) SetNextAgentId(val *int64) {
	p.NextAgentId = val
}

var fieldIDToName_AgentIntent = map[int16]string{
	1: "intent",
	2: "next_agent_id",
}

func (p *AgentIntent) IsSetIntent() bool {
	return p.Intent != nil
}

func (p *AgentIntent) IsSetNextAgentId() bool {
	return p.NextAgentId != nil
}

func (p *AgentIntent) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AgentIntent[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AgentIntent) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Intent = _field
	return nil
}
func (p *AgentIntent) ReadField2(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.NextAgentId = _field
	return nil
}

func (p *AgentIntent) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("AgentIntent"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AgentIntent) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetIntent() {
		if err = oprot.WriteFieldBegin("intent", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Intent); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *AgentIntent) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetNextAgentId() {
		if err = oprot.WriteFieldBegin("next_agent_id", thrift.I64, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.NextAgentId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *AgentIntent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AgentIntent(%+v)", *p)

}

func (p *AgentIntent) DeepEqual(ano *AgentIntent) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Intent) {
		return false
	}
	if !p.Field2DeepEqual(ano.NextAgentId) {
		return false
	}
	return true
}

func (p *AgentIntent) Field1DeepEqual(src *string) bool {

	if p.Intent == src {
		return true
	} else if p.Intent == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Intent, *src) != 0 {
		return false
	}
	return true
}
func (p *AgentIntent) Field2DeepEqual(src *int64) bool {

	if p.NextAgentId == src {
		return true
	} else if p.NextAgentId == nil || src == nil {
		return false
	}
	if *p.NextAgentId != *src {
		return false
	}
	return true
}

type AgentJumpConfig struct {
	Backtrack       *AgentBacktrackMode    `thrift:"backtrack,1,optional" frugal:"1,optional,AgentBacktrackMode" json:"backtrack,omitempty"`
	Recognition     *AgentRecognitionMode  `thrift:"recognition,2,optional" frugal:"2,optional,AgentRecognitionMode" json:"recognition,omitempty"`
	AgentIntent     []*AgentIntent         `thrift:"agent_intent,3,optional" frugal:"3,optional,list<AgentIntent>" json:"agent_intent,omitempty"`
	Description     *string                `thrift:"description,4,optional" frugal:"4,optional,string" json:"description,omitempty"`
	SessionType     *AgentSessionType      `thrift:"session_type,5,optional" frugal:"5,optional,AgentSessionType" json:"session_type,omitempty"`
	IndependentConf *IndependentModeConfig `thrift:"independent_conf,6,optional" frugal:"6,optional,IndependentModeConfig" json:"independent_conf,omitempty"`
}

func NewAgentJumpConfig() *AgentJumpConfig {
	return &AgentJumpConfig{}
}

func (p *AgentJumpConfig) InitDefault() {
}

var AgentJumpConfig_Backtrack_DEFAULT AgentBacktrackMode

func (p *AgentJumpConfig) GetBacktrack() (v AgentBacktrackMode) {
	if !p.IsSetBacktrack() {
		return AgentJumpConfig_Backtrack_DEFAULT
	}
	return *p.Backtrack
}

var AgentJumpConfig_Recognition_DEFAULT AgentRecognitionMode

func (p *AgentJumpConfig) GetRecognition() (v AgentRecognitionMode) {
	if !p.IsSetRecognition() {
		return AgentJumpConfig_Recognition_DEFAULT
	}
	return *p.Recognition
}

var AgentJumpConfig_AgentIntent_DEFAULT []*AgentIntent

func (p *AgentJumpConfig) GetAgentIntent() (v []*AgentIntent) {
	if !p.IsSetAgentIntent() {
		return AgentJumpConfig_AgentIntent_DEFAULT
	}
	return p.AgentIntent
}

var AgentJumpConfig_Description_DEFAULT string

func (p *AgentJumpConfig) GetDescription() (v string) {
	if !p.IsSetDescription() {
		return AgentJumpConfig_Description_DEFAULT
	}
	return *p.Description
}

var AgentJumpConfig_SessionType_DEFAULT AgentSessionType

func (p *AgentJumpConfig) GetSessionType() (v AgentSessionType) {
	if !p.IsSetSessionType() {
		return AgentJumpConfig_SessionType_DEFAULT
	}
	return *p.SessionType
}

var AgentJumpConfig_IndependentConf_DEFAULT *IndependentModeConfig

func (p *AgentJumpConfig) GetIndependentConf() (v *IndependentModeConfig) {
	if !p.IsSetIndependentConf() {
		return AgentJumpConfig_IndependentConf_DEFAULT
	}
	return p.IndependentConf
}
func (p *AgentJumpConfig) SetBacktrack(val *AgentBacktrackMode) {
	p.Backtrack = val
}
func (p *AgentJumpConfig) SetRecognition(val *AgentRecognitionMode) {
	p.Recognition = val
}
func (p *AgentJumpConfig) SetAgentIntent(val []*AgentIntent) {
	p.AgentIntent = val
}
func (p *AgentJumpConfig) SetDescription(val *string) {
	p.Description = val
}
func (p *AgentJumpConfig) SetSessionType(val *AgentSessionType) {
	p.SessionType = val
}
func (p *AgentJumpConfig) SetIndependentConf(val *IndependentModeConfig) {
	p.IndependentConf = val
}

var fieldIDToName_AgentJumpConfig = map[int16]string{
	1: "backtrack",
	2: "recognition",
	3: "agent_intent",
	4: "description",
	5: "session_type",
	6: "independent_conf",
}

func (p *AgentJumpConfig) IsSetBacktrack() bool {
	return p.Backtrack != nil
}

func (p *AgentJumpConfig) IsSetRecognition() bool {
	return p.Recognition != nil
}

func (p *AgentJumpConfig) IsSetAgentIntent() bool {
	return p.AgentIntent != nil
}

func (p *AgentJumpConfig) IsSetDescription() bool {
	return p.Description != nil
}

func (p *AgentJumpConfig) IsSetSessionType() bool {
	return p.SessionType != nil
}

func (p *AgentJumpConfig) IsSetIndependentConf() bool {
	return p.IndependentConf != nil
}

func (p *AgentJumpConfig) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AgentJumpConfig[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AgentJumpConfig) ReadField1(iprot thrift.TProtocol) error {

	var _field *AgentBacktrackMode
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := AgentBacktrackMode(v)
		_field = &tmp
	}
	p.Backtrack = _field
	return nil
}
func (p *AgentJumpConfig) ReadField2(iprot thrift.TProtocol) error {

	var _field *AgentRecognitionMode
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := AgentRecognitionMode(v)
		_field = &tmp
	}
	p.Recognition = _field
	return nil
}
func (p *AgentJumpConfig) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*AgentIntent, 0, size)
	values := make([]AgentIntent, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.AgentIntent = _field
	return nil
}
func (p *AgentJumpConfig) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Description = _field
	return nil
}
func (p *AgentJumpConfig) ReadField5(iprot thrift.TProtocol) error {

	var _field *AgentSessionType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := AgentSessionType(v)
		_field = &tmp
	}
	p.SessionType = _field
	return nil
}
func (p *AgentJumpConfig) ReadField6(iprot thrift.TProtocol) error {
	_field := NewIndependentModeConfig()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.IndependentConf = _field
	return nil
}

func (p *AgentJumpConfig) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("AgentJumpConfig"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AgentJumpConfig) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetBacktrack() {
		if err = oprot.WriteFieldBegin("backtrack", thrift.I32, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.Backtrack)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *AgentJumpConfig) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetRecognition() {
		if err = oprot.WriteFieldBegin("recognition", thrift.I32, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.Recognition)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *AgentJumpConfig) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetAgentIntent() {
		if err = oprot.WriteFieldBegin("agent_intent", thrift.LIST, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.AgentIntent)); err != nil {
			return err
		}
		for _, v := range p.AgentIntent {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *AgentJumpConfig) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetDescription() {
		if err = oprot.WriteFieldBegin("description", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Description); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *AgentJumpConfig) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetSessionType() {
		if err = oprot.WriteFieldBegin("session_type", thrift.I32, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.SessionType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *AgentJumpConfig) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetIndependentConf() {
		if err = oprot.WriteFieldBegin("independent_conf", thrift.STRUCT, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.IndependentConf.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *AgentJumpConfig) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AgentJumpConfig(%+v)", *p)

}

func (p *AgentJumpConfig) DeepEqual(ano *AgentJumpConfig) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Backtrack) {
		return false
	}
	if !p.Field2DeepEqual(ano.Recognition) {
		return false
	}
	if !p.Field3DeepEqual(ano.AgentIntent) {
		return false
	}
	if !p.Field4DeepEqual(ano.Description) {
		return false
	}
	if !p.Field5DeepEqual(ano.SessionType) {
		return false
	}
	if !p.Field6DeepEqual(ano.IndependentConf) {
		return false
	}
	return true
}

func (p *AgentJumpConfig) Field1DeepEqual(src *AgentBacktrackMode) bool {

	if p.Backtrack == src {
		return true
	} else if p.Backtrack == nil || src == nil {
		return false
	}
	if *p.Backtrack != *src {
		return false
	}
	return true
}
func (p *AgentJumpConfig) Field2DeepEqual(src *AgentRecognitionMode) bool {

	if p.Recognition == src {
		return true
	} else if p.Recognition == nil || src == nil {
		return false
	}
	if *p.Recognition != *src {
		return false
	}
	return true
}
func (p *AgentJumpConfig) Field3DeepEqual(src []*AgentIntent) bool {

	if len(p.AgentIntent) != len(src) {
		return false
	}
	for i, v := range p.AgentIntent {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *AgentJumpConfig) Field4DeepEqual(src *string) bool {

	if p.Description == src {
		return true
	} else if p.Description == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Description, *src) != 0 {
		return false
	}
	return true
}
func (p *AgentJumpConfig) Field5DeepEqual(src *AgentSessionType) bool {

	if p.SessionType == src {
		return true
	} else if p.SessionType == nil || src == nil {
		return false
	}
	if *p.SessionType != *src {
		return false
	}
	return true
}
func (p *AgentJumpConfig) Field6DeepEqual(src *IndependentModeConfig) bool {

	if !p.IndependentConf.DeepEqual(src) {
		return false
	}
	return true
}

type IndependentModeConfig struct {
	JudgeTiming  *IndependentTiming               `thrift:"judge_timing,1,optional" frugal:"1,optional,IndependentTiming" json:"judge_timing,omitempty"`
	ModelType    *IndependentRecognitionModelType `thrift:"model_type,2,optional" frugal:"2,optional,IndependentRecognitionModelType" json:"model_type,omitempty"`
	HistoryRound *int32                           `thrift:"history_round,3,optional" frugal:"3,optional,i32" json:"history_round,omitempty"`
	ModelId      *int64                           `thrift:"model_id,4,optional" frugal:"4,optional,i64" json:"model_id,omitempty"`
	Prompt       *string                          `thrift:"prompt,5,optional" frugal:"5,optional,string" json:"prompt,omitempty"`
}

func NewIndependentModeConfig() *IndependentModeConfig {
	return &IndependentModeConfig{}
}

func (p *IndependentModeConfig) InitDefault() {
}

var IndependentModeConfig_JudgeTiming_DEFAULT IndependentTiming

func (p *IndependentModeConfig) GetJudgeTiming() (v IndependentTiming) {
	if !p.IsSetJudgeTiming() {
		return IndependentModeConfig_JudgeTiming_DEFAULT
	}
	return *p.JudgeTiming
}

var IndependentModeConfig_ModelType_DEFAULT IndependentRecognitionModelType

func (p *IndependentModeConfig) GetModelType() (v IndependentRecognitionModelType) {
	if !p.IsSetModelType() {
		return IndependentModeConfig_ModelType_DEFAULT
	}
	return *p.ModelType
}

var IndependentModeConfig_HistoryRound_DEFAULT int32

func (p *IndependentModeConfig) GetHistoryRound() (v int32) {
	if !p.IsSetHistoryRound() {
		return IndependentModeConfig_HistoryRound_DEFAULT
	}
	return *p.HistoryRound
}

var IndependentModeConfig_ModelId_DEFAULT int64

func (p *IndependentModeConfig) GetModelId() (v int64) {
	if !p.IsSetModelId() {
		return IndependentModeConfig_ModelId_DEFAULT
	}
	return *p.ModelId
}

var IndependentModeConfig_Prompt_DEFAULT string

func (p *IndependentModeConfig) GetPrompt() (v string) {
	if !p.IsSetPrompt() {
		return IndependentModeConfig_Prompt_DEFAULT
	}
	return *p.Prompt
}
func (p *IndependentModeConfig) SetJudgeTiming(val *IndependentTiming) {
	p.JudgeTiming = val
}
func (p *IndependentModeConfig) SetModelType(val *IndependentRecognitionModelType) {
	p.ModelType = val
}
func (p *IndependentModeConfig) SetHistoryRound(val *int32) {
	p.HistoryRound = val
}
func (p *IndependentModeConfig) SetModelId(val *int64) {
	p.ModelId = val
}
func (p *IndependentModeConfig) SetPrompt(val *string) {
	p.Prompt = val
}

var fieldIDToName_IndependentModeConfig = map[int16]string{
	1: "judge_timing",
	2: "model_type",
	3: "history_round",
	4: "model_id",
	5: "prompt",
}

func (p *IndependentModeConfig) IsSetJudgeTiming() bool {
	return p.JudgeTiming != nil
}

func (p *IndependentModeConfig) IsSetModelType() bool {
	return p.ModelType != nil
}

func (p *IndependentModeConfig) IsSetHistoryRound() bool {
	return p.HistoryRound != nil
}

func (p *IndependentModeConfig) IsSetModelId() bool {
	return p.ModelId != nil
}

func (p *IndependentModeConfig) IsSetPrompt() bool {
	return p.Prompt != nil
}

func (p *IndependentModeConfig) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_IndependentModeConfig[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *IndependentModeConfig) ReadField1(iprot thrift.TProtocol) error {

	var _field *IndependentTiming
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := IndependentTiming(v)
		_field = &tmp
	}
	p.JudgeTiming = _field
	return nil
}
func (p *IndependentModeConfig) ReadField2(iprot thrift.TProtocol) error {

	var _field *IndependentRecognitionModelType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := IndependentRecognitionModelType(v)
		_field = &tmp
	}
	p.ModelType = _field
	return nil
}
func (p *IndependentModeConfig) ReadField3(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.HistoryRound = _field
	return nil
}
func (p *IndependentModeConfig) ReadField4(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ModelId = _field
	return nil
}
func (p *IndependentModeConfig) ReadField5(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Prompt = _field
	return nil
}

func (p *IndependentModeConfig) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("IndependentModeConfig"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *IndependentModeConfig) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetJudgeTiming() {
		if err = oprot.WriteFieldBegin("judge_timing", thrift.I32, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.JudgeTiming)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *IndependentModeConfig) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetModelType() {
		if err = oprot.WriteFieldBegin("model_type", thrift.I32, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.ModelType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *IndependentModeConfig) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetHistoryRound() {
		if err = oprot.WriteFieldBegin("history_round", thrift.I32, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.HistoryRound); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *IndependentModeConfig) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetModelId() {
		if err = oprot.WriteFieldBegin("model_id", thrift.I64, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.ModelId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *IndependentModeConfig) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetPrompt() {
		if err = oprot.WriteFieldBegin("prompt", thrift.STRING, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Prompt); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *IndependentModeConfig) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IndependentModeConfig(%+v)", *p)

}

func (p *IndependentModeConfig) DeepEqual(ano *IndependentModeConfig) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.JudgeTiming) {
		return false
	}
	if !p.Field2DeepEqual(ano.ModelType) {
		return false
	}
	if !p.Field3DeepEqual(ano.HistoryRound) {
		return false
	}
	if !p.Field4DeepEqual(ano.ModelId) {
		return false
	}
	if !p.Field5DeepEqual(ano.Prompt) {
		return false
	}
	return true
}

func (p *IndependentModeConfig) Field1DeepEqual(src *IndependentTiming) bool {

	if p.JudgeTiming == src {
		return true
	} else if p.JudgeTiming == nil || src == nil {
		return false
	}
	if *p.JudgeTiming != *src {
		return false
	}
	return true
}
func (p *IndependentModeConfig) Field2DeepEqual(src *IndependentRecognitionModelType) bool {

	if p.ModelType == src {
		return true
	} else if p.ModelType == nil || src == nil {
		return false
	}
	if *p.ModelType != *src {
		return false
	}
	return true
}
func (p *IndependentModeConfig) Field3DeepEqual(src *int32) bool {

	if p.HistoryRound == src {
		return true
	} else if p.HistoryRound == nil || src == nil {
		return false
	}
	if *p.HistoryRound != *src {
		return false
	}
	return true
}
func (p *IndependentModeConfig) Field4DeepEqual(src *int64) bool {

	if p.ModelId == src {
		return true
	} else if p.ModelId == nil || src == nil {
		return false
	}
	if *p.ModelId != *src {
		return false
	}
	return true
}
func (p *IndependentModeConfig) Field5DeepEqual(src *string) bool {

	if p.Prompt == src {
		return true
	} else if p.Prompt == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Prompt, *src) != 0 {
		return false
	}
	return true
}

type AgentBasic struct {
	AgentId             *int64     `thrift:"agent_id,1,optional" frugal:"1,optional,i64" json:"agent_id,omitempty"`
	Name                *string    `thrift:"name,2,optional" frugal:"2,optional,string" json:"name,omitempty"`
	IconUri             *string    `thrift:"icon_uri,3,optional" frugal:"3,optional,string" json:"icon_uri,omitempty"`
	AgentType           *AgentType `thrift:"agent_type,4,optional" frugal:"4,optional,AgentType" json:"agent_type,omitempty"`
	ReferenceBotId      *int64     `thrift:"reference_bot_id,5,optional" frugal:"5,optional,i64" json:"reference_bot_id,omitempty"`
	ReferenceBotVersion *string    `thrift:"reference_bot_version,6,optional" frugal:"6,optional,string" json:"reference_bot_version,omitempty"`
	IsRootAgent         *bool      `thrift:"is_root_agent,7,optional" frugal:"7,optional,bool" json:"is_root_agent,omitempty"`
}

func NewAgentBasic() *AgentBasic {
	return &AgentBasic{}
}

func (p *AgentBasic) InitDefault() {
}

var AgentBasic_AgentId_DEFAULT int64

func (p *AgentBasic) GetAgentId() (v int64) {
	if !p.IsSetAgentId() {
		return AgentBasic_AgentId_DEFAULT
	}
	return *p.AgentId
}

var AgentBasic_Name_DEFAULT string

func (p *AgentBasic) GetName() (v string) {
	if !p.IsSetName() {
		return AgentBasic_Name_DEFAULT
	}
	return *p.Name
}

var AgentBasic_IconUri_DEFAULT string

func (p *AgentBasic) GetIconUri() (v string) {
	if !p.IsSetIconUri() {
		return AgentBasic_IconUri_DEFAULT
	}
	return *p.IconUri
}

var AgentBasic_AgentType_DEFAULT AgentType

func (p *AgentBasic) GetAgentType() (v AgentType) {
	if !p.IsSetAgentType() {
		return AgentBasic_AgentType_DEFAULT
	}
	return *p.AgentType
}

var AgentBasic_ReferenceBotId_DEFAULT int64

func (p *AgentBasic) GetReferenceBotId() (v int64) {
	if !p.IsSetReferenceBotId() {
		return AgentBasic_ReferenceBotId_DEFAULT
	}
	return *p.ReferenceBotId
}

var AgentBasic_ReferenceBotVersion_DEFAULT string

func (p *AgentBasic) GetReferenceBotVersion() (v string) {
	if !p.IsSetReferenceBotVersion() {
		return AgentBasic_ReferenceBotVersion_DEFAULT
	}
	return *p.ReferenceBotVersion
}

var AgentBasic_IsRootAgent_DEFAULT bool

func (p *AgentBasic) GetIsRootAgent() (v bool) {
	if !p.IsSetIsRootAgent() {
		return AgentBasic_IsRootAgent_DEFAULT
	}
	return *p.IsRootAgent
}
func (p *AgentBasic) SetAgentId(val *int64) {
	p.AgentId = val
}
func (p *AgentBasic) SetName(val *string) {
	p.Name = val
}
func (p *AgentBasic) SetIconUri(val *string) {
	p.IconUri = val
}
func (p *AgentBasic) SetAgentType(val *AgentType) {
	p.AgentType = val
}
func (p *AgentBasic) SetReferenceBotId(val *int64) {
	p.ReferenceBotId = val
}
func (p *AgentBasic) SetReferenceBotVersion(val *string) {
	p.ReferenceBotVersion = val
}
func (p *AgentBasic) SetIsRootAgent(val *bool) {
	p.IsRootAgent = val
}

var fieldIDToName_AgentBasic = map[int16]string{
	1: "agent_id",
	2: "name",
	3: "icon_uri",
	4: "agent_type",
	5: "reference_bot_id",
	6: "reference_bot_version",
	7: "is_root_agent",
}

func (p *AgentBasic) IsSetAgentId() bool {
	return p.AgentId != nil
}

func (p *AgentBasic) IsSetName() bool {
	return p.Name != nil
}

func (p *AgentBasic) IsSetIconUri() bool {
	return p.IconUri != nil
}

func (p *AgentBasic) IsSetAgentType() bool {
	return p.AgentType != nil
}

func (p *AgentBasic) IsSetReferenceBotId() bool {
	return p.ReferenceBotId != nil
}

func (p *AgentBasic) IsSetReferenceBotVersion() bool {
	return p.ReferenceBotVersion != nil
}

func (p *AgentBasic) IsSetIsRootAgent() bool {
	return p.IsRootAgent != nil
}

func (p *AgentBasic) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AgentBasic[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AgentBasic) ReadField1(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.AgentId = _field
	return nil
}
func (p *AgentBasic) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Name = _field
	return nil
}
func (p *AgentBasic) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.IconUri = _field
	return nil
}
func (p *AgentBasic) ReadField4(iprot thrift.TProtocol) error {

	var _field *AgentType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := AgentType(v)
		_field = &tmp
	}
	p.AgentType = _field
	return nil
}
func (p *AgentBasic) ReadField5(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ReferenceBotId = _field
	return nil
}
func (p *AgentBasic) ReadField6(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ReferenceBotVersion = _field
	return nil
}
func (p *AgentBasic) ReadField7(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.IsRootAgent = _field
	return nil
}

func (p *AgentBasic) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("AgentBasic"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AgentBasic) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetAgentId() {
		if err = oprot.WriteFieldBegin("agent_id", thrift.I64, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.AgentId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *AgentBasic) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetName() {
		if err = oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Name); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *AgentBasic) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetIconUri() {
		if err = oprot.WriteFieldBegin("icon_uri", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.IconUri); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *AgentBasic) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetAgentType() {
		if err = oprot.WriteFieldBegin("agent_type", thrift.I32, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.AgentType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *AgentBasic) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetReferenceBotId() {
		if err = oprot.WriteFieldBegin("reference_bot_id", thrift.I64, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.ReferenceBotId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *AgentBasic) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetReferenceBotVersion() {
		if err = oprot.WriteFieldBegin("reference_bot_version", thrift.STRING, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ReferenceBotVersion); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *AgentBasic) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetIsRootAgent() {
		if err = oprot.WriteFieldBegin("is_root_agent", thrift.BOOL, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.IsRootAgent); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *AgentBasic) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AgentBasic(%+v)", *p)

}

func (p *AgentBasic) DeepEqual(ano *AgentBasic) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.AgentId) {
		return false
	}
	if !p.Field2DeepEqual(ano.Name) {
		return false
	}
	if !p.Field3DeepEqual(ano.IconUri) {
		return false
	}
	if !p.Field4DeepEqual(ano.AgentType) {
		return false
	}
	if !p.Field5DeepEqual(ano.ReferenceBotId) {
		return false
	}
	if !p.Field6DeepEqual(ano.ReferenceBotVersion) {
		return false
	}
	if !p.Field7DeepEqual(ano.IsRootAgent) {
		return false
	}
	return true
}

func (p *AgentBasic) Field1DeepEqual(src *int64) bool {

	if p.AgentId == src {
		return true
	} else if p.AgentId == nil || src == nil {
		return false
	}
	if *p.AgentId != *src {
		return false
	}
	return true
}
func (p *AgentBasic) Field2DeepEqual(src *string) bool {

	if p.Name == src {
		return true
	} else if p.Name == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Name, *src) != 0 {
		return false
	}
	return true
}
func (p *AgentBasic) Field3DeepEqual(src *string) bool {

	if p.IconUri == src {
		return true
	} else if p.IconUri == nil || src == nil {
		return false
	}
	if strings.Compare(*p.IconUri, *src) != 0 {
		return false
	}
	return true
}
func (p *AgentBasic) Field4DeepEqual(src *AgentType) bool {

	if p.AgentType == src {
		return true
	} else if p.AgentType == nil || src == nil {
		return false
	}
	if *p.AgentType != *src {
		return false
	}
	return true
}
func (p *AgentBasic) Field5DeepEqual(src *int64) bool {

	if p.ReferenceBotId == src {
		return true
	} else if p.ReferenceBotId == nil || src == nil {
		return false
	}
	if *p.ReferenceBotId != *src {
		return false
	}
	return true
}
func (p *AgentBasic) Field6DeepEqual(src *string) bool {

	if p.ReferenceBotVersion == src {
		return true
	} else if p.ReferenceBotVersion == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ReferenceBotVersion, *src) != 0 {
		return false
	}
	return true
}
func (p *AgentBasic) Field7DeepEqual(src *bool) bool {

	if p.IsRootAgent == src {
		return true
	} else if p.IsRootAgent == nil || src == nil {
		return false
	}
	if *p.IsRootAgent != *src {
		return false
	}
	return true
}

type HookInfo struct {
	PreAgentJumpHook  []*HookItem `thrift:"pre_agent_jump_hook,1,optional" frugal:"1,optional,list<HookItem>" json:"pre_agent_jump_hook,omitempty"`
	PostAgentJumpHook []*HookItem `thrift:"post_agent_jump_hook,2,optional" frugal:"2,optional,list<HookItem>" json:"post_agent_jump_hook,omitempty"`
	FlowHook          []*HookItem `thrift:"flow_hook,3,optional" frugal:"3,optional,list<HookItem>" json:"flow_hook,omitempty"`
	AtomicHook        []*HookItem `thrift:"atomic_hook,4,optional" frugal:"4,optional,list<HookItem>" json:"atomic_hook,omitempty"`
	LlmCallHook       []*HookItem `thrift:"llm_call_hook,5,optional" frugal:"5,optional,list<HookItem>" json:"llm_call_hook,omitempty"`
	ResParsingHook    []*HookItem `thrift:"res_parsing_hook,6,optional" frugal:"6,optional,list<HookItem>" json:"res_parsing_hook,omitempty"`
	SuggestionHook    []*HookItem `thrift:"suggestion_hook,7,optional" frugal:"7,optional,list<HookItem>" json:"suggestion_hook,omitempty"`
}

func NewHookInfo() *HookInfo {
	return &HookInfo{}
}

func (p *HookInfo) InitDefault() {
}

var HookInfo_PreAgentJumpHook_DEFAULT []*HookItem

func (p *HookInfo) GetPreAgentJumpHook() (v []*HookItem) {
	if !p.IsSetPreAgentJumpHook() {
		return HookInfo_PreAgentJumpHook_DEFAULT
	}
	return p.PreAgentJumpHook
}

var HookInfo_PostAgentJumpHook_DEFAULT []*HookItem

func (p *HookInfo) GetPostAgentJumpHook() (v []*HookItem) {
	if !p.IsSetPostAgentJumpHook() {
		return HookInfo_PostAgentJumpHook_DEFAULT
	}
	return p.PostAgentJumpHook
}

var HookInfo_FlowHook_DEFAULT []*HookItem

func (p *HookInfo) GetFlowHook() (v []*HookItem) {
	if !p.IsSetFlowHook() {
		return HookInfo_FlowHook_DEFAULT
	}
	return p.FlowHook
}

var HookInfo_AtomicHook_DEFAULT []*HookItem

func (p *HookInfo) GetAtomicHook() (v []*HookItem) {
	if !p.IsSetAtomicHook() {
		return HookInfo_AtomicHook_DEFAULT
	}
	return p.AtomicHook
}

var HookInfo_LlmCallHook_DEFAULT []*HookItem

func (p *HookInfo) GetLlmCallHook() (v []*HookItem) {
	if !p.IsSetLlmCallHook() {
		return HookInfo_LlmCallHook_DEFAULT
	}
	return p.LlmCallHook
}

var HookInfo_ResParsingHook_DEFAULT []*HookItem

func (p *HookInfo) GetResParsingHook() (v []*HookItem) {
	if !p.IsSetResParsingHook() {
		return HookInfo_ResParsingHook_DEFAULT
	}
	return p.ResParsingHook
}

var HookInfo_SuggestionHook_DEFAULT []*HookItem

func (p *HookInfo) GetSuggestionHook() (v []*HookItem) {
	if !p.IsSetSuggestionHook() {
		return HookInfo_SuggestionHook_DEFAULT
	}
	return p.SuggestionHook
}
func (p *HookInfo) SetPreAgentJumpHook(val []*HookItem) {
	p.PreAgentJumpHook = val
}
func (p *HookInfo) SetPostAgentJumpHook(val []*HookItem) {
	p.PostAgentJumpHook = val
}
func (p *HookInfo) SetFlowHook(val []*HookItem) {
	p.FlowHook = val
}
func (p *HookInfo) SetAtomicHook(val []*HookItem) {
	p.AtomicHook = val
}
func (p *HookInfo) SetLlmCallHook(val []*HookItem) {
	p.LlmCallHook = val
}
func (p *HookInfo) SetResParsingHook(val []*HookItem) {
	p.ResParsingHook = val
}
func (p *HookInfo) SetSuggestionHook(val []*HookItem) {
	p.SuggestionHook = val
}

var fieldIDToName_HookInfo = map[int16]string{
	1: "pre_agent_jump_hook",
	2: "post_agent_jump_hook",
	3: "flow_hook",
	4: "atomic_hook",
	5: "llm_call_hook",
	6: "res_parsing_hook",
	7: "suggestion_hook",
}

func (p *HookInfo) IsSetPreAgentJumpHook() bool {
	return p.PreAgentJumpHook != nil
}

func (p *HookInfo) IsSetPostAgentJumpHook() bool {
	return p.PostAgentJumpHook != nil
}

func (p *HookInfo) IsSetFlowHook() bool {
	return p.FlowHook != nil
}

func (p *HookInfo) IsSetAtomicHook() bool {
	return p.AtomicHook != nil
}

func (p *HookInfo) IsSetLlmCallHook() bool {
	return p.LlmCallHook != nil
}

func (p *HookInfo) IsSetResParsingHook() bool {
	return p.ResParsingHook != nil
}

func (p *HookInfo) IsSetSuggestionHook() bool {
	return p.SuggestionHook != nil
}

func (p *HookInfo) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_HookInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *HookInfo) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*HookItem, 0, size)
	values := make([]HookItem, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.PreAgentJumpHook = _field
	return nil
}
func (p *HookInfo) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*HookItem, 0, size)
	values := make([]HookItem, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.PostAgentJumpHook = _field
	return nil
}
func (p *HookInfo) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*HookItem, 0, size)
	values := make([]HookItem, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.FlowHook = _field
	return nil
}
func (p *HookInfo) ReadField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*HookItem, 0, size)
	values := make([]HookItem, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.AtomicHook = _field
	return nil
}
func (p *HookInfo) ReadField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*HookItem, 0, size)
	values := make([]HookItem, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.LlmCallHook = _field
	return nil
}
func (p *HookInfo) ReadField6(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*HookItem, 0, size)
	values := make([]HookItem, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ResParsingHook = _field
	return nil
}
func (p *HookInfo) ReadField7(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*HookItem, 0, size)
	values := make([]HookItem, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.SuggestionHook = _field
	return nil
}

func (p *HookInfo) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("HookInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *HookInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetPreAgentJumpHook() {
		if err = oprot.WriteFieldBegin("pre_agent_jump_hook", thrift.LIST, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.PreAgentJumpHook)); err != nil {
			return err
		}
		for _, v := range p.PreAgentJumpHook {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *HookInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetPostAgentJumpHook() {
		if err = oprot.WriteFieldBegin("post_agent_jump_hook", thrift.LIST, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.PostAgentJumpHook)); err != nil {
			return err
		}
		for _, v := range p.PostAgentJumpHook {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *HookInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetFlowHook() {
		if err = oprot.WriteFieldBegin("flow_hook", thrift.LIST, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.FlowHook)); err != nil {
			return err
		}
		for _, v := range p.FlowHook {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *HookInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetAtomicHook() {
		if err = oprot.WriteFieldBegin("atomic_hook", thrift.LIST, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.AtomicHook)); err != nil {
			return err
		}
		for _, v := range p.AtomicHook {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *HookInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetLlmCallHook() {
		if err = oprot.WriteFieldBegin("llm_call_hook", thrift.LIST, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.LlmCallHook)); err != nil {
			return err
		}
		for _, v := range p.LlmCallHook {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *HookInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetResParsingHook() {
		if err = oprot.WriteFieldBegin("res_parsing_hook", thrift.LIST, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ResParsingHook)); err != nil {
			return err
		}
		for _, v := range p.ResParsingHook {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *HookInfo) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuggestionHook() {
		if err = oprot.WriteFieldBegin("suggestion_hook", thrift.LIST, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.SuggestionHook)); err != nil {
			return err
		}
		for _, v := range p.SuggestionHook {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *HookInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("HookInfo(%+v)", *p)

}

func (p *HookInfo) DeepEqual(ano *HookInfo) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.PreAgentJumpHook) {
		return false
	}
	if !p.Field2DeepEqual(ano.PostAgentJumpHook) {
		return false
	}
	if !p.Field3DeepEqual(ano.FlowHook) {
		return false
	}
	if !p.Field4DeepEqual(ano.AtomicHook) {
		return false
	}
	if !p.Field5DeepEqual(ano.LlmCallHook) {
		return false
	}
	if !p.Field6DeepEqual(ano.ResParsingHook) {
		return false
	}
	if !p.Field7DeepEqual(ano.SuggestionHook) {
		return false
	}
	return true
}

func (p *HookInfo) Field1DeepEqual(src []*HookItem) bool {

	if len(p.PreAgentJumpHook) != len(src) {
		return false
	}
	for i, v := range p.PreAgentJumpHook {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *HookInfo) Field2DeepEqual(src []*HookItem) bool {

	if len(p.PostAgentJumpHook) != len(src) {
		return false
	}
	for i, v := range p.PostAgentJumpHook {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *HookInfo) Field3DeepEqual(src []*HookItem) bool {

	if len(p.FlowHook) != len(src) {
		return false
	}
	for i, v := range p.FlowHook {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *HookInfo) Field4DeepEqual(src []*HookItem) bool {

	if len(p.AtomicHook) != len(src) {
		return false
	}
	for i, v := range p.AtomicHook {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *HookInfo) Field5DeepEqual(src []*HookItem) bool {

	if len(p.LlmCallHook) != len(src) {
		return false
	}
	for i, v := range p.LlmCallHook {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *HookInfo) Field6DeepEqual(src []*HookItem) bool {

	if len(p.ResParsingHook) != len(src) {
		return false
	}
	for i, v := range p.ResParsingHook {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *HookInfo) Field7DeepEqual(src []*HookItem) bool {

	if len(p.SuggestionHook) != len(src) {
		return false
	}
	for i, v := range p.SuggestionHook {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type PromptInfo struct {
	BotPersona   *string      `thrift:"bot_persona,1,optional" frugal:"1,optional,string" json:"bot_persona,omitempty"`
	TemplateName *string      `thrift:"template_name,2,optional" frugal:"2,optional,string" json:"template_name,omitempty"`
	ContextMode  *ContextMode `thrift:"context_mode,3,optional" frugal:"3,optional,ContextMode" json:"context_mode,omitempty"`
	HistoryRound *int32       `thrift:"history_round,4,optional" frugal:"4,optional,i32" json:"history_round,omitempty"`
}

func NewPromptInfo() *PromptInfo {
	return &PromptInfo{}
}

func (p *PromptInfo) InitDefault() {
}

var PromptInfo_BotPersona_DEFAULT string

func (p *PromptInfo) GetBotPersona() (v string) {
	if !p.IsSetBotPersona() {
		return PromptInfo_BotPersona_DEFAULT
	}
	return *p.BotPersona
}

var PromptInfo_TemplateName_DEFAULT string

func (p *PromptInfo) GetTemplateName() (v string) {
	if !p.IsSetTemplateName() {
		return PromptInfo_TemplateName_DEFAULT
	}
	return *p.TemplateName
}

var PromptInfo_ContextMode_DEFAULT ContextMode

func (p *PromptInfo) GetContextMode() (v ContextMode) {
	if !p.IsSetContextMode() {
		return PromptInfo_ContextMode_DEFAULT
	}
	return *p.ContextMode
}

var PromptInfo_HistoryRound_DEFAULT int32

func (p *PromptInfo) GetHistoryRound() (v int32) {
	if !p.IsSetHistoryRound() {
		return PromptInfo_HistoryRound_DEFAULT
	}
	return *p.HistoryRound
}
func (p *PromptInfo) SetBotPersona(val *string) {
	p.BotPersona = val
}
func (p *PromptInfo) SetTemplateName(val *string) {
	p.TemplateName = val
}
func (p *PromptInfo) SetContextMode(val *ContextMode) {
	p.ContextMode = val
}
func (p *PromptInfo) SetHistoryRound(val *int32) {
	p.HistoryRound = val
}

var fieldIDToName_PromptInfo = map[int16]string{
	1: "bot_persona",
	2: "template_name",
	3: "context_mode",
	4: "history_round",
}

func (p *PromptInfo) IsSetBotPersona() bool {
	return p.BotPersona != nil
}

func (p *PromptInfo) IsSetTemplateName() bool {
	return p.TemplateName != nil
}

func (p *PromptInfo) IsSetContextMode() bool {
	return p.ContextMode != nil
}

func (p *PromptInfo) IsSetHistoryRound() bool {
	return p.HistoryRound != nil
}

func (p *PromptInfo) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PromptInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *PromptInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.BotPersona = _field
	return nil
}
func (p *PromptInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.TemplateName = _field
	return nil
}
func (p *PromptInfo) ReadField3(iprot thrift.TProtocol) error {

	var _field *ContextMode
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := ContextMode(v)
		_field = &tmp
	}
	p.ContextMode = _field
	return nil
}
func (p *PromptInfo) ReadField4(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.HistoryRound = _field
	return nil
}

func (p *PromptInfo) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("PromptInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *PromptInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetBotPersona() {
		if err = oprot.WriteFieldBegin("bot_persona", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.BotPersona); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *PromptInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetTemplateName() {
		if err = oprot.WriteFieldBegin("template_name", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.TemplateName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *PromptInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetContextMode() {
		if err = oprot.WriteFieldBegin("context_mode", thrift.I32, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.ContextMode)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *PromptInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetHistoryRound() {
		if err = oprot.WriteFieldBegin("history_round", thrift.I32, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.HistoryRound); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *PromptInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PromptInfo(%+v)", *p)

}

func (p *PromptInfo) DeepEqual(ano *PromptInfo) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.BotPersona) {
		return false
	}
	if !p.Field2DeepEqual(ano.TemplateName) {
		return false
	}
	if !p.Field3DeepEqual(ano.ContextMode) {
		return false
	}
	if !p.Field4DeepEqual(ano.HistoryRound) {
		return false
	}
	return true
}

func (p *PromptInfo) Field1DeepEqual(src *string) bool {

	if p.BotPersona == src {
		return true
	} else if p.BotPersona == nil || src == nil {
		return false
	}
	if strings.Compare(*p.BotPersona, *src) != 0 {
		return false
	}
	return true
}
func (p *PromptInfo) Field2DeepEqual(src *string) bool {

	if p.TemplateName == src {
		return true
	} else if p.TemplateName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.TemplateName, *src) != 0 {
		return false
	}
	return true
}
func (p *PromptInfo) Field3DeepEqual(src *ContextMode) bool {

	if p.ContextMode == src {
		return true
	} else if p.ContextMode == nil || src == nil {
		return false
	}
	if *p.ContextMode != *src {
		return false
	}
	return true
}
func (p *PromptInfo) Field4DeepEqual(src *int32) bool {

	if p.HistoryRound == src {
		return true
	} else if p.HistoryRound == nil || src == nil {
		return false
	}
	if *p.HistoryRound != *src {
		return false
	}
	return true
}

type Model struct {
	ModelId          *int64          `thrift:"model_id,1,optional" frugal:"1,optional,i64" json:"model_id,omitempty"`
	Temperature      *float64        `thrift:"temperature,2,optional" frugal:"2,optional,double" json:"temperature,omitempty"`
	TopK             *int32          `thrift:"top_k,3,optional" frugal:"3,optional,i32" json:"top_k,omitempty"`
	TopP             *float64        `thrift:"top_p,4,optional" frugal:"4,optional,double" json:"top_p,omitempty"`
	FrequencyPenalty *float64        `thrift:"frequency_penalty,5,optional" frugal:"5,optional,double" json:"frequency_penalty,omitempty"`
	PresencePenalty  *float64        `thrift:"presence_penalty,6,optional" frugal:"6,optional,double" json:"presence_penalty,omitempty"`
	MaxTokens        *int32          `thrift:"max_tokens,7,optional" frugal:"7,optional,i32" json:"max_tokens,omitempty"`
	ResponseFormat   *ResponseFormat `thrift:"response_format,8,optional" frugal:"8,optional,ResponseFormat" json:"response_format,omitempty"`
	UseOptionalParam *bool           `thrift:"use_optional_param,99,optional" frugal:"99,optional,bool" json:"use_optional_param,omitempty"`
	FlexConfig       *string         `thrift:"flex_config,100,optional" frugal:"100,optional,string" json:"flex_config,omitempty"`
}

func NewModel() *Model {
	return &Model{}
}

func (p *Model) InitDefault() {
}

var Model_ModelId_DEFAULT int64

func (p *Model) GetModelId() (v int64) {
	if !p.IsSetModelId() {
		return Model_ModelId_DEFAULT
	}
	return *p.ModelId
}

var Model_Temperature_DEFAULT float64

func (p *Model) GetTemperature() (v float64) {
	if !p.IsSetTemperature() {
		return Model_Temperature_DEFAULT
	}
	return *p.Temperature
}

var Model_TopK_DEFAULT int32

func (p *Model) GetTopK() (v int32) {
	if !p.IsSetTopK() {
		return Model_TopK_DEFAULT
	}
	return *p.TopK
}

var Model_TopP_DEFAULT float64

func (p *Model) GetTopP() (v float64) {
	if !p.IsSetTopP() {
		return Model_TopP_DEFAULT
	}
	return *p.TopP
}

var Model_FrequencyPenalty_DEFAULT float64

func (p *Model) GetFrequencyPenalty() (v float64) {
	if !p.IsSetFrequencyPenalty() {
		return Model_FrequencyPenalty_DEFAULT
	}
	return *p.FrequencyPenalty
}

var Model_PresencePenalty_DEFAULT float64

func (p *Model) GetPresencePenalty() (v float64) {
	if !p.IsSetPresencePenalty() {
		return Model_PresencePenalty_DEFAULT
	}
	return *p.PresencePenalty
}

var Model_MaxTokens_DEFAULT int32

func (p *Model) GetMaxTokens() (v int32) {
	if !p.IsSetMaxTokens() {
		return Model_MaxTokens_DEFAULT
	}
	return *p.MaxTokens
}

var Model_ResponseFormat_DEFAULT ResponseFormat

func (p *Model) GetResponseFormat() (v ResponseFormat) {
	if !p.IsSetResponseFormat() {
		return Model_ResponseFormat_DEFAULT
	}
	return *p.ResponseFormat
}

var Model_UseOptionalParam_DEFAULT bool

func (p *Model) GetUseOptionalParam() (v bool) {
	if !p.IsSetUseOptionalParam() {
		return Model_UseOptionalParam_DEFAULT
	}
	return *p.UseOptionalParam
}

var Model_FlexConfig_DEFAULT string

func (p *Model) GetFlexConfig() (v string) {
	if !p.IsSetFlexConfig() {
		return Model_FlexConfig_DEFAULT
	}
	return *p.FlexConfig
}
func (p *Model) SetModelId(val *int64) {
	p.ModelId = val
}
func (p *Model) SetTemperature(val *float64) {
	p.Temperature = val
}
func (p *Model) SetTopK(val *int32) {
	p.TopK = val
}
func (p *Model) SetTopP(val *float64) {
	p.TopP = val
}
func (p *Model) SetFrequencyPenalty(val *float64) {
	p.FrequencyPenalty = val
}
func (p *Model) SetPresencePenalty(val *float64) {
	p.PresencePenalty = val
}
func (p *Model) SetMaxTokens(val *int32) {
	p.MaxTokens = val
}
func (p *Model) SetResponseFormat(val *ResponseFormat) {
	p.ResponseFormat = val
}
func (p *Model) SetUseOptionalParam(val *bool) {
	p.UseOptionalParam = val
}
func (p *Model) SetFlexConfig(val *string) {
	p.FlexConfig = val
}

var fieldIDToName_Model = map[int16]string{
	1:   "model_id",
	2:   "temperature",
	3:   "top_k",
	4:   "top_p",
	5:   "frequency_penalty",
	6:   "presence_penalty",
	7:   "max_tokens",
	8:   "response_format",
	99:  "use_optional_param",
	100: "flex_config",
}

func (p *Model) IsSetModelId() bool {
	return p.ModelId != nil
}

func (p *Model) IsSetTemperature() bool {
	return p.Temperature != nil
}

func (p *Model) IsSetTopK() bool {
	return p.TopK != nil
}

func (p *Model) IsSetTopP() bool {
	return p.TopP != nil
}

func (p *Model) IsSetFrequencyPenalty() bool {
	return p.FrequencyPenalty != nil
}

func (p *Model) IsSetPresencePenalty() bool {
	return p.PresencePenalty != nil
}

func (p *Model) IsSetMaxTokens() bool {
	return p.MaxTokens != nil
}

func (p *Model) IsSetResponseFormat() bool {
	return p.ResponseFormat != nil
}

func (p *Model) IsSetUseOptionalParam() bool {
	return p.UseOptionalParam != nil
}

func (p *Model) IsSetFlexConfig() bool {
	return p.FlexConfig != nil
}

func (p *Model) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.DOUBLE {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.DOUBLE {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.DOUBLE {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.DOUBLE {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 99:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField99(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 100:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField100(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_Model[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *Model) ReadField1(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ModelId = _field
	return nil
}
func (p *Model) ReadField2(iprot thrift.TProtocol) error {

	var _field *float64
	if v, err := iprot.ReadDouble(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Temperature = _field
	return nil
}
func (p *Model) ReadField3(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.TopK = _field
	return nil
}
func (p *Model) ReadField4(iprot thrift.TProtocol) error {

	var _field *float64
	if v, err := iprot.ReadDouble(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.TopP = _field
	return nil
}
func (p *Model) ReadField5(iprot thrift.TProtocol) error {

	var _field *float64
	if v, err := iprot.ReadDouble(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.FrequencyPenalty = _field
	return nil
}
func (p *Model) ReadField6(iprot thrift.TProtocol) error {

	var _field *float64
	if v, err := iprot.ReadDouble(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PresencePenalty = _field
	return nil
}
func (p *Model) ReadField7(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.MaxTokens = _field
	return nil
}
func (p *Model) ReadField8(iprot thrift.TProtocol) error {

	var _field *ResponseFormat
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := ResponseFormat(v)
		_field = &tmp
	}
	p.ResponseFormat = _field
	return nil
}
func (p *Model) ReadField99(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.UseOptionalParam = _field
	return nil
}
func (p *Model) ReadField100(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.FlexConfig = _field
	return nil
}

func (p *Model) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("Model"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField99(oprot); err != nil {
			fieldId = 99
			goto WriteFieldError
		}
		if err = p.writeField100(oprot); err != nil {
			fieldId = 100
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *Model) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetModelId() {
		if err = oprot.WriteFieldBegin("model_id", thrift.I64, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.ModelId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *Model) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetTemperature() {
		if err = oprot.WriteFieldBegin("temperature", thrift.DOUBLE, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteDouble(*p.Temperature); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *Model) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetTopK() {
		if err = oprot.WriteFieldBegin("top_k", thrift.I32, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.TopK); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *Model) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetTopP() {
		if err = oprot.WriteFieldBegin("top_p", thrift.DOUBLE, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteDouble(*p.TopP); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *Model) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetFrequencyPenalty() {
		if err = oprot.WriteFieldBegin("frequency_penalty", thrift.DOUBLE, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteDouble(*p.FrequencyPenalty); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *Model) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetPresencePenalty() {
		if err = oprot.WriteFieldBegin("presence_penalty", thrift.DOUBLE, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteDouble(*p.PresencePenalty); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *Model) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetMaxTokens() {
		if err = oprot.WriteFieldBegin("max_tokens", thrift.I32, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.MaxTokens); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}
func (p *Model) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetResponseFormat() {
		if err = oprot.WriteFieldBegin("response_format", thrift.I32, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.ResponseFormat)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}
func (p *Model) writeField99(oprot thrift.TProtocol) (err error) {
	if p.IsSetUseOptionalParam() {
		if err = oprot.WriteFieldBegin("use_optional_param", thrift.BOOL, 99); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.UseOptionalParam); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 99 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 99 end error: ", p), err)
}
func (p *Model) writeField100(oprot thrift.TProtocol) (err error) {
	if p.IsSetFlexConfig() {
		if err = oprot.WriteFieldBegin("flex_config", thrift.STRING, 100); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.FlexConfig); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 100 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 100 end error: ", p), err)
}

func (p *Model) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Model(%+v)", *p)

}

func (p *Model) DeepEqual(ano *Model) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ModelId) {
		return false
	}
	if !p.Field2DeepEqual(ano.Temperature) {
		return false
	}
	if !p.Field3DeepEqual(ano.TopK) {
		return false
	}
	if !p.Field4DeepEqual(ano.TopP) {
		return false
	}
	if !p.Field5DeepEqual(ano.FrequencyPenalty) {
		return false
	}
	if !p.Field6DeepEqual(ano.PresencePenalty) {
		return false
	}
	if !p.Field7DeepEqual(ano.MaxTokens) {
		return false
	}
	if !p.Field8DeepEqual(ano.ResponseFormat) {
		return false
	}
	if !p.Field99DeepEqual(ano.UseOptionalParam) {
		return false
	}
	if !p.Field100DeepEqual(ano.FlexConfig) {
		return false
	}
	return true
}

func (p *Model) Field1DeepEqual(src *int64) bool {

	if p.ModelId == src {
		return true
	} else if p.ModelId == nil || src == nil {
		return false
	}
	if *p.ModelId != *src {
		return false
	}
	return true
}
func (p *Model) Field2DeepEqual(src *float64) bool {

	if p.Temperature == src {
		return true
	} else if p.Temperature == nil || src == nil {
		return false
	}
	if *p.Temperature != *src {
		return false
	}
	return true
}
func (p *Model) Field3DeepEqual(src *int32) bool {

	if p.TopK == src {
		return true
	} else if p.TopK == nil || src == nil {
		return false
	}
	if *p.TopK != *src {
		return false
	}
	return true
}
func (p *Model) Field4DeepEqual(src *float64) bool {

	if p.TopP == src {
		return true
	} else if p.TopP == nil || src == nil {
		return false
	}
	if *p.TopP != *src {
		return false
	}
	return true
}
func (p *Model) Field5DeepEqual(src *float64) bool {

	if p.FrequencyPenalty == src {
		return true
	} else if p.FrequencyPenalty == nil || src == nil {
		return false
	}
	if *p.FrequencyPenalty != *src {
		return false
	}
	return true
}
func (p *Model) Field6DeepEqual(src *float64) bool {

	if p.PresencePenalty == src {
		return true
	} else if p.PresencePenalty == nil || src == nil {
		return false
	}
	if *p.PresencePenalty != *src {
		return false
	}
	return true
}
func (p *Model) Field7DeepEqual(src *int32) bool {

	if p.MaxTokens == src {
		return true
	} else if p.MaxTokens == nil || src == nil {
		return false
	}
	if *p.MaxTokens != *src {
		return false
	}
	return true
}
func (p *Model) Field8DeepEqual(src *ResponseFormat) bool {

	if p.ResponseFormat == src {
		return true
	} else if p.ResponseFormat == nil || src == nil {
		return false
	}
	if *p.ResponseFormat != *src {
		return false
	}
	return true
}
func (p *Model) Field99DeepEqual(src *bool) bool {

	if p.UseOptionalParam == src {
		return true
	} else if p.UseOptionalParam == nil || src == nil {
		return false
	}
	if *p.UseOptionalParam != *src {
		return false
	}
	return true
}
func (p *Model) Field100DeepEqual(src *string) bool {

	if p.FlexConfig == src {
		return true
	} else if p.FlexConfig == nil || src == nil {
		return false
	}
	if strings.Compare(*p.FlexConfig, *src) != 0 {
		return false
	}
	return true
}

type SwitchConf struct {
	EnablePlugin      *bool `thrift:"enable_plugin,1,optional" frugal:"1,optional,bool" json:"enable_plugin,omitempty"`
	EnableWorkflow    *bool `thrift:"enable_workflow,2,optional" frugal:"2,optional,bool" json:"enable_workflow,omitempty"`
	EnableKnowledge   *bool `thrift:"enable_knowledge,3,optional" frugal:"3,optional,bool" json:"enable_knowledge,omitempty"`
	EnableVariable    *bool `thrift:"enable_variable,4,optional" frugal:"4,optional,bool" json:"enable_variable,omitempty"`
	EnableDatabase    *bool `thrift:"enable_database,5,optional" frugal:"5,optional,bool" json:"enable_database,omitempty"`
	EnableTimeCapsule *bool `thrift:"enable_time_capsule,6,optional" frugal:"6,optional,bool" json:"enable_time_capsule,omitempty"`
	EnableFileBox     *bool `thrift:"enable_file_box,7,optional" frugal:"7,optional,bool" json:"enable_file_box,omitempty"`
	EnableTrigger     *bool `thrift:"enable_trigger,8,optional" frugal:"8,optional,bool" json:"enable_trigger,omitempty"`
	EnableApplet      *bool `thrift:"enable_applet,9,optional" frugal:"9,optional,bool" json:"enable_applet,omitempty"`
	EnableSuggest     *bool `thrift:"enable_suggest,10,optional" frugal:"10,optional,bool" json:"enable_suggest,omitempty"`
}

func NewSwitchConf() *SwitchConf {
	return &SwitchConf{}
}

func (p *SwitchConf) InitDefault() {
}

var SwitchConf_EnablePlugin_DEFAULT bool

func (p *SwitchConf) GetEnablePlugin() (v bool) {
	if !p.IsSetEnablePlugin() {
		return SwitchConf_EnablePlugin_DEFAULT
	}
	return *p.EnablePlugin
}

var SwitchConf_EnableWorkflow_DEFAULT bool

func (p *SwitchConf) GetEnableWorkflow() (v bool) {
	if !p.IsSetEnableWorkflow() {
		return SwitchConf_EnableWorkflow_DEFAULT
	}
	return *p.EnableWorkflow
}

var SwitchConf_EnableKnowledge_DEFAULT bool

func (p *SwitchConf) GetEnableKnowledge() (v bool) {
	if !p.IsSetEnableKnowledge() {
		return SwitchConf_EnableKnowledge_DEFAULT
	}
	return *p.EnableKnowledge
}

var SwitchConf_EnableVariable_DEFAULT bool

func (p *SwitchConf) GetEnableVariable() (v bool) {
	if !p.IsSetEnableVariable() {
		return SwitchConf_EnableVariable_DEFAULT
	}
	return *p.EnableVariable
}

var SwitchConf_EnableDatabase_DEFAULT bool

func (p *SwitchConf) GetEnableDatabase() (v bool) {
	if !p.IsSetEnableDatabase() {
		return SwitchConf_EnableDatabase_DEFAULT
	}
	return *p.EnableDatabase
}

var SwitchConf_EnableTimeCapsule_DEFAULT bool

func (p *SwitchConf) GetEnableTimeCapsule() (v bool) {
	if !p.IsSetEnableTimeCapsule() {
		return SwitchConf_EnableTimeCapsule_DEFAULT
	}
	return *p.EnableTimeCapsule
}

var SwitchConf_EnableFileBox_DEFAULT bool

func (p *SwitchConf) GetEnableFileBox() (v bool) {
	if !p.IsSetEnableFileBox() {
		return SwitchConf_EnableFileBox_DEFAULT
	}
	return *p.EnableFileBox
}

var SwitchConf_EnableTrigger_DEFAULT bool

func (p *SwitchConf) GetEnableTrigger() (v bool) {
	if !p.IsSetEnableTrigger() {
		return SwitchConf_EnableTrigger_DEFAULT
	}
	return *p.EnableTrigger
}

var SwitchConf_EnableApplet_DEFAULT bool

func (p *SwitchConf) GetEnableApplet() (v bool) {
	if !p.IsSetEnableApplet() {
		return SwitchConf_EnableApplet_DEFAULT
	}
	return *p.EnableApplet
}

var SwitchConf_EnableSuggest_DEFAULT bool

func (p *SwitchConf) GetEnableSuggest() (v bool) {
	if !p.IsSetEnableSuggest() {
		return SwitchConf_EnableSuggest_DEFAULT
	}
	return *p.EnableSuggest
}
func (p *SwitchConf) SetEnablePlugin(val *bool) {
	p.EnablePlugin = val
}
func (p *SwitchConf) SetEnableWorkflow(val *bool) {
	p.EnableWorkflow = val
}
func (p *SwitchConf) SetEnableKnowledge(val *bool) {
	p.EnableKnowledge = val
}
func (p *SwitchConf) SetEnableVariable(val *bool) {
	p.EnableVariable = val
}
func (p *SwitchConf) SetEnableDatabase(val *bool) {
	p.EnableDatabase = val
}
func (p *SwitchConf) SetEnableTimeCapsule(val *bool) {
	p.EnableTimeCapsule = val
}
func (p *SwitchConf) SetEnableFileBox(val *bool) {
	p.EnableFileBox = val
}
func (p *SwitchConf) SetEnableTrigger(val *bool) {
	p.EnableTrigger = val
}
func (p *SwitchConf) SetEnableApplet(val *bool) {
	p.EnableApplet = val
}
func (p *SwitchConf) SetEnableSuggest(val *bool) {
	p.EnableSuggest = val
}

var fieldIDToName_SwitchConf = map[int16]string{
	1:  "enable_plugin",
	2:  "enable_workflow",
	3:  "enable_knowledge",
	4:  "enable_variable",
	5:  "enable_database",
	6:  "enable_time_capsule",
	7:  "enable_file_box",
	8:  "enable_trigger",
	9:  "enable_applet",
	10: "enable_suggest",
}

func (p *SwitchConf) IsSetEnablePlugin() bool {
	return p.EnablePlugin != nil
}

func (p *SwitchConf) IsSetEnableWorkflow() bool {
	return p.EnableWorkflow != nil
}

func (p *SwitchConf) IsSetEnableKnowledge() bool {
	return p.EnableKnowledge != nil
}

func (p *SwitchConf) IsSetEnableVariable() bool {
	return p.EnableVariable != nil
}

func (p *SwitchConf) IsSetEnableDatabase() bool {
	return p.EnableDatabase != nil
}

func (p *SwitchConf) IsSetEnableTimeCapsule() bool {
	return p.EnableTimeCapsule != nil
}

func (p *SwitchConf) IsSetEnableFileBox() bool {
	return p.EnableFileBox != nil
}

func (p *SwitchConf) IsSetEnableTrigger() bool {
	return p.EnableTrigger != nil
}

func (p *SwitchConf) IsSetEnableApplet() bool {
	return p.EnableApplet != nil
}

func (p *SwitchConf) IsSetEnableSuggest() bool {
	return p.EnableSuggest != nil
}

func (p *SwitchConf) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SwitchConf[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *SwitchConf) ReadField1(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.EnablePlugin = _field
	return nil
}
func (p *SwitchConf) ReadField2(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.EnableWorkflow = _field
	return nil
}
func (p *SwitchConf) ReadField3(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.EnableKnowledge = _field
	return nil
}
func (p *SwitchConf) ReadField4(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.EnableVariable = _field
	return nil
}
func (p *SwitchConf) ReadField5(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.EnableDatabase = _field
	return nil
}
func (p *SwitchConf) ReadField6(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.EnableTimeCapsule = _field
	return nil
}
func (p *SwitchConf) ReadField7(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.EnableFileBox = _field
	return nil
}
func (p *SwitchConf) ReadField8(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.EnableTrigger = _field
	return nil
}
func (p *SwitchConf) ReadField9(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.EnableApplet = _field
	return nil
}
func (p *SwitchConf) ReadField10(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.EnableSuggest = _field
	return nil
}

func (p *SwitchConf) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("SwitchConf"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SwitchConf) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetEnablePlugin() {
		if err = oprot.WriteFieldBegin("enable_plugin", thrift.BOOL, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.EnablePlugin); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *SwitchConf) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetEnableWorkflow() {
		if err = oprot.WriteFieldBegin("enable_workflow", thrift.BOOL, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.EnableWorkflow); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *SwitchConf) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetEnableKnowledge() {
		if err = oprot.WriteFieldBegin("enable_knowledge", thrift.BOOL, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.EnableKnowledge); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *SwitchConf) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetEnableVariable() {
		if err = oprot.WriteFieldBegin("enable_variable", thrift.BOOL, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.EnableVariable); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *SwitchConf) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetEnableDatabase() {
		if err = oprot.WriteFieldBegin("enable_database", thrift.BOOL, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.EnableDatabase); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *SwitchConf) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetEnableTimeCapsule() {
		if err = oprot.WriteFieldBegin("enable_time_capsule", thrift.BOOL, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.EnableTimeCapsule); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *SwitchConf) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetEnableFileBox() {
		if err = oprot.WriteFieldBegin("enable_file_box", thrift.BOOL, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.EnableFileBox); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}
func (p *SwitchConf) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetEnableTrigger() {
		if err = oprot.WriteFieldBegin("enable_trigger", thrift.BOOL, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.EnableTrigger); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}
func (p *SwitchConf) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetEnableApplet() {
		if err = oprot.WriteFieldBegin("enable_applet", thrift.BOOL, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.EnableApplet); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}
func (p *SwitchConf) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetEnableSuggest() {
		if err = oprot.WriteFieldBegin("enable_suggest", thrift.BOOL, 10); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.EnableSuggest); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *SwitchConf) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SwitchConf(%+v)", *p)

}

func (p *SwitchConf) DeepEqual(ano *SwitchConf) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.EnablePlugin) {
		return false
	}
	if !p.Field2DeepEqual(ano.EnableWorkflow) {
		return false
	}
	if !p.Field3DeepEqual(ano.EnableKnowledge) {
		return false
	}
	if !p.Field4DeepEqual(ano.EnableVariable) {
		return false
	}
	if !p.Field5DeepEqual(ano.EnableDatabase) {
		return false
	}
	if !p.Field6DeepEqual(ano.EnableTimeCapsule) {
		return false
	}
	if !p.Field7DeepEqual(ano.EnableFileBox) {
		return false
	}
	if !p.Field8DeepEqual(ano.EnableTrigger) {
		return false
	}
	if !p.Field9DeepEqual(ano.EnableApplet) {
		return false
	}
	if !p.Field10DeepEqual(ano.EnableSuggest) {
		return false
	}
	return true
}

func (p *SwitchConf) Field1DeepEqual(src *bool) bool {

	if p.EnablePlugin == src {
		return true
	} else if p.EnablePlugin == nil || src == nil {
		return false
	}
	if *p.EnablePlugin != *src {
		return false
	}
	return true
}
func (p *SwitchConf) Field2DeepEqual(src *bool) bool {

	if p.EnableWorkflow == src {
		return true
	} else if p.EnableWorkflow == nil || src == nil {
		return false
	}
	if *p.EnableWorkflow != *src {
		return false
	}
	return true
}
func (p *SwitchConf) Field3DeepEqual(src *bool) bool {

	if p.EnableKnowledge == src {
		return true
	} else if p.EnableKnowledge == nil || src == nil {
		return false
	}
	if *p.EnableKnowledge != *src {
		return false
	}
	return true
}
func (p *SwitchConf) Field4DeepEqual(src *bool) bool {

	if p.EnableVariable == src {
		return true
	} else if p.EnableVariable == nil || src == nil {
		return false
	}
	if *p.EnableVariable != *src {
		return false
	}
	return true
}
func (p *SwitchConf) Field5DeepEqual(src *bool) bool {

	if p.EnableDatabase == src {
		return true
	} else if p.EnableDatabase == nil || src == nil {
		return false
	}
	if *p.EnableDatabase != *src {
		return false
	}
	return true
}
func (p *SwitchConf) Field6DeepEqual(src *bool) bool {

	if p.EnableTimeCapsule == src {
		return true
	} else if p.EnableTimeCapsule == nil || src == nil {
		return false
	}
	if *p.EnableTimeCapsule != *src {
		return false
	}
	return true
}
func (p *SwitchConf) Field7DeepEqual(src *bool) bool {

	if p.EnableFileBox == src {
		return true
	} else if p.EnableFileBox == nil || src == nil {
		return false
	}
	if *p.EnableFileBox != *src {
		return false
	}
	return true
}
func (p *SwitchConf) Field8DeepEqual(src *bool) bool {

	if p.EnableTrigger == src {
		return true
	} else if p.EnableTrigger == nil || src == nil {
		return false
	}
	if *p.EnableTrigger != *src {
		return false
	}
	return true
}
func (p *SwitchConf) Field9DeepEqual(src *bool) bool {

	if p.EnableApplet == src {
		return true
	} else if p.EnableApplet == nil || src == nil {
		return false
	}
	if *p.EnableApplet != *src {
		return false
	}
	return true
}
func (p *SwitchConf) Field10DeepEqual(src *bool) bool {

	if p.EnableSuggest == src {
		return true
	} else if p.EnableSuggest == nil || src == nil {
		return false
	}
	if *p.EnableSuggest != *src {
		return false
	}
	return true
}

type PluginAPI struct {
	PluginId *int64  `thrift:"plugin_id,1,optional" frugal:"1,optional,i64" json:"plugin_id,omitempty"`
	ApiId    *int64  `thrift:"api_id,2,optional" frugal:"2,optional,i64" json:"api_id,omitempty"`
	ApiName  *string `thrift:"api_name,3,optional" frugal:"3,optional,string" json:"api_name,omitempty"`
}

func NewPluginAPI() *PluginAPI {
	return &PluginAPI{}
}

func (p *PluginAPI) InitDefault() {
}

var PluginAPI_PluginId_DEFAULT int64

func (p *PluginAPI) GetPluginId() (v int64) {
	if !p.IsSetPluginId() {
		return PluginAPI_PluginId_DEFAULT
	}
	return *p.PluginId
}

var PluginAPI_ApiId_DEFAULT int64

func (p *PluginAPI) GetApiId() (v int64) {
	if !p.IsSetApiId() {
		return PluginAPI_ApiId_DEFAULT
	}
	return *p.ApiId
}

var PluginAPI_ApiName_DEFAULT string

func (p *PluginAPI) GetApiName() (v string) {
	if !p.IsSetApiName() {
		return PluginAPI_ApiName_DEFAULT
	}
	return *p.ApiName
}
func (p *PluginAPI) SetPluginId(val *int64) {
	p.PluginId = val
}
func (p *PluginAPI) SetApiId(val *int64) {
	p.ApiId = val
}
func (p *PluginAPI) SetApiName(val *string) {
	p.ApiName = val
}

var fieldIDToName_PluginAPI = map[int16]string{
	1: "plugin_id",
	2: "api_id",
	3: "api_name",
}

func (p *PluginAPI) IsSetPluginId() bool {
	return p.PluginId != nil
}

func (p *PluginAPI) IsSetApiId() bool {
	return p.ApiId != nil
}

func (p *PluginAPI) IsSetApiName() bool {
	return p.ApiName != nil
}

func (p *PluginAPI) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PluginAPI[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *PluginAPI) ReadField1(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PluginId = _field
	return nil
}
func (p *PluginAPI) ReadField2(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ApiId = _field
	return nil
}
func (p *PluginAPI) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ApiName = _field
	return nil
}

func (p *PluginAPI) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("PluginAPI"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *PluginAPI) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetPluginId() {
		if err = oprot.WriteFieldBegin("plugin_id", thrift.I64, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.PluginId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *PluginAPI) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetApiId() {
		if err = oprot.WriteFieldBegin("api_id", thrift.I64, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.ApiId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *PluginAPI) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetApiName() {
		if err = oprot.WriteFieldBegin("api_name", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ApiName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *PluginAPI) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PluginAPI(%+v)", *p)

}

func (p *PluginAPI) DeepEqual(ano *PluginAPI) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.PluginId) {
		return false
	}
	if !p.Field2DeepEqual(ano.ApiId) {
		return false
	}
	if !p.Field3DeepEqual(ano.ApiName) {
		return false
	}
	return true
}

func (p *PluginAPI) Field1DeepEqual(src *int64) bool {

	if p.PluginId == src {
		return true
	} else if p.PluginId == nil || src == nil {
		return false
	}
	if *p.PluginId != *src {
		return false
	}
	return true
}
func (p *PluginAPI) Field2DeepEqual(src *int64) bool {

	if p.ApiId == src {
		return true
	} else if p.ApiId == nil || src == nil {
		return false
	}
	if *p.ApiId != *src {
		return false
	}
	return true
}
func (p *PluginAPI) Field3DeepEqual(src *string) bool {

	if p.ApiName == src {
		return true
	} else if p.ApiName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ApiName, *src) != 0 {
		return false
	}
	return true
}

type WorkflowAPI struct {
	WorkflowId *int64 `thrift:"workflow_id,1,optional" frugal:"1,optional,i64" json:"workflow_id,omitempty"`
	PluginId   *int64 `thrift:"plugin_id,2,optional" frugal:"2,optional,i64" json:"plugin_id,omitempty"`
	ApiId      *int64 `thrift:"api_id,3,optional" frugal:"3,optional,i64" json:"api_id,omitempty"`
}

func NewWorkflowAPI() *WorkflowAPI {
	return &WorkflowAPI{}
}

func (p *WorkflowAPI) InitDefault() {
}

var WorkflowAPI_WorkflowId_DEFAULT int64

func (p *WorkflowAPI) GetWorkflowId() (v int64) {
	if !p.IsSetWorkflowId() {
		return WorkflowAPI_WorkflowId_DEFAULT
	}
	return *p.WorkflowId
}

var WorkflowAPI_PluginId_DEFAULT int64

func (p *WorkflowAPI) GetPluginId() (v int64) {
	if !p.IsSetPluginId() {
		return WorkflowAPI_PluginId_DEFAULT
	}
	return *p.PluginId
}

var WorkflowAPI_ApiId_DEFAULT int64

func (p *WorkflowAPI) GetApiId() (v int64) {
	if !p.IsSetApiId() {
		return WorkflowAPI_ApiId_DEFAULT
	}
	return *p.ApiId
}
func (p *WorkflowAPI) SetWorkflowId(val *int64) {
	p.WorkflowId = val
}
func (p *WorkflowAPI) SetPluginId(val *int64) {
	p.PluginId = val
}
func (p *WorkflowAPI) SetApiId(val *int64) {
	p.ApiId = val
}

var fieldIDToName_WorkflowAPI = map[int16]string{
	1: "workflow_id",
	2: "plugin_id",
	3: "api_id",
}

func (p *WorkflowAPI) IsSetWorkflowId() bool {
	return p.WorkflowId != nil
}

func (p *WorkflowAPI) IsSetPluginId() bool {
	return p.PluginId != nil
}

func (p *WorkflowAPI) IsSetApiId() bool {
	return p.ApiId != nil
}

func (p *WorkflowAPI) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_WorkflowAPI[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *WorkflowAPI) ReadField1(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.WorkflowId = _field
	return nil
}
func (p *WorkflowAPI) ReadField2(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PluginId = _field
	return nil
}
func (p *WorkflowAPI) ReadField3(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ApiId = _field
	return nil
}

func (p *WorkflowAPI) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("WorkflowAPI"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *WorkflowAPI) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetWorkflowId() {
		if err = oprot.WriteFieldBegin("workflow_id", thrift.I64, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.WorkflowId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *WorkflowAPI) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetPluginId() {
		if err = oprot.WriteFieldBegin("plugin_id", thrift.I64, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.PluginId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *WorkflowAPI) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetApiId() {
		if err = oprot.WriteFieldBegin("api_id", thrift.I64, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.ApiId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *WorkflowAPI) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("WorkflowAPI(%+v)", *p)

}

func (p *WorkflowAPI) DeepEqual(ano *WorkflowAPI) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.WorkflowId) {
		return false
	}
	if !p.Field2DeepEqual(ano.PluginId) {
		return false
	}
	if !p.Field3DeepEqual(ano.ApiId) {
		return false
	}
	return true
}

func (p *WorkflowAPI) Field1DeepEqual(src *int64) bool {

	if p.WorkflowId == src {
		return true
	} else if p.WorkflowId == nil || src == nil {
		return false
	}
	if *p.WorkflowId != *src {
		return false
	}
	return true
}
func (p *WorkflowAPI) Field2DeepEqual(src *int64) bool {

	if p.PluginId == src {
		return true
	} else if p.PluginId == nil || src == nil {
		return false
	}
	if *p.PluginId != *src {
		return false
	}
	return true
}
func (p *WorkflowAPI) Field3DeepEqual(src *int64) bool {

	if p.ApiId == src {
		return true
	} else if p.ApiId == nil || src == nil {
		return false
	}
	if *p.ApiId != *src {
		return false
	}
	return true
}

type Knowledge struct {
	Id                           *string                     `thrift:"id,1,optional" frugal:"1,optional,string" json:"id,omitempty"`
	Name                         *string                     `thrift:"name,2,optional" frugal:"2,optional,string" json:"name,omitempty"`
	TopK                         *int32                      `thrift:"top_k,3,optional" frugal:"3,optional,i32" json:"top_k,omitempty"`
	MinScore                     *float64                    `thrift:"min_score,4,optional" frugal:"4,optional,double" json:"min_score,omitempty"`
	Auto                         *bool                       `thrift:"auto,5,optional" frugal:"5,optional,bool" json:"auto,omitempty"`
	SearchMode                   *KnowledgeSearchMode        `thrift:"search_mode,6,optional" frugal:"6,optional,KnowledgeSearchMode" json:"search_mode,omitempty"`
	ShowSource                   *bool                       `thrift:"show_source,7,optional" frugal:"7,optional,bool" json:"show_source,omitempty"`
	ShowSourceMode               *KnowledgeShowSourceMode    `thrift:"show_source_mode,8,optional" frugal:"8,optional,KnowledgeShowSourceMode" json:"show_source_mode,omitempty"`
	NoRecallReplyMode            *KnowledgeNoRecallReplyMode `thrift:"no_recall_reply_mode,9,optional" frugal:"9,optional,KnowledgeNoRecallReplyMode" json:"no_recall_reply_mode,omitempty"`
	NoRecallReplyCustomizePrompt *string                     `thrift:"no_recall_reply_customize_prompt,10,optional" frugal:"10,optional,string" json:"no_recall_reply_customize_prompt,omitempty"`
}

func NewKnowledge() *Knowledge {
	return &Knowledge{}
}

func (p *Knowledge) InitDefault() {
}

var Knowledge_Id_DEFAULT string

func (p *Knowledge) GetId() (v string) {
	if !p.IsSetId() {
		return Knowledge_Id_DEFAULT
	}
	return *p.Id
}

var Knowledge_Name_DEFAULT string

func (p *Knowledge) GetName() (v string) {
	if !p.IsSetName() {
		return Knowledge_Name_DEFAULT
	}
	return *p.Name
}

var Knowledge_TopK_DEFAULT int32

func (p *Knowledge) GetTopK() (v int32) {
	if !p.IsSetTopK() {
		return Knowledge_TopK_DEFAULT
	}
	return *p.TopK
}

var Knowledge_MinScore_DEFAULT float64

func (p *Knowledge) GetMinScore() (v float64) {
	if !p.IsSetMinScore() {
		return Knowledge_MinScore_DEFAULT
	}
	return *p.MinScore
}

var Knowledge_Auto_DEFAULT bool

func (p *Knowledge) GetAuto() (v bool) {
	if !p.IsSetAuto() {
		return Knowledge_Auto_DEFAULT
	}
	return *p.Auto
}

var Knowledge_SearchMode_DEFAULT KnowledgeSearchMode

func (p *Knowledge) GetSearchMode() (v KnowledgeSearchMode) {
	if !p.IsSetSearchMode() {
		return Knowledge_SearchMode_DEFAULT
	}
	return *p.SearchMode
}

var Knowledge_ShowSource_DEFAULT bool

func (p *Knowledge) GetShowSource() (v bool) {
	if !p.IsSetShowSource() {
		return Knowledge_ShowSource_DEFAULT
	}
	return *p.ShowSource
}

var Knowledge_ShowSourceMode_DEFAULT KnowledgeShowSourceMode

func (p *Knowledge) GetShowSourceMode() (v KnowledgeShowSourceMode) {
	if !p.IsSetShowSourceMode() {
		return Knowledge_ShowSourceMode_DEFAULT
	}
	return *p.ShowSourceMode
}

var Knowledge_NoRecallReplyMode_DEFAULT KnowledgeNoRecallReplyMode

func (p *Knowledge) GetNoRecallReplyMode() (v KnowledgeNoRecallReplyMode) {
	if !p.IsSetNoRecallReplyMode() {
		return Knowledge_NoRecallReplyMode_DEFAULT
	}
	return *p.NoRecallReplyMode
}

var Knowledge_NoRecallReplyCustomizePrompt_DEFAULT string

func (p *Knowledge) GetNoRecallReplyCustomizePrompt() (v string) {
	if !p.IsSetNoRecallReplyCustomizePrompt() {
		return Knowledge_NoRecallReplyCustomizePrompt_DEFAULT
	}
	return *p.NoRecallReplyCustomizePrompt
}
func (p *Knowledge) SetId(val *string) {
	p.Id = val
}
func (p *Knowledge) SetName(val *string) {
	p.Name = val
}
func (p *Knowledge) SetTopK(val *int32) {
	p.TopK = val
}
func (p *Knowledge) SetMinScore(val *float64) {
	p.MinScore = val
}
func (p *Knowledge) SetAuto(val *bool) {
	p.Auto = val
}
func (p *Knowledge) SetSearchMode(val *KnowledgeSearchMode) {
	p.SearchMode = val
}
func (p *Knowledge) SetShowSource(val *bool) {
	p.ShowSource = val
}
func (p *Knowledge) SetShowSourceMode(val *KnowledgeShowSourceMode) {
	p.ShowSourceMode = val
}
func (p *Knowledge) SetNoRecallReplyMode(val *KnowledgeNoRecallReplyMode) {
	p.NoRecallReplyMode = val
}
func (p *Knowledge) SetNoRecallReplyCustomizePrompt(val *string) {
	p.NoRecallReplyCustomizePrompt = val
}

var fieldIDToName_Knowledge = map[int16]string{
	1:  "id",
	2:  "name",
	3:  "top_k",
	4:  "min_score",
	5:  "auto",
	6:  "search_mode",
	7:  "show_source",
	8:  "show_source_mode",
	9:  "no_recall_reply_mode",
	10: "no_recall_reply_customize_prompt",
}

func (p *Knowledge) IsSetId() bool {
	return p.Id != nil
}

func (p *Knowledge) IsSetName() bool {
	return p.Name != nil
}

func (p *Knowledge) IsSetTopK() bool {
	return p.TopK != nil
}

func (p *Knowledge) IsSetMinScore() bool {
	return p.MinScore != nil
}

func (p *Knowledge) IsSetAuto() bool {
	return p.Auto != nil
}

func (p *Knowledge) IsSetSearchMode() bool {
	return p.SearchMode != nil
}

func (p *Knowledge) IsSetShowSource() bool {
	return p.ShowSource != nil
}

func (p *Knowledge) IsSetShowSourceMode() bool {
	return p.ShowSourceMode != nil
}

func (p *Knowledge) IsSetNoRecallReplyMode() bool {
	return p.NoRecallReplyMode != nil
}

func (p *Knowledge) IsSetNoRecallReplyCustomizePrompt() bool {
	return p.NoRecallReplyCustomizePrompt != nil
}

func (p *Knowledge) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.DOUBLE {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_Knowledge[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *Knowledge) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Id = _field
	return nil
}
func (p *Knowledge) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Name = _field
	return nil
}
func (p *Knowledge) ReadField3(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.TopK = _field
	return nil
}
func (p *Knowledge) ReadField4(iprot thrift.TProtocol) error {

	var _field *float64
	if v, err := iprot.ReadDouble(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.MinScore = _field
	return nil
}
func (p *Knowledge) ReadField5(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Auto = _field
	return nil
}
func (p *Knowledge) ReadField6(iprot thrift.TProtocol) error {

	var _field *KnowledgeSearchMode
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := KnowledgeSearchMode(v)
		_field = &tmp
	}
	p.SearchMode = _field
	return nil
}
func (p *Knowledge) ReadField7(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ShowSource = _field
	return nil
}
func (p *Knowledge) ReadField8(iprot thrift.TProtocol) error {

	var _field *KnowledgeShowSourceMode
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := KnowledgeShowSourceMode(v)
		_field = &tmp
	}
	p.ShowSourceMode = _field
	return nil
}
func (p *Knowledge) ReadField9(iprot thrift.TProtocol) error {

	var _field *KnowledgeNoRecallReplyMode
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := KnowledgeNoRecallReplyMode(v)
		_field = &tmp
	}
	p.NoRecallReplyMode = _field
	return nil
}
func (p *Knowledge) ReadField10(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.NoRecallReplyCustomizePrompt = _field
	return nil
}

func (p *Knowledge) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("Knowledge"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *Knowledge) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetId() {
		if err = oprot.WriteFieldBegin("id", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Id); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *Knowledge) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetName() {
		if err = oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Name); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *Knowledge) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetTopK() {
		if err = oprot.WriteFieldBegin("top_k", thrift.I32, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.TopK); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *Knowledge) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetMinScore() {
		if err = oprot.WriteFieldBegin("min_score", thrift.DOUBLE, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteDouble(*p.MinScore); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *Knowledge) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetAuto() {
		if err = oprot.WriteFieldBegin("auto", thrift.BOOL, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.Auto); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *Knowledge) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetSearchMode() {
		if err = oprot.WriteFieldBegin("search_mode", thrift.I32, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.SearchMode)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *Knowledge) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetShowSource() {
		if err = oprot.WriteFieldBegin("show_source", thrift.BOOL, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.ShowSource); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}
func (p *Knowledge) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetShowSourceMode() {
		if err = oprot.WriteFieldBegin("show_source_mode", thrift.I32, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.ShowSourceMode)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}
func (p *Knowledge) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetNoRecallReplyMode() {
		if err = oprot.WriteFieldBegin("no_recall_reply_mode", thrift.I32, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.NoRecallReplyMode)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}
func (p *Knowledge) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetNoRecallReplyCustomizePrompt() {
		if err = oprot.WriteFieldBegin("no_recall_reply_customize_prompt", thrift.STRING, 10); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.NoRecallReplyCustomizePrompt); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *Knowledge) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Knowledge(%+v)", *p)

}

func (p *Knowledge) DeepEqual(ano *Knowledge) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Id) {
		return false
	}
	if !p.Field2DeepEqual(ano.Name) {
		return false
	}
	if !p.Field3DeepEqual(ano.TopK) {
		return false
	}
	if !p.Field4DeepEqual(ano.MinScore) {
		return false
	}
	if !p.Field5DeepEqual(ano.Auto) {
		return false
	}
	if !p.Field6DeepEqual(ano.SearchMode) {
		return false
	}
	if !p.Field7DeepEqual(ano.ShowSource) {
		return false
	}
	if !p.Field8DeepEqual(ano.ShowSourceMode) {
		return false
	}
	if !p.Field9DeepEqual(ano.NoRecallReplyMode) {
		return false
	}
	if !p.Field10DeepEqual(ano.NoRecallReplyCustomizePrompt) {
		return false
	}
	return true
}

func (p *Knowledge) Field1DeepEqual(src *string) bool {

	if p.Id == src {
		return true
	} else if p.Id == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Id, *src) != 0 {
		return false
	}
	return true
}
func (p *Knowledge) Field2DeepEqual(src *string) bool {

	if p.Name == src {
		return true
	} else if p.Name == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Name, *src) != 0 {
		return false
	}
	return true
}
func (p *Knowledge) Field3DeepEqual(src *int32) bool {

	if p.TopK == src {
		return true
	} else if p.TopK == nil || src == nil {
		return false
	}
	if *p.TopK != *src {
		return false
	}
	return true
}
func (p *Knowledge) Field4DeepEqual(src *float64) bool {

	if p.MinScore == src {
		return true
	} else if p.MinScore == nil || src == nil {
		return false
	}
	if *p.MinScore != *src {
		return false
	}
	return true
}
func (p *Knowledge) Field5DeepEqual(src *bool) bool {

	if p.Auto == src {
		return true
	} else if p.Auto == nil || src == nil {
		return false
	}
	if *p.Auto != *src {
		return false
	}
	return true
}
func (p *Knowledge) Field6DeepEqual(src *KnowledgeSearchMode) bool {

	if p.SearchMode == src {
		return true
	} else if p.SearchMode == nil || src == nil {
		return false
	}
	if *p.SearchMode != *src {
		return false
	}
	return true
}
func (p *Knowledge) Field7DeepEqual(src *bool) bool {

	if p.ShowSource == src {
		return true
	} else if p.ShowSource == nil || src == nil {
		return false
	}
	if *p.ShowSource != *src {
		return false
	}
	return true
}
func (p *Knowledge) Field8DeepEqual(src *KnowledgeShowSourceMode) bool {

	if p.ShowSourceMode == src {
		return true
	} else if p.ShowSourceMode == nil || src == nil {
		return false
	}
	if *p.ShowSourceMode != *src {
		return false
	}
	return true
}
func (p *Knowledge) Field9DeepEqual(src *KnowledgeNoRecallReplyMode) bool {

	if p.NoRecallReplyMode == src {
		return true
	} else if p.NoRecallReplyMode == nil || src == nil {
		return false
	}
	if *p.NoRecallReplyMode != *src {
		return false
	}
	return true
}
func (p *Knowledge) Field10DeepEqual(src *string) bool {

	if p.NoRecallReplyCustomizePrompt == src {
		return true
	} else if p.NoRecallReplyCustomizePrompt == nil || src == nil {
		return false
	}
	if strings.Compare(*p.NoRecallReplyCustomizePrompt, *src) != 0 {
		return false
	}
	return true
}

type Variable struct {
	Key            *string `thrift:"key,1,optional" frugal:"1,optional,string" json:"key,omitempty"`
	Description    *string `thrift:"description,2,optional" frugal:"2,optional,string" json:"description,omitempty"`
	DefaultValue   *string `thrift:"default_value,3,optional" frugal:"3,optional,string" json:"default_value,omitempty"`
	IsSystem       *bool   `thrift:"is_system,4,optional" frugal:"4,optional,bool" json:"is_system,omitempty"`
	PromptDisabled *bool   `thrift:"prompt_disabled,5,optional" frugal:"5,optional,bool" json:"prompt_disabled,omitempty"`
}

func NewVariable() *Variable {
	return &Variable{}
}

func (p *Variable) InitDefault() {
}

var Variable_Key_DEFAULT string

func (p *Variable) GetKey() (v string) {
	if !p.IsSetKey() {
		return Variable_Key_DEFAULT
	}
	return *p.Key
}

var Variable_Description_DEFAULT string

func (p *Variable) GetDescription() (v string) {
	if !p.IsSetDescription() {
		return Variable_Description_DEFAULT
	}
	return *p.Description
}

var Variable_DefaultValue_DEFAULT string

func (p *Variable) GetDefaultValue() (v string) {
	if !p.IsSetDefaultValue() {
		return Variable_DefaultValue_DEFAULT
	}
	return *p.DefaultValue
}

var Variable_IsSystem_DEFAULT bool

func (p *Variable) GetIsSystem() (v bool) {
	if !p.IsSetIsSystem() {
		return Variable_IsSystem_DEFAULT
	}
	return *p.IsSystem
}

var Variable_PromptDisabled_DEFAULT bool

func (p *Variable) GetPromptDisabled() (v bool) {
	if !p.IsSetPromptDisabled() {
		return Variable_PromptDisabled_DEFAULT
	}
	return *p.PromptDisabled
}
func (p *Variable) SetKey(val *string) {
	p.Key = val
}
func (p *Variable) SetDescription(val *string) {
	p.Description = val
}
func (p *Variable) SetDefaultValue(val *string) {
	p.DefaultValue = val
}
func (p *Variable) SetIsSystem(val *bool) {
	p.IsSystem = val
}
func (p *Variable) SetPromptDisabled(val *bool) {
	p.PromptDisabled = val
}

var fieldIDToName_Variable = map[int16]string{
	1: "key",
	2: "description",
	3: "default_value",
	4: "is_system",
	5: "prompt_disabled",
}

func (p *Variable) IsSetKey() bool {
	return p.Key != nil
}

func (p *Variable) IsSetDescription() bool {
	return p.Description != nil
}

func (p *Variable) IsSetDefaultValue() bool {
	return p.DefaultValue != nil
}

func (p *Variable) IsSetIsSystem() bool {
	return p.IsSystem != nil
}

func (p *Variable) IsSetPromptDisabled() bool {
	return p.PromptDisabled != nil
}

func (p *Variable) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_Variable[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *Variable) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Key = _field
	return nil
}
func (p *Variable) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Description = _field
	return nil
}
func (p *Variable) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.DefaultValue = _field
	return nil
}
func (p *Variable) ReadField4(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.IsSystem = _field
	return nil
}
func (p *Variable) ReadField5(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PromptDisabled = _field
	return nil
}

func (p *Variable) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("Variable"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *Variable) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetKey() {
		if err = oprot.WriteFieldBegin("key", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Key); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *Variable) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetDescription() {
		if err = oprot.WriteFieldBegin("description", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Description); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *Variable) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetDefaultValue() {
		if err = oprot.WriteFieldBegin("default_value", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.DefaultValue); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *Variable) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetIsSystem() {
		if err = oprot.WriteFieldBegin("is_system", thrift.BOOL, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.IsSystem); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *Variable) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetPromptDisabled() {
		if err = oprot.WriteFieldBegin("prompt_disabled", thrift.BOOL, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.PromptDisabled); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *Variable) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Variable(%+v)", *p)

}

func (p *Variable) DeepEqual(ano *Variable) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Key) {
		return false
	}
	if !p.Field2DeepEqual(ano.Description) {
		return false
	}
	if !p.Field3DeepEqual(ano.DefaultValue) {
		return false
	}
	if !p.Field4DeepEqual(ano.IsSystem) {
		return false
	}
	if !p.Field5DeepEqual(ano.PromptDisabled) {
		return false
	}
	return true
}

func (p *Variable) Field1DeepEqual(src *string) bool {

	if p.Key == src {
		return true
	} else if p.Key == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Key, *src) != 0 {
		return false
	}
	return true
}
func (p *Variable) Field2DeepEqual(src *string) bool {

	if p.Description == src {
		return true
	} else if p.Description == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Description, *src) != 0 {
		return false
	}
	return true
}
func (p *Variable) Field3DeepEqual(src *string) bool {

	if p.DefaultValue == src {
		return true
	} else if p.DefaultValue == nil || src == nil {
		return false
	}
	if strings.Compare(*p.DefaultValue, *src) != 0 {
		return false
	}
	return true
}
func (p *Variable) Field4DeepEqual(src *bool) bool {

	if p.IsSystem == src {
		return true
	} else if p.IsSystem == nil || src == nil {
		return false
	}
	if *p.IsSystem != *src {
		return false
	}
	return true
}
func (p *Variable) Field5DeepEqual(src *bool) bool {

	if p.PromptDisabled == src {
		return true
	} else if p.PromptDisabled == nil || src == nil {
		return false
	}
	if *p.PromptDisabled != *src {
		return false
	}
	return true
}

type Database struct {
	TableId        *string      `thrift:"table_id,1,optional" frugal:"1,optional,string" json:"table_id,omitempty"`
	TableName      *string      `thrift:"table_name,2,optional" frugal:"2,optional,string" json:"table_name,omitempty"`
	TableDesc      *string      `thrift:"table_desc,3,optional" frugal:"3,optional,string" json:"table_desc,omitempty"`
	FieldList      []*FieldItem `thrift:"field_list,4,optional" frugal:"4,optional,list<FieldItem>" json:"field_list,omitempty"`
	PromptDisabled *bool        `thrift:"prompt_disabled,5,optional" frugal:"5,optional,bool" json:"prompt_disabled,omitempty"`
}

func NewDatabase() *Database {
	return &Database{}
}

func (p *Database) InitDefault() {
}

var Database_TableId_DEFAULT string

func (p *Database) GetTableId() (v string) {
	if !p.IsSetTableId() {
		return Database_TableId_DEFAULT
	}
	return *p.TableId
}

var Database_TableName_DEFAULT string

func (p *Database) GetTableName() (v string) {
	if !p.IsSetTableName() {
		return Database_TableName_DEFAULT
	}
	return *p.TableName
}

var Database_TableDesc_DEFAULT string

func (p *Database) GetTableDesc() (v string) {
	if !p.IsSetTableDesc() {
		return Database_TableDesc_DEFAULT
	}
	return *p.TableDesc
}

var Database_FieldList_DEFAULT []*FieldItem

func (p *Database) GetFieldList() (v []*FieldItem) {
	if !p.IsSetFieldList() {
		return Database_FieldList_DEFAULT
	}
	return p.FieldList
}

var Database_PromptDisabled_DEFAULT bool

func (p *Database) GetPromptDisabled() (v bool) {
	if !p.IsSetPromptDisabled() {
		return Database_PromptDisabled_DEFAULT
	}
	return *p.PromptDisabled
}
func (p *Database) SetTableId(val *string) {
	p.TableId = val
}
func (p *Database) SetTableName(val *string) {
	p.TableName = val
}
func (p *Database) SetTableDesc(val *string) {
	p.TableDesc = val
}
func (p *Database) SetFieldList(val []*FieldItem) {
	p.FieldList = val
}
func (p *Database) SetPromptDisabled(val *bool) {
	p.PromptDisabled = val
}

var fieldIDToName_Database = map[int16]string{
	1: "table_id",
	2: "table_name",
	3: "table_desc",
	4: "field_list",
	5: "prompt_disabled",
}

func (p *Database) IsSetTableId() bool {
	return p.TableId != nil
}

func (p *Database) IsSetTableName() bool {
	return p.TableName != nil
}

func (p *Database) IsSetTableDesc() bool {
	return p.TableDesc != nil
}

func (p *Database) IsSetFieldList() bool {
	return p.FieldList != nil
}

func (p *Database) IsSetPromptDisabled() bool {
	return p.PromptDisabled != nil
}

func (p *Database) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_Database[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *Database) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.TableId = _field
	return nil
}
func (p *Database) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.TableName = _field
	return nil
}
func (p *Database) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.TableDesc = _field
	return nil
}
func (p *Database) ReadField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*FieldItem, 0, size)
	values := make([]FieldItem, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.FieldList = _field
	return nil
}
func (p *Database) ReadField5(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PromptDisabled = _field
	return nil
}

func (p *Database) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("Database"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *Database) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetTableId() {
		if err = oprot.WriteFieldBegin("table_id", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.TableId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *Database) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetTableName() {
		if err = oprot.WriteFieldBegin("table_name", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.TableName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *Database) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetTableDesc() {
		if err = oprot.WriteFieldBegin("table_desc", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.TableDesc); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *Database) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetFieldList() {
		if err = oprot.WriteFieldBegin("field_list", thrift.LIST, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.FieldList)); err != nil {
			return err
		}
		for _, v := range p.FieldList {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *Database) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetPromptDisabled() {
		if err = oprot.WriteFieldBegin("prompt_disabled", thrift.BOOL, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.PromptDisabled); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *Database) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Database(%+v)", *p)

}

func (p *Database) DeepEqual(ano *Database) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TableId) {
		return false
	}
	if !p.Field2DeepEqual(ano.TableName) {
		return false
	}
	if !p.Field3DeepEqual(ano.TableDesc) {
		return false
	}
	if !p.Field4DeepEqual(ano.FieldList) {
		return false
	}
	if !p.Field5DeepEqual(ano.PromptDisabled) {
		return false
	}
	return true
}

func (p *Database) Field1DeepEqual(src *string) bool {

	if p.TableId == src {
		return true
	} else if p.TableId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.TableId, *src) != 0 {
		return false
	}
	return true
}
func (p *Database) Field2DeepEqual(src *string) bool {

	if p.TableName == src {
		return true
	} else if p.TableName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.TableName, *src) != 0 {
		return false
	}
	return true
}
func (p *Database) Field3DeepEqual(src *string) bool {

	if p.TableDesc == src {
		return true
	} else if p.TableDesc == nil || src == nil {
		return false
	}
	if strings.Compare(*p.TableDesc, *src) != 0 {
		return false
	}
	return true
}
func (p *Database) Field4DeepEqual(src []*FieldItem) bool {

	if len(p.FieldList) != len(src) {
		return false
	}
	for i, v := range p.FieldList {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *Database) Field5DeepEqual(src *bool) bool {

	if p.PromptDisabled == src {
		return true
	} else if p.PromptDisabled == nil || src == nil {
		return false
	}
	if *p.PromptDisabled != *src {
		return false
	}
	return true
}

type FieldItem struct {
	Name         *string        `thrift:"name,1,optional" frugal:"1,optional,string" json:"name,omitempty"`
	Desc         *string        `thrift:"desc,2,optional" frugal:"2,optional,string" json:"desc,omitempty"`
	Type         *FieldItemType `thrift:"type,3,optional" frugal:"3,optional,FieldItemType" json:"type,omitempty"`
	MustRequired *bool          `thrift:"must_required,4,optional" frugal:"4,optional,bool" json:"must_required,omitempty"`
	Id           *int64         `thrift:"id,5,optional" frugal:"5,optional,i64" json:"id,omitempty"`
	TypeStr      *string        `thrift:"type_str,6,optional" frugal:"6,optional,string" json:"type_str,omitempty"`
}

func NewFieldItem() *FieldItem {
	return &FieldItem{}
}

func (p *FieldItem) InitDefault() {
}

var FieldItem_Name_DEFAULT string

func (p *FieldItem) GetName() (v string) {
	if !p.IsSetName() {
		return FieldItem_Name_DEFAULT
	}
	return *p.Name
}

var FieldItem_Desc_DEFAULT string

func (p *FieldItem) GetDesc() (v string) {
	if !p.IsSetDesc() {
		return FieldItem_Desc_DEFAULT
	}
	return *p.Desc
}

var FieldItem_Type_DEFAULT FieldItemType

func (p *FieldItem) GetType() (v FieldItemType) {
	if !p.IsSetType() {
		return FieldItem_Type_DEFAULT
	}
	return *p.Type
}

var FieldItem_MustRequired_DEFAULT bool

func (p *FieldItem) GetMustRequired() (v bool) {
	if !p.IsSetMustRequired() {
		return FieldItem_MustRequired_DEFAULT
	}
	return *p.MustRequired
}

var FieldItem_Id_DEFAULT int64

func (p *FieldItem) GetId() (v int64) {
	if !p.IsSetId() {
		return FieldItem_Id_DEFAULT
	}
	return *p.Id
}

var FieldItem_TypeStr_DEFAULT string

func (p *FieldItem) GetTypeStr() (v string) {
	if !p.IsSetTypeStr() {
		return FieldItem_TypeStr_DEFAULT
	}
	return *p.TypeStr
}
func (p *FieldItem) SetName(val *string) {
	p.Name = val
}
func (p *FieldItem) SetDesc(val *string) {
	p.Desc = val
}
func (p *FieldItem) SetType(val *FieldItemType) {
	p.Type = val
}
func (p *FieldItem) SetMustRequired(val *bool) {
	p.MustRequired = val
}
func (p *FieldItem) SetId(val *int64) {
	p.Id = val
}
func (p *FieldItem) SetTypeStr(val *string) {
	p.TypeStr = val
}

var fieldIDToName_FieldItem = map[int16]string{
	1: "name",
	2: "desc",
	3: "type",
	4: "must_required",
	5: "id",
	6: "type_str",
}

func (p *FieldItem) IsSetName() bool {
	return p.Name != nil
}

func (p *FieldItem) IsSetDesc() bool {
	return p.Desc != nil
}

func (p *FieldItem) IsSetType() bool {
	return p.Type != nil
}

func (p *FieldItem) IsSetMustRequired() bool {
	return p.MustRequired != nil
}

func (p *FieldItem) IsSetId() bool {
	return p.Id != nil
}

func (p *FieldItem) IsSetTypeStr() bool {
	return p.TypeStr != nil
}

func (p *FieldItem) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FieldItem[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *FieldItem) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Name = _field
	return nil
}
func (p *FieldItem) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Desc = _field
	return nil
}
func (p *FieldItem) ReadField3(iprot thrift.TProtocol) error {

	var _field *FieldItemType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := FieldItemType(v)
		_field = &tmp
	}
	p.Type = _field
	return nil
}
func (p *FieldItem) ReadField4(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.MustRequired = _field
	return nil
}
func (p *FieldItem) ReadField5(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Id = _field
	return nil
}
func (p *FieldItem) ReadField6(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.TypeStr = _field
	return nil
}

func (p *FieldItem) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("FieldItem"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *FieldItem) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetName() {
		if err = oprot.WriteFieldBegin("name", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Name); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *FieldItem) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetDesc() {
		if err = oprot.WriteFieldBegin("desc", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Desc); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *FieldItem) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetType() {
		if err = oprot.WriteFieldBegin("type", thrift.I32, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.Type)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *FieldItem) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetMustRequired() {
		if err = oprot.WriteFieldBegin("must_required", thrift.BOOL, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.MustRequired); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *FieldItem) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetId() {
		if err = oprot.WriteFieldBegin("id", thrift.I64, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.Id); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *FieldItem) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetTypeStr() {
		if err = oprot.WriteFieldBegin("type_str", thrift.STRING, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.TypeStr); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *FieldItem) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FieldItem(%+v)", *p)

}

func (p *FieldItem) DeepEqual(ano *FieldItem) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Name) {
		return false
	}
	if !p.Field2DeepEqual(ano.Desc) {
		return false
	}
	if !p.Field3DeepEqual(ano.Type) {
		return false
	}
	if !p.Field4DeepEqual(ano.MustRequired) {
		return false
	}
	if !p.Field5DeepEqual(ano.Id) {
		return false
	}
	if !p.Field6DeepEqual(ano.TypeStr) {
		return false
	}
	return true
}

func (p *FieldItem) Field1DeepEqual(src *string) bool {

	if p.Name == src {
		return true
	} else if p.Name == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Name, *src) != 0 {
		return false
	}
	return true
}
func (p *FieldItem) Field2DeepEqual(src *string) bool {

	if p.Desc == src {
		return true
	} else if p.Desc == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Desc, *src) != 0 {
		return false
	}
	return true
}
func (p *FieldItem) Field3DeepEqual(src *FieldItemType) bool {

	if p.Type == src {
		return true
	} else if p.Type == nil || src == nil {
		return false
	}
	if *p.Type != *src {
		return false
	}
	return true
}
func (p *FieldItem) Field4DeepEqual(src *bool) bool {

	if p.MustRequired == src {
		return true
	} else if p.MustRequired == nil || src == nil {
		return false
	}
	if *p.MustRequired != *src {
		return false
	}
	return true
}
func (p *FieldItem) Field5DeepEqual(src *int64) bool {

	if p.Id == src {
		return true
	} else if p.Id == nil || src == nil {
		return false
	}
	if *p.Id != *src {
		return false
	}
	return true
}
func (p *FieldItem) Field6DeepEqual(src *string) bool {

	if p.TypeStr == src {
		return true
	} else if p.TypeStr == nil || src == nil {
		return false
	}
	if strings.Compare(*p.TypeStr, *src) != 0 {
		return false
	}
	return true
}

type TimeCapsule struct {
	Mode                 *TimeCapsuleMode      `thrift:"mode,1,optional" frugal:"1,optional,TimeCapsuleMode" json:"mode,omitempty"`
	DisablePromptCalling *DisablePromptCalling `thrift:"disable_prompt_calling,2,optional" frugal:"2,optional,DisablePromptCalling" json:"disable_prompt_calling,omitempty"`
}

func NewTimeCapsule() *TimeCapsule {
	return &TimeCapsule{}
}

func (p *TimeCapsule) InitDefault() {
}

var TimeCapsule_Mode_DEFAULT TimeCapsuleMode

func (p *TimeCapsule) GetMode() (v TimeCapsuleMode) {
	if !p.IsSetMode() {
		return TimeCapsule_Mode_DEFAULT
	}
	return *p.Mode
}

var TimeCapsule_DisablePromptCalling_DEFAULT DisablePromptCalling

func (p *TimeCapsule) GetDisablePromptCalling() (v DisablePromptCalling) {
	if !p.IsSetDisablePromptCalling() {
		return TimeCapsule_DisablePromptCalling_DEFAULT
	}
	return *p.DisablePromptCalling
}
func (p *TimeCapsule) SetMode(val *TimeCapsuleMode) {
	p.Mode = val
}
func (p *TimeCapsule) SetDisablePromptCalling(val *DisablePromptCalling) {
	p.DisablePromptCalling = val
}

var fieldIDToName_TimeCapsule = map[int16]string{
	1: "mode",
	2: "disable_prompt_calling",
}

func (p *TimeCapsule) IsSetMode() bool {
	return p.Mode != nil
}

func (p *TimeCapsule) IsSetDisablePromptCalling() bool {
	return p.DisablePromptCalling != nil
}

func (p *TimeCapsule) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TimeCapsule[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *TimeCapsule) ReadField1(iprot thrift.TProtocol) error {

	var _field *TimeCapsuleMode
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := TimeCapsuleMode(v)
		_field = &tmp
	}
	p.Mode = _field
	return nil
}
func (p *TimeCapsule) ReadField2(iprot thrift.TProtocol) error {

	var _field *DisablePromptCalling
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := DisablePromptCalling(v)
		_field = &tmp
	}
	p.DisablePromptCalling = _field
	return nil
}

func (p *TimeCapsule) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("TimeCapsule"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TimeCapsule) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetMode() {
		if err = oprot.WriteFieldBegin("mode", thrift.I32, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.Mode)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *TimeCapsule) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetDisablePromptCalling() {
		if err = oprot.WriteFieldBegin("disable_prompt_calling", thrift.I32, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.DisablePromptCalling)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *TimeCapsule) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TimeCapsule(%+v)", *p)

}

func (p *TimeCapsule) DeepEqual(ano *TimeCapsule) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Mode) {
		return false
	}
	if !p.Field2DeepEqual(ano.DisablePromptCalling) {
		return false
	}
	return true
}

func (p *TimeCapsule) Field1DeepEqual(src *TimeCapsuleMode) bool {

	if p.Mode == src {
		return true
	} else if p.Mode == nil || src == nil {
		return false
	}
	if *p.Mode != *src {
		return false
	}
	return true
}
func (p *TimeCapsule) Field2DeepEqual(src *DisablePromptCalling) bool {

	if p.DisablePromptCalling == src {
		return true
	} else if p.DisablePromptCalling == nil || src == nil {
		return false
	}
	if *p.DisablePromptCalling != *src {
		return false
	}
	return true
}

type FileBox struct {
	SubApiList []*PluginAPI     `thrift:"sub_api_list,1,optional" frugal:"1,optional,list<PluginAPI>" json:"sub_api_list,omitempty"`
	Mode       *FileboxInfoMode `thrift:"mode,2,optional" frugal:"2,optional,FileboxInfoMode" json:"mode,omitempty"`
}

func NewFileBox() *FileBox {
	return &FileBox{}
}

func (p *FileBox) InitDefault() {
}

var FileBox_SubApiList_DEFAULT []*PluginAPI

func (p *FileBox) GetSubApiList() (v []*PluginAPI) {
	if !p.IsSetSubApiList() {
		return FileBox_SubApiList_DEFAULT
	}
	return p.SubApiList
}

var FileBox_Mode_DEFAULT FileboxInfoMode

func (p *FileBox) GetMode() (v FileboxInfoMode) {
	if !p.IsSetMode() {
		return FileBox_Mode_DEFAULT
	}
	return *p.Mode
}
func (p *FileBox) SetSubApiList(val []*PluginAPI) {
	p.SubApiList = val
}
func (p *FileBox) SetMode(val *FileboxInfoMode) {
	p.Mode = val
}

var fieldIDToName_FileBox = map[int16]string{
	1: "sub_api_list",
	2: "mode",
}

func (p *FileBox) IsSetSubApiList() bool {
	return p.SubApiList != nil
}

func (p *FileBox) IsSetMode() bool {
	return p.Mode != nil
}

func (p *FileBox) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FileBox[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *FileBox) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*PluginAPI, 0, size)
	values := make([]PluginAPI, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.SubApiList = _field
	return nil
}
func (p *FileBox) ReadField2(iprot thrift.TProtocol) error {

	var _field *FileboxInfoMode
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := FileboxInfoMode(v)
		_field = &tmp
	}
	p.Mode = _field
	return nil
}

func (p *FileBox) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("FileBox"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *FileBox) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetSubApiList() {
		if err = oprot.WriteFieldBegin("sub_api_list", thrift.LIST, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.SubApiList)); err != nil {
			return err
		}
		for _, v := range p.SubApiList {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *FileBox) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetMode() {
		if err = oprot.WriteFieldBegin("mode", thrift.I32, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.Mode)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *FileBox) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FileBox(%+v)", *p)

}

func (p *FileBox) DeepEqual(ano *FileBox) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SubApiList) {
		return false
	}
	if !p.Field2DeepEqual(ano.Mode) {
		return false
	}
	return true
}

func (p *FileBox) Field1DeepEqual(src []*PluginAPI) bool {

	if len(p.SubApiList) != len(src) {
		return false
	}
	for i, v := range p.SubApiList {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *FileBox) Field2DeepEqual(src *FileboxInfoMode) bool {

	if p.Mode == src {
		return true
	} else if p.Mode == nil || src == nil {
		return false
	}
	if *p.Mode != *src {
		return false
	}
	return true
}

type Trigger struct {
	AllowUserTask    *bool `thrift:"allow_user_task,1,optional" frugal:"1,optional,bool" json:"allow_user_task,omitempty"`
	EnablePresetTask *bool `thrift:"enable_preset_task,2,optional" frugal:"2,optional,bool" json:"enable_preset_task,omitempty"`
}

func NewTrigger() *Trigger {
	return &Trigger{}
}

func (p *Trigger) InitDefault() {
}

var Trigger_AllowUserTask_DEFAULT bool

func (p *Trigger) GetAllowUserTask() (v bool) {
	if !p.IsSetAllowUserTask() {
		return Trigger_AllowUserTask_DEFAULT
	}
	return *p.AllowUserTask
}

var Trigger_EnablePresetTask_DEFAULT bool

func (p *Trigger) GetEnablePresetTask() (v bool) {
	if !p.IsSetEnablePresetTask() {
		return Trigger_EnablePresetTask_DEFAULT
	}
	return *p.EnablePresetTask
}
func (p *Trigger) SetAllowUserTask(val *bool) {
	p.AllowUserTask = val
}
func (p *Trigger) SetEnablePresetTask(val *bool) {
	p.EnablePresetTask = val
}

var fieldIDToName_Trigger = map[int16]string{
	1: "allow_user_task",
	2: "enable_preset_task",
}

func (p *Trigger) IsSetAllowUserTask() bool {
	return p.AllowUserTask != nil
}

func (p *Trigger) IsSetEnablePresetTask() bool {
	return p.EnablePresetTask != nil
}

func (p *Trigger) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_Trigger[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *Trigger) ReadField1(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.AllowUserTask = _field
	return nil
}
func (p *Trigger) ReadField2(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.EnablePresetTask = _field
	return nil
}

func (p *Trigger) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("Trigger"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *Trigger) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetAllowUserTask() {
		if err = oprot.WriteFieldBegin("allow_user_task", thrift.BOOL, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.AllowUserTask); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *Trigger) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetEnablePresetTask() {
		if err = oprot.WriteFieldBegin("enable_preset_task", thrift.BOOL, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.EnablePresetTask); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *Trigger) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Trigger(%+v)", *p)

}

func (p *Trigger) DeepEqual(ano *Trigger) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.AllowUserTask) {
		return false
	}
	if !p.Field2DeepEqual(ano.EnablePresetTask) {
		return false
	}
	return true
}

func (p *Trigger) Field1DeepEqual(src *bool) bool {

	if p.AllowUserTask == src {
		return true
	} else if p.AllowUserTask == nil || src == nil {
		return false
	}
	if *p.AllowUserTask != *src {
		return false
	}
	return true
}
func (p *Trigger) Field2DeepEqual(src *bool) bool {

	if p.EnablePresetTask == src {
		return true
	} else if p.EnablePresetTask == nil || src == nil {
		return false
	}
	if *p.EnablePresetTask != *src {
		return false
	}
	return true
}

type Applet struct {
	BindingMp *bool `thrift:"binding_mp,1,optional" frugal:"1,optional,bool" json:"binding_mp,omitempty"`
}

func NewApplet() *Applet {
	return &Applet{}
}

func (p *Applet) InitDefault() {
}

var Applet_BindingMp_DEFAULT bool

func (p *Applet) GetBindingMp() (v bool) {
	if !p.IsSetBindingMp() {
		return Applet_BindingMp_DEFAULT
	}
	return *p.BindingMp
}
func (p *Applet) SetBindingMp(val *bool) {
	p.BindingMp = val
}

var fieldIDToName_Applet = map[int16]string{
	1: "binding_mp",
}

func (p *Applet) IsSetBindingMp() bool {
	return p.BindingMp != nil
}

func (p *Applet) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_Applet[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *Applet) ReadField1(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.BindingMp = _field
	return nil
}

func (p *Applet) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("Applet"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *Applet) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetBindingMp() {
		if err = oprot.WriteFieldBegin("binding_mp", thrift.BOOL, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.BindingMp); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *Applet) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Applet(%+v)", *p)

}

func (p *Applet) DeepEqual(ano *Applet) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.BindingMp) {
		return false
	}
	return true
}

func (p *Applet) Field1DeepEqual(src *bool) bool {

	if p.BindingMp == src {
		return true
	} else if p.BindingMp == nil || src == nil {
		return false
	}
	if *p.BindingMp != *src {
		return false
	}
	return true
}

type Suggest struct {
	ReplyMode        *SuggestReplyMode `thrift:"reply_mode,1,optional" frugal:"1,optional,SuggestReplyMode" json:"reply_mode,omitempty"`
	CustomizedPrompt *string           `thrift:"customized_prompt,2,optional" frugal:"2,optional,string" json:"customized_prompt,omitempty"`
	TaskName         *string           `thrift:"task_name,3,optional" frugal:"3,optional,string" json:"task_name,omitempty"`
}

func NewSuggest() *Suggest {
	return &Suggest{}
}

func (p *Suggest) InitDefault() {
}

var Suggest_ReplyMode_DEFAULT SuggestReplyMode

func (p *Suggest) GetReplyMode() (v SuggestReplyMode) {
	if !p.IsSetReplyMode() {
		return Suggest_ReplyMode_DEFAULT
	}
	return *p.ReplyMode
}

var Suggest_CustomizedPrompt_DEFAULT string

func (p *Suggest) GetCustomizedPrompt() (v string) {
	if !p.IsSetCustomizedPrompt() {
		return Suggest_CustomizedPrompt_DEFAULT
	}
	return *p.CustomizedPrompt
}

var Suggest_TaskName_DEFAULT string

func (p *Suggest) GetTaskName() (v string) {
	if !p.IsSetTaskName() {
		return Suggest_TaskName_DEFAULT
	}
	return *p.TaskName
}
func (p *Suggest) SetReplyMode(val *SuggestReplyMode) {
	p.ReplyMode = val
}
func (p *Suggest) SetCustomizedPrompt(val *string) {
	p.CustomizedPrompt = val
}
func (p *Suggest) SetTaskName(val *string) {
	p.TaskName = val
}

var fieldIDToName_Suggest = map[int16]string{
	1: "reply_mode",
	2: "customized_prompt",
	3: "task_name",
}

func (p *Suggest) IsSetReplyMode() bool {
	return p.ReplyMode != nil
}

func (p *Suggest) IsSetCustomizedPrompt() bool {
	return p.CustomizedPrompt != nil
}

func (p *Suggest) IsSetTaskName() bool {
	return p.TaskName != nil
}

func (p *Suggest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_Suggest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *Suggest) ReadField1(iprot thrift.TProtocol) error {

	var _field *SuggestReplyMode
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := SuggestReplyMode(v)
		_field = &tmp
	}
	p.ReplyMode = _field
	return nil
}
func (p *Suggest) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.CustomizedPrompt = _field
	return nil
}
func (p *Suggest) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.TaskName = _field
	return nil
}

func (p *Suggest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("Suggest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *Suggest) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetReplyMode() {
		if err = oprot.WriteFieldBegin("reply_mode", thrift.I32, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.ReplyMode)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *Suggest) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetCustomizedPrompt() {
		if err = oprot.WriteFieldBegin("customized_prompt", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.CustomizedPrompt); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *Suggest) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetTaskName() {
		if err = oprot.WriteFieldBegin("task_name", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.TaskName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *Suggest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Suggest(%+v)", *p)

}

func (p *Suggest) DeepEqual(ano *Suggest) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ReplyMode) {
		return false
	}
	if !p.Field2DeepEqual(ano.CustomizedPrompt) {
		return false
	}
	if !p.Field3DeepEqual(ano.TaskName) {
		return false
	}
	return true
}

func (p *Suggest) Field1DeepEqual(src *SuggestReplyMode) bool {

	if p.ReplyMode == src {
		return true
	} else if p.ReplyMode == nil || src == nil {
		return false
	}
	if *p.ReplyMode != *src {
		return false
	}
	return true
}
func (p *Suggest) Field2DeepEqual(src *string) bool {

	if p.CustomizedPrompt == src {
		return true
	} else if p.CustomizedPrompt == nil || src == nil {
		return false
	}
	if strings.Compare(*p.CustomizedPrompt, *src) != 0 {
		return false
	}
	return true
}
func (p *Suggest) Field3DeepEqual(src *string) bool {

	if p.TaskName == src {
		return true
	} else if p.TaskName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.TaskName, *src) != 0 {
		return false
	}
	return true
}

type LayoutInfo struct {
	WorkflowId *string `thrift:"workflow_id,1,optional" frugal:"1,optional,string" json:"workflow_id,omitempty"`
	PluginId   *string `thrift:"plugin_id,2,optional" frugal:"2,optional,string" json:"plugin_id,omitempty"`
}

func NewLayoutInfo() *LayoutInfo {
	return &LayoutInfo{}
}

func (p *LayoutInfo) InitDefault() {
}

var LayoutInfo_WorkflowId_DEFAULT string

func (p *LayoutInfo) GetWorkflowId() (v string) {
	if !p.IsSetWorkflowId() {
		return LayoutInfo_WorkflowId_DEFAULT
	}
	return *p.WorkflowId
}

var LayoutInfo_PluginId_DEFAULT string

func (p *LayoutInfo) GetPluginId() (v string) {
	if !p.IsSetPluginId() {
		return LayoutInfo_PluginId_DEFAULT
	}
	return *p.PluginId
}
func (p *LayoutInfo) SetWorkflowId(val *string) {
	p.WorkflowId = val
}
func (p *LayoutInfo) SetPluginId(val *string) {
	p.PluginId = val
}

var fieldIDToName_LayoutInfo = map[int16]string{
	1: "workflow_id",
	2: "plugin_id",
}

func (p *LayoutInfo) IsSetWorkflowId() bool {
	return p.WorkflowId != nil
}

func (p *LayoutInfo) IsSetPluginId() bool {
	return p.PluginId != nil
}

func (p *LayoutInfo) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_LayoutInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *LayoutInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.WorkflowId = _field
	return nil
}
func (p *LayoutInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PluginId = _field
	return nil
}

func (p *LayoutInfo) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("LayoutInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *LayoutInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetWorkflowId() {
		if err = oprot.WriteFieldBegin("workflow_id", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.WorkflowId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *LayoutInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetPluginId() {
		if err = oprot.WriteFieldBegin("plugin_id", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.PluginId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *LayoutInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("LayoutInfo(%+v)", *p)

}

func (p *LayoutInfo) DeepEqual(ano *LayoutInfo) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.WorkflowId) {
		return false
	}
	if !p.Field2DeepEqual(ano.PluginId) {
		return false
	}
	return true
}

func (p *LayoutInfo) Field1DeepEqual(src *string) bool {

	if p.WorkflowId == src {
		return true
	} else if p.WorkflowId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.WorkflowId, *src) != 0 {
		return false
	}
	return true
}
func (p *LayoutInfo) Field2DeepEqual(src *string) bool {

	if p.PluginId == src {
		return true
	} else if p.PluginId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.PluginId, *src) != 0 {
		return false
	}
	return true
}

type InterveneInfo struct {
	MatchConfig *InterveneMatchConfig `thrift:"match_config,1,optional" frugal:"1,optional,InterveneMatchConfig" json:"match_config,omitempty"`
	ActionList  []*InterveneAction    `thrift:"action_list,2,optional" frugal:"2,optional,list<InterveneAction>" json:"action_list,omitempty"`
}

func NewInterveneInfo() *InterveneInfo {
	return &InterveneInfo{}
}

func (p *InterveneInfo) InitDefault() {
}

var InterveneInfo_MatchConfig_DEFAULT *InterveneMatchConfig

func (p *InterveneInfo) GetMatchConfig() (v *InterveneMatchConfig) {
	if !p.IsSetMatchConfig() {
		return InterveneInfo_MatchConfig_DEFAULT
	}
	return p.MatchConfig
}

var InterveneInfo_ActionList_DEFAULT []*InterveneAction

func (p *InterveneInfo) GetActionList() (v []*InterveneAction) {
	if !p.IsSetActionList() {
		return InterveneInfo_ActionList_DEFAULT
	}
	return p.ActionList
}
func (p *InterveneInfo) SetMatchConfig(val *InterveneMatchConfig) {
	p.MatchConfig = val
}
func (p *InterveneInfo) SetActionList(val []*InterveneAction) {
	p.ActionList = val
}

var fieldIDToName_InterveneInfo = map[int16]string{
	1: "match_config",
	2: "action_list",
}

func (p *InterveneInfo) IsSetMatchConfig() bool {
	return p.MatchConfig != nil
}

func (p *InterveneInfo) IsSetActionList() bool {
	return p.ActionList != nil
}

func (p *InterveneInfo) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_InterveneInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *InterveneInfo) ReadField1(iprot thrift.TProtocol) error {
	_field := NewInterveneMatchConfig()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.MatchConfig = _field
	return nil
}
func (p *InterveneInfo) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*InterveneAction, 0, size)
	values := make([]InterveneAction, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ActionList = _field
	return nil
}

func (p *InterveneInfo) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("InterveneInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *InterveneInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetMatchConfig() {
		if err = oprot.WriteFieldBegin("match_config", thrift.STRUCT, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.MatchConfig.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *InterveneInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetActionList() {
		if err = oprot.WriteFieldBegin("action_list", thrift.LIST, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ActionList)); err != nil {
			return err
		}
		for _, v := range p.ActionList {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *InterveneInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("InterveneInfo(%+v)", *p)

}

func (p *InterveneInfo) DeepEqual(ano *InterveneInfo) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.MatchConfig) {
		return false
	}
	if !p.Field2DeepEqual(ano.ActionList) {
		return false
	}
	return true
}

func (p *InterveneInfo) Field1DeepEqual(src *InterveneMatchConfig) bool {

	if !p.MatchConfig.DeepEqual(src) {
		return false
	}
	return true
}
func (p *InterveneInfo) Field2DeepEqual(src []*InterveneAction) bool {

	if len(p.ActionList) != len(src) {
		return false
	}
	for i, v := range p.ActionList {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type InterveneMatchConfig struct {
	ItemList   []*InterveneMatchItem `thrift:"item_list,1,optional" frugal:"1,optional,list<InterveneMatchItem>" json:"item_list,omitempty"`
	RelationOp *RelationOP           `thrift:"relation_op,2,optional" frugal:"2,optional,RelationOP" json:"relation_op,omitempty"`
}

func NewInterveneMatchConfig() *InterveneMatchConfig {
	return &InterveneMatchConfig{}
}

func (p *InterveneMatchConfig) InitDefault() {
}

var InterveneMatchConfig_ItemList_DEFAULT []*InterveneMatchItem

func (p *InterveneMatchConfig) GetItemList() (v []*InterveneMatchItem) {
	if !p.IsSetItemList() {
		return InterveneMatchConfig_ItemList_DEFAULT
	}
	return p.ItemList
}

var InterveneMatchConfig_RelationOp_DEFAULT RelationOP

func (p *InterveneMatchConfig) GetRelationOp() (v RelationOP) {
	if !p.IsSetRelationOp() {
		return InterveneMatchConfig_RelationOp_DEFAULT
	}
	return *p.RelationOp
}
func (p *InterveneMatchConfig) SetItemList(val []*InterveneMatchItem) {
	p.ItemList = val
}
func (p *InterveneMatchConfig) SetRelationOp(val *RelationOP) {
	p.RelationOp = val
}

var fieldIDToName_InterveneMatchConfig = map[int16]string{
	1: "item_list",
	2: "relation_op",
}

func (p *InterveneMatchConfig) IsSetItemList() bool {
	return p.ItemList != nil
}

func (p *InterveneMatchConfig) IsSetRelationOp() bool {
	return p.RelationOp != nil
}

func (p *InterveneMatchConfig) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_InterveneMatchConfig[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *InterveneMatchConfig) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*InterveneMatchItem, 0, size)
	values := make([]InterveneMatchItem, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ItemList = _field
	return nil
}
func (p *InterveneMatchConfig) ReadField2(iprot thrift.TProtocol) error {

	var _field *RelationOP
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := RelationOP(v)
		_field = &tmp
	}
	p.RelationOp = _field
	return nil
}

func (p *InterveneMatchConfig) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("InterveneMatchConfig"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *InterveneMatchConfig) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetItemList() {
		if err = oprot.WriteFieldBegin("item_list", thrift.LIST, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ItemList)); err != nil {
			return err
		}
		for _, v := range p.ItemList {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *InterveneMatchConfig) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetRelationOp() {
		if err = oprot.WriteFieldBegin("relation_op", thrift.I32, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.RelationOp)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *InterveneMatchConfig) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("InterveneMatchConfig(%+v)", *p)

}

func (p *InterveneMatchConfig) DeepEqual(ano *InterveneMatchConfig) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ItemList) {
		return false
	}
	if !p.Field2DeepEqual(ano.RelationOp) {
		return false
	}
	return true
}

func (p *InterveneMatchConfig) Field1DeepEqual(src []*InterveneMatchItem) bool {

	if len(p.ItemList) != len(src) {
		return false
	}
	for i, v := range p.ItemList {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *InterveneMatchConfig) Field2DeepEqual(src *RelationOP) bool {

	if p.RelationOp == src {
		return true
	} else if p.RelationOp == nil || src == nil {
		return false
	}
	if *p.RelationOp != *src {
		return false
	}
	return true
}

type InterveneMatchItem struct {
	MatchType  *InterveneMatchType `thrift:"match_type,1,optional" frugal:"1,optional,InterveneMatchType" json:"match_type,omitempty"`
	MatchList  []string            `thrift:"match_list,2,optional" frugal:"2,optional,list<string>" json:"match_list,omitempty"`
	RelationOp *RelationOP         `thrift:"relation_op,3,optional" frugal:"3,optional,RelationOP" json:"relation_op,omitempty"`
}

func NewInterveneMatchItem() *InterveneMatchItem {
	return &InterveneMatchItem{}
}

func (p *InterveneMatchItem) InitDefault() {
}

var InterveneMatchItem_MatchType_DEFAULT InterveneMatchType

func (p *InterveneMatchItem) GetMatchType() (v InterveneMatchType) {
	if !p.IsSetMatchType() {
		return InterveneMatchItem_MatchType_DEFAULT
	}
	return *p.MatchType
}

var InterveneMatchItem_MatchList_DEFAULT []string

func (p *InterveneMatchItem) GetMatchList() (v []string) {
	if !p.IsSetMatchList() {
		return InterveneMatchItem_MatchList_DEFAULT
	}
	return p.MatchList
}

var InterveneMatchItem_RelationOp_DEFAULT RelationOP

func (p *InterveneMatchItem) GetRelationOp() (v RelationOP) {
	if !p.IsSetRelationOp() {
		return InterveneMatchItem_RelationOp_DEFAULT
	}
	return *p.RelationOp
}
func (p *InterveneMatchItem) SetMatchType(val *InterveneMatchType) {
	p.MatchType = val
}
func (p *InterveneMatchItem) SetMatchList(val []string) {
	p.MatchList = val
}
func (p *InterveneMatchItem) SetRelationOp(val *RelationOP) {
	p.RelationOp = val
}

var fieldIDToName_InterveneMatchItem = map[int16]string{
	1: "match_type",
	2: "match_list",
	3: "relation_op",
}

func (p *InterveneMatchItem) IsSetMatchType() bool {
	return p.MatchType != nil
}

func (p *InterveneMatchItem) IsSetMatchList() bool {
	return p.MatchList != nil
}

func (p *InterveneMatchItem) IsSetRelationOp() bool {
	return p.RelationOp != nil
}

func (p *InterveneMatchItem) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_InterveneMatchItem[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *InterveneMatchItem) ReadField1(iprot thrift.TProtocol) error {

	var _field *InterveneMatchType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := InterveneMatchType(v)
		_field = &tmp
	}
	p.MatchType = _field
	return nil
}
func (p *InterveneMatchItem) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.MatchList = _field
	return nil
}
func (p *InterveneMatchItem) ReadField3(iprot thrift.TProtocol) error {

	var _field *RelationOP
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := RelationOP(v)
		_field = &tmp
	}
	p.RelationOp = _field
	return nil
}

func (p *InterveneMatchItem) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("InterveneMatchItem"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *InterveneMatchItem) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetMatchType() {
		if err = oprot.WriteFieldBegin("match_type", thrift.I32, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.MatchType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *InterveneMatchItem) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetMatchList() {
		if err = oprot.WriteFieldBegin("match_list", thrift.LIST, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.MatchList)); err != nil {
			return err
		}
		for _, v := range p.MatchList {
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *InterveneMatchItem) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetRelationOp() {
		if err = oprot.WriteFieldBegin("relation_op", thrift.I32, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.RelationOp)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *InterveneMatchItem) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("InterveneMatchItem(%+v)", *p)

}

func (p *InterveneMatchItem) DeepEqual(ano *InterveneMatchItem) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.MatchType) {
		return false
	}
	if !p.Field2DeepEqual(ano.MatchList) {
		return false
	}
	if !p.Field3DeepEqual(ano.RelationOp) {
		return false
	}
	return true
}

func (p *InterveneMatchItem) Field1DeepEqual(src *InterveneMatchType) bool {

	if p.MatchType == src {
		return true
	} else if p.MatchType == nil || src == nil {
		return false
	}
	if *p.MatchType != *src {
		return false
	}
	return true
}
func (p *InterveneMatchItem) Field2DeepEqual(src []string) bool {

	if len(p.MatchList) != len(src) {
		return false
	}
	for i, v := range p.MatchList {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *InterveneMatchItem) Field3DeepEqual(src *RelationOP) bool {

	if p.RelationOp == src {
		return true
	} else if p.RelationOp == nil || src == nil {
		return false
	}
	if *p.RelationOp != *src {
		return false
	}
	return true
}

type InterveneAction struct {
	ActionType  *InterveneActionType  `thrift:"action_type,1,optional" frugal:"1,optional,InterveneActionType" json:"action_type,omitempty"`
	ActionScope *InterveneActionScope `thrift:"action_scope,2,optional" frugal:"2,optional,InterveneActionScope" json:"action_scope,omitempty"`
	AgentIdList []int64               `thrift:"agent_id_list,3,optional" frugal:"3,optional,list<i64>" json:"agent_id_list,omitempty"`
	ToolFlow    *ToolFlow             `thrift:"tool_flow,4,optional" frugal:"4,optional,ToolFlow" json:"tool_flow,omitempty"`
	ToolList    []*PluginAPI          `thrift:"tool_list,5,optional" frugal:"5,optional,list<PluginAPI>" json:"tool_list,omitempty"`
}

func NewInterveneAction() *InterveneAction {
	return &InterveneAction{}
}

func (p *InterveneAction) InitDefault() {
}

var InterveneAction_ActionType_DEFAULT InterveneActionType

func (p *InterveneAction) GetActionType() (v InterveneActionType) {
	if !p.IsSetActionType() {
		return InterveneAction_ActionType_DEFAULT
	}
	return *p.ActionType
}

var InterveneAction_ActionScope_DEFAULT InterveneActionScope

func (p *InterveneAction) GetActionScope() (v InterveneActionScope) {
	if !p.IsSetActionScope() {
		return InterveneAction_ActionScope_DEFAULT
	}
	return *p.ActionScope
}

var InterveneAction_AgentIdList_DEFAULT []int64

func (p *InterveneAction) GetAgentIdList() (v []int64) {
	if !p.IsSetAgentIdList() {
		return InterveneAction_AgentIdList_DEFAULT
	}
	return p.AgentIdList
}

var InterveneAction_ToolFlow_DEFAULT *ToolFlow

func (p *InterveneAction) GetToolFlow() (v *ToolFlow) {
	if !p.IsSetToolFlow() {
		return InterveneAction_ToolFlow_DEFAULT
	}
	return p.ToolFlow
}

var InterveneAction_ToolList_DEFAULT []*PluginAPI

func (p *InterveneAction) GetToolList() (v []*PluginAPI) {
	if !p.IsSetToolList() {
		return InterveneAction_ToolList_DEFAULT
	}
	return p.ToolList
}
func (p *InterveneAction) SetActionType(val *InterveneActionType) {
	p.ActionType = val
}
func (p *InterveneAction) SetActionScope(val *InterveneActionScope) {
	p.ActionScope = val
}
func (p *InterveneAction) SetAgentIdList(val []int64) {
	p.AgentIdList = val
}
func (p *InterveneAction) SetToolFlow(val *ToolFlow) {
	p.ToolFlow = val
}
func (p *InterveneAction) SetToolList(val []*PluginAPI) {
	p.ToolList = val
}

var fieldIDToName_InterveneAction = map[int16]string{
	1: "action_type",
	2: "action_scope",
	3: "agent_id_list",
	4: "tool_flow",
	5: "tool_list",
}

func (p *InterveneAction) IsSetActionType() bool {
	return p.ActionType != nil
}

func (p *InterveneAction) IsSetActionScope() bool {
	return p.ActionScope != nil
}

func (p *InterveneAction) IsSetAgentIdList() bool {
	return p.AgentIdList != nil
}

func (p *InterveneAction) IsSetToolFlow() bool {
	return p.ToolFlow != nil
}

func (p *InterveneAction) IsSetToolList() bool {
	return p.ToolList != nil
}

func (p *InterveneAction) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_InterveneAction[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *InterveneAction) ReadField1(iprot thrift.TProtocol) error {

	var _field *InterveneActionType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := InterveneActionType(v)
		_field = &tmp
	}
	p.ActionType = _field
	return nil
}
func (p *InterveneAction) ReadField2(iprot thrift.TProtocol) error {

	var _field *InterveneActionScope
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := InterveneActionScope(v)
		_field = &tmp
	}
	p.ActionScope = _field
	return nil
}
func (p *InterveneAction) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]int64, 0, size)
	for i := 0; i < size; i++ {

		var _elem int64
		if v, err := iprot.ReadI64(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.AgentIdList = _field
	return nil
}
func (p *InterveneAction) ReadField4(iprot thrift.TProtocol) error {
	_field := NewToolFlow()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.ToolFlow = _field
	return nil
}
func (p *InterveneAction) ReadField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*PluginAPI, 0, size)
	values := make([]PluginAPI, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ToolList = _field
	return nil
}

func (p *InterveneAction) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("InterveneAction"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *InterveneAction) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetActionType() {
		if err = oprot.WriteFieldBegin("action_type", thrift.I32, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.ActionType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *InterveneAction) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetActionScope() {
		if err = oprot.WriteFieldBegin("action_scope", thrift.I32, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.ActionScope)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *InterveneAction) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetAgentIdList() {
		if err = oprot.WriteFieldBegin("agent_id_list", thrift.LIST, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.I64, len(p.AgentIdList)); err != nil {
			return err
		}
		for _, v := range p.AgentIdList {
			if err := oprot.WriteI64(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *InterveneAction) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetToolFlow() {
		if err = oprot.WriteFieldBegin("tool_flow", thrift.STRUCT, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.ToolFlow.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *InterveneAction) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetToolList() {
		if err = oprot.WriteFieldBegin("tool_list", thrift.LIST, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ToolList)); err != nil {
			return err
		}
		for _, v := range p.ToolList {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *InterveneAction) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("InterveneAction(%+v)", *p)

}

func (p *InterveneAction) DeepEqual(ano *InterveneAction) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ActionType) {
		return false
	}
	if !p.Field2DeepEqual(ano.ActionScope) {
		return false
	}
	if !p.Field3DeepEqual(ano.AgentIdList) {
		return false
	}
	if !p.Field4DeepEqual(ano.ToolFlow) {
		return false
	}
	if !p.Field5DeepEqual(ano.ToolList) {
		return false
	}
	return true
}

func (p *InterveneAction) Field1DeepEqual(src *InterveneActionType) bool {

	if p.ActionType == src {
		return true
	} else if p.ActionType == nil || src == nil {
		return false
	}
	if *p.ActionType != *src {
		return false
	}
	return true
}
func (p *InterveneAction) Field2DeepEqual(src *InterveneActionScope) bool {

	if p.ActionScope == src {
		return true
	} else if p.ActionScope == nil || src == nil {
		return false
	}
	if *p.ActionScope != *src {
		return false
	}
	return true
}
func (p *InterveneAction) Field3DeepEqual(src []int64) bool {

	if len(p.AgentIdList) != len(src) {
		return false
	}
	for i, v := range p.AgentIdList {
		_src := src[i]
		if v != _src {
			return false
		}
	}
	return true
}
func (p *InterveneAction) Field4DeepEqual(src *ToolFlow) bool {

	if !p.ToolFlow.DeepEqual(src) {
		return false
	}
	return true
}
func (p *InterveneAction) Field5DeepEqual(src []*PluginAPI) bool {

	if len(p.ToolList) != len(src) {
		return false
	}
	for i, v := range p.ToolList {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type ToolFlow struct {
	PluginId  int64  `thrift:"plugin_id,1" frugal:"1,default,i64" json:"plugin_id"`
	ApiName   string `thrift:"api_name,2" frugal:"2,default,string" json:"api_name"`
	Arguments string `thrift:"arguments,3" frugal:"3,default,string" json:"arguments"`
}

func NewToolFlow() *ToolFlow {
	return &ToolFlow{}
}

func (p *ToolFlow) InitDefault() {
}

func (p *ToolFlow) GetPluginId() (v int64) {
	return p.PluginId
}

func (p *ToolFlow) GetApiName() (v string) {
	return p.ApiName
}

func (p *ToolFlow) GetArguments() (v string) {
	return p.Arguments
}
func (p *ToolFlow) SetPluginId(val int64) {
	p.PluginId = val
}
func (p *ToolFlow) SetApiName(val string) {
	p.ApiName = val
}
func (p *ToolFlow) SetArguments(val string) {
	p.Arguments = val
}

var fieldIDToName_ToolFlow = map[int16]string{
	1: "plugin_id",
	2: "api_name",
	3: "arguments",
}

func (p *ToolFlow) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ToolFlow[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ToolFlow) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.PluginId = _field
	return nil
}
func (p *ToolFlow) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ApiName = _field
	return nil
}
func (p *ToolFlow) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Arguments = _field
	return nil
}

func (p *ToolFlow) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ToolFlow"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ToolFlow) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("plugin_id", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.PluginId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ToolFlow) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("api_name", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ApiName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ToolFlow) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("arguments", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Arguments); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ToolFlow) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ToolFlow(%+v)", *p)

}

func (p *ToolFlow) DeepEqual(ano *ToolFlow) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.PluginId) {
		return false
	}
	if !p.Field2DeepEqual(ano.ApiName) {
		return false
	}
	if !p.Field3DeepEqual(ano.Arguments) {
		return false
	}
	return true
}

func (p *ToolFlow) Field1DeepEqual(src int64) bool {

	if p.PluginId != src {
		return false
	}
	return true
}
func (p *ToolFlow) Field2DeepEqual(src string) bool {

	if strings.Compare(p.ApiName, src) != 0 {
		return false
	}
	return true
}
func (p *ToolFlow) Field3DeepEqual(src string) bool {

	if strings.Compare(p.Arguments, src) != 0 {
		return false
	}
	return true
}

type GetBotInfoReq struct {
	ConnectorId     int64         `thrift:"connector_id,1,required" frugal:"1,required,i64" json:"connector_id"`
	BotId           int64         `thrift:"bot_id,2,required" frugal:"2,required,i64" json:"bot_id"`
	Version         *string       `thrift:"version,3,optional" frugal:"3,optional,string" json:"version,omitempty"`
	BotMode         *BotMode      `thrift:"bot_mode,4,optional" frugal:"4,optional,BotMode" json:"bot_mode,omitempty"`
	VersionTypeList []VersionType `thrift:"version_type_list,5,optional" frugal:"5,optional,list<VersionType>" json:"version_type_list,omitempty"`
	Base            *base.Base    `thrift:"Base,255" frugal:"255,default,base.Base" json:"Base"`
}

func NewGetBotInfoReq() *GetBotInfoReq {
	return &GetBotInfoReq{}
}

func (p *GetBotInfoReq) InitDefault() {
}

func (p *GetBotInfoReq) GetConnectorId() (v int64) {
	return p.ConnectorId
}

func (p *GetBotInfoReq) GetBotId() (v int64) {
	return p.BotId
}

var GetBotInfoReq_Version_DEFAULT string

func (p *GetBotInfoReq) GetVersion() (v string) {
	if !p.IsSetVersion() {
		return GetBotInfoReq_Version_DEFAULT
	}
	return *p.Version
}

var GetBotInfoReq_BotMode_DEFAULT BotMode

func (p *GetBotInfoReq) GetBotMode() (v BotMode) {
	if !p.IsSetBotMode() {
		return GetBotInfoReq_BotMode_DEFAULT
	}
	return *p.BotMode
}

var GetBotInfoReq_VersionTypeList_DEFAULT []VersionType

func (p *GetBotInfoReq) GetVersionTypeList() (v []VersionType) {
	if !p.IsSetVersionTypeList() {
		return GetBotInfoReq_VersionTypeList_DEFAULT
	}
	return p.VersionTypeList
}

var GetBotInfoReq_Base_DEFAULT *base.Base

func (p *GetBotInfoReq) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return GetBotInfoReq_Base_DEFAULT
	}
	return p.Base
}
func (p *GetBotInfoReq) SetConnectorId(val int64) {
	p.ConnectorId = val
}
func (p *GetBotInfoReq) SetBotId(val int64) {
	p.BotId = val
}
func (p *GetBotInfoReq) SetVersion(val *string) {
	p.Version = val
}
func (p *GetBotInfoReq) SetBotMode(val *BotMode) {
	p.BotMode = val
}
func (p *GetBotInfoReq) SetVersionTypeList(val []VersionType) {
	p.VersionTypeList = val
}
func (p *GetBotInfoReq) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_GetBotInfoReq = map[int16]string{
	1:   "connector_id",
	2:   "bot_id",
	3:   "version",
	4:   "bot_mode",
	5:   "version_type_list",
	255: "Base",
}

func (p *GetBotInfoReq) IsSetVersion() bool {
	return p.Version != nil
}

func (p *GetBotInfoReq) IsSetBotMode() bool {
	return p.BotMode != nil
}

func (p *GetBotInfoReq) IsSetVersionTypeList() bool {
	return p.VersionTypeList != nil
}

func (p *GetBotInfoReq) IsSetBase() bool {
	return p.Base != nil
}

func (p *GetBotInfoReq) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetConnectorId bool = false
	var issetBotId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetConnectorId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetBotId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetConnectorId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetBotId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetBotInfoReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_GetBotInfoReq[fieldId]))
}

func (p *GetBotInfoReq) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ConnectorId = _field
	return nil
}
func (p *GetBotInfoReq) ReadField2(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BotId = _field
	return nil
}
func (p *GetBotInfoReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Version = _field
	return nil
}
func (p *GetBotInfoReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *BotMode
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := BotMode(v)
		_field = &tmp
	}
	p.BotMode = _field
	return nil
}
func (p *GetBotInfoReq) ReadField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]VersionType, 0, size)
	for i := 0; i < size; i++ {

		var _elem VersionType
		if v, err := iprot.ReadI32(); err != nil {
			return err
		} else {
			_elem = VersionType(v)
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.VersionTypeList = _field
	return nil
}
func (p *GetBotInfoReq) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *GetBotInfoReq) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetBotInfoReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetBotInfoReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("connector_id", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.ConnectorId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *GetBotInfoReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("bot_id", thrift.I64, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.BotId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *GetBotInfoReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetVersion() {
		if err = oprot.WriteFieldBegin("version", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Version); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *GetBotInfoReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetBotMode() {
		if err = oprot.WriteFieldBegin("bot_mode", thrift.I32, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.BotMode)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *GetBotInfoReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetVersionTypeList() {
		if err = oprot.WriteFieldBegin("version_type_list", thrift.LIST, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.VersionTypeList)); err != nil {
			return err
		}
		for _, v := range p.VersionTypeList {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *GetBotInfoReq) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Base.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *GetBotInfoReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetBotInfoReq(%+v)", *p)

}

func (p *GetBotInfoReq) DeepEqual(ano *GetBotInfoReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ConnectorId) {
		return false
	}
	if !p.Field2DeepEqual(ano.BotId) {
		return false
	}
	if !p.Field3DeepEqual(ano.Version) {
		return false
	}
	if !p.Field4DeepEqual(ano.BotMode) {
		return false
	}
	if !p.Field5DeepEqual(ano.VersionTypeList) {
		return false
	}
	if !p.Field255DeepEqual(ano.Base) {
		return false
	}
	return true
}

func (p *GetBotInfoReq) Field1DeepEqual(src int64) bool {

	if p.ConnectorId != src {
		return false
	}
	return true
}
func (p *GetBotInfoReq) Field2DeepEqual(src int64) bool {

	if p.BotId != src {
		return false
	}
	return true
}
func (p *GetBotInfoReq) Field3DeepEqual(src *string) bool {

	if p.Version == src {
		return true
	} else if p.Version == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Version, *src) != 0 {
		return false
	}
	return true
}
func (p *GetBotInfoReq) Field4DeepEqual(src *BotMode) bool {

	if p.BotMode == src {
		return true
	} else if p.BotMode == nil || src == nil {
		return false
	}
	if *p.BotMode != *src {
		return false
	}
	return true
}
func (p *GetBotInfoReq) Field5DeepEqual(src []VersionType) bool {

	if len(p.VersionTypeList) != len(src) {
		return false
	}
	for i, v := range p.VersionTypeList {
		_src := src[i]
		if v != _src {
			return false
		}
	}
	return true
}
func (p *GetBotInfoReq) Field255DeepEqual(src *base.Base) bool {

	if !p.Base.DeepEqual(src) {
		return false
	}
	return true
}

type GetBotInfoResp struct {
	Bot      *Bot           `thrift:"bot,1" frugal:"1,default,Bot" json:"bot"`
	BaseResp *base.BaseResp `thrift:"BaseResp,255" frugal:"255,default,base.BaseResp" json:"BaseResp"`
}

func NewGetBotInfoResp() *GetBotInfoResp {
	return &GetBotInfoResp{}
}

func (p *GetBotInfoResp) InitDefault() {
}

var GetBotInfoResp_Bot_DEFAULT *Bot

func (p *GetBotInfoResp) GetBot() (v *Bot) {
	if !p.IsSetBot() {
		return GetBotInfoResp_Bot_DEFAULT
	}
	return p.Bot
}

var GetBotInfoResp_BaseResp_DEFAULT *base.BaseResp

func (p *GetBotInfoResp) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return GetBotInfoResp_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *GetBotInfoResp) SetBot(val *Bot) {
	p.Bot = val
}
func (p *GetBotInfoResp) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_GetBotInfoResp = map[int16]string{
	1:   "bot",
	255: "BaseResp",
}

func (p *GetBotInfoResp) IsSetBot() bool {
	return p.Bot != nil
}

func (p *GetBotInfoResp) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *GetBotInfoResp) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetBotInfoResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *GetBotInfoResp) ReadField1(iprot thrift.TProtocol) error {
	_field := NewBot()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Bot = _field
	return nil
}
func (p *GetBotInfoResp) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *GetBotInfoResp) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetBotInfoResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetBotInfoResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("bot", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Bot.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *GetBotInfoResp) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.BaseResp.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *GetBotInfoResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetBotInfoResp(%+v)", *p)

}

func (p *GetBotInfoResp) DeepEqual(ano *GetBotInfoResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Bot) {
		return false
	}
	if !p.Field255DeepEqual(ano.BaseResp) {
		return false
	}
	return true
}

func (p *GetBotInfoResp) Field1DeepEqual(src *Bot) bool {

	if !p.Bot.DeepEqual(src) {
		return false
	}
	return true
}
func (p *GetBotInfoResp) Field255DeepEqual(src *base.BaseResp) bool {

	if !p.BaseResp.DeepEqual(src) {
		return false
	}
	return true
}

type CreateBotInfoReq struct {
	ConnectorIdList []int64    `thrift:"connector_id_list,1,required" frugal:"1,required,list<i64>" json:"connector_id_list"`
	Bot             *Bot       `thrift:"bot,2,required" frugal:"2,required,Bot" json:"bot"`
	Base            *base.Base `thrift:"Base,255" frugal:"255,default,base.Base" json:"Base"`
}

func NewCreateBotInfoReq() *CreateBotInfoReq {
	return &CreateBotInfoReq{}
}

func (p *CreateBotInfoReq) InitDefault() {
}

func (p *CreateBotInfoReq) GetConnectorIdList() (v []int64) {
	return p.ConnectorIdList
}

var CreateBotInfoReq_Bot_DEFAULT *Bot

func (p *CreateBotInfoReq) GetBot() (v *Bot) {
	if !p.IsSetBot() {
		return CreateBotInfoReq_Bot_DEFAULT
	}
	return p.Bot
}

var CreateBotInfoReq_Base_DEFAULT *base.Base

func (p *CreateBotInfoReq) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return CreateBotInfoReq_Base_DEFAULT
	}
	return p.Base
}
func (p *CreateBotInfoReq) SetConnectorIdList(val []int64) {
	p.ConnectorIdList = val
}
func (p *CreateBotInfoReq) SetBot(val *Bot) {
	p.Bot = val
}
func (p *CreateBotInfoReq) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_CreateBotInfoReq = map[int16]string{
	1:   "connector_id_list",
	2:   "bot",
	255: "Base",
}

func (p *CreateBotInfoReq) IsSetBot() bool {
	return p.Bot != nil
}

func (p *CreateBotInfoReq) IsSetBase() bool {
	return p.Base != nil
}

func (p *CreateBotInfoReq) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetConnectorIdList bool = false
	var issetBot bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetConnectorIdList = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetBot = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetConnectorIdList {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetBot {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateBotInfoReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateBotInfoReq[fieldId]))
}

func (p *CreateBotInfoReq) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]int64, 0, size)
	for i := 0; i < size; i++ {

		var _elem int64
		if v, err := iprot.ReadI64(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ConnectorIdList = _field
	return nil
}
func (p *CreateBotInfoReq) ReadField2(iprot thrift.TProtocol) error {
	_field := NewBot()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Bot = _field
	return nil
}
func (p *CreateBotInfoReq) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *CreateBotInfoReq) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("CreateBotInfoReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateBotInfoReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("connector_id_list", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.I64, len(p.ConnectorIdList)); err != nil {
		return err
	}
	for _, v := range p.ConnectorIdList {
		if err := oprot.WriteI64(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *CreateBotInfoReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("bot", thrift.STRUCT, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Bot.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *CreateBotInfoReq) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Base.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *CreateBotInfoReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateBotInfoReq(%+v)", *p)

}

func (p *CreateBotInfoReq) DeepEqual(ano *CreateBotInfoReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ConnectorIdList) {
		return false
	}
	if !p.Field2DeepEqual(ano.Bot) {
		return false
	}
	if !p.Field255DeepEqual(ano.Base) {
		return false
	}
	return true
}

func (p *CreateBotInfoReq) Field1DeepEqual(src []int64) bool {

	if len(p.ConnectorIdList) != len(src) {
		return false
	}
	for i, v := range p.ConnectorIdList {
		_src := src[i]
		if v != _src {
			return false
		}
	}
	return true
}
func (p *CreateBotInfoReq) Field2DeepEqual(src *Bot) bool {

	if !p.Bot.DeepEqual(src) {
		return false
	}
	return true
}
func (p *CreateBotInfoReq) Field255DeepEqual(src *base.Base) bool {

	if !p.Base.DeepEqual(src) {
		return false
	}
	return true
}

type CreateBotInfoResp struct {
	BotId    int64          `thrift:"bot_id,1" frugal:"1,default,i64" json:"bot_id"`
	Version  string         `thrift:"version,2" frugal:"2,default,string" json:"version"`
	BotList  []*Bot         `thrift:"bot_list,3" frugal:"3,default,list<Bot>" json:"bot_list"`
	BaseResp *base.BaseResp `thrift:"BaseResp,255" frugal:"255,default,base.BaseResp" json:"BaseResp"`
}

func NewCreateBotInfoResp() *CreateBotInfoResp {
	return &CreateBotInfoResp{}
}

func (p *CreateBotInfoResp) InitDefault() {
}

func (p *CreateBotInfoResp) GetBotId() (v int64) {
	return p.BotId
}

func (p *CreateBotInfoResp) GetVersion() (v string) {
	return p.Version
}

func (p *CreateBotInfoResp) GetBotList() (v []*Bot) {
	return p.BotList
}

var CreateBotInfoResp_BaseResp_DEFAULT *base.BaseResp

func (p *CreateBotInfoResp) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return CreateBotInfoResp_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *CreateBotInfoResp) SetBotId(val int64) {
	p.BotId = val
}
func (p *CreateBotInfoResp) SetVersion(val string) {
	p.Version = val
}
func (p *CreateBotInfoResp) SetBotList(val []*Bot) {
	p.BotList = val
}
func (p *CreateBotInfoResp) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_CreateBotInfoResp = map[int16]string{
	1:   "bot_id",
	2:   "version",
	3:   "bot_list",
	255: "BaseResp",
}

func (p *CreateBotInfoResp) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *CreateBotInfoResp) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateBotInfoResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *CreateBotInfoResp) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BotId = _field
	return nil
}
func (p *CreateBotInfoResp) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Version = _field
	return nil
}
func (p *CreateBotInfoResp) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*Bot, 0, size)
	values := make([]Bot, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.BotList = _field
	return nil
}
func (p *CreateBotInfoResp) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *CreateBotInfoResp) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("CreateBotInfoResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateBotInfoResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("bot_id", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.BotId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *CreateBotInfoResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("version", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Version); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *CreateBotInfoResp) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("bot_list", thrift.LIST, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.BotList)); err != nil {
		return err
	}
	for _, v := range p.BotList {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *CreateBotInfoResp) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.BaseResp.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *CreateBotInfoResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateBotInfoResp(%+v)", *p)

}

func (p *CreateBotInfoResp) DeepEqual(ano *CreateBotInfoResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.BotId) {
		return false
	}
	if !p.Field2DeepEqual(ano.Version) {
		return false
	}
	if !p.Field3DeepEqual(ano.BotList) {
		return false
	}
	if !p.Field255DeepEqual(ano.BaseResp) {
		return false
	}
	return true
}

func (p *CreateBotInfoResp) Field1DeepEqual(src int64) bool {

	if p.BotId != src {
		return false
	}
	return true
}
func (p *CreateBotInfoResp) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Version, src) != 0 {
		return false
	}
	return true
}
func (p *CreateBotInfoResp) Field3DeepEqual(src []*Bot) bool {

	if len(p.BotList) != len(src) {
		return false
	}
	for i, v := range p.BotList {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *CreateBotInfoResp) Field255DeepEqual(src *base.BaseResp) bool {

	if !p.BaseResp.DeepEqual(src) {
		return false
	}
	return true
}

type UpdateBotInfoReq struct {
	BotId         int64      `thrift:"bot_id,1,required" frugal:"1,required,i64" json:"bot_id"`
	ConnectorId   int64      `thrift:"connector_id,2,required" frugal:"2,required,i64" json:"connector_id"`
	Bot           *Bot       `thrift:"bot,3" frugal:"3,default,Bot" json:"bot"`
	NullFieldList []string   `thrift:"null_field_list,4" frugal:"4,default,list<string>" json:"null_field_list"`
	Base          *base.Base `thrift:"Base,255" frugal:"255,default,base.Base" json:"Base"`
}

func NewUpdateBotInfoReq() *UpdateBotInfoReq {
	return &UpdateBotInfoReq{}
}

func (p *UpdateBotInfoReq) InitDefault() {
}

func (p *UpdateBotInfoReq) GetBotId() (v int64) {
	return p.BotId
}

func (p *UpdateBotInfoReq) GetConnectorId() (v int64) {
	return p.ConnectorId
}

var UpdateBotInfoReq_Bot_DEFAULT *Bot

func (p *UpdateBotInfoReq) GetBot() (v *Bot) {
	if !p.IsSetBot() {
		return UpdateBotInfoReq_Bot_DEFAULT
	}
	return p.Bot
}

func (p *UpdateBotInfoReq) GetNullFieldList() (v []string) {
	return p.NullFieldList
}

var UpdateBotInfoReq_Base_DEFAULT *base.Base

func (p *UpdateBotInfoReq) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return UpdateBotInfoReq_Base_DEFAULT
	}
	return p.Base
}
func (p *UpdateBotInfoReq) SetBotId(val int64) {
	p.BotId = val
}
func (p *UpdateBotInfoReq) SetConnectorId(val int64) {
	p.ConnectorId = val
}
func (p *UpdateBotInfoReq) SetBot(val *Bot) {
	p.Bot = val
}
func (p *UpdateBotInfoReq) SetNullFieldList(val []string) {
	p.NullFieldList = val
}
func (p *UpdateBotInfoReq) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_UpdateBotInfoReq = map[int16]string{
	1:   "bot_id",
	2:   "connector_id",
	3:   "bot",
	4:   "null_field_list",
	255: "Base",
}

func (p *UpdateBotInfoReq) IsSetBot() bool {
	return p.Bot != nil
}

func (p *UpdateBotInfoReq) IsSetBase() bool {
	return p.Base != nil
}

func (p *UpdateBotInfoReq) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetBotId bool = false
	var issetConnectorId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetBotId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetConnectorId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetBotId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetConnectorId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UpdateBotInfoReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_UpdateBotInfoReq[fieldId]))
}

func (p *UpdateBotInfoReq) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BotId = _field
	return nil
}
func (p *UpdateBotInfoReq) ReadField2(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ConnectorId = _field
	return nil
}
func (p *UpdateBotInfoReq) ReadField3(iprot thrift.TProtocol) error {
	_field := NewBot()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Bot = _field
	return nil
}
func (p *UpdateBotInfoReq) ReadField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.NullFieldList = _field
	return nil
}
func (p *UpdateBotInfoReq) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *UpdateBotInfoReq) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("UpdateBotInfoReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *UpdateBotInfoReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("bot_id", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.BotId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *UpdateBotInfoReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("connector_id", thrift.I64, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.ConnectorId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *UpdateBotInfoReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("bot", thrift.STRUCT, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Bot.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *UpdateBotInfoReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("null_field_list", thrift.LIST, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.NullFieldList)); err != nil {
		return err
	}
	for _, v := range p.NullFieldList {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *UpdateBotInfoReq) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Base.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *UpdateBotInfoReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateBotInfoReq(%+v)", *p)

}

func (p *UpdateBotInfoReq) DeepEqual(ano *UpdateBotInfoReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.BotId) {
		return false
	}
	if !p.Field2DeepEqual(ano.ConnectorId) {
		return false
	}
	if !p.Field3DeepEqual(ano.Bot) {
		return false
	}
	if !p.Field4DeepEqual(ano.NullFieldList) {
		return false
	}
	if !p.Field255DeepEqual(ano.Base) {
		return false
	}
	return true
}

func (p *UpdateBotInfoReq) Field1DeepEqual(src int64) bool {

	if p.BotId != src {
		return false
	}
	return true
}
func (p *UpdateBotInfoReq) Field2DeepEqual(src int64) bool {

	if p.ConnectorId != src {
		return false
	}
	return true
}
func (p *UpdateBotInfoReq) Field3DeepEqual(src *Bot) bool {

	if !p.Bot.DeepEqual(src) {
		return false
	}
	return true
}
func (p *UpdateBotInfoReq) Field4DeepEqual(src []string) bool {

	if len(p.NullFieldList) != len(src) {
		return false
	}
	for i, v := range p.NullFieldList {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *UpdateBotInfoReq) Field255DeepEqual(src *base.Base) bool {

	if !p.Base.DeepEqual(src) {
		return false
	}
	return true
}

type UpdateBotInfoResp struct {
	BotId    int64          `thrift:"bot_id,1" frugal:"1,default,i64" json:"bot_id"`
	Version  string         `thrift:"version,2" frugal:"2,default,string" json:"version"`
	BotList  []*Bot         `thrift:"bot_list,3" frugal:"3,default,list<Bot>" json:"bot_list"`
	BaseResp *base.BaseResp `thrift:"BaseResp,255" frugal:"255,default,base.BaseResp" json:"BaseResp"`
}

func NewUpdateBotInfoResp() *UpdateBotInfoResp {
	return &UpdateBotInfoResp{}
}

func (p *UpdateBotInfoResp) InitDefault() {
}

func (p *UpdateBotInfoResp) GetBotId() (v int64) {
	return p.BotId
}

func (p *UpdateBotInfoResp) GetVersion() (v string) {
	return p.Version
}

func (p *UpdateBotInfoResp) GetBotList() (v []*Bot) {
	return p.BotList
}

var UpdateBotInfoResp_BaseResp_DEFAULT *base.BaseResp

func (p *UpdateBotInfoResp) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return UpdateBotInfoResp_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *UpdateBotInfoResp) SetBotId(val int64) {
	p.BotId = val
}
func (p *UpdateBotInfoResp) SetVersion(val string) {
	p.Version = val
}
func (p *UpdateBotInfoResp) SetBotList(val []*Bot) {
	p.BotList = val
}
func (p *UpdateBotInfoResp) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_UpdateBotInfoResp = map[int16]string{
	1:   "bot_id",
	2:   "version",
	3:   "bot_list",
	255: "BaseResp",
}

func (p *UpdateBotInfoResp) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *UpdateBotInfoResp) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UpdateBotInfoResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *UpdateBotInfoResp) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BotId = _field
	return nil
}
func (p *UpdateBotInfoResp) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Version = _field
	return nil
}
func (p *UpdateBotInfoResp) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*Bot, 0, size)
	values := make([]Bot, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.BotList = _field
	return nil
}
func (p *UpdateBotInfoResp) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *UpdateBotInfoResp) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("UpdateBotInfoResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *UpdateBotInfoResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("bot_id", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.BotId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *UpdateBotInfoResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("version", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Version); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *UpdateBotInfoResp) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("bot_list", thrift.LIST, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.BotList)); err != nil {
		return err
	}
	for _, v := range p.BotList {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *UpdateBotInfoResp) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.BaseResp.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *UpdateBotInfoResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateBotInfoResp(%+v)", *p)

}

func (p *UpdateBotInfoResp) DeepEqual(ano *UpdateBotInfoResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.BotId) {
		return false
	}
	if !p.Field2DeepEqual(ano.Version) {
		return false
	}
	if !p.Field3DeepEqual(ano.BotList) {
		return false
	}
	if !p.Field255DeepEqual(ano.BaseResp) {
		return false
	}
	return true
}

func (p *UpdateBotInfoResp) Field1DeepEqual(src int64) bool {

	if p.BotId != src {
		return false
	}
	return true
}
func (p *UpdateBotInfoResp) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Version, src) != 0 {
		return false
	}
	return true
}
func (p *UpdateBotInfoResp) Field3DeepEqual(src []*Bot) bool {

	if len(p.BotList) != len(src) {
		return false
	}
	for i, v := range p.BotList {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *UpdateBotInfoResp) Field255DeepEqual(src *base.BaseResp) bool {

	if !p.BaseResp.DeepEqual(src) {
		return false
	}
	return true
}

type UpdateHookInfoReq struct {
	BotId         int64            `thrift:"bot_id,1,required" frugal:"1,required,i64" json:"bot_id"`
	Version       string           `thrift:"version,2,required" frugal:"2,required,string" json:"version"`
	BotHookInfo   *string          `thrift:"bot_hook_info,3,optional" frugal:"3,optional,string" json:"bot_hook_info,omitempty"`
	AgentHookInfo map[int64]string `thrift:"agent_hook_info,4,optional" frugal:"4,optional,map<i64:string>" json:"agent_hook_info,omitempty"`
	Base          *base.Base       `thrift:"Base,255" frugal:"255,default,base.Base" json:"Base"`
}

func NewUpdateHookInfoReq() *UpdateHookInfoReq {
	return &UpdateHookInfoReq{}
}

func (p *UpdateHookInfoReq) InitDefault() {
}

func (p *UpdateHookInfoReq) GetBotId() (v int64) {
	return p.BotId
}

func (p *UpdateHookInfoReq) GetVersion() (v string) {
	return p.Version
}

var UpdateHookInfoReq_BotHookInfo_DEFAULT string

func (p *UpdateHookInfoReq) GetBotHookInfo() (v string) {
	if !p.IsSetBotHookInfo() {
		return UpdateHookInfoReq_BotHookInfo_DEFAULT
	}
	return *p.BotHookInfo
}

var UpdateHookInfoReq_AgentHookInfo_DEFAULT map[int64]string

func (p *UpdateHookInfoReq) GetAgentHookInfo() (v map[int64]string) {
	if !p.IsSetAgentHookInfo() {
		return UpdateHookInfoReq_AgentHookInfo_DEFAULT
	}
	return p.AgentHookInfo
}

var UpdateHookInfoReq_Base_DEFAULT *base.Base

func (p *UpdateHookInfoReq) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return UpdateHookInfoReq_Base_DEFAULT
	}
	return p.Base
}
func (p *UpdateHookInfoReq) SetBotId(val int64) {
	p.BotId = val
}
func (p *UpdateHookInfoReq) SetVersion(val string) {
	p.Version = val
}
func (p *UpdateHookInfoReq) SetBotHookInfo(val *string) {
	p.BotHookInfo = val
}
func (p *UpdateHookInfoReq) SetAgentHookInfo(val map[int64]string) {
	p.AgentHookInfo = val
}
func (p *UpdateHookInfoReq) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_UpdateHookInfoReq = map[int16]string{
	1:   "bot_id",
	2:   "version",
	3:   "bot_hook_info",
	4:   "agent_hook_info",
	255: "Base",
}

func (p *UpdateHookInfoReq) IsSetBotHookInfo() bool {
	return p.BotHookInfo != nil
}

func (p *UpdateHookInfoReq) IsSetAgentHookInfo() bool {
	return p.AgentHookInfo != nil
}

func (p *UpdateHookInfoReq) IsSetBase() bool {
	return p.Base != nil
}

func (p *UpdateHookInfoReq) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetBotId bool = false
	var issetVersion bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetBotId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetVersion = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetBotId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetVersion {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UpdateHookInfoReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_UpdateHookInfoReq[fieldId]))
}

func (p *UpdateHookInfoReq) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BotId = _field
	return nil
}
func (p *UpdateHookInfoReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Version = _field
	return nil
}
func (p *UpdateHookInfoReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.BotHookInfo = _field
	return nil
}
func (p *UpdateHookInfoReq) ReadField4(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[int64]string, size)
	for i := 0; i < size; i++ {
		var _key int64
		if v, err := iprot.ReadI64(); err != nil {
			return err
		} else {
			_key = v
		}

		var _val string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_val = v
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.AgentHookInfo = _field
	return nil
}
func (p *UpdateHookInfoReq) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *UpdateHookInfoReq) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("UpdateHookInfoReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *UpdateHookInfoReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("bot_id", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.BotId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *UpdateHookInfoReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("version", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Version); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *UpdateHookInfoReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetBotHookInfo() {
		if err = oprot.WriteFieldBegin("bot_hook_info", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.BotHookInfo); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *UpdateHookInfoReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetAgentHookInfo() {
		if err = oprot.WriteFieldBegin("agent_hook_info", thrift.MAP, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteMapBegin(thrift.I64, thrift.STRING, len(p.AgentHookInfo)); err != nil {
			return err
		}
		for k, v := range p.AgentHookInfo {
			if err := oprot.WriteI64(k); err != nil {
				return err
			}
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *UpdateHookInfoReq) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Base.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *UpdateHookInfoReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateHookInfoReq(%+v)", *p)

}

func (p *UpdateHookInfoReq) DeepEqual(ano *UpdateHookInfoReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.BotId) {
		return false
	}
	if !p.Field2DeepEqual(ano.Version) {
		return false
	}
	if !p.Field3DeepEqual(ano.BotHookInfo) {
		return false
	}
	if !p.Field4DeepEqual(ano.AgentHookInfo) {
		return false
	}
	if !p.Field255DeepEqual(ano.Base) {
		return false
	}
	return true
}

func (p *UpdateHookInfoReq) Field1DeepEqual(src int64) bool {

	if p.BotId != src {
		return false
	}
	return true
}
func (p *UpdateHookInfoReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Version, src) != 0 {
		return false
	}
	return true
}
func (p *UpdateHookInfoReq) Field3DeepEqual(src *string) bool {

	if p.BotHookInfo == src {
		return true
	} else if p.BotHookInfo == nil || src == nil {
		return false
	}
	if strings.Compare(*p.BotHookInfo, *src) != 0 {
		return false
	}
	return true
}
func (p *UpdateHookInfoReq) Field4DeepEqual(src map[int64]string) bool {

	if len(p.AgentHookInfo) != len(src) {
		return false
	}
	for k, v := range p.AgentHookInfo {
		_src := src[k]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *UpdateHookInfoReq) Field255DeepEqual(src *base.Base) bool {

	if !p.Base.DeepEqual(src) {
		return false
	}
	return true
}

type UpdateHookInfoResp struct {
	BaseResp *base.BaseResp `thrift:"BaseResp,255" frugal:"255,default,base.BaseResp" json:"BaseResp"`
}

func NewUpdateHookInfoResp() *UpdateHookInfoResp {
	return &UpdateHookInfoResp{}
}

func (p *UpdateHookInfoResp) InitDefault() {
}

var UpdateHookInfoResp_BaseResp_DEFAULT *base.BaseResp

func (p *UpdateHookInfoResp) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return UpdateHookInfoResp_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *UpdateHookInfoResp) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_UpdateHookInfoResp = map[int16]string{
	255: "BaseResp",
}

func (p *UpdateHookInfoResp) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *UpdateHookInfoResp) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UpdateHookInfoResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *UpdateHookInfoResp) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *UpdateHookInfoResp) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("UpdateHookInfoResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *UpdateHookInfoResp) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.BaseResp.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *UpdateHookInfoResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateHookInfoResp(%+v)", *p)

}

func (p *UpdateHookInfoResp) DeepEqual(ano *UpdateHookInfoResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field255DeepEqual(ano.BaseResp) {
		return false
	}
	return true
}

func (p *UpdateHookInfoResp) Field255DeepEqual(src *base.BaseResp) bool {

	if !p.BaseResp.DeepEqual(src) {
		return false
	}
	return true
}
