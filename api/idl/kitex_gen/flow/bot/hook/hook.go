// Code generated by thriftgo (0.4.2). DO NOT EDIT.

package hook

import (
	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/base"
	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/flow/bot/engine"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type FlowHookRequest struct {
	BotContext *engine.BotContext `thrift:"bot_context,1" frugal:"1,default,engine.BotContext" json:"bot_context"`
	Base       *base.Base         `thrift:"Base,255" frugal:"255,default,base.Base" json:"Base"`
}

func NewFlowHookRequest() *FlowHookRequest {
	return &FlowHookRequest{}
}

func (p *FlowHookRequest) InitDefault() {
}

var FlowHookRequest_BotContext_DEFAULT *engine.BotContext

func (p *FlowHookRequest) GetBotContext() (v *engine.BotContext) {
	if !p.IsSetBotContext() {
		return FlowHookRequest_BotContext_DEFAULT
	}
	return p.BotContext
}

var FlowHookRequest_Base_DEFAULT *base.Base

func (p *FlowHookRequest) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return FlowHookRequest_Base_DEFAULT
	}
	return p.Base
}
func (p *FlowHookRequest) SetBotContext(val *engine.BotContext) {
	p.BotContext = val
}
func (p *FlowHookRequest) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_FlowHookRequest = map[int16]string{
	1:   "bot_context",
	255: "Base",
}

func (p *FlowHookRequest) IsSetBotContext() bool {
	return p.BotContext != nil
}

func (p *FlowHookRequest) IsSetBase() bool {
	return p.Base != nil
}

func (p *FlowHookRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FlowHookRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *FlowHookRequest) ReadField1(iprot thrift.TProtocol) error {
	_field := engine.NewBotContext()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BotContext = _field
	return nil
}
func (p *FlowHookRequest) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *FlowHookRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("FlowHookRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *FlowHookRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("bot_context", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.BotContext.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *FlowHookRequest) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Base.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *FlowHookRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FlowHookRequest(%+v)", *p)

}

func (p *FlowHookRequest) DeepEqual(ano *FlowHookRequest) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.BotContext) {
		return false
	}
	if !p.Field255DeepEqual(ano.Base) {
		return false
	}
	return true
}

func (p *FlowHookRequest) Field1DeepEqual(src *engine.BotContext) bool {

	if !p.BotContext.DeepEqual(src) {
		return false
	}
	return true
}
func (p *FlowHookRequest) Field255DeepEqual(src *base.Base) bool {

	if !p.Base.DeepEqual(src) {
		return false
	}
	return true
}

type FlowHookResponse struct {
	FlowType        FlowType                 `thrift:"flow_type,1" frugal:"1,default,FlowType" json:"flow_type"`
	VerboseMessages []*engine.VerboseMessage `thrift:"verbose_messages,2" frugal:"2,default,list<engine.VerboseMessage>" json:"verbose_messages"`
	ContextExt      map[string]string        `thrift:"context_ext,3,optional" frugal:"3,optional,map<string:string>" json:"context_ext,omitempty"`
	StreamFlow      *StreamFlow              `thrift:"stream_flow,4,optional" frugal:"4,optional,StreamFlow" json:"stream_flow,omitempty"`
	TaskFlow        *TaskFlow                `thrift:"task_flow,5,optional" frugal:"5,optional,TaskFlow" json:"task_flow,omitempty"`
	ToolFlow        *ToolFlow                `thrift:"tool_flow,6,optional" frugal:"6,optional,ToolFlow" json:"tool_flow,omitempty"`
	FinishFlow      *FinishFlow              `thrift:"finish_flow,7,optional" frugal:"7,optional,FinishFlow" json:"finish_flow,omitempty"`
	BaseResp        *base.BaseResp           `thrift:"BaseResp,255" frugal:"255,default,base.BaseResp" json:"BaseResp"`
}

func NewFlowHookResponse() *FlowHookResponse {
	return &FlowHookResponse{}
}

func (p *FlowHookResponse) InitDefault() {
}

func (p *FlowHookResponse) GetFlowType() (v FlowType) {
	return p.FlowType
}

func (p *FlowHookResponse) GetVerboseMessages() (v []*engine.VerboseMessage) {
	return p.VerboseMessages
}

var FlowHookResponse_ContextExt_DEFAULT map[string]string

func (p *FlowHookResponse) GetContextExt() (v map[string]string) {
	if !p.IsSetContextExt() {
		return FlowHookResponse_ContextExt_DEFAULT
	}
	return p.ContextExt
}

var FlowHookResponse_StreamFlow_DEFAULT *StreamFlow

func (p *FlowHookResponse) GetStreamFlow() (v *StreamFlow) {
	if !p.IsSetStreamFlow() {
		return FlowHookResponse_StreamFlow_DEFAULT
	}
	return p.StreamFlow
}

var FlowHookResponse_TaskFlow_DEFAULT *TaskFlow

func (p *FlowHookResponse) GetTaskFlow() (v *TaskFlow) {
	if !p.IsSetTaskFlow() {
		return FlowHookResponse_TaskFlow_DEFAULT
	}
	return p.TaskFlow
}

var FlowHookResponse_ToolFlow_DEFAULT *ToolFlow

func (p *FlowHookResponse) GetToolFlow() (v *ToolFlow) {
	if !p.IsSetToolFlow() {
		return FlowHookResponse_ToolFlow_DEFAULT
	}
	return p.ToolFlow
}

var FlowHookResponse_FinishFlow_DEFAULT *FinishFlow

func (p *FlowHookResponse) GetFinishFlow() (v *FinishFlow) {
	if !p.IsSetFinishFlow() {
		return FlowHookResponse_FinishFlow_DEFAULT
	}
	return p.FinishFlow
}

var FlowHookResponse_BaseResp_DEFAULT *base.BaseResp

func (p *FlowHookResponse) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return FlowHookResponse_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *FlowHookResponse) SetFlowType(val FlowType) {
	p.FlowType = val
}
func (p *FlowHookResponse) SetVerboseMessages(val []*engine.VerboseMessage) {
	p.VerboseMessages = val
}
func (p *FlowHookResponse) SetContextExt(val map[string]string) {
	p.ContextExt = val
}
func (p *FlowHookResponse) SetStreamFlow(val *StreamFlow) {
	p.StreamFlow = val
}
func (p *FlowHookResponse) SetTaskFlow(val *TaskFlow) {
	p.TaskFlow = val
}
func (p *FlowHookResponse) SetToolFlow(val *ToolFlow) {
	p.ToolFlow = val
}
func (p *FlowHookResponse) SetFinishFlow(val *FinishFlow) {
	p.FinishFlow = val
}
func (p *FlowHookResponse) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_FlowHookResponse = map[int16]string{
	1:   "flow_type",
	2:   "verbose_messages",
	3:   "context_ext",
	4:   "stream_flow",
	5:   "task_flow",
	6:   "tool_flow",
	7:   "finish_flow",
	255: "BaseResp",
}

func (p *FlowHookResponse) IsSetContextExt() bool {
	return p.ContextExt != nil
}

func (p *FlowHookResponse) IsSetStreamFlow() bool {
	return p.StreamFlow != nil
}

func (p *FlowHookResponse) IsSetTaskFlow() bool {
	return p.TaskFlow != nil
}

func (p *FlowHookResponse) IsSetToolFlow() bool {
	return p.ToolFlow != nil
}

func (p *FlowHookResponse) IsSetFinishFlow() bool {
	return p.FinishFlow != nil
}

func (p *FlowHookResponse) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *FlowHookResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FlowHookResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *FlowHookResponse) ReadField1(iprot thrift.TProtocol) error {

	var _field FlowType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = FlowType(v)
	}
	p.FlowType = _field
	return nil
}
func (p *FlowHookResponse) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*engine.VerboseMessage, 0, size)
	values := make([]engine.VerboseMessage, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.VerboseMessages = _field
	return nil
}
func (p *FlowHookResponse) ReadField3(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_key = v
		}

		var _val string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_val = v
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.ContextExt = _field
	return nil
}
func (p *FlowHookResponse) ReadField4(iprot thrift.TProtocol) error {
	_field := NewStreamFlow()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.StreamFlow = _field
	return nil
}
func (p *FlowHookResponse) ReadField5(iprot thrift.TProtocol) error {
	_field := NewTaskFlow()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.TaskFlow = _field
	return nil
}
func (p *FlowHookResponse) ReadField6(iprot thrift.TProtocol) error {
	_field := NewToolFlow()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.ToolFlow = _field
	return nil
}
func (p *FlowHookResponse) ReadField7(iprot thrift.TProtocol) error {
	_field := NewFinishFlow()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.FinishFlow = _field
	return nil
}
func (p *FlowHookResponse) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *FlowHookResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("FlowHookResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *FlowHookResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("flow_type", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.FlowType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *FlowHookResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("verbose_messages", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.VerboseMessages)); err != nil {
		return err
	}
	for _, v := range p.VerboseMessages {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *FlowHookResponse) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetContextExt() {
		if err = oprot.WriteFieldBegin("context_ext", thrift.MAP, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.ContextExt)); err != nil {
			return err
		}
		for k, v := range p.ContextExt {
			if err := oprot.WriteString(k); err != nil {
				return err
			}
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *FlowHookResponse) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetStreamFlow() {
		if err = oprot.WriteFieldBegin("stream_flow", thrift.STRUCT, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.StreamFlow.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *FlowHookResponse) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetTaskFlow() {
		if err = oprot.WriteFieldBegin("task_flow", thrift.STRUCT, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.TaskFlow.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *FlowHookResponse) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetToolFlow() {
		if err = oprot.WriteFieldBegin("tool_flow", thrift.STRUCT, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.ToolFlow.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *FlowHookResponse) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetFinishFlow() {
		if err = oprot.WriteFieldBegin("finish_flow", thrift.STRUCT, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.FinishFlow.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}
func (p *FlowHookResponse) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.BaseResp.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *FlowHookResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FlowHookResponse(%+v)", *p)

}

func (p *FlowHookResponse) DeepEqual(ano *FlowHookResponse) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.FlowType) {
		return false
	}
	if !p.Field2DeepEqual(ano.VerboseMessages) {
		return false
	}
	if !p.Field3DeepEqual(ano.ContextExt) {
		return false
	}
	if !p.Field4DeepEqual(ano.StreamFlow) {
		return false
	}
	if !p.Field5DeepEqual(ano.TaskFlow) {
		return false
	}
	if !p.Field6DeepEqual(ano.ToolFlow) {
		return false
	}
	if !p.Field7DeepEqual(ano.FinishFlow) {
		return false
	}
	if !p.Field255DeepEqual(ano.BaseResp) {
		return false
	}
	return true
}

func (p *FlowHookResponse) Field1DeepEqual(src FlowType) bool {

	if p.FlowType != src {
		return false
	}
	return true
}
func (p *FlowHookResponse) Field2DeepEqual(src []*engine.VerboseMessage) bool {

	if len(p.VerboseMessages) != len(src) {
		return false
	}
	for i, v := range p.VerboseMessages {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *FlowHookResponse) Field3DeepEqual(src map[string]string) bool {

	if len(p.ContextExt) != len(src) {
		return false
	}
	for k, v := range p.ContextExt {
		_src := src[k]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *FlowHookResponse) Field4DeepEqual(src *StreamFlow) bool {

	if !p.StreamFlow.DeepEqual(src) {
		return false
	}
	return true
}
func (p *FlowHookResponse) Field5DeepEqual(src *TaskFlow) bool {

	if !p.TaskFlow.DeepEqual(src) {
		return false
	}
	return true
}
func (p *FlowHookResponse) Field6DeepEqual(src *ToolFlow) bool {

	if !p.ToolFlow.DeepEqual(src) {
		return false
	}
	return true
}
func (p *FlowHookResponse) Field7DeepEqual(src *FinishFlow) bool {

	if !p.FinishFlow.DeepEqual(src) {
		return false
	}
	return true
}
func (p *FlowHookResponse) Field255DeepEqual(src *base.BaseResp) bool {

	if !p.BaseResp.DeepEqual(src) {
		return false
	}
	return true
}
