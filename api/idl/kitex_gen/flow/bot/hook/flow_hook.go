// Code generated by thriftgo (0.4.2). DO NOT EDIT.

package hook

import (
	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/base"
	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/flow/cloud/copilot"
	"database/sql"
	"database/sql/driver"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

const (
	InternalContentTypeLLMOutput = 2001

	InternalContentTypeToolOutput = 2002
)

type FlowType int64

const (
	FlowType_None   FlowType = 1
	FlowType_Stream FlowType = 2
	FlowType_Task   FlowType = 3
	FlowType_Tool   FlowType = 4
	FlowType_Finish FlowType = 5
	FlowType_Stop   FlowType = 6
)

func (p FlowType) String() string {
	switch p {
	case FlowType_None:
		return "None"
	case FlowType_Stream:
		return "Stream"
	case FlowType_Task:
		return "Task"
	case FlowType_Tool:
		return "Tool"
	case FlowType_Finish:
		return "Finish"
	case FlowType_Stop:
		return "Stop"
	}
	return "<UNSET>"
}

func FlowTypeFromString(s string) (FlowType, error) {
	switch s {
	case "None":
		return FlowType_None, nil
	case "Stream":
		return FlowType_Stream, nil
	case "Task":
		return FlowType_Task, nil
	case "Tool":
		return FlowType_Tool, nil
	case "Finish":
		return FlowType_Finish, nil
	case "Stop":
		return FlowType_Stop, nil
	}
	return FlowType(0), fmt.Errorf("not a valid FlowType string")
}

func FlowTypePtr(v FlowType) *FlowType { return &v }
func (p *FlowType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = FlowType(result.Int64)
	return
}

func (p *FlowType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type StreamServiceType int64

const (
	StreamServiceType_Grpc            StreamServiceType = 1
	StreamServiceType_ThriftStreaming StreamServiceType = 2
	StreamServiceType_Http            StreamServiceType = 3
)

func (p StreamServiceType) String() string {
	switch p {
	case StreamServiceType_Grpc:
		return "Grpc"
	case StreamServiceType_ThriftStreaming:
		return "ThriftStreaming"
	case StreamServiceType_Http:
		return "Http"
	}
	return "<UNSET>"
}

func StreamServiceTypeFromString(s string) (StreamServiceType, error) {
	switch s {
	case "Grpc":
		return StreamServiceType_Grpc, nil
	case "ThriftStreaming":
		return StreamServiceType_ThriftStreaming, nil
	case "Http":
		return StreamServiceType_Http, nil
	}
	return StreamServiceType(0), fmt.Errorf("not a valid StreamServiceType string")
}

func StreamServiceTypePtr(v StreamServiceType) *StreamServiceType { return &v }
func (p *StreamServiceType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = StreamServiceType(result.Int64)
	return
}

func (p *StreamServiceType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type PacketContentType int64

const (
	PacketContentType_Reserve PacketContentType = 1000
	PacketContentType_Verbose PacketContentType = 1001
	PacketContentType_Answer  PacketContentType = 1002
	PacketContentType_Card    PacketContentType = 1003
	PacketContentType_BotCard PacketContentType = 1004
	PacketContentType_Widget  PacketContentType = 1005
)

func (p PacketContentType) String() string {
	switch p {
	case PacketContentType_Reserve:
		return "Reserve"
	case PacketContentType_Verbose:
		return "Verbose"
	case PacketContentType_Answer:
		return "Answer"
	case PacketContentType_Card:
		return "Card"
	case PacketContentType_BotCard:
		return "BotCard"
	case PacketContentType_Widget:
		return "Widget"
	}
	return "<UNSET>"
}

func PacketContentTypeFromString(s string) (PacketContentType, error) {
	switch s {
	case "Reserve":
		return PacketContentType_Reserve, nil
	case "Verbose":
		return PacketContentType_Verbose, nil
	case "Answer":
		return PacketContentType_Answer, nil
	case "Card":
		return PacketContentType_Card, nil
	case "BotCard":
		return PacketContentType_BotCard, nil
	case "Widget":
		return PacketContentType_Widget, nil
	}
	return PacketContentType(0), fmt.Errorf("not a valid PacketContentType string")
}

func PacketContentTypePtr(v PacketContentType) *PacketContentType { return &v }
func (p *PacketContentType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = PacketContentType(result.Int64)
	return
}

func (p *PacketContentType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type StreamFlow struct {
	StreamServiceType StreamServiceType `thrift:"stream_service_type,1" frugal:"1,default,StreamServiceType" json:"stream_service_type"`
	Uri               string            `thrift:"uri,2" frugal:"2,default,string" json:"uri"`
}

func NewStreamFlow() *StreamFlow {
	return &StreamFlow{}
}

func (p *StreamFlow) InitDefault() {
}

func (p *StreamFlow) GetStreamServiceType() (v StreamServiceType) {
	return p.StreamServiceType
}

func (p *StreamFlow) GetUri() (v string) {
	return p.Uri
}
func (p *StreamFlow) SetStreamServiceType(val StreamServiceType) {
	p.StreamServiceType = val
}
func (p *StreamFlow) SetUri(val string) {
	p.Uri = val
}

var fieldIDToName_StreamFlow = map[int16]string{
	1: "stream_service_type",
	2: "uri",
}

func (p *StreamFlow) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_StreamFlow[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *StreamFlow) ReadField1(iprot thrift.TProtocol) error {

	var _field StreamServiceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = StreamServiceType(v)
	}
	p.StreamServiceType = _field
	return nil
}
func (p *StreamFlow) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Uri = _field
	return nil
}

func (p *StreamFlow) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("StreamFlow"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *StreamFlow) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("stream_service_type", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.StreamServiceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *StreamFlow) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("uri", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Uri); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *StreamFlow) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("StreamFlow(%+v)", *p)

}

func (p *StreamFlow) DeepEqual(ano *StreamFlow) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.StreamServiceType) {
		return false
	}
	if !p.Field2DeepEqual(ano.Uri) {
		return false
	}
	return true
}

func (p *StreamFlow) Field1DeepEqual(src StreamServiceType) bool {

	if p.StreamServiceType != src {
		return false
	}
	return true
}
func (p *StreamFlow) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Uri, src) != 0 {
		return false
	}
	return true
}

type StreamPacket struct {
	Content          string            `thrift:"content,1" frugal:"1,default,string" json:"content"`
	ContentType      int64             `thrift:"content_type,2" frugal:"2,default,i64" json:"content_type"`
	IsFinish         bool              `thrift:"is_finish,3" frugal:"3,default,bool" json:"is_finish"`
	StreamId         string            `thrift:"stream_id,4" frugal:"4,default,string" json:"stream_id"`
	Ext              map[string]string `thrift:"ext,5" frugal:"5,default,map<string:string>" json:"ext"`
	ResponseForModel string            `thrift:"response_for_model,6" frugal:"6,default,string" json:"response_for_model"`
	BaseResp         *base.BaseResp    `thrift:"BaseResp,255,optional" frugal:"255,optional,base.BaseResp" json:"BaseResp,omitempty"`
}

func NewStreamPacket() *StreamPacket {
	return &StreamPacket{}
}

func (p *StreamPacket) InitDefault() {
}

func (p *StreamPacket) GetContent() (v string) {
	return p.Content
}

func (p *StreamPacket) GetContentType() (v int64) {
	return p.ContentType
}

func (p *StreamPacket) GetIsFinish() (v bool) {
	return p.IsFinish
}

func (p *StreamPacket) GetStreamId() (v string) {
	return p.StreamId
}

func (p *StreamPacket) GetExt() (v map[string]string) {
	return p.Ext
}

func (p *StreamPacket) GetResponseForModel() (v string) {
	return p.ResponseForModel
}

var StreamPacket_BaseResp_DEFAULT *base.BaseResp

func (p *StreamPacket) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return StreamPacket_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *StreamPacket) SetContent(val string) {
	p.Content = val
}
func (p *StreamPacket) SetContentType(val int64) {
	p.ContentType = val
}
func (p *StreamPacket) SetIsFinish(val bool) {
	p.IsFinish = val
}
func (p *StreamPacket) SetStreamId(val string) {
	p.StreamId = val
}
func (p *StreamPacket) SetExt(val map[string]string) {
	p.Ext = val
}
func (p *StreamPacket) SetResponseForModel(val string) {
	p.ResponseForModel = val
}
func (p *StreamPacket) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_StreamPacket = map[int16]string{
	1:   "content",
	2:   "content_type",
	3:   "is_finish",
	4:   "stream_id",
	5:   "ext",
	6:   "response_for_model",
	255: "BaseResp",
}

func (p *StreamPacket) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *StreamPacket) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_StreamPacket[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *StreamPacket) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Content = _field
	return nil
}
func (p *StreamPacket) ReadField2(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ContentType = _field
	return nil
}
func (p *StreamPacket) ReadField3(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.IsFinish = _field
	return nil
}
func (p *StreamPacket) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.StreamId = _field
	return nil
}
func (p *StreamPacket) ReadField5(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_key = v
		}

		var _val string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_val = v
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.Ext = _field
	return nil
}
func (p *StreamPacket) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ResponseForModel = _field
	return nil
}
func (p *StreamPacket) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *StreamPacket) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("StreamPacket"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *StreamPacket) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("content", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Content); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *StreamPacket) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("content_type", thrift.I64, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.ContentType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *StreamPacket) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("is_finish", thrift.BOOL, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.IsFinish); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *StreamPacket) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("stream_id", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.StreamId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *StreamPacket) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ext", thrift.MAP, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.Ext)); err != nil {
		return err
	}
	for k, v := range p.Ext {
		if err := oprot.WriteString(k); err != nil {
			return err
		}
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteMapEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *StreamPacket) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("response_for_model", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ResponseForModel); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *StreamPacket) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBaseResp() {
		if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.BaseResp.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *StreamPacket) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("StreamPacket(%+v)", *p)

}

func (p *StreamPacket) DeepEqual(ano *StreamPacket) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Content) {
		return false
	}
	if !p.Field2DeepEqual(ano.ContentType) {
		return false
	}
	if !p.Field3DeepEqual(ano.IsFinish) {
		return false
	}
	if !p.Field4DeepEqual(ano.StreamId) {
		return false
	}
	if !p.Field5DeepEqual(ano.Ext) {
		return false
	}
	if !p.Field6DeepEqual(ano.ResponseForModel) {
		return false
	}
	if !p.Field255DeepEqual(ano.BaseResp) {
		return false
	}
	return true
}

func (p *StreamPacket) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Content, src) != 0 {
		return false
	}
	return true
}
func (p *StreamPacket) Field2DeepEqual(src int64) bool {

	if p.ContentType != src {
		return false
	}
	return true
}
func (p *StreamPacket) Field3DeepEqual(src bool) bool {

	if p.IsFinish != src {
		return false
	}
	return true
}
func (p *StreamPacket) Field4DeepEqual(src string) bool {

	if strings.Compare(p.StreamId, src) != 0 {
		return false
	}
	return true
}
func (p *StreamPacket) Field5DeepEqual(src map[string]string) bool {

	if len(p.Ext) != len(src) {
		return false
	}
	for k, v := range p.Ext {
		_src := src[k]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *StreamPacket) Field6DeepEqual(src string) bool {

	if strings.Compare(p.ResponseForModel, src) != 0 {
		return false
	}
	return true
}
func (p *StreamPacket) Field255DeepEqual(src *base.BaseResp) bool {

	if !p.BaseResp.DeepEqual(src) {
		return false
	}
	return true
}

type PacketError struct {
	ErrorCode int64  `thrift:"error_code,1" frugal:"1,default,i64" json:"error_code"`
	ErrorMsg  string `thrift:"error_msg,2" frugal:"2,default,string" json:"error_msg"`
}

func NewPacketError() *PacketError {
	return &PacketError{}
}

func (p *PacketError) InitDefault() {
}

func (p *PacketError) GetErrorCode() (v int64) {
	return p.ErrorCode
}

func (p *PacketError) GetErrorMsg() (v string) {
	return p.ErrorMsg
}
func (p *PacketError) SetErrorCode(val int64) {
	p.ErrorCode = val
}
func (p *PacketError) SetErrorMsg(val string) {
	p.ErrorMsg = val
}

var fieldIDToName_PacketError = map[int16]string{
	1: "error_code",
	2: "error_msg",
}

func (p *PacketError) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PacketError[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *PacketError) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ErrorCode = _field
	return nil
}
func (p *PacketError) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ErrorMsg = _field
	return nil
}

func (p *PacketError) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("PacketError"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *PacketError) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("error_code", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.ErrorCode); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *PacketError) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("error_msg", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ErrorMsg); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *PacketError) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PacketError(%+v)", *p)

}

func (p *PacketError) DeepEqual(ano *PacketError) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ErrorCode) {
		return false
	}
	if !p.Field2DeepEqual(ano.ErrorMsg) {
		return false
	}
	return true
}

func (p *PacketError) Field1DeepEqual(src int64) bool {

	if p.ErrorCode != src {
		return false
	}
	return true
}
func (p *PacketError) Field2DeepEqual(src string) bool {

	if strings.Compare(p.ErrorMsg, src) != 0 {
		return false
	}
	return true
}

type TaskReq struct {
	TaskInfo     *TaskInfo          `thrift:"task_info,1,optional" frugal:"1,optional,TaskInfo" json:"task_info,omitempty"`
	Variables    *copilot.Variables `thrift:"variables,2" frugal:"2,default,copilot.Variables" json:"variables"`
	ConnectorId  int64              `thrift:"connector_id,3" frugal:"3,default,i64" json:"connector_id"`
	ConnectorUid string             `thrift:"connector_uid,4" frugal:"4,default,string" json:"connector_uid"`
	Ext          map[string]string  `thrift:"ext,5" frugal:"5,default,map<string:string>" json:"ext"`
	LlmScene     copilot.LLMScene   `thrift:"llm_scene,6" frugal:"6,default,LLMScene" json:"llm_scene"`
	SourceType   copilot.SourceType `thrift:"source_type,7" frugal:"7,default,SourceType" json:"source_type"`
	ChainInfo    *ChainInfoCustom   `thrift:"chain_info,8,optional" frugal:"8,optional,ChainInfoCustom" json:"chain_info,omitempty"`
}

func NewTaskReq() *TaskReq {
	return &TaskReq{}
}

func (p *TaskReq) InitDefault() {
}

var TaskReq_TaskInfo_DEFAULT *TaskInfo

func (p *TaskReq) GetTaskInfo() (v *TaskInfo) {
	if !p.IsSetTaskInfo() {
		return TaskReq_TaskInfo_DEFAULT
	}
	return p.TaskInfo
}

var TaskReq_Variables_DEFAULT *copilot.Variables

func (p *TaskReq) GetVariables() (v *copilot.Variables) {
	if !p.IsSetVariables() {
		return TaskReq_Variables_DEFAULT
	}
	return p.Variables
}

func (p *TaskReq) GetConnectorId() (v int64) {
	return p.ConnectorId
}

func (p *TaskReq) GetConnectorUid() (v string) {
	return p.ConnectorUid
}

func (p *TaskReq) GetExt() (v map[string]string) {
	return p.Ext
}

func (p *TaskReq) GetLlmScene() (v copilot.LLMScene) {
	return p.LlmScene
}

func (p *TaskReq) GetSourceType() (v copilot.SourceType) {
	return p.SourceType
}

var TaskReq_ChainInfo_DEFAULT *ChainInfoCustom

func (p *TaskReq) GetChainInfo() (v *ChainInfoCustom) {
	if !p.IsSetChainInfo() {
		return TaskReq_ChainInfo_DEFAULT
	}
	return p.ChainInfo
}
func (p *TaskReq) SetTaskInfo(val *TaskInfo) {
	p.TaskInfo = val
}
func (p *TaskReq) SetVariables(val *copilot.Variables) {
	p.Variables = val
}
func (p *TaskReq) SetConnectorId(val int64) {
	p.ConnectorId = val
}
func (p *TaskReq) SetConnectorUid(val string) {
	p.ConnectorUid = val
}
func (p *TaskReq) SetExt(val map[string]string) {
	p.Ext = val
}
func (p *TaskReq) SetLlmScene(val copilot.LLMScene) {
	p.LlmScene = val
}
func (p *TaskReq) SetSourceType(val copilot.SourceType) {
	p.SourceType = val
}
func (p *TaskReq) SetChainInfo(val *ChainInfoCustom) {
	p.ChainInfo = val
}

var fieldIDToName_TaskReq = map[int16]string{
	1: "task_info",
	2: "variables",
	3: "connector_id",
	4: "connector_uid",
	5: "ext",
	6: "llm_scene",
	7: "source_type",
	8: "chain_info",
}

func (p *TaskReq) IsSetTaskInfo() bool {
	return p.TaskInfo != nil
}

func (p *TaskReq) IsSetVariables() bool {
	return p.Variables != nil
}

func (p *TaskReq) IsSetChainInfo() bool {
	return p.ChainInfo != nil
}

func (p *TaskReq) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TaskReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *TaskReq) ReadField1(iprot thrift.TProtocol) error {
	_field := NewTaskInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.TaskInfo = _field
	return nil
}
func (p *TaskReq) ReadField2(iprot thrift.TProtocol) error {
	_field := copilot.NewVariables()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Variables = _field
	return nil
}
func (p *TaskReq) ReadField3(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ConnectorId = _field
	return nil
}
func (p *TaskReq) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ConnectorUid = _field
	return nil
}
func (p *TaskReq) ReadField5(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_key = v
		}

		var _val string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_val = v
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.Ext = _field
	return nil
}
func (p *TaskReq) ReadField6(iprot thrift.TProtocol) error {

	var _field copilot.LLMScene
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = copilot.LLMScene(v)
	}
	p.LlmScene = _field
	return nil
}
func (p *TaskReq) ReadField7(iprot thrift.TProtocol) error {

	var _field copilot.SourceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = copilot.SourceType(v)
	}
	p.SourceType = _field
	return nil
}
func (p *TaskReq) ReadField8(iprot thrift.TProtocol) error {
	_field := NewChainInfoCustom()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.ChainInfo = _field
	return nil
}

func (p *TaskReq) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("TaskReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TaskReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetTaskInfo() {
		if err = oprot.WriteFieldBegin("task_info", thrift.STRUCT, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.TaskInfo.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *TaskReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("variables", thrift.STRUCT, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Variables.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *TaskReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("connector_id", thrift.I64, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.ConnectorId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *TaskReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("connector_uid", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ConnectorUid); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *TaskReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ext", thrift.MAP, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.Ext)); err != nil {
		return err
	}
	for k, v := range p.Ext {
		if err := oprot.WriteString(k); err != nil {
			return err
		}
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteMapEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *TaskReq) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("llm_scene", thrift.I32, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.LlmScene)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *TaskReq) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("source_type", thrift.I32, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.SourceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}
func (p *TaskReq) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetChainInfo() {
		if err = oprot.WriteFieldBegin("chain_info", thrift.STRUCT, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.ChainInfo.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *TaskReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TaskReq(%+v)", *p)

}

func (p *TaskReq) DeepEqual(ano *TaskReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TaskInfo) {
		return false
	}
	if !p.Field2DeepEqual(ano.Variables) {
		return false
	}
	if !p.Field3DeepEqual(ano.ConnectorId) {
		return false
	}
	if !p.Field4DeepEqual(ano.ConnectorUid) {
		return false
	}
	if !p.Field5DeepEqual(ano.Ext) {
		return false
	}
	if !p.Field6DeepEqual(ano.LlmScene) {
		return false
	}
	if !p.Field7DeepEqual(ano.SourceType) {
		return false
	}
	if !p.Field8DeepEqual(ano.ChainInfo) {
		return false
	}
	return true
}

func (p *TaskReq) Field1DeepEqual(src *TaskInfo) bool {

	if !p.TaskInfo.DeepEqual(src) {
		return false
	}
	return true
}
func (p *TaskReq) Field2DeepEqual(src *copilot.Variables) bool {

	if !p.Variables.DeepEqual(src) {
		return false
	}
	return true
}
func (p *TaskReq) Field3DeepEqual(src int64) bool {

	if p.ConnectorId != src {
		return false
	}
	return true
}
func (p *TaskReq) Field4DeepEqual(src string) bool {

	if strings.Compare(p.ConnectorUid, src) != 0 {
		return false
	}
	return true
}
func (p *TaskReq) Field5DeepEqual(src map[string]string) bool {

	if len(p.Ext) != len(src) {
		return false
	}
	for k, v := range p.Ext {
		_src := src[k]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *TaskReq) Field6DeepEqual(src copilot.LLMScene) bool {

	if p.LlmScene != src {
		return false
	}
	return true
}
func (p *TaskReq) Field7DeepEqual(src copilot.SourceType) bool {

	if p.SourceType != src {
		return false
	}
	return true
}
func (p *TaskReq) Field8DeepEqual(src *ChainInfoCustom) bool {

	if !p.ChainInfo.DeepEqual(src) {
		return false
	}
	return true
}

type ChainInfoCustom struct {
	ModelInfo            *ModelInfoCustom              `thrift:"model_info,1,optional" frugal:"1,optional,ModelInfoCustom" json:"model_info,omitempty"`
	Prompt               *copilot.Prompt               `thrift:"prompt,2,optional" frugal:"2,optional,copilot.Prompt" json:"prompt,omitempty"`
	PromptTemplateFromat *copilot.PromptTemplateFormat `thrift:"prompt_template_fromat,3,optional" frugal:"3,optional,PromptTemplateFormat" json:"prompt_template_fromat,omitempty"`
}

func NewChainInfoCustom() *ChainInfoCustom {
	return &ChainInfoCustom{}
}

func (p *ChainInfoCustom) InitDefault() {
}

var ChainInfoCustom_ModelInfo_DEFAULT *ModelInfoCustom

func (p *ChainInfoCustom) GetModelInfo() (v *ModelInfoCustom) {
	if !p.IsSetModelInfo() {
		return ChainInfoCustom_ModelInfo_DEFAULT
	}
	return p.ModelInfo
}

var ChainInfoCustom_Prompt_DEFAULT *copilot.Prompt

func (p *ChainInfoCustom) GetPrompt() (v *copilot.Prompt) {
	if !p.IsSetPrompt() {
		return ChainInfoCustom_Prompt_DEFAULT
	}
	return p.Prompt
}

var ChainInfoCustom_PromptTemplateFromat_DEFAULT copilot.PromptTemplateFormat

func (p *ChainInfoCustom) GetPromptTemplateFromat() (v copilot.PromptTemplateFormat) {
	if !p.IsSetPromptTemplateFromat() {
		return ChainInfoCustom_PromptTemplateFromat_DEFAULT
	}
	return *p.PromptTemplateFromat
}
func (p *ChainInfoCustom) SetModelInfo(val *ModelInfoCustom) {
	p.ModelInfo = val
}
func (p *ChainInfoCustom) SetPrompt(val *copilot.Prompt) {
	p.Prompt = val
}
func (p *ChainInfoCustom) SetPromptTemplateFromat(val *copilot.PromptTemplateFormat) {
	p.PromptTemplateFromat = val
}

var fieldIDToName_ChainInfoCustom = map[int16]string{
	1: "model_info",
	2: "prompt",
	3: "prompt_template_fromat",
}

func (p *ChainInfoCustom) IsSetModelInfo() bool {
	return p.ModelInfo != nil
}

func (p *ChainInfoCustom) IsSetPrompt() bool {
	return p.Prompt != nil
}

func (p *ChainInfoCustom) IsSetPromptTemplateFromat() bool {
	return p.PromptTemplateFromat != nil
}

func (p *ChainInfoCustom) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ChainInfoCustom[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ChainInfoCustom) ReadField1(iprot thrift.TProtocol) error {
	_field := NewModelInfoCustom()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.ModelInfo = _field
	return nil
}
func (p *ChainInfoCustom) ReadField2(iprot thrift.TProtocol) error {
	_field := copilot.NewPrompt()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Prompt = _field
	return nil
}
func (p *ChainInfoCustom) ReadField3(iprot thrift.TProtocol) error {

	var _field *copilot.PromptTemplateFormat
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := copilot.PromptTemplateFormat(v)
		_field = &tmp
	}
	p.PromptTemplateFromat = _field
	return nil
}

func (p *ChainInfoCustom) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ChainInfoCustom"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ChainInfoCustom) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetModelInfo() {
		if err = oprot.WriteFieldBegin("model_info", thrift.STRUCT, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.ModelInfo.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ChainInfoCustom) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetPrompt() {
		if err = oprot.WriteFieldBegin("prompt", thrift.STRUCT, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Prompt.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ChainInfoCustom) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetPromptTemplateFromat() {
		if err = oprot.WriteFieldBegin("prompt_template_fromat", thrift.I32, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.PromptTemplateFromat)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ChainInfoCustom) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ChainInfoCustom(%+v)", *p)

}

func (p *ChainInfoCustom) DeepEqual(ano *ChainInfoCustom) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ModelInfo) {
		return false
	}
	if !p.Field2DeepEqual(ano.Prompt) {
		return false
	}
	if !p.Field3DeepEqual(ano.PromptTemplateFromat) {
		return false
	}
	return true
}

func (p *ChainInfoCustom) Field1DeepEqual(src *ModelInfoCustom) bool {

	if !p.ModelInfo.DeepEqual(src) {
		return false
	}
	return true
}
func (p *ChainInfoCustom) Field2DeepEqual(src *copilot.Prompt) bool {

	if !p.Prompt.DeepEqual(src) {
		return false
	}
	return true
}
func (p *ChainInfoCustom) Field3DeepEqual(src *copilot.PromptTemplateFormat) bool {

	if p.PromptTemplateFromat == src {
		return true
	} else if p.PromptTemplateFromat == nil || src == nil {
		return false
	}
	if *p.PromptTemplateFromat != *src {
		return false
	}
	return true
}

type ModelInfoCustom struct {
	ModelId     *int64   `thrift:"model_id,1,optional" frugal:"1,optional,i64" json:"model_id,omitempty"`
	Temperature *float64 `thrift:"temperature,2,optional" frugal:"2,optional,double" json:"temperature,omitempty"`
	MaxTokens   *int64   `thrift:"max_tokens,3,optional" frugal:"3,optional,i64" json:"max_tokens,omitempty"`
	TopK        *int32   `thrift:"top_k,4,optional" frugal:"4,optional,i32" json:"top_k,omitempty"`
	TopP        *float64 `thrift:"top_p,5,optional" frugal:"5,optional,double" json:"top_p,omitempty"`
}

func NewModelInfoCustom() *ModelInfoCustom {
	return &ModelInfoCustom{}
}

func (p *ModelInfoCustom) InitDefault() {
}

var ModelInfoCustom_ModelId_DEFAULT int64

func (p *ModelInfoCustom) GetModelId() (v int64) {
	if !p.IsSetModelId() {
		return ModelInfoCustom_ModelId_DEFAULT
	}
	return *p.ModelId
}

var ModelInfoCustom_Temperature_DEFAULT float64

func (p *ModelInfoCustom) GetTemperature() (v float64) {
	if !p.IsSetTemperature() {
		return ModelInfoCustom_Temperature_DEFAULT
	}
	return *p.Temperature
}

var ModelInfoCustom_MaxTokens_DEFAULT int64

func (p *ModelInfoCustom) GetMaxTokens() (v int64) {
	if !p.IsSetMaxTokens() {
		return ModelInfoCustom_MaxTokens_DEFAULT
	}
	return *p.MaxTokens
}

var ModelInfoCustom_TopK_DEFAULT int32

func (p *ModelInfoCustom) GetTopK() (v int32) {
	if !p.IsSetTopK() {
		return ModelInfoCustom_TopK_DEFAULT
	}
	return *p.TopK
}

var ModelInfoCustom_TopP_DEFAULT float64

func (p *ModelInfoCustom) GetTopP() (v float64) {
	if !p.IsSetTopP() {
		return ModelInfoCustom_TopP_DEFAULT
	}
	return *p.TopP
}
func (p *ModelInfoCustom) SetModelId(val *int64) {
	p.ModelId = val
}
func (p *ModelInfoCustom) SetTemperature(val *float64) {
	p.Temperature = val
}
func (p *ModelInfoCustom) SetMaxTokens(val *int64) {
	p.MaxTokens = val
}
func (p *ModelInfoCustom) SetTopK(val *int32) {
	p.TopK = val
}
func (p *ModelInfoCustom) SetTopP(val *float64) {
	p.TopP = val
}

var fieldIDToName_ModelInfoCustom = map[int16]string{
	1: "model_id",
	2: "temperature",
	3: "max_tokens",
	4: "top_k",
	5: "top_p",
}

func (p *ModelInfoCustom) IsSetModelId() bool {
	return p.ModelId != nil
}

func (p *ModelInfoCustom) IsSetTemperature() bool {
	return p.Temperature != nil
}

func (p *ModelInfoCustom) IsSetMaxTokens() bool {
	return p.MaxTokens != nil
}

func (p *ModelInfoCustom) IsSetTopK() bool {
	return p.TopK != nil
}

func (p *ModelInfoCustom) IsSetTopP() bool {
	return p.TopP != nil
}

func (p *ModelInfoCustom) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.DOUBLE {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.DOUBLE {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ModelInfoCustom[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ModelInfoCustom) ReadField1(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ModelId = _field
	return nil
}
func (p *ModelInfoCustom) ReadField2(iprot thrift.TProtocol) error {

	var _field *float64
	if v, err := iprot.ReadDouble(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Temperature = _field
	return nil
}
func (p *ModelInfoCustom) ReadField3(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.MaxTokens = _field
	return nil
}
func (p *ModelInfoCustom) ReadField4(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.TopK = _field
	return nil
}
func (p *ModelInfoCustom) ReadField5(iprot thrift.TProtocol) error {

	var _field *float64
	if v, err := iprot.ReadDouble(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.TopP = _field
	return nil
}

func (p *ModelInfoCustom) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ModelInfoCustom"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModelInfoCustom) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetModelId() {
		if err = oprot.WriteFieldBegin("model_id", thrift.I64, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.ModelId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ModelInfoCustom) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetTemperature() {
		if err = oprot.WriteFieldBegin("temperature", thrift.DOUBLE, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteDouble(*p.Temperature); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ModelInfoCustom) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetMaxTokens() {
		if err = oprot.WriteFieldBegin("max_tokens", thrift.I64, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.MaxTokens); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *ModelInfoCustom) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetTopK() {
		if err = oprot.WriteFieldBegin("top_k", thrift.I32, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.TopK); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *ModelInfoCustom) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetTopP() {
		if err = oprot.WriteFieldBegin("top_p", thrift.DOUBLE, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteDouble(*p.TopP); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *ModelInfoCustom) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModelInfoCustom(%+v)", *p)

}

func (p *ModelInfoCustom) DeepEqual(ano *ModelInfoCustom) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ModelId) {
		return false
	}
	if !p.Field2DeepEqual(ano.Temperature) {
		return false
	}
	if !p.Field3DeepEqual(ano.MaxTokens) {
		return false
	}
	if !p.Field4DeepEqual(ano.TopK) {
		return false
	}
	if !p.Field5DeepEqual(ano.TopP) {
		return false
	}
	return true
}

func (p *ModelInfoCustom) Field1DeepEqual(src *int64) bool {

	if p.ModelId == src {
		return true
	} else if p.ModelId == nil || src == nil {
		return false
	}
	if *p.ModelId != *src {
		return false
	}
	return true
}
func (p *ModelInfoCustom) Field2DeepEqual(src *float64) bool {

	if p.Temperature == src {
		return true
	} else if p.Temperature == nil || src == nil {
		return false
	}
	if *p.Temperature != *src {
		return false
	}
	return true
}
func (p *ModelInfoCustom) Field3DeepEqual(src *int64) bool {

	if p.MaxTokens == src {
		return true
	} else if p.MaxTokens == nil || src == nil {
		return false
	}
	if *p.MaxTokens != *src {
		return false
	}
	return true
}
func (p *ModelInfoCustom) Field4DeepEqual(src *int32) bool {

	if p.TopK == src {
		return true
	} else if p.TopK == nil || src == nil {
		return false
	}
	if *p.TopK != *src {
		return false
	}
	return true
}
func (p *ModelInfoCustom) Field5DeepEqual(src *float64) bool {

	if p.TopP == src {
		return true
	} else if p.TopP == nil || src == nil {
		return false
	}
	if *p.TopP != *src {
		return false
	}
	return true
}

type TaskInfo struct {
	AppId       int64  `thrift:"app_id,1" frugal:"1,default,i64" json:"app_id"`
	TaskName    string `thrift:"task_name,2" frugal:"2,default,string" json:"task_name"`
	TaskVersion int64  `thrift:"task_version,3" frugal:"3,default,i64" json:"task_version"`
	AppName     string `thrift:"app_name,4" frugal:"4,default,string" json:"app_name"`
}

func NewTaskInfo() *TaskInfo {
	return &TaskInfo{}
}

func (p *TaskInfo) InitDefault() {
}

func (p *TaskInfo) GetAppId() (v int64) {
	return p.AppId
}

func (p *TaskInfo) GetTaskName() (v string) {
	return p.TaskName
}

func (p *TaskInfo) GetTaskVersion() (v int64) {
	return p.TaskVersion
}

func (p *TaskInfo) GetAppName() (v string) {
	return p.AppName
}
func (p *TaskInfo) SetAppId(val int64) {
	p.AppId = val
}
func (p *TaskInfo) SetTaskName(val string) {
	p.TaskName = val
}
func (p *TaskInfo) SetTaskVersion(val int64) {
	p.TaskVersion = val
}
func (p *TaskInfo) SetAppName(val string) {
	p.AppName = val
}

var fieldIDToName_TaskInfo = map[int16]string{
	1: "app_id",
	2: "task_name",
	3: "task_version",
	4: "app_name",
}

func (p *TaskInfo) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TaskInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *TaskInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AppId = _field
	return nil
}
func (p *TaskInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TaskName = _field
	return nil
}
func (p *TaskInfo) ReadField3(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TaskVersion = _field
	return nil
}
func (p *TaskInfo) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AppName = _field
	return nil
}

func (p *TaskInfo) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("TaskInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TaskInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("app_id", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.AppId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *TaskInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("task_name", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TaskName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *TaskInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("task_version", thrift.I64, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.TaskVersion); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *TaskInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("app_name", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AppName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *TaskInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TaskInfo(%+v)", *p)

}

func (p *TaskInfo) DeepEqual(ano *TaskInfo) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.AppId) {
		return false
	}
	if !p.Field2DeepEqual(ano.TaskName) {
		return false
	}
	if !p.Field3DeepEqual(ano.TaskVersion) {
		return false
	}
	if !p.Field4DeepEqual(ano.AppName) {
		return false
	}
	return true
}

func (p *TaskInfo) Field1DeepEqual(src int64) bool {

	if p.AppId != src {
		return false
	}
	return true
}
func (p *TaskInfo) Field2DeepEqual(src string) bool {

	if strings.Compare(p.TaskName, src) != 0 {
		return false
	}
	return true
}
func (p *TaskInfo) Field3DeepEqual(src int64) bool {

	if p.TaskVersion != src {
		return false
	}
	return true
}
func (p *TaskInfo) Field4DeepEqual(src string) bool {

	if strings.Compare(p.AppName, src) != 0 {
		return false
	}
	return true
}

type TaskFlow struct {
	TaskReq *TaskReq `thrift:"task_req,1" frugal:"1,default,TaskReq" json:"task_req"`
}

func NewTaskFlow() *TaskFlow {
	return &TaskFlow{}
}

func (p *TaskFlow) InitDefault() {
}

var TaskFlow_TaskReq_DEFAULT *TaskReq

func (p *TaskFlow) GetTaskReq() (v *TaskReq) {
	if !p.IsSetTaskReq() {
		return TaskFlow_TaskReq_DEFAULT
	}
	return p.TaskReq
}
func (p *TaskFlow) SetTaskReq(val *TaskReq) {
	p.TaskReq = val
}

var fieldIDToName_TaskFlow = map[int16]string{
	1: "task_req",
}

func (p *TaskFlow) IsSetTaskReq() bool {
	return p.TaskReq != nil
}

func (p *TaskFlow) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TaskFlow[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *TaskFlow) ReadField1(iprot thrift.TProtocol) error {
	_field := NewTaskReq()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.TaskReq = _field
	return nil
}

func (p *TaskFlow) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("TaskFlow"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TaskFlow) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("task_req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.TaskReq.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *TaskFlow) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TaskFlow(%+v)", *p)

}

func (p *TaskFlow) DeepEqual(ano *TaskFlow) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TaskReq) {
		return false
	}
	return true
}

func (p *TaskFlow) Field1DeepEqual(src *TaskReq) bool {

	if !p.TaskReq.DeepEqual(src) {
		return false
	}
	return true
}

type ToolFlow struct {
	PluginId  int64  `thrift:"plugin_id,1" frugal:"1,default,i64" json:"plugin_id"`
	ApiName   string `thrift:"api_name,2" frugal:"2,default,string" json:"api_name"`
	Arguments string `thrift:"arguments,3" frugal:"3,default,string" json:"arguments"`
}

func NewToolFlow() *ToolFlow {
	return &ToolFlow{}
}

func (p *ToolFlow) InitDefault() {
}

func (p *ToolFlow) GetPluginId() (v int64) {
	return p.PluginId
}

func (p *ToolFlow) GetApiName() (v string) {
	return p.ApiName
}

func (p *ToolFlow) GetArguments() (v string) {
	return p.Arguments
}
func (p *ToolFlow) SetPluginId(val int64) {
	p.PluginId = val
}
func (p *ToolFlow) SetApiName(val string) {
	p.ApiName = val
}
func (p *ToolFlow) SetArguments(val string) {
	p.Arguments = val
}

var fieldIDToName_ToolFlow = map[int16]string{
	1: "plugin_id",
	2: "api_name",
	3: "arguments",
}

func (p *ToolFlow) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ToolFlow[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ToolFlow) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.PluginId = _field
	return nil
}
func (p *ToolFlow) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ApiName = _field
	return nil
}
func (p *ToolFlow) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Arguments = _field
	return nil
}

func (p *ToolFlow) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ToolFlow"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ToolFlow) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("plugin_id", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.PluginId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ToolFlow) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("api_name", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ApiName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ToolFlow) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("arguments", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Arguments); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ToolFlow) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ToolFlow(%+v)", *p)

}

func (p *ToolFlow) DeepEqual(ano *ToolFlow) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.PluginId) {
		return false
	}
	if !p.Field2DeepEqual(ano.ApiName) {
		return false
	}
	if !p.Field3DeepEqual(ano.Arguments) {
		return false
	}
	return true
}

func (p *ToolFlow) Field1DeepEqual(src int64) bool {

	if p.PluginId != src {
		return false
	}
	return true
}
func (p *ToolFlow) Field2DeepEqual(src string) bool {

	if strings.Compare(p.ApiName, src) != 0 {
		return false
	}
	return true
}
func (p *ToolFlow) Field3DeepEqual(src string) bool {

	if strings.Compare(p.Arguments, src) != 0 {
		return false
	}
	return true
}

type FinishFlow struct {
	Packet *StreamPacket `thrift:"packet,1" frugal:"1,default,StreamPacket" json:"packet"`
}

func NewFinishFlow() *FinishFlow {
	return &FinishFlow{}
}

func (p *FinishFlow) InitDefault() {
}

var FinishFlow_Packet_DEFAULT *StreamPacket

func (p *FinishFlow) GetPacket() (v *StreamPacket) {
	if !p.IsSetPacket() {
		return FinishFlow_Packet_DEFAULT
	}
	return p.Packet
}
func (p *FinishFlow) SetPacket(val *StreamPacket) {
	p.Packet = val
}

var fieldIDToName_FinishFlow = map[int16]string{
	1: "packet",
}

func (p *FinishFlow) IsSetPacket() bool {
	return p.Packet != nil
}

func (p *FinishFlow) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FinishFlow[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *FinishFlow) ReadField1(iprot thrift.TProtocol) error {
	_field := NewStreamPacket()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Packet = _field
	return nil
}

func (p *FinishFlow) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("FinishFlow"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *FinishFlow) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("packet", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Packet.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *FinishFlow) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FinishFlow(%+v)", *p)

}

func (p *FinishFlow) DeepEqual(ano *FinishFlow) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Packet) {
		return false
	}
	return true
}

func (p *FinishFlow) Field1DeepEqual(src *StreamPacket) bool {

	if !p.Packet.DeepEqual(src) {
		return false
	}
	return true
}
