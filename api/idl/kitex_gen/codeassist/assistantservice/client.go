// Code generated by Kitex v1.17.2. DO NOT EDIT.

package assistantservice

import (
	codeassist "code.byted.org/devgpt/kiwis/api/idl/kitex_gen/codeassist"
	byted "code.byted.org/kite/kitex/byted"
	client "code.byted.org/kite/kitex/client"
	callopt "code.byted.org/kite/kitex/client/callopt"
	"context"
	"github.com/cloudwego/kitex/client/callopt/streamcall"
	"github.com/cloudwego/kitex/client/streamclient"
	streaming "github.com/cloudwego/kitex/pkg/streaming"
	transport "github.com/cloudwego/kitex/transport"
)

// Client is designed to provide IDL-compatible methods with call-option parameter for kitex framework.
type Client interface {
	Ping(ctx context.Context, req *codeassist.PingRequest, callOptions ...callopt.Option) (r *codeassist.PingResponse, err error)
	PromptsRender(ctx context.Context, req *codeassist.PromptsRenderRequest, callOptions ...callopt.Option) (r *codeassist.PromptsRenderResponse, err error)
	E2EPromptsRender(ctx context.Context, req *codeassist.E2EPromptsRenderRequest, callOptions ...callopt.Option) (r *codeassist.E2EPromptsRenderResponse, err error)
	HomePage(ctx context.Context, req *codeassist.HomePageRequest, callOptions ...callopt.Option) (r *codeassist.HomePageResponse, err error)
	SniffRepositoryLink(ctx context.Context, req *codeassist.SniffRepositoryLinkRequest, callOptions ...callopt.Option) (r *codeassist.SniffRepositoryLinkResponse, err error)
	CreateContext(ctx context.Context, req *codeassist.CreateContextRequest, callOptions ...callopt.Option) (r *codeassist.CreateContextResponse, err error)
	MGetContextStatus(ctx context.Context, req *codeassist.MGetContextStatusRequest, callOptions ...callopt.Option) (r *codeassist.MGetContextStatusResponse, err error)
	GetContextDirectoryNodes(ctx context.Context, req *codeassist.GetContextDirectoryNodesRequest, callOptions ...callopt.Option) (r *codeassist.GetContextDirectoryNodesResponse, err error)
	GetRepoFileContent(ctx context.Context, req *codeassist.GetRepoFileContentRequest, callOptions ...callopt.Option) (r *codeassist.GetRepoFileContentResponse, err error)
	GetContextInfo(ctx context.Context, req *codeassist.GetContextInfoRequest, callOptions ...callopt.Option) (r *codeassist.GetContextInfoResponse, err error)
	GetContextDownloadURL(ctx context.Context, req *codeassist.GetContextDownloadURLRequest, callOptions ...callopt.Option) (r *codeassist.GetContextDownloadURLResponse, err error)
	GetCodeResourceDownloadURL(ctx context.Context, req *codeassist.GetCodeResourceDownloadURLRequest, callOptions ...callopt.Option) (r *codeassist.GetCodeResourceDownloadURLResponse, err error)
	DesensitizeContext(ctx context.Context, req *codeassist.DesensitizeContextRequest, callOptions ...callopt.Option) (r *codeassist.DesensitizeContextResponse, err error)
	BatchCheckFilePath(ctx context.Context, req *codeassist.BatchCheckFilePathRequest, callOptions ...callopt.Option) (r *codeassist.BatchCheckFilePathResponse, err error)
	CodeExecutable(ctx context.Context, req *codeassist.CodeExecutableRequest, callOptions ...callopt.Option) (r *codeassist.CodeExecutableResponse, err error)
	RunCode(ctx context.Context, req *codeassist.RunCodeRequest, callOptions ...callopt.Option) (r *codeassist.RunCodeResponse, err error)
	RunCodeV2(ctx context.Context, req *codeassist.RunCodeRequestV2, callOptions ...callopt.Option) (r *codeassist.RunCodeResponseV2, err error)
	GetArtifactTemplateFile(ctx context.Context, req *codeassist.GetArtifactTemplateFileRequest, callOptions ...callopt.Option) (r *codeassist.GetArtifactTemplateFileResponse, err error)
	GetArtifactTemplateDir(ctx context.Context, req *codeassist.GetArtifactTemplateDirRequest, callOptions ...callopt.Option) (r *codeassist.GetArtifactTemplateDirResponse, err error)
	CompileCodeArtifact(ctx context.Context, req *codeassist.CompileCodeArtifactRequest, callOptions ...callopt.Option) (r *codeassist.CompileCodeArtifactResponse, err error)
	GetCompileStatus(ctx context.Context, req *codeassist.GetCompileStatusRequest, callOptions ...callopt.Option) (r *codeassist.GetCompileStatusResponse, err error)
	GetArtifactsCodeURI(ctx context.Context, req *codeassist.GetArtifactsCodeURIRequest, callOptions ...callopt.Option) (r *codeassist.GetArtifactsCodeURIResponse, err error)
	CreateSandbox(ctx context.Context, req *codeassist.CreateSandboxRequest, callOptions ...callopt.Option) (r *codeassist.CreateSandboxResponse, err error)
	ReleaseSandbox(ctx context.Context, req *codeassist.ReleaseSandboxRequest, callOptions ...callopt.Option) (r *codeassist.ReleaseSandboxResponse, err error)
	ExecuteCode(ctx context.Context, req *codeassist.ExecuteCodeRequest, callOptions ...callopt.Option) (r *codeassist.ExecuteCodeResponse, err error)
	UploadFile(ctx context.Context, req *codeassist.UploadFileRequest, callOptions ...callopt.Option) (r *codeassist.UploadFileResponse, err error)
	DownloadFile(ctx context.Context, req *codeassist.DownloadFileRequest, callOptions ...callopt.Option) (r *codeassist.DownloadFileResponse, err error)
	ImageReview(ctx context.Context, req *codeassist.ImageReviewRequest, callOptions ...callopt.Option) (r *codeassist.ImageReviewResponse, err error)
	SearchImages(ctx context.Context, req *codeassist.SearchImagesRequest, callOptions ...callopt.Option) (r *codeassist.SearchImagesResponse, err error)
	InterruptCodeAgentTask(ctx context.Context, req *codeassist.InterruptCodeAgentTaskRequest, callOptions ...callopt.Option) (r *codeassist.InterruptCodeAgentTaskResponse, err error)
	ExecuteJobCallback(ctx context.Context, req *codeassist.ExecuteJobCallbackReq, callOptions ...callopt.Option) (r *codeassist.ExecuteJobCallbackResp, err error)
}

// StreamClient is designed to provide Interface for Streaming APIs.
type StreamClient interface {
	Chat(ctx context.Context, req *codeassist.ChatRequest, callOptions ...streamcall.Option) (stream AssistantService_ChatClient, err error)
}

type AssistantService_ChatClient interface {
	streaming.Stream
	Recv() (*codeassist.StreamChatResponse, error)
}

// NewClient creates a client for the service defined in IDL.
func NewClient(destService string, opts ...client.Option) (Client, error) {
	var options []client.Option
	options = append(options, client.WithDestService(destService))

	config := byted.NewClientConfig()
	config.DestService = destService
	options = append(options, byted.ClientSuiteWithConfig(serviceInfoForClient(), config))

	options = append(options, opts...)

	kc, err := client.NewClient(serviceInfoForClient(), options...)
	if err != nil {
		return nil, err
	}
	return &kAssistantServiceClient{
		kClient: newServiceClient(kc),
	}, nil
}

// MustNewClient creates a client for the service defined in IDL. It panics if any error occurs.
func MustNewClient(destService string, opts ...client.Option) Client {
	kc, err := NewClient(destService, opts...)
	if err != nil {
		panic(err)
	}
	return kc
}

type kAssistantServiceClient struct {
	*kClient
}

func (p *kAssistantServiceClient) Ping(ctx context.Context, req *codeassist.PingRequest, callOptions ...callopt.Option) (r *codeassist.PingResponse, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.Ping(ctx, req)
}

func (p *kAssistantServiceClient) PromptsRender(ctx context.Context, req *codeassist.PromptsRenderRequest, callOptions ...callopt.Option) (r *codeassist.PromptsRenderResponse, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.PromptsRender(ctx, req)
}

func (p *kAssistantServiceClient) E2EPromptsRender(ctx context.Context, req *codeassist.E2EPromptsRenderRequest, callOptions ...callopt.Option) (r *codeassist.E2EPromptsRenderResponse, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.E2EPromptsRender(ctx, req)
}

func (p *kAssistantServiceClient) HomePage(ctx context.Context, req *codeassist.HomePageRequest, callOptions ...callopt.Option) (r *codeassist.HomePageResponse, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.HomePage(ctx, req)
}

func (p *kAssistantServiceClient) SniffRepositoryLink(ctx context.Context, req *codeassist.SniffRepositoryLinkRequest, callOptions ...callopt.Option) (r *codeassist.SniffRepositoryLinkResponse, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.SniffRepositoryLink(ctx, req)
}

func (p *kAssistantServiceClient) CreateContext(ctx context.Context, req *codeassist.CreateContextRequest, callOptions ...callopt.Option) (r *codeassist.CreateContextResponse, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.CreateContext(ctx, req)
}

func (p *kAssistantServiceClient) MGetContextStatus(ctx context.Context, req *codeassist.MGetContextStatusRequest, callOptions ...callopt.Option) (r *codeassist.MGetContextStatusResponse, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.MGetContextStatus(ctx, req)
}

func (p *kAssistantServiceClient) GetContextDirectoryNodes(ctx context.Context, req *codeassist.GetContextDirectoryNodesRequest, callOptions ...callopt.Option) (r *codeassist.GetContextDirectoryNodesResponse, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.GetContextDirectoryNodes(ctx, req)
}

func (p *kAssistantServiceClient) GetRepoFileContent(ctx context.Context, req *codeassist.GetRepoFileContentRequest, callOptions ...callopt.Option) (r *codeassist.GetRepoFileContentResponse, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.GetRepoFileContent(ctx, req)
}

func (p *kAssistantServiceClient) GetContextInfo(ctx context.Context, req *codeassist.GetContextInfoRequest, callOptions ...callopt.Option) (r *codeassist.GetContextInfoResponse, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.GetContextInfo(ctx, req)
}

func (p *kAssistantServiceClient) GetContextDownloadURL(ctx context.Context, req *codeassist.GetContextDownloadURLRequest, callOptions ...callopt.Option) (r *codeassist.GetContextDownloadURLResponse, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.GetContextDownloadURL(ctx, req)
}

func (p *kAssistantServiceClient) GetCodeResourceDownloadURL(ctx context.Context, req *codeassist.GetCodeResourceDownloadURLRequest, callOptions ...callopt.Option) (r *codeassist.GetCodeResourceDownloadURLResponse, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.GetCodeResourceDownloadURL(ctx, req)
}

func (p *kAssistantServiceClient) DesensitizeContext(ctx context.Context, req *codeassist.DesensitizeContextRequest, callOptions ...callopt.Option) (r *codeassist.DesensitizeContextResponse, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.DesensitizeContext(ctx, req)
}

func (p *kAssistantServiceClient) BatchCheckFilePath(ctx context.Context, req *codeassist.BatchCheckFilePathRequest, callOptions ...callopt.Option) (r *codeassist.BatchCheckFilePathResponse, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.BatchCheckFilePath(ctx, req)
}

func (p *kAssistantServiceClient) CodeExecutable(ctx context.Context, req *codeassist.CodeExecutableRequest, callOptions ...callopt.Option) (r *codeassist.CodeExecutableResponse, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.CodeExecutable(ctx, req)
}

func (p *kAssistantServiceClient) RunCode(ctx context.Context, req *codeassist.RunCodeRequest, callOptions ...callopt.Option) (r *codeassist.RunCodeResponse, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.RunCode(ctx, req)
}

func (p *kAssistantServiceClient) RunCodeV2(ctx context.Context, req *codeassist.RunCodeRequestV2, callOptions ...callopt.Option) (r *codeassist.RunCodeResponseV2, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.RunCodeV2(ctx, req)
}

func (p *kAssistantServiceClient) GetArtifactTemplateFile(ctx context.Context, req *codeassist.GetArtifactTemplateFileRequest, callOptions ...callopt.Option) (r *codeassist.GetArtifactTemplateFileResponse, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.GetArtifactTemplateFile(ctx, req)
}

func (p *kAssistantServiceClient) GetArtifactTemplateDir(ctx context.Context, req *codeassist.GetArtifactTemplateDirRequest, callOptions ...callopt.Option) (r *codeassist.GetArtifactTemplateDirResponse, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.GetArtifactTemplateDir(ctx, req)
}

func (p *kAssistantServiceClient) CompileCodeArtifact(ctx context.Context, req *codeassist.CompileCodeArtifactRequest, callOptions ...callopt.Option) (r *codeassist.CompileCodeArtifactResponse, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.CompileCodeArtifact(ctx, req)
}

func (p *kAssistantServiceClient) GetCompileStatus(ctx context.Context, req *codeassist.GetCompileStatusRequest, callOptions ...callopt.Option) (r *codeassist.GetCompileStatusResponse, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.GetCompileStatus(ctx, req)
}

func (p *kAssistantServiceClient) GetArtifactsCodeURI(ctx context.Context, req *codeassist.GetArtifactsCodeURIRequest, callOptions ...callopt.Option) (r *codeassist.GetArtifactsCodeURIResponse, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.GetArtifactsCodeURI(ctx, req)
}

func (p *kAssistantServiceClient) CreateSandbox(ctx context.Context, req *codeassist.CreateSandboxRequest, callOptions ...callopt.Option) (r *codeassist.CreateSandboxResponse, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.CreateSandbox(ctx, req)
}

func (p *kAssistantServiceClient) ReleaseSandbox(ctx context.Context, req *codeassist.ReleaseSandboxRequest, callOptions ...callopt.Option) (r *codeassist.ReleaseSandboxResponse, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.ReleaseSandbox(ctx, req)
}

func (p *kAssistantServiceClient) ExecuteCode(ctx context.Context, req *codeassist.ExecuteCodeRequest, callOptions ...callopt.Option) (r *codeassist.ExecuteCodeResponse, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.ExecuteCode(ctx, req)
}

func (p *kAssistantServiceClient) UploadFile(ctx context.Context, req *codeassist.UploadFileRequest, callOptions ...callopt.Option) (r *codeassist.UploadFileResponse, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.UploadFile(ctx, req)
}

func (p *kAssistantServiceClient) DownloadFile(ctx context.Context, req *codeassist.DownloadFileRequest, callOptions ...callopt.Option) (r *codeassist.DownloadFileResponse, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.DownloadFile(ctx, req)
}

func (p *kAssistantServiceClient) ImageReview(ctx context.Context, req *codeassist.ImageReviewRequest, callOptions ...callopt.Option) (r *codeassist.ImageReviewResponse, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.ImageReview(ctx, req)
}

func (p *kAssistantServiceClient) SearchImages(ctx context.Context, req *codeassist.SearchImagesRequest, callOptions ...callopt.Option) (r *codeassist.SearchImagesResponse, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.SearchImages(ctx, req)
}

func (p *kAssistantServiceClient) InterruptCodeAgentTask(ctx context.Context, req *codeassist.InterruptCodeAgentTaskRequest, callOptions ...callopt.Option) (r *codeassist.InterruptCodeAgentTaskResponse, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.InterruptCodeAgentTask(ctx, req)
}

func (p *kAssistantServiceClient) ExecuteJobCallback(ctx context.Context, req *codeassist.ExecuteJobCallbackReq, callOptions ...callopt.Option) (r *codeassist.ExecuteJobCallbackResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.ExecuteJobCallback(ctx, req)
}

// NewStreamClient creates a stream client for the service's streaming APIs defined in IDL.
func NewStreamClient(destService string, opts ...streamclient.Option) (StreamClient, error) {
	var options []client.Option
	options = append(options, client.WithDestService(destService))

	config := byted.NewClientConfig()
	config.DestService = destService
	config.Transport = byted.GRPC
	options = append(options, byted.ClientSuiteWithConfig(serviceInfoForStreamClient(), config))

	options = append(options, client.WithTransportProtocol(transport.GRPC))
	options = append(options, streamclient.GetClientOptions(opts)...)

	kc, err := client.NewClient(serviceInfoForStreamClient(), options...)
	if err != nil {
		return nil, err
	}
	return &kAssistantServiceStreamClient{
		kClient: newServiceClient(kc),
	}, nil
}

// MustNewStreamClient creates a stream client for the service's streaming APIs defined in IDL.
// It panics if any error occurs.
func MustNewStreamClient(destService string, opts ...streamclient.Option) StreamClient {
	kc, err := NewStreamClient(destService, opts...)
	if err != nil {
		panic(err)
	}
	return kc
}

type kAssistantServiceStreamClient struct {
	*kClient
}

func (p *kAssistantServiceStreamClient) Chat(ctx context.Context, req *codeassist.ChatRequest, callOptions ...streamcall.Option) (stream AssistantService_ChatClient, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, streamcall.GetCallOptions(callOptions))
	return p.kClient.Chat(ctx, req)
}

// NewClientWithBytedConfig creates a client for the service defined in IDL.
func NewClientWithBytedConfig(destService string, config *byted.ClientConfig, opts ...client.Option) (Client, error) {
	if config == nil {
		config = byted.NewClientConfig()
	}
	config.DestService = destService

	var options []client.Option
	options = append(options, client.WithDestService(destService))

	clientServiceInfo := serviceInfoForClient()
	options = append(options, byted.ClientSuiteWithConfig(clientServiceInfo, config))
	options = append(options, opts...)
	kc, err := client.NewClient(clientServiceInfo, options...)
	if err != nil {
		return nil, err
	}
	return &kAssistantServiceClient{
		kClient: newServiceClient(kc),
	}, nil
}

// MustNewClientWithBytedConfig creates a client for the service defined in IDL. It panics if any error occurs.
func MustNewClientWithBytedConfig(destService string, config *byted.ClientConfig, opts ...client.Option) Client {
	kc, err := NewClientWithBytedConfig(destService, config, opts...)
	if err != nil {
		panic(err)
	}
	return kc
}

// NewStreamClientWithBytedConfig creates a stream client for the service defined in IDL.
func NewStreamClientWithBytedConfig(destService string, config *byted.ClientConfig, opts ...streamclient.Option) (StreamClient, error) {
	if config == nil {
		config = byted.NewClientConfig()
	}
	config.DestService = destService
	config.Transport = byted.GRPC

	var options []client.Option
	options = append(options, client.WithTransportProtocol(transport.GRPC))
	options = append(options, client.WithDestService(destService))
	options = append(options, byted.ClientSuiteWithConfig(serviceInfoForStreamClient(), config))
	options = append(options, streamclient.GetClientOptions(opts)...)
	kc, err := client.NewClient(serviceInfoForStreamClient(), options...)
	if err != nil {
		return nil, err
	}
	return &kAssistantServiceStreamClient{
		kClient: newServiceClient(kc),
	}, nil
}

// MustNewStreamClientWithBytedConfig creates a stream client for the service. It panics if any error occurs.
func MustNewStreamClientWithBytedConfig(destService string, config *byted.ClientConfig, opts ...streamclient.Option) StreamClient {
	kc, err := NewStreamClientWithBytedConfig(destService, config, opts...)
	if err != nil {
		panic(err)
	}
	return kc
}
