// Code generated by Kitex v1.17.2. DO NOT EDIT.

package assistantservice

import (
	codeassist "code.byted.org/devgpt/kiwis/api/idl/kitex_gen/codeassist"
	client "code.byted.org/kite/kitex/client"
	"context"
	"errors"
	"fmt"
	kitex "github.com/cloudwego/kitex/pkg/serviceinfo"
	streaming "github.com/cloudwego/kitex/pkg/streaming"
)

var errInvalidMessageType = errors.New("invalid message type for service method handler")

var serviceMethods = map[string]kitex.MethodInfo{
	"Ping": kitex.NewMethodInfo(
		pingHandler,
		newAssistantServicePingArgs,
		newAssistantServicePingResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"Chat": kitex.NewMethodInfo(
		chatHandler,
		newAssistantServiceChatArgs,
		newAssistantServiceChatResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingServer),
	),
	"PromptsRender": kitex.NewMethodInfo(
		promptsRenderHand<PERSON>,
		newAssistantServicePromptsRenderArgs,
		newAssistantServicePromptsRenderResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"E2EPromptsRender": kitex.NewMethodInfo(
		e2EPromptsRenderHandler,
		newAssistantServiceE2EPromptsRenderArgs,
		newAssistantServiceE2EPromptsRenderResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"HomePage": kitex.NewMethodInfo(
		homePageHandler,
		newAssistantServiceHomePageArgs,
		newAssistantServiceHomePageResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"SniffRepositoryLink": kitex.NewMethodInfo(
		sniffRepositoryLinkHandler,
		newAssistantServiceSniffRepositoryLinkArgs,
		newAssistantServiceSniffRepositoryLinkResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"CreateContext": kitex.NewMethodInfo(
		createContextHandler,
		newAssistantServiceCreateContextArgs,
		newAssistantServiceCreateContextResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"MGetContextStatus": kitex.NewMethodInfo(
		mGetContextStatusHandler,
		newAssistantServiceMGetContextStatusArgs,
		newAssistantServiceMGetContextStatusResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"GetContextDirectoryNodes": kitex.NewMethodInfo(
		getContextDirectoryNodesHandler,
		newAssistantServiceGetContextDirectoryNodesArgs,
		newAssistantServiceGetContextDirectoryNodesResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"GetRepoFileContent": kitex.NewMethodInfo(
		getRepoFileContentHandler,
		newAssistantServiceGetRepoFileContentArgs,
		newAssistantServiceGetRepoFileContentResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"GetContextInfo": kitex.NewMethodInfo(
		getContextInfoHandler,
		newAssistantServiceGetContextInfoArgs,
		newAssistantServiceGetContextInfoResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"GetContextDownloadURL": kitex.NewMethodInfo(
		getContextDownloadURLHandler,
		newAssistantServiceGetContextDownloadURLArgs,
		newAssistantServiceGetContextDownloadURLResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"GetCodeResourceDownloadURL": kitex.NewMethodInfo(
		getCodeResourceDownloadURLHandler,
		newAssistantServiceGetCodeResourceDownloadURLArgs,
		newAssistantServiceGetCodeResourceDownloadURLResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"DesensitizeContext": kitex.NewMethodInfo(
		desensitizeContextHandler,
		newAssistantServiceDesensitizeContextArgs,
		newAssistantServiceDesensitizeContextResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"BatchCheckFilePath": kitex.NewMethodInfo(
		batchCheckFilePathHandler,
		newAssistantServiceBatchCheckFilePathArgs,
		newAssistantServiceBatchCheckFilePathResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"CodeExecutable": kitex.NewMethodInfo(
		codeExecutableHandler,
		newAssistantServiceCodeExecutableArgs,
		newAssistantServiceCodeExecutableResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"RunCode": kitex.NewMethodInfo(
		runCodeHandler,
		newAssistantServiceRunCodeArgs,
		newAssistantServiceRunCodeResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"RunCodeV2": kitex.NewMethodInfo(
		runCodeV2Handler,
		newAssistantServiceRunCodeV2Args,
		newAssistantServiceRunCodeV2Result,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"GetArtifactTemplateFile": kitex.NewMethodInfo(
		getArtifactTemplateFileHandler,
		newAssistantServiceGetArtifactTemplateFileArgs,
		newAssistantServiceGetArtifactTemplateFileResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"GetArtifactTemplateDir": kitex.NewMethodInfo(
		getArtifactTemplateDirHandler,
		newAssistantServiceGetArtifactTemplateDirArgs,
		newAssistantServiceGetArtifactTemplateDirResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"CompileCodeArtifact": kitex.NewMethodInfo(
		compileCodeArtifactHandler,
		newAssistantServiceCompileCodeArtifactArgs,
		newAssistantServiceCompileCodeArtifactResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"GetCompileStatus": kitex.NewMethodInfo(
		getCompileStatusHandler,
		newAssistantServiceGetCompileStatusArgs,
		newAssistantServiceGetCompileStatusResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"GetArtifactsCodeURI": kitex.NewMethodInfo(
		getArtifactsCodeURIHandler,
		newAssistantServiceGetArtifactsCodeURIArgs,
		newAssistantServiceGetArtifactsCodeURIResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"CreateSandbox": kitex.NewMethodInfo(
		createSandboxHandler,
		newAssistantServiceCreateSandboxArgs,
		newAssistantServiceCreateSandboxResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"ReleaseSandbox": kitex.NewMethodInfo(
		releaseSandboxHandler,
		newAssistantServiceReleaseSandboxArgs,
		newAssistantServiceReleaseSandboxResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"ExecuteCode": kitex.NewMethodInfo(
		executeCodeHandler,
		newAssistantServiceExecuteCodeArgs,
		newAssistantServiceExecuteCodeResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"UploadFile": kitex.NewMethodInfo(
		uploadFileHandler,
		newAssistantServiceUploadFileArgs,
		newAssistantServiceUploadFileResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"DownloadFile": kitex.NewMethodInfo(
		downloadFileHandler,
		newAssistantServiceDownloadFileArgs,
		newAssistantServiceDownloadFileResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"ImageReview": kitex.NewMethodInfo(
		imageReviewHandler,
		newAssistantServiceImageReviewArgs,
		newAssistantServiceImageReviewResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"SearchImages": kitex.NewMethodInfo(
		searchImagesHandler,
		newAssistantServiceSearchImagesArgs,
		newAssistantServiceSearchImagesResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"InterruptCodeAgentTask": kitex.NewMethodInfo(
		interruptCodeAgentTaskHandler,
		newAssistantServiceInterruptCodeAgentTaskArgs,
		newAssistantServiceInterruptCodeAgentTaskResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"ExecuteJobCallback": kitex.NewMethodInfo(
		executeJobCallbackHandler,
		newAssistantServiceExecuteJobCallbackArgs,
		newAssistantServiceExecuteJobCallbackResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
}

var (
	assistantServiceServiceInfo                = NewServiceInfo()
	assistantServiceServiceInfoForClient       = NewServiceInfoForClient()
	assistantServiceServiceInfoForStreamClient = NewServiceInfoForStreamClient()
)

// for server
func serviceInfo() *kitex.ServiceInfo {
	return assistantServiceServiceInfo
}

// for stream client
func serviceInfoForStreamClient() *kitex.ServiceInfo {
	return assistantServiceServiceInfoForStreamClient
}

// for client
func serviceInfoForClient() *kitex.ServiceInfo {
	return assistantServiceServiceInfoForClient
}

// NewServiceInfo creates a new ServiceInfo containing all methods
func NewServiceInfo() *kitex.ServiceInfo {
	return newServiceInfo(true, true, true)
}

// NewServiceInfo creates a new ServiceInfo containing non-streaming methods
func NewServiceInfoForClient() *kitex.ServiceInfo {
	return newServiceInfo(false, false, true)
}
func NewServiceInfoForStreamClient() *kitex.ServiceInfo {
	return newServiceInfo(true, true, false)
}

func newServiceInfo(hasStreaming bool, keepStreamingMethods bool, keepNonStreamingMethods bool) *kitex.ServiceInfo {
	serviceName := "AssistantService"
	handlerType := (*codeassist.AssistantService)(nil)
	methods := map[string]kitex.MethodInfo{}
	for name, m := range serviceMethods {
		if m.IsStreaming() && !keepStreamingMethods {
			continue
		}
		if !m.IsStreaming() && !keepNonStreamingMethods {
			continue
		}
		methods[name] = m
	}
	extra := map[string]interface{}{
		"PackageName": "codeassist",
	}
	if hasStreaming {
		extra["streaming"] = hasStreaming
	}
	svcInfo := &kitex.ServiceInfo{
		ServiceName:     serviceName,
		HandlerType:     handlerType,
		Methods:         methods,
		PayloadCodec:    kitex.Thrift,
		KiteXGenVersion: "v1.17.2",
		Extra:           extra,
	}
	return svcInfo
}

func pingHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*codeassist.AssistantServicePingArgs)
	realResult := result.(*codeassist.AssistantServicePingResult)
	success, err := handler.(codeassist.AssistantService).Ping(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newAssistantServicePingArgs() interface{} {
	return codeassist.NewAssistantServicePingArgs()
}

func newAssistantServicePingResult() interface{} {
	return codeassist.NewAssistantServicePingResult()
}

func chatHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	st, ok := arg.(*streaming.Args)
	if !ok {
		return errors.New("AssistantService.Chat is a thrift streaming method, please call with Kitex StreamClient")
	}
	stream := &assistantServiceChatServer{st.Stream}
	req := new(codeassist.ChatRequest)
	if err := st.Stream.RecvMsg(req); err != nil {
		return err
	}
	return handler.(codeassist.AssistantService).Chat(req, stream)
}

type assistantServiceChatClient struct {
	streaming.Stream
}

func (x *assistantServiceChatClient) DoFinish(err error) {
	if finisher, ok := x.Stream.(streaming.WithDoFinish); ok {
		finisher.DoFinish(err)
	} else {
		panic(fmt.Sprintf("streaming.WithDoFinish is not implemented by %T", x.Stream))
	}
}
func (x *assistantServiceChatClient) Recv() (*codeassist.StreamChatResponse, error) {
	m := new(codeassist.StreamChatResponse)
	return m, x.Stream.RecvMsg(m)
}

type assistantServiceChatServer struct {
	streaming.Stream
}

func (x *assistantServiceChatServer) Send(m *codeassist.StreamChatResponse) error {
	return x.Stream.SendMsg(m)
}

func newAssistantServiceChatArgs() interface{} {
	return codeassist.NewAssistantServiceChatArgs()
}

func newAssistantServiceChatResult() interface{} {
	return codeassist.NewAssistantServiceChatResult()
}

func promptsRenderHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*codeassist.AssistantServicePromptsRenderArgs)
	realResult := result.(*codeassist.AssistantServicePromptsRenderResult)
	success, err := handler.(codeassist.AssistantService).PromptsRender(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newAssistantServicePromptsRenderArgs() interface{} {
	return codeassist.NewAssistantServicePromptsRenderArgs()
}

func newAssistantServicePromptsRenderResult() interface{} {
	return codeassist.NewAssistantServicePromptsRenderResult()
}

func e2EPromptsRenderHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*codeassist.AssistantServiceE2EPromptsRenderArgs)
	realResult := result.(*codeassist.AssistantServiceE2EPromptsRenderResult)
	success, err := handler.(codeassist.AssistantService).E2EPromptsRender(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newAssistantServiceE2EPromptsRenderArgs() interface{} {
	return codeassist.NewAssistantServiceE2EPromptsRenderArgs()
}

func newAssistantServiceE2EPromptsRenderResult() interface{} {
	return codeassist.NewAssistantServiceE2EPromptsRenderResult()
}

func homePageHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*codeassist.AssistantServiceHomePageArgs)
	realResult := result.(*codeassist.AssistantServiceHomePageResult)
	success, err := handler.(codeassist.AssistantService).HomePage(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newAssistantServiceHomePageArgs() interface{} {
	return codeassist.NewAssistantServiceHomePageArgs()
}

func newAssistantServiceHomePageResult() interface{} {
	return codeassist.NewAssistantServiceHomePageResult()
}

func sniffRepositoryLinkHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*codeassist.AssistantServiceSniffRepositoryLinkArgs)
	realResult := result.(*codeassist.AssistantServiceSniffRepositoryLinkResult)
	success, err := handler.(codeassist.AssistantService).SniffRepositoryLink(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newAssistantServiceSniffRepositoryLinkArgs() interface{} {
	return codeassist.NewAssistantServiceSniffRepositoryLinkArgs()
}

func newAssistantServiceSniffRepositoryLinkResult() interface{} {
	return codeassist.NewAssistantServiceSniffRepositoryLinkResult()
}

func createContextHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*codeassist.AssistantServiceCreateContextArgs)
	realResult := result.(*codeassist.AssistantServiceCreateContextResult)
	success, err := handler.(codeassist.AssistantService).CreateContext(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newAssistantServiceCreateContextArgs() interface{} {
	return codeassist.NewAssistantServiceCreateContextArgs()
}

func newAssistantServiceCreateContextResult() interface{} {
	return codeassist.NewAssistantServiceCreateContextResult()
}

func mGetContextStatusHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*codeassist.AssistantServiceMGetContextStatusArgs)
	realResult := result.(*codeassist.AssistantServiceMGetContextStatusResult)
	success, err := handler.(codeassist.AssistantService).MGetContextStatus(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newAssistantServiceMGetContextStatusArgs() interface{} {
	return codeassist.NewAssistantServiceMGetContextStatusArgs()
}

func newAssistantServiceMGetContextStatusResult() interface{} {
	return codeassist.NewAssistantServiceMGetContextStatusResult()
}

func getContextDirectoryNodesHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*codeassist.AssistantServiceGetContextDirectoryNodesArgs)
	realResult := result.(*codeassist.AssistantServiceGetContextDirectoryNodesResult)
	success, err := handler.(codeassist.AssistantService).GetContextDirectoryNodes(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newAssistantServiceGetContextDirectoryNodesArgs() interface{} {
	return codeassist.NewAssistantServiceGetContextDirectoryNodesArgs()
}

func newAssistantServiceGetContextDirectoryNodesResult() interface{} {
	return codeassist.NewAssistantServiceGetContextDirectoryNodesResult()
}

func getRepoFileContentHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*codeassist.AssistantServiceGetRepoFileContentArgs)
	realResult := result.(*codeassist.AssistantServiceGetRepoFileContentResult)
	success, err := handler.(codeassist.AssistantService).GetRepoFileContent(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newAssistantServiceGetRepoFileContentArgs() interface{} {
	return codeassist.NewAssistantServiceGetRepoFileContentArgs()
}

func newAssistantServiceGetRepoFileContentResult() interface{} {
	return codeassist.NewAssistantServiceGetRepoFileContentResult()
}

func getContextInfoHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*codeassist.AssistantServiceGetContextInfoArgs)
	realResult := result.(*codeassist.AssistantServiceGetContextInfoResult)
	success, err := handler.(codeassist.AssistantService).GetContextInfo(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newAssistantServiceGetContextInfoArgs() interface{} {
	return codeassist.NewAssistantServiceGetContextInfoArgs()
}

func newAssistantServiceGetContextInfoResult() interface{} {
	return codeassist.NewAssistantServiceGetContextInfoResult()
}

func getContextDownloadURLHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*codeassist.AssistantServiceGetContextDownloadURLArgs)
	realResult := result.(*codeassist.AssistantServiceGetContextDownloadURLResult)
	success, err := handler.(codeassist.AssistantService).GetContextDownloadURL(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newAssistantServiceGetContextDownloadURLArgs() interface{} {
	return codeassist.NewAssistantServiceGetContextDownloadURLArgs()
}

func newAssistantServiceGetContextDownloadURLResult() interface{} {
	return codeassist.NewAssistantServiceGetContextDownloadURLResult()
}

func getCodeResourceDownloadURLHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*codeassist.AssistantServiceGetCodeResourceDownloadURLArgs)
	realResult := result.(*codeassist.AssistantServiceGetCodeResourceDownloadURLResult)
	success, err := handler.(codeassist.AssistantService).GetCodeResourceDownloadURL(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newAssistantServiceGetCodeResourceDownloadURLArgs() interface{} {
	return codeassist.NewAssistantServiceGetCodeResourceDownloadURLArgs()
}

func newAssistantServiceGetCodeResourceDownloadURLResult() interface{} {
	return codeassist.NewAssistantServiceGetCodeResourceDownloadURLResult()
}

func desensitizeContextHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*codeassist.AssistantServiceDesensitizeContextArgs)
	realResult := result.(*codeassist.AssistantServiceDesensitizeContextResult)
	success, err := handler.(codeassist.AssistantService).DesensitizeContext(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newAssistantServiceDesensitizeContextArgs() interface{} {
	return codeassist.NewAssistantServiceDesensitizeContextArgs()
}

func newAssistantServiceDesensitizeContextResult() interface{} {
	return codeassist.NewAssistantServiceDesensitizeContextResult()
}

func batchCheckFilePathHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*codeassist.AssistantServiceBatchCheckFilePathArgs)
	realResult := result.(*codeassist.AssistantServiceBatchCheckFilePathResult)
	success, err := handler.(codeassist.AssistantService).BatchCheckFilePath(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newAssistantServiceBatchCheckFilePathArgs() interface{} {
	return codeassist.NewAssistantServiceBatchCheckFilePathArgs()
}

func newAssistantServiceBatchCheckFilePathResult() interface{} {
	return codeassist.NewAssistantServiceBatchCheckFilePathResult()
}

func codeExecutableHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*codeassist.AssistantServiceCodeExecutableArgs)
	realResult := result.(*codeassist.AssistantServiceCodeExecutableResult)
	success, err := handler.(codeassist.AssistantService).CodeExecutable(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newAssistantServiceCodeExecutableArgs() interface{} {
	return codeassist.NewAssistantServiceCodeExecutableArgs()
}

func newAssistantServiceCodeExecutableResult() interface{} {
	return codeassist.NewAssistantServiceCodeExecutableResult()
}

func runCodeHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*codeassist.AssistantServiceRunCodeArgs)
	realResult := result.(*codeassist.AssistantServiceRunCodeResult)
	success, err := handler.(codeassist.AssistantService).RunCode(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newAssistantServiceRunCodeArgs() interface{} {
	return codeassist.NewAssistantServiceRunCodeArgs()
}

func newAssistantServiceRunCodeResult() interface{} {
	return codeassist.NewAssistantServiceRunCodeResult()
}

func runCodeV2Handler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*codeassist.AssistantServiceRunCodeV2Args)
	realResult := result.(*codeassist.AssistantServiceRunCodeV2Result)
	success, err := handler.(codeassist.AssistantService).RunCodeV2(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newAssistantServiceRunCodeV2Args() interface{} {
	return codeassist.NewAssistantServiceRunCodeV2Args()
}

func newAssistantServiceRunCodeV2Result() interface{} {
	return codeassist.NewAssistantServiceRunCodeV2Result()
}

func getArtifactTemplateFileHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*codeassist.AssistantServiceGetArtifactTemplateFileArgs)
	realResult := result.(*codeassist.AssistantServiceGetArtifactTemplateFileResult)
	success, err := handler.(codeassist.AssistantService).GetArtifactTemplateFile(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newAssistantServiceGetArtifactTemplateFileArgs() interface{} {
	return codeassist.NewAssistantServiceGetArtifactTemplateFileArgs()
}

func newAssistantServiceGetArtifactTemplateFileResult() interface{} {
	return codeassist.NewAssistantServiceGetArtifactTemplateFileResult()
}

func getArtifactTemplateDirHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*codeassist.AssistantServiceGetArtifactTemplateDirArgs)
	realResult := result.(*codeassist.AssistantServiceGetArtifactTemplateDirResult)
	success, err := handler.(codeassist.AssistantService).GetArtifactTemplateDir(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newAssistantServiceGetArtifactTemplateDirArgs() interface{} {
	return codeassist.NewAssistantServiceGetArtifactTemplateDirArgs()
}

func newAssistantServiceGetArtifactTemplateDirResult() interface{} {
	return codeassist.NewAssistantServiceGetArtifactTemplateDirResult()
}

func compileCodeArtifactHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*codeassist.AssistantServiceCompileCodeArtifactArgs)
	realResult := result.(*codeassist.AssistantServiceCompileCodeArtifactResult)
	success, err := handler.(codeassist.AssistantService).CompileCodeArtifact(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newAssistantServiceCompileCodeArtifactArgs() interface{} {
	return codeassist.NewAssistantServiceCompileCodeArtifactArgs()
}

func newAssistantServiceCompileCodeArtifactResult() interface{} {
	return codeassist.NewAssistantServiceCompileCodeArtifactResult()
}

func getCompileStatusHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*codeassist.AssistantServiceGetCompileStatusArgs)
	realResult := result.(*codeassist.AssistantServiceGetCompileStatusResult)
	success, err := handler.(codeassist.AssistantService).GetCompileStatus(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newAssistantServiceGetCompileStatusArgs() interface{} {
	return codeassist.NewAssistantServiceGetCompileStatusArgs()
}

func newAssistantServiceGetCompileStatusResult() interface{} {
	return codeassist.NewAssistantServiceGetCompileStatusResult()
}

func getArtifactsCodeURIHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*codeassist.AssistantServiceGetArtifactsCodeURIArgs)
	realResult := result.(*codeassist.AssistantServiceGetArtifactsCodeURIResult)
	success, err := handler.(codeassist.AssistantService).GetArtifactsCodeURI(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newAssistantServiceGetArtifactsCodeURIArgs() interface{} {
	return codeassist.NewAssistantServiceGetArtifactsCodeURIArgs()
}

func newAssistantServiceGetArtifactsCodeURIResult() interface{} {
	return codeassist.NewAssistantServiceGetArtifactsCodeURIResult()
}

func createSandboxHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*codeassist.AssistantServiceCreateSandboxArgs)
	realResult := result.(*codeassist.AssistantServiceCreateSandboxResult)
	success, err := handler.(codeassist.AssistantService).CreateSandbox(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newAssistantServiceCreateSandboxArgs() interface{} {
	return codeassist.NewAssistantServiceCreateSandboxArgs()
}

func newAssistantServiceCreateSandboxResult() interface{} {
	return codeassist.NewAssistantServiceCreateSandboxResult()
}

func releaseSandboxHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*codeassist.AssistantServiceReleaseSandboxArgs)
	realResult := result.(*codeassist.AssistantServiceReleaseSandboxResult)
	success, err := handler.(codeassist.AssistantService).ReleaseSandbox(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newAssistantServiceReleaseSandboxArgs() interface{} {
	return codeassist.NewAssistantServiceReleaseSandboxArgs()
}

func newAssistantServiceReleaseSandboxResult() interface{} {
	return codeassist.NewAssistantServiceReleaseSandboxResult()
}

func executeCodeHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*codeassist.AssistantServiceExecuteCodeArgs)
	realResult := result.(*codeassist.AssistantServiceExecuteCodeResult)
	success, err := handler.(codeassist.AssistantService).ExecuteCode(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newAssistantServiceExecuteCodeArgs() interface{} {
	return codeassist.NewAssistantServiceExecuteCodeArgs()
}

func newAssistantServiceExecuteCodeResult() interface{} {
	return codeassist.NewAssistantServiceExecuteCodeResult()
}

func uploadFileHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*codeassist.AssistantServiceUploadFileArgs)
	realResult := result.(*codeassist.AssistantServiceUploadFileResult)
	success, err := handler.(codeassist.AssistantService).UploadFile(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newAssistantServiceUploadFileArgs() interface{} {
	return codeassist.NewAssistantServiceUploadFileArgs()
}

func newAssistantServiceUploadFileResult() interface{} {
	return codeassist.NewAssistantServiceUploadFileResult()
}

func downloadFileHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*codeassist.AssistantServiceDownloadFileArgs)
	realResult := result.(*codeassist.AssistantServiceDownloadFileResult)
	success, err := handler.(codeassist.AssistantService).DownloadFile(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newAssistantServiceDownloadFileArgs() interface{} {
	return codeassist.NewAssistantServiceDownloadFileArgs()
}

func newAssistantServiceDownloadFileResult() interface{} {
	return codeassist.NewAssistantServiceDownloadFileResult()
}

func imageReviewHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*codeassist.AssistantServiceImageReviewArgs)
	realResult := result.(*codeassist.AssistantServiceImageReviewResult)
	success, err := handler.(codeassist.AssistantService).ImageReview(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newAssistantServiceImageReviewArgs() interface{} {
	return codeassist.NewAssistantServiceImageReviewArgs()
}

func newAssistantServiceImageReviewResult() interface{} {
	return codeassist.NewAssistantServiceImageReviewResult()
}

func searchImagesHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*codeassist.AssistantServiceSearchImagesArgs)
	realResult := result.(*codeassist.AssistantServiceSearchImagesResult)
	success, err := handler.(codeassist.AssistantService).SearchImages(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newAssistantServiceSearchImagesArgs() interface{} {
	return codeassist.NewAssistantServiceSearchImagesArgs()
}

func newAssistantServiceSearchImagesResult() interface{} {
	return codeassist.NewAssistantServiceSearchImagesResult()
}

func interruptCodeAgentTaskHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*codeassist.AssistantServiceInterruptCodeAgentTaskArgs)
	realResult := result.(*codeassist.AssistantServiceInterruptCodeAgentTaskResult)
	success, err := handler.(codeassist.AssistantService).InterruptCodeAgentTask(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newAssistantServiceInterruptCodeAgentTaskArgs() interface{} {
	return codeassist.NewAssistantServiceInterruptCodeAgentTaskArgs()
}

func newAssistantServiceInterruptCodeAgentTaskResult() interface{} {
	return codeassist.NewAssistantServiceInterruptCodeAgentTaskResult()
}

func executeJobCallbackHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*codeassist.AssistantServiceExecuteJobCallbackArgs)
	realResult := result.(*codeassist.AssistantServiceExecuteJobCallbackResult)
	success, err := handler.(codeassist.AssistantService).ExecuteJobCallback(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newAssistantServiceExecuteJobCallbackArgs() interface{} {
	return codeassist.NewAssistantServiceExecuteJobCallbackArgs()
}

func newAssistantServiceExecuteJobCallbackResult() interface{} {
	return codeassist.NewAssistantServiceExecuteJobCallbackResult()
}

type kClient struct {
	c client.Client
}

func newServiceClient(c client.Client) *kClient {
	return &kClient{
		c: c,
	}
}

func (p *kClient) Ping(ctx context.Context, req *codeassist.PingRequest) (r *codeassist.PingResponse, err error) {
	var _args codeassist.AssistantServicePingArgs
	_args.Req = req
	var _result codeassist.AssistantServicePingResult
	if err = p.c.Call(ctx, "Ping", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) Chat(ctx context.Context, req *codeassist.ChatRequest) (AssistantService_ChatClient, error) {
	streamClient, ok := p.c.(client.Streaming)
	if !ok {
		return nil, fmt.Errorf("client not support streaming")
	}
	res := new(streaming.Result)
	err := streamClient.Stream(ctx, "Chat", nil, res)
	if err != nil {
		return nil, err
	}
	stream := &assistantServiceChatClient{res.Stream}

	if err := stream.Stream.SendMsg(req); err != nil {
		return nil, err
	}
	if err := stream.Stream.Close(); err != nil {
		return nil, err
	}
	return stream, nil
}

func (p *kClient) PromptsRender(ctx context.Context, req *codeassist.PromptsRenderRequest) (r *codeassist.PromptsRenderResponse, err error) {
	var _args codeassist.AssistantServicePromptsRenderArgs
	_args.Req = req
	var _result codeassist.AssistantServicePromptsRenderResult
	if err = p.c.Call(ctx, "PromptsRender", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) E2EPromptsRender(ctx context.Context, req *codeassist.E2EPromptsRenderRequest) (r *codeassist.E2EPromptsRenderResponse, err error) {
	var _args codeassist.AssistantServiceE2EPromptsRenderArgs
	_args.Req = req
	var _result codeassist.AssistantServiceE2EPromptsRenderResult
	if err = p.c.Call(ctx, "E2EPromptsRender", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) HomePage(ctx context.Context, req *codeassist.HomePageRequest) (r *codeassist.HomePageResponse, err error) {
	var _args codeassist.AssistantServiceHomePageArgs
	_args.Req = req
	var _result codeassist.AssistantServiceHomePageResult
	if err = p.c.Call(ctx, "HomePage", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) SniffRepositoryLink(ctx context.Context, req *codeassist.SniffRepositoryLinkRequest) (r *codeassist.SniffRepositoryLinkResponse, err error) {
	var _args codeassist.AssistantServiceSniffRepositoryLinkArgs
	_args.Req = req
	var _result codeassist.AssistantServiceSniffRepositoryLinkResult
	if err = p.c.Call(ctx, "SniffRepositoryLink", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) CreateContext(ctx context.Context, req *codeassist.CreateContextRequest) (r *codeassist.CreateContextResponse, err error) {
	var _args codeassist.AssistantServiceCreateContextArgs
	_args.Req = req
	var _result codeassist.AssistantServiceCreateContextResult
	if err = p.c.Call(ctx, "CreateContext", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) MGetContextStatus(ctx context.Context, req *codeassist.MGetContextStatusRequest) (r *codeassist.MGetContextStatusResponse, err error) {
	var _args codeassist.AssistantServiceMGetContextStatusArgs
	_args.Req = req
	var _result codeassist.AssistantServiceMGetContextStatusResult
	if err = p.c.Call(ctx, "MGetContextStatus", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) GetContextDirectoryNodes(ctx context.Context, req *codeassist.GetContextDirectoryNodesRequest) (r *codeassist.GetContextDirectoryNodesResponse, err error) {
	var _args codeassist.AssistantServiceGetContextDirectoryNodesArgs
	_args.Req = req
	var _result codeassist.AssistantServiceGetContextDirectoryNodesResult
	if err = p.c.Call(ctx, "GetContextDirectoryNodes", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) GetRepoFileContent(ctx context.Context, req *codeassist.GetRepoFileContentRequest) (r *codeassist.GetRepoFileContentResponse, err error) {
	var _args codeassist.AssistantServiceGetRepoFileContentArgs
	_args.Req = req
	var _result codeassist.AssistantServiceGetRepoFileContentResult
	if err = p.c.Call(ctx, "GetRepoFileContent", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) GetContextInfo(ctx context.Context, req *codeassist.GetContextInfoRequest) (r *codeassist.GetContextInfoResponse, err error) {
	var _args codeassist.AssistantServiceGetContextInfoArgs
	_args.Req = req
	var _result codeassist.AssistantServiceGetContextInfoResult
	if err = p.c.Call(ctx, "GetContextInfo", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) GetContextDownloadURL(ctx context.Context, req *codeassist.GetContextDownloadURLRequest) (r *codeassist.GetContextDownloadURLResponse, err error) {
	var _args codeassist.AssistantServiceGetContextDownloadURLArgs
	_args.Req = req
	var _result codeassist.AssistantServiceGetContextDownloadURLResult
	if err = p.c.Call(ctx, "GetContextDownloadURL", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) GetCodeResourceDownloadURL(ctx context.Context, req *codeassist.GetCodeResourceDownloadURLRequest) (r *codeassist.GetCodeResourceDownloadURLResponse, err error) {
	var _args codeassist.AssistantServiceGetCodeResourceDownloadURLArgs
	_args.Req = req
	var _result codeassist.AssistantServiceGetCodeResourceDownloadURLResult
	if err = p.c.Call(ctx, "GetCodeResourceDownloadURL", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) DesensitizeContext(ctx context.Context, req *codeassist.DesensitizeContextRequest) (r *codeassist.DesensitizeContextResponse, err error) {
	var _args codeassist.AssistantServiceDesensitizeContextArgs
	_args.Req = req
	var _result codeassist.AssistantServiceDesensitizeContextResult
	if err = p.c.Call(ctx, "DesensitizeContext", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) BatchCheckFilePath(ctx context.Context, req *codeassist.BatchCheckFilePathRequest) (r *codeassist.BatchCheckFilePathResponse, err error) {
	var _args codeassist.AssistantServiceBatchCheckFilePathArgs
	_args.Req = req
	var _result codeassist.AssistantServiceBatchCheckFilePathResult
	if err = p.c.Call(ctx, "BatchCheckFilePath", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) CodeExecutable(ctx context.Context, req *codeassist.CodeExecutableRequest) (r *codeassist.CodeExecutableResponse, err error) {
	var _args codeassist.AssistantServiceCodeExecutableArgs
	_args.Req = req
	var _result codeassist.AssistantServiceCodeExecutableResult
	if err = p.c.Call(ctx, "CodeExecutable", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) RunCode(ctx context.Context, req *codeassist.RunCodeRequest) (r *codeassist.RunCodeResponse, err error) {
	var _args codeassist.AssistantServiceRunCodeArgs
	_args.Req = req
	var _result codeassist.AssistantServiceRunCodeResult
	if err = p.c.Call(ctx, "RunCode", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) RunCodeV2(ctx context.Context, req *codeassist.RunCodeRequestV2) (r *codeassist.RunCodeResponseV2, err error) {
	var _args codeassist.AssistantServiceRunCodeV2Args
	_args.Req = req
	var _result codeassist.AssistantServiceRunCodeV2Result
	if err = p.c.Call(ctx, "RunCodeV2", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) GetArtifactTemplateFile(ctx context.Context, req *codeassist.GetArtifactTemplateFileRequest) (r *codeassist.GetArtifactTemplateFileResponse, err error) {
	var _args codeassist.AssistantServiceGetArtifactTemplateFileArgs
	_args.Req = req
	var _result codeassist.AssistantServiceGetArtifactTemplateFileResult
	if err = p.c.Call(ctx, "GetArtifactTemplateFile", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) GetArtifactTemplateDir(ctx context.Context, req *codeassist.GetArtifactTemplateDirRequest) (r *codeassist.GetArtifactTemplateDirResponse, err error) {
	var _args codeassist.AssistantServiceGetArtifactTemplateDirArgs
	_args.Req = req
	var _result codeassist.AssistantServiceGetArtifactTemplateDirResult
	if err = p.c.Call(ctx, "GetArtifactTemplateDir", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) CompileCodeArtifact(ctx context.Context, req *codeassist.CompileCodeArtifactRequest) (r *codeassist.CompileCodeArtifactResponse, err error) {
	var _args codeassist.AssistantServiceCompileCodeArtifactArgs
	_args.Req = req
	var _result codeassist.AssistantServiceCompileCodeArtifactResult
	if err = p.c.Call(ctx, "CompileCodeArtifact", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) GetCompileStatus(ctx context.Context, req *codeassist.GetCompileStatusRequest) (r *codeassist.GetCompileStatusResponse, err error) {
	var _args codeassist.AssistantServiceGetCompileStatusArgs
	_args.Req = req
	var _result codeassist.AssistantServiceGetCompileStatusResult
	if err = p.c.Call(ctx, "GetCompileStatus", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) GetArtifactsCodeURI(ctx context.Context, req *codeassist.GetArtifactsCodeURIRequest) (r *codeassist.GetArtifactsCodeURIResponse, err error) {
	var _args codeassist.AssistantServiceGetArtifactsCodeURIArgs
	_args.Req = req
	var _result codeassist.AssistantServiceGetArtifactsCodeURIResult
	if err = p.c.Call(ctx, "GetArtifactsCodeURI", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) CreateSandbox(ctx context.Context, req *codeassist.CreateSandboxRequest) (r *codeassist.CreateSandboxResponse, err error) {
	var _args codeassist.AssistantServiceCreateSandboxArgs
	_args.Req = req
	var _result codeassist.AssistantServiceCreateSandboxResult
	if err = p.c.Call(ctx, "CreateSandbox", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) ReleaseSandbox(ctx context.Context, req *codeassist.ReleaseSandboxRequest) (r *codeassist.ReleaseSandboxResponse, err error) {
	var _args codeassist.AssistantServiceReleaseSandboxArgs
	_args.Req = req
	var _result codeassist.AssistantServiceReleaseSandboxResult
	if err = p.c.Call(ctx, "ReleaseSandbox", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) ExecuteCode(ctx context.Context, req *codeassist.ExecuteCodeRequest) (r *codeassist.ExecuteCodeResponse, err error) {
	var _args codeassist.AssistantServiceExecuteCodeArgs
	_args.Req = req
	var _result codeassist.AssistantServiceExecuteCodeResult
	if err = p.c.Call(ctx, "ExecuteCode", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) UploadFile(ctx context.Context, req *codeassist.UploadFileRequest) (r *codeassist.UploadFileResponse, err error) {
	var _args codeassist.AssistantServiceUploadFileArgs
	_args.Req = req
	var _result codeassist.AssistantServiceUploadFileResult
	if err = p.c.Call(ctx, "UploadFile", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) DownloadFile(ctx context.Context, req *codeassist.DownloadFileRequest) (r *codeassist.DownloadFileResponse, err error) {
	var _args codeassist.AssistantServiceDownloadFileArgs
	_args.Req = req
	var _result codeassist.AssistantServiceDownloadFileResult
	if err = p.c.Call(ctx, "DownloadFile", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) ImageReview(ctx context.Context, req *codeassist.ImageReviewRequest) (r *codeassist.ImageReviewResponse, err error) {
	var _args codeassist.AssistantServiceImageReviewArgs
	_args.Req = req
	var _result codeassist.AssistantServiceImageReviewResult
	if err = p.c.Call(ctx, "ImageReview", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) SearchImages(ctx context.Context, req *codeassist.SearchImagesRequest) (r *codeassist.SearchImagesResponse, err error) {
	var _args codeassist.AssistantServiceSearchImagesArgs
	_args.Req = req
	var _result codeassist.AssistantServiceSearchImagesResult
	if err = p.c.Call(ctx, "SearchImages", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) InterruptCodeAgentTask(ctx context.Context, req *codeassist.InterruptCodeAgentTaskRequest) (r *codeassist.InterruptCodeAgentTaskResponse, err error) {
	var _args codeassist.AssistantServiceInterruptCodeAgentTaskArgs
	_args.Req = req
	var _result codeassist.AssistantServiceInterruptCodeAgentTaskResult
	if err = p.c.Call(ctx, "InterruptCodeAgentTask", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) ExecuteJobCallback(ctx context.Context, req *codeassist.ExecuteJobCallbackReq) (r *codeassist.ExecuteJobCallbackResp, err error) {
	var _args codeassist.AssistantServiceExecuteJobCallbackArgs
	_args.Req = req
	var _result codeassist.AssistantServiceExecuteJobCallbackResult
	if err = p.c.Call(ctx, "ExecuteJobCallback", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
