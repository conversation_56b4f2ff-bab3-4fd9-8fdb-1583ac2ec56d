// Code generated by thriftgo (0.4.2). DO NOT EDIT.

package codeassist

import (
	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/base"
	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/common"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type ImageReviewRequest struct {
	MessageID   int64      `thrift:"MessageID,1,required" frugal:"1,required,i64" json:"MessageID"`
	UserID      int64      `thrift:"UserID,2,required" frugal:"2,required,i64" json:"UserID"`
	ImageURL    []string   `thrift:"ImageURL,3,optional" frugal:"3,optional,list<string>" json:"ImageURL,omitempty"`
	ImageBase64 []string   `thrift:"ImageBase64,4,optional" frugal:"4,optional,list<string>" json:"ImageBase64,omitempty"`
	Base        *base.Base `thrift:"Base,255,optional" frugal:"255,optional,base.Base" json:"Base,omitempty"`
}

func NewImageReviewRequest() *ImageReviewRequest {
	return &ImageReviewRequest{}
}

func (p *ImageReviewRequest) InitDefault() {
}

func (p *ImageReviewRequest) GetMessageID() (v int64) {
	return p.MessageID
}

func (p *ImageReviewRequest) GetUserID() (v int64) {
	return p.UserID
}

var ImageReviewRequest_ImageURL_DEFAULT []string

func (p *ImageReviewRequest) GetImageURL() (v []string) {
	if !p.IsSetImageURL() {
		return ImageReviewRequest_ImageURL_DEFAULT
	}
	return p.ImageURL
}

var ImageReviewRequest_ImageBase64_DEFAULT []string

func (p *ImageReviewRequest) GetImageBase64() (v []string) {
	if !p.IsSetImageBase64() {
		return ImageReviewRequest_ImageBase64_DEFAULT
	}
	return p.ImageBase64
}

var ImageReviewRequest_Base_DEFAULT *base.Base

func (p *ImageReviewRequest) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return ImageReviewRequest_Base_DEFAULT
	}
	return p.Base
}
func (p *ImageReviewRequest) SetMessageID(val int64) {
	p.MessageID = val
}
func (p *ImageReviewRequest) SetUserID(val int64) {
	p.UserID = val
}
func (p *ImageReviewRequest) SetImageURL(val []string) {
	p.ImageURL = val
}
func (p *ImageReviewRequest) SetImageBase64(val []string) {
	p.ImageBase64 = val
}
func (p *ImageReviewRequest) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_ImageReviewRequest = map[int16]string{
	1:   "MessageID",
	2:   "UserID",
	3:   "ImageURL",
	4:   "ImageBase64",
	255: "Base",
}

func (p *ImageReviewRequest) IsSetImageURL() bool {
	return p.ImageURL != nil
}

func (p *ImageReviewRequest) IsSetImageBase64() bool {
	return p.ImageBase64 != nil
}

func (p *ImageReviewRequest) IsSetBase() bool {
	return p.Base != nil
}

func (p *ImageReviewRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetMessageID bool = false
	var issetUserID bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetMessageID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetUserID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetMessageID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetUserID {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ImageReviewRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ImageReviewRequest[fieldId]))
}

func (p *ImageReviewRequest) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.MessageID = _field
	return nil
}
func (p *ImageReviewRequest) ReadField2(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UserID = _field
	return nil
}
func (p *ImageReviewRequest) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ImageURL = _field
	return nil
}
func (p *ImageReviewRequest) ReadField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ImageBase64 = _field
	return nil
}
func (p *ImageReviewRequest) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *ImageReviewRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ImageReviewRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ImageReviewRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("MessageID", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.MessageID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ImageReviewRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("UserID", thrift.I64, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.UserID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ImageReviewRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetImageURL() {
		if err = oprot.WriteFieldBegin("ImageURL", thrift.LIST, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.ImageURL)); err != nil {
			return err
		}
		for _, v := range p.ImageURL {
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *ImageReviewRequest) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetImageBase64() {
		if err = oprot.WriteFieldBegin("ImageBase64", thrift.LIST, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.ImageBase64)); err != nil {
			return err
		}
		for _, v := range p.ImageBase64 {
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *ImageReviewRequest) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *ImageReviewRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ImageReviewRequest(%+v)", *p)

}

func (p *ImageReviewRequest) DeepEqual(ano *ImageReviewRequest) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.MessageID) {
		return false
	}
	if !p.Field2DeepEqual(ano.UserID) {
		return false
	}
	if !p.Field3DeepEqual(ano.ImageURL) {
		return false
	}
	if !p.Field4DeepEqual(ano.ImageBase64) {
		return false
	}
	if !p.Field255DeepEqual(ano.Base) {
		return false
	}
	return true
}

func (p *ImageReviewRequest) Field1DeepEqual(src int64) bool {

	if p.MessageID != src {
		return false
	}
	return true
}
func (p *ImageReviewRequest) Field2DeepEqual(src int64) bool {

	if p.UserID != src {
		return false
	}
	return true
}
func (p *ImageReviewRequest) Field3DeepEqual(src []string) bool {

	if len(p.ImageURL) != len(src) {
		return false
	}
	for i, v := range p.ImageURL {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *ImageReviewRequest) Field4DeepEqual(src []string) bool {

	if len(p.ImageBase64) != len(src) {
		return false
	}
	for i, v := range p.ImageBase64 {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *ImageReviewRequest) Field255DeepEqual(src *base.Base) bool {

	if !p.Base.DeepEqual(src) {
		return false
	}
	return true
}

type ImageReviewCheckResult_ struct {
	Code *common.ErrorCode `thrift:"Code,1,optional" frugal:"1,optional,ErrorCode" json:"Code,omitempty"`
}

func NewImageReviewCheckResult_() *ImageReviewCheckResult_ {
	return &ImageReviewCheckResult_{}
}

func (p *ImageReviewCheckResult_) InitDefault() {
}

var ImageReviewCheckResult__Code_DEFAULT common.ErrorCode

func (p *ImageReviewCheckResult_) GetCode() (v common.ErrorCode) {
	if !p.IsSetCode() {
		return ImageReviewCheckResult__Code_DEFAULT
	}
	return *p.Code
}
func (p *ImageReviewCheckResult_) SetCode(val *common.ErrorCode) {
	p.Code = val
}

var fieldIDToName_ImageReviewCheckResult_ = map[int16]string{
	1: "Code",
}

func (p *ImageReviewCheckResult_) IsSetCode() bool {
	return p.Code != nil
}

func (p *ImageReviewCheckResult_) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ImageReviewCheckResult_[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ImageReviewCheckResult_) ReadField1(iprot thrift.TProtocol) error {

	var _field *common.ErrorCode
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := common.ErrorCode(v)
		_field = &tmp
	}
	p.Code = _field
	return nil
}

func (p *ImageReviewCheckResult_) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ImageReviewCheckResult"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ImageReviewCheckResult_) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetCode() {
		if err = oprot.WriteFieldBegin("Code", thrift.I32, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.Code)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ImageReviewCheckResult_) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ImageReviewCheckResult_(%+v)", *p)

}

func (p *ImageReviewCheckResult_) DeepEqual(ano *ImageReviewCheckResult_) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Code) {
		return false
	}
	return true
}

func (p *ImageReviewCheckResult_) Field1DeepEqual(src *common.ErrorCode) bool {

	if p.Code == src {
		return true
	} else if p.Code == nil || src == nil {
		return false
	}
	if *p.Code != *src {
		return false
	}
	return true
}

type ImageReviewResponse struct {
	UrlResult_    []*ImageReviewCheckResult_ `thrift:"UrlResult,1" frugal:"1,default,list<ImageReviewCheckResult_>" json:"UrlResult"`
	Base64Result_ []*ImageReviewCheckResult_ `thrift:"Base64Result,2" frugal:"2,default,list<ImageReviewCheckResult_>" json:"Base64Result"`
	BaseResp      *base.BaseResp             `thrift:"BaseResp,255,optional" frugal:"255,optional,base.BaseResp" json:"BaseResp,omitempty"`
}

func NewImageReviewResponse() *ImageReviewResponse {
	return &ImageReviewResponse{}
}

func (p *ImageReviewResponse) InitDefault() {
}

func (p *ImageReviewResponse) GetUrlResult_() (v []*ImageReviewCheckResult_) {
	return p.UrlResult_
}

func (p *ImageReviewResponse) GetBase64Result_() (v []*ImageReviewCheckResult_) {
	return p.Base64Result_
}

var ImageReviewResponse_BaseResp_DEFAULT *base.BaseResp

func (p *ImageReviewResponse) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return ImageReviewResponse_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *ImageReviewResponse) SetUrlResult_(val []*ImageReviewCheckResult_) {
	p.UrlResult_ = val
}
func (p *ImageReviewResponse) SetBase64Result_(val []*ImageReviewCheckResult_) {
	p.Base64Result_ = val
}
func (p *ImageReviewResponse) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_ImageReviewResponse = map[int16]string{
	1:   "UrlResult",
	2:   "Base64Result",
	255: "BaseResp",
}

func (p *ImageReviewResponse) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *ImageReviewResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ImageReviewResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ImageReviewResponse) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*ImageReviewCheckResult_, 0, size)
	values := make([]ImageReviewCheckResult_, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.UrlResult_ = _field
	return nil
}
func (p *ImageReviewResponse) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*ImageReviewCheckResult_, 0, size)
	values := make([]ImageReviewCheckResult_, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Base64Result_ = _field
	return nil
}
func (p *ImageReviewResponse) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *ImageReviewResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ImageReviewResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ImageReviewResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("UrlResult", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.UrlResult_)); err != nil {
		return err
	}
	for _, v := range p.UrlResult_ {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ImageReviewResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Base64Result", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Base64Result_)); err != nil {
		return err
	}
	for _, v := range p.Base64Result_ {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ImageReviewResponse) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBaseResp() {
		if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.BaseResp.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *ImageReviewResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ImageReviewResponse(%+v)", *p)

}

func (p *ImageReviewResponse) DeepEqual(ano *ImageReviewResponse) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.UrlResult_) {
		return false
	}
	if !p.Field2DeepEqual(ano.Base64Result_) {
		return false
	}
	if !p.Field255DeepEqual(ano.BaseResp) {
		return false
	}
	return true
}

func (p *ImageReviewResponse) Field1DeepEqual(src []*ImageReviewCheckResult_) bool {

	if len(p.UrlResult_) != len(src) {
		return false
	}
	for i, v := range p.UrlResult_ {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *ImageReviewResponse) Field2DeepEqual(src []*ImageReviewCheckResult_) bool {

	if len(p.Base64Result_) != len(src) {
		return false
	}
	for i, v := range p.Base64Result_ {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *ImageReviewResponse) Field255DeepEqual(src *base.BaseResp) bool {

	if !p.BaseResp.DeepEqual(src) {
		return false
	}
	return true
}
