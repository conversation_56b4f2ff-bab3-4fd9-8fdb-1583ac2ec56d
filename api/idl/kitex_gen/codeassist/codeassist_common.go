// Code generated by thriftgo (0.4.2). DO NOT EDIT.

package codeassist

import (
	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/base"
	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/common"
	"database/sql"
	"database/sql/driver"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type ResourceType int64

const (
	ResourceType_Repository ResourceType = 1
	ResourceType_File       ResourceType = 2
	ResourceType_Directory  ResourceType = 3
)

func (p ResourceType) String() string {
	switch p {
	case ResourceType_Repository:
		return "Repository"
	case ResourceType_File:
		return "File"
	case ResourceType_Directory:
		return "Directory"
	}
	return "<UNSET>"
}

func ResourceTypeFromString(s string) (ResourceType, error) {
	switch s {
	case "Repository":
		return ResourceType_Repository, nil
	case "File":
		return ResourceType_File, nil
	case "Directory":
		return ResourceType_Directory, nil
	}
	return ResourceType(0), fmt.Errorf("not a valid ResourceType string")
}

func ResourceTypePtr(v ResourceType) *ResourceType { return &v }
func (p *ResourceType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = ResourceType(result.Int64)
	return
}

func (p *ResourceType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type PingRequest struct {
	HTTPRequest *common.HTTPRequest `thrift:"HTTPRequest,201,optional" frugal:"201,optional,common.HTTPRequest" json:"HTTPRequest,omitempty"`
	Base        *base.Base          `thrift:"Base,255,optional" frugal:"255,optional,base.Base" json:"Base,omitempty"`
}

func NewPingRequest() *PingRequest {
	return &PingRequest{}
}

func (p *PingRequest) InitDefault() {
}

var PingRequest_HTTPRequest_DEFAULT *common.HTTPRequest

func (p *PingRequest) GetHTTPRequest() (v *common.HTTPRequest) {
	if !p.IsSetHTTPRequest() {
		return PingRequest_HTTPRequest_DEFAULT
	}
	return p.HTTPRequest
}

var PingRequest_Base_DEFAULT *base.Base

func (p *PingRequest) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return PingRequest_Base_DEFAULT
	}
	return p.Base
}
func (p *PingRequest) SetHTTPRequest(val *common.HTTPRequest) {
	p.HTTPRequest = val
}
func (p *PingRequest) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_PingRequest = map[int16]string{
	201: "HTTPRequest",
	255: "Base",
}

func (p *PingRequest) IsSetHTTPRequest() bool {
	return p.HTTPRequest != nil
}

func (p *PingRequest) IsSetBase() bool {
	return p.Base != nil
}

func (p *PingRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 201:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField201(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PingRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *PingRequest) ReadField201(iprot thrift.TProtocol) error {
	_field := common.NewHTTPRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.HTTPRequest = _field
	return nil
}
func (p *PingRequest) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *PingRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("PingRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField201(oprot); err != nil {
			fieldId = 201
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *PingRequest) writeField201(oprot thrift.TProtocol) (err error) {
	if p.IsSetHTTPRequest() {
		if err = oprot.WriteFieldBegin("HTTPRequest", thrift.STRUCT, 201); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.HTTPRequest.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 201 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 201 end error: ", p), err)
}
func (p *PingRequest) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *PingRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PingRequest(%+v)", *p)

}

func (p *PingRequest) DeepEqual(ano *PingRequest) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field201DeepEqual(ano.HTTPRequest) {
		return false
	}
	if !p.Field255DeepEqual(ano.Base) {
		return false
	}
	return true
}

func (p *PingRequest) Field201DeepEqual(src *common.HTTPRequest) bool {

	if !p.HTTPRequest.DeepEqual(src) {
		return false
	}
	return true
}
func (p *PingRequest) Field255DeepEqual(src *base.Base) bool {

	if !p.Base.DeepEqual(src) {
		return false
	}
	return true
}

type PingResponse struct {
	Code         *common.ErrorCode    `thrift:"Code,201,optional" frugal:"201,optional,ErrorCode" json:"code"`
	Message      *string              `thrift:"Message,202,optional" frugal:"202,optional,string" json:"message"`
	HTTPCode     *int32               `thrift:"HTTPCode,203,optional" frugal:"203,optional,i32" json:"HTTPCode,omitempty"`
	HTTPResponse *common.HTTPResponse `thrift:"HTTPResponse,204,optional" frugal:"204,optional,common.HTTPResponse" json:"HTTPResponse,omitempty"`
	BaseResp     *base.BaseResp       `thrift:"BaseResp,255,optional" frugal:"255,optional,base.BaseResp" json:"BaseResp,omitempty"`
}

func NewPingResponse() *PingResponse {
	return &PingResponse{}
}

func (p *PingResponse) InitDefault() {
}

var PingResponse_Code_DEFAULT common.ErrorCode

func (p *PingResponse) GetCode() (v common.ErrorCode) {
	if !p.IsSetCode() {
		return PingResponse_Code_DEFAULT
	}
	return *p.Code
}

var PingResponse_Message_DEFAULT string

func (p *PingResponse) GetMessage() (v string) {
	if !p.IsSetMessage() {
		return PingResponse_Message_DEFAULT
	}
	return *p.Message
}

var PingResponse_HTTPCode_DEFAULT int32

func (p *PingResponse) GetHTTPCode() (v int32) {
	if !p.IsSetHTTPCode() {
		return PingResponse_HTTPCode_DEFAULT
	}
	return *p.HTTPCode
}

var PingResponse_HTTPResponse_DEFAULT *common.HTTPResponse

func (p *PingResponse) GetHTTPResponse() (v *common.HTTPResponse) {
	if !p.IsSetHTTPResponse() {
		return PingResponse_HTTPResponse_DEFAULT
	}
	return p.HTTPResponse
}

var PingResponse_BaseResp_DEFAULT *base.BaseResp

func (p *PingResponse) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return PingResponse_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *PingResponse) SetCode(val *common.ErrorCode) {
	p.Code = val
}
func (p *PingResponse) SetMessage(val *string) {
	p.Message = val
}
func (p *PingResponse) SetHTTPCode(val *int32) {
	p.HTTPCode = val
}
func (p *PingResponse) SetHTTPResponse(val *common.HTTPResponse) {
	p.HTTPResponse = val
}
func (p *PingResponse) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_PingResponse = map[int16]string{
	201: "Code",
	202: "Message",
	203: "HTTPCode",
	204: "HTTPResponse",
	255: "BaseResp",
}

func (p *PingResponse) IsSetCode() bool {
	return p.Code != nil
}

func (p *PingResponse) IsSetMessage() bool {
	return p.Message != nil
}

func (p *PingResponse) IsSetHTTPCode() bool {
	return p.HTTPCode != nil
}

func (p *PingResponse) IsSetHTTPResponse() bool {
	return p.HTTPResponse != nil
}

func (p *PingResponse) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *PingResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 201:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField201(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 202:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField202(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 203:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField203(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 204:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField204(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PingResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *PingResponse) ReadField201(iprot thrift.TProtocol) error {

	var _field *common.ErrorCode
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := common.ErrorCode(v)
		_field = &tmp
	}
	p.Code = _field
	return nil
}
func (p *PingResponse) ReadField202(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Message = _field
	return nil
}
func (p *PingResponse) ReadField203(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.HTTPCode = _field
	return nil
}
func (p *PingResponse) ReadField204(iprot thrift.TProtocol) error {
	_field := common.NewHTTPResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.HTTPResponse = _field
	return nil
}
func (p *PingResponse) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *PingResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("PingResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField201(oprot); err != nil {
			fieldId = 201
			goto WriteFieldError
		}
		if err = p.writeField202(oprot); err != nil {
			fieldId = 202
			goto WriteFieldError
		}
		if err = p.writeField203(oprot); err != nil {
			fieldId = 203
			goto WriteFieldError
		}
		if err = p.writeField204(oprot); err != nil {
			fieldId = 204
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *PingResponse) writeField201(oprot thrift.TProtocol) (err error) {
	if p.IsSetCode() {
		if err = oprot.WriteFieldBegin("Code", thrift.I32, 201); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.Code)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 201 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 201 end error: ", p), err)
}
func (p *PingResponse) writeField202(oprot thrift.TProtocol) (err error) {
	if p.IsSetMessage() {
		if err = oprot.WriteFieldBegin("Message", thrift.STRING, 202); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Message); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 202 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 202 end error: ", p), err)
}
func (p *PingResponse) writeField203(oprot thrift.TProtocol) (err error) {
	if p.IsSetHTTPCode() {
		if err = oprot.WriteFieldBegin("HTTPCode", thrift.I32, 203); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.HTTPCode); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 203 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 203 end error: ", p), err)
}
func (p *PingResponse) writeField204(oprot thrift.TProtocol) (err error) {
	if p.IsSetHTTPResponse() {
		if err = oprot.WriteFieldBegin("HTTPResponse", thrift.STRUCT, 204); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.HTTPResponse.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 204 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 204 end error: ", p), err)
}
func (p *PingResponse) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBaseResp() {
		if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.BaseResp.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *PingResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PingResponse(%+v)", *p)

}

func (p *PingResponse) DeepEqual(ano *PingResponse) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field201DeepEqual(ano.Code) {
		return false
	}
	if !p.Field202DeepEqual(ano.Message) {
		return false
	}
	if !p.Field203DeepEqual(ano.HTTPCode) {
		return false
	}
	if !p.Field204DeepEqual(ano.HTTPResponse) {
		return false
	}
	if !p.Field255DeepEqual(ano.BaseResp) {
		return false
	}
	return true
}

func (p *PingResponse) Field201DeepEqual(src *common.ErrorCode) bool {

	if p.Code == src {
		return true
	} else if p.Code == nil || src == nil {
		return false
	}
	if *p.Code != *src {
		return false
	}
	return true
}
func (p *PingResponse) Field202DeepEqual(src *string) bool {

	if p.Message == src {
		return true
	} else if p.Message == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Message, *src) != 0 {
		return false
	}
	return true
}
func (p *PingResponse) Field203DeepEqual(src *int32) bool {

	if p.HTTPCode == src {
		return true
	} else if p.HTTPCode == nil || src == nil {
		return false
	}
	if *p.HTTPCode != *src {
		return false
	}
	return true
}
func (p *PingResponse) Field204DeepEqual(src *common.HTTPResponse) bool {

	if !p.HTTPResponse.DeepEqual(src) {
		return false
	}
	return true
}
func (p *PingResponse) Field255DeepEqual(src *base.BaseResp) bool {

	if !p.BaseResp.DeepEqual(src) {
		return false
	}
	return true
}

type ContextIdentifier struct {
	Type        ResourceType `thrift:"Type,1,required" frugal:"1,required,ResourceType" json:"Type"`
	ResourceKey string       `thrift:"ResourceKey,2,required" frugal:"2,required,string" json:"ResourceKey"`
	Name        *string      `thrift:"Name,3,optional" frugal:"3,optional,string" json:"Name,omitempty"`
}

func NewContextIdentifier() *ContextIdentifier {
	return &ContextIdentifier{}
}

func (p *ContextIdentifier) InitDefault() {
}

func (p *ContextIdentifier) GetType() (v ResourceType) {
	return p.Type
}

func (p *ContextIdentifier) GetResourceKey() (v string) {
	return p.ResourceKey
}

var ContextIdentifier_Name_DEFAULT string

func (p *ContextIdentifier) GetName() (v string) {
	if !p.IsSetName() {
		return ContextIdentifier_Name_DEFAULT
	}
	return *p.Name
}
func (p *ContextIdentifier) SetType(val ResourceType) {
	p.Type = val
}
func (p *ContextIdentifier) SetResourceKey(val string) {
	p.ResourceKey = val
}
func (p *ContextIdentifier) SetName(val *string) {
	p.Name = val
}

var fieldIDToName_ContextIdentifier = map[int16]string{
	1: "Type",
	2: "ResourceKey",
	3: "Name",
}

func (p *ContextIdentifier) IsSetName() bool {
	return p.Name != nil
}

func (p *ContextIdentifier) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetType bool = false
	var issetResourceKey bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetResourceKey = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetType {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetResourceKey {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ContextIdentifier[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ContextIdentifier[fieldId]))
}

func (p *ContextIdentifier) ReadField1(iprot thrift.TProtocol) error {

	var _field ResourceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = ResourceType(v)
	}
	p.Type = _field
	return nil
}
func (p *ContextIdentifier) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ResourceKey = _field
	return nil
}
func (p *ContextIdentifier) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Name = _field
	return nil
}

func (p *ContextIdentifier) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ContextIdentifier"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ContextIdentifier) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Type", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.Type)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ContextIdentifier) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ResourceKey", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ResourceKey); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ContextIdentifier) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetName() {
		if err = oprot.WriteFieldBegin("Name", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Name); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ContextIdentifier) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ContextIdentifier(%+v)", *p)

}

func (p *ContextIdentifier) DeepEqual(ano *ContextIdentifier) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Type) {
		return false
	}
	if !p.Field2DeepEqual(ano.ResourceKey) {
		return false
	}
	if !p.Field3DeepEqual(ano.Name) {
		return false
	}
	return true
}

func (p *ContextIdentifier) Field1DeepEqual(src ResourceType) bool {

	if p.Type != src {
		return false
	}
	return true
}
func (p *ContextIdentifier) Field2DeepEqual(src string) bool {

	if strings.Compare(p.ResourceKey, src) != 0 {
		return false
	}
	return true
}
func (p *ContextIdentifier) Field3DeepEqual(src *string) bool {

	if p.Name == src {
		return true
	} else if p.Name == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Name, *src) != 0 {
		return false
	}
	return true
}
