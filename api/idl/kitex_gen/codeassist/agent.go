// Code generated by thriftgo (0.4.2). DO NOT EDIT.

package codeassist

import (
	"bytes"
	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/base"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type InterruptCodeAgentTaskRequest struct {
	UserID         int64      `thrift:"UserID,1" frugal:"1,default,i64" json:"UserID"`
	MessageID      int64      `thrift:"MessageID,2" frugal:"2,default,i64" json:"MessageID"`
	ConversationID int64      `thrift:"ConversationID,3" frugal:"3,default,i64" json:"ConversationID"`
	Base           *base.Base `thrift:"Base,255,optional" frugal:"255,optional,base.Base" json:"Base,omitempty"`
}

func NewInterruptCodeAgentTaskRequest() *InterruptCodeAgentTaskRequest {
	return &InterruptCodeAgentTaskRequest{}
}

func (p *InterruptCodeAgentTaskRequest) InitDefault() {
}

func (p *InterruptCodeAgentTaskRequest) GetUserID() (v int64) {
	return p.UserID
}

func (p *InterruptCodeAgentTaskRequest) GetMessageID() (v int64) {
	return p.MessageID
}

func (p *InterruptCodeAgentTaskRequest) GetConversationID() (v int64) {
	return p.ConversationID
}

var InterruptCodeAgentTaskRequest_Base_DEFAULT *base.Base

func (p *InterruptCodeAgentTaskRequest) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return InterruptCodeAgentTaskRequest_Base_DEFAULT
	}
	return p.Base
}
func (p *InterruptCodeAgentTaskRequest) SetUserID(val int64) {
	p.UserID = val
}
func (p *InterruptCodeAgentTaskRequest) SetMessageID(val int64) {
	p.MessageID = val
}
func (p *InterruptCodeAgentTaskRequest) SetConversationID(val int64) {
	p.ConversationID = val
}
func (p *InterruptCodeAgentTaskRequest) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_InterruptCodeAgentTaskRequest = map[int16]string{
	1:   "UserID",
	2:   "MessageID",
	3:   "ConversationID",
	255: "Base",
}

func (p *InterruptCodeAgentTaskRequest) IsSetBase() bool {
	return p.Base != nil
}

func (p *InterruptCodeAgentTaskRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_InterruptCodeAgentTaskRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *InterruptCodeAgentTaskRequest) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UserID = _field
	return nil
}
func (p *InterruptCodeAgentTaskRequest) ReadField2(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.MessageID = _field
	return nil
}
func (p *InterruptCodeAgentTaskRequest) ReadField3(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ConversationID = _field
	return nil
}
func (p *InterruptCodeAgentTaskRequest) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *InterruptCodeAgentTaskRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("InterruptCodeAgentTaskRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *InterruptCodeAgentTaskRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("UserID", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.UserID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *InterruptCodeAgentTaskRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("MessageID", thrift.I64, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.MessageID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *InterruptCodeAgentTaskRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ConversationID", thrift.I64, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.ConversationID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *InterruptCodeAgentTaskRequest) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *InterruptCodeAgentTaskRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("InterruptCodeAgentTaskRequest(%+v)", *p)

}

func (p *InterruptCodeAgentTaskRequest) DeepEqual(ano *InterruptCodeAgentTaskRequest) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.UserID) {
		return false
	}
	if !p.Field2DeepEqual(ano.MessageID) {
		return false
	}
	if !p.Field3DeepEqual(ano.ConversationID) {
		return false
	}
	if !p.Field255DeepEqual(ano.Base) {
		return false
	}
	return true
}

func (p *InterruptCodeAgentTaskRequest) Field1DeepEqual(src int64) bool {

	if p.UserID != src {
		return false
	}
	return true
}
func (p *InterruptCodeAgentTaskRequest) Field2DeepEqual(src int64) bool {

	if p.MessageID != src {
		return false
	}
	return true
}
func (p *InterruptCodeAgentTaskRequest) Field3DeepEqual(src int64) bool {

	if p.ConversationID != src {
		return false
	}
	return true
}
func (p *InterruptCodeAgentTaskRequest) Field255DeepEqual(src *base.Base) bool {

	if !p.Base.DeepEqual(src) {
		return false
	}
	return true
}

type InterruptCodeAgentTaskResponse struct {
	BaseResp *base.BaseResp `thrift:"BaseResp,255,optional" frugal:"255,optional,base.BaseResp" json:"BaseResp,omitempty"`
}

func NewInterruptCodeAgentTaskResponse() *InterruptCodeAgentTaskResponse {
	return &InterruptCodeAgentTaskResponse{}
}

func (p *InterruptCodeAgentTaskResponse) InitDefault() {
}

var InterruptCodeAgentTaskResponse_BaseResp_DEFAULT *base.BaseResp

func (p *InterruptCodeAgentTaskResponse) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return InterruptCodeAgentTaskResponse_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *InterruptCodeAgentTaskResponse) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_InterruptCodeAgentTaskResponse = map[int16]string{
	255: "BaseResp",
}

func (p *InterruptCodeAgentTaskResponse) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *InterruptCodeAgentTaskResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_InterruptCodeAgentTaskResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *InterruptCodeAgentTaskResponse) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *InterruptCodeAgentTaskResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("InterruptCodeAgentTaskResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *InterruptCodeAgentTaskResponse) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBaseResp() {
		if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.BaseResp.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *InterruptCodeAgentTaskResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("InterruptCodeAgentTaskResponse(%+v)", *p)

}

func (p *InterruptCodeAgentTaskResponse) DeepEqual(ano *InterruptCodeAgentTaskResponse) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field255DeepEqual(ano.BaseResp) {
		return false
	}
	return true
}

func (p *InterruptCodeAgentTaskResponse) Field255DeepEqual(src *base.BaseResp) bool {

	if !p.BaseResp.DeepEqual(src) {
		return false
	}
	return true
}

type ExecuteJobCallbackReq struct {
	InstanceID    int64      `thrift:"InstanceID,1" frugal:"1,default,i64" json:"InstanceID"`
	BizKey        string     `thrift:"BizKey,2" frugal:"2,default,string" json:"BizKey"`
	BizID         string     `thrift:"BizID,3" frugal:"3,default,string" json:"BizID"`
	JobParamBytes []byte     `thrift:"JobParamBytes,4" frugal:"4,default,binary" json:"JobParamBytes"`
	Base          *base.Base `thrift:"Base,255" frugal:"255,default,base.Base" json:"Base"`
}

func NewExecuteJobCallbackReq() *ExecuteJobCallbackReq {
	return &ExecuteJobCallbackReq{}
}

func (p *ExecuteJobCallbackReq) InitDefault() {
}

func (p *ExecuteJobCallbackReq) GetInstanceID() (v int64) {
	return p.InstanceID
}

func (p *ExecuteJobCallbackReq) GetBizKey() (v string) {
	return p.BizKey
}

func (p *ExecuteJobCallbackReq) GetBizID() (v string) {
	return p.BizID
}

func (p *ExecuteJobCallbackReq) GetJobParamBytes() (v []byte) {
	return p.JobParamBytes
}

var ExecuteJobCallbackReq_Base_DEFAULT *base.Base

func (p *ExecuteJobCallbackReq) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return ExecuteJobCallbackReq_Base_DEFAULT
	}
	return p.Base
}
func (p *ExecuteJobCallbackReq) SetInstanceID(val int64) {
	p.InstanceID = val
}
func (p *ExecuteJobCallbackReq) SetBizKey(val string) {
	p.BizKey = val
}
func (p *ExecuteJobCallbackReq) SetBizID(val string) {
	p.BizID = val
}
func (p *ExecuteJobCallbackReq) SetJobParamBytes(val []byte) {
	p.JobParamBytes = val
}
func (p *ExecuteJobCallbackReq) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_ExecuteJobCallbackReq = map[int16]string{
	1:   "InstanceID",
	2:   "BizKey",
	3:   "BizID",
	4:   "JobParamBytes",
	255: "Base",
}

func (p *ExecuteJobCallbackReq) IsSetBase() bool {
	return p.Base != nil
}

func (p *ExecuteJobCallbackReq) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ExecuteJobCallbackReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ExecuteJobCallbackReq) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceID = _field
	return nil
}
func (p *ExecuteJobCallbackReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BizKey = _field
	return nil
}
func (p *ExecuteJobCallbackReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BizID = _field
	return nil
}
func (p *ExecuteJobCallbackReq) ReadField4(iprot thrift.TProtocol) error {

	var _field []byte
	if v, err := iprot.ReadBinary(); err != nil {
		return err
	} else {
		_field = []byte(v)
	}
	p.JobParamBytes = _field
	return nil
}
func (p *ExecuteJobCallbackReq) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *ExecuteJobCallbackReq) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ExecuteJobCallbackReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ExecuteJobCallbackReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceID", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.InstanceID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ExecuteJobCallbackReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BizKey", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.BizKey); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ExecuteJobCallbackReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BizID", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.BizID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *ExecuteJobCallbackReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("JobParamBytes", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBinary([]byte(p.JobParamBytes)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *ExecuteJobCallbackReq) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Base.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *ExecuteJobCallbackReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ExecuteJobCallbackReq(%+v)", *p)

}

func (p *ExecuteJobCallbackReq) DeepEqual(ano *ExecuteJobCallbackReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceID) {
		return false
	}
	if !p.Field2DeepEqual(ano.BizKey) {
		return false
	}
	if !p.Field3DeepEqual(ano.BizID) {
		return false
	}
	if !p.Field4DeepEqual(ano.JobParamBytes) {
		return false
	}
	if !p.Field255DeepEqual(ano.Base) {
		return false
	}
	return true
}

func (p *ExecuteJobCallbackReq) Field1DeepEqual(src int64) bool {

	if p.InstanceID != src {
		return false
	}
	return true
}
func (p *ExecuteJobCallbackReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.BizKey, src) != 0 {
		return false
	}
	return true
}
func (p *ExecuteJobCallbackReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.BizID, src) != 0 {
		return false
	}
	return true
}
func (p *ExecuteJobCallbackReq) Field4DeepEqual(src []byte) bool {

	if bytes.Compare(p.JobParamBytes, src) != 0 {
		return false
	}
	return true
}
func (p *ExecuteJobCallbackReq) Field255DeepEqual(src *base.Base) bool {

	if !p.Base.DeepEqual(src) {
		return false
	}
	return true
}

type ExecuteJobCallbackResp struct {
	BaseResp *base.BaseResp `thrift:"BaseResp,255" frugal:"255,default,base.BaseResp" json:"BaseResp"`
}

func NewExecuteJobCallbackResp() *ExecuteJobCallbackResp {
	return &ExecuteJobCallbackResp{}
}

func (p *ExecuteJobCallbackResp) InitDefault() {
}

var ExecuteJobCallbackResp_BaseResp_DEFAULT *base.BaseResp

func (p *ExecuteJobCallbackResp) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return ExecuteJobCallbackResp_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *ExecuteJobCallbackResp) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_ExecuteJobCallbackResp = map[int16]string{
	255: "BaseResp",
}

func (p *ExecuteJobCallbackResp) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *ExecuteJobCallbackResp) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ExecuteJobCallbackResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ExecuteJobCallbackResp) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *ExecuteJobCallbackResp) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ExecuteJobCallbackResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ExecuteJobCallbackResp) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.BaseResp.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *ExecuteJobCallbackResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ExecuteJobCallbackResp(%+v)", *p)

}

func (p *ExecuteJobCallbackResp) DeepEqual(ano *ExecuteJobCallbackResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field255DeepEqual(ano.BaseResp) {
		return false
	}
	return true
}

func (p *ExecuteJobCallbackResp) Field255DeepEqual(src *base.BaseResp) bool {

	if !p.BaseResp.DeepEqual(src) {
		return false
	}
	return true
}
