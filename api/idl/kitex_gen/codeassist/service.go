// Code generated by thriftgo (0.4.2). DO NOT EDIT.

package codeassist

import (
	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/flow/bot/hook"
	"context"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"github.com/cloudwego/kitex/pkg/streaming"
)

type AssistantService interface {
	Ping(ctx context.Context, req *PingRequest) (r *PingResponse, err error)

	Chat(req *ChatRequest, stream AssistantService_ChatServer) (err error)

	PromptsRender(ctx context.Context, req *PromptsRenderRequest) (r *PromptsRenderResponse, err error)

	E2EPromptsRender(ctx context.Context, req *E2EPromptsRenderRequest) (r *E2EPromptsRenderResponse, err error)

	HomePage(ctx context.Context, req *HomePageRequest) (r *HomePageResponse, err error)

	SniffRepositoryLink(ctx context.Context, req *SniffRepositoryLinkRequest) (r *SniffRepositoryLinkResponse, err error)

	CreateContext(ctx context.Context, req *CreateContextRequest) (r *CreateContextResponse, err error)

	MGetContextStatus(ctx context.Context, req *MGetContextStatusRequest) (r *MGetContextStatusResponse, err error)

	GetContextDirectoryNodes(ctx context.Context, req *GetContextDirectoryNodesRequest) (r *GetContextDirectoryNodesResponse, err error)

	GetRepoFileContent(ctx context.Context, req *GetRepoFileContentRequest) (r *GetRepoFileContentResponse, err error)

	GetContextInfo(ctx context.Context, req *GetContextInfoRequest) (r *GetContextInfoResponse, err error)

	GetContextDownloadURL(ctx context.Context, req *GetContextDownloadURLRequest) (r *GetContextDownloadURLResponse, err error)

	GetCodeResourceDownloadURL(ctx context.Context, req *GetCodeResourceDownloadURLRequest) (r *GetCodeResourceDownloadURLResponse, err error)

	DesensitizeContext(ctx context.Context, req *DesensitizeContextRequest) (r *DesensitizeContextResponse, err error)

	BatchCheckFilePath(ctx context.Context, req *BatchCheckFilePathRequest) (r *BatchCheckFilePathResponse, err error)

	CodeExecutable(ctx context.Context, req *CodeExecutableRequest) (r *CodeExecutableResponse, err error)

	RunCode(ctx context.Context, req *RunCodeRequest) (r *RunCodeResponse, err error)

	RunCodeV2(ctx context.Context, req *RunCodeRequestV2) (r *RunCodeResponseV2, err error)

	GetArtifactTemplateFile(ctx context.Context, req *GetArtifactTemplateFileRequest) (r *GetArtifactTemplateFileResponse, err error)

	GetArtifactTemplateDir(ctx context.Context, req *GetArtifactTemplateDirRequest) (r *GetArtifactTemplateDirResponse, err error)

	CompileCodeArtifact(ctx context.Context, req *CompileCodeArtifactRequest) (r *CompileCodeArtifactResponse, err error)

	GetCompileStatus(ctx context.Context, req *GetCompileStatusRequest) (r *GetCompileStatusResponse, err error)

	GetArtifactsCodeURI(ctx context.Context, req *GetArtifactsCodeURIRequest) (r *GetArtifactsCodeURIResponse, err error)

	CreateSandbox(ctx context.Context, req *CreateSandboxRequest) (r *CreateSandboxResponse, err error)

	ReleaseSandbox(ctx context.Context, req *ReleaseSandboxRequest) (r *ReleaseSandboxResponse, err error)

	ExecuteCode(ctx context.Context, req *ExecuteCodeRequest) (r *ExecuteCodeResponse, err error)

	UploadFile(ctx context.Context, req *UploadFileRequest) (r *UploadFileResponse, err error)

	DownloadFile(ctx context.Context, req *DownloadFileRequest) (r *DownloadFileResponse, err error)

	ImageReview(ctx context.Context, req *ImageReviewRequest) (r *ImageReviewResponse, err error)

	SearchImages(ctx context.Context, req *SearchImagesRequest) (r *SearchImagesResponse, err error)

	InterruptCodeAgentTask(ctx context.Context, req *InterruptCodeAgentTaskRequest) (r *InterruptCodeAgentTaskResponse, err error)

	ExecuteJobCallback(ctx context.Context, req *ExecuteJobCallbackReq) (r *ExecuteJobCallbackResp, err error)
}

type AssistantService_ChatServer interface {
	streaming.Stream

	Send(*StreamChatResponse) error
}

type HookService interface {
	FlowHook(ctx context.Context, req *hook.FlowHookRequest) (r *hook.FlowHookResponse, err error)

	FlowStream(req *hook.FlowHookRequest, stream HookService_FlowStreamServer) (err error)
}

type HookService_FlowStreamServer interface {
	streaming.Stream

	Send(*hook.StreamPacket) error
}

type AssistantServicePingArgs struct {
	Req *PingRequest `thrift:"req,1" frugal:"1,default,PingRequest" json:"req"`
}

func NewAssistantServicePingArgs() *AssistantServicePingArgs {
	return &AssistantServicePingArgs{}
}

func (p *AssistantServicePingArgs) InitDefault() {
}

var AssistantServicePingArgs_Req_DEFAULT *PingRequest

func (p *AssistantServicePingArgs) GetReq() (v *PingRequest) {
	if !p.IsSetReq() {
		return AssistantServicePingArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *AssistantServicePingArgs) SetReq(val *PingRequest) {
	p.Req = val
}

var fieldIDToName_AssistantServicePingArgs = map[int16]string{
	1: "req",
}

func (p *AssistantServicePingArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *AssistantServicePingArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AssistantServicePingArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AssistantServicePingArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewPingRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *AssistantServicePingArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("Ping_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssistantServicePingArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *AssistantServicePingArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssistantServicePingArgs(%+v)", *p)

}

func (p *AssistantServicePingArgs) DeepEqual(ano *AssistantServicePingArgs) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Req) {
		return false
	}
	return true
}

func (p *AssistantServicePingArgs) Field1DeepEqual(src *PingRequest) bool {

	if !p.Req.DeepEqual(src) {
		return false
	}
	return true
}

type AssistantServicePingResult struct {
	Success *PingResponse `thrift:"success,0,optional" frugal:"0,optional,PingResponse" json:"success,omitempty"`
}

func NewAssistantServicePingResult() *AssistantServicePingResult {
	return &AssistantServicePingResult{}
}

func (p *AssistantServicePingResult) InitDefault() {
}

var AssistantServicePingResult_Success_DEFAULT *PingResponse

func (p *AssistantServicePingResult) GetSuccess() (v *PingResponse) {
	if !p.IsSetSuccess() {
		return AssistantServicePingResult_Success_DEFAULT
	}
	return p.Success
}
func (p *AssistantServicePingResult) SetSuccess(x interface{}) {
	p.Success = x.(*PingResponse)
}

var fieldIDToName_AssistantServicePingResult = map[int16]string{
	0: "success",
}

func (p *AssistantServicePingResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *AssistantServicePingResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AssistantServicePingResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AssistantServicePingResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewPingResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *AssistantServicePingResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("Ping_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssistantServicePingResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *AssistantServicePingResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssistantServicePingResult(%+v)", *p)

}

func (p *AssistantServicePingResult) DeepEqual(ano *AssistantServicePingResult) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field0DeepEqual(ano.Success) {
		return false
	}
	return true
}

func (p *AssistantServicePingResult) Field0DeepEqual(src *PingResponse) bool {

	if !p.Success.DeepEqual(src) {
		return false
	}
	return true
}

type AssistantServiceChatArgs struct {
	Req *ChatRequest `thrift:"req,1" frugal:"1,default,ChatRequest" json:"req"`
}

func NewAssistantServiceChatArgs() *AssistantServiceChatArgs {
	return &AssistantServiceChatArgs{}
}

func (p *AssistantServiceChatArgs) InitDefault() {
}

var AssistantServiceChatArgs_Req_DEFAULT *ChatRequest

func (p *AssistantServiceChatArgs) GetReq() (v *ChatRequest) {
	if !p.IsSetReq() {
		return AssistantServiceChatArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *AssistantServiceChatArgs) SetReq(val *ChatRequest) {
	p.Req = val
}

var fieldIDToName_AssistantServiceChatArgs = map[int16]string{
	1: "req",
}

func (p *AssistantServiceChatArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *AssistantServiceChatArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AssistantServiceChatArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AssistantServiceChatArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewChatRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *AssistantServiceChatArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("Chat_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssistantServiceChatArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *AssistantServiceChatArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssistantServiceChatArgs(%+v)", *p)

}

func (p *AssistantServiceChatArgs) DeepEqual(ano *AssistantServiceChatArgs) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Req) {
		return false
	}
	return true
}

func (p *AssistantServiceChatArgs) Field1DeepEqual(src *ChatRequest) bool {

	if !p.Req.DeepEqual(src) {
		return false
	}
	return true
}

type AssistantServiceChatResult struct {
	Success *StreamChatResponse `thrift:"success,0,optional" frugal:"0,optional,StreamChatResponse" json:"success,omitempty"`
}

func NewAssistantServiceChatResult() *AssistantServiceChatResult {
	return &AssistantServiceChatResult{}
}

func (p *AssistantServiceChatResult) InitDefault() {
}

var AssistantServiceChatResult_Success_DEFAULT *StreamChatResponse

func (p *AssistantServiceChatResult) GetSuccess() (v *StreamChatResponse) {
	if !p.IsSetSuccess() {
		return AssistantServiceChatResult_Success_DEFAULT
	}
	return p.Success
}
func (p *AssistantServiceChatResult) SetSuccess(x interface{}) {
	p.Success = x.(*StreamChatResponse)
}

var fieldIDToName_AssistantServiceChatResult = map[int16]string{
	0: "success",
}

func (p *AssistantServiceChatResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *AssistantServiceChatResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AssistantServiceChatResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AssistantServiceChatResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewStreamChatResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *AssistantServiceChatResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("Chat_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssistantServiceChatResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *AssistantServiceChatResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssistantServiceChatResult(%+v)", *p)

}

func (p *AssistantServiceChatResult) DeepEqual(ano *AssistantServiceChatResult) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field0DeepEqual(ano.Success) {
		return false
	}
	return true
}

func (p *AssistantServiceChatResult) Field0DeepEqual(src *StreamChatResponse) bool {

	if !p.Success.DeepEqual(src) {
		return false
	}
	return true
}

type AssistantServicePromptsRenderArgs struct {
	Req *PromptsRenderRequest `thrift:"req,1" frugal:"1,default,PromptsRenderRequest" json:"req"`
}

func NewAssistantServicePromptsRenderArgs() *AssistantServicePromptsRenderArgs {
	return &AssistantServicePromptsRenderArgs{}
}

func (p *AssistantServicePromptsRenderArgs) InitDefault() {
}

var AssistantServicePromptsRenderArgs_Req_DEFAULT *PromptsRenderRequest

func (p *AssistantServicePromptsRenderArgs) GetReq() (v *PromptsRenderRequest) {
	if !p.IsSetReq() {
		return AssistantServicePromptsRenderArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *AssistantServicePromptsRenderArgs) SetReq(val *PromptsRenderRequest) {
	p.Req = val
}

var fieldIDToName_AssistantServicePromptsRenderArgs = map[int16]string{
	1: "req",
}

func (p *AssistantServicePromptsRenderArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *AssistantServicePromptsRenderArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AssistantServicePromptsRenderArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AssistantServicePromptsRenderArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewPromptsRenderRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *AssistantServicePromptsRenderArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("PromptsRender_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssistantServicePromptsRenderArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *AssistantServicePromptsRenderArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssistantServicePromptsRenderArgs(%+v)", *p)

}

func (p *AssistantServicePromptsRenderArgs) DeepEqual(ano *AssistantServicePromptsRenderArgs) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Req) {
		return false
	}
	return true
}

func (p *AssistantServicePromptsRenderArgs) Field1DeepEqual(src *PromptsRenderRequest) bool {

	if !p.Req.DeepEqual(src) {
		return false
	}
	return true
}

type AssistantServicePromptsRenderResult struct {
	Success *PromptsRenderResponse `thrift:"success,0,optional" frugal:"0,optional,PromptsRenderResponse" json:"success,omitempty"`
}

func NewAssistantServicePromptsRenderResult() *AssistantServicePromptsRenderResult {
	return &AssistantServicePromptsRenderResult{}
}

func (p *AssistantServicePromptsRenderResult) InitDefault() {
}

var AssistantServicePromptsRenderResult_Success_DEFAULT *PromptsRenderResponse

func (p *AssistantServicePromptsRenderResult) GetSuccess() (v *PromptsRenderResponse) {
	if !p.IsSetSuccess() {
		return AssistantServicePromptsRenderResult_Success_DEFAULT
	}
	return p.Success
}
func (p *AssistantServicePromptsRenderResult) SetSuccess(x interface{}) {
	p.Success = x.(*PromptsRenderResponse)
}

var fieldIDToName_AssistantServicePromptsRenderResult = map[int16]string{
	0: "success",
}

func (p *AssistantServicePromptsRenderResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *AssistantServicePromptsRenderResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AssistantServicePromptsRenderResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AssistantServicePromptsRenderResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewPromptsRenderResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *AssistantServicePromptsRenderResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("PromptsRender_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssistantServicePromptsRenderResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *AssistantServicePromptsRenderResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssistantServicePromptsRenderResult(%+v)", *p)

}

func (p *AssistantServicePromptsRenderResult) DeepEqual(ano *AssistantServicePromptsRenderResult) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field0DeepEqual(ano.Success) {
		return false
	}
	return true
}

func (p *AssistantServicePromptsRenderResult) Field0DeepEqual(src *PromptsRenderResponse) bool {

	if !p.Success.DeepEqual(src) {
		return false
	}
	return true
}

type AssistantServiceE2EPromptsRenderArgs struct {
	Req *E2EPromptsRenderRequest `thrift:"req,1" frugal:"1,default,E2EPromptsRenderRequest" json:"req"`
}

func NewAssistantServiceE2EPromptsRenderArgs() *AssistantServiceE2EPromptsRenderArgs {
	return &AssistantServiceE2EPromptsRenderArgs{}
}

func (p *AssistantServiceE2EPromptsRenderArgs) InitDefault() {
}

var AssistantServiceE2EPromptsRenderArgs_Req_DEFAULT *E2EPromptsRenderRequest

func (p *AssistantServiceE2EPromptsRenderArgs) GetReq() (v *E2EPromptsRenderRequest) {
	if !p.IsSetReq() {
		return AssistantServiceE2EPromptsRenderArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *AssistantServiceE2EPromptsRenderArgs) SetReq(val *E2EPromptsRenderRequest) {
	p.Req = val
}

var fieldIDToName_AssistantServiceE2EPromptsRenderArgs = map[int16]string{
	1: "req",
}

func (p *AssistantServiceE2EPromptsRenderArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *AssistantServiceE2EPromptsRenderArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AssistantServiceE2EPromptsRenderArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AssistantServiceE2EPromptsRenderArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewE2EPromptsRenderRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *AssistantServiceE2EPromptsRenderArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("E2EPromptsRender_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssistantServiceE2EPromptsRenderArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *AssistantServiceE2EPromptsRenderArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssistantServiceE2EPromptsRenderArgs(%+v)", *p)

}

func (p *AssistantServiceE2EPromptsRenderArgs) DeepEqual(ano *AssistantServiceE2EPromptsRenderArgs) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Req) {
		return false
	}
	return true
}

func (p *AssistantServiceE2EPromptsRenderArgs) Field1DeepEqual(src *E2EPromptsRenderRequest) bool {

	if !p.Req.DeepEqual(src) {
		return false
	}
	return true
}

type AssistantServiceE2EPromptsRenderResult struct {
	Success *E2EPromptsRenderResponse `thrift:"success,0,optional" frugal:"0,optional,E2EPromptsRenderResponse" json:"success,omitempty"`
}

func NewAssistantServiceE2EPromptsRenderResult() *AssistantServiceE2EPromptsRenderResult {
	return &AssistantServiceE2EPromptsRenderResult{}
}

func (p *AssistantServiceE2EPromptsRenderResult) InitDefault() {
}

var AssistantServiceE2EPromptsRenderResult_Success_DEFAULT *E2EPromptsRenderResponse

func (p *AssistantServiceE2EPromptsRenderResult) GetSuccess() (v *E2EPromptsRenderResponse) {
	if !p.IsSetSuccess() {
		return AssistantServiceE2EPromptsRenderResult_Success_DEFAULT
	}
	return p.Success
}
func (p *AssistantServiceE2EPromptsRenderResult) SetSuccess(x interface{}) {
	p.Success = x.(*E2EPromptsRenderResponse)
}

var fieldIDToName_AssistantServiceE2EPromptsRenderResult = map[int16]string{
	0: "success",
}

func (p *AssistantServiceE2EPromptsRenderResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *AssistantServiceE2EPromptsRenderResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AssistantServiceE2EPromptsRenderResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AssistantServiceE2EPromptsRenderResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewE2EPromptsRenderResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *AssistantServiceE2EPromptsRenderResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("E2EPromptsRender_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssistantServiceE2EPromptsRenderResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *AssistantServiceE2EPromptsRenderResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssistantServiceE2EPromptsRenderResult(%+v)", *p)

}

func (p *AssistantServiceE2EPromptsRenderResult) DeepEqual(ano *AssistantServiceE2EPromptsRenderResult) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field0DeepEqual(ano.Success) {
		return false
	}
	return true
}

func (p *AssistantServiceE2EPromptsRenderResult) Field0DeepEqual(src *E2EPromptsRenderResponse) bool {

	if !p.Success.DeepEqual(src) {
		return false
	}
	return true
}

type AssistantServiceHomePageArgs struct {
	Req *HomePageRequest `thrift:"req,1" frugal:"1,default,HomePageRequest" json:"req"`
}

func NewAssistantServiceHomePageArgs() *AssistantServiceHomePageArgs {
	return &AssistantServiceHomePageArgs{}
}

func (p *AssistantServiceHomePageArgs) InitDefault() {
}

var AssistantServiceHomePageArgs_Req_DEFAULT *HomePageRequest

func (p *AssistantServiceHomePageArgs) GetReq() (v *HomePageRequest) {
	if !p.IsSetReq() {
		return AssistantServiceHomePageArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *AssistantServiceHomePageArgs) SetReq(val *HomePageRequest) {
	p.Req = val
}

var fieldIDToName_AssistantServiceHomePageArgs = map[int16]string{
	1: "req",
}

func (p *AssistantServiceHomePageArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *AssistantServiceHomePageArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AssistantServiceHomePageArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AssistantServiceHomePageArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewHomePageRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *AssistantServiceHomePageArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("HomePage_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssistantServiceHomePageArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *AssistantServiceHomePageArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssistantServiceHomePageArgs(%+v)", *p)

}

func (p *AssistantServiceHomePageArgs) DeepEqual(ano *AssistantServiceHomePageArgs) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Req) {
		return false
	}
	return true
}

func (p *AssistantServiceHomePageArgs) Field1DeepEqual(src *HomePageRequest) bool {

	if !p.Req.DeepEqual(src) {
		return false
	}
	return true
}

type AssistantServiceHomePageResult struct {
	Success *HomePageResponse `thrift:"success,0,optional" frugal:"0,optional,HomePageResponse" json:"success,omitempty"`
}

func NewAssistantServiceHomePageResult() *AssistantServiceHomePageResult {
	return &AssistantServiceHomePageResult{}
}

func (p *AssistantServiceHomePageResult) InitDefault() {
}

var AssistantServiceHomePageResult_Success_DEFAULT *HomePageResponse

func (p *AssistantServiceHomePageResult) GetSuccess() (v *HomePageResponse) {
	if !p.IsSetSuccess() {
		return AssistantServiceHomePageResult_Success_DEFAULT
	}
	return p.Success
}
func (p *AssistantServiceHomePageResult) SetSuccess(x interface{}) {
	p.Success = x.(*HomePageResponse)
}

var fieldIDToName_AssistantServiceHomePageResult = map[int16]string{
	0: "success",
}

func (p *AssistantServiceHomePageResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *AssistantServiceHomePageResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AssistantServiceHomePageResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AssistantServiceHomePageResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewHomePageResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *AssistantServiceHomePageResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("HomePage_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssistantServiceHomePageResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *AssistantServiceHomePageResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssistantServiceHomePageResult(%+v)", *p)

}

func (p *AssistantServiceHomePageResult) DeepEqual(ano *AssistantServiceHomePageResult) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field0DeepEqual(ano.Success) {
		return false
	}
	return true
}

func (p *AssistantServiceHomePageResult) Field0DeepEqual(src *HomePageResponse) bool {

	if !p.Success.DeepEqual(src) {
		return false
	}
	return true
}

type AssistantServiceSniffRepositoryLinkArgs struct {
	Req *SniffRepositoryLinkRequest `thrift:"req,1" frugal:"1,default,SniffRepositoryLinkRequest" json:"req"`
}

func NewAssistantServiceSniffRepositoryLinkArgs() *AssistantServiceSniffRepositoryLinkArgs {
	return &AssistantServiceSniffRepositoryLinkArgs{}
}

func (p *AssistantServiceSniffRepositoryLinkArgs) InitDefault() {
}

var AssistantServiceSniffRepositoryLinkArgs_Req_DEFAULT *SniffRepositoryLinkRequest

func (p *AssistantServiceSniffRepositoryLinkArgs) GetReq() (v *SniffRepositoryLinkRequest) {
	if !p.IsSetReq() {
		return AssistantServiceSniffRepositoryLinkArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *AssistantServiceSniffRepositoryLinkArgs) SetReq(val *SniffRepositoryLinkRequest) {
	p.Req = val
}

var fieldIDToName_AssistantServiceSniffRepositoryLinkArgs = map[int16]string{
	1: "req",
}

func (p *AssistantServiceSniffRepositoryLinkArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *AssistantServiceSniffRepositoryLinkArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AssistantServiceSniffRepositoryLinkArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AssistantServiceSniffRepositoryLinkArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewSniffRepositoryLinkRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *AssistantServiceSniffRepositoryLinkArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("SniffRepositoryLink_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssistantServiceSniffRepositoryLinkArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *AssistantServiceSniffRepositoryLinkArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssistantServiceSniffRepositoryLinkArgs(%+v)", *p)

}

func (p *AssistantServiceSniffRepositoryLinkArgs) DeepEqual(ano *AssistantServiceSniffRepositoryLinkArgs) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Req) {
		return false
	}
	return true
}

func (p *AssistantServiceSniffRepositoryLinkArgs) Field1DeepEqual(src *SniffRepositoryLinkRequest) bool {

	if !p.Req.DeepEqual(src) {
		return false
	}
	return true
}

type AssistantServiceSniffRepositoryLinkResult struct {
	Success *SniffRepositoryLinkResponse `thrift:"success,0,optional" frugal:"0,optional,SniffRepositoryLinkResponse" json:"success,omitempty"`
}

func NewAssistantServiceSniffRepositoryLinkResult() *AssistantServiceSniffRepositoryLinkResult {
	return &AssistantServiceSniffRepositoryLinkResult{}
}

func (p *AssistantServiceSniffRepositoryLinkResult) InitDefault() {
}

var AssistantServiceSniffRepositoryLinkResult_Success_DEFAULT *SniffRepositoryLinkResponse

func (p *AssistantServiceSniffRepositoryLinkResult) GetSuccess() (v *SniffRepositoryLinkResponse) {
	if !p.IsSetSuccess() {
		return AssistantServiceSniffRepositoryLinkResult_Success_DEFAULT
	}
	return p.Success
}
func (p *AssistantServiceSniffRepositoryLinkResult) SetSuccess(x interface{}) {
	p.Success = x.(*SniffRepositoryLinkResponse)
}

var fieldIDToName_AssistantServiceSniffRepositoryLinkResult = map[int16]string{
	0: "success",
}

func (p *AssistantServiceSniffRepositoryLinkResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *AssistantServiceSniffRepositoryLinkResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AssistantServiceSniffRepositoryLinkResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AssistantServiceSniffRepositoryLinkResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewSniffRepositoryLinkResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *AssistantServiceSniffRepositoryLinkResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("SniffRepositoryLink_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssistantServiceSniffRepositoryLinkResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *AssistantServiceSniffRepositoryLinkResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssistantServiceSniffRepositoryLinkResult(%+v)", *p)

}

func (p *AssistantServiceSniffRepositoryLinkResult) DeepEqual(ano *AssistantServiceSniffRepositoryLinkResult) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field0DeepEqual(ano.Success) {
		return false
	}
	return true
}

func (p *AssistantServiceSniffRepositoryLinkResult) Field0DeepEqual(src *SniffRepositoryLinkResponse) bool {

	if !p.Success.DeepEqual(src) {
		return false
	}
	return true
}

type AssistantServiceCreateContextArgs struct {
	Req *CreateContextRequest `thrift:"req,1" frugal:"1,default,CreateContextRequest" json:"req"`
}

func NewAssistantServiceCreateContextArgs() *AssistantServiceCreateContextArgs {
	return &AssistantServiceCreateContextArgs{}
}

func (p *AssistantServiceCreateContextArgs) InitDefault() {
}

var AssistantServiceCreateContextArgs_Req_DEFAULT *CreateContextRequest

func (p *AssistantServiceCreateContextArgs) GetReq() (v *CreateContextRequest) {
	if !p.IsSetReq() {
		return AssistantServiceCreateContextArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *AssistantServiceCreateContextArgs) SetReq(val *CreateContextRequest) {
	p.Req = val
}

var fieldIDToName_AssistantServiceCreateContextArgs = map[int16]string{
	1: "req",
}

func (p *AssistantServiceCreateContextArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *AssistantServiceCreateContextArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AssistantServiceCreateContextArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AssistantServiceCreateContextArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewCreateContextRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *AssistantServiceCreateContextArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("CreateContext_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssistantServiceCreateContextArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *AssistantServiceCreateContextArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssistantServiceCreateContextArgs(%+v)", *p)

}

func (p *AssistantServiceCreateContextArgs) DeepEqual(ano *AssistantServiceCreateContextArgs) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Req) {
		return false
	}
	return true
}

func (p *AssistantServiceCreateContextArgs) Field1DeepEqual(src *CreateContextRequest) bool {

	if !p.Req.DeepEqual(src) {
		return false
	}
	return true
}

type AssistantServiceCreateContextResult struct {
	Success *CreateContextResponse `thrift:"success,0,optional" frugal:"0,optional,CreateContextResponse" json:"success,omitempty"`
}

func NewAssistantServiceCreateContextResult() *AssistantServiceCreateContextResult {
	return &AssistantServiceCreateContextResult{}
}

func (p *AssistantServiceCreateContextResult) InitDefault() {
}

var AssistantServiceCreateContextResult_Success_DEFAULT *CreateContextResponse

func (p *AssistantServiceCreateContextResult) GetSuccess() (v *CreateContextResponse) {
	if !p.IsSetSuccess() {
		return AssistantServiceCreateContextResult_Success_DEFAULT
	}
	return p.Success
}
func (p *AssistantServiceCreateContextResult) SetSuccess(x interface{}) {
	p.Success = x.(*CreateContextResponse)
}

var fieldIDToName_AssistantServiceCreateContextResult = map[int16]string{
	0: "success",
}

func (p *AssistantServiceCreateContextResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *AssistantServiceCreateContextResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AssistantServiceCreateContextResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AssistantServiceCreateContextResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewCreateContextResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *AssistantServiceCreateContextResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("CreateContext_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssistantServiceCreateContextResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *AssistantServiceCreateContextResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssistantServiceCreateContextResult(%+v)", *p)

}

func (p *AssistantServiceCreateContextResult) DeepEqual(ano *AssistantServiceCreateContextResult) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field0DeepEqual(ano.Success) {
		return false
	}
	return true
}

func (p *AssistantServiceCreateContextResult) Field0DeepEqual(src *CreateContextResponse) bool {

	if !p.Success.DeepEqual(src) {
		return false
	}
	return true
}

type AssistantServiceMGetContextStatusArgs struct {
	Req *MGetContextStatusRequest `thrift:"req,1" frugal:"1,default,MGetContextStatusRequest" json:"req"`
}

func NewAssistantServiceMGetContextStatusArgs() *AssistantServiceMGetContextStatusArgs {
	return &AssistantServiceMGetContextStatusArgs{}
}

func (p *AssistantServiceMGetContextStatusArgs) InitDefault() {
}

var AssistantServiceMGetContextStatusArgs_Req_DEFAULT *MGetContextStatusRequest

func (p *AssistantServiceMGetContextStatusArgs) GetReq() (v *MGetContextStatusRequest) {
	if !p.IsSetReq() {
		return AssistantServiceMGetContextStatusArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *AssistantServiceMGetContextStatusArgs) SetReq(val *MGetContextStatusRequest) {
	p.Req = val
}

var fieldIDToName_AssistantServiceMGetContextStatusArgs = map[int16]string{
	1: "req",
}

func (p *AssistantServiceMGetContextStatusArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *AssistantServiceMGetContextStatusArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AssistantServiceMGetContextStatusArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AssistantServiceMGetContextStatusArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewMGetContextStatusRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *AssistantServiceMGetContextStatusArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("MGetContextStatus_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssistantServiceMGetContextStatusArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *AssistantServiceMGetContextStatusArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssistantServiceMGetContextStatusArgs(%+v)", *p)

}

func (p *AssistantServiceMGetContextStatusArgs) DeepEqual(ano *AssistantServiceMGetContextStatusArgs) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Req) {
		return false
	}
	return true
}

func (p *AssistantServiceMGetContextStatusArgs) Field1DeepEqual(src *MGetContextStatusRequest) bool {

	if !p.Req.DeepEqual(src) {
		return false
	}
	return true
}

type AssistantServiceMGetContextStatusResult struct {
	Success *MGetContextStatusResponse `thrift:"success,0,optional" frugal:"0,optional,MGetContextStatusResponse" json:"success,omitempty"`
}

func NewAssistantServiceMGetContextStatusResult() *AssistantServiceMGetContextStatusResult {
	return &AssistantServiceMGetContextStatusResult{}
}

func (p *AssistantServiceMGetContextStatusResult) InitDefault() {
}

var AssistantServiceMGetContextStatusResult_Success_DEFAULT *MGetContextStatusResponse

func (p *AssistantServiceMGetContextStatusResult) GetSuccess() (v *MGetContextStatusResponse) {
	if !p.IsSetSuccess() {
		return AssistantServiceMGetContextStatusResult_Success_DEFAULT
	}
	return p.Success
}
func (p *AssistantServiceMGetContextStatusResult) SetSuccess(x interface{}) {
	p.Success = x.(*MGetContextStatusResponse)
}

var fieldIDToName_AssistantServiceMGetContextStatusResult = map[int16]string{
	0: "success",
}

func (p *AssistantServiceMGetContextStatusResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *AssistantServiceMGetContextStatusResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AssistantServiceMGetContextStatusResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AssistantServiceMGetContextStatusResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewMGetContextStatusResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *AssistantServiceMGetContextStatusResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("MGetContextStatus_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssistantServiceMGetContextStatusResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *AssistantServiceMGetContextStatusResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssistantServiceMGetContextStatusResult(%+v)", *p)

}

func (p *AssistantServiceMGetContextStatusResult) DeepEqual(ano *AssistantServiceMGetContextStatusResult) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field0DeepEqual(ano.Success) {
		return false
	}
	return true
}

func (p *AssistantServiceMGetContextStatusResult) Field0DeepEqual(src *MGetContextStatusResponse) bool {

	if !p.Success.DeepEqual(src) {
		return false
	}
	return true
}

type AssistantServiceGetContextDirectoryNodesArgs struct {
	Req *GetContextDirectoryNodesRequest `thrift:"req,1" frugal:"1,default,GetContextDirectoryNodesRequest" json:"req"`
}

func NewAssistantServiceGetContextDirectoryNodesArgs() *AssistantServiceGetContextDirectoryNodesArgs {
	return &AssistantServiceGetContextDirectoryNodesArgs{}
}

func (p *AssistantServiceGetContextDirectoryNodesArgs) InitDefault() {
}

var AssistantServiceGetContextDirectoryNodesArgs_Req_DEFAULT *GetContextDirectoryNodesRequest

func (p *AssistantServiceGetContextDirectoryNodesArgs) GetReq() (v *GetContextDirectoryNodesRequest) {
	if !p.IsSetReq() {
		return AssistantServiceGetContextDirectoryNodesArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *AssistantServiceGetContextDirectoryNodesArgs) SetReq(val *GetContextDirectoryNodesRequest) {
	p.Req = val
}

var fieldIDToName_AssistantServiceGetContextDirectoryNodesArgs = map[int16]string{
	1: "req",
}

func (p *AssistantServiceGetContextDirectoryNodesArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *AssistantServiceGetContextDirectoryNodesArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AssistantServiceGetContextDirectoryNodesArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AssistantServiceGetContextDirectoryNodesArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewGetContextDirectoryNodesRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *AssistantServiceGetContextDirectoryNodesArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetContextDirectoryNodes_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssistantServiceGetContextDirectoryNodesArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *AssistantServiceGetContextDirectoryNodesArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssistantServiceGetContextDirectoryNodesArgs(%+v)", *p)

}

func (p *AssistantServiceGetContextDirectoryNodesArgs) DeepEqual(ano *AssistantServiceGetContextDirectoryNodesArgs) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Req) {
		return false
	}
	return true
}

func (p *AssistantServiceGetContextDirectoryNodesArgs) Field1DeepEqual(src *GetContextDirectoryNodesRequest) bool {

	if !p.Req.DeepEqual(src) {
		return false
	}
	return true
}

type AssistantServiceGetContextDirectoryNodesResult struct {
	Success *GetContextDirectoryNodesResponse `thrift:"success,0,optional" frugal:"0,optional,GetContextDirectoryNodesResponse" json:"success,omitempty"`
}

func NewAssistantServiceGetContextDirectoryNodesResult() *AssistantServiceGetContextDirectoryNodesResult {
	return &AssistantServiceGetContextDirectoryNodesResult{}
}

func (p *AssistantServiceGetContextDirectoryNodesResult) InitDefault() {
}

var AssistantServiceGetContextDirectoryNodesResult_Success_DEFAULT *GetContextDirectoryNodesResponse

func (p *AssistantServiceGetContextDirectoryNodesResult) GetSuccess() (v *GetContextDirectoryNodesResponse) {
	if !p.IsSetSuccess() {
		return AssistantServiceGetContextDirectoryNodesResult_Success_DEFAULT
	}
	return p.Success
}
func (p *AssistantServiceGetContextDirectoryNodesResult) SetSuccess(x interface{}) {
	p.Success = x.(*GetContextDirectoryNodesResponse)
}

var fieldIDToName_AssistantServiceGetContextDirectoryNodesResult = map[int16]string{
	0: "success",
}

func (p *AssistantServiceGetContextDirectoryNodesResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *AssistantServiceGetContextDirectoryNodesResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AssistantServiceGetContextDirectoryNodesResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AssistantServiceGetContextDirectoryNodesResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewGetContextDirectoryNodesResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *AssistantServiceGetContextDirectoryNodesResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetContextDirectoryNodes_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssistantServiceGetContextDirectoryNodesResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *AssistantServiceGetContextDirectoryNodesResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssistantServiceGetContextDirectoryNodesResult(%+v)", *p)

}

func (p *AssistantServiceGetContextDirectoryNodesResult) DeepEqual(ano *AssistantServiceGetContextDirectoryNodesResult) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field0DeepEqual(ano.Success) {
		return false
	}
	return true
}

func (p *AssistantServiceGetContextDirectoryNodesResult) Field0DeepEqual(src *GetContextDirectoryNodesResponse) bool {

	if !p.Success.DeepEqual(src) {
		return false
	}
	return true
}

type AssistantServiceGetRepoFileContentArgs struct {
	Req *GetRepoFileContentRequest `thrift:"req,1" frugal:"1,default,GetRepoFileContentRequest" json:"req"`
}

func NewAssistantServiceGetRepoFileContentArgs() *AssistantServiceGetRepoFileContentArgs {
	return &AssistantServiceGetRepoFileContentArgs{}
}

func (p *AssistantServiceGetRepoFileContentArgs) InitDefault() {
}

var AssistantServiceGetRepoFileContentArgs_Req_DEFAULT *GetRepoFileContentRequest

func (p *AssistantServiceGetRepoFileContentArgs) GetReq() (v *GetRepoFileContentRequest) {
	if !p.IsSetReq() {
		return AssistantServiceGetRepoFileContentArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *AssistantServiceGetRepoFileContentArgs) SetReq(val *GetRepoFileContentRequest) {
	p.Req = val
}

var fieldIDToName_AssistantServiceGetRepoFileContentArgs = map[int16]string{
	1: "req",
}

func (p *AssistantServiceGetRepoFileContentArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *AssistantServiceGetRepoFileContentArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AssistantServiceGetRepoFileContentArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AssistantServiceGetRepoFileContentArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewGetRepoFileContentRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *AssistantServiceGetRepoFileContentArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetRepoFileContent_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssistantServiceGetRepoFileContentArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *AssistantServiceGetRepoFileContentArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssistantServiceGetRepoFileContentArgs(%+v)", *p)

}

func (p *AssistantServiceGetRepoFileContentArgs) DeepEqual(ano *AssistantServiceGetRepoFileContentArgs) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Req) {
		return false
	}
	return true
}

func (p *AssistantServiceGetRepoFileContentArgs) Field1DeepEqual(src *GetRepoFileContentRequest) bool {

	if !p.Req.DeepEqual(src) {
		return false
	}
	return true
}

type AssistantServiceGetRepoFileContentResult struct {
	Success *GetRepoFileContentResponse `thrift:"success,0,optional" frugal:"0,optional,GetRepoFileContentResponse" json:"success,omitempty"`
}

func NewAssistantServiceGetRepoFileContentResult() *AssistantServiceGetRepoFileContentResult {
	return &AssistantServiceGetRepoFileContentResult{}
}

func (p *AssistantServiceGetRepoFileContentResult) InitDefault() {
}

var AssistantServiceGetRepoFileContentResult_Success_DEFAULT *GetRepoFileContentResponse

func (p *AssistantServiceGetRepoFileContentResult) GetSuccess() (v *GetRepoFileContentResponse) {
	if !p.IsSetSuccess() {
		return AssistantServiceGetRepoFileContentResult_Success_DEFAULT
	}
	return p.Success
}
func (p *AssistantServiceGetRepoFileContentResult) SetSuccess(x interface{}) {
	p.Success = x.(*GetRepoFileContentResponse)
}

var fieldIDToName_AssistantServiceGetRepoFileContentResult = map[int16]string{
	0: "success",
}

func (p *AssistantServiceGetRepoFileContentResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *AssistantServiceGetRepoFileContentResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AssistantServiceGetRepoFileContentResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AssistantServiceGetRepoFileContentResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewGetRepoFileContentResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *AssistantServiceGetRepoFileContentResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetRepoFileContent_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssistantServiceGetRepoFileContentResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *AssistantServiceGetRepoFileContentResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssistantServiceGetRepoFileContentResult(%+v)", *p)

}

func (p *AssistantServiceGetRepoFileContentResult) DeepEqual(ano *AssistantServiceGetRepoFileContentResult) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field0DeepEqual(ano.Success) {
		return false
	}
	return true
}

func (p *AssistantServiceGetRepoFileContentResult) Field0DeepEqual(src *GetRepoFileContentResponse) bool {

	if !p.Success.DeepEqual(src) {
		return false
	}
	return true
}

type AssistantServiceGetContextInfoArgs struct {
	Req *GetContextInfoRequest `thrift:"req,1" frugal:"1,default,GetContextInfoRequest" json:"req"`
}

func NewAssistantServiceGetContextInfoArgs() *AssistantServiceGetContextInfoArgs {
	return &AssistantServiceGetContextInfoArgs{}
}

func (p *AssistantServiceGetContextInfoArgs) InitDefault() {
}

var AssistantServiceGetContextInfoArgs_Req_DEFAULT *GetContextInfoRequest

func (p *AssistantServiceGetContextInfoArgs) GetReq() (v *GetContextInfoRequest) {
	if !p.IsSetReq() {
		return AssistantServiceGetContextInfoArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *AssistantServiceGetContextInfoArgs) SetReq(val *GetContextInfoRequest) {
	p.Req = val
}

var fieldIDToName_AssistantServiceGetContextInfoArgs = map[int16]string{
	1: "req",
}

func (p *AssistantServiceGetContextInfoArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *AssistantServiceGetContextInfoArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AssistantServiceGetContextInfoArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AssistantServiceGetContextInfoArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewGetContextInfoRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *AssistantServiceGetContextInfoArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetContextInfo_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssistantServiceGetContextInfoArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *AssistantServiceGetContextInfoArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssistantServiceGetContextInfoArgs(%+v)", *p)

}

func (p *AssistantServiceGetContextInfoArgs) DeepEqual(ano *AssistantServiceGetContextInfoArgs) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Req) {
		return false
	}
	return true
}

func (p *AssistantServiceGetContextInfoArgs) Field1DeepEqual(src *GetContextInfoRequest) bool {

	if !p.Req.DeepEqual(src) {
		return false
	}
	return true
}

type AssistantServiceGetContextInfoResult struct {
	Success *GetContextInfoResponse `thrift:"success,0,optional" frugal:"0,optional,GetContextInfoResponse" json:"success,omitempty"`
}

func NewAssistantServiceGetContextInfoResult() *AssistantServiceGetContextInfoResult {
	return &AssistantServiceGetContextInfoResult{}
}

func (p *AssistantServiceGetContextInfoResult) InitDefault() {
}

var AssistantServiceGetContextInfoResult_Success_DEFAULT *GetContextInfoResponse

func (p *AssistantServiceGetContextInfoResult) GetSuccess() (v *GetContextInfoResponse) {
	if !p.IsSetSuccess() {
		return AssistantServiceGetContextInfoResult_Success_DEFAULT
	}
	return p.Success
}
func (p *AssistantServiceGetContextInfoResult) SetSuccess(x interface{}) {
	p.Success = x.(*GetContextInfoResponse)
}

var fieldIDToName_AssistantServiceGetContextInfoResult = map[int16]string{
	0: "success",
}

func (p *AssistantServiceGetContextInfoResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *AssistantServiceGetContextInfoResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AssistantServiceGetContextInfoResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AssistantServiceGetContextInfoResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewGetContextInfoResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *AssistantServiceGetContextInfoResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetContextInfo_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssistantServiceGetContextInfoResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *AssistantServiceGetContextInfoResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssistantServiceGetContextInfoResult(%+v)", *p)

}

func (p *AssistantServiceGetContextInfoResult) DeepEqual(ano *AssistantServiceGetContextInfoResult) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field0DeepEqual(ano.Success) {
		return false
	}
	return true
}

func (p *AssistantServiceGetContextInfoResult) Field0DeepEqual(src *GetContextInfoResponse) bool {

	if !p.Success.DeepEqual(src) {
		return false
	}
	return true
}

type AssistantServiceGetContextDownloadURLArgs struct {
	Req *GetContextDownloadURLRequest `thrift:"req,1" frugal:"1,default,GetContextDownloadURLRequest" json:"req"`
}

func NewAssistantServiceGetContextDownloadURLArgs() *AssistantServiceGetContextDownloadURLArgs {
	return &AssistantServiceGetContextDownloadURLArgs{}
}

func (p *AssistantServiceGetContextDownloadURLArgs) InitDefault() {
}

var AssistantServiceGetContextDownloadURLArgs_Req_DEFAULT *GetContextDownloadURLRequest

func (p *AssistantServiceGetContextDownloadURLArgs) GetReq() (v *GetContextDownloadURLRequest) {
	if !p.IsSetReq() {
		return AssistantServiceGetContextDownloadURLArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *AssistantServiceGetContextDownloadURLArgs) SetReq(val *GetContextDownloadURLRequest) {
	p.Req = val
}

var fieldIDToName_AssistantServiceGetContextDownloadURLArgs = map[int16]string{
	1: "req",
}

func (p *AssistantServiceGetContextDownloadURLArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *AssistantServiceGetContextDownloadURLArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AssistantServiceGetContextDownloadURLArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AssistantServiceGetContextDownloadURLArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewGetContextDownloadURLRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *AssistantServiceGetContextDownloadURLArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetContextDownloadURL_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssistantServiceGetContextDownloadURLArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *AssistantServiceGetContextDownloadURLArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssistantServiceGetContextDownloadURLArgs(%+v)", *p)

}

func (p *AssistantServiceGetContextDownloadURLArgs) DeepEqual(ano *AssistantServiceGetContextDownloadURLArgs) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Req) {
		return false
	}
	return true
}

func (p *AssistantServiceGetContextDownloadURLArgs) Field1DeepEqual(src *GetContextDownloadURLRequest) bool {

	if !p.Req.DeepEqual(src) {
		return false
	}
	return true
}

type AssistantServiceGetContextDownloadURLResult struct {
	Success *GetContextDownloadURLResponse `thrift:"success,0,optional" frugal:"0,optional,GetContextDownloadURLResponse" json:"success,omitempty"`
}

func NewAssistantServiceGetContextDownloadURLResult() *AssistantServiceGetContextDownloadURLResult {
	return &AssistantServiceGetContextDownloadURLResult{}
}

func (p *AssistantServiceGetContextDownloadURLResult) InitDefault() {
}

var AssistantServiceGetContextDownloadURLResult_Success_DEFAULT *GetContextDownloadURLResponse

func (p *AssistantServiceGetContextDownloadURLResult) GetSuccess() (v *GetContextDownloadURLResponse) {
	if !p.IsSetSuccess() {
		return AssistantServiceGetContextDownloadURLResult_Success_DEFAULT
	}
	return p.Success
}
func (p *AssistantServiceGetContextDownloadURLResult) SetSuccess(x interface{}) {
	p.Success = x.(*GetContextDownloadURLResponse)
}

var fieldIDToName_AssistantServiceGetContextDownloadURLResult = map[int16]string{
	0: "success",
}

func (p *AssistantServiceGetContextDownloadURLResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *AssistantServiceGetContextDownloadURLResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AssistantServiceGetContextDownloadURLResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AssistantServiceGetContextDownloadURLResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewGetContextDownloadURLResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *AssistantServiceGetContextDownloadURLResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetContextDownloadURL_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssistantServiceGetContextDownloadURLResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *AssistantServiceGetContextDownloadURLResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssistantServiceGetContextDownloadURLResult(%+v)", *p)

}

func (p *AssistantServiceGetContextDownloadURLResult) DeepEqual(ano *AssistantServiceGetContextDownloadURLResult) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field0DeepEqual(ano.Success) {
		return false
	}
	return true
}

func (p *AssistantServiceGetContextDownloadURLResult) Field0DeepEqual(src *GetContextDownloadURLResponse) bool {

	if !p.Success.DeepEqual(src) {
		return false
	}
	return true
}

type AssistantServiceGetCodeResourceDownloadURLArgs struct {
	Req *GetCodeResourceDownloadURLRequest `thrift:"req,1" frugal:"1,default,GetCodeResourceDownloadURLRequest" json:"req"`
}

func NewAssistantServiceGetCodeResourceDownloadURLArgs() *AssistantServiceGetCodeResourceDownloadURLArgs {
	return &AssistantServiceGetCodeResourceDownloadURLArgs{}
}

func (p *AssistantServiceGetCodeResourceDownloadURLArgs) InitDefault() {
}

var AssistantServiceGetCodeResourceDownloadURLArgs_Req_DEFAULT *GetCodeResourceDownloadURLRequest

func (p *AssistantServiceGetCodeResourceDownloadURLArgs) GetReq() (v *GetCodeResourceDownloadURLRequest) {
	if !p.IsSetReq() {
		return AssistantServiceGetCodeResourceDownloadURLArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *AssistantServiceGetCodeResourceDownloadURLArgs) SetReq(val *GetCodeResourceDownloadURLRequest) {
	p.Req = val
}

var fieldIDToName_AssistantServiceGetCodeResourceDownloadURLArgs = map[int16]string{
	1: "req",
}

func (p *AssistantServiceGetCodeResourceDownloadURLArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *AssistantServiceGetCodeResourceDownloadURLArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AssistantServiceGetCodeResourceDownloadURLArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AssistantServiceGetCodeResourceDownloadURLArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewGetCodeResourceDownloadURLRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *AssistantServiceGetCodeResourceDownloadURLArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetCodeResourceDownloadURL_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssistantServiceGetCodeResourceDownloadURLArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *AssistantServiceGetCodeResourceDownloadURLArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssistantServiceGetCodeResourceDownloadURLArgs(%+v)", *p)

}

func (p *AssistantServiceGetCodeResourceDownloadURLArgs) DeepEqual(ano *AssistantServiceGetCodeResourceDownloadURLArgs) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Req) {
		return false
	}
	return true
}

func (p *AssistantServiceGetCodeResourceDownloadURLArgs) Field1DeepEqual(src *GetCodeResourceDownloadURLRequest) bool {

	if !p.Req.DeepEqual(src) {
		return false
	}
	return true
}

type AssistantServiceGetCodeResourceDownloadURLResult struct {
	Success *GetCodeResourceDownloadURLResponse `thrift:"success,0,optional" frugal:"0,optional,GetCodeResourceDownloadURLResponse" json:"success,omitempty"`
}

func NewAssistantServiceGetCodeResourceDownloadURLResult() *AssistantServiceGetCodeResourceDownloadURLResult {
	return &AssistantServiceGetCodeResourceDownloadURLResult{}
}

func (p *AssistantServiceGetCodeResourceDownloadURLResult) InitDefault() {
}

var AssistantServiceGetCodeResourceDownloadURLResult_Success_DEFAULT *GetCodeResourceDownloadURLResponse

func (p *AssistantServiceGetCodeResourceDownloadURLResult) GetSuccess() (v *GetCodeResourceDownloadURLResponse) {
	if !p.IsSetSuccess() {
		return AssistantServiceGetCodeResourceDownloadURLResult_Success_DEFAULT
	}
	return p.Success
}
func (p *AssistantServiceGetCodeResourceDownloadURLResult) SetSuccess(x interface{}) {
	p.Success = x.(*GetCodeResourceDownloadURLResponse)
}

var fieldIDToName_AssistantServiceGetCodeResourceDownloadURLResult = map[int16]string{
	0: "success",
}

func (p *AssistantServiceGetCodeResourceDownloadURLResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *AssistantServiceGetCodeResourceDownloadURLResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AssistantServiceGetCodeResourceDownloadURLResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AssistantServiceGetCodeResourceDownloadURLResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewGetCodeResourceDownloadURLResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *AssistantServiceGetCodeResourceDownloadURLResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetCodeResourceDownloadURL_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssistantServiceGetCodeResourceDownloadURLResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *AssistantServiceGetCodeResourceDownloadURLResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssistantServiceGetCodeResourceDownloadURLResult(%+v)", *p)

}

func (p *AssistantServiceGetCodeResourceDownloadURLResult) DeepEqual(ano *AssistantServiceGetCodeResourceDownloadURLResult) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field0DeepEqual(ano.Success) {
		return false
	}
	return true
}

func (p *AssistantServiceGetCodeResourceDownloadURLResult) Field0DeepEqual(src *GetCodeResourceDownloadURLResponse) bool {

	if !p.Success.DeepEqual(src) {
		return false
	}
	return true
}

type AssistantServiceDesensitizeContextArgs struct {
	Req *DesensitizeContextRequest `thrift:"req,1" frugal:"1,default,DesensitizeContextRequest" json:"req"`
}

func NewAssistantServiceDesensitizeContextArgs() *AssistantServiceDesensitizeContextArgs {
	return &AssistantServiceDesensitizeContextArgs{}
}

func (p *AssistantServiceDesensitizeContextArgs) InitDefault() {
}

var AssistantServiceDesensitizeContextArgs_Req_DEFAULT *DesensitizeContextRequest

func (p *AssistantServiceDesensitizeContextArgs) GetReq() (v *DesensitizeContextRequest) {
	if !p.IsSetReq() {
		return AssistantServiceDesensitizeContextArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *AssistantServiceDesensitizeContextArgs) SetReq(val *DesensitizeContextRequest) {
	p.Req = val
}

var fieldIDToName_AssistantServiceDesensitizeContextArgs = map[int16]string{
	1: "req",
}

func (p *AssistantServiceDesensitizeContextArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *AssistantServiceDesensitizeContextArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AssistantServiceDesensitizeContextArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AssistantServiceDesensitizeContextArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewDesensitizeContextRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *AssistantServiceDesensitizeContextArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("DesensitizeContext_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssistantServiceDesensitizeContextArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *AssistantServiceDesensitizeContextArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssistantServiceDesensitizeContextArgs(%+v)", *p)

}

func (p *AssistantServiceDesensitizeContextArgs) DeepEqual(ano *AssistantServiceDesensitizeContextArgs) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Req) {
		return false
	}
	return true
}

func (p *AssistantServiceDesensitizeContextArgs) Field1DeepEqual(src *DesensitizeContextRequest) bool {

	if !p.Req.DeepEqual(src) {
		return false
	}
	return true
}

type AssistantServiceDesensitizeContextResult struct {
	Success *DesensitizeContextResponse `thrift:"success,0,optional" frugal:"0,optional,DesensitizeContextResponse" json:"success,omitempty"`
}

func NewAssistantServiceDesensitizeContextResult() *AssistantServiceDesensitizeContextResult {
	return &AssistantServiceDesensitizeContextResult{}
}

func (p *AssistantServiceDesensitizeContextResult) InitDefault() {
}

var AssistantServiceDesensitizeContextResult_Success_DEFAULT *DesensitizeContextResponse

func (p *AssistantServiceDesensitizeContextResult) GetSuccess() (v *DesensitizeContextResponse) {
	if !p.IsSetSuccess() {
		return AssistantServiceDesensitizeContextResult_Success_DEFAULT
	}
	return p.Success
}
func (p *AssistantServiceDesensitizeContextResult) SetSuccess(x interface{}) {
	p.Success = x.(*DesensitizeContextResponse)
}

var fieldIDToName_AssistantServiceDesensitizeContextResult = map[int16]string{
	0: "success",
}

func (p *AssistantServiceDesensitizeContextResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *AssistantServiceDesensitizeContextResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AssistantServiceDesensitizeContextResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AssistantServiceDesensitizeContextResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewDesensitizeContextResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *AssistantServiceDesensitizeContextResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("DesensitizeContext_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssistantServiceDesensitizeContextResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *AssistantServiceDesensitizeContextResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssistantServiceDesensitizeContextResult(%+v)", *p)

}

func (p *AssistantServiceDesensitizeContextResult) DeepEqual(ano *AssistantServiceDesensitizeContextResult) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field0DeepEqual(ano.Success) {
		return false
	}
	return true
}

func (p *AssistantServiceDesensitizeContextResult) Field0DeepEqual(src *DesensitizeContextResponse) bool {

	if !p.Success.DeepEqual(src) {
		return false
	}
	return true
}

type AssistantServiceBatchCheckFilePathArgs struct {
	Req *BatchCheckFilePathRequest `thrift:"req,1" frugal:"1,default,BatchCheckFilePathRequest" json:"req"`
}

func NewAssistantServiceBatchCheckFilePathArgs() *AssistantServiceBatchCheckFilePathArgs {
	return &AssistantServiceBatchCheckFilePathArgs{}
}

func (p *AssistantServiceBatchCheckFilePathArgs) InitDefault() {
}

var AssistantServiceBatchCheckFilePathArgs_Req_DEFAULT *BatchCheckFilePathRequest

func (p *AssistantServiceBatchCheckFilePathArgs) GetReq() (v *BatchCheckFilePathRequest) {
	if !p.IsSetReq() {
		return AssistantServiceBatchCheckFilePathArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *AssistantServiceBatchCheckFilePathArgs) SetReq(val *BatchCheckFilePathRequest) {
	p.Req = val
}

var fieldIDToName_AssistantServiceBatchCheckFilePathArgs = map[int16]string{
	1: "req",
}

func (p *AssistantServiceBatchCheckFilePathArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *AssistantServiceBatchCheckFilePathArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AssistantServiceBatchCheckFilePathArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AssistantServiceBatchCheckFilePathArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewBatchCheckFilePathRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *AssistantServiceBatchCheckFilePathArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("BatchCheckFilePath_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssistantServiceBatchCheckFilePathArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *AssistantServiceBatchCheckFilePathArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssistantServiceBatchCheckFilePathArgs(%+v)", *p)

}

func (p *AssistantServiceBatchCheckFilePathArgs) DeepEqual(ano *AssistantServiceBatchCheckFilePathArgs) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Req) {
		return false
	}
	return true
}

func (p *AssistantServiceBatchCheckFilePathArgs) Field1DeepEqual(src *BatchCheckFilePathRequest) bool {

	if !p.Req.DeepEqual(src) {
		return false
	}
	return true
}

type AssistantServiceBatchCheckFilePathResult struct {
	Success *BatchCheckFilePathResponse `thrift:"success,0,optional" frugal:"0,optional,BatchCheckFilePathResponse" json:"success,omitempty"`
}

func NewAssistantServiceBatchCheckFilePathResult() *AssistantServiceBatchCheckFilePathResult {
	return &AssistantServiceBatchCheckFilePathResult{}
}

func (p *AssistantServiceBatchCheckFilePathResult) InitDefault() {
}

var AssistantServiceBatchCheckFilePathResult_Success_DEFAULT *BatchCheckFilePathResponse

func (p *AssistantServiceBatchCheckFilePathResult) GetSuccess() (v *BatchCheckFilePathResponse) {
	if !p.IsSetSuccess() {
		return AssistantServiceBatchCheckFilePathResult_Success_DEFAULT
	}
	return p.Success
}
func (p *AssistantServiceBatchCheckFilePathResult) SetSuccess(x interface{}) {
	p.Success = x.(*BatchCheckFilePathResponse)
}

var fieldIDToName_AssistantServiceBatchCheckFilePathResult = map[int16]string{
	0: "success",
}

func (p *AssistantServiceBatchCheckFilePathResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *AssistantServiceBatchCheckFilePathResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AssistantServiceBatchCheckFilePathResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AssistantServiceBatchCheckFilePathResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewBatchCheckFilePathResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *AssistantServiceBatchCheckFilePathResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("BatchCheckFilePath_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssistantServiceBatchCheckFilePathResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *AssistantServiceBatchCheckFilePathResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssistantServiceBatchCheckFilePathResult(%+v)", *p)

}

func (p *AssistantServiceBatchCheckFilePathResult) DeepEqual(ano *AssistantServiceBatchCheckFilePathResult) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field0DeepEqual(ano.Success) {
		return false
	}
	return true
}

func (p *AssistantServiceBatchCheckFilePathResult) Field0DeepEqual(src *BatchCheckFilePathResponse) bool {

	if !p.Success.DeepEqual(src) {
		return false
	}
	return true
}

type AssistantServiceCodeExecutableArgs struct {
	Req *CodeExecutableRequest `thrift:"req,1" frugal:"1,default,CodeExecutableRequest" json:"req"`
}

func NewAssistantServiceCodeExecutableArgs() *AssistantServiceCodeExecutableArgs {
	return &AssistantServiceCodeExecutableArgs{}
}

func (p *AssistantServiceCodeExecutableArgs) InitDefault() {
}

var AssistantServiceCodeExecutableArgs_Req_DEFAULT *CodeExecutableRequest

func (p *AssistantServiceCodeExecutableArgs) GetReq() (v *CodeExecutableRequest) {
	if !p.IsSetReq() {
		return AssistantServiceCodeExecutableArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *AssistantServiceCodeExecutableArgs) SetReq(val *CodeExecutableRequest) {
	p.Req = val
}

var fieldIDToName_AssistantServiceCodeExecutableArgs = map[int16]string{
	1: "req",
}

func (p *AssistantServiceCodeExecutableArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *AssistantServiceCodeExecutableArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AssistantServiceCodeExecutableArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AssistantServiceCodeExecutableArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewCodeExecutableRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *AssistantServiceCodeExecutableArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("CodeExecutable_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssistantServiceCodeExecutableArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *AssistantServiceCodeExecutableArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssistantServiceCodeExecutableArgs(%+v)", *p)

}

func (p *AssistantServiceCodeExecutableArgs) DeepEqual(ano *AssistantServiceCodeExecutableArgs) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Req) {
		return false
	}
	return true
}

func (p *AssistantServiceCodeExecutableArgs) Field1DeepEqual(src *CodeExecutableRequest) bool {

	if !p.Req.DeepEqual(src) {
		return false
	}
	return true
}

type AssistantServiceCodeExecutableResult struct {
	Success *CodeExecutableResponse `thrift:"success,0,optional" frugal:"0,optional,CodeExecutableResponse" json:"success,omitempty"`
}

func NewAssistantServiceCodeExecutableResult() *AssistantServiceCodeExecutableResult {
	return &AssistantServiceCodeExecutableResult{}
}

func (p *AssistantServiceCodeExecutableResult) InitDefault() {
}

var AssistantServiceCodeExecutableResult_Success_DEFAULT *CodeExecutableResponse

func (p *AssistantServiceCodeExecutableResult) GetSuccess() (v *CodeExecutableResponse) {
	if !p.IsSetSuccess() {
		return AssistantServiceCodeExecutableResult_Success_DEFAULT
	}
	return p.Success
}
func (p *AssistantServiceCodeExecutableResult) SetSuccess(x interface{}) {
	p.Success = x.(*CodeExecutableResponse)
}

var fieldIDToName_AssistantServiceCodeExecutableResult = map[int16]string{
	0: "success",
}

func (p *AssistantServiceCodeExecutableResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *AssistantServiceCodeExecutableResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AssistantServiceCodeExecutableResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AssistantServiceCodeExecutableResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewCodeExecutableResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *AssistantServiceCodeExecutableResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("CodeExecutable_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssistantServiceCodeExecutableResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *AssistantServiceCodeExecutableResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssistantServiceCodeExecutableResult(%+v)", *p)

}

func (p *AssistantServiceCodeExecutableResult) DeepEqual(ano *AssistantServiceCodeExecutableResult) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field0DeepEqual(ano.Success) {
		return false
	}
	return true
}

func (p *AssistantServiceCodeExecutableResult) Field0DeepEqual(src *CodeExecutableResponse) bool {

	if !p.Success.DeepEqual(src) {
		return false
	}
	return true
}

type AssistantServiceRunCodeArgs struct {
	Req *RunCodeRequest `thrift:"req,1" frugal:"1,default,RunCodeRequest" json:"req"`
}

func NewAssistantServiceRunCodeArgs() *AssistantServiceRunCodeArgs {
	return &AssistantServiceRunCodeArgs{}
}

func (p *AssistantServiceRunCodeArgs) InitDefault() {
}

var AssistantServiceRunCodeArgs_Req_DEFAULT *RunCodeRequest

func (p *AssistantServiceRunCodeArgs) GetReq() (v *RunCodeRequest) {
	if !p.IsSetReq() {
		return AssistantServiceRunCodeArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *AssistantServiceRunCodeArgs) SetReq(val *RunCodeRequest) {
	p.Req = val
}

var fieldIDToName_AssistantServiceRunCodeArgs = map[int16]string{
	1: "req",
}

func (p *AssistantServiceRunCodeArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *AssistantServiceRunCodeArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AssistantServiceRunCodeArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AssistantServiceRunCodeArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewRunCodeRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *AssistantServiceRunCodeArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("RunCode_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssistantServiceRunCodeArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *AssistantServiceRunCodeArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssistantServiceRunCodeArgs(%+v)", *p)

}

func (p *AssistantServiceRunCodeArgs) DeepEqual(ano *AssistantServiceRunCodeArgs) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Req) {
		return false
	}
	return true
}

func (p *AssistantServiceRunCodeArgs) Field1DeepEqual(src *RunCodeRequest) bool {

	if !p.Req.DeepEqual(src) {
		return false
	}
	return true
}

type AssistantServiceRunCodeResult struct {
	Success *RunCodeResponse `thrift:"success,0,optional" frugal:"0,optional,RunCodeResponse" json:"success,omitempty"`
}

func NewAssistantServiceRunCodeResult() *AssistantServiceRunCodeResult {
	return &AssistantServiceRunCodeResult{}
}

func (p *AssistantServiceRunCodeResult) InitDefault() {
}

var AssistantServiceRunCodeResult_Success_DEFAULT *RunCodeResponse

func (p *AssistantServiceRunCodeResult) GetSuccess() (v *RunCodeResponse) {
	if !p.IsSetSuccess() {
		return AssistantServiceRunCodeResult_Success_DEFAULT
	}
	return p.Success
}
func (p *AssistantServiceRunCodeResult) SetSuccess(x interface{}) {
	p.Success = x.(*RunCodeResponse)
}

var fieldIDToName_AssistantServiceRunCodeResult = map[int16]string{
	0: "success",
}

func (p *AssistantServiceRunCodeResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *AssistantServiceRunCodeResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AssistantServiceRunCodeResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AssistantServiceRunCodeResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewRunCodeResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *AssistantServiceRunCodeResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("RunCode_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssistantServiceRunCodeResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *AssistantServiceRunCodeResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssistantServiceRunCodeResult(%+v)", *p)

}

func (p *AssistantServiceRunCodeResult) DeepEqual(ano *AssistantServiceRunCodeResult) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field0DeepEqual(ano.Success) {
		return false
	}
	return true
}

func (p *AssistantServiceRunCodeResult) Field0DeepEqual(src *RunCodeResponse) bool {

	if !p.Success.DeepEqual(src) {
		return false
	}
	return true
}

type AssistantServiceRunCodeV2Args struct {
	Req *RunCodeRequestV2 `thrift:"req,1" frugal:"1,default,RunCodeRequestV2" json:"req"`
}

func NewAssistantServiceRunCodeV2Args() *AssistantServiceRunCodeV2Args {
	return &AssistantServiceRunCodeV2Args{}
}

func (p *AssistantServiceRunCodeV2Args) InitDefault() {
}

var AssistantServiceRunCodeV2Args_Req_DEFAULT *RunCodeRequestV2

func (p *AssistantServiceRunCodeV2Args) GetReq() (v *RunCodeRequestV2) {
	if !p.IsSetReq() {
		return AssistantServiceRunCodeV2Args_Req_DEFAULT
	}
	return p.Req
}
func (p *AssistantServiceRunCodeV2Args) SetReq(val *RunCodeRequestV2) {
	p.Req = val
}

var fieldIDToName_AssistantServiceRunCodeV2Args = map[int16]string{
	1: "req",
}

func (p *AssistantServiceRunCodeV2Args) IsSetReq() bool {
	return p.Req != nil
}

func (p *AssistantServiceRunCodeV2Args) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AssistantServiceRunCodeV2Args[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AssistantServiceRunCodeV2Args) ReadField1(iprot thrift.TProtocol) error {
	_field := NewRunCodeRequestV2()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *AssistantServiceRunCodeV2Args) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("RunCodeV2_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssistantServiceRunCodeV2Args) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *AssistantServiceRunCodeV2Args) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssistantServiceRunCodeV2Args(%+v)", *p)

}

func (p *AssistantServiceRunCodeV2Args) DeepEqual(ano *AssistantServiceRunCodeV2Args) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Req) {
		return false
	}
	return true
}

func (p *AssistantServiceRunCodeV2Args) Field1DeepEqual(src *RunCodeRequestV2) bool {

	if !p.Req.DeepEqual(src) {
		return false
	}
	return true
}

type AssistantServiceRunCodeV2Result struct {
	Success *RunCodeResponseV2 `thrift:"success,0,optional" frugal:"0,optional,RunCodeResponseV2" json:"success,omitempty"`
}

func NewAssistantServiceRunCodeV2Result() *AssistantServiceRunCodeV2Result {
	return &AssistantServiceRunCodeV2Result{}
}

func (p *AssistantServiceRunCodeV2Result) InitDefault() {
}

var AssistantServiceRunCodeV2Result_Success_DEFAULT *RunCodeResponseV2

func (p *AssistantServiceRunCodeV2Result) GetSuccess() (v *RunCodeResponseV2) {
	if !p.IsSetSuccess() {
		return AssistantServiceRunCodeV2Result_Success_DEFAULT
	}
	return p.Success
}
func (p *AssistantServiceRunCodeV2Result) SetSuccess(x interface{}) {
	p.Success = x.(*RunCodeResponseV2)
}

var fieldIDToName_AssistantServiceRunCodeV2Result = map[int16]string{
	0: "success",
}

func (p *AssistantServiceRunCodeV2Result) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *AssistantServiceRunCodeV2Result) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AssistantServiceRunCodeV2Result[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AssistantServiceRunCodeV2Result) ReadField0(iprot thrift.TProtocol) error {
	_field := NewRunCodeResponseV2()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *AssistantServiceRunCodeV2Result) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("RunCodeV2_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssistantServiceRunCodeV2Result) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *AssistantServiceRunCodeV2Result) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssistantServiceRunCodeV2Result(%+v)", *p)

}

func (p *AssistantServiceRunCodeV2Result) DeepEqual(ano *AssistantServiceRunCodeV2Result) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field0DeepEqual(ano.Success) {
		return false
	}
	return true
}

func (p *AssistantServiceRunCodeV2Result) Field0DeepEqual(src *RunCodeResponseV2) bool {

	if !p.Success.DeepEqual(src) {
		return false
	}
	return true
}

type AssistantServiceGetArtifactTemplateFileArgs struct {
	Req *GetArtifactTemplateFileRequest `thrift:"req,1" frugal:"1,default,GetArtifactTemplateFileRequest" json:"req"`
}

func NewAssistantServiceGetArtifactTemplateFileArgs() *AssistantServiceGetArtifactTemplateFileArgs {
	return &AssistantServiceGetArtifactTemplateFileArgs{}
}

func (p *AssistantServiceGetArtifactTemplateFileArgs) InitDefault() {
}

var AssistantServiceGetArtifactTemplateFileArgs_Req_DEFAULT *GetArtifactTemplateFileRequest

func (p *AssistantServiceGetArtifactTemplateFileArgs) GetReq() (v *GetArtifactTemplateFileRequest) {
	if !p.IsSetReq() {
		return AssistantServiceGetArtifactTemplateFileArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *AssistantServiceGetArtifactTemplateFileArgs) SetReq(val *GetArtifactTemplateFileRequest) {
	p.Req = val
}

var fieldIDToName_AssistantServiceGetArtifactTemplateFileArgs = map[int16]string{
	1: "req",
}

func (p *AssistantServiceGetArtifactTemplateFileArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *AssistantServiceGetArtifactTemplateFileArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AssistantServiceGetArtifactTemplateFileArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AssistantServiceGetArtifactTemplateFileArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewGetArtifactTemplateFileRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *AssistantServiceGetArtifactTemplateFileArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetArtifactTemplateFile_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssistantServiceGetArtifactTemplateFileArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *AssistantServiceGetArtifactTemplateFileArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssistantServiceGetArtifactTemplateFileArgs(%+v)", *p)

}

func (p *AssistantServiceGetArtifactTemplateFileArgs) DeepEqual(ano *AssistantServiceGetArtifactTemplateFileArgs) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Req) {
		return false
	}
	return true
}

func (p *AssistantServiceGetArtifactTemplateFileArgs) Field1DeepEqual(src *GetArtifactTemplateFileRequest) bool {

	if !p.Req.DeepEqual(src) {
		return false
	}
	return true
}

type AssistantServiceGetArtifactTemplateFileResult struct {
	Success *GetArtifactTemplateFileResponse `thrift:"success,0,optional" frugal:"0,optional,GetArtifactTemplateFileResponse" json:"success,omitempty"`
}

func NewAssistantServiceGetArtifactTemplateFileResult() *AssistantServiceGetArtifactTemplateFileResult {
	return &AssistantServiceGetArtifactTemplateFileResult{}
}

func (p *AssistantServiceGetArtifactTemplateFileResult) InitDefault() {
}

var AssistantServiceGetArtifactTemplateFileResult_Success_DEFAULT *GetArtifactTemplateFileResponse

func (p *AssistantServiceGetArtifactTemplateFileResult) GetSuccess() (v *GetArtifactTemplateFileResponse) {
	if !p.IsSetSuccess() {
		return AssistantServiceGetArtifactTemplateFileResult_Success_DEFAULT
	}
	return p.Success
}
func (p *AssistantServiceGetArtifactTemplateFileResult) SetSuccess(x interface{}) {
	p.Success = x.(*GetArtifactTemplateFileResponse)
}

var fieldIDToName_AssistantServiceGetArtifactTemplateFileResult = map[int16]string{
	0: "success",
}

func (p *AssistantServiceGetArtifactTemplateFileResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *AssistantServiceGetArtifactTemplateFileResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AssistantServiceGetArtifactTemplateFileResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AssistantServiceGetArtifactTemplateFileResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewGetArtifactTemplateFileResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *AssistantServiceGetArtifactTemplateFileResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetArtifactTemplateFile_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssistantServiceGetArtifactTemplateFileResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *AssistantServiceGetArtifactTemplateFileResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssistantServiceGetArtifactTemplateFileResult(%+v)", *p)

}

func (p *AssistantServiceGetArtifactTemplateFileResult) DeepEqual(ano *AssistantServiceGetArtifactTemplateFileResult) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field0DeepEqual(ano.Success) {
		return false
	}
	return true
}

func (p *AssistantServiceGetArtifactTemplateFileResult) Field0DeepEqual(src *GetArtifactTemplateFileResponse) bool {

	if !p.Success.DeepEqual(src) {
		return false
	}
	return true
}

type AssistantServiceGetArtifactTemplateDirArgs struct {
	Req *GetArtifactTemplateDirRequest `thrift:"req,1" frugal:"1,default,GetArtifactTemplateDirRequest" json:"req"`
}

func NewAssistantServiceGetArtifactTemplateDirArgs() *AssistantServiceGetArtifactTemplateDirArgs {
	return &AssistantServiceGetArtifactTemplateDirArgs{}
}

func (p *AssistantServiceGetArtifactTemplateDirArgs) InitDefault() {
}

var AssistantServiceGetArtifactTemplateDirArgs_Req_DEFAULT *GetArtifactTemplateDirRequest

func (p *AssistantServiceGetArtifactTemplateDirArgs) GetReq() (v *GetArtifactTemplateDirRequest) {
	if !p.IsSetReq() {
		return AssistantServiceGetArtifactTemplateDirArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *AssistantServiceGetArtifactTemplateDirArgs) SetReq(val *GetArtifactTemplateDirRequest) {
	p.Req = val
}

var fieldIDToName_AssistantServiceGetArtifactTemplateDirArgs = map[int16]string{
	1: "req",
}

func (p *AssistantServiceGetArtifactTemplateDirArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *AssistantServiceGetArtifactTemplateDirArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AssistantServiceGetArtifactTemplateDirArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AssistantServiceGetArtifactTemplateDirArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewGetArtifactTemplateDirRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *AssistantServiceGetArtifactTemplateDirArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetArtifactTemplateDir_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssistantServiceGetArtifactTemplateDirArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *AssistantServiceGetArtifactTemplateDirArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssistantServiceGetArtifactTemplateDirArgs(%+v)", *p)

}

func (p *AssistantServiceGetArtifactTemplateDirArgs) DeepEqual(ano *AssistantServiceGetArtifactTemplateDirArgs) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Req) {
		return false
	}
	return true
}

func (p *AssistantServiceGetArtifactTemplateDirArgs) Field1DeepEqual(src *GetArtifactTemplateDirRequest) bool {

	if !p.Req.DeepEqual(src) {
		return false
	}
	return true
}

type AssistantServiceGetArtifactTemplateDirResult struct {
	Success *GetArtifactTemplateDirResponse `thrift:"success,0,optional" frugal:"0,optional,GetArtifactTemplateDirResponse" json:"success,omitempty"`
}

func NewAssistantServiceGetArtifactTemplateDirResult() *AssistantServiceGetArtifactTemplateDirResult {
	return &AssistantServiceGetArtifactTemplateDirResult{}
}

func (p *AssistantServiceGetArtifactTemplateDirResult) InitDefault() {
}

var AssistantServiceGetArtifactTemplateDirResult_Success_DEFAULT *GetArtifactTemplateDirResponse

func (p *AssistantServiceGetArtifactTemplateDirResult) GetSuccess() (v *GetArtifactTemplateDirResponse) {
	if !p.IsSetSuccess() {
		return AssistantServiceGetArtifactTemplateDirResult_Success_DEFAULT
	}
	return p.Success
}
func (p *AssistantServiceGetArtifactTemplateDirResult) SetSuccess(x interface{}) {
	p.Success = x.(*GetArtifactTemplateDirResponse)
}

var fieldIDToName_AssistantServiceGetArtifactTemplateDirResult = map[int16]string{
	0: "success",
}

func (p *AssistantServiceGetArtifactTemplateDirResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *AssistantServiceGetArtifactTemplateDirResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AssistantServiceGetArtifactTemplateDirResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AssistantServiceGetArtifactTemplateDirResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewGetArtifactTemplateDirResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *AssistantServiceGetArtifactTemplateDirResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetArtifactTemplateDir_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssistantServiceGetArtifactTemplateDirResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *AssistantServiceGetArtifactTemplateDirResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssistantServiceGetArtifactTemplateDirResult(%+v)", *p)

}

func (p *AssistantServiceGetArtifactTemplateDirResult) DeepEqual(ano *AssistantServiceGetArtifactTemplateDirResult) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field0DeepEqual(ano.Success) {
		return false
	}
	return true
}

func (p *AssistantServiceGetArtifactTemplateDirResult) Field0DeepEqual(src *GetArtifactTemplateDirResponse) bool {

	if !p.Success.DeepEqual(src) {
		return false
	}
	return true
}

type AssistantServiceCompileCodeArtifactArgs struct {
	Req *CompileCodeArtifactRequest `thrift:"req,1" frugal:"1,default,CompileCodeArtifactRequest" json:"req"`
}

func NewAssistantServiceCompileCodeArtifactArgs() *AssistantServiceCompileCodeArtifactArgs {
	return &AssistantServiceCompileCodeArtifactArgs{}
}

func (p *AssistantServiceCompileCodeArtifactArgs) InitDefault() {
}

var AssistantServiceCompileCodeArtifactArgs_Req_DEFAULT *CompileCodeArtifactRequest

func (p *AssistantServiceCompileCodeArtifactArgs) GetReq() (v *CompileCodeArtifactRequest) {
	if !p.IsSetReq() {
		return AssistantServiceCompileCodeArtifactArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *AssistantServiceCompileCodeArtifactArgs) SetReq(val *CompileCodeArtifactRequest) {
	p.Req = val
}

var fieldIDToName_AssistantServiceCompileCodeArtifactArgs = map[int16]string{
	1: "req",
}

func (p *AssistantServiceCompileCodeArtifactArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *AssistantServiceCompileCodeArtifactArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AssistantServiceCompileCodeArtifactArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AssistantServiceCompileCodeArtifactArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewCompileCodeArtifactRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *AssistantServiceCompileCodeArtifactArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("CompileCodeArtifact_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssistantServiceCompileCodeArtifactArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *AssistantServiceCompileCodeArtifactArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssistantServiceCompileCodeArtifactArgs(%+v)", *p)

}

func (p *AssistantServiceCompileCodeArtifactArgs) DeepEqual(ano *AssistantServiceCompileCodeArtifactArgs) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Req) {
		return false
	}
	return true
}

func (p *AssistantServiceCompileCodeArtifactArgs) Field1DeepEqual(src *CompileCodeArtifactRequest) bool {

	if !p.Req.DeepEqual(src) {
		return false
	}
	return true
}

type AssistantServiceCompileCodeArtifactResult struct {
	Success *CompileCodeArtifactResponse `thrift:"success,0,optional" frugal:"0,optional,CompileCodeArtifactResponse" json:"success,omitempty"`
}

func NewAssistantServiceCompileCodeArtifactResult() *AssistantServiceCompileCodeArtifactResult {
	return &AssistantServiceCompileCodeArtifactResult{}
}

func (p *AssistantServiceCompileCodeArtifactResult) InitDefault() {
}

var AssistantServiceCompileCodeArtifactResult_Success_DEFAULT *CompileCodeArtifactResponse

func (p *AssistantServiceCompileCodeArtifactResult) GetSuccess() (v *CompileCodeArtifactResponse) {
	if !p.IsSetSuccess() {
		return AssistantServiceCompileCodeArtifactResult_Success_DEFAULT
	}
	return p.Success
}
func (p *AssistantServiceCompileCodeArtifactResult) SetSuccess(x interface{}) {
	p.Success = x.(*CompileCodeArtifactResponse)
}

var fieldIDToName_AssistantServiceCompileCodeArtifactResult = map[int16]string{
	0: "success",
}

func (p *AssistantServiceCompileCodeArtifactResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *AssistantServiceCompileCodeArtifactResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AssistantServiceCompileCodeArtifactResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AssistantServiceCompileCodeArtifactResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewCompileCodeArtifactResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *AssistantServiceCompileCodeArtifactResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("CompileCodeArtifact_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssistantServiceCompileCodeArtifactResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *AssistantServiceCompileCodeArtifactResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssistantServiceCompileCodeArtifactResult(%+v)", *p)

}

func (p *AssistantServiceCompileCodeArtifactResult) DeepEqual(ano *AssistantServiceCompileCodeArtifactResult) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field0DeepEqual(ano.Success) {
		return false
	}
	return true
}

func (p *AssistantServiceCompileCodeArtifactResult) Field0DeepEqual(src *CompileCodeArtifactResponse) bool {

	if !p.Success.DeepEqual(src) {
		return false
	}
	return true
}

type AssistantServiceGetCompileStatusArgs struct {
	Req *GetCompileStatusRequest `thrift:"req,1" frugal:"1,default,GetCompileStatusRequest" json:"req"`
}

func NewAssistantServiceGetCompileStatusArgs() *AssistantServiceGetCompileStatusArgs {
	return &AssistantServiceGetCompileStatusArgs{}
}

func (p *AssistantServiceGetCompileStatusArgs) InitDefault() {
}

var AssistantServiceGetCompileStatusArgs_Req_DEFAULT *GetCompileStatusRequest

func (p *AssistantServiceGetCompileStatusArgs) GetReq() (v *GetCompileStatusRequest) {
	if !p.IsSetReq() {
		return AssistantServiceGetCompileStatusArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *AssistantServiceGetCompileStatusArgs) SetReq(val *GetCompileStatusRequest) {
	p.Req = val
}

var fieldIDToName_AssistantServiceGetCompileStatusArgs = map[int16]string{
	1: "req",
}

func (p *AssistantServiceGetCompileStatusArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *AssistantServiceGetCompileStatusArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AssistantServiceGetCompileStatusArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AssistantServiceGetCompileStatusArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewGetCompileStatusRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *AssistantServiceGetCompileStatusArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetCompileStatus_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssistantServiceGetCompileStatusArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *AssistantServiceGetCompileStatusArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssistantServiceGetCompileStatusArgs(%+v)", *p)

}

func (p *AssistantServiceGetCompileStatusArgs) DeepEqual(ano *AssistantServiceGetCompileStatusArgs) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Req) {
		return false
	}
	return true
}

func (p *AssistantServiceGetCompileStatusArgs) Field1DeepEqual(src *GetCompileStatusRequest) bool {

	if !p.Req.DeepEqual(src) {
		return false
	}
	return true
}

type AssistantServiceGetCompileStatusResult struct {
	Success *GetCompileStatusResponse `thrift:"success,0,optional" frugal:"0,optional,GetCompileStatusResponse" json:"success,omitempty"`
}

func NewAssistantServiceGetCompileStatusResult() *AssistantServiceGetCompileStatusResult {
	return &AssistantServiceGetCompileStatusResult{}
}

func (p *AssistantServiceGetCompileStatusResult) InitDefault() {
}

var AssistantServiceGetCompileStatusResult_Success_DEFAULT *GetCompileStatusResponse

func (p *AssistantServiceGetCompileStatusResult) GetSuccess() (v *GetCompileStatusResponse) {
	if !p.IsSetSuccess() {
		return AssistantServiceGetCompileStatusResult_Success_DEFAULT
	}
	return p.Success
}
func (p *AssistantServiceGetCompileStatusResult) SetSuccess(x interface{}) {
	p.Success = x.(*GetCompileStatusResponse)
}

var fieldIDToName_AssistantServiceGetCompileStatusResult = map[int16]string{
	0: "success",
}

func (p *AssistantServiceGetCompileStatusResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *AssistantServiceGetCompileStatusResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AssistantServiceGetCompileStatusResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AssistantServiceGetCompileStatusResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewGetCompileStatusResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *AssistantServiceGetCompileStatusResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetCompileStatus_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssistantServiceGetCompileStatusResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *AssistantServiceGetCompileStatusResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssistantServiceGetCompileStatusResult(%+v)", *p)

}

func (p *AssistantServiceGetCompileStatusResult) DeepEqual(ano *AssistantServiceGetCompileStatusResult) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field0DeepEqual(ano.Success) {
		return false
	}
	return true
}

func (p *AssistantServiceGetCompileStatusResult) Field0DeepEqual(src *GetCompileStatusResponse) bool {

	if !p.Success.DeepEqual(src) {
		return false
	}
	return true
}

type AssistantServiceGetArtifactsCodeURIArgs struct {
	Req *GetArtifactsCodeURIRequest `thrift:"req,1" frugal:"1,default,GetArtifactsCodeURIRequest" json:"req"`
}

func NewAssistantServiceGetArtifactsCodeURIArgs() *AssistantServiceGetArtifactsCodeURIArgs {
	return &AssistantServiceGetArtifactsCodeURIArgs{}
}

func (p *AssistantServiceGetArtifactsCodeURIArgs) InitDefault() {
}

var AssistantServiceGetArtifactsCodeURIArgs_Req_DEFAULT *GetArtifactsCodeURIRequest

func (p *AssistantServiceGetArtifactsCodeURIArgs) GetReq() (v *GetArtifactsCodeURIRequest) {
	if !p.IsSetReq() {
		return AssistantServiceGetArtifactsCodeURIArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *AssistantServiceGetArtifactsCodeURIArgs) SetReq(val *GetArtifactsCodeURIRequest) {
	p.Req = val
}

var fieldIDToName_AssistantServiceGetArtifactsCodeURIArgs = map[int16]string{
	1: "req",
}

func (p *AssistantServiceGetArtifactsCodeURIArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *AssistantServiceGetArtifactsCodeURIArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AssistantServiceGetArtifactsCodeURIArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AssistantServiceGetArtifactsCodeURIArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewGetArtifactsCodeURIRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *AssistantServiceGetArtifactsCodeURIArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetArtifactsCodeURI_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssistantServiceGetArtifactsCodeURIArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *AssistantServiceGetArtifactsCodeURIArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssistantServiceGetArtifactsCodeURIArgs(%+v)", *p)

}

func (p *AssistantServiceGetArtifactsCodeURIArgs) DeepEqual(ano *AssistantServiceGetArtifactsCodeURIArgs) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Req) {
		return false
	}
	return true
}

func (p *AssistantServiceGetArtifactsCodeURIArgs) Field1DeepEqual(src *GetArtifactsCodeURIRequest) bool {

	if !p.Req.DeepEqual(src) {
		return false
	}
	return true
}

type AssistantServiceGetArtifactsCodeURIResult struct {
	Success *GetArtifactsCodeURIResponse `thrift:"success,0,optional" frugal:"0,optional,GetArtifactsCodeURIResponse" json:"success,omitempty"`
}

func NewAssistantServiceGetArtifactsCodeURIResult() *AssistantServiceGetArtifactsCodeURIResult {
	return &AssistantServiceGetArtifactsCodeURIResult{}
}

func (p *AssistantServiceGetArtifactsCodeURIResult) InitDefault() {
}

var AssistantServiceGetArtifactsCodeURIResult_Success_DEFAULT *GetArtifactsCodeURIResponse

func (p *AssistantServiceGetArtifactsCodeURIResult) GetSuccess() (v *GetArtifactsCodeURIResponse) {
	if !p.IsSetSuccess() {
		return AssistantServiceGetArtifactsCodeURIResult_Success_DEFAULT
	}
	return p.Success
}
func (p *AssistantServiceGetArtifactsCodeURIResult) SetSuccess(x interface{}) {
	p.Success = x.(*GetArtifactsCodeURIResponse)
}

var fieldIDToName_AssistantServiceGetArtifactsCodeURIResult = map[int16]string{
	0: "success",
}

func (p *AssistantServiceGetArtifactsCodeURIResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *AssistantServiceGetArtifactsCodeURIResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AssistantServiceGetArtifactsCodeURIResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AssistantServiceGetArtifactsCodeURIResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewGetArtifactsCodeURIResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *AssistantServiceGetArtifactsCodeURIResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetArtifactsCodeURI_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssistantServiceGetArtifactsCodeURIResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *AssistantServiceGetArtifactsCodeURIResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssistantServiceGetArtifactsCodeURIResult(%+v)", *p)

}

func (p *AssistantServiceGetArtifactsCodeURIResult) DeepEqual(ano *AssistantServiceGetArtifactsCodeURIResult) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field0DeepEqual(ano.Success) {
		return false
	}
	return true
}

func (p *AssistantServiceGetArtifactsCodeURIResult) Field0DeepEqual(src *GetArtifactsCodeURIResponse) bool {

	if !p.Success.DeepEqual(src) {
		return false
	}
	return true
}

type AssistantServiceCreateSandboxArgs struct {
	Req *CreateSandboxRequest `thrift:"req,1" frugal:"1,default,CreateSandboxRequest" json:"req"`
}

func NewAssistantServiceCreateSandboxArgs() *AssistantServiceCreateSandboxArgs {
	return &AssistantServiceCreateSandboxArgs{}
}

func (p *AssistantServiceCreateSandboxArgs) InitDefault() {
}

var AssistantServiceCreateSandboxArgs_Req_DEFAULT *CreateSandboxRequest

func (p *AssistantServiceCreateSandboxArgs) GetReq() (v *CreateSandboxRequest) {
	if !p.IsSetReq() {
		return AssistantServiceCreateSandboxArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *AssistantServiceCreateSandboxArgs) SetReq(val *CreateSandboxRequest) {
	p.Req = val
}

var fieldIDToName_AssistantServiceCreateSandboxArgs = map[int16]string{
	1: "req",
}

func (p *AssistantServiceCreateSandboxArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *AssistantServiceCreateSandboxArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AssistantServiceCreateSandboxArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AssistantServiceCreateSandboxArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewCreateSandboxRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *AssistantServiceCreateSandboxArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("CreateSandbox_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssistantServiceCreateSandboxArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *AssistantServiceCreateSandboxArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssistantServiceCreateSandboxArgs(%+v)", *p)

}

func (p *AssistantServiceCreateSandboxArgs) DeepEqual(ano *AssistantServiceCreateSandboxArgs) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Req) {
		return false
	}
	return true
}

func (p *AssistantServiceCreateSandboxArgs) Field1DeepEqual(src *CreateSandboxRequest) bool {

	if !p.Req.DeepEqual(src) {
		return false
	}
	return true
}

type AssistantServiceCreateSandboxResult struct {
	Success *CreateSandboxResponse `thrift:"success,0,optional" frugal:"0,optional,CreateSandboxResponse" json:"success,omitempty"`
}

func NewAssistantServiceCreateSandboxResult() *AssistantServiceCreateSandboxResult {
	return &AssistantServiceCreateSandboxResult{}
}

func (p *AssistantServiceCreateSandboxResult) InitDefault() {
}

var AssistantServiceCreateSandboxResult_Success_DEFAULT *CreateSandboxResponse

func (p *AssistantServiceCreateSandboxResult) GetSuccess() (v *CreateSandboxResponse) {
	if !p.IsSetSuccess() {
		return AssistantServiceCreateSandboxResult_Success_DEFAULT
	}
	return p.Success
}
func (p *AssistantServiceCreateSandboxResult) SetSuccess(x interface{}) {
	p.Success = x.(*CreateSandboxResponse)
}

var fieldIDToName_AssistantServiceCreateSandboxResult = map[int16]string{
	0: "success",
}

func (p *AssistantServiceCreateSandboxResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *AssistantServiceCreateSandboxResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AssistantServiceCreateSandboxResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AssistantServiceCreateSandboxResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewCreateSandboxResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *AssistantServiceCreateSandboxResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("CreateSandbox_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssistantServiceCreateSandboxResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *AssistantServiceCreateSandboxResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssistantServiceCreateSandboxResult(%+v)", *p)

}

func (p *AssistantServiceCreateSandboxResult) DeepEqual(ano *AssistantServiceCreateSandboxResult) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field0DeepEqual(ano.Success) {
		return false
	}
	return true
}

func (p *AssistantServiceCreateSandboxResult) Field0DeepEqual(src *CreateSandboxResponse) bool {

	if !p.Success.DeepEqual(src) {
		return false
	}
	return true
}

type AssistantServiceReleaseSandboxArgs struct {
	Req *ReleaseSandboxRequest `thrift:"req,1" frugal:"1,default,ReleaseSandboxRequest" json:"req"`
}

func NewAssistantServiceReleaseSandboxArgs() *AssistantServiceReleaseSandboxArgs {
	return &AssistantServiceReleaseSandboxArgs{}
}

func (p *AssistantServiceReleaseSandboxArgs) InitDefault() {
}

var AssistantServiceReleaseSandboxArgs_Req_DEFAULT *ReleaseSandboxRequest

func (p *AssistantServiceReleaseSandboxArgs) GetReq() (v *ReleaseSandboxRequest) {
	if !p.IsSetReq() {
		return AssistantServiceReleaseSandboxArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *AssistantServiceReleaseSandboxArgs) SetReq(val *ReleaseSandboxRequest) {
	p.Req = val
}

var fieldIDToName_AssistantServiceReleaseSandboxArgs = map[int16]string{
	1: "req",
}

func (p *AssistantServiceReleaseSandboxArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *AssistantServiceReleaseSandboxArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AssistantServiceReleaseSandboxArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AssistantServiceReleaseSandboxArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewReleaseSandboxRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *AssistantServiceReleaseSandboxArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ReleaseSandbox_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssistantServiceReleaseSandboxArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *AssistantServiceReleaseSandboxArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssistantServiceReleaseSandboxArgs(%+v)", *p)

}

func (p *AssistantServiceReleaseSandboxArgs) DeepEqual(ano *AssistantServiceReleaseSandboxArgs) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Req) {
		return false
	}
	return true
}

func (p *AssistantServiceReleaseSandboxArgs) Field1DeepEqual(src *ReleaseSandboxRequest) bool {

	if !p.Req.DeepEqual(src) {
		return false
	}
	return true
}

type AssistantServiceReleaseSandboxResult struct {
	Success *ReleaseSandboxResponse `thrift:"success,0,optional" frugal:"0,optional,ReleaseSandboxResponse" json:"success,omitempty"`
}

func NewAssistantServiceReleaseSandboxResult() *AssistantServiceReleaseSandboxResult {
	return &AssistantServiceReleaseSandboxResult{}
}

func (p *AssistantServiceReleaseSandboxResult) InitDefault() {
}

var AssistantServiceReleaseSandboxResult_Success_DEFAULT *ReleaseSandboxResponse

func (p *AssistantServiceReleaseSandboxResult) GetSuccess() (v *ReleaseSandboxResponse) {
	if !p.IsSetSuccess() {
		return AssistantServiceReleaseSandboxResult_Success_DEFAULT
	}
	return p.Success
}
func (p *AssistantServiceReleaseSandboxResult) SetSuccess(x interface{}) {
	p.Success = x.(*ReleaseSandboxResponse)
}

var fieldIDToName_AssistantServiceReleaseSandboxResult = map[int16]string{
	0: "success",
}

func (p *AssistantServiceReleaseSandboxResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *AssistantServiceReleaseSandboxResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AssistantServiceReleaseSandboxResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AssistantServiceReleaseSandboxResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewReleaseSandboxResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *AssistantServiceReleaseSandboxResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ReleaseSandbox_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssistantServiceReleaseSandboxResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *AssistantServiceReleaseSandboxResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssistantServiceReleaseSandboxResult(%+v)", *p)

}

func (p *AssistantServiceReleaseSandboxResult) DeepEqual(ano *AssistantServiceReleaseSandboxResult) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field0DeepEqual(ano.Success) {
		return false
	}
	return true
}

func (p *AssistantServiceReleaseSandboxResult) Field0DeepEqual(src *ReleaseSandboxResponse) bool {

	if !p.Success.DeepEqual(src) {
		return false
	}
	return true
}

type AssistantServiceExecuteCodeArgs struct {
	Req *ExecuteCodeRequest `thrift:"req,1" frugal:"1,default,ExecuteCodeRequest" json:"req"`
}

func NewAssistantServiceExecuteCodeArgs() *AssistantServiceExecuteCodeArgs {
	return &AssistantServiceExecuteCodeArgs{}
}

func (p *AssistantServiceExecuteCodeArgs) InitDefault() {
}

var AssistantServiceExecuteCodeArgs_Req_DEFAULT *ExecuteCodeRequest

func (p *AssistantServiceExecuteCodeArgs) GetReq() (v *ExecuteCodeRequest) {
	if !p.IsSetReq() {
		return AssistantServiceExecuteCodeArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *AssistantServiceExecuteCodeArgs) SetReq(val *ExecuteCodeRequest) {
	p.Req = val
}

var fieldIDToName_AssistantServiceExecuteCodeArgs = map[int16]string{
	1: "req",
}

func (p *AssistantServiceExecuteCodeArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *AssistantServiceExecuteCodeArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AssistantServiceExecuteCodeArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AssistantServiceExecuteCodeArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewExecuteCodeRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *AssistantServiceExecuteCodeArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ExecuteCode_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssistantServiceExecuteCodeArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *AssistantServiceExecuteCodeArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssistantServiceExecuteCodeArgs(%+v)", *p)

}

func (p *AssistantServiceExecuteCodeArgs) DeepEqual(ano *AssistantServiceExecuteCodeArgs) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Req) {
		return false
	}
	return true
}

func (p *AssistantServiceExecuteCodeArgs) Field1DeepEqual(src *ExecuteCodeRequest) bool {

	if !p.Req.DeepEqual(src) {
		return false
	}
	return true
}

type AssistantServiceExecuteCodeResult struct {
	Success *ExecuteCodeResponse `thrift:"success,0,optional" frugal:"0,optional,ExecuteCodeResponse" json:"success,omitempty"`
}

func NewAssistantServiceExecuteCodeResult() *AssistantServiceExecuteCodeResult {
	return &AssistantServiceExecuteCodeResult{}
}

func (p *AssistantServiceExecuteCodeResult) InitDefault() {
}

var AssistantServiceExecuteCodeResult_Success_DEFAULT *ExecuteCodeResponse

func (p *AssistantServiceExecuteCodeResult) GetSuccess() (v *ExecuteCodeResponse) {
	if !p.IsSetSuccess() {
		return AssistantServiceExecuteCodeResult_Success_DEFAULT
	}
	return p.Success
}
func (p *AssistantServiceExecuteCodeResult) SetSuccess(x interface{}) {
	p.Success = x.(*ExecuteCodeResponse)
}

var fieldIDToName_AssistantServiceExecuteCodeResult = map[int16]string{
	0: "success",
}

func (p *AssistantServiceExecuteCodeResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *AssistantServiceExecuteCodeResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AssistantServiceExecuteCodeResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AssistantServiceExecuteCodeResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewExecuteCodeResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *AssistantServiceExecuteCodeResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ExecuteCode_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssistantServiceExecuteCodeResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *AssistantServiceExecuteCodeResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssistantServiceExecuteCodeResult(%+v)", *p)

}

func (p *AssistantServiceExecuteCodeResult) DeepEqual(ano *AssistantServiceExecuteCodeResult) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field0DeepEqual(ano.Success) {
		return false
	}
	return true
}

func (p *AssistantServiceExecuteCodeResult) Field0DeepEqual(src *ExecuteCodeResponse) bool {

	if !p.Success.DeepEqual(src) {
		return false
	}
	return true
}

type AssistantServiceUploadFileArgs struct {
	Req *UploadFileRequest `thrift:"req,1" frugal:"1,default,UploadFileRequest" json:"req"`
}

func NewAssistantServiceUploadFileArgs() *AssistantServiceUploadFileArgs {
	return &AssistantServiceUploadFileArgs{}
}

func (p *AssistantServiceUploadFileArgs) InitDefault() {
}

var AssistantServiceUploadFileArgs_Req_DEFAULT *UploadFileRequest

func (p *AssistantServiceUploadFileArgs) GetReq() (v *UploadFileRequest) {
	if !p.IsSetReq() {
		return AssistantServiceUploadFileArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *AssistantServiceUploadFileArgs) SetReq(val *UploadFileRequest) {
	p.Req = val
}

var fieldIDToName_AssistantServiceUploadFileArgs = map[int16]string{
	1: "req",
}

func (p *AssistantServiceUploadFileArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *AssistantServiceUploadFileArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AssistantServiceUploadFileArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AssistantServiceUploadFileArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewUploadFileRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *AssistantServiceUploadFileArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("UploadFile_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssistantServiceUploadFileArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *AssistantServiceUploadFileArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssistantServiceUploadFileArgs(%+v)", *p)

}

func (p *AssistantServiceUploadFileArgs) DeepEqual(ano *AssistantServiceUploadFileArgs) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Req) {
		return false
	}
	return true
}

func (p *AssistantServiceUploadFileArgs) Field1DeepEqual(src *UploadFileRequest) bool {

	if !p.Req.DeepEqual(src) {
		return false
	}
	return true
}

type AssistantServiceUploadFileResult struct {
	Success *UploadFileResponse `thrift:"success,0,optional" frugal:"0,optional,UploadFileResponse" json:"success,omitempty"`
}

func NewAssistantServiceUploadFileResult() *AssistantServiceUploadFileResult {
	return &AssistantServiceUploadFileResult{}
}

func (p *AssistantServiceUploadFileResult) InitDefault() {
}

var AssistantServiceUploadFileResult_Success_DEFAULT *UploadFileResponse

func (p *AssistantServiceUploadFileResult) GetSuccess() (v *UploadFileResponse) {
	if !p.IsSetSuccess() {
		return AssistantServiceUploadFileResult_Success_DEFAULT
	}
	return p.Success
}
func (p *AssistantServiceUploadFileResult) SetSuccess(x interface{}) {
	p.Success = x.(*UploadFileResponse)
}

var fieldIDToName_AssistantServiceUploadFileResult = map[int16]string{
	0: "success",
}

func (p *AssistantServiceUploadFileResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *AssistantServiceUploadFileResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AssistantServiceUploadFileResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AssistantServiceUploadFileResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewUploadFileResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *AssistantServiceUploadFileResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("UploadFile_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssistantServiceUploadFileResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *AssistantServiceUploadFileResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssistantServiceUploadFileResult(%+v)", *p)

}

func (p *AssistantServiceUploadFileResult) DeepEqual(ano *AssistantServiceUploadFileResult) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field0DeepEqual(ano.Success) {
		return false
	}
	return true
}

func (p *AssistantServiceUploadFileResult) Field0DeepEqual(src *UploadFileResponse) bool {

	if !p.Success.DeepEqual(src) {
		return false
	}
	return true
}

type AssistantServiceDownloadFileArgs struct {
	Req *DownloadFileRequest `thrift:"req,1" frugal:"1,default,DownloadFileRequest" json:"req"`
}

func NewAssistantServiceDownloadFileArgs() *AssistantServiceDownloadFileArgs {
	return &AssistantServiceDownloadFileArgs{}
}

func (p *AssistantServiceDownloadFileArgs) InitDefault() {
}

var AssistantServiceDownloadFileArgs_Req_DEFAULT *DownloadFileRequest

func (p *AssistantServiceDownloadFileArgs) GetReq() (v *DownloadFileRequest) {
	if !p.IsSetReq() {
		return AssistantServiceDownloadFileArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *AssistantServiceDownloadFileArgs) SetReq(val *DownloadFileRequest) {
	p.Req = val
}

var fieldIDToName_AssistantServiceDownloadFileArgs = map[int16]string{
	1: "req",
}

func (p *AssistantServiceDownloadFileArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *AssistantServiceDownloadFileArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AssistantServiceDownloadFileArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AssistantServiceDownloadFileArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewDownloadFileRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *AssistantServiceDownloadFileArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("DownloadFile_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssistantServiceDownloadFileArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *AssistantServiceDownloadFileArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssistantServiceDownloadFileArgs(%+v)", *p)

}

func (p *AssistantServiceDownloadFileArgs) DeepEqual(ano *AssistantServiceDownloadFileArgs) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Req) {
		return false
	}
	return true
}

func (p *AssistantServiceDownloadFileArgs) Field1DeepEqual(src *DownloadFileRequest) bool {

	if !p.Req.DeepEqual(src) {
		return false
	}
	return true
}

type AssistantServiceDownloadFileResult struct {
	Success *DownloadFileResponse `thrift:"success,0,optional" frugal:"0,optional,DownloadFileResponse" json:"success,omitempty"`
}

func NewAssistantServiceDownloadFileResult() *AssistantServiceDownloadFileResult {
	return &AssistantServiceDownloadFileResult{}
}

func (p *AssistantServiceDownloadFileResult) InitDefault() {
}

var AssistantServiceDownloadFileResult_Success_DEFAULT *DownloadFileResponse

func (p *AssistantServiceDownloadFileResult) GetSuccess() (v *DownloadFileResponse) {
	if !p.IsSetSuccess() {
		return AssistantServiceDownloadFileResult_Success_DEFAULT
	}
	return p.Success
}
func (p *AssistantServiceDownloadFileResult) SetSuccess(x interface{}) {
	p.Success = x.(*DownloadFileResponse)
}

var fieldIDToName_AssistantServiceDownloadFileResult = map[int16]string{
	0: "success",
}

func (p *AssistantServiceDownloadFileResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *AssistantServiceDownloadFileResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AssistantServiceDownloadFileResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AssistantServiceDownloadFileResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewDownloadFileResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *AssistantServiceDownloadFileResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("DownloadFile_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssistantServiceDownloadFileResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *AssistantServiceDownloadFileResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssistantServiceDownloadFileResult(%+v)", *p)

}

func (p *AssistantServiceDownloadFileResult) DeepEqual(ano *AssistantServiceDownloadFileResult) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field0DeepEqual(ano.Success) {
		return false
	}
	return true
}

func (p *AssistantServiceDownloadFileResult) Field0DeepEqual(src *DownloadFileResponse) bool {

	if !p.Success.DeepEqual(src) {
		return false
	}
	return true
}

type AssistantServiceImageReviewArgs struct {
	Req *ImageReviewRequest `thrift:"req,1" frugal:"1,default,ImageReviewRequest" json:"req"`
}

func NewAssistantServiceImageReviewArgs() *AssistantServiceImageReviewArgs {
	return &AssistantServiceImageReviewArgs{}
}

func (p *AssistantServiceImageReviewArgs) InitDefault() {
}

var AssistantServiceImageReviewArgs_Req_DEFAULT *ImageReviewRequest

func (p *AssistantServiceImageReviewArgs) GetReq() (v *ImageReviewRequest) {
	if !p.IsSetReq() {
		return AssistantServiceImageReviewArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *AssistantServiceImageReviewArgs) SetReq(val *ImageReviewRequest) {
	p.Req = val
}

var fieldIDToName_AssistantServiceImageReviewArgs = map[int16]string{
	1: "req",
}

func (p *AssistantServiceImageReviewArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *AssistantServiceImageReviewArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AssistantServiceImageReviewArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AssistantServiceImageReviewArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewImageReviewRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *AssistantServiceImageReviewArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ImageReview_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssistantServiceImageReviewArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *AssistantServiceImageReviewArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssistantServiceImageReviewArgs(%+v)", *p)

}

func (p *AssistantServiceImageReviewArgs) DeepEqual(ano *AssistantServiceImageReviewArgs) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Req) {
		return false
	}
	return true
}

func (p *AssistantServiceImageReviewArgs) Field1DeepEqual(src *ImageReviewRequest) bool {

	if !p.Req.DeepEqual(src) {
		return false
	}
	return true
}

type AssistantServiceImageReviewResult struct {
	Success *ImageReviewResponse `thrift:"success,0,optional" frugal:"0,optional,ImageReviewResponse" json:"success,omitempty"`
}

func NewAssistantServiceImageReviewResult() *AssistantServiceImageReviewResult {
	return &AssistantServiceImageReviewResult{}
}

func (p *AssistantServiceImageReviewResult) InitDefault() {
}

var AssistantServiceImageReviewResult_Success_DEFAULT *ImageReviewResponse

func (p *AssistantServiceImageReviewResult) GetSuccess() (v *ImageReviewResponse) {
	if !p.IsSetSuccess() {
		return AssistantServiceImageReviewResult_Success_DEFAULT
	}
	return p.Success
}
func (p *AssistantServiceImageReviewResult) SetSuccess(x interface{}) {
	p.Success = x.(*ImageReviewResponse)
}

var fieldIDToName_AssistantServiceImageReviewResult = map[int16]string{
	0: "success",
}

func (p *AssistantServiceImageReviewResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *AssistantServiceImageReviewResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AssistantServiceImageReviewResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AssistantServiceImageReviewResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewImageReviewResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *AssistantServiceImageReviewResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ImageReview_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssistantServiceImageReviewResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *AssistantServiceImageReviewResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssistantServiceImageReviewResult(%+v)", *p)

}

func (p *AssistantServiceImageReviewResult) DeepEqual(ano *AssistantServiceImageReviewResult) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field0DeepEqual(ano.Success) {
		return false
	}
	return true
}

func (p *AssistantServiceImageReviewResult) Field0DeepEqual(src *ImageReviewResponse) bool {

	if !p.Success.DeepEqual(src) {
		return false
	}
	return true
}

type AssistantServiceSearchImagesArgs struct {
	Req *SearchImagesRequest `thrift:"req,1" frugal:"1,default,SearchImagesRequest" json:"req"`
}

func NewAssistantServiceSearchImagesArgs() *AssistantServiceSearchImagesArgs {
	return &AssistantServiceSearchImagesArgs{}
}

func (p *AssistantServiceSearchImagesArgs) InitDefault() {
}

var AssistantServiceSearchImagesArgs_Req_DEFAULT *SearchImagesRequest

func (p *AssistantServiceSearchImagesArgs) GetReq() (v *SearchImagesRequest) {
	if !p.IsSetReq() {
		return AssistantServiceSearchImagesArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *AssistantServiceSearchImagesArgs) SetReq(val *SearchImagesRequest) {
	p.Req = val
}

var fieldIDToName_AssistantServiceSearchImagesArgs = map[int16]string{
	1: "req",
}

func (p *AssistantServiceSearchImagesArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *AssistantServiceSearchImagesArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AssistantServiceSearchImagesArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AssistantServiceSearchImagesArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewSearchImagesRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *AssistantServiceSearchImagesArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("SearchImages_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssistantServiceSearchImagesArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *AssistantServiceSearchImagesArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssistantServiceSearchImagesArgs(%+v)", *p)

}

func (p *AssistantServiceSearchImagesArgs) DeepEqual(ano *AssistantServiceSearchImagesArgs) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Req) {
		return false
	}
	return true
}

func (p *AssistantServiceSearchImagesArgs) Field1DeepEqual(src *SearchImagesRequest) bool {

	if !p.Req.DeepEqual(src) {
		return false
	}
	return true
}

type AssistantServiceSearchImagesResult struct {
	Success *SearchImagesResponse `thrift:"success,0,optional" frugal:"0,optional,SearchImagesResponse" json:"success,omitempty"`
}

func NewAssistantServiceSearchImagesResult() *AssistantServiceSearchImagesResult {
	return &AssistantServiceSearchImagesResult{}
}

func (p *AssistantServiceSearchImagesResult) InitDefault() {
}

var AssistantServiceSearchImagesResult_Success_DEFAULT *SearchImagesResponse

func (p *AssistantServiceSearchImagesResult) GetSuccess() (v *SearchImagesResponse) {
	if !p.IsSetSuccess() {
		return AssistantServiceSearchImagesResult_Success_DEFAULT
	}
	return p.Success
}
func (p *AssistantServiceSearchImagesResult) SetSuccess(x interface{}) {
	p.Success = x.(*SearchImagesResponse)
}

var fieldIDToName_AssistantServiceSearchImagesResult = map[int16]string{
	0: "success",
}

func (p *AssistantServiceSearchImagesResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *AssistantServiceSearchImagesResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AssistantServiceSearchImagesResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AssistantServiceSearchImagesResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewSearchImagesResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *AssistantServiceSearchImagesResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("SearchImages_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssistantServiceSearchImagesResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *AssistantServiceSearchImagesResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssistantServiceSearchImagesResult(%+v)", *p)

}

func (p *AssistantServiceSearchImagesResult) DeepEqual(ano *AssistantServiceSearchImagesResult) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field0DeepEqual(ano.Success) {
		return false
	}
	return true
}

func (p *AssistantServiceSearchImagesResult) Field0DeepEqual(src *SearchImagesResponse) bool {

	if !p.Success.DeepEqual(src) {
		return false
	}
	return true
}

type AssistantServiceInterruptCodeAgentTaskArgs struct {
	Req *InterruptCodeAgentTaskRequest `thrift:"req,1" frugal:"1,default,InterruptCodeAgentTaskRequest" json:"req"`
}

func NewAssistantServiceInterruptCodeAgentTaskArgs() *AssistantServiceInterruptCodeAgentTaskArgs {
	return &AssistantServiceInterruptCodeAgentTaskArgs{}
}

func (p *AssistantServiceInterruptCodeAgentTaskArgs) InitDefault() {
}

var AssistantServiceInterruptCodeAgentTaskArgs_Req_DEFAULT *InterruptCodeAgentTaskRequest

func (p *AssistantServiceInterruptCodeAgentTaskArgs) GetReq() (v *InterruptCodeAgentTaskRequest) {
	if !p.IsSetReq() {
		return AssistantServiceInterruptCodeAgentTaskArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *AssistantServiceInterruptCodeAgentTaskArgs) SetReq(val *InterruptCodeAgentTaskRequest) {
	p.Req = val
}

var fieldIDToName_AssistantServiceInterruptCodeAgentTaskArgs = map[int16]string{
	1: "req",
}

func (p *AssistantServiceInterruptCodeAgentTaskArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *AssistantServiceInterruptCodeAgentTaskArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AssistantServiceInterruptCodeAgentTaskArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AssistantServiceInterruptCodeAgentTaskArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewInterruptCodeAgentTaskRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *AssistantServiceInterruptCodeAgentTaskArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("InterruptCodeAgentTask_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssistantServiceInterruptCodeAgentTaskArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *AssistantServiceInterruptCodeAgentTaskArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssistantServiceInterruptCodeAgentTaskArgs(%+v)", *p)

}

func (p *AssistantServiceInterruptCodeAgentTaskArgs) DeepEqual(ano *AssistantServiceInterruptCodeAgentTaskArgs) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Req) {
		return false
	}
	return true
}

func (p *AssistantServiceInterruptCodeAgentTaskArgs) Field1DeepEqual(src *InterruptCodeAgentTaskRequest) bool {

	if !p.Req.DeepEqual(src) {
		return false
	}
	return true
}

type AssistantServiceInterruptCodeAgentTaskResult struct {
	Success *InterruptCodeAgentTaskResponse `thrift:"success,0,optional" frugal:"0,optional,InterruptCodeAgentTaskResponse" json:"success,omitempty"`
}

func NewAssistantServiceInterruptCodeAgentTaskResult() *AssistantServiceInterruptCodeAgentTaskResult {
	return &AssistantServiceInterruptCodeAgentTaskResult{}
}

func (p *AssistantServiceInterruptCodeAgentTaskResult) InitDefault() {
}

var AssistantServiceInterruptCodeAgentTaskResult_Success_DEFAULT *InterruptCodeAgentTaskResponse

func (p *AssistantServiceInterruptCodeAgentTaskResult) GetSuccess() (v *InterruptCodeAgentTaskResponse) {
	if !p.IsSetSuccess() {
		return AssistantServiceInterruptCodeAgentTaskResult_Success_DEFAULT
	}
	return p.Success
}
func (p *AssistantServiceInterruptCodeAgentTaskResult) SetSuccess(x interface{}) {
	p.Success = x.(*InterruptCodeAgentTaskResponse)
}

var fieldIDToName_AssistantServiceInterruptCodeAgentTaskResult = map[int16]string{
	0: "success",
}

func (p *AssistantServiceInterruptCodeAgentTaskResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *AssistantServiceInterruptCodeAgentTaskResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AssistantServiceInterruptCodeAgentTaskResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AssistantServiceInterruptCodeAgentTaskResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewInterruptCodeAgentTaskResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *AssistantServiceInterruptCodeAgentTaskResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("InterruptCodeAgentTask_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssistantServiceInterruptCodeAgentTaskResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *AssistantServiceInterruptCodeAgentTaskResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssistantServiceInterruptCodeAgentTaskResult(%+v)", *p)

}

func (p *AssistantServiceInterruptCodeAgentTaskResult) DeepEqual(ano *AssistantServiceInterruptCodeAgentTaskResult) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field0DeepEqual(ano.Success) {
		return false
	}
	return true
}

func (p *AssistantServiceInterruptCodeAgentTaskResult) Field0DeepEqual(src *InterruptCodeAgentTaskResponse) bool {

	if !p.Success.DeepEqual(src) {
		return false
	}
	return true
}

type AssistantServiceExecuteJobCallbackArgs struct {
	Req *ExecuteJobCallbackReq `thrift:"req,1" frugal:"1,default,ExecuteJobCallbackReq" json:"req"`
}

func NewAssistantServiceExecuteJobCallbackArgs() *AssistantServiceExecuteJobCallbackArgs {
	return &AssistantServiceExecuteJobCallbackArgs{}
}

func (p *AssistantServiceExecuteJobCallbackArgs) InitDefault() {
}

var AssistantServiceExecuteJobCallbackArgs_Req_DEFAULT *ExecuteJobCallbackReq

func (p *AssistantServiceExecuteJobCallbackArgs) GetReq() (v *ExecuteJobCallbackReq) {
	if !p.IsSetReq() {
		return AssistantServiceExecuteJobCallbackArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *AssistantServiceExecuteJobCallbackArgs) SetReq(val *ExecuteJobCallbackReq) {
	p.Req = val
}

var fieldIDToName_AssistantServiceExecuteJobCallbackArgs = map[int16]string{
	1: "req",
}

func (p *AssistantServiceExecuteJobCallbackArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *AssistantServiceExecuteJobCallbackArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AssistantServiceExecuteJobCallbackArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AssistantServiceExecuteJobCallbackArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewExecuteJobCallbackReq()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *AssistantServiceExecuteJobCallbackArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ExecuteJobCallback_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssistantServiceExecuteJobCallbackArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *AssistantServiceExecuteJobCallbackArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssistantServiceExecuteJobCallbackArgs(%+v)", *p)

}

func (p *AssistantServiceExecuteJobCallbackArgs) DeepEqual(ano *AssistantServiceExecuteJobCallbackArgs) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Req) {
		return false
	}
	return true
}

func (p *AssistantServiceExecuteJobCallbackArgs) Field1DeepEqual(src *ExecuteJobCallbackReq) bool {

	if !p.Req.DeepEqual(src) {
		return false
	}
	return true
}

type AssistantServiceExecuteJobCallbackResult struct {
	Success *ExecuteJobCallbackResp `thrift:"success,0,optional" frugal:"0,optional,ExecuteJobCallbackResp" json:"success,omitempty"`
}

func NewAssistantServiceExecuteJobCallbackResult() *AssistantServiceExecuteJobCallbackResult {
	return &AssistantServiceExecuteJobCallbackResult{}
}

func (p *AssistantServiceExecuteJobCallbackResult) InitDefault() {
}

var AssistantServiceExecuteJobCallbackResult_Success_DEFAULT *ExecuteJobCallbackResp

func (p *AssistantServiceExecuteJobCallbackResult) GetSuccess() (v *ExecuteJobCallbackResp) {
	if !p.IsSetSuccess() {
		return AssistantServiceExecuteJobCallbackResult_Success_DEFAULT
	}
	return p.Success
}
func (p *AssistantServiceExecuteJobCallbackResult) SetSuccess(x interface{}) {
	p.Success = x.(*ExecuteJobCallbackResp)
}

var fieldIDToName_AssistantServiceExecuteJobCallbackResult = map[int16]string{
	0: "success",
}

func (p *AssistantServiceExecuteJobCallbackResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *AssistantServiceExecuteJobCallbackResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AssistantServiceExecuteJobCallbackResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AssistantServiceExecuteJobCallbackResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewExecuteJobCallbackResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *AssistantServiceExecuteJobCallbackResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ExecuteJobCallback_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AssistantServiceExecuteJobCallbackResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *AssistantServiceExecuteJobCallbackResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssistantServiceExecuteJobCallbackResult(%+v)", *p)

}

func (p *AssistantServiceExecuteJobCallbackResult) DeepEqual(ano *AssistantServiceExecuteJobCallbackResult) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field0DeepEqual(ano.Success) {
		return false
	}
	return true
}

func (p *AssistantServiceExecuteJobCallbackResult) Field0DeepEqual(src *ExecuteJobCallbackResp) bool {

	if !p.Success.DeepEqual(src) {
		return false
	}
	return true
}

type HookServiceFlowHookArgs struct {
	Req *hook.FlowHookRequest `thrift:"req,1" frugal:"1,default,hook.FlowHookRequest" json:"req"`
}

func NewHookServiceFlowHookArgs() *HookServiceFlowHookArgs {
	return &HookServiceFlowHookArgs{}
}

func (p *HookServiceFlowHookArgs) InitDefault() {
}

var HookServiceFlowHookArgs_Req_DEFAULT *hook.FlowHookRequest

func (p *HookServiceFlowHookArgs) GetReq() (v *hook.FlowHookRequest) {
	if !p.IsSetReq() {
		return HookServiceFlowHookArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *HookServiceFlowHookArgs) SetReq(val *hook.FlowHookRequest) {
	p.Req = val
}

var fieldIDToName_HookServiceFlowHookArgs = map[int16]string{
	1: "req",
}

func (p *HookServiceFlowHookArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *HookServiceFlowHookArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_HookServiceFlowHookArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *HookServiceFlowHookArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := hook.NewFlowHookRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *HookServiceFlowHookArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("FlowHook_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *HookServiceFlowHookArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *HookServiceFlowHookArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("HookServiceFlowHookArgs(%+v)", *p)

}

func (p *HookServiceFlowHookArgs) DeepEqual(ano *HookServiceFlowHookArgs) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Req) {
		return false
	}
	return true
}

func (p *HookServiceFlowHookArgs) Field1DeepEqual(src *hook.FlowHookRequest) bool {

	if !p.Req.DeepEqual(src) {
		return false
	}
	return true
}

type HookServiceFlowHookResult struct {
	Success *hook.FlowHookResponse `thrift:"success,0,optional" frugal:"0,optional,hook.FlowHookResponse" json:"success,omitempty"`
}

func NewHookServiceFlowHookResult() *HookServiceFlowHookResult {
	return &HookServiceFlowHookResult{}
}

func (p *HookServiceFlowHookResult) InitDefault() {
}

var HookServiceFlowHookResult_Success_DEFAULT *hook.FlowHookResponse

func (p *HookServiceFlowHookResult) GetSuccess() (v *hook.FlowHookResponse) {
	if !p.IsSetSuccess() {
		return HookServiceFlowHookResult_Success_DEFAULT
	}
	return p.Success
}
func (p *HookServiceFlowHookResult) SetSuccess(x interface{}) {
	p.Success = x.(*hook.FlowHookResponse)
}

var fieldIDToName_HookServiceFlowHookResult = map[int16]string{
	0: "success",
}

func (p *HookServiceFlowHookResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *HookServiceFlowHookResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_HookServiceFlowHookResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *HookServiceFlowHookResult) ReadField0(iprot thrift.TProtocol) error {
	_field := hook.NewFlowHookResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *HookServiceFlowHookResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("FlowHook_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *HookServiceFlowHookResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *HookServiceFlowHookResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("HookServiceFlowHookResult(%+v)", *p)

}

func (p *HookServiceFlowHookResult) DeepEqual(ano *HookServiceFlowHookResult) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field0DeepEqual(ano.Success) {
		return false
	}
	return true
}

func (p *HookServiceFlowHookResult) Field0DeepEqual(src *hook.FlowHookResponse) bool {

	if !p.Success.DeepEqual(src) {
		return false
	}
	return true
}

type HookServiceFlowStreamArgs struct {
	Req *hook.FlowHookRequest `thrift:"req,1" frugal:"1,default,hook.FlowHookRequest" json:"req"`
}

func NewHookServiceFlowStreamArgs() *HookServiceFlowStreamArgs {
	return &HookServiceFlowStreamArgs{}
}

func (p *HookServiceFlowStreamArgs) InitDefault() {
}

var HookServiceFlowStreamArgs_Req_DEFAULT *hook.FlowHookRequest

func (p *HookServiceFlowStreamArgs) GetReq() (v *hook.FlowHookRequest) {
	if !p.IsSetReq() {
		return HookServiceFlowStreamArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *HookServiceFlowStreamArgs) SetReq(val *hook.FlowHookRequest) {
	p.Req = val
}

var fieldIDToName_HookServiceFlowStreamArgs = map[int16]string{
	1: "req",
}

func (p *HookServiceFlowStreamArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *HookServiceFlowStreamArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_HookServiceFlowStreamArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *HookServiceFlowStreamArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := hook.NewFlowHookRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *HookServiceFlowStreamArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("FlowStream_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *HookServiceFlowStreamArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *HookServiceFlowStreamArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("HookServiceFlowStreamArgs(%+v)", *p)

}

func (p *HookServiceFlowStreamArgs) DeepEqual(ano *HookServiceFlowStreamArgs) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Req) {
		return false
	}
	return true
}

func (p *HookServiceFlowStreamArgs) Field1DeepEqual(src *hook.FlowHookRequest) bool {

	if !p.Req.DeepEqual(src) {
		return false
	}
	return true
}

type HookServiceFlowStreamResult struct {
	Success *hook.StreamPacket `thrift:"success,0,optional" frugal:"0,optional,hook.StreamPacket" json:"success,omitempty"`
}

func NewHookServiceFlowStreamResult() *HookServiceFlowStreamResult {
	return &HookServiceFlowStreamResult{}
}

func (p *HookServiceFlowStreamResult) InitDefault() {
}

var HookServiceFlowStreamResult_Success_DEFAULT *hook.StreamPacket

func (p *HookServiceFlowStreamResult) GetSuccess() (v *hook.StreamPacket) {
	if !p.IsSetSuccess() {
		return HookServiceFlowStreamResult_Success_DEFAULT
	}
	return p.Success
}
func (p *HookServiceFlowStreamResult) SetSuccess(x interface{}) {
	p.Success = x.(*hook.StreamPacket)
}

var fieldIDToName_HookServiceFlowStreamResult = map[int16]string{
	0: "success",
}

func (p *HookServiceFlowStreamResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *HookServiceFlowStreamResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_HookServiceFlowStreamResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *HookServiceFlowStreamResult) ReadField0(iprot thrift.TProtocol) error {
	_field := hook.NewStreamPacket()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *HookServiceFlowStreamResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("FlowStream_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *HookServiceFlowStreamResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *HookServiceFlowStreamResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("HookServiceFlowStreamResult(%+v)", *p)

}

func (p *HookServiceFlowStreamResult) DeepEqual(ano *HookServiceFlowStreamResult) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field0DeepEqual(ano.Success) {
		return false
	}
	return true
}

func (p *HookServiceFlowStreamResult) Field0DeepEqual(src *hook.StreamPacket) bool {

	if !p.Success.DeepEqual(src) {
		return false
	}
	return true
}
