// Code generated by thriftgo (0.4.2). DO NOT EDIT.

package codeassist

import (
	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/base"
	"database/sql"
	"database/sql/driver"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type RepositoryUpdateMode int64

const (
	RepositoryUpdateMode_Normal    RepositoryUpdateMode = 0
	RepositoryUpdateMode_Force     RepositoryUpdateMode = 1
	RepositoryUpdateMode_Overwrite RepositoryUpdateMode = 2
)

func (p RepositoryUpdateMode) String() string {
	switch p {
	case RepositoryUpdateMode_Normal:
		return "Normal"
	case RepositoryUpdateMode_Force:
		return "Force"
	case RepositoryUpdateMode_Overwrite:
		return "Overwrite"
	}
	return "<UNSET>"
}

func RepositoryUpdateModeFromString(s string) (RepositoryUpdateMode, error) {
	switch s {
	case "Normal":
		return RepositoryUpdateMode_Normal, nil
	case "Force":
		return RepositoryUpdateMode_Force, nil
	case "Overwrite":
		return RepositoryUpdateMode_Overwrite, nil
	}
	return RepositoryUpdateMode(0), fmt.Errorf("not a valid RepositoryUpdateMode string")
}

func RepositoryUpdateModePtr(v RepositoryUpdateMode) *RepositoryUpdateMode { return &v }
func (p *RepositoryUpdateMode) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = RepositoryUpdateMode(result.Int64)
	return
}

func (p *RepositoryUpdateMode) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type RepositorySource int64

const (
	RepositorySource_Unknown RepositorySource = 0
	RepositorySource_Github  RepositorySource = 1
)

func (p RepositorySource) String() string {
	switch p {
	case RepositorySource_Unknown:
		return "Unknown"
	case RepositorySource_Github:
		return "Github"
	}
	return "<UNSET>"
}

func RepositorySourceFromString(s string) (RepositorySource, error) {
	switch s {
	case "Unknown":
		return RepositorySource_Unknown, nil
	case "Github":
		return RepositorySource_Github, nil
	}
	return RepositorySource(0), fmt.Errorf("not a valid RepositorySource string")
}

func RepositorySourcePtr(v RepositorySource) *RepositorySource { return &v }
func (p *RepositorySource) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = RepositorySource(result.Int64)
	return
}

func (p *RepositorySource) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type SniffRepositoryNotSupportReason int64

const (
	SniffRepositoryNotSupportReason_NoReason            SniffRepositoryNotSupportReason = 0
	SniffRepositoryNotSupportReason_NotGitHubLink       SniffRepositoryNotSupportReason = 1
	SniffRepositoryNotSupportReason_SizeOverLimit       SniffRepositoryNotSupportReason = 2
	SniffRepositoryNotSupportReason_NotPublicRepository SniffRepositoryNotSupportReason = 3
)

func (p SniffRepositoryNotSupportReason) String() string {
	switch p {
	case SniffRepositoryNotSupportReason_NoReason:
		return "NoReason"
	case SniffRepositoryNotSupportReason_NotGitHubLink:
		return "NotGitHubLink"
	case SniffRepositoryNotSupportReason_SizeOverLimit:
		return "SizeOverLimit"
	case SniffRepositoryNotSupportReason_NotPublicRepository:
		return "NotPublicRepository"
	}
	return "<UNSET>"
}

func SniffRepositoryNotSupportReasonFromString(s string) (SniffRepositoryNotSupportReason, error) {
	switch s {
	case "NoReason":
		return SniffRepositoryNotSupportReason_NoReason, nil
	case "NotGitHubLink":
		return SniffRepositoryNotSupportReason_NotGitHubLink, nil
	case "SizeOverLimit":
		return SniffRepositoryNotSupportReason_SizeOverLimit, nil
	case "NotPublicRepository":
		return SniffRepositoryNotSupportReason_NotPublicRepository, nil
	}
	return SniffRepositoryNotSupportReason(0), fmt.Errorf("not a valid SniffRepositoryNotSupportReason string")
}

func SniffRepositoryNotSupportReasonPtr(v SniffRepositoryNotSupportReason) *SniffRepositoryNotSupportReason {
	return &v
}
func (p *SniffRepositoryNotSupportReason) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = SniffRepositoryNotSupportReason(result.Int64)
	return
}

func (p *SniffRepositoryNotSupportReason) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type ContextStatus int64

const (
	ContextStatus_Processing ContextStatus = 1
	ContextStatus_Success    ContextStatus = 2
	ContextStatus_Failed     ContextStatus = 3
	ContextStatus_Ready      ContextStatus = 4
)

func (p ContextStatus) String() string {
	switch p {
	case ContextStatus_Processing:
		return "Processing"
	case ContextStatus_Success:
		return "Success"
	case ContextStatus_Failed:
		return "Failed"
	case ContextStatus_Ready:
		return "Ready"
	}
	return "<UNSET>"
}

func ContextStatusFromString(s string) (ContextStatus, error) {
	switch s {
	case "Processing":
		return ContextStatus_Processing, nil
	case "Success":
		return ContextStatus_Success, nil
	case "Failed":
		return ContextStatus_Failed, nil
	case "Ready":
		return ContextStatus_Ready, nil
	}
	return ContextStatus(0), fmt.Errorf("not a valid ContextStatus string")
}

func ContextStatusPtr(v ContextStatus) *ContextStatus { return &v }
func (p *ContextStatus) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = ContextStatus(result.Int64)
	return
}

func (p *ContextStatus) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type FileFetchWay int64

const (
	FileFetchWay_Repository FileFetchWay = 1
	FileFetchWay_ImageX     FileFetchWay = 2
)

func (p FileFetchWay) String() string {
	switch p {
	case FileFetchWay_Repository:
		return "Repository"
	case FileFetchWay_ImageX:
		return "ImageX"
	}
	return "<UNSET>"
}

func FileFetchWayFromString(s string) (FileFetchWay, error) {
	switch s {
	case "Repository":
		return FileFetchWay_Repository, nil
	case "ImageX":
		return FileFetchWay_ImageX, nil
	}
	return FileFetchWay(0), fmt.Errorf("not a valid FileFetchWay string")
}

func FileFetchWayPtr(v FileFetchWay) *FileFetchWay { return &v }
func (p *FileFetchWay) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = FileFetchWay(result.Int64)
	return
}

func (p *FileFetchWay) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type DirectoryNodeType int64

const (
	DirectoryNodeType_Unknown   DirectoryNodeType = 0
	DirectoryNodeType_Directory DirectoryNodeType = 1
	DirectoryNodeType_File      DirectoryNodeType = 2
)

func (p DirectoryNodeType) String() string {
	switch p {
	case DirectoryNodeType_Unknown:
		return "Unknown"
	case DirectoryNodeType_Directory:
		return "Directory"
	case DirectoryNodeType_File:
		return "File"
	}
	return "<UNSET>"
}

func DirectoryNodeTypeFromString(s string) (DirectoryNodeType, error) {
	switch s {
	case "Unknown":
		return DirectoryNodeType_Unknown, nil
	case "Directory":
		return DirectoryNodeType_Directory, nil
	case "File":
		return DirectoryNodeType_File, nil
	}
	return DirectoryNodeType(0), fmt.Errorf("not a valid DirectoryNodeType string")
}

func DirectoryNodeTypePtr(v DirectoryNodeType) *DirectoryNodeType { return &v }
func (p *DirectoryNodeType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = DirectoryNodeType(result.Int64)
	return
}

func (p *DirectoryNodeType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type EncodingType int64

const (
	EncodingType_Base64 EncodingType = 1
)

func (p EncodingType) String() string {
	switch p {
	case EncodingType_Base64:
		return "Base64"
	}
	return "<UNSET>"
}

func EncodingTypeFromString(s string) (EncodingType, error) {
	switch s {
	case "Base64":
		return EncodingType_Base64, nil
	}
	return EncodingType(0), fmt.Errorf("not a valid EncodingType string")
}

func EncodingTypePtr(v EncodingType) *EncodingType { return &v }
func (p *EncodingType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = EncodingType(result.Int64)
	return
}

func (p *EncodingType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type URLSource int64

const (
	URLSource_Github URLSource = 1
	URLSource_ImageX URLSource = 2
)

func (p URLSource) String() string {
	switch p {
	case URLSource_Github:
		return "Github"
	case URLSource_ImageX:
		return "ImageX"
	}
	return "<UNSET>"
}

func URLSourceFromString(s string) (URLSource, error) {
	switch s {
	case "Github":
		return URLSource_Github, nil
	case "ImageX":
		return URLSource_ImageX, nil
	}
	return URLSource(0), fmt.Errorf("not a valid URLSource string")
}

func URLSourcePtr(v URLSource) *URLSource { return &v }
func (p *URLSource) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = URLSource(result.Int64)
	return
}

func (p *URLSource) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type CodeResourceDownloadType int64

const (
	CodeResourceDownloadType_MultiFileContext  CodeResourceDownloadType = 1
	CodeResourceDownloadType_DirectoryContext  CodeResourceDownloadType = 2
	CodeResourceDownloadType_Artifacts         CodeResourceDownloadType = 3
	CodeResourceDownloadType_SingleFileContext CodeResourceDownloadType = 4
)

func (p CodeResourceDownloadType) String() string {
	switch p {
	case CodeResourceDownloadType_MultiFileContext:
		return "MultiFileContext"
	case CodeResourceDownloadType_DirectoryContext:
		return "DirectoryContext"
	case CodeResourceDownloadType_Artifacts:
		return "Artifacts"
	case CodeResourceDownloadType_SingleFileContext:
		return "SingleFileContext"
	}
	return "<UNSET>"
}

func CodeResourceDownloadTypeFromString(s string) (CodeResourceDownloadType, error) {
	switch s {
	case "MultiFileContext":
		return CodeResourceDownloadType_MultiFileContext, nil
	case "DirectoryContext":
		return CodeResourceDownloadType_DirectoryContext, nil
	case "Artifacts":
		return CodeResourceDownloadType_Artifacts, nil
	case "SingleFileContext":
		return CodeResourceDownloadType_SingleFileContext, nil
	}
	return CodeResourceDownloadType(0), fmt.Errorf("not a valid CodeResourceDownloadType string")
}

func CodeResourceDownloadTypePtr(v CodeResourceDownloadType) *CodeResourceDownloadType { return &v }
func (p *CodeResourceDownloadType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = CodeResourceDownloadType(result.Int64)
	return
}

func (p *CodeResourceDownloadType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type SniffRepositoryLinkRequest struct {
	Link       string                `thrift:"Link,1,required" frugal:"1,required,string" json:"Link"`
	UserID     int64                 `thrift:"UserID,2" frugal:"2,default,i64" json:"UserID"`
	UpdateMode *RepositoryUpdateMode `thrift:"UpdateMode,3,optional" frugal:"3,optional,RepositoryUpdateMode" json:"UpdateMode,omitempty"`
	Base       *base.Base            `thrift:"Base,255,optional" frugal:"255,optional,base.Base" json:"Base,omitempty"`
}

func NewSniffRepositoryLinkRequest() *SniffRepositoryLinkRequest {
	return &SniffRepositoryLinkRequest{}
}

func (p *SniffRepositoryLinkRequest) InitDefault() {
}

func (p *SniffRepositoryLinkRequest) GetLink() (v string) {
	return p.Link
}

func (p *SniffRepositoryLinkRequest) GetUserID() (v int64) {
	return p.UserID
}

var SniffRepositoryLinkRequest_UpdateMode_DEFAULT RepositoryUpdateMode

func (p *SniffRepositoryLinkRequest) GetUpdateMode() (v RepositoryUpdateMode) {
	if !p.IsSetUpdateMode() {
		return SniffRepositoryLinkRequest_UpdateMode_DEFAULT
	}
	return *p.UpdateMode
}

var SniffRepositoryLinkRequest_Base_DEFAULT *base.Base

func (p *SniffRepositoryLinkRequest) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return SniffRepositoryLinkRequest_Base_DEFAULT
	}
	return p.Base
}
func (p *SniffRepositoryLinkRequest) SetLink(val string) {
	p.Link = val
}
func (p *SniffRepositoryLinkRequest) SetUserID(val int64) {
	p.UserID = val
}
func (p *SniffRepositoryLinkRequest) SetUpdateMode(val *RepositoryUpdateMode) {
	p.UpdateMode = val
}
func (p *SniffRepositoryLinkRequest) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_SniffRepositoryLinkRequest = map[int16]string{
	1:   "Link",
	2:   "UserID",
	3:   "UpdateMode",
	255: "Base",
}

func (p *SniffRepositoryLinkRequest) IsSetUpdateMode() bool {
	return p.UpdateMode != nil
}

func (p *SniffRepositoryLinkRequest) IsSetBase() bool {
	return p.Base != nil
}

func (p *SniffRepositoryLinkRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetLink bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetLink = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetLink {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SniffRepositoryLinkRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_SniffRepositoryLinkRequest[fieldId]))
}

func (p *SniffRepositoryLinkRequest) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Link = _field
	return nil
}
func (p *SniffRepositoryLinkRequest) ReadField2(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UserID = _field
	return nil
}
func (p *SniffRepositoryLinkRequest) ReadField3(iprot thrift.TProtocol) error {

	var _field *RepositoryUpdateMode
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := RepositoryUpdateMode(v)
		_field = &tmp
	}
	p.UpdateMode = _field
	return nil
}
func (p *SniffRepositoryLinkRequest) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *SniffRepositoryLinkRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("SniffRepositoryLinkRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SniffRepositoryLinkRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Link", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Link); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *SniffRepositoryLinkRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("UserID", thrift.I64, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.UserID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *SniffRepositoryLinkRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetUpdateMode() {
		if err = oprot.WriteFieldBegin("UpdateMode", thrift.I32, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.UpdateMode)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *SniffRepositoryLinkRequest) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *SniffRepositoryLinkRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SniffRepositoryLinkRequest(%+v)", *p)

}

func (p *SniffRepositoryLinkRequest) DeepEqual(ano *SniffRepositoryLinkRequest) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Link) {
		return false
	}
	if !p.Field2DeepEqual(ano.UserID) {
		return false
	}
	if !p.Field3DeepEqual(ano.UpdateMode) {
		return false
	}
	if !p.Field255DeepEqual(ano.Base) {
		return false
	}
	return true
}

func (p *SniffRepositoryLinkRequest) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Link, src) != 0 {
		return false
	}
	return true
}
func (p *SniffRepositoryLinkRequest) Field2DeepEqual(src int64) bool {

	if p.UserID != src {
		return false
	}
	return true
}
func (p *SniffRepositoryLinkRequest) Field3DeepEqual(src *RepositoryUpdateMode) bool {

	if p.UpdateMode == src {
		return true
	} else if p.UpdateMode == nil || src == nil {
		return false
	}
	if *p.UpdateMode != *src {
		return false
	}
	return true
}
func (p *SniffRepositoryLinkRequest) Field255DeepEqual(src *base.Base) bool {

	if !p.Base.DeepEqual(src) {
		return false
	}
	return true
}

type Repository struct {
	ID              string           `thrift:"ID,1" frugal:"1,default,string" json:"ID"`
	Source          RepositorySource `thrift:"Source,2" frugal:"2,default,RepositorySource" json:"Source"`
	Name            string           `thrift:"Name,3" frugal:"3,default,string" json:"Name"`
	Language        string           `thrift:"Language,4" frugal:"4,default,string" json:"Language"`
	Ref             string           `thrift:"Ref,5" frugal:"5,default,string" json:"Ref"`
	Link            string           `thrift:"Link,6" frugal:"6,default,string" json:"Link"`
	Description     string           `thrift:"Description,7" frugal:"7,default,string" json:"Description"`
	Size            int64            `thrift:"Size,8" frugal:"8,default,i64" json:"Size"`
	StargazersCount int64            `thrift:"StargazersCount,9" frugal:"9,default,i64" json:"StargazersCount"`
}

func NewRepository() *Repository {
	return &Repository{}
}

func (p *Repository) InitDefault() {
}

func (p *Repository) GetID() (v string) {
	return p.ID
}

func (p *Repository) GetSource() (v RepositorySource) {
	return p.Source
}

func (p *Repository) GetName() (v string) {
	return p.Name
}

func (p *Repository) GetLanguage() (v string) {
	return p.Language
}

func (p *Repository) GetRef() (v string) {
	return p.Ref
}

func (p *Repository) GetLink() (v string) {
	return p.Link
}

func (p *Repository) GetDescription() (v string) {
	return p.Description
}

func (p *Repository) GetSize() (v int64) {
	return p.Size
}

func (p *Repository) GetStargazersCount() (v int64) {
	return p.StargazersCount
}
func (p *Repository) SetID(val string) {
	p.ID = val
}
func (p *Repository) SetSource(val RepositorySource) {
	p.Source = val
}
func (p *Repository) SetName(val string) {
	p.Name = val
}
func (p *Repository) SetLanguage(val string) {
	p.Language = val
}
func (p *Repository) SetRef(val string) {
	p.Ref = val
}
func (p *Repository) SetLink(val string) {
	p.Link = val
}
func (p *Repository) SetDescription(val string) {
	p.Description = val
}
func (p *Repository) SetSize(val int64) {
	p.Size = val
}
func (p *Repository) SetStargazersCount(val int64) {
	p.StargazersCount = val
}

var fieldIDToName_Repository = map[int16]string{
	1: "ID",
	2: "Source",
	3: "Name",
	4: "Language",
	5: "Ref",
	6: "Link",
	7: "Description",
	8: "Size",
	9: "StargazersCount",
}

func (p *Repository) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_Repository[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *Repository) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ID = _field
	return nil
}
func (p *Repository) ReadField2(iprot thrift.TProtocol) error {

	var _field RepositorySource
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = RepositorySource(v)
	}
	p.Source = _field
	return nil
}
func (p *Repository) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Name = _field
	return nil
}
func (p *Repository) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Language = _field
	return nil
}
func (p *Repository) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Ref = _field
	return nil
}
func (p *Repository) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Link = _field
	return nil
}
func (p *Repository) ReadField7(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Description = _field
	return nil
}
func (p *Repository) ReadField8(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Size = _field
	return nil
}
func (p *Repository) ReadField9(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.StargazersCount = _field
	return nil
}

func (p *Repository) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("Repository"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *Repository) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ID", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *Repository) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Source", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.Source)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *Repository) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Name", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Name); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *Repository) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Language", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Language); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *Repository) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Ref", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Ref); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *Repository) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Link", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Link); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *Repository) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Description", thrift.STRING, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Description); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}
func (p *Repository) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Size", thrift.I64, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.Size); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}
func (p *Repository) writeField9(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StargazersCount", thrift.I64, 9); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.StargazersCount); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *Repository) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Repository(%+v)", *p)

}

func (p *Repository) DeepEqual(ano *Repository) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ID) {
		return false
	}
	if !p.Field2DeepEqual(ano.Source) {
		return false
	}
	if !p.Field3DeepEqual(ano.Name) {
		return false
	}
	if !p.Field4DeepEqual(ano.Language) {
		return false
	}
	if !p.Field5DeepEqual(ano.Ref) {
		return false
	}
	if !p.Field6DeepEqual(ano.Link) {
		return false
	}
	if !p.Field7DeepEqual(ano.Description) {
		return false
	}
	if !p.Field8DeepEqual(ano.Size) {
		return false
	}
	if !p.Field9DeepEqual(ano.StargazersCount) {
		return false
	}
	return true
}

func (p *Repository) Field1DeepEqual(src string) bool {

	if strings.Compare(p.ID, src) != 0 {
		return false
	}
	return true
}
func (p *Repository) Field2DeepEqual(src RepositorySource) bool {

	if p.Source != src {
		return false
	}
	return true
}
func (p *Repository) Field3DeepEqual(src string) bool {

	if strings.Compare(p.Name, src) != 0 {
		return false
	}
	return true
}
func (p *Repository) Field4DeepEqual(src string) bool {

	if strings.Compare(p.Language, src) != 0 {
		return false
	}
	return true
}
func (p *Repository) Field5DeepEqual(src string) bool {

	if strings.Compare(p.Ref, src) != 0 {
		return false
	}
	return true
}
func (p *Repository) Field6DeepEqual(src string) bool {

	if strings.Compare(p.Link, src) != 0 {
		return false
	}
	return true
}
func (p *Repository) Field7DeepEqual(src string) bool {

	if strings.Compare(p.Description, src) != 0 {
		return false
	}
	return true
}
func (p *Repository) Field8DeepEqual(src int64) bool {

	if p.Size != src {
		return false
	}
	return true
}
func (p *Repository) Field9DeepEqual(src int64) bool {

	if p.StargazersCount != src {
		return false
	}
	return true
}

type SniffRepositoryLinkResponse struct {
	IsRepository     bool                            `thrift:"IsRepository,1" frugal:"1,default,bool" json:"IsRepository"`
	Repository       *Repository                     `thrift:"Repository,2" frugal:"2,default,Repository" json:"Repository"`
	NotSupportReason SniffRepositoryNotSupportReason `thrift:"NotSupportReason,3" frugal:"3,default,SniffRepositoryNotSupportReason" json:"NotSupportReason"`
	SizeLimitKB      int64                           `thrift:"SizeLimitKB,4" frugal:"4,default,i64" json:"SizeLimitKB"`
	BaseResp         *base.BaseResp                  `thrift:"BaseResp,255,optional" frugal:"255,optional,base.BaseResp" json:"BaseResp,omitempty"`
}

func NewSniffRepositoryLinkResponse() *SniffRepositoryLinkResponse {
	return &SniffRepositoryLinkResponse{}
}

func (p *SniffRepositoryLinkResponse) InitDefault() {
}

func (p *SniffRepositoryLinkResponse) GetIsRepository() (v bool) {
	return p.IsRepository
}

var SniffRepositoryLinkResponse_Repository_DEFAULT *Repository

func (p *SniffRepositoryLinkResponse) GetRepository() (v *Repository) {
	if !p.IsSetRepository() {
		return SniffRepositoryLinkResponse_Repository_DEFAULT
	}
	return p.Repository
}

func (p *SniffRepositoryLinkResponse) GetNotSupportReason() (v SniffRepositoryNotSupportReason) {
	return p.NotSupportReason
}

func (p *SniffRepositoryLinkResponse) GetSizeLimitKB() (v int64) {
	return p.SizeLimitKB
}

var SniffRepositoryLinkResponse_BaseResp_DEFAULT *base.BaseResp

func (p *SniffRepositoryLinkResponse) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return SniffRepositoryLinkResponse_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *SniffRepositoryLinkResponse) SetIsRepository(val bool) {
	p.IsRepository = val
}
func (p *SniffRepositoryLinkResponse) SetRepository(val *Repository) {
	p.Repository = val
}
func (p *SniffRepositoryLinkResponse) SetNotSupportReason(val SniffRepositoryNotSupportReason) {
	p.NotSupportReason = val
}
func (p *SniffRepositoryLinkResponse) SetSizeLimitKB(val int64) {
	p.SizeLimitKB = val
}
func (p *SniffRepositoryLinkResponse) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_SniffRepositoryLinkResponse = map[int16]string{
	1:   "IsRepository",
	2:   "Repository",
	3:   "NotSupportReason",
	4:   "SizeLimitKB",
	255: "BaseResp",
}

func (p *SniffRepositoryLinkResponse) IsSetRepository() bool {
	return p.Repository != nil
}

func (p *SniffRepositoryLinkResponse) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *SniffRepositoryLinkResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SniffRepositoryLinkResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *SniffRepositoryLinkResponse) ReadField1(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.IsRepository = _field
	return nil
}
func (p *SniffRepositoryLinkResponse) ReadField2(iprot thrift.TProtocol) error {
	_field := NewRepository()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Repository = _field
	return nil
}
func (p *SniffRepositoryLinkResponse) ReadField3(iprot thrift.TProtocol) error {

	var _field SniffRepositoryNotSupportReason
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = SniffRepositoryNotSupportReason(v)
	}
	p.NotSupportReason = _field
	return nil
}
func (p *SniffRepositoryLinkResponse) ReadField4(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SizeLimitKB = _field
	return nil
}
func (p *SniffRepositoryLinkResponse) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *SniffRepositoryLinkResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("SniffRepositoryLinkResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SniffRepositoryLinkResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("IsRepository", thrift.BOOL, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.IsRepository); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *SniffRepositoryLinkResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Repository", thrift.STRUCT, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Repository.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *SniffRepositoryLinkResponse) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("NotSupportReason", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.NotSupportReason)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *SniffRepositoryLinkResponse) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SizeLimitKB", thrift.I64, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.SizeLimitKB); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *SniffRepositoryLinkResponse) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBaseResp() {
		if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.BaseResp.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *SniffRepositoryLinkResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SniffRepositoryLinkResponse(%+v)", *p)

}

func (p *SniffRepositoryLinkResponse) DeepEqual(ano *SniffRepositoryLinkResponse) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.IsRepository) {
		return false
	}
	if !p.Field2DeepEqual(ano.Repository) {
		return false
	}
	if !p.Field3DeepEqual(ano.NotSupportReason) {
		return false
	}
	if !p.Field4DeepEqual(ano.SizeLimitKB) {
		return false
	}
	if !p.Field255DeepEqual(ano.BaseResp) {
		return false
	}
	return true
}

func (p *SniffRepositoryLinkResponse) Field1DeepEqual(src bool) bool {

	if p.IsRepository != src {
		return false
	}
	return true
}
func (p *SniffRepositoryLinkResponse) Field2DeepEqual(src *Repository) bool {

	if !p.Repository.DeepEqual(src) {
		return false
	}
	return true
}
func (p *SniffRepositoryLinkResponse) Field3DeepEqual(src SniffRepositoryNotSupportReason) bool {

	if p.NotSupportReason != src {
		return false
	}
	return true
}
func (p *SniffRepositoryLinkResponse) Field4DeepEqual(src int64) bool {

	if p.SizeLimitKB != src {
		return false
	}
	return true
}
func (p *SniffRepositoryLinkResponse) Field255DeepEqual(src *base.BaseResp) bool {

	if !p.BaseResp.DeepEqual(src) {
		return false
	}
	return true
}

type CreateContextRequest struct {
	Identifier *ContextIdentifier `thrift:"Identifier,1,required" frugal:"1,required,ContextIdentifier" json:"Identifier"`
	Name       string             `thrift:"Name,2,required" frugal:"2,required,string" json:"Name"`
	CreatorID  int64              `thrift:"CreatorID,3" frugal:"3,default,i64" json:"CreatorID"`
	Base       *base.Base         `thrift:"Base,255" frugal:"255,default,base.Base" json:"Base"`
}

func NewCreateContextRequest() *CreateContextRequest {
	return &CreateContextRequest{}
}

func (p *CreateContextRequest) InitDefault() {
}

var CreateContextRequest_Identifier_DEFAULT *ContextIdentifier

func (p *CreateContextRequest) GetIdentifier() (v *ContextIdentifier) {
	if !p.IsSetIdentifier() {
		return CreateContextRequest_Identifier_DEFAULT
	}
	return p.Identifier
}

func (p *CreateContextRequest) GetName() (v string) {
	return p.Name
}

func (p *CreateContextRequest) GetCreatorID() (v int64) {
	return p.CreatorID
}

var CreateContextRequest_Base_DEFAULT *base.Base

func (p *CreateContextRequest) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return CreateContextRequest_Base_DEFAULT
	}
	return p.Base
}
func (p *CreateContextRequest) SetIdentifier(val *ContextIdentifier) {
	p.Identifier = val
}
func (p *CreateContextRequest) SetName(val string) {
	p.Name = val
}
func (p *CreateContextRequest) SetCreatorID(val int64) {
	p.CreatorID = val
}
func (p *CreateContextRequest) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_CreateContextRequest = map[int16]string{
	1:   "Identifier",
	2:   "Name",
	3:   "CreatorID",
	255: "Base",
}

func (p *CreateContextRequest) IsSetIdentifier() bool {
	return p.Identifier != nil
}

func (p *CreateContextRequest) IsSetBase() bool {
	return p.Base != nil
}

func (p *CreateContextRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetIdentifier bool = false
	var issetName bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetIdentifier = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetIdentifier {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetName {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateContextRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateContextRequest[fieldId]))
}

func (p *CreateContextRequest) ReadField1(iprot thrift.TProtocol) error {
	_field := NewContextIdentifier()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Identifier = _field
	return nil
}
func (p *CreateContextRequest) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Name = _field
	return nil
}
func (p *CreateContextRequest) ReadField3(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CreatorID = _field
	return nil
}
func (p *CreateContextRequest) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *CreateContextRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("CreateContextRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateContextRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Identifier", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Identifier.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *CreateContextRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Name", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Name); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *CreateContextRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CreatorID", thrift.I64, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.CreatorID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *CreateContextRequest) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Base.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *CreateContextRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateContextRequest(%+v)", *p)

}

func (p *CreateContextRequest) DeepEqual(ano *CreateContextRequest) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Identifier) {
		return false
	}
	if !p.Field2DeepEqual(ano.Name) {
		return false
	}
	if !p.Field3DeepEqual(ano.CreatorID) {
		return false
	}
	if !p.Field255DeepEqual(ano.Base) {
		return false
	}
	return true
}

func (p *CreateContextRequest) Field1DeepEqual(src *ContextIdentifier) bool {

	if !p.Identifier.DeepEqual(src) {
		return false
	}
	return true
}
func (p *CreateContextRequest) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Name, src) != 0 {
		return false
	}
	return true
}
func (p *CreateContextRequest) Field3DeepEqual(src int64) bool {

	if p.CreatorID != src {
		return false
	}
	return true
}
func (p *CreateContextRequest) Field255DeepEqual(src *base.Base) bool {

	if !p.Base.DeepEqual(src) {
		return false
	}
	return true
}

type CreateContextResponse struct {
	BaseResp *base.BaseResp `thrift:"BaseResp,255" frugal:"255,default,base.BaseResp" json:"BaseResp"`
}

func NewCreateContextResponse() *CreateContextResponse {
	return &CreateContextResponse{}
}

func (p *CreateContextResponse) InitDefault() {
}

var CreateContextResponse_BaseResp_DEFAULT *base.BaseResp

func (p *CreateContextResponse) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return CreateContextResponse_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *CreateContextResponse) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_CreateContextResponse = map[int16]string{
	255: "BaseResp",
}

func (p *CreateContextResponse) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *CreateContextResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateContextResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *CreateContextResponse) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *CreateContextResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("CreateContextResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateContextResponse) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.BaseResp.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *CreateContextResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateContextResponse(%+v)", *p)

}

func (p *CreateContextResponse) DeepEqual(ano *CreateContextResponse) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field255DeepEqual(ano.BaseResp) {
		return false
	}
	return true
}

func (p *CreateContextResponse) Field255DeepEqual(src *base.BaseResp) bool {

	if !p.BaseResp.DeepEqual(src) {
		return false
	}
	return true
}

type MGetContextStatusRequest struct {
	Identifiers []*ContextIdentifier `thrift:"Identifiers,1,required" frugal:"1,required,list<ContextIdentifier>" json:"Identifiers"`
}

func NewMGetContextStatusRequest() *MGetContextStatusRequest {
	return &MGetContextStatusRequest{}
}

func (p *MGetContextStatusRequest) InitDefault() {
}

func (p *MGetContextStatusRequest) GetIdentifiers() (v []*ContextIdentifier) {
	return p.Identifiers
}
func (p *MGetContextStatusRequest) SetIdentifiers(val []*ContextIdentifier) {
	p.Identifiers = val
}

var fieldIDToName_MGetContextStatusRequest = map[int16]string{
	1: "Identifiers",
}

func (p *MGetContextStatusRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetIdentifiers bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetIdentifiers = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetIdentifiers {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_MGetContextStatusRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_MGetContextStatusRequest[fieldId]))
}

func (p *MGetContextStatusRequest) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*ContextIdentifier, 0, size)
	values := make([]ContextIdentifier, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Identifiers = _field
	return nil
}

func (p *MGetContextStatusRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("MGetContextStatusRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *MGetContextStatusRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Identifiers", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Identifiers)); err != nil {
		return err
	}
	for _, v := range p.Identifiers {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *MGetContextStatusRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MGetContextStatusRequest(%+v)", *p)

}

func (p *MGetContextStatusRequest) DeepEqual(ano *MGetContextStatusRequest) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Identifiers) {
		return false
	}
	return true
}

func (p *MGetContextStatusRequest) Field1DeepEqual(src []*ContextIdentifier) bool {

	if len(p.Identifiers) != len(src) {
		return false
	}
	for i, v := range p.Identifiers {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type ContextState struct {
	Identifier *ContextIdentifier `thrift:"Identifier,1" frugal:"1,default,ContextIdentifier" json:"Identifier"`
	Status     ContextStatus      `thrift:"Status,2" frugal:"2,default,ContextStatus" json:"Status"`
	Percentage int8               `thrift:"Percentage,3" frugal:"3,default,i8" json:"Percentage"`
}

func NewContextState() *ContextState {
	return &ContextState{}
}

func (p *ContextState) InitDefault() {
}

var ContextState_Identifier_DEFAULT *ContextIdentifier

func (p *ContextState) GetIdentifier() (v *ContextIdentifier) {
	if !p.IsSetIdentifier() {
		return ContextState_Identifier_DEFAULT
	}
	return p.Identifier
}

func (p *ContextState) GetStatus() (v ContextStatus) {
	return p.Status
}

func (p *ContextState) GetPercentage() (v int8) {
	return p.Percentage
}
func (p *ContextState) SetIdentifier(val *ContextIdentifier) {
	p.Identifier = val
}
func (p *ContextState) SetStatus(val ContextStatus) {
	p.Status = val
}
func (p *ContextState) SetPercentage(val int8) {
	p.Percentage = val
}

var fieldIDToName_ContextState = map[int16]string{
	1: "Identifier",
	2: "Status",
	3: "Percentage",
}

func (p *ContextState) IsSetIdentifier() bool {
	return p.Identifier != nil
}

func (p *ContextState) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.BYTE {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ContextState[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ContextState) ReadField1(iprot thrift.TProtocol) error {
	_field := NewContextIdentifier()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Identifier = _field
	return nil
}
func (p *ContextState) ReadField2(iprot thrift.TProtocol) error {

	var _field ContextStatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = ContextStatus(v)
	}
	p.Status = _field
	return nil
}
func (p *ContextState) ReadField3(iprot thrift.TProtocol) error {

	var _field int8
	if v, err := iprot.ReadByte(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Percentage = _field
	return nil
}

func (p *ContextState) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ContextState"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ContextState) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Identifier", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Identifier.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ContextState) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Status", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.Status)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ContextState) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Percentage", thrift.BYTE, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteByte(p.Percentage); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ContextState) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ContextState(%+v)", *p)

}

func (p *ContextState) DeepEqual(ano *ContextState) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Identifier) {
		return false
	}
	if !p.Field2DeepEqual(ano.Status) {
		return false
	}
	if !p.Field3DeepEqual(ano.Percentage) {
		return false
	}
	return true
}

func (p *ContextState) Field1DeepEqual(src *ContextIdentifier) bool {

	if !p.Identifier.DeepEqual(src) {
		return false
	}
	return true
}
func (p *ContextState) Field2DeepEqual(src ContextStatus) bool {

	if p.Status != src {
		return false
	}
	return true
}
func (p *ContextState) Field3DeepEqual(src int8) bool {

	if p.Percentage != src {
		return false
	}
	return true
}

type MGetContextStatusResponse struct {
	States   []*ContextState `thrift:"States,1" frugal:"1,default,list<ContextState>" json:"States"`
	BaseResp *base.BaseResp  `thrift:"BaseResp,255" frugal:"255,default,base.BaseResp" json:"BaseResp"`
}

func NewMGetContextStatusResponse() *MGetContextStatusResponse {
	return &MGetContextStatusResponse{}
}

func (p *MGetContextStatusResponse) InitDefault() {
}

func (p *MGetContextStatusResponse) GetStates() (v []*ContextState) {
	return p.States
}

var MGetContextStatusResponse_BaseResp_DEFAULT *base.BaseResp

func (p *MGetContextStatusResponse) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return MGetContextStatusResponse_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *MGetContextStatusResponse) SetStates(val []*ContextState) {
	p.States = val
}
func (p *MGetContextStatusResponse) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_MGetContextStatusResponse = map[int16]string{
	1:   "States",
	255: "BaseResp",
}

func (p *MGetContextStatusResponse) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *MGetContextStatusResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_MGetContextStatusResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *MGetContextStatusResponse) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*ContextState, 0, size)
	values := make([]ContextState, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.States = _field
	return nil
}
func (p *MGetContextStatusResponse) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *MGetContextStatusResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("MGetContextStatusResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *MGetContextStatusResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("States", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.States)); err != nil {
		return err
	}
	for _, v := range p.States {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *MGetContextStatusResponse) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.BaseResp.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *MGetContextStatusResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MGetContextStatusResponse(%+v)", *p)

}

func (p *MGetContextStatusResponse) DeepEqual(ano *MGetContextStatusResponse) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.States) {
		return false
	}
	if !p.Field255DeepEqual(ano.BaseResp) {
		return false
	}
	return true
}

func (p *MGetContextStatusResponse) Field1DeepEqual(src []*ContextState) bool {

	if len(p.States) != len(src) {
		return false
	}
	for i, v := range p.States {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *MGetContextStatusResponse) Field255DeepEqual(src *base.BaseResp) bool {

	if !p.BaseResp.DeepEqual(src) {
		return false
	}
	return true
}

type GetContextDirectoryNodesRequest struct {
	Identifier *ContextIdentifier `thrift:"Identifier,1,required" frugal:"1,required,ContextIdentifier" json:"Identifier"`
	Path       string             `thrift:"Path,2,required" frugal:"2,required,string" json:"Path"`
	UserID     int64              `thrift:"UserID,3,required" frugal:"3,required,i64" json:"UserID"`
}

func NewGetContextDirectoryNodesRequest() *GetContextDirectoryNodesRequest {
	return &GetContextDirectoryNodesRequest{}
}

func (p *GetContextDirectoryNodesRequest) InitDefault() {
}

var GetContextDirectoryNodesRequest_Identifier_DEFAULT *ContextIdentifier

func (p *GetContextDirectoryNodesRequest) GetIdentifier() (v *ContextIdentifier) {
	if !p.IsSetIdentifier() {
		return GetContextDirectoryNodesRequest_Identifier_DEFAULT
	}
	return p.Identifier
}

func (p *GetContextDirectoryNodesRequest) GetPath() (v string) {
	return p.Path
}

func (p *GetContextDirectoryNodesRequest) GetUserID() (v int64) {
	return p.UserID
}
func (p *GetContextDirectoryNodesRequest) SetIdentifier(val *ContextIdentifier) {
	p.Identifier = val
}
func (p *GetContextDirectoryNodesRequest) SetPath(val string) {
	p.Path = val
}
func (p *GetContextDirectoryNodesRequest) SetUserID(val int64) {
	p.UserID = val
}

var fieldIDToName_GetContextDirectoryNodesRequest = map[int16]string{
	1: "Identifier",
	2: "Path",
	3: "UserID",
}

func (p *GetContextDirectoryNodesRequest) IsSetIdentifier() bool {
	return p.Identifier != nil
}

func (p *GetContextDirectoryNodesRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetIdentifier bool = false
	var issetPath bool = false
	var issetUserID bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetIdentifier = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetPath = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetUserID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetIdentifier {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetPath {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetUserID {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetContextDirectoryNodesRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_GetContextDirectoryNodesRequest[fieldId]))
}

func (p *GetContextDirectoryNodesRequest) ReadField1(iprot thrift.TProtocol) error {
	_field := NewContextIdentifier()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Identifier = _field
	return nil
}
func (p *GetContextDirectoryNodesRequest) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Path = _field
	return nil
}
func (p *GetContextDirectoryNodesRequest) ReadField3(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UserID = _field
	return nil
}

func (p *GetContextDirectoryNodesRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetContextDirectoryNodesRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetContextDirectoryNodesRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Identifier", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Identifier.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *GetContextDirectoryNodesRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Path", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Path); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *GetContextDirectoryNodesRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("UserID", thrift.I64, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.UserID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *GetContextDirectoryNodesRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetContextDirectoryNodesRequest(%+v)", *p)

}

func (p *GetContextDirectoryNodesRequest) DeepEqual(ano *GetContextDirectoryNodesRequest) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Identifier) {
		return false
	}
	if !p.Field2DeepEqual(ano.Path) {
		return false
	}
	if !p.Field3DeepEqual(ano.UserID) {
		return false
	}
	return true
}

func (p *GetContextDirectoryNodesRequest) Field1DeepEqual(src *ContextIdentifier) bool {

	if !p.Identifier.DeepEqual(src) {
		return false
	}
	return true
}
func (p *GetContextDirectoryNodesRequest) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Path, src) != 0 {
		return false
	}
	return true
}
func (p *GetContextDirectoryNodesRequest) Field3DeepEqual(src int64) bool {

	if p.UserID != src {
		return false
	}
	return true
}

type GetContextDirectoryNodesResponse struct {
	Nodes    []*DirectoryNode `thrift:"Nodes,1" frugal:"1,default,list<DirectoryNode>" json:"Nodes"`
	BaseResp *base.BaseResp   `thrift:"BaseResp,255" frugal:"255,default,base.BaseResp" json:"BaseResp"`
}

func NewGetContextDirectoryNodesResponse() *GetContextDirectoryNodesResponse {
	return &GetContextDirectoryNodesResponse{}
}

func (p *GetContextDirectoryNodesResponse) InitDefault() {
}

func (p *GetContextDirectoryNodesResponse) GetNodes() (v []*DirectoryNode) {
	return p.Nodes
}

var GetContextDirectoryNodesResponse_BaseResp_DEFAULT *base.BaseResp

func (p *GetContextDirectoryNodesResponse) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return GetContextDirectoryNodesResponse_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *GetContextDirectoryNodesResponse) SetNodes(val []*DirectoryNode) {
	p.Nodes = val
}
func (p *GetContextDirectoryNodesResponse) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_GetContextDirectoryNodesResponse = map[int16]string{
	1:   "Nodes",
	255: "BaseResp",
}

func (p *GetContextDirectoryNodesResponse) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *GetContextDirectoryNodesResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetContextDirectoryNodesResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *GetContextDirectoryNodesResponse) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*DirectoryNode, 0, size)
	values := make([]DirectoryNode, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Nodes = _field
	return nil
}
func (p *GetContextDirectoryNodesResponse) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *GetContextDirectoryNodesResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetContextDirectoryNodesResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetContextDirectoryNodesResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Nodes", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Nodes)); err != nil {
		return err
	}
	for _, v := range p.Nodes {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *GetContextDirectoryNodesResponse) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.BaseResp.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *GetContextDirectoryNodesResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetContextDirectoryNodesResponse(%+v)", *p)

}

func (p *GetContextDirectoryNodesResponse) DeepEqual(ano *GetContextDirectoryNodesResponse) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Nodes) {
		return false
	}
	if !p.Field255DeepEqual(ano.BaseResp) {
		return false
	}
	return true
}

func (p *GetContextDirectoryNodesResponse) Field1DeepEqual(src []*DirectoryNode) bool {

	if len(p.Nodes) != len(src) {
		return false
	}
	for i, v := range p.Nodes {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *GetContextDirectoryNodesResponse) Field255DeepEqual(src *base.BaseResp) bool {

	if !p.BaseResp.DeepEqual(src) {
		return false
	}
	return true
}

type DirectoryNode struct {
	Name         string            `thrift:"Name,1" frugal:"1,default,string" json:"Name"`
	NodeType     DirectoryNodeType `thrift:"NodeType,2" frugal:"2,default,DirectoryNodeType" json:"NodeType"`
	Path         string            `thrift:"Path,3" frugal:"3,default,string" json:"Path"`
	Disable      bool              `thrift:"Disable,4" frugal:"4,default,bool" json:"Disable"`
	URI          *string           `thrift:"URI,5,optional" frugal:"5,optional,string" json:"URI,omitempty"`
	FileFetchWay FileFetchWay      `thrift:"FileFetchWay,6" frugal:"6,default,FileFetchWay" json:"FileFetchWay"`
}

func NewDirectoryNode() *DirectoryNode {
	return &DirectoryNode{}
}

func (p *DirectoryNode) InitDefault() {
}

func (p *DirectoryNode) GetName() (v string) {
	return p.Name
}

func (p *DirectoryNode) GetNodeType() (v DirectoryNodeType) {
	return p.NodeType
}

func (p *DirectoryNode) GetPath() (v string) {
	return p.Path
}

func (p *DirectoryNode) GetDisable() (v bool) {
	return p.Disable
}

var DirectoryNode_URI_DEFAULT string

func (p *DirectoryNode) GetURI() (v string) {
	if !p.IsSetURI() {
		return DirectoryNode_URI_DEFAULT
	}
	return *p.URI
}

func (p *DirectoryNode) GetFileFetchWay() (v FileFetchWay) {
	return p.FileFetchWay
}
func (p *DirectoryNode) SetName(val string) {
	p.Name = val
}
func (p *DirectoryNode) SetNodeType(val DirectoryNodeType) {
	p.NodeType = val
}
func (p *DirectoryNode) SetPath(val string) {
	p.Path = val
}
func (p *DirectoryNode) SetDisable(val bool) {
	p.Disable = val
}
func (p *DirectoryNode) SetURI(val *string) {
	p.URI = val
}
func (p *DirectoryNode) SetFileFetchWay(val FileFetchWay) {
	p.FileFetchWay = val
}

var fieldIDToName_DirectoryNode = map[int16]string{
	1: "Name",
	2: "NodeType",
	3: "Path",
	4: "Disable",
	5: "URI",
	6: "FileFetchWay",
}

func (p *DirectoryNode) IsSetURI() bool {
	return p.URI != nil
}

func (p *DirectoryNode) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DirectoryNode[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DirectoryNode) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Name = _field
	return nil
}
func (p *DirectoryNode) ReadField2(iprot thrift.TProtocol) error {

	var _field DirectoryNodeType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = DirectoryNodeType(v)
	}
	p.NodeType = _field
	return nil
}
func (p *DirectoryNode) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Path = _field
	return nil
}
func (p *DirectoryNode) ReadField4(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Disable = _field
	return nil
}
func (p *DirectoryNode) ReadField5(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.URI = _field
	return nil
}
func (p *DirectoryNode) ReadField6(iprot thrift.TProtocol) error {

	var _field FileFetchWay
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = FileFetchWay(v)
	}
	p.FileFetchWay = _field
	return nil
}

func (p *DirectoryNode) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("DirectoryNode"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DirectoryNode) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Name", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Name); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *DirectoryNode) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("NodeType", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.NodeType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *DirectoryNode) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Path", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Path); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *DirectoryNode) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Disable", thrift.BOOL, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.Disable); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *DirectoryNode) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetURI() {
		if err = oprot.WriteFieldBegin("URI", thrift.STRING, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.URI); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *DirectoryNode) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("FileFetchWay", thrift.I32, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.FileFetchWay)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DirectoryNode) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DirectoryNode(%+v)", *p)

}

func (p *DirectoryNode) DeepEqual(ano *DirectoryNode) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Name) {
		return false
	}
	if !p.Field2DeepEqual(ano.NodeType) {
		return false
	}
	if !p.Field3DeepEqual(ano.Path) {
		return false
	}
	if !p.Field4DeepEqual(ano.Disable) {
		return false
	}
	if !p.Field5DeepEqual(ano.URI) {
		return false
	}
	if !p.Field6DeepEqual(ano.FileFetchWay) {
		return false
	}
	return true
}

func (p *DirectoryNode) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Name, src) != 0 {
		return false
	}
	return true
}
func (p *DirectoryNode) Field2DeepEqual(src DirectoryNodeType) bool {

	if p.NodeType != src {
		return false
	}
	return true
}
func (p *DirectoryNode) Field3DeepEqual(src string) bool {

	if strings.Compare(p.Path, src) != 0 {
		return false
	}
	return true
}
func (p *DirectoryNode) Field4DeepEqual(src bool) bool {

	if p.Disable != src {
		return false
	}
	return true
}
func (p *DirectoryNode) Field5DeepEqual(src *string) bool {

	if p.URI == src {
		return true
	} else if p.URI == nil || src == nil {
		return false
	}
	if strings.Compare(*p.URI, *src) != 0 {
		return false
	}
	return true
}
func (p *DirectoryNode) Field6DeepEqual(src FileFetchWay) bool {

	if p.FileFetchWay != src {
		return false
	}
	return true
}

type GetRepoFileContentRequest struct {
	RepoID   string        `thrift:"RepoID,1,required" frugal:"1,required,string" json:"RepoID"`
	Path     string        `thrift:"Path,2,required" frugal:"2,required,string" json:"Path"`
	Encoding *EncodingType `thrift:"Encoding,3,optional" frugal:"3,optional,EncodingType" json:"Encoding,omitempty"`
}

func NewGetRepoFileContentRequest() *GetRepoFileContentRequest {
	return &GetRepoFileContentRequest{}
}

func (p *GetRepoFileContentRequest) InitDefault() {
}

func (p *GetRepoFileContentRequest) GetRepoID() (v string) {
	return p.RepoID
}

func (p *GetRepoFileContentRequest) GetPath() (v string) {
	return p.Path
}

var GetRepoFileContentRequest_Encoding_DEFAULT EncodingType

func (p *GetRepoFileContentRequest) GetEncoding() (v EncodingType) {
	if !p.IsSetEncoding() {
		return GetRepoFileContentRequest_Encoding_DEFAULT
	}
	return *p.Encoding
}
func (p *GetRepoFileContentRequest) SetRepoID(val string) {
	p.RepoID = val
}
func (p *GetRepoFileContentRequest) SetPath(val string) {
	p.Path = val
}
func (p *GetRepoFileContentRequest) SetEncoding(val *EncodingType) {
	p.Encoding = val
}

var fieldIDToName_GetRepoFileContentRequest = map[int16]string{
	1: "RepoID",
	2: "Path",
	3: "Encoding",
}

func (p *GetRepoFileContentRequest) IsSetEncoding() bool {
	return p.Encoding != nil
}

func (p *GetRepoFileContentRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetRepoID bool = false
	var issetPath bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetRepoID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetPath = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetRepoID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetPath {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetRepoFileContentRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_GetRepoFileContentRequest[fieldId]))
}

func (p *GetRepoFileContentRequest) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RepoID = _field
	return nil
}
func (p *GetRepoFileContentRequest) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Path = _field
	return nil
}
func (p *GetRepoFileContentRequest) ReadField3(iprot thrift.TProtocol) error {

	var _field *EncodingType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := EncodingType(v)
		_field = &tmp
	}
	p.Encoding = _field
	return nil
}

func (p *GetRepoFileContentRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetRepoFileContentRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetRepoFileContentRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RepoID", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RepoID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *GetRepoFileContentRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Path", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Path); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *GetRepoFileContentRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetEncoding() {
		if err = oprot.WriteFieldBegin("Encoding", thrift.I32, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.Encoding)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *GetRepoFileContentRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetRepoFileContentRequest(%+v)", *p)

}

func (p *GetRepoFileContentRequest) DeepEqual(ano *GetRepoFileContentRequest) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.RepoID) {
		return false
	}
	if !p.Field2DeepEqual(ano.Path) {
		return false
	}
	if !p.Field3DeepEqual(ano.Encoding) {
		return false
	}
	return true
}

func (p *GetRepoFileContentRequest) Field1DeepEqual(src string) bool {

	if strings.Compare(p.RepoID, src) != 0 {
		return false
	}
	return true
}
func (p *GetRepoFileContentRequest) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Path, src) != 0 {
		return false
	}
	return true
}
func (p *GetRepoFileContentRequest) Field3DeepEqual(src *EncodingType) bool {

	if p.Encoding == src {
		return true
	} else if p.Encoding == nil || src == nil {
		return false
	}
	if *p.Encoding != *src {
		return false
	}
	return true
}

type GetRepoFileContentResponse struct {
	Content   string         `thrift:"Content,1" frugal:"1,default,string" json:"Content"`
	Size      int64          `thrift:"Size,2" frugal:"2,default,i64" json:"Size"`
	Truncated bool           `thrift:"Truncated,3" frugal:"3,default,bool" json:"Truncated"`
	BaseResp  *base.BaseResp `thrift:"BaseResp,255" frugal:"255,default,base.BaseResp" json:"BaseResp"`
}

func NewGetRepoFileContentResponse() *GetRepoFileContentResponse {
	return &GetRepoFileContentResponse{}
}

func (p *GetRepoFileContentResponse) InitDefault() {
}

func (p *GetRepoFileContentResponse) GetContent() (v string) {
	return p.Content
}

func (p *GetRepoFileContentResponse) GetSize() (v int64) {
	return p.Size
}

func (p *GetRepoFileContentResponse) GetTruncated() (v bool) {
	return p.Truncated
}

var GetRepoFileContentResponse_BaseResp_DEFAULT *base.BaseResp

func (p *GetRepoFileContentResponse) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return GetRepoFileContentResponse_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *GetRepoFileContentResponse) SetContent(val string) {
	p.Content = val
}
func (p *GetRepoFileContentResponse) SetSize(val int64) {
	p.Size = val
}
func (p *GetRepoFileContentResponse) SetTruncated(val bool) {
	p.Truncated = val
}
func (p *GetRepoFileContentResponse) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_GetRepoFileContentResponse = map[int16]string{
	1:   "Content",
	2:   "Size",
	3:   "Truncated",
	255: "BaseResp",
}

func (p *GetRepoFileContentResponse) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *GetRepoFileContentResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetRepoFileContentResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *GetRepoFileContentResponse) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Content = _field
	return nil
}
func (p *GetRepoFileContentResponse) ReadField2(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Size = _field
	return nil
}
func (p *GetRepoFileContentResponse) ReadField3(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Truncated = _field
	return nil
}
func (p *GetRepoFileContentResponse) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *GetRepoFileContentResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetRepoFileContentResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetRepoFileContentResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Content", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Content); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *GetRepoFileContentResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Size", thrift.I64, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.Size); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *GetRepoFileContentResponse) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Truncated", thrift.BOOL, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.Truncated); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *GetRepoFileContentResponse) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.BaseResp.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *GetRepoFileContentResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetRepoFileContentResponse(%+v)", *p)

}

func (p *GetRepoFileContentResponse) DeepEqual(ano *GetRepoFileContentResponse) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Content) {
		return false
	}
	if !p.Field2DeepEqual(ano.Size) {
		return false
	}
	if !p.Field3DeepEqual(ano.Truncated) {
		return false
	}
	if !p.Field255DeepEqual(ano.BaseResp) {
		return false
	}
	return true
}

func (p *GetRepoFileContentResponse) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Content, src) != 0 {
		return false
	}
	return true
}
func (p *GetRepoFileContentResponse) Field2DeepEqual(src int64) bool {

	if p.Size != src {
		return false
	}
	return true
}
func (p *GetRepoFileContentResponse) Field3DeepEqual(src bool) bool {

	if p.Truncated != src {
		return false
	}
	return true
}
func (p *GetRepoFileContentResponse) Field255DeepEqual(src *base.BaseResp) bool {

	if !p.BaseResp.DeepEqual(src) {
		return false
	}
	return true
}

type GetContextInfoRequest struct {
	Identifier *ContextIdentifier `thrift:"Identifier,1,required" frugal:"1,required,ContextIdentifier" json:"Identifier"`
}

func NewGetContextInfoRequest() *GetContextInfoRequest {
	return &GetContextInfoRequest{}
}

func (p *GetContextInfoRequest) InitDefault() {
}

var GetContextInfoRequest_Identifier_DEFAULT *ContextIdentifier

func (p *GetContextInfoRequest) GetIdentifier() (v *ContextIdentifier) {
	if !p.IsSetIdentifier() {
		return GetContextInfoRequest_Identifier_DEFAULT
	}
	return p.Identifier
}
func (p *GetContextInfoRequest) SetIdentifier(val *ContextIdentifier) {
	p.Identifier = val
}

var fieldIDToName_GetContextInfoRequest = map[int16]string{
	1: "Identifier",
}

func (p *GetContextInfoRequest) IsSetIdentifier() bool {
	return p.Identifier != nil
}

func (p *GetContextInfoRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetIdentifier bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetIdentifier = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetIdentifier {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetContextInfoRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_GetContextInfoRequest[fieldId]))
}

func (p *GetContextInfoRequest) ReadField1(iprot thrift.TProtocol) error {
	_field := NewContextIdentifier()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Identifier = _field
	return nil
}

func (p *GetContextInfoRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetContextInfoRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetContextInfoRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Identifier", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Identifier.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *GetContextInfoRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetContextInfoRequest(%+v)", *p)

}

func (p *GetContextInfoRequest) DeepEqual(ano *GetContextInfoRequest) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Identifier) {
		return false
	}
	return true
}

func (p *GetContextInfoRequest) Field1DeepEqual(src *ContextIdentifier) bool {

	if !p.Identifier.DeepEqual(src) {
		return false
	}
	return true
}

type FileContextInfo struct {
	Name string `thrift:"Name,1" frugal:"1,default,string" json:"Name"`
}

func NewFileContextInfo() *FileContextInfo {
	return &FileContextInfo{}
}

func (p *FileContextInfo) InitDefault() {
}

func (p *FileContextInfo) GetName() (v string) {
	return p.Name
}
func (p *FileContextInfo) SetName(val string) {
	p.Name = val
}

var fieldIDToName_FileContextInfo = map[int16]string{
	1: "Name",
}

func (p *FileContextInfo) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FileContextInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *FileContextInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Name = _field
	return nil
}

func (p *FileContextInfo) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("FileContextInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *FileContextInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Name", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Name); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *FileContextInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FileContextInfo(%+v)", *p)

}

func (p *FileContextInfo) DeepEqual(ano *FileContextInfo) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Name) {
		return false
	}
	return true
}

func (p *FileContextInfo) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Name, src) != 0 {
		return false
	}
	return true
}

type FolderContextInfo struct {
	Name     string `thrift:"Name,1" frugal:"1,default,string" json:"Name"`
	Language string `thrift:"Language,2" frugal:"2,default,string" json:"Language"`
}

func NewFolderContextInfo() *FolderContextInfo {
	return &FolderContextInfo{}
}

func (p *FolderContextInfo) InitDefault() {
}

func (p *FolderContextInfo) GetName() (v string) {
	return p.Name
}

func (p *FolderContextInfo) GetLanguage() (v string) {
	return p.Language
}
func (p *FolderContextInfo) SetName(val string) {
	p.Name = val
}
func (p *FolderContextInfo) SetLanguage(val string) {
	p.Language = val
}

var fieldIDToName_FolderContextInfo = map[int16]string{
	1: "Name",
	2: "Language",
}

func (p *FolderContextInfo) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FolderContextInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *FolderContextInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Name = _field
	return nil
}
func (p *FolderContextInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Language = _field
	return nil
}

func (p *FolderContextInfo) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("FolderContextInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *FolderContextInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Name", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Name); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *FolderContextInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Language", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Language); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *FolderContextInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FolderContextInfo(%+v)", *p)

}

func (p *FolderContextInfo) DeepEqual(ano *FolderContextInfo) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Name) {
		return false
	}
	if !p.Field2DeepEqual(ano.Language) {
		return false
	}
	return true
}

func (p *FolderContextInfo) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Name, src) != 0 {
		return false
	}
	return true
}
func (p *FolderContextInfo) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Language, src) != 0 {
		return false
	}
	return true
}

type RepositoryContextInfo struct {
	Name            string `thrift:"Name,1" frugal:"1,default,string" json:"Name"`
	Language        string `thrift:"Language,2" frugal:"2,default,string" json:"Language"`
	Ref             string `thrift:"Ref,3" frugal:"3,default,string" json:"Ref"`
	Link            string `thrift:"Link,4" frugal:"4,default,string" json:"Link"`
	Description     string `thrift:"Description,5" frugal:"5,default,string" json:"Description"`
	Size            int64  `thrift:"Size,6" frugal:"6,default,i64" json:"Size"`
	StargazersCount int64  `thrift:"StargazersCount,7" frugal:"7,default,i64" json:"StargazersCount"`
}

func NewRepositoryContextInfo() *RepositoryContextInfo {
	return &RepositoryContextInfo{}
}

func (p *RepositoryContextInfo) InitDefault() {
}

func (p *RepositoryContextInfo) GetName() (v string) {
	return p.Name
}

func (p *RepositoryContextInfo) GetLanguage() (v string) {
	return p.Language
}

func (p *RepositoryContextInfo) GetRef() (v string) {
	return p.Ref
}

func (p *RepositoryContextInfo) GetLink() (v string) {
	return p.Link
}

func (p *RepositoryContextInfo) GetDescription() (v string) {
	return p.Description
}

func (p *RepositoryContextInfo) GetSize() (v int64) {
	return p.Size
}

func (p *RepositoryContextInfo) GetStargazersCount() (v int64) {
	return p.StargazersCount
}
func (p *RepositoryContextInfo) SetName(val string) {
	p.Name = val
}
func (p *RepositoryContextInfo) SetLanguage(val string) {
	p.Language = val
}
func (p *RepositoryContextInfo) SetRef(val string) {
	p.Ref = val
}
func (p *RepositoryContextInfo) SetLink(val string) {
	p.Link = val
}
func (p *RepositoryContextInfo) SetDescription(val string) {
	p.Description = val
}
func (p *RepositoryContextInfo) SetSize(val int64) {
	p.Size = val
}
func (p *RepositoryContextInfo) SetStargazersCount(val int64) {
	p.StargazersCount = val
}

var fieldIDToName_RepositoryContextInfo = map[int16]string{
	1: "Name",
	2: "Language",
	3: "Ref",
	4: "Link",
	5: "Description",
	6: "Size",
	7: "StargazersCount",
}

func (p *RepositoryContextInfo) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_RepositoryContextInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *RepositoryContextInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Name = _field
	return nil
}
func (p *RepositoryContextInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Language = _field
	return nil
}
func (p *RepositoryContextInfo) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Ref = _field
	return nil
}
func (p *RepositoryContextInfo) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Link = _field
	return nil
}
func (p *RepositoryContextInfo) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Description = _field
	return nil
}
func (p *RepositoryContextInfo) ReadField6(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Size = _field
	return nil
}
func (p *RepositoryContextInfo) ReadField7(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.StargazersCount = _field
	return nil
}

func (p *RepositoryContextInfo) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("RepositoryContextInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *RepositoryContextInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Name", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Name); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *RepositoryContextInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Language", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Language); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *RepositoryContextInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Ref", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Ref); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *RepositoryContextInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Link", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Link); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *RepositoryContextInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Description", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Description); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *RepositoryContextInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Size", thrift.I64, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.Size); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *RepositoryContextInfo) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StargazersCount", thrift.I64, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.StargazersCount); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *RepositoryContextInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RepositoryContextInfo(%+v)", *p)

}

func (p *RepositoryContextInfo) DeepEqual(ano *RepositoryContextInfo) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Name) {
		return false
	}
	if !p.Field2DeepEqual(ano.Language) {
		return false
	}
	if !p.Field3DeepEqual(ano.Ref) {
		return false
	}
	if !p.Field4DeepEqual(ano.Link) {
		return false
	}
	if !p.Field5DeepEqual(ano.Description) {
		return false
	}
	if !p.Field6DeepEqual(ano.Size) {
		return false
	}
	if !p.Field7DeepEqual(ano.StargazersCount) {
		return false
	}
	return true
}

func (p *RepositoryContextInfo) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Name, src) != 0 {
		return false
	}
	return true
}
func (p *RepositoryContextInfo) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Language, src) != 0 {
		return false
	}
	return true
}
func (p *RepositoryContextInfo) Field3DeepEqual(src string) bool {

	if strings.Compare(p.Ref, src) != 0 {
		return false
	}
	return true
}
func (p *RepositoryContextInfo) Field4DeepEqual(src string) bool {

	if strings.Compare(p.Link, src) != 0 {
		return false
	}
	return true
}
func (p *RepositoryContextInfo) Field5DeepEqual(src string) bool {

	if strings.Compare(p.Description, src) != 0 {
		return false
	}
	return true
}
func (p *RepositoryContextInfo) Field6DeepEqual(src int64) bool {

	if p.Size != src {
		return false
	}
	return true
}
func (p *RepositoryContextInfo) Field7DeepEqual(src int64) bool {

	if p.StargazersCount != src {
		return false
	}
	return true
}

type GetContextInfoResponse struct {
	Identifier            *ContextIdentifier     `thrift:"Identifier,1" frugal:"1,default,ContextIdentifier" json:"Identifier"`
	FileContextInfo       *FileContextInfo       `thrift:"FileContextInfo,2,optional" frugal:"2,optional,FileContextInfo" json:"FileContextInfo,omitempty"`
	FolderContextInfo     *FolderContextInfo     `thrift:"FolderContextInfo,3,optional" frugal:"3,optional,FolderContextInfo" json:"FolderContextInfo,omitempty"`
	RepositoryContextInfo *RepositoryContextInfo `thrift:"RepositoryContextInfo,4,optional" frugal:"4,optional,RepositoryContextInfo" json:"RepositoryContextInfo,omitempty"`
	BaseResp              *base.BaseResp         `thrift:"BaseResp,255" frugal:"255,default,base.BaseResp" json:"BaseResp"`
}

func NewGetContextInfoResponse() *GetContextInfoResponse {
	return &GetContextInfoResponse{}
}

func (p *GetContextInfoResponse) InitDefault() {
}

var GetContextInfoResponse_Identifier_DEFAULT *ContextIdentifier

func (p *GetContextInfoResponse) GetIdentifier() (v *ContextIdentifier) {
	if !p.IsSetIdentifier() {
		return GetContextInfoResponse_Identifier_DEFAULT
	}
	return p.Identifier
}

var GetContextInfoResponse_FileContextInfo_DEFAULT *FileContextInfo

func (p *GetContextInfoResponse) GetFileContextInfo() (v *FileContextInfo) {
	if !p.IsSetFileContextInfo() {
		return GetContextInfoResponse_FileContextInfo_DEFAULT
	}
	return p.FileContextInfo
}

var GetContextInfoResponse_FolderContextInfo_DEFAULT *FolderContextInfo

func (p *GetContextInfoResponse) GetFolderContextInfo() (v *FolderContextInfo) {
	if !p.IsSetFolderContextInfo() {
		return GetContextInfoResponse_FolderContextInfo_DEFAULT
	}
	return p.FolderContextInfo
}

var GetContextInfoResponse_RepositoryContextInfo_DEFAULT *RepositoryContextInfo

func (p *GetContextInfoResponse) GetRepositoryContextInfo() (v *RepositoryContextInfo) {
	if !p.IsSetRepositoryContextInfo() {
		return GetContextInfoResponse_RepositoryContextInfo_DEFAULT
	}
	return p.RepositoryContextInfo
}

var GetContextInfoResponse_BaseResp_DEFAULT *base.BaseResp

func (p *GetContextInfoResponse) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return GetContextInfoResponse_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *GetContextInfoResponse) SetIdentifier(val *ContextIdentifier) {
	p.Identifier = val
}
func (p *GetContextInfoResponse) SetFileContextInfo(val *FileContextInfo) {
	p.FileContextInfo = val
}
func (p *GetContextInfoResponse) SetFolderContextInfo(val *FolderContextInfo) {
	p.FolderContextInfo = val
}
func (p *GetContextInfoResponse) SetRepositoryContextInfo(val *RepositoryContextInfo) {
	p.RepositoryContextInfo = val
}
func (p *GetContextInfoResponse) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_GetContextInfoResponse = map[int16]string{
	1:   "Identifier",
	2:   "FileContextInfo",
	3:   "FolderContextInfo",
	4:   "RepositoryContextInfo",
	255: "BaseResp",
}

func (p *GetContextInfoResponse) IsSetIdentifier() bool {
	return p.Identifier != nil
}

func (p *GetContextInfoResponse) IsSetFileContextInfo() bool {
	return p.FileContextInfo != nil
}

func (p *GetContextInfoResponse) IsSetFolderContextInfo() bool {
	return p.FolderContextInfo != nil
}

func (p *GetContextInfoResponse) IsSetRepositoryContextInfo() bool {
	return p.RepositoryContextInfo != nil
}

func (p *GetContextInfoResponse) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *GetContextInfoResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetContextInfoResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *GetContextInfoResponse) ReadField1(iprot thrift.TProtocol) error {
	_field := NewContextIdentifier()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Identifier = _field
	return nil
}
func (p *GetContextInfoResponse) ReadField2(iprot thrift.TProtocol) error {
	_field := NewFileContextInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.FileContextInfo = _field
	return nil
}
func (p *GetContextInfoResponse) ReadField3(iprot thrift.TProtocol) error {
	_field := NewFolderContextInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.FolderContextInfo = _field
	return nil
}
func (p *GetContextInfoResponse) ReadField4(iprot thrift.TProtocol) error {
	_field := NewRepositoryContextInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.RepositoryContextInfo = _field
	return nil
}
func (p *GetContextInfoResponse) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *GetContextInfoResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetContextInfoResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetContextInfoResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Identifier", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Identifier.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *GetContextInfoResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetFileContextInfo() {
		if err = oprot.WriteFieldBegin("FileContextInfo", thrift.STRUCT, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.FileContextInfo.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *GetContextInfoResponse) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetFolderContextInfo() {
		if err = oprot.WriteFieldBegin("FolderContextInfo", thrift.STRUCT, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.FolderContextInfo.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *GetContextInfoResponse) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetRepositoryContextInfo() {
		if err = oprot.WriteFieldBegin("RepositoryContextInfo", thrift.STRUCT, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.RepositoryContextInfo.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *GetContextInfoResponse) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.BaseResp.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *GetContextInfoResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetContextInfoResponse(%+v)", *p)

}

func (p *GetContextInfoResponse) DeepEqual(ano *GetContextInfoResponse) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Identifier) {
		return false
	}
	if !p.Field2DeepEqual(ano.FileContextInfo) {
		return false
	}
	if !p.Field3DeepEqual(ano.FolderContextInfo) {
		return false
	}
	if !p.Field4DeepEqual(ano.RepositoryContextInfo) {
		return false
	}
	if !p.Field255DeepEqual(ano.BaseResp) {
		return false
	}
	return true
}

func (p *GetContextInfoResponse) Field1DeepEqual(src *ContextIdentifier) bool {

	if !p.Identifier.DeepEqual(src) {
		return false
	}
	return true
}
func (p *GetContextInfoResponse) Field2DeepEqual(src *FileContextInfo) bool {

	if !p.FileContextInfo.DeepEqual(src) {
		return false
	}
	return true
}
func (p *GetContextInfoResponse) Field3DeepEqual(src *FolderContextInfo) bool {

	if !p.FolderContextInfo.DeepEqual(src) {
		return false
	}
	return true
}
func (p *GetContextInfoResponse) Field4DeepEqual(src *RepositoryContextInfo) bool {

	if !p.RepositoryContextInfo.DeepEqual(src) {
		return false
	}
	return true
}
func (p *GetContextInfoResponse) Field255DeepEqual(src *base.BaseResp) bool {

	if !p.BaseResp.DeepEqual(src) {
		return false
	}
	return true
}

type GetContextDownloadURLRequest struct {
	Identifier *ContextIdentifier `thrift:"Identifier,1,required" frugal:"1,required,ContextIdentifier" json:"Identifier"`
	UserID     int64              `thrift:"UserID,2,required" frugal:"2,required,i64" json:"UserID"`
}

func NewGetContextDownloadURLRequest() *GetContextDownloadURLRequest {
	return &GetContextDownloadURLRequest{}
}

func (p *GetContextDownloadURLRequest) InitDefault() {
}

var GetContextDownloadURLRequest_Identifier_DEFAULT *ContextIdentifier

func (p *GetContextDownloadURLRequest) GetIdentifier() (v *ContextIdentifier) {
	if !p.IsSetIdentifier() {
		return GetContextDownloadURLRequest_Identifier_DEFAULT
	}
	return p.Identifier
}

func (p *GetContextDownloadURLRequest) GetUserID() (v int64) {
	return p.UserID
}
func (p *GetContextDownloadURLRequest) SetIdentifier(val *ContextIdentifier) {
	p.Identifier = val
}
func (p *GetContextDownloadURLRequest) SetUserID(val int64) {
	p.UserID = val
}

var fieldIDToName_GetContextDownloadURLRequest = map[int16]string{
	1: "Identifier",
	2: "UserID",
}

func (p *GetContextDownloadURLRequest) IsSetIdentifier() bool {
	return p.Identifier != nil
}

func (p *GetContextDownloadURLRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetIdentifier bool = false
	var issetUserID bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetIdentifier = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetUserID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetIdentifier {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetUserID {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetContextDownloadURLRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_GetContextDownloadURLRequest[fieldId]))
}

func (p *GetContextDownloadURLRequest) ReadField1(iprot thrift.TProtocol) error {
	_field := NewContextIdentifier()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Identifier = _field
	return nil
}
func (p *GetContextDownloadURLRequest) ReadField2(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UserID = _field
	return nil
}

func (p *GetContextDownloadURLRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetContextDownloadURLRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetContextDownloadURLRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Identifier", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Identifier.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *GetContextDownloadURLRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("UserID", thrift.I64, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.UserID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *GetContextDownloadURLRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetContextDownloadURLRequest(%+v)", *p)

}

func (p *GetContextDownloadURLRequest) DeepEqual(ano *GetContextDownloadURLRequest) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Identifier) {
		return false
	}
	if !p.Field2DeepEqual(ano.UserID) {
		return false
	}
	return true
}

func (p *GetContextDownloadURLRequest) Field1DeepEqual(src *ContextIdentifier) bool {

	if !p.Identifier.DeepEqual(src) {
		return false
	}
	return true
}
func (p *GetContextDownloadURLRequest) Field2DeepEqual(src int64) bool {

	if p.UserID != src {
		return false
	}
	return true
}

type GetContextDownloadURLResponse struct {
	URL       string         `thrift:"URL,1" frugal:"1,default,string" json:"URL"`
	URLSource URLSource      `thrift:"URLSource,2" frugal:"2,default,URLSource" json:"URLSource"`
	Name      string         `thrift:"Name,3" frugal:"3,default,string" json:"Name"`
	BaseResp  *base.BaseResp `thrift:"BaseResp,255" frugal:"255,default,base.BaseResp" json:"BaseResp"`
}

func NewGetContextDownloadURLResponse() *GetContextDownloadURLResponse {
	return &GetContextDownloadURLResponse{}
}

func (p *GetContextDownloadURLResponse) InitDefault() {
}

func (p *GetContextDownloadURLResponse) GetURL() (v string) {
	return p.URL
}

func (p *GetContextDownloadURLResponse) GetURLSource() (v URLSource) {
	return p.URLSource
}

func (p *GetContextDownloadURLResponse) GetName() (v string) {
	return p.Name
}

var GetContextDownloadURLResponse_BaseResp_DEFAULT *base.BaseResp

func (p *GetContextDownloadURLResponse) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return GetContextDownloadURLResponse_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *GetContextDownloadURLResponse) SetURL(val string) {
	p.URL = val
}
func (p *GetContextDownloadURLResponse) SetURLSource(val URLSource) {
	p.URLSource = val
}
func (p *GetContextDownloadURLResponse) SetName(val string) {
	p.Name = val
}
func (p *GetContextDownloadURLResponse) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_GetContextDownloadURLResponse = map[int16]string{
	1:   "URL",
	2:   "URLSource",
	3:   "Name",
	255: "BaseResp",
}

func (p *GetContextDownloadURLResponse) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *GetContextDownloadURLResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetContextDownloadURLResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *GetContextDownloadURLResponse) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.URL = _field
	return nil
}
func (p *GetContextDownloadURLResponse) ReadField2(iprot thrift.TProtocol) error {

	var _field URLSource
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = URLSource(v)
	}
	p.URLSource = _field
	return nil
}
func (p *GetContextDownloadURLResponse) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Name = _field
	return nil
}
func (p *GetContextDownloadURLResponse) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *GetContextDownloadURLResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetContextDownloadURLResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetContextDownloadURLResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("URL", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.URL); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *GetContextDownloadURLResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("URLSource", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.URLSource)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *GetContextDownloadURLResponse) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Name", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Name); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *GetContextDownloadURLResponse) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.BaseResp.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *GetContextDownloadURLResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetContextDownloadURLResponse(%+v)", *p)

}

func (p *GetContextDownloadURLResponse) DeepEqual(ano *GetContextDownloadURLResponse) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.URL) {
		return false
	}
	if !p.Field2DeepEqual(ano.URLSource) {
		return false
	}
	if !p.Field3DeepEqual(ano.Name) {
		return false
	}
	if !p.Field255DeepEqual(ano.BaseResp) {
		return false
	}
	return true
}

func (p *GetContextDownloadURLResponse) Field1DeepEqual(src string) bool {

	if strings.Compare(p.URL, src) != 0 {
		return false
	}
	return true
}
func (p *GetContextDownloadURLResponse) Field2DeepEqual(src URLSource) bool {

	if p.URLSource != src {
		return false
	}
	return true
}
func (p *GetContextDownloadURLResponse) Field3DeepEqual(src string) bool {

	if strings.Compare(p.Name, src) != 0 {
		return false
	}
	return true
}
func (p *GetContextDownloadURLResponse) Field255DeepEqual(src *base.BaseResp) bool {

	if !p.BaseResp.DeepEqual(src) {
		return false
	}
	return true
}

type ArtifactsIdentifier struct {
	CodeID      string `thrift:"CodeID,1,required" frugal:"1,required,string" json:"CodeID"`
	CodeVersion string `thrift:"CodeVersion,2,required" frugal:"2,required,string" json:"CodeVersion"`
	Title       string `thrift:"Title,3,required" frugal:"3,required,string" json:"Title"`
}

func NewArtifactsIdentifier() *ArtifactsIdentifier {
	return &ArtifactsIdentifier{}
}

func (p *ArtifactsIdentifier) InitDefault() {
}

func (p *ArtifactsIdentifier) GetCodeID() (v string) {
	return p.CodeID
}

func (p *ArtifactsIdentifier) GetCodeVersion() (v string) {
	return p.CodeVersion
}

func (p *ArtifactsIdentifier) GetTitle() (v string) {
	return p.Title
}
func (p *ArtifactsIdentifier) SetCodeID(val string) {
	p.CodeID = val
}
func (p *ArtifactsIdentifier) SetCodeVersion(val string) {
	p.CodeVersion = val
}
func (p *ArtifactsIdentifier) SetTitle(val string) {
	p.Title = val
}

var fieldIDToName_ArtifactsIdentifier = map[int16]string{
	1: "CodeID",
	2: "CodeVersion",
	3: "Title",
}

func (p *ArtifactsIdentifier) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetCodeID bool = false
	var issetCodeVersion bool = false
	var issetTitle bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetCodeID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetCodeVersion = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetTitle = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetCodeID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetCodeVersion {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetTitle {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ArtifactsIdentifier[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ArtifactsIdentifier[fieldId]))
}

func (p *ArtifactsIdentifier) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CodeID = _field
	return nil
}
func (p *ArtifactsIdentifier) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CodeVersion = _field
	return nil
}
func (p *ArtifactsIdentifier) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Title = _field
	return nil
}

func (p *ArtifactsIdentifier) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ArtifactsIdentifier"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ArtifactsIdentifier) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CodeID", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CodeID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ArtifactsIdentifier) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CodeVersion", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CodeVersion); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ArtifactsIdentifier) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Title", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Title); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ArtifactsIdentifier) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ArtifactsIdentifier(%+v)", *p)

}

func (p *ArtifactsIdentifier) DeepEqual(ano *ArtifactsIdentifier) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.CodeID) {
		return false
	}
	if !p.Field2DeepEqual(ano.CodeVersion) {
		return false
	}
	if !p.Field3DeepEqual(ano.Title) {
		return false
	}
	return true
}

func (p *ArtifactsIdentifier) Field1DeepEqual(src string) bool {

	if strings.Compare(p.CodeID, src) != 0 {
		return false
	}
	return true
}
func (p *ArtifactsIdentifier) Field2DeepEqual(src string) bool {

	if strings.Compare(p.CodeVersion, src) != 0 {
		return false
	}
	return true
}
func (p *ArtifactsIdentifier) Field3DeepEqual(src string) bool {

	if strings.Compare(p.Title, src) != 0 {
		return false
	}
	return true
}

type GetCodeResourceDownloadURLRequest struct {
	UserID                      int64                    `thrift:"UserID,1,required" frugal:"1,required,i64" json:"UserID"`
	Type                        CodeResourceDownloadType `thrift:"Type,2,required" frugal:"2,required,CodeResourceDownloadType" json:"Type"`
	MultiFileContextIdentifiers []*ContextIdentifier     `thrift:"MultiFileContextIdentifiers,3,optional" frugal:"3,optional,list<ContextIdentifier>" json:"MultiFileContextIdentifiers,omitempty"`
	DirectoryContextIdentifier  *ContextIdentifier       `thrift:"DirectoryContextIdentifier,4,optional" frugal:"4,optional,ContextIdentifier" json:"DirectoryContextIdentifier,omitempty"`
	ArtifactsIdentifier         *ArtifactsIdentifier     `thrift:"ArtifactsIdentifier,5,optional" frugal:"5,optional,ArtifactsIdentifier" json:"ArtifactsIdentifier,omitempty"`
	SingleFileContextIdentifier *ContextIdentifier       `thrift:"SingleFileContextIdentifier,6,optional" frugal:"6,optional,ContextIdentifier" json:"SingleFileContextIdentifier,omitempty"`
}

func NewGetCodeResourceDownloadURLRequest() *GetCodeResourceDownloadURLRequest {
	return &GetCodeResourceDownloadURLRequest{}
}

func (p *GetCodeResourceDownloadURLRequest) InitDefault() {
}

func (p *GetCodeResourceDownloadURLRequest) GetUserID() (v int64) {
	return p.UserID
}

func (p *GetCodeResourceDownloadURLRequest) GetType() (v CodeResourceDownloadType) {
	return p.Type
}

var GetCodeResourceDownloadURLRequest_MultiFileContextIdentifiers_DEFAULT []*ContextIdentifier

func (p *GetCodeResourceDownloadURLRequest) GetMultiFileContextIdentifiers() (v []*ContextIdentifier) {
	if !p.IsSetMultiFileContextIdentifiers() {
		return GetCodeResourceDownloadURLRequest_MultiFileContextIdentifiers_DEFAULT
	}
	return p.MultiFileContextIdentifiers
}

var GetCodeResourceDownloadURLRequest_DirectoryContextIdentifier_DEFAULT *ContextIdentifier

func (p *GetCodeResourceDownloadURLRequest) GetDirectoryContextIdentifier() (v *ContextIdentifier) {
	if !p.IsSetDirectoryContextIdentifier() {
		return GetCodeResourceDownloadURLRequest_DirectoryContextIdentifier_DEFAULT
	}
	return p.DirectoryContextIdentifier
}

var GetCodeResourceDownloadURLRequest_ArtifactsIdentifier_DEFAULT *ArtifactsIdentifier

func (p *GetCodeResourceDownloadURLRequest) GetArtifactsIdentifier() (v *ArtifactsIdentifier) {
	if !p.IsSetArtifactsIdentifier() {
		return GetCodeResourceDownloadURLRequest_ArtifactsIdentifier_DEFAULT
	}
	return p.ArtifactsIdentifier
}

var GetCodeResourceDownloadURLRequest_SingleFileContextIdentifier_DEFAULT *ContextIdentifier

func (p *GetCodeResourceDownloadURLRequest) GetSingleFileContextIdentifier() (v *ContextIdentifier) {
	if !p.IsSetSingleFileContextIdentifier() {
		return GetCodeResourceDownloadURLRequest_SingleFileContextIdentifier_DEFAULT
	}
	return p.SingleFileContextIdentifier
}
func (p *GetCodeResourceDownloadURLRequest) SetUserID(val int64) {
	p.UserID = val
}
func (p *GetCodeResourceDownloadURLRequest) SetType(val CodeResourceDownloadType) {
	p.Type = val
}
func (p *GetCodeResourceDownloadURLRequest) SetMultiFileContextIdentifiers(val []*ContextIdentifier) {
	p.MultiFileContextIdentifiers = val
}
func (p *GetCodeResourceDownloadURLRequest) SetDirectoryContextIdentifier(val *ContextIdentifier) {
	p.DirectoryContextIdentifier = val
}
func (p *GetCodeResourceDownloadURLRequest) SetArtifactsIdentifier(val *ArtifactsIdentifier) {
	p.ArtifactsIdentifier = val
}
func (p *GetCodeResourceDownloadURLRequest) SetSingleFileContextIdentifier(val *ContextIdentifier) {
	p.SingleFileContextIdentifier = val
}

var fieldIDToName_GetCodeResourceDownloadURLRequest = map[int16]string{
	1: "UserID",
	2: "Type",
	3: "MultiFileContextIdentifiers",
	4: "DirectoryContextIdentifier",
	5: "ArtifactsIdentifier",
	6: "SingleFileContextIdentifier",
}

func (p *GetCodeResourceDownloadURLRequest) IsSetMultiFileContextIdentifiers() bool {
	return p.MultiFileContextIdentifiers != nil
}

func (p *GetCodeResourceDownloadURLRequest) IsSetDirectoryContextIdentifier() bool {
	return p.DirectoryContextIdentifier != nil
}

func (p *GetCodeResourceDownloadURLRequest) IsSetArtifactsIdentifier() bool {
	return p.ArtifactsIdentifier != nil
}

func (p *GetCodeResourceDownloadURLRequest) IsSetSingleFileContextIdentifier() bool {
	return p.SingleFileContextIdentifier != nil
}

func (p *GetCodeResourceDownloadURLRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetUserID bool = false
	var issetType bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetUserID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetUserID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetType {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetCodeResourceDownloadURLRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_GetCodeResourceDownloadURLRequest[fieldId]))
}

func (p *GetCodeResourceDownloadURLRequest) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UserID = _field
	return nil
}
func (p *GetCodeResourceDownloadURLRequest) ReadField2(iprot thrift.TProtocol) error {

	var _field CodeResourceDownloadType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = CodeResourceDownloadType(v)
	}
	p.Type = _field
	return nil
}
func (p *GetCodeResourceDownloadURLRequest) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*ContextIdentifier, 0, size)
	values := make([]ContextIdentifier, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.MultiFileContextIdentifiers = _field
	return nil
}
func (p *GetCodeResourceDownloadURLRequest) ReadField4(iprot thrift.TProtocol) error {
	_field := NewContextIdentifier()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.DirectoryContextIdentifier = _field
	return nil
}
func (p *GetCodeResourceDownloadURLRequest) ReadField5(iprot thrift.TProtocol) error {
	_field := NewArtifactsIdentifier()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.ArtifactsIdentifier = _field
	return nil
}
func (p *GetCodeResourceDownloadURLRequest) ReadField6(iprot thrift.TProtocol) error {
	_field := NewContextIdentifier()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.SingleFileContextIdentifier = _field
	return nil
}

func (p *GetCodeResourceDownloadURLRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetCodeResourceDownloadURLRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetCodeResourceDownloadURLRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("UserID", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.UserID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *GetCodeResourceDownloadURLRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Type", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.Type)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *GetCodeResourceDownloadURLRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetMultiFileContextIdentifiers() {
		if err = oprot.WriteFieldBegin("MultiFileContextIdentifiers", thrift.LIST, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.MultiFileContextIdentifiers)); err != nil {
			return err
		}
		for _, v := range p.MultiFileContextIdentifiers {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *GetCodeResourceDownloadURLRequest) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetDirectoryContextIdentifier() {
		if err = oprot.WriteFieldBegin("DirectoryContextIdentifier", thrift.STRUCT, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.DirectoryContextIdentifier.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *GetCodeResourceDownloadURLRequest) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetArtifactsIdentifier() {
		if err = oprot.WriteFieldBegin("ArtifactsIdentifier", thrift.STRUCT, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.ArtifactsIdentifier.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *GetCodeResourceDownloadURLRequest) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetSingleFileContextIdentifier() {
		if err = oprot.WriteFieldBegin("SingleFileContextIdentifier", thrift.STRUCT, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.SingleFileContextIdentifier.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *GetCodeResourceDownloadURLRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetCodeResourceDownloadURLRequest(%+v)", *p)

}

func (p *GetCodeResourceDownloadURLRequest) DeepEqual(ano *GetCodeResourceDownloadURLRequest) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.UserID) {
		return false
	}
	if !p.Field2DeepEqual(ano.Type) {
		return false
	}
	if !p.Field3DeepEqual(ano.MultiFileContextIdentifiers) {
		return false
	}
	if !p.Field4DeepEqual(ano.DirectoryContextIdentifier) {
		return false
	}
	if !p.Field5DeepEqual(ano.ArtifactsIdentifier) {
		return false
	}
	if !p.Field6DeepEqual(ano.SingleFileContextIdentifier) {
		return false
	}
	return true
}

func (p *GetCodeResourceDownloadURLRequest) Field1DeepEqual(src int64) bool {

	if p.UserID != src {
		return false
	}
	return true
}
func (p *GetCodeResourceDownloadURLRequest) Field2DeepEqual(src CodeResourceDownloadType) bool {

	if p.Type != src {
		return false
	}
	return true
}
func (p *GetCodeResourceDownloadURLRequest) Field3DeepEqual(src []*ContextIdentifier) bool {

	if len(p.MultiFileContextIdentifiers) != len(src) {
		return false
	}
	for i, v := range p.MultiFileContextIdentifiers {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *GetCodeResourceDownloadURLRequest) Field4DeepEqual(src *ContextIdentifier) bool {

	if !p.DirectoryContextIdentifier.DeepEqual(src) {
		return false
	}
	return true
}
func (p *GetCodeResourceDownloadURLRequest) Field5DeepEqual(src *ArtifactsIdentifier) bool {

	if !p.ArtifactsIdentifier.DeepEqual(src) {
		return false
	}
	return true
}
func (p *GetCodeResourceDownloadURLRequest) Field6DeepEqual(src *ContextIdentifier) bool {

	if !p.SingleFileContextIdentifier.DeepEqual(src) {
		return false
	}
	return true
}

type GetCodeResourceDownloadURLResponse struct {
	URL      string         `thrift:"URL,1" frugal:"1,default,string" json:"URL"`
	Name     string         `thrift:"Name,2" frugal:"2,default,string" json:"Name"`
	BaseResp *base.BaseResp `thrift:"BaseResp,255" frugal:"255,default,base.BaseResp" json:"BaseResp"`
}

func NewGetCodeResourceDownloadURLResponse() *GetCodeResourceDownloadURLResponse {
	return &GetCodeResourceDownloadURLResponse{}
}

func (p *GetCodeResourceDownloadURLResponse) InitDefault() {
}

func (p *GetCodeResourceDownloadURLResponse) GetURL() (v string) {
	return p.URL
}

func (p *GetCodeResourceDownloadURLResponse) GetName() (v string) {
	return p.Name
}

var GetCodeResourceDownloadURLResponse_BaseResp_DEFAULT *base.BaseResp

func (p *GetCodeResourceDownloadURLResponse) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return GetCodeResourceDownloadURLResponse_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *GetCodeResourceDownloadURLResponse) SetURL(val string) {
	p.URL = val
}
func (p *GetCodeResourceDownloadURLResponse) SetName(val string) {
	p.Name = val
}
func (p *GetCodeResourceDownloadURLResponse) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_GetCodeResourceDownloadURLResponse = map[int16]string{
	1:   "URL",
	2:   "Name",
	255: "BaseResp",
}

func (p *GetCodeResourceDownloadURLResponse) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *GetCodeResourceDownloadURLResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetCodeResourceDownloadURLResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *GetCodeResourceDownloadURLResponse) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.URL = _field
	return nil
}
func (p *GetCodeResourceDownloadURLResponse) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Name = _field
	return nil
}
func (p *GetCodeResourceDownloadURLResponse) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *GetCodeResourceDownloadURLResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetCodeResourceDownloadURLResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetCodeResourceDownloadURLResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("URL", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.URL); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *GetCodeResourceDownloadURLResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Name", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Name); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *GetCodeResourceDownloadURLResponse) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.BaseResp.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *GetCodeResourceDownloadURLResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetCodeResourceDownloadURLResponse(%+v)", *p)

}

func (p *GetCodeResourceDownloadURLResponse) DeepEqual(ano *GetCodeResourceDownloadURLResponse) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.URL) {
		return false
	}
	if !p.Field2DeepEqual(ano.Name) {
		return false
	}
	if !p.Field255DeepEqual(ano.BaseResp) {
		return false
	}
	return true
}

func (p *GetCodeResourceDownloadURLResponse) Field1DeepEqual(src string) bool {

	if strings.Compare(p.URL, src) != 0 {
		return false
	}
	return true
}
func (p *GetCodeResourceDownloadURLResponse) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Name, src) != 0 {
		return false
	}
	return true
}
func (p *GetCodeResourceDownloadURLResponse) Field255DeepEqual(src *base.BaseResp) bool {

	if !p.BaseResp.DeepEqual(src) {
		return false
	}
	return true
}

type DesensitizeContextRequest struct {
	Type         ResourceType `thrift:"Type,1,required" frugal:"1,required,ResourceType" json:"Type"`
	ResourceKeys []string     `thrift:"ResourceKeys,2,required" frugal:"2,required,list<string>" json:"ResourceKeys"`
}

func NewDesensitizeContextRequest() *DesensitizeContextRequest {
	return &DesensitizeContextRequest{}
}

func (p *DesensitizeContextRequest) InitDefault() {
}

func (p *DesensitizeContextRequest) GetType() (v ResourceType) {
	return p.Type
}

func (p *DesensitizeContextRequest) GetResourceKeys() (v []string) {
	return p.ResourceKeys
}
func (p *DesensitizeContextRequest) SetType(val ResourceType) {
	p.Type = val
}
func (p *DesensitizeContextRequest) SetResourceKeys(val []string) {
	p.ResourceKeys = val
}

var fieldIDToName_DesensitizeContextRequest = map[int16]string{
	1: "Type",
	2: "ResourceKeys",
}

func (p *DesensitizeContextRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetType bool = false
	var issetResourceKeys bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetResourceKeys = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetType {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetResourceKeys {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DesensitizeContextRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DesensitizeContextRequest[fieldId]))
}

func (p *DesensitizeContextRequest) ReadField1(iprot thrift.TProtocol) error {

	var _field ResourceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = ResourceType(v)
	}
	p.Type = _field
	return nil
}
func (p *DesensitizeContextRequest) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ResourceKeys = _field
	return nil
}

func (p *DesensitizeContextRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("DesensitizeContextRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DesensitizeContextRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Type", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.Type)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *DesensitizeContextRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ResourceKeys", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.ResourceKeys)); err != nil {
		return err
	}
	for _, v := range p.ResourceKeys {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DesensitizeContextRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DesensitizeContextRequest(%+v)", *p)

}

func (p *DesensitizeContextRequest) DeepEqual(ano *DesensitizeContextRequest) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Type) {
		return false
	}
	if !p.Field2DeepEqual(ano.ResourceKeys) {
		return false
	}
	return true
}

func (p *DesensitizeContextRequest) Field1DeepEqual(src ResourceType) bool {

	if p.Type != src {
		return false
	}
	return true
}
func (p *DesensitizeContextRequest) Field2DeepEqual(src []string) bool {

	if len(p.ResourceKeys) != len(src) {
		return false
	}
	for i, v := range p.ResourceKeys {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}

type DesensitizeContextResponse struct {
	DesensitizedContextID           string         `thrift:"DesensitizedContextID,1" frugal:"1,default,string" json:"DesensitizedContextID"`
	DesensitizedContextDownloadLink string         `thrift:"DesensitizedContextDownloadLink,2" frugal:"2,default,string" json:"DesensitizedContextDownloadLink"`
	BaseResp                        *base.BaseResp `thrift:"BaseResp,255" frugal:"255,default,base.BaseResp" json:"BaseResp"`
}

func NewDesensitizeContextResponse() *DesensitizeContextResponse {
	return &DesensitizeContextResponse{}
}

func (p *DesensitizeContextResponse) InitDefault() {
}

func (p *DesensitizeContextResponse) GetDesensitizedContextID() (v string) {
	return p.DesensitizedContextID
}

func (p *DesensitizeContextResponse) GetDesensitizedContextDownloadLink() (v string) {
	return p.DesensitizedContextDownloadLink
}

var DesensitizeContextResponse_BaseResp_DEFAULT *base.BaseResp

func (p *DesensitizeContextResponse) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return DesensitizeContextResponse_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *DesensitizeContextResponse) SetDesensitizedContextID(val string) {
	p.DesensitizedContextID = val
}
func (p *DesensitizeContextResponse) SetDesensitizedContextDownloadLink(val string) {
	p.DesensitizedContextDownloadLink = val
}
func (p *DesensitizeContextResponse) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_DesensitizeContextResponse = map[int16]string{
	1:   "DesensitizedContextID",
	2:   "DesensitizedContextDownloadLink",
	255: "BaseResp",
}

func (p *DesensitizeContextResponse) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *DesensitizeContextResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DesensitizeContextResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DesensitizeContextResponse) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DesensitizedContextID = _field
	return nil
}
func (p *DesensitizeContextResponse) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DesensitizedContextDownloadLink = _field
	return nil
}
func (p *DesensitizeContextResponse) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *DesensitizeContextResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("DesensitizeContextResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DesensitizeContextResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DesensitizedContextID", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DesensitizedContextID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *DesensitizeContextResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DesensitizedContextDownloadLink", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DesensitizedContextDownloadLink); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *DesensitizeContextResponse) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.BaseResp.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *DesensitizeContextResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DesensitizeContextResponse(%+v)", *p)

}

func (p *DesensitizeContextResponse) DeepEqual(ano *DesensitizeContextResponse) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.DesensitizedContextID) {
		return false
	}
	if !p.Field2DeepEqual(ano.DesensitizedContextDownloadLink) {
		return false
	}
	if !p.Field255DeepEqual(ano.BaseResp) {
		return false
	}
	return true
}

func (p *DesensitizeContextResponse) Field1DeepEqual(src string) bool {

	if strings.Compare(p.DesensitizedContextID, src) != 0 {
		return false
	}
	return true
}
func (p *DesensitizeContextResponse) Field2DeepEqual(src string) bool {

	if strings.Compare(p.DesensitizedContextDownloadLink, src) != 0 {
		return false
	}
	return true
}
func (p *DesensitizeContextResponse) Field255DeepEqual(src *base.BaseResp) bool {

	if !p.BaseResp.DeepEqual(src) {
		return false
	}
	return true
}

type BatchCheckFilePathRequest struct {
	Identifier   *ContextIdentifier `thrift:"Identifier,1,required" frugal:"1,required,ContextIdentifier" json:"Identifier"`
	FilePathList []string           `thrift:"FilePathList,2,required" frugal:"2,required,list<string>" json:"FilePathList"`
}

func NewBatchCheckFilePathRequest() *BatchCheckFilePathRequest {
	return &BatchCheckFilePathRequest{}
}

func (p *BatchCheckFilePathRequest) InitDefault() {
}

var BatchCheckFilePathRequest_Identifier_DEFAULT *ContextIdentifier

func (p *BatchCheckFilePathRequest) GetIdentifier() (v *ContextIdentifier) {
	if !p.IsSetIdentifier() {
		return BatchCheckFilePathRequest_Identifier_DEFAULT
	}
	return p.Identifier
}

func (p *BatchCheckFilePathRequest) GetFilePathList() (v []string) {
	return p.FilePathList
}
func (p *BatchCheckFilePathRequest) SetIdentifier(val *ContextIdentifier) {
	p.Identifier = val
}
func (p *BatchCheckFilePathRequest) SetFilePathList(val []string) {
	p.FilePathList = val
}

var fieldIDToName_BatchCheckFilePathRequest = map[int16]string{
	1: "Identifier",
	2: "FilePathList",
}

func (p *BatchCheckFilePathRequest) IsSetIdentifier() bool {
	return p.Identifier != nil
}

func (p *BatchCheckFilePathRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetIdentifier bool = false
	var issetFilePathList bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetIdentifier = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetFilePathList = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetIdentifier {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetFilePathList {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_BatchCheckFilePathRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_BatchCheckFilePathRequest[fieldId]))
}

func (p *BatchCheckFilePathRequest) ReadField1(iprot thrift.TProtocol) error {
	_field := NewContextIdentifier()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Identifier = _field
	return nil
}
func (p *BatchCheckFilePathRequest) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.FilePathList = _field
	return nil
}

func (p *BatchCheckFilePathRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("BatchCheckFilePathRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *BatchCheckFilePathRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Identifier", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Identifier.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *BatchCheckFilePathRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("FilePathList", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.FilePathList)); err != nil {
		return err
	}
	for _, v := range p.FilePathList {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *BatchCheckFilePathRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BatchCheckFilePathRequest(%+v)", *p)

}

func (p *BatchCheckFilePathRequest) DeepEqual(ano *BatchCheckFilePathRequest) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Identifier) {
		return false
	}
	if !p.Field2DeepEqual(ano.FilePathList) {
		return false
	}
	return true
}

func (p *BatchCheckFilePathRequest) Field1DeepEqual(src *ContextIdentifier) bool {

	if !p.Identifier.DeepEqual(src) {
		return false
	}
	return true
}
func (p *BatchCheckFilePathRequest) Field2DeepEqual(src []string) bool {

	if len(p.FilePathList) != len(src) {
		return false
	}
	for i, v := range p.FilePathList {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}

type BatchCheckFilePathResponse struct {
	CheckResultList []*FilePathCheckResultData `thrift:"CheckResultList,1" frugal:"1,default,list<FilePathCheckResultData>" json:"CheckResultList"`
	BaseResp        *base.BaseResp             `thrift:"BaseResp,255" frugal:"255,default,base.BaseResp" json:"BaseResp"`
}

func NewBatchCheckFilePathResponse() *BatchCheckFilePathResponse {
	return &BatchCheckFilePathResponse{}
}

func (p *BatchCheckFilePathResponse) InitDefault() {
}

func (p *BatchCheckFilePathResponse) GetCheckResultList() (v []*FilePathCheckResultData) {
	return p.CheckResultList
}

var BatchCheckFilePathResponse_BaseResp_DEFAULT *base.BaseResp

func (p *BatchCheckFilePathResponse) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return BatchCheckFilePathResponse_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *BatchCheckFilePathResponse) SetCheckResultList(val []*FilePathCheckResultData) {
	p.CheckResultList = val
}
func (p *BatchCheckFilePathResponse) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_BatchCheckFilePathResponse = map[int16]string{
	1:   "CheckResultList",
	255: "BaseResp",
}

func (p *BatchCheckFilePathResponse) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *BatchCheckFilePathResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_BatchCheckFilePathResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *BatchCheckFilePathResponse) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*FilePathCheckResultData, 0, size)
	values := make([]FilePathCheckResultData, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.CheckResultList = _field
	return nil
}
func (p *BatchCheckFilePathResponse) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *BatchCheckFilePathResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("BatchCheckFilePathResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *BatchCheckFilePathResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CheckResultList", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.CheckResultList)); err != nil {
		return err
	}
	for _, v := range p.CheckResultList {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *BatchCheckFilePathResponse) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.BaseResp.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *BatchCheckFilePathResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BatchCheckFilePathResponse(%+v)", *p)

}

func (p *BatchCheckFilePathResponse) DeepEqual(ano *BatchCheckFilePathResponse) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.CheckResultList) {
		return false
	}
	if !p.Field255DeepEqual(ano.BaseResp) {
		return false
	}
	return true
}

func (p *BatchCheckFilePathResponse) Field1DeepEqual(src []*FilePathCheckResultData) bool {

	if len(p.CheckResultList) != len(src) {
		return false
	}
	for i, v := range p.CheckResultList {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *BatchCheckFilePathResponse) Field255DeepEqual(src *base.BaseResp) bool {

	if !p.BaseResp.DeepEqual(src) {
		return false
	}
	return true
}

type FilePathCheckResultData struct {
	FullPath   string `thrift:"FullPath,1" frugal:"1,default,string" json:"FullPath"`
	OriginPath string `thrift:"OriginPath,2" frugal:"2,default,string" json:"OriginPath"`
}

func NewFilePathCheckResultData() *FilePathCheckResultData {
	return &FilePathCheckResultData{}
}

func (p *FilePathCheckResultData) InitDefault() {
}

func (p *FilePathCheckResultData) GetFullPath() (v string) {
	return p.FullPath
}

func (p *FilePathCheckResultData) GetOriginPath() (v string) {
	return p.OriginPath
}
func (p *FilePathCheckResultData) SetFullPath(val string) {
	p.FullPath = val
}
func (p *FilePathCheckResultData) SetOriginPath(val string) {
	p.OriginPath = val
}

var fieldIDToName_FilePathCheckResultData = map[int16]string{
	1: "FullPath",
	2: "OriginPath",
}

func (p *FilePathCheckResultData) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FilePathCheckResultData[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *FilePathCheckResultData) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.FullPath = _field
	return nil
}
func (p *FilePathCheckResultData) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.OriginPath = _field
	return nil
}

func (p *FilePathCheckResultData) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("FilePathCheckResultData"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *FilePathCheckResultData) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("FullPath", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.FullPath); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *FilePathCheckResultData) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("OriginPath", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.OriginPath); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *FilePathCheckResultData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FilePathCheckResultData(%+v)", *p)

}

func (p *FilePathCheckResultData) DeepEqual(ano *FilePathCheckResultData) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.FullPath) {
		return false
	}
	if !p.Field2DeepEqual(ano.OriginPath) {
		return false
	}
	return true
}

func (p *FilePathCheckResultData) Field1DeepEqual(src string) bool {

	if strings.Compare(p.FullPath, src) != 0 {
		return false
	}
	return true
}
func (p *FilePathCheckResultData) Field2DeepEqual(src string) bool {

	if strings.Compare(p.OriginPath, src) != 0 {
		return false
	}
	return true
}
