// Code generated by thriftgo (0.4.2). DO NOT EDIT.

package codeassist

import (
	"bytes"
	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/base"
	"database/sql"
	"database/sql/driver"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type CodeType int64

const (
	CodeType_Artifacts CodeType = 1
	CodeType_Raw       CodeType = 2
)

func (p CodeType) String() string {
	switch p {
	case CodeType_Artifacts:
		return "Artifacts"
	case CodeType_Raw:
		return "Raw"
	}
	return "<UNSET>"
}

func CodeTypeFromString(s string) (CodeType, error) {
	switch s {
	case "Artifacts":
		return CodeType_Artifacts, nil
	case "Raw":
		return CodeType_Raw, nil
	}
	return CodeType(0), fmt.Errorf("not a valid CodeType string")
}

func CodeTypePtr(v CodeType) *CodeType { return &v }
func (p *CodeType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = CodeType(result.Int64)
	return
}

func (p *CodeType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type TemplateNodeType int64

const (
	TemplateNodeType_Unknown   TemplateNodeType = 0
	TemplateNodeType_Directory TemplateNodeType = 1
	TemplateNodeType_File      TemplateNodeType = 2
)

func (p TemplateNodeType) String() string {
	switch p {
	case TemplateNodeType_Unknown:
		return "Unknown"
	case TemplateNodeType_Directory:
		return "Directory"
	case TemplateNodeType_File:
		return "File"
	}
	return "<UNSET>"
}

func TemplateNodeTypeFromString(s string) (TemplateNodeType, error) {
	switch s {
	case "Unknown":
		return TemplateNodeType_Unknown, nil
	case "Directory":
		return TemplateNodeType_Directory, nil
	case "File":
		return TemplateNodeType_File, nil
	}
	return TemplateNodeType(0), fmt.Errorf("not a valid TemplateNodeType string")
}

func TemplateNodeTypePtr(v TemplateNodeType) *TemplateNodeType { return &v }
func (p *TemplateNodeType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = TemplateNodeType(result.Int64)
	return
}

func (p *TemplateNodeType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type CompileStatus int64

const (
	CompileStatus_StatusDefault   CompileStatus = 0
	CompileStatus_StatusCompiling CompileStatus = 1
	CompileStatus_StatusFail      CompileStatus = 2
	CompileStatus_StatusSuccess   CompileStatus = 3
)

func (p CompileStatus) String() string {
	switch p {
	case CompileStatus_StatusDefault:
		return "StatusDefault"
	case CompileStatus_StatusCompiling:
		return "StatusCompiling"
	case CompileStatus_StatusFail:
		return "StatusFail"
	case CompileStatus_StatusSuccess:
		return "StatusSuccess"
	}
	return "<UNSET>"
}

func CompileStatusFromString(s string) (CompileStatus, error) {
	switch s {
	case "StatusDefault":
		return CompileStatus_StatusDefault, nil
	case "StatusCompiling":
		return CompileStatus_StatusCompiling, nil
	case "StatusFail":
		return CompileStatus_StatusFail, nil
	case "StatusSuccess":
		return CompileStatus_StatusSuccess, nil
	}
	return CompileStatus(0), fmt.Errorf("not a valid CompileStatus string")
}

func CompileStatusPtr(v CompileStatus) *CompileStatus { return &v }
func (p *CompileStatus) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = CompileStatus(result.Int64)
	return
}

func (p *CompileStatus) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type SandboxFunctionType int64

const (
	SandboxFunctionType_TypeRun          SandboxFunctionType = 1
	SandboxFunctionType_TypeCompile      SandboxFunctionType = 2
	SandboxFunctionType_TypeAgent        SandboxFunctionType = 3
	SandboxFunctionType_TypeProcessData  SandboxFunctionType = 4
	SandboxFunctionType_TypeDeepResearch SandboxFunctionType = 5
)

func (p SandboxFunctionType) String() string {
	switch p {
	case SandboxFunctionType_TypeRun:
		return "TypeRun"
	case SandboxFunctionType_TypeCompile:
		return "TypeCompile"
	case SandboxFunctionType_TypeAgent:
		return "TypeAgent"
	case SandboxFunctionType_TypeProcessData:
		return "TypeProcessData"
	case SandboxFunctionType_TypeDeepResearch:
		return "TypeDeepResearch"
	}
	return "<UNSET>"
}

func SandboxFunctionTypeFromString(s string) (SandboxFunctionType, error) {
	switch s {
	case "TypeRun":
		return SandboxFunctionType_TypeRun, nil
	case "TypeCompile":
		return SandboxFunctionType_TypeCompile, nil
	case "TypeAgent":
		return SandboxFunctionType_TypeAgent, nil
	case "TypeProcessData":
		return SandboxFunctionType_TypeProcessData, nil
	case "TypeDeepResearch":
		return SandboxFunctionType_TypeDeepResearch, nil
	}
	return SandboxFunctionType(0), fmt.Errorf("not a valid SandboxFunctionType string")
}

func SandboxFunctionTypePtr(v SandboxFunctionType) *SandboxFunctionType { return &v }
func (p *SandboxFunctionType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = SandboxFunctionType(result.Int64)
	return
}

func (p *SandboxFunctionType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type SandboxType int64

const (
	SandboxType_SandboxTypeExclusive SandboxType = 0
	SandboxType_SandboxTypeShare     SandboxType = 1
)

func (p SandboxType) String() string {
	switch p {
	case SandboxType_SandboxTypeExclusive:
		return "SandboxTypeExclusive"
	case SandboxType_SandboxTypeShare:
		return "SandboxTypeShare"
	}
	return "<UNSET>"
}

func SandboxTypeFromString(s string) (SandboxType, error) {
	switch s {
	case "SandboxTypeExclusive":
		return SandboxType_SandboxTypeExclusive, nil
	case "SandboxTypeShare":
		return SandboxType_SandboxTypeShare, nil
	}
	return SandboxType(0), fmt.Errorf("not a valid SandboxType string")
}

func SandboxTypePtr(v SandboxType) *SandboxType { return &v }
func (p *SandboxType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = SandboxType(result.Int64)
	return
}

func (p *SandboxType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type CodeExecutableRequest struct {
	Identifier string     `thrift:"Identifier,1,required" frugal:"1,required,string" json:"Identifier"`
	Version    string     `thrift:"Version,2,required" frugal:"2,required,string" json:"Version"`
	MessageID  *int64     `thrift:"MessageID,3,optional" frugal:"3,optional,i64" json:"MessageID,omitempty"`
	CodeFile   *CodeInfo  `thrift:"CodeFile,4,required" frugal:"4,required,CodeInfo" json:"CodeFile"`
	UserID     int64      `thrift:"UserID,5,required" frugal:"5,required,i64" json:"UserID"`
	Base       *base.Base `thrift:"Base,255,optional" frugal:"255,optional,base.Base" json:"Base,omitempty"`
}

func NewCodeExecutableRequest() *CodeExecutableRequest {
	return &CodeExecutableRequest{}
}

func (p *CodeExecutableRequest) InitDefault() {
}

func (p *CodeExecutableRequest) GetIdentifier() (v string) {
	return p.Identifier
}

func (p *CodeExecutableRequest) GetVersion() (v string) {
	return p.Version
}

var CodeExecutableRequest_MessageID_DEFAULT int64

func (p *CodeExecutableRequest) GetMessageID() (v int64) {
	if !p.IsSetMessageID() {
		return CodeExecutableRequest_MessageID_DEFAULT
	}
	return *p.MessageID
}

var CodeExecutableRequest_CodeFile_DEFAULT *CodeInfo

func (p *CodeExecutableRequest) GetCodeFile() (v *CodeInfo) {
	if !p.IsSetCodeFile() {
		return CodeExecutableRequest_CodeFile_DEFAULT
	}
	return p.CodeFile
}

func (p *CodeExecutableRequest) GetUserID() (v int64) {
	return p.UserID
}

var CodeExecutableRequest_Base_DEFAULT *base.Base

func (p *CodeExecutableRequest) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return CodeExecutableRequest_Base_DEFAULT
	}
	return p.Base
}
func (p *CodeExecutableRequest) SetIdentifier(val string) {
	p.Identifier = val
}
func (p *CodeExecutableRequest) SetVersion(val string) {
	p.Version = val
}
func (p *CodeExecutableRequest) SetMessageID(val *int64) {
	p.MessageID = val
}
func (p *CodeExecutableRequest) SetCodeFile(val *CodeInfo) {
	p.CodeFile = val
}
func (p *CodeExecutableRequest) SetUserID(val int64) {
	p.UserID = val
}
func (p *CodeExecutableRequest) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_CodeExecutableRequest = map[int16]string{
	1:   "Identifier",
	2:   "Version",
	3:   "MessageID",
	4:   "CodeFile",
	5:   "UserID",
	255: "Base",
}

func (p *CodeExecutableRequest) IsSetMessageID() bool {
	return p.MessageID != nil
}

func (p *CodeExecutableRequest) IsSetCodeFile() bool {
	return p.CodeFile != nil
}

func (p *CodeExecutableRequest) IsSetBase() bool {
	return p.Base != nil
}

func (p *CodeExecutableRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetIdentifier bool = false
	var issetVersion bool = false
	var issetCodeFile bool = false
	var issetUserID bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetIdentifier = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetVersion = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetCodeFile = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetUserID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetIdentifier {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetVersion {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetCodeFile {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetUserID {
		fieldId = 5
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CodeExecutableRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CodeExecutableRequest[fieldId]))
}

func (p *CodeExecutableRequest) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Identifier = _field
	return nil
}
func (p *CodeExecutableRequest) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Version = _field
	return nil
}
func (p *CodeExecutableRequest) ReadField3(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.MessageID = _field
	return nil
}
func (p *CodeExecutableRequest) ReadField4(iprot thrift.TProtocol) error {
	_field := NewCodeInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.CodeFile = _field
	return nil
}
func (p *CodeExecutableRequest) ReadField5(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UserID = _field
	return nil
}
func (p *CodeExecutableRequest) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *CodeExecutableRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("CodeExecutableRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CodeExecutableRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Identifier", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Identifier); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *CodeExecutableRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Version", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Version); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *CodeExecutableRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetMessageID() {
		if err = oprot.WriteFieldBegin("MessageID", thrift.I64, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.MessageID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *CodeExecutableRequest) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CodeFile", thrift.STRUCT, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.CodeFile.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *CodeExecutableRequest) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("UserID", thrift.I64, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.UserID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *CodeExecutableRequest) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *CodeExecutableRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CodeExecutableRequest(%+v)", *p)

}

func (p *CodeExecutableRequest) DeepEqual(ano *CodeExecutableRequest) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Identifier) {
		return false
	}
	if !p.Field2DeepEqual(ano.Version) {
		return false
	}
	if !p.Field3DeepEqual(ano.MessageID) {
		return false
	}
	if !p.Field4DeepEqual(ano.CodeFile) {
		return false
	}
	if !p.Field5DeepEqual(ano.UserID) {
		return false
	}
	if !p.Field255DeepEqual(ano.Base) {
		return false
	}
	return true
}

func (p *CodeExecutableRequest) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Identifier, src) != 0 {
		return false
	}
	return true
}
func (p *CodeExecutableRequest) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Version, src) != 0 {
		return false
	}
	return true
}
func (p *CodeExecutableRequest) Field3DeepEqual(src *int64) bool {

	if p.MessageID == src {
		return true
	} else if p.MessageID == nil || src == nil {
		return false
	}
	if *p.MessageID != *src {
		return false
	}
	return true
}
func (p *CodeExecutableRequest) Field4DeepEqual(src *CodeInfo) bool {

	if !p.CodeFile.DeepEqual(src) {
		return false
	}
	return true
}
func (p *CodeExecutableRequest) Field5DeepEqual(src int64) bool {

	if p.UserID != src {
		return false
	}
	return true
}
func (p *CodeExecutableRequest) Field255DeepEqual(src *base.Base) bool {

	if !p.Base.DeepEqual(src) {
		return false
	}
	return true
}

type CodeExecutableResponse struct {
	Executable bool           `thrift:"Executable,1,required" frugal:"1,required,bool" json:"Executable"`
	BaseResp   *base.BaseResp `thrift:"BaseResp,255,optional" frugal:"255,optional,base.BaseResp" json:"BaseResp,omitempty"`
}

func NewCodeExecutableResponse() *CodeExecutableResponse {
	return &CodeExecutableResponse{}
}

func (p *CodeExecutableResponse) InitDefault() {
}

func (p *CodeExecutableResponse) GetExecutable() (v bool) {
	return p.Executable
}

var CodeExecutableResponse_BaseResp_DEFAULT *base.BaseResp

func (p *CodeExecutableResponse) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return CodeExecutableResponse_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *CodeExecutableResponse) SetExecutable(val bool) {
	p.Executable = val
}
func (p *CodeExecutableResponse) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_CodeExecutableResponse = map[int16]string{
	1:   "Executable",
	255: "BaseResp",
}

func (p *CodeExecutableResponse) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *CodeExecutableResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetExecutable bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetExecutable = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetExecutable {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CodeExecutableResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CodeExecutableResponse[fieldId]))
}

func (p *CodeExecutableResponse) ReadField1(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Executable = _field
	return nil
}
func (p *CodeExecutableResponse) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *CodeExecutableResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("CodeExecutableResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CodeExecutableResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Executable", thrift.BOOL, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.Executable); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *CodeExecutableResponse) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBaseResp() {
		if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.BaseResp.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *CodeExecutableResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CodeExecutableResponse(%+v)", *p)

}

func (p *CodeExecutableResponse) DeepEqual(ano *CodeExecutableResponse) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Executable) {
		return false
	}
	if !p.Field255DeepEqual(ano.BaseResp) {
		return false
	}
	return true
}

func (p *CodeExecutableResponse) Field1DeepEqual(src bool) bool {

	if p.Executable != src {
		return false
	}
	return true
}
func (p *CodeExecutableResponse) Field255DeepEqual(src *base.BaseResp) bool {

	if !p.BaseResp.DeepEqual(src) {
		return false
	}
	return true
}

type RunCodeRequest struct {
	Identifier   string     `thrift:"Identifier,1,required" frugal:"1,required,string" json:"Identifier"`
	Version      string     `thrift:"Version,2,required" frugal:"2,required,string" json:"Version"`
	ConversionID string     `thrift:"ConversionID,3,required" frugal:"3,required,string" json:"ConversionID"`
	MessageID    *int64     `thrift:"MessageID,4,optional" frugal:"4,optional,i64" json:"MessageID,omitempty"`
	CodeFile     *CodeInfo  `thrift:"CodeFile,5,required" frugal:"5,required,CodeInfo" json:"CodeFile"`
	UserID       int64      `thrift:"UserID,6,required" frugal:"6,required,i64" json:"UserID"`
	Base         *base.Base `thrift:"Base,255,optional" frugal:"255,optional,base.Base" json:"Base,omitempty"`
}

func NewRunCodeRequest() *RunCodeRequest {
	return &RunCodeRequest{}
}

func (p *RunCodeRequest) InitDefault() {
}

func (p *RunCodeRequest) GetIdentifier() (v string) {
	return p.Identifier
}

func (p *RunCodeRequest) GetVersion() (v string) {
	return p.Version
}

func (p *RunCodeRequest) GetConversionID() (v string) {
	return p.ConversionID
}

var RunCodeRequest_MessageID_DEFAULT int64

func (p *RunCodeRequest) GetMessageID() (v int64) {
	if !p.IsSetMessageID() {
		return RunCodeRequest_MessageID_DEFAULT
	}
	return *p.MessageID
}

var RunCodeRequest_CodeFile_DEFAULT *CodeInfo

func (p *RunCodeRequest) GetCodeFile() (v *CodeInfo) {
	if !p.IsSetCodeFile() {
		return RunCodeRequest_CodeFile_DEFAULT
	}
	return p.CodeFile
}

func (p *RunCodeRequest) GetUserID() (v int64) {
	return p.UserID
}

var RunCodeRequest_Base_DEFAULT *base.Base

func (p *RunCodeRequest) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return RunCodeRequest_Base_DEFAULT
	}
	return p.Base
}
func (p *RunCodeRequest) SetIdentifier(val string) {
	p.Identifier = val
}
func (p *RunCodeRequest) SetVersion(val string) {
	p.Version = val
}
func (p *RunCodeRequest) SetConversionID(val string) {
	p.ConversionID = val
}
func (p *RunCodeRequest) SetMessageID(val *int64) {
	p.MessageID = val
}
func (p *RunCodeRequest) SetCodeFile(val *CodeInfo) {
	p.CodeFile = val
}
func (p *RunCodeRequest) SetUserID(val int64) {
	p.UserID = val
}
func (p *RunCodeRequest) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_RunCodeRequest = map[int16]string{
	1:   "Identifier",
	2:   "Version",
	3:   "ConversionID",
	4:   "MessageID",
	5:   "CodeFile",
	6:   "UserID",
	255: "Base",
}

func (p *RunCodeRequest) IsSetMessageID() bool {
	return p.MessageID != nil
}

func (p *RunCodeRequest) IsSetCodeFile() bool {
	return p.CodeFile != nil
}

func (p *RunCodeRequest) IsSetBase() bool {
	return p.Base != nil
}

func (p *RunCodeRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetIdentifier bool = false
	var issetVersion bool = false
	var issetConversionID bool = false
	var issetCodeFile bool = false
	var issetUserID bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetIdentifier = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetVersion = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetConversionID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetCodeFile = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetUserID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetIdentifier {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetVersion {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetConversionID {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetCodeFile {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetUserID {
		fieldId = 6
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_RunCodeRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_RunCodeRequest[fieldId]))
}

func (p *RunCodeRequest) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Identifier = _field
	return nil
}
func (p *RunCodeRequest) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Version = _field
	return nil
}
func (p *RunCodeRequest) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ConversionID = _field
	return nil
}
func (p *RunCodeRequest) ReadField4(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.MessageID = _field
	return nil
}
func (p *RunCodeRequest) ReadField5(iprot thrift.TProtocol) error {
	_field := NewCodeInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.CodeFile = _field
	return nil
}
func (p *RunCodeRequest) ReadField6(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UserID = _field
	return nil
}
func (p *RunCodeRequest) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *RunCodeRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("RunCodeRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *RunCodeRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Identifier", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Identifier); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *RunCodeRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Version", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Version); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *RunCodeRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ConversionID", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ConversionID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *RunCodeRequest) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetMessageID() {
		if err = oprot.WriteFieldBegin("MessageID", thrift.I64, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.MessageID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *RunCodeRequest) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CodeFile", thrift.STRUCT, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.CodeFile.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *RunCodeRequest) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("UserID", thrift.I64, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.UserID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *RunCodeRequest) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *RunCodeRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RunCodeRequest(%+v)", *p)

}

func (p *RunCodeRequest) DeepEqual(ano *RunCodeRequest) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Identifier) {
		return false
	}
	if !p.Field2DeepEqual(ano.Version) {
		return false
	}
	if !p.Field3DeepEqual(ano.ConversionID) {
		return false
	}
	if !p.Field4DeepEqual(ano.MessageID) {
		return false
	}
	if !p.Field5DeepEqual(ano.CodeFile) {
		return false
	}
	if !p.Field6DeepEqual(ano.UserID) {
		return false
	}
	if !p.Field255DeepEqual(ano.Base) {
		return false
	}
	return true
}

func (p *RunCodeRequest) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Identifier, src) != 0 {
		return false
	}
	return true
}
func (p *RunCodeRequest) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Version, src) != 0 {
		return false
	}
	return true
}
func (p *RunCodeRequest) Field3DeepEqual(src string) bool {

	if strings.Compare(p.ConversionID, src) != 0 {
		return false
	}
	return true
}
func (p *RunCodeRequest) Field4DeepEqual(src *int64) bool {

	if p.MessageID == src {
		return true
	} else if p.MessageID == nil || src == nil {
		return false
	}
	if *p.MessageID != *src {
		return false
	}
	return true
}
func (p *RunCodeRequest) Field5DeepEqual(src *CodeInfo) bool {

	if !p.CodeFile.DeepEqual(src) {
		return false
	}
	return true
}
func (p *RunCodeRequest) Field6DeepEqual(src int64) bool {

	if p.UserID != src {
		return false
	}
	return true
}
func (p *RunCodeRequest) Field255DeepEqual(src *base.Base) bool {

	if !p.Base.DeepEqual(src) {
		return false
	}
	return true
}

type CodeInfo struct {
	CodeID      string `thrift:"CodeID,1,required" frugal:"1,required,string" json:"CodeID"`
	CodeVersion string `thrift:"CodeVersion,2,required" frugal:"2,required,string" json:"CodeVersion"`
}

func NewCodeInfo() *CodeInfo {
	return &CodeInfo{}
}

func (p *CodeInfo) InitDefault() {
}

func (p *CodeInfo) GetCodeID() (v string) {
	return p.CodeID
}

func (p *CodeInfo) GetCodeVersion() (v string) {
	return p.CodeVersion
}
func (p *CodeInfo) SetCodeID(val string) {
	p.CodeID = val
}
func (p *CodeInfo) SetCodeVersion(val string) {
	p.CodeVersion = val
}

var fieldIDToName_CodeInfo = map[int16]string{
	1: "CodeID",
	2: "CodeVersion",
}

func (p *CodeInfo) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetCodeID bool = false
	var issetCodeVersion bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetCodeID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetCodeVersion = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetCodeID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetCodeVersion {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CodeInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CodeInfo[fieldId]))
}

func (p *CodeInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CodeID = _field
	return nil
}
func (p *CodeInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CodeVersion = _field
	return nil
}

func (p *CodeInfo) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("CodeInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CodeInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CodeID", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CodeID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *CodeInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CodeVersion", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CodeVersion); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CodeInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CodeInfo(%+v)", *p)

}

func (p *CodeInfo) DeepEqual(ano *CodeInfo) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.CodeID) {
		return false
	}
	if !p.Field2DeepEqual(ano.CodeVersion) {
		return false
	}
	return true
}

func (p *CodeInfo) Field1DeepEqual(src string) bool {

	if strings.Compare(p.CodeID, src) != 0 {
		return false
	}
	return true
}
func (p *CodeInfo) Field2DeepEqual(src string) bool {

	if strings.Compare(p.CodeVersion, src) != 0 {
		return false
	}
	return true
}

type RunCodeResponse struct {
	Data     *RunCodeData   `thrift:"Data,1,optional" frugal:"1,optional,RunCodeData" json:"Data,omitempty"`
	Message  *string        `thrift:"Message,2,optional" frugal:"2,optional,string" json:"Message,omitempty"`
	Code     *int32         `thrift:"Code,3,optional" frugal:"3,optional,i32" json:"Code,omitempty"`
	BaseResp *base.BaseResp `thrift:"BaseResp,255,optional" frugal:"255,optional,base.BaseResp" json:"BaseResp,omitempty"`
}

func NewRunCodeResponse() *RunCodeResponse {
	return &RunCodeResponse{}
}

func (p *RunCodeResponse) InitDefault() {
}

var RunCodeResponse_Data_DEFAULT *RunCodeData

func (p *RunCodeResponse) GetData() (v *RunCodeData) {
	if !p.IsSetData() {
		return RunCodeResponse_Data_DEFAULT
	}
	return p.Data
}

var RunCodeResponse_Message_DEFAULT string

func (p *RunCodeResponse) GetMessage() (v string) {
	if !p.IsSetMessage() {
		return RunCodeResponse_Message_DEFAULT
	}
	return *p.Message
}

var RunCodeResponse_Code_DEFAULT int32

func (p *RunCodeResponse) GetCode() (v int32) {
	if !p.IsSetCode() {
		return RunCodeResponse_Code_DEFAULT
	}
	return *p.Code
}

var RunCodeResponse_BaseResp_DEFAULT *base.BaseResp

func (p *RunCodeResponse) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return RunCodeResponse_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *RunCodeResponse) SetData(val *RunCodeData) {
	p.Data = val
}
func (p *RunCodeResponse) SetMessage(val *string) {
	p.Message = val
}
func (p *RunCodeResponse) SetCode(val *int32) {
	p.Code = val
}
func (p *RunCodeResponse) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_RunCodeResponse = map[int16]string{
	1:   "Data",
	2:   "Message",
	3:   "Code",
	255: "BaseResp",
}

func (p *RunCodeResponse) IsSetData() bool {
	return p.Data != nil
}

func (p *RunCodeResponse) IsSetMessage() bool {
	return p.Message != nil
}

func (p *RunCodeResponse) IsSetCode() bool {
	return p.Code != nil
}

func (p *RunCodeResponse) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *RunCodeResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_RunCodeResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *RunCodeResponse) ReadField1(iprot thrift.TProtocol) error {
	_field := NewRunCodeData()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Data = _field
	return nil
}
func (p *RunCodeResponse) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Message = _field
	return nil
}
func (p *RunCodeResponse) ReadField3(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Code = _field
	return nil
}
func (p *RunCodeResponse) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *RunCodeResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("RunCodeResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *RunCodeResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetData() {
		if err = oprot.WriteFieldBegin("Data", thrift.STRUCT, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Data.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *RunCodeResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetMessage() {
		if err = oprot.WriteFieldBegin("Message", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Message); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *RunCodeResponse) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetCode() {
		if err = oprot.WriteFieldBegin("Code", thrift.I32, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.Code); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *RunCodeResponse) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBaseResp() {
		if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.BaseResp.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *RunCodeResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RunCodeResponse(%+v)", *p)

}

func (p *RunCodeResponse) DeepEqual(ano *RunCodeResponse) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Data) {
		return false
	}
	if !p.Field2DeepEqual(ano.Message) {
		return false
	}
	if !p.Field3DeepEqual(ano.Code) {
		return false
	}
	if !p.Field255DeepEqual(ano.BaseResp) {
		return false
	}
	return true
}

func (p *RunCodeResponse) Field1DeepEqual(src *RunCodeData) bool {

	if !p.Data.DeepEqual(src) {
		return false
	}
	return true
}
func (p *RunCodeResponse) Field2DeepEqual(src *string) bool {

	if p.Message == src {
		return true
	} else if p.Message == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Message, *src) != 0 {
		return false
	}
	return true
}
func (p *RunCodeResponse) Field3DeepEqual(src *int32) bool {

	if p.Code == src {
		return true
	} else if p.Code == nil || src == nil {
		return false
	}
	if *p.Code != *src {
		return false
	}
	return true
}
func (p *RunCodeResponse) Field255DeepEqual(src *base.BaseResp) bool {

	if !p.BaseResp.DeepEqual(src) {
		return false
	}
	return true
}

type RunCodeData struct {
	IsPartial  *bool           `thrift:"IsPartial,1,optional" frugal:"1,optional,bool" json:"IsPartial,omitempty"`
	Result_    *RunCodeResult_ `thrift:"Result,2,optional" frugal:"2,optional,RunCodeResult_" json:"Result,omitempty"`
	IsTruncate *bool           `thrift:"IsTruncate,3,optional" frugal:"3,optional,bool" json:"IsTruncate,omitempty"`
}

func NewRunCodeData() *RunCodeData {
	return &RunCodeData{}
}

func (p *RunCodeData) InitDefault() {
}

var RunCodeData_IsPartial_DEFAULT bool

func (p *RunCodeData) GetIsPartial() (v bool) {
	if !p.IsSetIsPartial() {
		return RunCodeData_IsPartial_DEFAULT
	}
	return *p.IsPartial
}

var RunCodeData_Result__DEFAULT *RunCodeResult_

func (p *RunCodeData) GetResult_() (v *RunCodeResult_) {
	if !p.IsSetResult_() {
		return RunCodeData_Result__DEFAULT
	}
	return p.Result_
}

var RunCodeData_IsTruncate_DEFAULT bool

func (p *RunCodeData) GetIsTruncate() (v bool) {
	if !p.IsSetIsTruncate() {
		return RunCodeData_IsTruncate_DEFAULT
	}
	return *p.IsTruncate
}
func (p *RunCodeData) SetIsPartial(val *bool) {
	p.IsPartial = val
}
func (p *RunCodeData) SetResult_(val *RunCodeResult_) {
	p.Result_ = val
}
func (p *RunCodeData) SetIsTruncate(val *bool) {
	p.IsTruncate = val
}

var fieldIDToName_RunCodeData = map[int16]string{
	1: "IsPartial",
	2: "Result",
	3: "IsTruncate",
}

func (p *RunCodeData) IsSetIsPartial() bool {
	return p.IsPartial != nil
}

func (p *RunCodeData) IsSetResult_() bool {
	return p.Result_ != nil
}

func (p *RunCodeData) IsSetIsTruncate() bool {
	return p.IsTruncate != nil
}

func (p *RunCodeData) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_RunCodeData[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *RunCodeData) ReadField1(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.IsPartial = _field
	return nil
}
func (p *RunCodeData) ReadField2(iprot thrift.TProtocol) error {
	_field := NewRunCodeResult_()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Result_ = _field
	return nil
}
func (p *RunCodeData) ReadField3(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.IsTruncate = _field
	return nil
}

func (p *RunCodeData) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("RunCodeData"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *RunCodeData) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetIsPartial() {
		if err = oprot.WriteFieldBegin("IsPartial", thrift.BOOL, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.IsPartial); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *RunCodeData) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetResult_() {
		if err = oprot.WriteFieldBegin("Result", thrift.STRUCT, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Result_.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *RunCodeData) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetIsTruncate() {
		if err = oprot.WriteFieldBegin("IsTruncate", thrift.BOOL, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.IsTruncate); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *RunCodeData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RunCodeData(%+v)", *p)

}

func (p *RunCodeData) DeepEqual(ano *RunCodeData) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.IsPartial) {
		return false
	}
	if !p.Field2DeepEqual(ano.Result_) {
		return false
	}
	if !p.Field3DeepEqual(ano.IsTruncate) {
		return false
	}
	return true
}

func (p *RunCodeData) Field1DeepEqual(src *bool) bool {

	if p.IsPartial == src {
		return true
	} else if p.IsPartial == nil || src == nil {
		return false
	}
	if *p.IsPartial != *src {
		return false
	}
	return true
}
func (p *RunCodeData) Field2DeepEqual(src *RunCodeResult_) bool {

	if !p.Result_.DeepEqual(src) {
		return false
	}
	return true
}
func (p *RunCodeData) Field3DeepEqual(src *bool) bool {

	if p.IsTruncate == src {
		return true
	} else if p.IsTruncate == nil || src == nil {
		return false
	}
	if *p.IsTruncate != *src {
		return false
	}
	return true
}

type RunCodeResult_ struct {
	CodeOutputResult_ []*RunCodeOutputResult_ `thrift:"CodeOutputResult,1,optional" frugal:"1,optional,list<RunCodeOutputResult_>" json:"CodeOutputResult,omitempty"`
}

func NewRunCodeResult_() *RunCodeResult_ {
	return &RunCodeResult_{}
}

func (p *RunCodeResult_) InitDefault() {
}

var RunCodeResult__CodeOutputResult__DEFAULT []*RunCodeOutputResult_

func (p *RunCodeResult_) GetCodeOutputResult_() (v []*RunCodeOutputResult_) {
	if !p.IsSetCodeOutputResult_() {
		return RunCodeResult__CodeOutputResult__DEFAULT
	}
	return p.CodeOutputResult_
}
func (p *RunCodeResult_) SetCodeOutputResult_(val []*RunCodeOutputResult_) {
	p.CodeOutputResult_ = val
}

var fieldIDToName_RunCodeResult_ = map[int16]string{
	1: "CodeOutputResult",
}

func (p *RunCodeResult_) IsSetCodeOutputResult_() bool {
	return p.CodeOutputResult_ != nil
}

func (p *RunCodeResult_) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_RunCodeResult_[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *RunCodeResult_) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*RunCodeOutputResult_, 0, size)
	values := make([]RunCodeOutputResult_, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.CodeOutputResult_ = _field
	return nil
}

func (p *RunCodeResult_) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("RunCodeResult"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *RunCodeResult_) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetCodeOutputResult_() {
		if err = oprot.WriteFieldBegin("CodeOutputResult", thrift.LIST, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.CodeOutputResult_)); err != nil {
			return err
		}
		for _, v := range p.CodeOutputResult_ {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *RunCodeResult_) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RunCodeResult_(%+v)", *p)

}

func (p *RunCodeResult_) DeepEqual(ano *RunCodeResult_) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.CodeOutputResult_) {
		return false
	}
	return true
}

func (p *RunCodeResult_) Field1DeepEqual(src []*RunCodeOutputResult_) bool {

	if len(p.CodeOutputResult_) != len(src) {
		return false
	}
	for i, v := range p.CodeOutputResult_ {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type RunCodeOutputResult_ struct {
	Type    *string `thrift:"Type,1,optional" frugal:"1,optional,string" json:"Type,omitempty"`
	Content *string `thrift:"Content,2,optional" frugal:"2,optional,string" json:"Content,omitempty"`
	Reason  *string `thrift:"Reason,3,optional" frugal:"3,optional,string" json:"Reason,omitempty"`
}

func NewRunCodeOutputResult_() *RunCodeOutputResult_ {
	return &RunCodeOutputResult_{}
}

func (p *RunCodeOutputResult_) InitDefault() {
}

var RunCodeOutputResult__Type_DEFAULT string

func (p *RunCodeOutputResult_) GetType() (v string) {
	if !p.IsSetType() {
		return RunCodeOutputResult__Type_DEFAULT
	}
	return *p.Type
}

var RunCodeOutputResult__Content_DEFAULT string

func (p *RunCodeOutputResult_) GetContent() (v string) {
	if !p.IsSetContent() {
		return RunCodeOutputResult__Content_DEFAULT
	}
	return *p.Content
}

var RunCodeOutputResult__Reason_DEFAULT string

func (p *RunCodeOutputResult_) GetReason() (v string) {
	if !p.IsSetReason() {
		return RunCodeOutputResult__Reason_DEFAULT
	}
	return *p.Reason
}
func (p *RunCodeOutputResult_) SetType(val *string) {
	p.Type = val
}
func (p *RunCodeOutputResult_) SetContent(val *string) {
	p.Content = val
}
func (p *RunCodeOutputResult_) SetReason(val *string) {
	p.Reason = val
}

var fieldIDToName_RunCodeOutputResult_ = map[int16]string{
	1: "Type",
	2: "Content",
	3: "Reason",
}

func (p *RunCodeOutputResult_) IsSetType() bool {
	return p.Type != nil
}

func (p *RunCodeOutputResult_) IsSetContent() bool {
	return p.Content != nil
}

func (p *RunCodeOutputResult_) IsSetReason() bool {
	return p.Reason != nil
}

func (p *RunCodeOutputResult_) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_RunCodeOutputResult_[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *RunCodeOutputResult_) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Type = _field
	return nil
}
func (p *RunCodeOutputResult_) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Content = _field
	return nil
}
func (p *RunCodeOutputResult_) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Reason = _field
	return nil
}

func (p *RunCodeOutputResult_) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("RunCodeOutputResult"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *RunCodeOutputResult_) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetType() {
		if err = oprot.WriteFieldBegin("Type", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Type); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *RunCodeOutputResult_) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetContent() {
		if err = oprot.WriteFieldBegin("Content", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Content); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *RunCodeOutputResult_) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetReason() {
		if err = oprot.WriteFieldBegin("Reason", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Reason); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *RunCodeOutputResult_) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RunCodeOutputResult_(%+v)", *p)

}

func (p *RunCodeOutputResult_) DeepEqual(ano *RunCodeOutputResult_) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Type) {
		return false
	}
	if !p.Field2DeepEqual(ano.Content) {
		return false
	}
	if !p.Field3DeepEqual(ano.Reason) {
		return false
	}
	return true
}

func (p *RunCodeOutputResult_) Field1DeepEqual(src *string) bool {

	if p.Type == src {
		return true
	} else if p.Type == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Type, *src) != 0 {
		return false
	}
	return true
}
func (p *RunCodeOutputResult_) Field2DeepEqual(src *string) bool {

	if p.Content == src {
		return true
	} else if p.Content == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Content, *src) != 0 {
		return false
	}
	return true
}
func (p *RunCodeOutputResult_) Field3DeepEqual(src *string) bool {

	if p.Reason == src {
		return true
	} else if p.Reason == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Reason, *src) != 0 {
		return false
	}
	return true
}

type ArtifactsCodeInfo struct {
	Identifier string    `thrift:"Identifier,1" frugal:"1,default,string" json:"Identifier"`
	Version    string    `thrift:"Version,2" frugal:"2,default,string" json:"Version"`
	CodeFile   *CodeInfo `thrift:"CodeFile,3" frugal:"3,default,CodeInfo" json:"CodeFile"`
}

func NewArtifactsCodeInfo() *ArtifactsCodeInfo {
	return &ArtifactsCodeInfo{}
}

func (p *ArtifactsCodeInfo) InitDefault() {
}

func (p *ArtifactsCodeInfo) GetIdentifier() (v string) {
	return p.Identifier
}

func (p *ArtifactsCodeInfo) GetVersion() (v string) {
	return p.Version
}

var ArtifactsCodeInfo_CodeFile_DEFAULT *CodeInfo

func (p *ArtifactsCodeInfo) GetCodeFile() (v *CodeInfo) {
	if !p.IsSetCodeFile() {
		return ArtifactsCodeInfo_CodeFile_DEFAULT
	}
	return p.CodeFile
}
func (p *ArtifactsCodeInfo) SetIdentifier(val string) {
	p.Identifier = val
}
func (p *ArtifactsCodeInfo) SetVersion(val string) {
	p.Version = val
}
func (p *ArtifactsCodeInfo) SetCodeFile(val *CodeInfo) {
	p.CodeFile = val
}

var fieldIDToName_ArtifactsCodeInfo = map[int16]string{
	1: "Identifier",
	2: "Version",
	3: "CodeFile",
}

func (p *ArtifactsCodeInfo) IsSetCodeFile() bool {
	return p.CodeFile != nil
}

func (p *ArtifactsCodeInfo) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ArtifactsCodeInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ArtifactsCodeInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Identifier = _field
	return nil
}
func (p *ArtifactsCodeInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Version = _field
	return nil
}
func (p *ArtifactsCodeInfo) ReadField3(iprot thrift.TProtocol) error {
	_field := NewCodeInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.CodeFile = _field
	return nil
}

func (p *ArtifactsCodeInfo) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ArtifactsCodeInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ArtifactsCodeInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Identifier", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Identifier); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ArtifactsCodeInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Version", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Version); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ArtifactsCodeInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CodeFile", thrift.STRUCT, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.CodeFile.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ArtifactsCodeInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ArtifactsCodeInfo(%+v)", *p)

}

func (p *ArtifactsCodeInfo) DeepEqual(ano *ArtifactsCodeInfo) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Identifier) {
		return false
	}
	if !p.Field2DeepEqual(ano.Version) {
		return false
	}
	if !p.Field3DeepEqual(ano.CodeFile) {
		return false
	}
	return true
}

func (p *ArtifactsCodeInfo) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Identifier, src) != 0 {
		return false
	}
	return true
}
func (p *ArtifactsCodeInfo) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Version, src) != 0 {
		return false
	}
	return true
}
func (p *ArtifactsCodeInfo) Field3DeepEqual(src *CodeInfo) bool {

	if !p.CodeFile.DeepEqual(src) {
		return false
	}
	return true
}

type RawCodeInfo struct {
	Content  string `thrift:"Content,1" frugal:"1,default,string" json:"Content"`
	Language string `thrift:"Language,2" frugal:"2,default,string" json:"Language"`
}

func NewRawCodeInfo() *RawCodeInfo {
	return &RawCodeInfo{}
}

func (p *RawCodeInfo) InitDefault() {
}

func (p *RawCodeInfo) GetContent() (v string) {
	return p.Content
}

func (p *RawCodeInfo) GetLanguage() (v string) {
	return p.Language
}
func (p *RawCodeInfo) SetContent(val string) {
	p.Content = val
}
func (p *RawCodeInfo) SetLanguage(val string) {
	p.Language = val
}

var fieldIDToName_RawCodeInfo = map[int16]string{
	1: "Content",
	2: "Language",
}

func (p *RawCodeInfo) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_RawCodeInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *RawCodeInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Content = _field
	return nil
}
func (p *RawCodeInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Language = _field
	return nil
}

func (p *RawCodeInfo) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("RawCodeInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *RawCodeInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Content", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Content); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *RawCodeInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Language", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Language); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *RawCodeInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RawCodeInfo(%+v)", *p)

}

func (p *RawCodeInfo) DeepEqual(ano *RawCodeInfo) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Content) {
		return false
	}
	if !p.Field2DeepEqual(ano.Language) {
		return false
	}
	return true
}

func (p *RawCodeInfo) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Content, src) != 0 {
		return false
	}
	return true
}
func (p *RawCodeInfo) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Language, src) != 0 {
		return false
	}
	return true
}

type RunCodeRequestV2 struct {
	CodeType         CodeType           `thrift:"CodeType,1,required" frugal:"1,required,CodeType" json:"CodeType"`
	ConversionID     *string            `thrift:"ConversionID,2,optional" frugal:"2,optional,string" json:"ConversionID,omitempty"`
	MessageID        *int64             `thrift:"MessageID,3,optional" frugal:"3,optional,i64" json:"MessageID,omitempty"`
	UserID           int64              `thrift:"UserID,4" frugal:"4,default,i64" json:"UserID"`
	ArtifactCodeInfo *ArtifactsCodeInfo `thrift:"ArtifactCodeInfo,21,optional" frugal:"21,optional,ArtifactsCodeInfo" json:"ArtifactCodeInfo,omitempty"`
	RawCodeInfo      *RawCodeInfo       `thrift:"RawCodeInfo,22,optional" frugal:"22,optional,RawCodeInfo" json:"RawCodeInfo,omitempty"`
	Base             *base.Base         `thrift:"Base,255,optional" frugal:"255,optional,base.Base" json:"Base,omitempty"`
}

func NewRunCodeRequestV2() *RunCodeRequestV2 {
	return &RunCodeRequestV2{}
}

func (p *RunCodeRequestV2) InitDefault() {
}

func (p *RunCodeRequestV2) GetCodeType() (v CodeType) {
	return p.CodeType
}

var RunCodeRequestV2_ConversionID_DEFAULT string

func (p *RunCodeRequestV2) GetConversionID() (v string) {
	if !p.IsSetConversionID() {
		return RunCodeRequestV2_ConversionID_DEFAULT
	}
	return *p.ConversionID
}

var RunCodeRequestV2_MessageID_DEFAULT int64

func (p *RunCodeRequestV2) GetMessageID() (v int64) {
	if !p.IsSetMessageID() {
		return RunCodeRequestV2_MessageID_DEFAULT
	}
	return *p.MessageID
}

func (p *RunCodeRequestV2) GetUserID() (v int64) {
	return p.UserID
}

var RunCodeRequestV2_ArtifactCodeInfo_DEFAULT *ArtifactsCodeInfo

func (p *RunCodeRequestV2) GetArtifactCodeInfo() (v *ArtifactsCodeInfo) {
	if !p.IsSetArtifactCodeInfo() {
		return RunCodeRequestV2_ArtifactCodeInfo_DEFAULT
	}
	return p.ArtifactCodeInfo
}

var RunCodeRequestV2_RawCodeInfo_DEFAULT *RawCodeInfo

func (p *RunCodeRequestV2) GetRawCodeInfo() (v *RawCodeInfo) {
	if !p.IsSetRawCodeInfo() {
		return RunCodeRequestV2_RawCodeInfo_DEFAULT
	}
	return p.RawCodeInfo
}

var RunCodeRequestV2_Base_DEFAULT *base.Base

func (p *RunCodeRequestV2) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return RunCodeRequestV2_Base_DEFAULT
	}
	return p.Base
}
func (p *RunCodeRequestV2) SetCodeType(val CodeType) {
	p.CodeType = val
}
func (p *RunCodeRequestV2) SetConversionID(val *string) {
	p.ConversionID = val
}
func (p *RunCodeRequestV2) SetMessageID(val *int64) {
	p.MessageID = val
}
func (p *RunCodeRequestV2) SetUserID(val int64) {
	p.UserID = val
}
func (p *RunCodeRequestV2) SetArtifactCodeInfo(val *ArtifactsCodeInfo) {
	p.ArtifactCodeInfo = val
}
func (p *RunCodeRequestV2) SetRawCodeInfo(val *RawCodeInfo) {
	p.RawCodeInfo = val
}
func (p *RunCodeRequestV2) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_RunCodeRequestV2 = map[int16]string{
	1:   "CodeType",
	2:   "ConversionID",
	3:   "MessageID",
	4:   "UserID",
	21:  "ArtifactCodeInfo",
	22:  "RawCodeInfo",
	255: "Base",
}

func (p *RunCodeRequestV2) IsSetConversionID() bool {
	return p.ConversionID != nil
}

func (p *RunCodeRequestV2) IsSetMessageID() bool {
	return p.MessageID != nil
}

func (p *RunCodeRequestV2) IsSetArtifactCodeInfo() bool {
	return p.ArtifactCodeInfo != nil
}

func (p *RunCodeRequestV2) IsSetRawCodeInfo() bool {
	return p.RawCodeInfo != nil
}

func (p *RunCodeRequestV2) IsSetBase() bool {
	return p.Base != nil
}

func (p *RunCodeRequestV2) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetCodeType bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetCodeType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 21:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField21(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 22:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField22(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetCodeType {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_RunCodeRequestV2[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_RunCodeRequestV2[fieldId]))
}

func (p *RunCodeRequestV2) ReadField1(iprot thrift.TProtocol) error {

	var _field CodeType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = CodeType(v)
	}
	p.CodeType = _field
	return nil
}
func (p *RunCodeRequestV2) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ConversionID = _field
	return nil
}
func (p *RunCodeRequestV2) ReadField3(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.MessageID = _field
	return nil
}
func (p *RunCodeRequestV2) ReadField4(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UserID = _field
	return nil
}
func (p *RunCodeRequestV2) ReadField21(iprot thrift.TProtocol) error {
	_field := NewArtifactsCodeInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.ArtifactCodeInfo = _field
	return nil
}
func (p *RunCodeRequestV2) ReadField22(iprot thrift.TProtocol) error {
	_field := NewRawCodeInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.RawCodeInfo = _field
	return nil
}
func (p *RunCodeRequestV2) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *RunCodeRequestV2) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("RunCodeRequestV2"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField21(oprot); err != nil {
			fieldId = 21
			goto WriteFieldError
		}
		if err = p.writeField22(oprot); err != nil {
			fieldId = 22
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *RunCodeRequestV2) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CodeType", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.CodeType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *RunCodeRequestV2) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetConversionID() {
		if err = oprot.WriteFieldBegin("ConversionID", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ConversionID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *RunCodeRequestV2) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetMessageID() {
		if err = oprot.WriteFieldBegin("MessageID", thrift.I64, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.MessageID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *RunCodeRequestV2) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("UserID", thrift.I64, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.UserID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *RunCodeRequestV2) writeField21(oprot thrift.TProtocol) (err error) {
	if p.IsSetArtifactCodeInfo() {
		if err = oprot.WriteFieldBegin("ArtifactCodeInfo", thrift.STRUCT, 21); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.ArtifactCodeInfo.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 21 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 21 end error: ", p), err)
}
func (p *RunCodeRequestV2) writeField22(oprot thrift.TProtocol) (err error) {
	if p.IsSetRawCodeInfo() {
		if err = oprot.WriteFieldBegin("RawCodeInfo", thrift.STRUCT, 22); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.RawCodeInfo.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 22 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 22 end error: ", p), err)
}
func (p *RunCodeRequestV2) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *RunCodeRequestV2) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RunCodeRequestV2(%+v)", *p)

}

func (p *RunCodeRequestV2) DeepEqual(ano *RunCodeRequestV2) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.CodeType) {
		return false
	}
	if !p.Field2DeepEqual(ano.ConversionID) {
		return false
	}
	if !p.Field3DeepEqual(ano.MessageID) {
		return false
	}
	if !p.Field4DeepEqual(ano.UserID) {
		return false
	}
	if !p.Field21DeepEqual(ano.ArtifactCodeInfo) {
		return false
	}
	if !p.Field22DeepEqual(ano.RawCodeInfo) {
		return false
	}
	if !p.Field255DeepEqual(ano.Base) {
		return false
	}
	return true
}

func (p *RunCodeRequestV2) Field1DeepEqual(src CodeType) bool {

	if p.CodeType != src {
		return false
	}
	return true
}
func (p *RunCodeRequestV2) Field2DeepEqual(src *string) bool {

	if p.ConversionID == src {
		return true
	} else if p.ConversionID == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ConversionID, *src) != 0 {
		return false
	}
	return true
}
func (p *RunCodeRequestV2) Field3DeepEqual(src *int64) bool {

	if p.MessageID == src {
		return true
	} else if p.MessageID == nil || src == nil {
		return false
	}
	if *p.MessageID != *src {
		return false
	}
	return true
}
func (p *RunCodeRequestV2) Field4DeepEqual(src int64) bool {

	if p.UserID != src {
		return false
	}
	return true
}
func (p *RunCodeRequestV2) Field21DeepEqual(src *ArtifactsCodeInfo) bool {

	if !p.ArtifactCodeInfo.DeepEqual(src) {
		return false
	}
	return true
}
func (p *RunCodeRequestV2) Field22DeepEqual(src *RawCodeInfo) bool {

	if !p.RawCodeInfo.DeepEqual(src) {
		return false
	}
	return true
}
func (p *RunCodeRequestV2) Field255DeepEqual(src *base.Base) bool {

	if !p.Base.DeepEqual(src) {
		return false
	}
	return true
}

type RunCodeResponseV2 struct {
	Data     *RunCodeData   `thrift:"Data,1,optional" frugal:"1,optional,RunCodeData" json:"Data,omitempty"`
	Message  *string        `thrift:"Message,2,optional" frugal:"2,optional,string" json:"Message,omitempty"`
	Code     *int32         `thrift:"Code,3,optional" frugal:"3,optional,i32" json:"Code,omitempty"`
	BaseResp *base.BaseResp `thrift:"BaseResp,255,optional" frugal:"255,optional,base.BaseResp" json:"BaseResp,omitempty"`
}

func NewRunCodeResponseV2() *RunCodeResponseV2 {
	return &RunCodeResponseV2{}
}

func (p *RunCodeResponseV2) InitDefault() {
}

var RunCodeResponseV2_Data_DEFAULT *RunCodeData

func (p *RunCodeResponseV2) GetData() (v *RunCodeData) {
	if !p.IsSetData() {
		return RunCodeResponseV2_Data_DEFAULT
	}
	return p.Data
}

var RunCodeResponseV2_Message_DEFAULT string

func (p *RunCodeResponseV2) GetMessage() (v string) {
	if !p.IsSetMessage() {
		return RunCodeResponseV2_Message_DEFAULT
	}
	return *p.Message
}

var RunCodeResponseV2_Code_DEFAULT int32

func (p *RunCodeResponseV2) GetCode() (v int32) {
	if !p.IsSetCode() {
		return RunCodeResponseV2_Code_DEFAULT
	}
	return *p.Code
}

var RunCodeResponseV2_BaseResp_DEFAULT *base.BaseResp

func (p *RunCodeResponseV2) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return RunCodeResponseV2_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *RunCodeResponseV2) SetData(val *RunCodeData) {
	p.Data = val
}
func (p *RunCodeResponseV2) SetMessage(val *string) {
	p.Message = val
}
func (p *RunCodeResponseV2) SetCode(val *int32) {
	p.Code = val
}
func (p *RunCodeResponseV2) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_RunCodeResponseV2 = map[int16]string{
	1:   "Data",
	2:   "Message",
	3:   "Code",
	255: "BaseResp",
}

func (p *RunCodeResponseV2) IsSetData() bool {
	return p.Data != nil
}

func (p *RunCodeResponseV2) IsSetMessage() bool {
	return p.Message != nil
}

func (p *RunCodeResponseV2) IsSetCode() bool {
	return p.Code != nil
}

func (p *RunCodeResponseV2) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *RunCodeResponseV2) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_RunCodeResponseV2[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *RunCodeResponseV2) ReadField1(iprot thrift.TProtocol) error {
	_field := NewRunCodeData()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Data = _field
	return nil
}
func (p *RunCodeResponseV2) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Message = _field
	return nil
}
func (p *RunCodeResponseV2) ReadField3(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Code = _field
	return nil
}
func (p *RunCodeResponseV2) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *RunCodeResponseV2) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("RunCodeResponseV2"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *RunCodeResponseV2) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetData() {
		if err = oprot.WriteFieldBegin("Data", thrift.STRUCT, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Data.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *RunCodeResponseV2) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetMessage() {
		if err = oprot.WriteFieldBegin("Message", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Message); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *RunCodeResponseV2) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetCode() {
		if err = oprot.WriteFieldBegin("Code", thrift.I32, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.Code); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *RunCodeResponseV2) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBaseResp() {
		if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.BaseResp.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *RunCodeResponseV2) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RunCodeResponseV2(%+v)", *p)

}

func (p *RunCodeResponseV2) DeepEqual(ano *RunCodeResponseV2) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Data) {
		return false
	}
	if !p.Field2DeepEqual(ano.Message) {
		return false
	}
	if !p.Field3DeepEqual(ano.Code) {
		return false
	}
	if !p.Field255DeepEqual(ano.BaseResp) {
		return false
	}
	return true
}

func (p *RunCodeResponseV2) Field1DeepEqual(src *RunCodeData) bool {

	if !p.Data.DeepEqual(src) {
		return false
	}
	return true
}
func (p *RunCodeResponseV2) Field2DeepEqual(src *string) bool {

	if p.Message == src {
		return true
	} else if p.Message == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Message, *src) != 0 {
		return false
	}
	return true
}
func (p *RunCodeResponseV2) Field3DeepEqual(src *int32) bool {

	if p.Code == src {
		return true
	} else if p.Code == nil || src == nil {
		return false
	}
	if *p.Code != *src {
		return false
	}
	return true
}
func (p *RunCodeResponseV2) Field255DeepEqual(src *base.BaseResp) bool {

	if !p.BaseResp.DeepEqual(src) {
		return false
	}
	return true
}

type RunCodeResultV2 struct {
	CodeOutputResult_ []*RunCodeOutputResult_ `thrift:"CodeOutputResult,1" frugal:"1,default,list<RunCodeOutputResult_>" json:"CodeOutputResult"`
	Executable        bool                    `thrift:"Executable,2" frugal:"2,default,bool" json:"Executable"`
}

func NewRunCodeResultV2() *RunCodeResultV2 {
	return &RunCodeResultV2{}
}

func (p *RunCodeResultV2) InitDefault() {
}

func (p *RunCodeResultV2) GetCodeOutputResult_() (v []*RunCodeOutputResult_) {
	return p.CodeOutputResult_
}

func (p *RunCodeResultV2) GetExecutable() (v bool) {
	return p.Executable
}
func (p *RunCodeResultV2) SetCodeOutputResult_(val []*RunCodeOutputResult_) {
	p.CodeOutputResult_ = val
}
func (p *RunCodeResultV2) SetExecutable(val bool) {
	p.Executable = val
}

var fieldIDToName_RunCodeResultV2 = map[int16]string{
	1: "CodeOutputResult",
	2: "Executable",
}

func (p *RunCodeResultV2) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_RunCodeResultV2[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *RunCodeResultV2) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*RunCodeOutputResult_, 0, size)
	values := make([]RunCodeOutputResult_, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.CodeOutputResult_ = _field
	return nil
}
func (p *RunCodeResultV2) ReadField2(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Executable = _field
	return nil
}

func (p *RunCodeResultV2) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("RunCodeResultV2"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *RunCodeResultV2) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CodeOutputResult", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.CodeOutputResult_)); err != nil {
		return err
	}
	for _, v := range p.CodeOutputResult_ {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *RunCodeResultV2) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Executable", thrift.BOOL, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.Executable); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *RunCodeResultV2) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RunCodeResultV2(%+v)", *p)

}

func (p *RunCodeResultV2) DeepEqual(ano *RunCodeResultV2) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.CodeOutputResult_) {
		return false
	}
	if !p.Field2DeepEqual(ano.Executable) {
		return false
	}
	return true
}

func (p *RunCodeResultV2) Field1DeepEqual(src []*RunCodeOutputResult_) bool {

	if len(p.CodeOutputResult_) != len(src) {
		return false
	}
	for i, v := range p.CodeOutputResult_ {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *RunCodeResultV2) Field2DeepEqual(src bool) bool {

	if p.Executable != src {
		return false
	}
	return true
}

type GetArtifactTemplateDirRequest struct {
	ArtifactsID int64      `thrift:"ArtifactsID,1,required" frugal:"1,required,i64" json:"ArtifactsID"`
	UserID      int64      `thrift:"UserID,2,required" frugal:"2,required,i64" json:"UserID"`
	DirRootPath *string    `thrift:"DirRootPath,3,optional" frugal:"3,optional,string" json:"DirRootPath,omitempty"`
	Base        *base.Base `thrift:"Base,255,optional" frugal:"255,optional,base.Base" json:"Base,omitempty"`
}

func NewGetArtifactTemplateDirRequest() *GetArtifactTemplateDirRequest {
	return &GetArtifactTemplateDirRequest{}
}

func (p *GetArtifactTemplateDirRequest) InitDefault() {
}

func (p *GetArtifactTemplateDirRequest) GetArtifactsID() (v int64) {
	return p.ArtifactsID
}

func (p *GetArtifactTemplateDirRequest) GetUserID() (v int64) {
	return p.UserID
}

var GetArtifactTemplateDirRequest_DirRootPath_DEFAULT string

func (p *GetArtifactTemplateDirRequest) GetDirRootPath() (v string) {
	if !p.IsSetDirRootPath() {
		return GetArtifactTemplateDirRequest_DirRootPath_DEFAULT
	}
	return *p.DirRootPath
}

var GetArtifactTemplateDirRequest_Base_DEFAULT *base.Base

func (p *GetArtifactTemplateDirRequest) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return GetArtifactTemplateDirRequest_Base_DEFAULT
	}
	return p.Base
}
func (p *GetArtifactTemplateDirRequest) SetArtifactsID(val int64) {
	p.ArtifactsID = val
}
func (p *GetArtifactTemplateDirRequest) SetUserID(val int64) {
	p.UserID = val
}
func (p *GetArtifactTemplateDirRequest) SetDirRootPath(val *string) {
	p.DirRootPath = val
}
func (p *GetArtifactTemplateDirRequest) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_GetArtifactTemplateDirRequest = map[int16]string{
	1:   "ArtifactsID",
	2:   "UserID",
	3:   "DirRootPath",
	255: "Base",
}

func (p *GetArtifactTemplateDirRequest) IsSetDirRootPath() bool {
	return p.DirRootPath != nil
}

func (p *GetArtifactTemplateDirRequest) IsSetBase() bool {
	return p.Base != nil
}

func (p *GetArtifactTemplateDirRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetArtifactsID bool = false
	var issetUserID bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetArtifactsID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetUserID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetArtifactsID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetUserID {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetArtifactTemplateDirRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_GetArtifactTemplateDirRequest[fieldId]))
}

func (p *GetArtifactTemplateDirRequest) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ArtifactsID = _field
	return nil
}
func (p *GetArtifactTemplateDirRequest) ReadField2(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UserID = _field
	return nil
}
func (p *GetArtifactTemplateDirRequest) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.DirRootPath = _field
	return nil
}
func (p *GetArtifactTemplateDirRequest) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *GetArtifactTemplateDirRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetArtifactTemplateDirRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetArtifactTemplateDirRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ArtifactsID", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.ArtifactsID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *GetArtifactTemplateDirRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("UserID", thrift.I64, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.UserID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *GetArtifactTemplateDirRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetDirRootPath() {
		if err = oprot.WriteFieldBegin("DirRootPath", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.DirRootPath); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *GetArtifactTemplateDirRequest) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *GetArtifactTemplateDirRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetArtifactTemplateDirRequest(%+v)", *p)

}

func (p *GetArtifactTemplateDirRequest) DeepEqual(ano *GetArtifactTemplateDirRequest) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ArtifactsID) {
		return false
	}
	if !p.Field2DeepEqual(ano.UserID) {
		return false
	}
	if !p.Field3DeepEqual(ano.DirRootPath) {
		return false
	}
	if !p.Field255DeepEqual(ano.Base) {
		return false
	}
	return true
}

func (p *GetArtifactTemplateDirRequest) Field1DeepEqual(src int64) bool {

	if p.ArtifactsID != src {
		return false
	}
	return true
}
func (p *GetArtifactTemplateDirRequest) Field2DeepEqual(src int64) bool {

	if p.UserID != src {
		return false
	}
	return true
}
func (p *GetArtifactTemplateDirRequest) Field3DeepEqual(src *string) bool {

	if p.DirRootPath == src {
		return true
	} else if p.DirRootPath == nil || src == nil {
		return false
	}
	if strings.Compare(*p.DirRootPath, *src) != 0 {
		return false
	}
	return true
}
func (p *GetArtifactTemplateDirRequest) Field255DeepEqual(src *base.Base) bool {

	if !p.Base.DeepEqual(src) {
		return false
	}
	return true
}

type GetArtifactTemplateDirResponse struct {
	Data     *TemplateData  `thrift:"Data,1,required" frugal:"1,required,TemplateData" json:"Data"`
	BaseResp *base.BaseResp `thrift:"BaseResp,255,optional" frugal:"255,optional,base.BaseResp" json:"BaseResp,omitempty"`
}

func NewGetArtifactTemplateDirResponse() *GetArtifactTemplateDirResponse {
	return &GetArtifactTemplateDirResponse{}
}

func (p *GetArtifactTemplateDirResponse) InitDefault() {
}

var GetArtifactTemplateDirResponse_Data_DEFAULT *TemplateData

func (p *GetArtifactTemplateDirResponse) GetData() (v *TemplateData) {
	if !p.IsSetData() {
		return GetArtifactTemplateDirResponse_Data_DEFAULT
	}
	return p.Data
}

var GetArtifactTemplateDirResponse_BaseResp_DEFAULT *base.BaseResp

func (p *GetArtifactTemplateDirResponse) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return GetArtifactTemplateDirResponse_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *GetArtifactTemplateDirResponse) SetData(val *TemplateData) {
	p.Data = val
}
func (p *GetArtifactTemplateDirResponse) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_GetArtifactTemplateDirResponse = map[int16]string{
	1:   "Data",
	255: "BaseResp",
}

func (p *GetArtifactTemplateDirResponse) IsSetData() bool {
	return p.Data != nil
}

func (p *GetArtifactTemplateDirResponse) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *GetArtifactTemplateDirResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetData bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetData = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetData {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetArtifactTemplateDirResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_GetArtifactTemplateDirResponse[fieldId]))
}

func (p *GetArtifactTemplateDirResponse) ReadField1(iprot thrift.TProtocol) error {
	_field := NewTemplateData()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Data = _field
	return nil
}
func (p *GetArtifactTemplateDirResponse) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *GetArtifactTemplateDirResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetArtifactTemplateDirResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetArtifactTemplateDirResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Data", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Data.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *GetArtifactTemplateDirResponse) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBaseResp() {
		if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.BaseResp.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *GetArtifactTemplateDirResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetArtifactTemplateDirResponse(%+v)", *p)

}

func (p *GetArtifactTemplateDirResponse) DeepEqual(ano *GetArtifactTemplateDirResponse) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Data) {
		return false
	}
	if !p.Field255DeepEqual(ano.BaseResp) {
		return false
	}
	return true
}

func (p *GetArtifactTemplateDirResponse) Field1DeepEqual(src *TemplateData) bool {

	if !p.Data.DeepEqual(src) {
		return false
	}
	return true
}
func (p *GetArtifactTemplateDirResponse) Field255DeepEqual(src *base.BaseResp) bool {

	if !p.BaseResp.DeepEqual(src) {
		return false
	}
	return true
}

type GetArtifactTemplateFileRequest struct {
	ArtifactsID     int64      `thrift:"ArtifactsID,1,required" frugal:"1,required,i64" json:"ArtifactsID"`
	UserID          int64      `thrift:"UserID,2,required" frugal:"2,required,i64" json:"UserID"`
	FetchSingleFile bool       `thrift:"FetchSingleFile,3,required" frugal:"3,required,bool" json:"FetchSingleFile"`
	FilePath        *string    `thrift:"FilePath,4,optional" frugal:"4,optional,string" json:"FilePath,omitempty"`
	Base            *base.Base `thrift:"Base,255,optional" frugal:"255,optional,base.Base" json:"Base,omitempty"`
}

func NewGetArtifactTemplateFileRequest() *GetArtifactTemplateFileRequest {
	return &GetArtifactTemplateFileRequest{}
}

func (p *GetArtifactTemplateFileRequest) InitDefault() {
}

func (p *GetArtifactTemplateFileRequest) GetArtifactsID() (v int64) {
	return p.ArtifactsID
}

func (p *GetArtifactTemplateFileRequest) GetUserID() (v int64) {
	return p.UserID
}

func (p *GetArtifactTemplateFileRequest) GetFetchSingleFile() (v bool) {
	return p.FetchSingleFile
}

var GetArtifactTemplateFileRequest_FilePath_DEFAULT string

func (p *GetArtifactTemplateFileRequest) GetFilePath() (v string) {
	if !p.IsSetFilePath() {
		return GetArtifactTemplateFileRequest_FilePath_DEFAULT
	}
	return *p.FilePath
}

var GetArtifactTemplateFileRequest_Base_DEFAULT *base.Base

func (p *GetArtifactTemplateFileRequest) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return GetArtifactTemplateFileRequest_Base_DEFAULT
	}
	return p.Base
}
func (p *GetArtifactTemplateFileRequest) SetArtifactsID(val int64) {
	p.ArtifactsID = val
}
func (p *GetArtifactTemplateFileRequest) SetUserID(val int64) {
	p.UserID = val
}
func (p *GetArtifactTemplateFileRequest) SetFetchSingleFile(val bool) {
	p.FetchSingleFile = val
}
func (p *GetArtifactTemplateFileRequest) SetFilePath(val *string) {
	p.FilePath = val
}
func (p *GetArtifactTemplateFileRequest) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_GetArtifactTemplateFileRequest = map[int16]string{
	1:   "ArtifactsID",
	2:   "UserID",
	3:   "FetchSingleFile",
	4:   "FilePath",
	255: "Base",
}

func (p *GetArtifactTemplateFileRequest) IsSetFilePath() bool {
	return p.FilePath != nil
}

func (p *GetArtifactTemplateFileRequest) IsSetBase() bool {
	return p.Base != nil
}

func (p *GetArtifactTemplateFileRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetArtifactsID bool = false
	var issetUserID bool = false
	var issetFetchSingleFile bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetArtifactsID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetUserID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetFetchSingleFile = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetArtifactsID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetUserID {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetFetchSingleFile {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetArtifactTemplateFileRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_GetArtifactTemplateFileRequest[fieldId]))
}

func (p *GetArtifactTemplateFileRequest) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ArtifactsID = _field
	return nil
}
func (p *GetArtifactTemplateFileRequest) ReadField2(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UserID = _field
	return nil
}
func (p *GetArtifactTemplateFileRequest) ReadField3(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.FetchSingleFile = _field
	return nil
}
func (p *GetArtifactTemplateFileRequest) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.FilePath = _field
	return nil
}
func (p *GetArtifactTemplateFileRequest) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *GetArtifactTemplateFileRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetArtifactTemplateFileRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetArtifactTemplateFileRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ArtifactsID", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.ArtifactsID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *GetArtifactTemplateFileRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("UserID", thrift.I64, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.UserID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *GetArtifactTemplateFileRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("FetchSingleFile", thrift.BOOL, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.FetchSingleFile); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *GetArtifactTemplateFileRequest) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetFilePath() {
		if err = oprot.WriteFieldBegin("FilePath", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.FilePath); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *GetArtifactTemplateFileRequest) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *GetArtifactTemplateFileRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetArtifactTemplateFileRequest(%+v)", *p)

}

func (p *GetArtifactTemplateFileRequest) DeepEqual(ano *GetArtifactTemplateFileRequest) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ArtifactsID) {
		return false
	}
	if !p.Field2DeepEqual(ano.UserID) {
		return false
	}
	if !p.Field3DeepEqual(ano.FetchSingleFile) {
		return false
	}
	if !p.Field4DeepEqual(ano.FilePath) {
		return false
	}
	if !p.Field255DeepEqual(ano.Base) {
		return false
	}
	return true
}

func (p *GetArtifactTemplateFileRequest) Field1DeepEqual(src int64) bool {

	if p.ArtifactsID != src {
		return false
	}
	return true
}
func (p *GetArtifactTemplateFileRequest) Field2DeepEqual(src int64) bool {

	if p.UserID != src {
		return false
	}
	return true
}
func (p *GetArtifactTemplateFileRequest) Field3DeepEqual(src bool) bool {

	if p.FetchSingleFile != src {
		return false
	}
	return true
}
func (p *GetArtifactTemplateFileRequest) Field4DeepEqual(src *string) bool {

	if p.FilePath == src {
		return true
	} else if p.FilePath == nil || src == nil {
		return false
	}
	if strings.Compare(*p.FilePath, *src) != 0 {
		return false
	}
	return true
}
func (p *GetArtifactTemplateFileRequest) Field255DeepEqual(src *base.Base) bool {

	if !p.Base.DeepEqual(src) {
		return false
	}
	return true
}

type TemplateData struct {
	Nodes []*Node `thrift:"Nodes,1" frugal:"1,default,list<Node>" json:"Nodes"`
}

func NewTemplateData() *TemplateData {
	return &TemplateData{}
}

func (p *TemplateData) InitDefault() {
}

func (p *TemplateData) GetNodes() (v []*Node) {
	return p.Nodes
}
func (p *TemplateData) SetNodes(val []*Node) {
	p.Nodes = val
}

var fieldIDToName_TemplateData = map[int16]string{
	1: "Nodes",
}

func (p *TemplateData) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TemplateData[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *TemplateData) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*Node, 0, size)
	values := make([]Node, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Nodes = _field
	return nil
}

func (p *TemplateData) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("TemplateData"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TemplateData) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Nodes", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Nodes)); err != nil {
		return err
	}
	for _, v := range p.Nodes {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *TemplateData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TemplateData(%+v)", *p)

}

func (p *TemplateData) DeepEqual(ano *TemplateData) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Nodes) {
		return false
	}
	return true
}

func (p *TemplateData) Field1DeepEqual(src []*Node) bool {

	if len(p.Nodes) != len(src) {
		return false
	}
	for i, v := range p.Nodes {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type Node struct {
	Path     string           `thrift:"Path,1" frugal:"1,default,string" json:"Path"`
	Name     string           `thrift:"Name,2" frugal:"2,default,string" json:"Name"`
	Language string           `thrift:"Language,3" frugal:"3,default,string" json:"Language"`
	Content  string           `thrift:"Content,4" frugal:"4,default,string" json:"Content"`
	NodeType TemplateNodeType `thrift:"NodeType,5" frugal:"5,default,TemplateNodeType" json:"NodeType"`
	Index    int64            `thrift:"Index,6" frugal:"6,default,i64" json:"Index"`
}

func NewNode() *Node {
	return &Node{}
}

func (p *Node) InitDefault() {
}

func (p *Node) GetPath() (v string) {
	return p.Path
}

func (p *Node) GetName() (v string) {
	return p.Name
}

func (p *Node) GetLanguage() (v string) {
	return p.Language
}

func (p *Node) GetContent() (v string) {
	return p.Content
}

func (p *Node) GetNodeType() (v TemplateNodeType) {
	return p.NodeType
}

func (p *Node) GetIndex() (v int64) {
	return p.Index
}
func (p *Node) SetPath(val string) {
	p.Path = val
}
func (p *Node) SetName(val string) {
	p.Name = val
}
func (p *Node) SetLanguage(val string) {
	p.Language = val
}
func (p *Node) SetContent(val string) {
	p.Content = val
}
func (p *Node) SetNodeType(val TemplateNodeType) {
	p.NodeType = val
}
func (p *Node) SetIndex(val int64) {
	p.Index = val
}

var fieldIDToName_Node = map[int16]string{
	1: "Path",
	2: "Name",
	3: "Language",
	4: "Content",
	5: "NodeType",
	6: "Index",
}

func (p *Node) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_Node[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *Node) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Path = _field
	return nil
}
func (p *Node) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Name = _field
	return nil
}
func (p *Node) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Language = _field
	return nil
}
func (p *Node) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Content = _field
	return nil
}
func (p *Node) ReadField5(iprot thrift.TProtocol) error {

	var _field TemplateNodeType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = TemplateNodeType(v)
	}
	p.NodeType = _field
	return nil
}
func (p *Node) ReadField6(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Index = _field
	return nil
}

func (p *Node) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("Node"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *Node) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Path", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Path); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *Node) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Name", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Name); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *Node) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Language", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Language); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *Node) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Content", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Content); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *Node) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("NodeType", thrift.I32, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.NodeType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *Node) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Index", thrift.I64, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.Index); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *Node) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Node(%+v)", *p)

}

func (p *Node) DeepEqual(ano *Node) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Path) {
		return false
	}
	if !p.Field2DeepEqual(ano.Name) {
		return false
	}
	if !p.Field3DeepEqual(ano.Language) {
		return false
	}
	if !p.Field4DeepEqual(ano.Content) {
		return false
	}
	if !p.Field5DeepEqual(ano.NodeType) {
		return false
	}
	if !p.Field6DeepEqual(ano.Index) {
		return false
	}
	return true
}

func (p *Node) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Path, src) != 0 {
		return false
	}
	return true
}
func (p *Node) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Name, src) != 0 {
		return false
	}
	return true
}
func (p *Node) Field3DeepEqual(src string) bool {

	if strings.Compare(p.Language, src) != 0 {
		return false
	}
	return true
}
func (p *Node) Field4DeepEqual(src string) bool {

	if strings.Compare(p.Content, src) != 0 {
		return false
	}
	return true
}
func (p *Node) Field5DeepEqual(src TemplateNodeType) bool {

	if p.NodeType != src {
		return false
	}
	return true
}
func (p *Node) Field6DeepEqual(src int64) bool {

	if p.Index != src {
		return false
	}
	return true
}

type GetArtifactTemplateFileResponse struct {
	Data     *TemplateData  `thrift:"Data,1,required" frugal:"1,required,TemplateData" json:"Data"`
	BaseResp *base.BaseResp `thrift:"BaseResp,255,optional" frugal:"255,optional,base.BaseResp" json:"BaseResp,omitempty"`
}

func NewGetArtifactTemplateFileResponse() *GetArtifactTemplateFileResponse {
	return &GetArtifactTemplateFileResponse{}
}

func (p *GetArtifactTemplateFileResponse) InitDefault() {
}

var GetArtifactTemplateFileResponse_Data_DEFAULT *TemplateData

func (p *GetArtifactTemplateFileResponse) GetData() (v *TemplateData) {
	if !p.IsSetData() {
		return GetArtifactTemplateFileResponse_Data_DEFAULT
	}
	return p.Data
}

var GetArtifactTemplateFileResponse_BaseResp_DEFAULT *base.BaseResp

func (p *GetArtifactTemplateFileResponse) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return GetArtifactTemplateFileResponse_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *GetArtifactTemplateFileResponse) SetData(val *TemplateData) {
	p.Data = val
}
func (p *GetArtifactTemplateFileResponse) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_GetArtifactTemplateFileResponse = map[int16]string{
	1:   "Data",
	255: "BaseResp",
}

func (p *GetArtifactTemplateFileResponse) IsSetData() bool {
	return p.Data != nil
}

func (p *GetArtifactTemplateFileResponse) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *GetArtifactTemplateFileResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetData bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetData = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetData {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetArtifactTemplateFileResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_GetArtifactTemplateFileResponse[fieldId]))
}

func (p *GetArtifactTemplateFileResponse) ReadField1(iprot thrift.TProtocol) error {
	_field := NewTemplateData()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Data = _field
	return nil
}
func (p *GetArtifactTemplateFileResponse) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *GetArtifactTemplateFileResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetArtifactTemplateFileResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetArtifactTemplateFileResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Data", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Data.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *GetArtifactTemplateFileResponse) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBaseResp() {
		if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.BaseResp.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *GetArtifactTemplateFileResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetArtifactTemplateFileResponse(%+v)", *p)

}

func (p *GetArtifactTemplateFileResponse) DeepEqual(ano *GetArtifactTemplateFileResponse) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Data) {
		return false
	}
	if !p.Field255DeepEqual(ano.BaseResp) {
		return false
	}
	return true
}

func (p *GetArtifactTemplateFileResponse) Field1DeepEqual(src *TemplateData) bool {

	if !p.Data.DeepEqual(src) {
		return false
	}
	return true
}
func (p *GetArtifactTemplateFileResponse) Field255DeepEqual(src *base.BaseResp) bool {

	if !p.BaseResp.DeepEqual(src) {
		return false
	}
	return true
}

type CompileCodeArtifactRequest struct {
	UserID       int64      `thrift:"UserID,1" frugal:"1,default,i64" json:"UserID"`
	MessageID    *int64     `thrift:"MessageID,2,optional" frugal:"2,optional,i64" json:"MessageID,omitempty"`
	ConversionID *string    `thrift:"ConversionID,3,optional" frugal:"3,optional,string" json:"ConversionID,omitempty"`
	CodeID       int64      `thrift:"CodeID,4" frugal:"4,default,i64" json:"CodeID"`
	CodeVersion  int32      `thrift:"CodeVersion,5" frugal:"5,default,i32" json:"CodeVersion"`
	LinkPrefix   string     `thrift:"LinkPrefix,6" frugal:"6,default,string" json:"LinkPrefix"`
	Base         *base.Base `thrift:"Base,255,optional" frugal:"255,optional,base.Base" json:"Base,omitempty"`
}

func NewCompileCodeArtifactRequest() *CompileCodeArtifactRequest {
	return &CompileCodeArtifactRequest{}
}

func (p *CompileCodeArtifactRequest) InitDefault() {
}

func (p *CompileCodeArtifactRequest) GetUserID() (v int64) {
	return p.UserID
}

var CompileCodeArtifactRequest_MessageID_DEFAULT int64

func (p *CompileCodeArtifactRequest) GetMessageID() (v int64) {
	if !p.IsSetMessageID() {
		return CompileCodeArtifactRequest_MessageID_DEFAULT
	}
	return *p.MessageID
}

var CompileCodeArtifactRequest_ConversionID_DEFAULT string

func (p *CompileCodeArtifactRequest) GetConversionID() (v string) {
	if !p.IsSetConversionID() {
		return CompileCodeArtifactRequest_ConversionID_DEFAULT
	}
	return *p.ConversionID
}

func (p *CompileCodeArtifactRequest) GetCodeID() (v int64) {
	return p.CodeID
}

func (p *CompileCodeArtifactRequest) GetCodeVersion() (v int32) {
	return p.CodeVersion
}

func (p *CompileCodeArtifactRequest) GetLinkPrefix() (v string) {
	return p.LinkPrefix
}

var CompileCodeArtifactRequest_Base_DEFAULT *base.Base

func (p *CompileCodeArtifactRequest) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return CompileCodeArtifactRequest_Base_DEFAULT
	}
	return p.Base
}
func (p *CompileCodeArtifactRequest) SetUserID(val int64) {
	p.UserID = val
}
func (p *CompileCodeArtifactRequest) SetMessageID(val *int64) {
	p.MessageID = val
}
func (p *CompileCodeArtifactRequest) SetConversionID(val *string) {
	p.ConversionID = val
}
func (p *CompileCodeArtifactRequest) SetCodeID(val int64) {
	p.CodeID = val
}
func (p *CompileCodeArtifactRequest) SetCodeVersion(val int32) {
	p.CodeVersion = val
}
func (p *CompileCodeArtifactRequest) SetLinkPrefix(val string) {
	p.LinkPrefix = val
}
func (p *CompileCodeArtifactRequest) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_CompileCodeArtifactRequest = map[int16]string{
	1:   "UserID",
	2:   "MessageID",
	3:   "ConversionID",
	4:   "CodeID",
	5:   "CodeVersion",
	6:   "LinkPrefix",
	255: "Base",
}

func (p *CompileCodeArtifactRequest) IsSetMessageID() bool {
	return p.MessageID != nil
}

func (p *CompileCodeArtifactRequest) IsSetConversionID() bool {
	return p.ConversionID != nil
}

func (p *CompileCodeArtifactRequest) IsSetBase() bool {
	return p.Base != nil
}

func (p *CompileCodeArtifactRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CompileCodeArtifactRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *CompileCodeArtifactRequest) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UserID = _field
	return nil
}
func (p *CompileCodeArtifactRequest) ReadField2(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.MessageID = _field
	return nil
}
func (p *CompileCodeArtifactRequest) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ConversionID = _field
	return nil
}
func (p *CompileCodeArtifactRequest) ReadField4(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CodeID = _field
	return nil
}
func (p *CompileCodeArtifactRequest) ReadField5(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CodeVersion = _field
	return nil
}
func (p *CompileCodeArtifactRequest) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.LinkPrefix = _field
	return nil
}
func (p *CompileCodeArtifactRequest) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *CompileCodeArtifactRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("CompileCodeArtifactRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CompileCodeArtifactRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("UserID", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.UserID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *CompileCodeArtifactRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetMessageID() {
		if err = oprot.WriteFieldBegin("MessageID", thrift.I64, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.MessageID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *CompileCodeArtifactRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetConversionID() {
		if err = oprot.WriteFieldBegin("ConversionID", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ConversionID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *CompileCodeArtifactRequest) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CodeID", thrift.I64, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.CodeID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *CompileCodeArtifactRequest) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CodeVersion", thrift.I32, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.CodeVersion); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *CompileCodeArtifactRequest) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("LinkPrefix", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.LinkPrefix); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *CompileCodeArtifactRequest) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *CompileCodeArtifactRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CompileCodeArtifactRequest(%+v)", *p)

}

func (p *CompileCodeArtifactRequest) DeepEqual(ano *CompileCodeArtifactRequest) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.UserID) {
		return false
	}
	if !p.Field2DeepEqual(ano.MessageID) {
		return false
	}
	if !p.Field3DeepEqual(ano.ConversionID) {
		return false
	}
	if !p.Field4DeepEqual(ano.CodeID) {
		return false
	}
	if !p.Field5DeepEqual(ano.CodeVersion) {
		return false
	}
	if !p.Field6DeepEqual(ano.LinkPrefix) {
		return false
	}
	if !p.Field255DeepEqual(ano.Base) {
		return false
	}
	return true
}

func (p *CompileCodeArtifactRequest) Field1DeepEqual(src int64) bool {

	if p.UserID != src {
		return false
	}
	return true
}
func (p *CompileCodeArtifactRequest) Field2DeepEqual(src *int64) bool {

	if p.MessageID == src {
		return true
	} else if p.MessageID == nil || src == nil {
		return false
	}
	if *p.MessageID != *src {
		return false
	}
	return true
}
func (p *CompileCodeArtifactRequest) Field3DeepEqual(src *string) bool {

	if p.ConversionID == src {
		return true
	} else if p.ConversionID == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ConversionID, *src) != 0 {
		return false
	}
	return true
}
func (p *CompileCodeArtifactRequest) Field4DeepEqual(src int64) bool {

	if p.CodeID != src {
		return false
	}
	return true
}
func (p *CompileCodeArtifactRequest) Field5DeepEqual(src int32) bool {

	if p.CodeVersion != src {
		return false
	}
	return true
}
func (p *CompileCodeArtifactRequest) Field6DeepEqual(src string) bool {

	if strings.Compare(p.LinkPrefix, src) != 0 {
		return false
	}
	return true
}
func (p *CompileCodeArtifactRequest) Field255DeepEqual(src *base.Base) bool {

	if !p.Base.DeepEqual(src) {
		return false
	}
	return true
}

type CompileCodeArtifactResponse struct {
	BaseResp *base.BaseResp `thrift:"BaseResp,255,optional" frugal:"255,optional,base.BaseResp" json:"BaseResp,omitempty"`
}

func NewCompileCodeArtifactResponse() *CompileCodeArtifactResponse {
	return &CompileCodeArtifactResponse{}
}

func (p *CompileCodeArtifactResponse) InitDefault() {
}

var CompileCodeArtifactResponse_BaseResp_DEFAULT *base.BaseResp

func (p *CompileCodeArtifactResponse) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return CompileCodeArtifactResponse_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *CompileCodeArtifactResponse) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_CompileCodeArtifactResponse = map[int16]string{
	255: "BaseResp",
}

func (p *CompileCodeArtifactResponse) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *CompileCodeArtifactResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CompileCodeArtifactResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *CompileCodeArtifactResponse) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *CompileCodeArtifactResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("CompileCodeArtifactResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CompileCodeArtifactResponse) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBaseResp() {
		if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.BaseResp.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *CompileCodeArtifactResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CompileCodeArtifactResponse(%+v)", *p)

}

func (p *CompileCodeArtifactResponse) DeepEqual(ano *CompileCodeArtifactResponse) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field255DeepEqual(ano.BaseResp) {
		return false
	}
	return true
}

func (p *CompileCodeArtifactResponse) Field255DeepEqual(src *base.BaseResp) bool {

	if !p.BaseResp.DeepEqual(src) {
		return false
	}
	return true
}

type GetCompileStatusRequest struct {
	UserID      int64      `thrift:"UserID,1" frugal:"1,default,i64" json:"UserID"`
	CodeID      int64      `thrift:"CodeID,2" frugal:"2,default,i64" json:"CodeID"`
	CodeVersion int32      `thrift:"CodeVersion,3" frugal:"3,default,i32" json:"CodeVersion"`
	Base        *base.Base `thrift:"Base,255,optional" frugal:"255,optional,base.Base" json:"Base,omitempty"`
}

func NewGetCompileStatusRequest() *GetCompileStatusRequest {
	return &GetCompileStatusRequest{}
}

func (p *GetCompileStatusRequest) InitDefault() {
}

func (p *GetCompileStatusRequest) GetUserID() (v int64) {
	return p.UserID
}

func (p *GetCompileStatusRequest) GetCodeID() (v int64) {
	return p.CodeID
}

func (p *GetCompileStatusRequest) GetCodeVersion() (v int32) {
	return p.CodeVersion
}

var GetCompileStatusRequest_Base_DEFAULT *base.Base

func (p *GetCompileStatusRequest) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return GetCompileStatusRequest_Base_DEFAULT
	}
	return p.Base
}
func (p *GetCompileStatusRequest) SetUserID(val int64) {
	p.UserID = val
}
func (p *GetCompileStatusRequest) SetCodeID(val int64) {
	p.CodeID = val
}
func (p *GetCompileStatusRequest) SetCodeVersion(val int32) {
	p.CodeVersion = val
}
func (p *GetCompileStatusRequest) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_GetCompileStatusRequest = map[int16]string{
	1:   "UserID",
	2:   "CodeID",
	3:   "CodeVersion",
	255: "Base",
}

func (p *GetCompileStatusRequest) IsSetBase() bool {
	return p.Base != nil
}

func (p *GetCompileStatusRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetCompileStatusRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *GetCompileStatusRequest) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UserID = _field
	return nil
}
func (p *GetCompileStatusRequest) ReadField2(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CodeID = _field
	return nil
}
func (p *GetCompileStatusRequest) ReadField3(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CodeVersion = _field
	return nil
}
func (p *GetCompileStatusRequest) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *GetCompileStatusRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetCompileStatusRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetCompileStatusRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("UserID", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.UserID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *GetCompileStatusRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CodeID", thrift.I64, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.CodeID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *GetCompileStatusRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CodeVersion", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.CodeVersion); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *GetCompileStatusRequest) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *GetCompileStatusRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetCompileStatusRequest(%+v)", *p)

}

func (p *GetCompileStatusRequest) DeepEqual(ano *GetCompileStatusRequest) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.UserID) {
		return false
	}
	if !p.Field2DeepEqual(ano.CodeID) {
		return false
	}
	if !p.Field3DeepEqual(ano.CodeVersion) {
		return false
	}
	if !p.Field255DeepEqual(ano.Base) {
		return false
	}
	return true
}

func (p *GetCompileStatusRequest) Field1DeepEqual(src int64) bool {

	if p.UserID != src {
		return false
	}
	return true
}
func (p *GetCompileStatusRequest) Field2DeepEqual(src int64) bool {

	if p.CodeID != src {
		return false
	}
	return true
}
func (p *GetCompileStatusRequest) Field3DeepEqual(src int32) bool {

	if p.CodeVersion != src {
		return false
	}
	return true
}
func (p *GetCompileStatusRequest) Field255DeepEqual(src *base.Base) bool {

	if !p.Base.DeepEqual(src) {
		return false
	}
	return true
}

type GetCompileStatusResponse struct {
	Status         CompileStatus  `thrift:"Status,1" frugal:"1,default,CompileStatus" json:"Status"`
	CompileResult_ string         `thrift:"CompileResult,2" frugal:"2,default,string" json:"CompileResult"`
	BaseResp       *base.BaseResp `thrift:"BaseResp,255,optional" frugal:"255,optional,base.BaseResp" json:"BaseResp,omitempty"`
}

func NewGetCompileStatusResponse() *GetCompileStatusResponse {
	return &GetCompileStatusResponse{}
}

func (p *GetCompileStatusResponse) InitDefault() {
}

func (p *GetCompileStatusResponse) GetStatus() (v CompileStatus) {
	return p.Status
}

func (p *GetCompileStatusResponse) GetCompileResult_() (v string) {
	return p.CompileResult_
}

var GetCompileStatusResponse_BaseResp_DEFAULT *base.BaseResp

func (p *GetCompileStatusResponse) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return GetCompileStatusResponse_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *GetCompileStatusResponse) SetStatus(val CompileStatus) {
	p.Status = val
}
func (p *GetCompileStatusResponse) SetCompileResult_(val string) {
	p.CompileResult_ = val
}
func (p *GetCompileStatusResponse) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_GetCompileStatusResponse = map[int16]string{
	1:   "Status",
	2:   "CompileResult",
	255: "BaseResp",
}

func (p *GetCompileStatusResponse) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *GetCompileStatusResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetCompileStatusResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *GetCompileStatusResponse) ReadField1(iprot thrift.TProtocol) error {

	var _field CompileStatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = CompileStatus(v)
	}
	p.Status = _field
	return nil
}
func (p *GetCompileStatusResponse) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CompileResult_ = _field
	return nil
}
func (p *GetCompileStatusResponse) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *GetCompileStatusResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetCompileStatusResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetCompileStatusResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Status", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.Status)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *GetCompileStatusResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CompileResult", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CompileResult_); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *GetCompileStatusResponse) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBaseResp() {
		if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.BaseResp.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *GetCompileStatusResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetCompileStatusResponse(%+v)", *p)

}

func (p *GetCompileStatusResponse) DeepEqual(ano *GetCompileStatusResponse) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Status) {
		return false
	}
	if !p.Field2DeepEqual(ano.CompileResult_) {
		return false
	}
	if !p.Field255DeepEqual(ano.BaseResp) {
		return false
	}
	return true
}

func (p *GetCompileStatusResponse) Field1DeepEqual(src CompileStatus) bool {

	if p.Status != src {
		return false
	}
	return true
}
func (p *GetCompileStatusResponse) Field2DeepEqual(src string) bool {

	if strings.Compare(p.CompileResult_, src) != 0 {
		return false
	}
	return true
}
func (p *GetCompileStatusResponse) Field255DeepEqual(src *base.BaseResp) bool {

	if !p.BaseResp.DeepEqual(src) {
		return false
	}
	return true
}

type GetArtifactsCodeURIRequest struct {
	CodeID      int64      `thrift:"CodeID,1" frugal:"1,default,i64" json:"CodeID"`
	CodeVersion int32      `thrift:"CodeVersion,2" frugal:"2,default,i32" json:"CodeVersion"`
	FileName    *string    `thrift:"FileName,3,optional" frugal:"3,optional,string" json:"FileName,omitempty"`
	Base        *base.Base `thrift:"Base,255,optional" frugal:"255,optional,base.Base" json:"Base,omitempty"`
}

func NewGetArtifactsCodeURIRequest() *GetArtifactsCodeURIRequest {
	return &GetArtifactsCodeURIRequest{}
}

func (p *GetArtifactsCodeURIRequest) InitDefault() {
}

func (p *GetArtifactsCodeURIRequest) GetCodeID() (v int64) {
	return p.CodeID
}

func (p *GetArtifactsCodeURIRequest) GetCodeVersion() (v int32) {
	return p.CodeVersion
}

var GetArtifactsCodeURIRequest_FileName_DEFAULT string

func (p *GetArtifactsCodeURIRequest) GetFileName() (v string) {
	if !p.IsSetFileName() {
		return GetArtifactsCodeURIRequest_FileName_DEFAULT
	}
	return *p.FileName
}

var GetArtifactsCodeURIRequest_Base_DEFAULT *base.Base

func (p *GetArtifactsCodeURIRequest) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return GetArtifactsCodeURIRequest_Base_DEFAULT
	}
	return p.Base
}
func (p *GetArtifactsCodeURIRequest) SetCodeID(val int64) {
	p.CodeID = val
}
func (p *GetArtifactsCodeURIRequest) SetCodeVersion(val int32) {
	p.CodeVersion = val
}
func (p *GetArtifactsCodeURIRequest) SetFileName(val *string) {
	p.FileName = val
}
func (p *GetArtifactsCodeURIRequest) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_GetArtifactsCodeURIRequest = map[int16]string{
	1:   "CodeID",
	2:   "CodeVersion",
	3:   "FileName",
	255: "Base",
}

func (p *GetArtifactsCodeURIRequest) IsSetFileName() bool {
	return p.FileName != nil
}

func (p *GetArtifactsCodeURIRequest) IsSetBase() bool {
	return p.Base != nil
}

func (p *GetArtifactsCodeURIRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetArtifactsCodeURIRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *GetArtifactsCodeURIRequest) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CodeID = _field
	return nil
}
func (p *GetArtifactsCodeURIRequest) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CodeVersion = _field
	return nil
}
func (p *GetArtifactsCodeURIRequest) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.FileName = _field
	return nil
}
func (p *GetArtifactsCodeURIRequest) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *GetArtifactsCodeURIRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetArtifactsCodeURIRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetArtifactsCodeURIRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CodeID", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.CodeID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *GetArtifactsCodeURIRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CodeVersion", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.CodeVersion); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *GetArtifactsCodeURIRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetFileName() {
		if err = oprot.WriteFieldBegin("FileName", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.FileName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *GetArtifactsCodeURIRequest) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *GetArtifactsCodeURIRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetArtifactsCodeURIRequest(%+v)", *p)

}

func (p *GetArtifactsCodeURIRequest) DeepEqual(ano *GetArtifactsCodeURIRequest) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.CodeID) {
		return false
	}
	if !p.Field2DeepEqual(ano.CodeVersion) {
		return false
	}
	if !p.Field3DeepEqual(ano.FileName) {
		return false
	}
	if !p.Field255DeepEqual(ano.Base) {
		return false
	}
	return true
}

func (p *GetArtifactsCodeURIRequest) Field1DeepEqual(src int64) bool {

	if p.CodeID != src {
		return false
	}
	return true
}
func (p *GetArtifactsCodeURIRequest) Field2DeepEqual(src int32) bool {

	if p.CodeVersion != src {
		return false
	}
	return true
}
func (p *GetArtifactsCodeURIRequest) Field3DeepEqual(src *string) bool {

	if p.FileName == src {
		return true
	} else if p.FileName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.FileName, *src) != 0 {
		return false
	}
	return true
}
func (p *GetArtifactsCodeURIRequest) Field255DeepEqual(src *base.Base) bool {

	if !p.Base.DeepEqual(src) {
		return false
	}
	return true
}

type GetArtifactsCodeURIResponse struct {
	ArtifactsCodeURI string         `thrift:"ArtifactsCodeURI,1" frugal:"1,default,string" json:"ArtifactsCodeURI"`
	BaseResp         *base.BaseResp `thrift:"BaseResp,255,optional" frugal:"255,optional,base.BaseResp" json:"BaseResp,omitempty"`
}

func NewGetArtifactsCodeURIResponse() *GetArtifactsCodeURIResponse {
	return &GetArtifactsCodeURIResponse{}
}

func (p *GetArtifactsCodeURIResponse) InitDefault() {
}

func (p *GetArtifactsCodeURIResponse) GetArtifactsCodeURI() (v string) {
	return p.ArtifactsCodeURI
}

var GetArtifactsCodeURIResponse_BaseResp_DEFAULT *base.BaseResp

func (p *GetArtifactsCodeURIResponse) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return GetArtifactsCodeURIResponse_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *GetArtifactsCodeURIResponse) SetArtifactsCodeURI(val string) {
	p.ArtifactsCodeURI = val
}
func (p *GetArtifactsCodeURIResponse) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_GetArtifactsCodeURIResponse = map[int16]string{
	1:   "ArtifactsCodeURI",
	255: "BaseResp",
}

func (p *GetArtifactsCodeURIResponse) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *GetArtifactsCodeURIResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetArtifactsCodeURIResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *GetArtifactsCodeURIResponse) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ArtifactsCodeURI = _field
	return nil
}
func (p *GetArtifactsCodeURIResponse) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *GetArtifactsCodeURIResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetArtifactsCodeURIResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetArtifactsCodeURIResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ArtifactsCodeURI", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ArtifactsCodeURI); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *GetArtifactsCodeURIResponse) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBaseResp() {
		if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.BaseResp.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *GetArtifactsCodeURIResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetArtifactsCodeURIResponse(%+v)", *p)

}

func (p *GetArtifactsCodeURIResponse) DeepEqual(ano *GetArtifactsCodeURIResponse) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ArtifactsCodeURI) {
		return false
	}
	if !p.Field255DeepEqual(ano.BaseResp) {
		return false
	}
	return true
}

func (p *GetArtifactsCodeURIResponse) Field1DeepEqual(src string) bool {

	if strings.Compare(p.ArtifactsCodeURI, src) != 0 {
		return false
	}
	return true
}
func (p *GetArtifactsCodeURIResponse) Field255DeepEqual(src *base.BaseResp) bool {

	if !p.BaseResp.DeepEqual(src) {
		return false
	}
	return true
}

type SandboxInentifier struct {
	AllocationID string              `thrift:"AllocationID,1" frugal:"1,default,string" json:"AllocationID"`
	SessionID    string              `thrift:"SessionID,2" frugal:"2,default,string" json:"SessionID"`
	FunctionType SandboxFunctionType `thrift:"FunctionType,3" frugal:"3,default,SandboxFunctionType" json:"FunctionType"`
	SandboxType  SandboxType         `thrift:"SandboxType,4" frugal:"4,default,SandboxType" json:"SandboxType"`
}

func NewSandboxInentifier() *SandboxInentifier {
	return &SandboxInentifier{}
}

func (p *SandboxInentifier) InitDefault() {
}

func (p *SandboxInentifier) GetAllocationID() (v string) {
	return p.AllocationID
}

func (p *SandboxInentifier) GetSessionID() (v string) {
	return p.SessionID
}

func (p *SandboxInentifier) GetFunctionType() (v SandboxFunctionType) {
	return p.FunctionType
}

func (p *SandboxInentifier) GetSandboxType() (v SandboxType) {
	return p.SandboxType
}
func (p *SandboxInentifier) SetAllocationID(val string) {
	p.AllocationID = val
}
func (p *SandboxInentifier) SetSessionID(val string) {
	p.SessionID = val
}
func (p *SandboxInentifier) SetFunctionType(val SandboxFunctionType) {
	p.FunctionType = val
}
func (p *SandboxInentifier) SetSandboxType(val SandboxType) {
	p.SandboxType = val
}

var fieldIDToName_SandboxInentifier = map[int16]string{
	1: "AllocationID",
	2: "SessionID",
	3: "FunctionType",
	4: "SandboxType",
}

func (p *SandboxInentifier) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SandboxInentifier[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *SandboxInentifier) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AllocationID = _field
	return nil
}
func (p *SandboxInentifier) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SessionID = _field
	return nil
}
func (p *SandboxInentifier) ReadField3(iprot thrift.TProtocol) error {

	var _field SandboxFunctionType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = SandboxFunctionType(v)
	}
	p.FunctionType = _field
	return nil
}
func (p *SandboxInentifier) ReadField4(iprot thrift.TProtocol) error {

	var _field SandboxType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = SandboxType(v)
	}
	p.SandboxType = _field
	return nil
}

func (p *SandboxInentifier) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("SandboxInentifier"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SandboxInentifier) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AllocationID", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AllocationID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *SandboxInentifier) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SessionID", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SessionID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *SandboxInentifier) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("FunctionType", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.FunctionType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *SandboxInentifier) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SandboxType", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.SandboxType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *SandboxInentifier) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SandboxInentifier(%+v)", *p)

}

func (p *SandboxInentifier) DeepEqual(ano *SandboxInentifier) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.AllocationID) {
		return false
	}
	if !p.Field2DeepEqual(ano.SessionID) {
		return false
	}
	if !p.Field3DeepEqual(ano.FunctionType) {
		return false
	}
	if !p.Field4DeepEqual(ano.SandboxType) {
		return false
	}
	return true
}

func (p *SandboxInentifier) Field1DeepEqual(src string) bool {

	if strings.Compare(p.AllocationID, src) != 0 {
		return false
	}
	return true
}
func (p *SandboxInentifier) Field2DeepEqual(src string) bool {

	if strings.Compare(p.SessionID, src) != 0 {
		return false
	}
	return true
}
func (p *SandboxInentifier) Field3DeepEqual(src SandboxFunctionType) bool {

	if p.FunctionType != src {
		return false
	}
	return true
}
func (p *SandboxInentifier) Field4DeepEqual(src SandboxType) bool {

	if p.SandboxType != src {
		return false
	}
	return true
}

type CreateSandboxRequest struct {
	SandboxInentifier *SandboxInentifier `thrift:"SandboxInentifier,1,required" frugal:"1,required,SandboxInentifier" json:"SandboxInentifier"`
	AliveTime         *int64             `thrift:"AliveTime,2,optional" frugal:"2,optional,i64" json:"AliveTime,omitempty"`
	Base              *base.Base         `thrift:"Base,255,optional" frugal:"255,optional,base.Base" json:"Base,omitempty"`
}

func NewCreateSandboxRequest() *CreateSandboxRequest {
	return &CreateSandboxRequest{}
}

func (p *CreateSandboxRequest) InitDefault() {
}

var CreateSandboxRequest_SandboxInentifier_DEFAULT *SandboxInentifier

func (p *CreateSandboxRequest) GetSandboxInentifier() (v *SandboxInentifier) {
	if !p.IsSetSandboxInentifier() {
		return CreateSandboxRequest_SandboxInentifier_DEFAULT
	}
	return p.SandboxInentifier
}

var CreateSandboxRequest_AliveTime_DEFAULT int64

func (p *CreateSandboxRequest) GetAliveTime() (v int64) {
	if !p.IsSetAliveTime() {
		return CreateSandboxRequest_AliveTime_DEFAULT
	}
	return *p.AliveTime
}

var CreateSandboxRequest_Base_DEFAULT *base.Base

func (p *CreateSandboxRequest) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return CreateSandboxRequest_Base_DEFAULT
	}
	return p.Base
}
func (p *CreateSandboxRequest) SetSandboxInentifier(val *SandboxInentifier) {
	p.SandboxInentifier = val
}
func (p *CreateSandboxRequest) SetAliveTime(val *int64) {
	p.AliveTime = val
}
func (p *CreateSandboxRequest) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_CreateSandboxRequest = map[int16]string{
	1:   "SandboxInentifier",
	2:   "AliveTime",
	255: "Base",
}

func (p *CreateSandboxRequest) IsSetSandboxInentifier() bool {
	return p.SandboxInentifier != nil
}

func (p *CreateSandboxRequest) IsSetAliveTime() bool {
	return p.AliveTime != nil
}

func (p *CreateSandboxRequest) IsSetBase() bool {
	return p.Base != nil
}

func (p *CreateSandboxRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSandboxInentifier bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSandboxInentifier = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSandboxInentifier {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateSandboxRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateSandboxRequest[fieldId]))
}

func (p *CreateSandboxRequest) ReadField1(iprot thrift.TProtocol) error {
	_field := NewSandboxInentifier()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.SandboxInentifier = _field
	return nil
}
func (p *CreateSandboxRequest) ReadField2(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.AliveTime = _field
	return nil
}
func (p *CreateSandboxRequest) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *CreateSandboxRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("CreateSandboxRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateSandboxRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SandboxInentifier", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.SandboxInentifier.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *CreateSandboxRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetAliveTime() {
		if err = oprot.WriteFieldBegin("AliveTime", thrift.I64, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.AliveTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *CreateSandboxRequest) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *CreateSandboxRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateSandboxRequest(%+v)", *p)

}

func (p *CreateSandboxRequest) DeepEqual(ano *CreateSandboxRequest) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SandboxInentifier) {
		return false
	}
	if !p.Field2DeepEqual(ano.AliveTime) {
		return false
	}
	if !p.Field255DeepEqual(ano.Base) {
		return false
	}
	return true
}

func (p *CreateSandboxRequest) Field1DeepEqual(src *SandboxInentifier) bool {

	if !p.SandboxInentifier.DeepEqual(src) {
		return false
	}
	return true
}
func (p *CreateSandboxRequest) Field2DeepEqual(src *int64) bool {

	if p.AliveTime == src {
		return true
	} else if p.AliveTime == nil || src == nil {
		return false
	}
	if *p.AliveTime != *src {
		return false
	}
	return true
}
func (p *CreateSandboxRequest) Field255DeepEqual(src *base.Base) bool {

	if !p.Base.DeepEqual(src) {
		return false
	}
	return true
}

type CreateSandboxResponse struct {
	SandboxInentifier *SandboxInentifier `thrift:"SandboxInentifier,1" frugal:"1,default,SandboxInentifier" json:"SandboxInentifier"`
	BaseResp          *base.BaseResp     `thrift:"BaseResp,255,optional" frugal:"255,optional,base.BaseResp" json:"BaseResp,omitempty"`
}

func NewCreateSandboxResponse() *CreateSandboxResponse {
	return &CreateSandboxResponse{}
}

func (p *CreateSandboxResponse) InitDefault() {
}

var CreateSandboxResponse_SandboxInentifier_DEFAULT *SandboxInentifier

func (p *CreateSandboxResponse) GetSandboxInentifier() (v *SandboxInentifier) {
	if !p.IsSetSandboxInentifier() {
		return CreateSandboxResponse_SandboxInentifier_DEFAULT
	}
	return p.SandboxInentifier
}

var CreateSandboxResponse_BaseResp_DEFAULT *base.BaseResp

func (p *CreateSandboxResponse) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return CreateSandboxResponse_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *CreateSandboxResponse) SetSandboxInentifier(val *SandboxInentifier) {
	p.SandboxInentifier = val
}
func (p *CreateSandboxResponse) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_CreateSandboxResponse = map[int16]string{
	1:   "SandboxInentifier",
	255: "BaseResp",
}

func (p *CreateSandboxResponse) IsSetSandboxInentifier() bool {
	return p.SandboxInentifier != nil
}

func (p *CreateSandboxResponse) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *CreateSandboxResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateSandboxResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *CreateSandboxResponse) ReadField1(iprot thrift.TProtocol) error {
	_field := NewSandboxInentifier()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.SandboxInentifier = _field
	return nil
}
func (p *CreateSandboxResponse) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *CreateSandboxResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("CreateSandboxResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateSandboxResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SandboxInentifier", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.SandboxInentifier.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *CreateSandboxResponse) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBaseResp() {
		if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.BaseResp.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *CreateSandboxResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateSandboxResponse(%+v)", *p)

}

func (p *CreateSandboxResponse) DeepEqual(ano *CreateSandboxResponse) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SandboxInentifier) {
		return false
	}
	if !p.Field255DeepEqual(ano.BaseResp) {
		return false
	}
	return true
}

func (p *CreateSandboxResponse) Field1DeepEqual(src *SandboxInentifier) bool {

	if !p.SandboxInentifier.DeepEqual(src) {
		return false
	}
	return true
}
func (p *CreateSandboxResponse) Field255DeepEqual(src *base.BaseResp) bool {

	if !p.BaseResp.DeepEqual(src) {
		return false
	}
	return true
}

type ReleaseSandboxRequest struct {
	SandboxIdentifier *SandboxInentifier `thrift:"SandboxIdentifier,1,required" frugal:"1,required,SandboxInentifier" json:"SandboxIdentifier"`
	Base              *base.Base         `thrift:"Base,255,optional" frugal:"255,optional,base.Base" json:"Base,omitempty"`
}

func NewReleaseSandboxRequest() *ReleaseSandboxRequest {
	return &ReleaseSandboxRequest{}
}

func (p *ReleaseSandboxRequest) InitDefault() {
}

var ReleaseSandboxRequest_SandboxIdentifier_DEFAULT *SandboxInentifier

func (p *ReleaseSandboxRequest) GetSandboxIdentifier() (v *SandboxInentifier) {
	if !p.IsSetSandboxIdentifier() {
		return ReleaseSandboxRequest_SandboxIdentifier_DEFAULT
	}
	return p.SandboxIdentifier
}

var ReleaseSandboxRequest_Base_DEFAULT *base.Base

func (p *ReleaseSandboxRequest) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return ReleaseSandboxRequest_Base_DEFAULT
	}
	return p.Base
}
func (p *ReleaseSandboxRequest) SetSandboxIdentifier(val *SandboxInentifier) {
	p.SandboxIdentifier = val
}
func (p *ReleaseSandboxRequest) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_ReleaseSandboxRequest = map[int16]string{
	1:   "SandboxIdentifier",
	255: "Base",
}

func (p *ReleaseSandboxRequest) IsSetSandboxIdentifier() bool {
	return p.SandboxIdentifier != nil
}

func (p *ReleaseSandboxRequest) IsSetBase() bool {
	return p.Base != nil
}

func (p *ReleaseSandboxRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSandboxIdentifier bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSandboxIdentifier = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSandboxIdentifier {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ReleaseSandboxRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ReleaseSandboxRequest[fieldId]))
}

func (p *ReleaseSandboxRequest) ReadField1(iprot thrift.TProtocol) error {
	_field := NewSandboxInentifier()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.SandboxIdentifier = _field
	return nil
}
func (p *ReleaseSandboxRequest) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *ReleaseSandboxRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ReleaseSandboxRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ReleaseSandboxRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SandboxIdentifier", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.SandboxIdentifier.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ReleaseSandboxRequest) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *ReleaseSandboxRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ReleaseSandboxRequest(%+v)", *p)

}

func (p *ReleaseSandboxRequest) DeepEqual(ano *ReleaseSandboxRequest) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SandboxIdentifier) {
		return false
	}
	if !p.Field255DeepEqual(ano.Base) {
		return false
	}
	return true
}

func (p *ReleaseSandboxRequest) Field1DeepEqual(src *SandboxInentifier) bool {

	if !p.SandboxIdentifier.DeepEqual(src) {
		return false
	}
	return true
}
func (p *ReleaseSandboxRequest) Field255DeepEqual(src *base.Base) bool {

	if !p.Base.DeepEqual(src) {
		return false
	}
	return true
}

type ReleaseSandboxResponse struct {
	BaseResp *base.BaseResp `thrift:"BaseResp,255,optional" frugal:"255,optional,base.BaseResp" json:"BaseResp,omitempty"`
}

func NewReleaseSandboxResponse() *ReleaseSandboxResponse {
	return &ReleaseSandboxResponse{}
}

func (p *ReleaseSandboxResponse) InitDefault() {
}

var ReleaseSandboxResponse_BaseResp_DEFAULT *base.BaseResp

func (p *ReleaseSandboxResponse) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return ReleaseSandboxResponse_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *ReleaseSandboxResponse) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_ReleaseSandboxResponse = map[int16]string{
	255: "BaseResp",
}

func (p *ReleaseSandboxResponse) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *ReleaseSandboxResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ReleaseSandboxResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ReleaseSandboxResponse) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *ReleaseSandboxResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ReleaseSandboxResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ReleaseSandboxResponse) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBaseResp() {
		if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.BaseResp.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *ReleaseSandboxResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ReleaseSandboxResponse(%+v)", *p)

}

func (p *ReleaseSandboxResponse) DeepEqual(ano *ReleaseSandboxResponse) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field255DeepEqual(ano.BaseResp) {
		return false
	}
	return true
}

func (p *ReleaseSandboxResponse) Field255DeepEqual(src *base.BaseResp) bool {

	if !p.BaseResp.DeepEqual(src) {
		return false
	}
	return true
}

type ExecuteDetail struct {
	Code               *string           `thrift:"Code,1,optional" frugal:"1,optional,string" json:"Code,omitempty"`
	CopyRuntimeTimeout *float64          `thrift:"CopyRuntimeTimeout,2,optional" frugal:"2,optional,double" json:"CopyRuntimeTimeout,omitempty"`
	CompileTimeout     *float64          `thrift:"CompileTimeout,3,optional" frugal:"3,optional,double" json:"CompileTimeout,omitempty"`
	RunTimeout         *float64          `thrift:"RunTimeout,4,optional" frugal:"4,optional,double" json:"RunTimeout,omitempty"`
	Stdin              *string           `thrift:"Stdin,5,optional" frugal:"5,optional,string" json:"Stdin,omitempty"`
	Language           string            `thrift:"Language,6" frugal:"6,default,string" json:"Language"`
	Files              map[string]string `thrift:"Files,7,optional" frugal:"7,optional,map<string:string>" json:"Files,omitempty"`
	FetchFiles         []string          `thrift:"FetchFiles,8,optional" frugal:"8,optional,list<string>" json:"FetchFiles,omitempty"`
	LinkPrefix         string            `thrift:"LinkPrefix,9" frugal:"9,default,string" json:"LinkPrefix"`
	SessionID          *string           `thrift:"SessionID,10,optional" frugal:"10,optional,string" json:"SessionID,omitempty"`
	ShareContext       *bool             `thrift:"ShareContext,11,optional" frugal:"11,optional,bool" json:"ShareContext,omitempty"`
	CWD                *string           `thrift:"CWD,12,optional" frugal:"12,optional,string" json:"CWD,omitempty"`
}

func NewExecuteDetail() *ExecuteDetail {
	return &ExecuteDetail{}
}

func (p *ExecuteDetail) InitDefault() {
}

var ExecuteDetail_Code_DEFAULT string

func (p *ExecuteDetail) GetCode() (v string) {
	if !p.IsSetCode() {
		return ExecuteDetail_Code_DEFAULT
	}
	return *p.Code
}

var ExecuteDetail_CopyRuntimeTimeout_DEFAULT float64

func (p *ExecuteDetail) GetCopyRuntimeTimeout() (v float64) {
	if !p.IsSetCopyRuntimeTimeout() {
		return ExecuteDetail_CopyRuntimeTimeout_DEFAULT
	}
	return *p.CopyRuntimeTimeout
}

var ExecuteDetail_CompileTimeout_DEFAULT float64

func (p *ExecuteDetail) GetCompileTimeout() (v float64) {
	if !p.IsSetCompileTimeout() {
		return ExecuteDetail_CompileTimeout_DEFAULT
	}
	return *p.CompileTimeout
}

var ExecuteDetail_RunTimeout_DEFAULT float64

func (p *ExecuteDetail) GetRunTimeout() (v float64) {
	if !p.IsSetRunTimeout() {
		return ExecuteDetail_RunTimeout_DEFAULT
	}
	return *p.RunTimeout
}

var ExecuteDetail_Stdin_DEFAULT string

func (p *ExecuteDetail) GetStdin() (v string) {
	if !p.IsSetStdin() {
		return ExecuteDetail_Stdin_DEFAULT
	}
	return *p.Stdin
}

func (p *ExecuteDetail) GetLanguage() (v string) {
	return p.Language
}

var ExecuteDetail_Files_DEFAULT map[string]string

func (p *ExecuteDetail) GetFiles() (v map[string]string) {
	if !p.IsSetFiles() {
		return ExecuteDetail_Files_DEFAULT
	}
	return p.Files
}

var ExecuteDetail_FetchFiles_DEFAULT []string

func (p *ExecuteDetail) GetFetchFiles() (v []string) {
	if !p.IsSetFetchFiles() {
		return ExecuteDetail_FetchFiles_DEFAULT
	}
	return p.FetchFiles
}

func (p *ExecuteDetail) GetLinkPrefix() (v string) {
	return p.LinkPrefix
}

var ExecuteDetail_SessionID_DEFAULT string

func (p *ExecuteDetail) GetSessionID() (v string) {
	if !p.IsSetSessionID() {
		return ExecuteDetail_SessionID_DEFAULT
	}
	return *p.SessionID
}

var ExecuteDetail_ShareContext_DEFAULT bool

func (p *ExecuteDetail) GetShareContext() (v bool) {
	if !p.IsSetShareContext() {
		return ExecuteDetail_ShareContext_DEFAULT
	}
	return *p.ShareContext
}

var ExecuteDetail_CWD_DEFAULT string

func (p *ExecuteDetail) GetCWD() (v string) {
	if !p.IsSetCWD() {
		return ExecuteDetail_CWD_DEFAULT
	}
	return *p.CWD
}
func (p *ExecuteDetail) SetCode(val *string) {
	p.Code = val
}
func (p *ExecuteDetail) SetCopyRuntimeTimeout(val *float64) {
	p.CopyRuntimeTimeout = val
}
func (p *ExecuteDetail) SetCompileTimeout(val *float64) {
	p.CompileTimeout = val
}
func (p *ExecuteDetail) SetRunTimeout(val *float64) {
	p.RunTimeout = val
}
func (p *ExecuteDetail) SetStdin(val *string) {
	p.Stdin = val
}
func (p *ExecuteDetail) SetLanguage(val string) {
	p.Language = val
}
func (p *ExecuteDetail) SetFiles(val map[string]string) {
	p.Files = val
}
func (p *ExecuteDetail) SetFetchFiles(val []string) {
	p.FetchFiles = val
}
func (p *ExecuteDetail) SetLinkPrefix(val string) {
	p.LinkPrefix = val
}
func (p *ExecuteDetail) SetSessionID(val *string) {
	p.SessionID = val
}
func (p *ExecuteDetail) SetShareContext(val *bool) {
	p.ShareContext = val
}
func (p *ExecuteDetail) SetCWD(val *string) {
	p.CWD = val
}

var fieldIDToName_ExecuteDetail = map[int16]string{
	1:  "Code",
	2:  "CopyRuntimeTimeout",
	3:  "CompileTimeout",
	4:  "RunTimeout",
	5:  "Stdin",
	6:  "Language",
	7:  "Files",
	8:  "FetchFiles",
	9:  "LinkPrefix",
	10: "SessionID",
	11: "ShareContext",
	12: "CWD",
}

func (p *ExecuteDetail) IsSetCode() bool {
	return p.Code != nil
}

func (p *ExecuteDetail) IsSetCopyRuntimeTimeout() bool {
	return p.CopyRuntimeTimeout != nil
}

func (p *ExecuteDetail) IsSetCompileTimeout() bool {
	return p.CompileTimeout != nil
}

func (p *ExecuteDetail) IsSetRunTimeout() bool {
	return p.RunTimeout != nil
}

func (p *ExecuteDetail) IsSetStdin() bool {
	return p.Stdin != nil
}

func (p *ExecuteDetail) IsSetFiles() bool {
	return p.Files != nil
}

func (p *ExecuteDetail) IsSetFetchFiles() bool {
	return p.FetchFiles != nil
}

func (p *ExecuteDetail) IsSetSessionID() bool {
	return p.SessionID != nil
}

func (p *ExecuteDetail) IsSetShareContext() bool {
	return p.ShareContext != nil
}

func (p *ExecuteDetail) IsSetCWD() bool {
	return p.CWD != nil
}

func (p *ExecuteDetail) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.DOUBLE {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.DOUBLE {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.DOUBLE {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ExecuteDetail[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ExecuteDetail) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Code = _field
	return nil
}
func (p *ExecuteDetail) ReadField2(iprot thrift.TProtocol) error {

	var _field *float64
	if v, err := iprot.ReadDouble(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.CopyRuntimeTimeout = _field
	return nil
}
func (p *ExecuteDetail) ReadField3(iprot thrift.TProtocol) error {

	var _field *float64
	if v, err := iprot.ReadDouble(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.CompileTimeout = _field
	return nil
}
func (p *ExecuteDetail) ReadField4(iprot thrift.TProtocol) error {

	var _field *float64
	if v, err := iprot.ReadDouble(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.RunTimeout = _field
	return nil
}
func (p *ExecuteDetail) ReadField5(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Stdin = _field
	return nil
}
func (p *ExecuteDetail) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Language = _field
	return nil
}
func (p *ExecuteDetail) ReadField7(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_key = v
		}

		var _val string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_val = v
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.Files = _field
	return nil
}
func (p *ExecuteDetail) ReadField8(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.FetchFiles = _field
	return nil
}
func (p *ExecuteDetail) ReadField9(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.LinkPrefix = _field
	return nil
}
func (p *ExecuteDetail) ReadField10(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.SessionID = _field
	return nil
}
func (p *ExecuteDetail) ReadField11(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ShareContext = _field
	return nil
}
func (p *ExecuteDetail) ReadField12(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.CWD = _field
	return nil
}

func (p *ExecuteDetail) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ExecuteDetail"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ExecuteDetail) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetCode() {
		if err = oprot.WriteFieldBegin("Code", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Code); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ExecuteDetail) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetCopyRuntimeTimeout() {
		if err = oprot.WriteFieldBegin("CopyRuntimeTimeout", thrift.DOUBLE, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteDouble(*p.CopyRuntimeTimeout); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ExecuteDetail) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetCompileTimeout() {
		if err = oprot.WriteFieldBegin("CompileTimeout", thrift.DOUBLE, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteDouble(*p.CompileTimeout); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *ExecuteDetail) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetRunTimeout() {
		if err = oprot.WriteFieldBegin("RunTimeout", thrift.DOUBLE, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteDouble(*p.RunTimeout); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *ExecuteDetail) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetStdin() {
		if err = oprot.WriteFieldBegin("Stdin", thrift.STRING, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Stdin); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *ExecuteDetail) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Language", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Language); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *ExecuteDetail) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetFiles() {
		if err = oprot.WriteFieldBegin("Files", thrift.MAP, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.Files)); err != nil {
			return err
		}
		for k, v := range p.Files {
			if err := oprot.WriteString(k); err != nil {
				return err
			}
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}
func (p *ExecuteDetail) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetFetchFiles() {
		if err = oprot.WriteFieldBegin("FetchFiles", thrift.LIST, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.FetchFiles)); err != nil {
			return err
		}
		for _, v := range p.FetchFiles {
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}
func (p *ExecuteDetail) writeField9(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("LinkPrefix", thrift.STRING, 9); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.LinkPrefix); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}
func (p *ExecuteDetail) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetSessionID() {
		if err = oprot.WriteFieldBegin("SessionID", thrift.STRING, 10); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.SessionID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}
func (p *ExecuteDetail) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetShareContext() {
		if err = oprot.WriteFieldBegin("ShareContext", thrift.BOOL, 11); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.ShareContext); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}
func (p *ExecuteDetail) writeField12(oprot thrift.TProtocol) (err error) {
	if p.IsSetCWD() {
		if err = oprot.WriteFieldBegin("CWD", thrift.STRING, 12); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.CWD); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}

func (p *ExecuteDetail) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ExecuteDetail(%+v)", *p)

}

func (p *ExecuteDetail) DeepEqual(ano *ExecuteDetail) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Code) {
		return false
	}
	if !p.Field2DeepEqual(ano.CopyRuntimeTimeout) {
		return false
	}
	if !p.Field3DeepEqual(ano.CompileTimeout) {
		return false
	}
	if !p.Field4DeepEqual(ano.RunTimeout) {
		return false
	}
	if !p.Field5DeepEqual(ano.Stdin) {
		return false
	}
	if !p.Field6DeepEqual(ano.Language) {
		return false
	}
	if !p.Field7DeepEqual(ano.Files) {
		return false
	}
	if !p.Field8DeepEqual(ano.FetchFiles) {
		return false
	}
	if !p.Field9DeepEqual(ano.LinkPrefix) {
		return false
	}
	if !p.Field10DeepEqual(ano.SessionID) {
		return false
	}
	if !p.Field11DeepEqual(ano.ShareContext) {
		return false
	}
	if !p.Field12DeepEqual(ano.CWD) {
		return false
	}
	return true
}

func (p *ExecuteDetail) Field1DeepEqual(src *string) bool {

	if p.Code == src {
		return true
	} else if p.Code == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Code, *src) != 0 {
		return false
	}
	return true
}
func (p *ExecuteDetail) Field2DeepEqual(src *float64) bool {

	if p.CopyRuntimeTimeout == src {
		return true
	} else if p.CopyRuntimeTimeout == nil || src == nil {
		return false
	}
	if *p.CopyRuntimeTimeout != *src {
		return false
	}
	return true
}
func (p *ExecuteDetail) Field3DeepEqual(src *float64) bool {

	if p.CompileTimeout == src {
		return true
	} else if p.CompileTimeout == nil || src == nil {
		return false
	}
	if *p.CompileTimeout != *src {
		return false
	}
	return true
}
func (p *ExecuteDetail) Field4DeepEqual(src *float64) bool {

	if p.RunTimeout == src {
		return true
	} else if p.RunTimeout == nil || src == nil {
		return false
	}
	if *p.RunTimeout != *src {
		return false
	}
	return true
}
func (p *ExecuteDetail) Field5DeepEqual(src *string) bool {

	if p.Stdin == src {
		return true
	} else if p.Stdin == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Stdin, *src) != 0 {
		return false
	}
	return true
}
func (p *ExecuteDetail) Field6DeepEqual(src string) bool {

	if strings.Compare(p.Language, src) != 0 {
		return false
	}
	return true
}
func (p *ExecuteDetail) Field7DeepEqual(src map[string]string) bool {

	if len(p.Files) != len(src) {
		return false
	}
	for k, v := range p.Files {
		_src := src[k]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *ExecuteDetail) Field8DeepEqual(src []string) bool {

	if len(p.FetchFiles) != len(src) {
		return false
	}
	for i, v := range p.FetchFiles {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *ExecuteDetail) Field9DeepEqual(src string) bool {

	if strings.Compare(p.LinkPrefix, src) != 0 {
		return false
	}
	return true
}
func (p *ExecuteDetail) Field10DeepEqual(src *string) bool {

	if p.SessionID == src {
		return true
	} else if p.SessionID == nil || src == nil {
		return false
	}
	if strings.Compare(*p.SessionID, *src) != 0 {
		return false
	}
	return true
}
func (p *ExecuteDetail) Field11DeepEqual(src *bool) bool {

	if p.ShareContext == src {
		return true
	} else if p.ShareContext == nil || src == nil {
		return false
	}
	if *p.ShareContext != *src {
		return false
	}
	return true
}
func (p *ExecuteDetail) Field12DeepEqual(src *string) bool {

	if p.CWD == src {
		return true
	} else if p.CWD == nil || src == nil {
		return false
	}
	if strings.Compare(*p.CWD, *src) != 0 {
		return false
	}
	return true
}

type ExecuteCodeRequest struct {
	SandboxInentifier *SandboxInentifier `thrift:"SandboxInentifier,1,required" frugal:"1,required,SandboxInentifier" json:"SandboxInentifier"`
	ExecuteDetails    *ExecuteDetail     `thrift:"ExecuteDetails,2" frugal:"2,default,ExecuteDetail" json:"ExecuteDetails"`
	Base              *base.Base         `thrift:"Base,255,optional" frugal:"255,optional,base.Base" json:"Base,omitempty"`
}

func NewExecuteCodeRequest() *ExecuteCodeRequest {
	return &ExecuteCodeRequest{}
}

func (p *ExecuteCodeRequest) InitDefault() {
}

var ExecuteCodeRequest_SandboxInentifier_DEFAULT *SandboxInentifier

func (p *ExecuteCodeRequest) GetSandboxInentifier() (v *SandboxInentifier) {
	if !p.IsSetSandboxInentifier() {
		return ExecuteCodeRequest_SandboxInentifier_DEFAULT
	}
	return p.SandboxInentifier
}

var ExecuteCodeRequest_ExecuteDetails_DEFAULT *ExecuteDetail

func (p *ExecuteCodeRequest) GetExecuteDetails() (v *ExecuteDetail) {
	if !p.IsSetExecuteDetails() {
		return ExecuteCodeRequest_ExecuteDetails_DEFAULT
	}
	return p.ExecuteDetails
}

var ExecuteCodeRequest_Base_DEFAULT *base.Base

func (p *ExecuteCodeRequest) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return ExecuteCodeRequest_Base_DEFAULT
	}
	return p.Base
}
func (p *ExecuteCodeRequest) SetSandboxInentifier(val *SandboxInentifier) {
	p.SandboxInentifier = val
}
func (p *ExecuteCodeRequest) SetExecuteDetails(val *ExecuteDetail) {
	p.ExecuteDetails = val
}
func (p *ExecuteCodeRequest) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_ExecuteCodeRequest = map[int16]string{
	1:   "SandboxInentifier",
	2:   "ExecuteDetails",
	255: "Base",
}

func (p *ExecuteCodeRequest) IsSetSandboxInentifier() bool {
	return p.SandboxInentifier != nil
}

func (p *ExecuteCodeRequest) IsSetExecuteDetails() bool {
	return p.ExecuteDetails != nil
}

func (p *ExecuteCodeRequest) IsSetBase() bool {
	return p.Base != nil
}

func (p *ExecuteCodeRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSandboxInentifier bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSandboxInentifier = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSandboxInentifier {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ExecuteCodeRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ExecuteCodeRequest[fieldId]))
}

func (p *ExecuteCodeRequest) ReadField1(iprot thrift.TProtocol) error {
	_field := NewSandboxInentifier()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.SandboxInentifier = _field
	return nil
}
func (p *ExecuteCodeRequest) ReadField2(iprot thrift.TProtocol) error {
	_field := NewExecuteDetail()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.ExecuteDetails = _field
	return nil
}
func (p *ExecuteCodeRequest) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *ExecuteCodeRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ExecuteCodeRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ExecuteCodeRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SandboxInentifier", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.SandboxInentifier.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ExecuteCodeRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ExecuteDetails", thrift.STRUCT, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.ExecuteDetails.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ExecuteCodeRequest) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *ExecuteCodeRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ExecuteCodeRequest(%+v)", *p)

}

func (p *ExecuteCodeRequest) DeepEqual(ano *ExecuteCodeRequest) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SandboxInentifier) {
		return false
	}
	if !p.Field2DeepEqual(ano.ExecuteDetails) {
		return false
	}
	if !p.Field255DeepEqual(ano.Base) {
		return false
	}
	return true
}

func (p *ExecuteCodeRequest) Field1DeepEqual(src *SandboxInentifier) bool {

	if !p.SandboxInentifier.DeepEqual(src) {
		return false
	}
	return true
}
func (p *ExecuteCodeRequest) Field2DeepEqual(src *ExecuteDetail) bool {

	if !p.ExecuteDetails.DeepEqual(src) {
		return false
	}
	return true
}
func (p *ExecuteCodeRequest) Field255DeepEqual(src *base.Base) bool {

	if !p.Base.DeepEqual(src) {
		return false
	}
	return true
}

type ExecuteCommandResult_ struct {
	Status        string  `thrift:"Status,1" frugal:"1,default,string" json:"Status"`
	ExecutionTime float64 `thrift:"ExecutionTime,2" frugal:"2,default,double" json:"ExecutionTime"`
	ReturnCode    int32   `thrift:"ReturnCode,3" frugal:"3,default,i32" json:"ReturnCode"`
	Type          string  `thrift:"Type,4" frugal:"4,default,string" json:"Type"`
	Content       string  `thrift:"Content,5" frugal:"5,default,string" json:"Content"`
}

func NewExecuteCommandResult_() *ExecuteCommandResult_ {
	return &ExecuteCommandResult_{}
}

func (p *ExecuteCommandResult_) InitDefault() {
}

func (p *ExecuteCommandResult_) GetStatus() (v string) {
	return p.Status
}

func (p *ExecuteCommandResult_) GetExecutionTime() (v float64) {
	return p.ExecutionTime
}

func (p *ExecuteCommandResult_) GetReturnCode() (v int32) {
	return p.ReturnCode
}

func (p *ExecuteCommandResult_) GetType() (v string) {
	return p.Type
}

func (p *ExecuteCommandResult_) GetContent() (v string) {
	return p.Content
}
func (p *ExecuteCommandResult_) SetStatus(val string) {
	p.Status = val
}
func (p *ExecuteCommandResult_) SetExecutionTime(val float64) {
	p.ExecutionTime = val
}
func (p *ExecuteCommandResult_) SetReturnCode(val int32) {
	p.ReturnCode = val
}
func (p *ExecuteCommandResult_) SetType(val string) {
	p.Type = val
}
func (p *ExecuteCommandResult_) SetContent(val string) {
	p.Content = val
}

var fieldIDToName_ExecuteCommandResult_ = map[int16]string{
	1: "Status",
	2: "ExecutionTime",
	3: "ReturnCode",
	4: "Type",
	5: "Content",
}

func (p *ExecuteCommandResult_) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.DOUBLE {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ExecuteCommandResult_[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ExecuteCommandResult_) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Status = _field
	return nil
}
func (p *ExecuteCommandResult_) ReadField2(iprot thrift.TProtocol) error {

	var _field float64
	if v, err := iprot.ReadDouble(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ExecutionTime = _field
	return nil
}
func (p *ExecuteCommandResult_) ReadField3(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ReturnCode = _field
	return nil
}
func (p *ExecuteCommandResult_) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Type = _field
	return nil
}
func (p *ExecuteCommandResult_) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Content = _field
	return nil
}

func (p *ExecuteCommandResult_) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ExecuteCommandResult"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ExecuteCommandResult_) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Status", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Status); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ExecuteCommandResult_) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ExecutionTime", thrift.DOUBLE, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteDouble(p.ExecutionTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ExecuteCommandResult_) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ReturnCode", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.ReturnCode); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *ExecuteCommandResult_) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Type", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Type); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *ExecuteCommandResult_) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Content", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Content); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *ExecuteCommandResult_) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ExecuteCommandResult_(%+v)", *p)

}

func (p *ExecuteCommandResult_) DeepEqual(ano *ExecuteCommandResult_) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Status) {
		return false
	}
	if !p.Field2DeepEqual(ano.ExecutionTime) {
		return false
	}
	if !p.Field3DeepEqual(ano.ReturnCode) {
		return false
	}
	if !p.Field4DeepEqual(ano.Type) {
		return false
	}
	if !p.Field5DeepEqual(ano.Content) {
		return false
	}
	return true
}

func (p *ExecuteCommandResult_) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Status, src) != 0 {
		return false
	}
	return true
}
func (p *ExecuteCommandResult_) Field2DeepEqual(src float64) bool {

	if p.ExecutionTime != src {
		return false
	}
	return true
}
func (p *ExecuteCommandResult_) Field3DeepEqual(src int32) bool {

	if p.ReturnCode != src {
		return false
	}
	return true
}
func (p *ExecuteCommandResult_) Field4DeepEqual(src string) bool {

	if strings.Compare(p.Type, src) != 0 {
		return false
	}
	return true
}
func (p *ExecuteCommandResult_) Field5DeepEqual(src string) bool {

	if strings.Compare(p.Content, src) != 0 {
		return false
	}
	return true
}

type ExecuteCodeResponse struct {
	Status          string                   `thrift:"Status,1" frugal:"1,default,string" json:"Status"`
	Message         string                   `thrift:"Message,2" frugal:"2,default,string" json:"Message"`
	CompileResult_  []*ExecuteCommandResult_ `thrift:"CompileResult,3,optional" frugal:"3,optional,list<ExecuteCommandResult_>" json:"CompileResult,omitempty"`
	RunResult_      []*ExecuteCommandResult_ `thrift:"RunResult,4,optional" frugal:"4,optional,list<ExecuteCommandResult_>" json:"RunResult,omitempty"`
	ExecutorPodName string                   `thrift:"ExecutorPodName,5" frugal:"5,default,string" json:"ExecutorPodName"`
	Files           map[string]string        `thrift:"Files,6" frugal:"6,default,map<string:string>" json:"Files"`
	ErrorCode       *string                  `thrift:"ErrorCode,7,optional" frugal:"7,optional,string" json:"ErrorCode,omitempty"`
	ErrorMessage    *string                  `thrift:"ErrorMessage,8,optional" frugal:"8,optional,string" json:"ErrorMessage,omitempty"`
	RequestID       *string                  `thrift:"RequestID,9,optional" frugal:"9,optional,string" json:"RequestID,omitempty"`
	BaseResp        *base.BaseResp           `thrift:"BaseResp,255,optional" frugal:"255,optional,base.BaseResp" json:"BaseResp,omitempty"`
}

func NewExecuteCodeResponse() *ExecuteCodeResponse {
	return &ExecuteCodeResponse{}
}

func (p *ExecuteCodeResponse) InitDefault() {
}

func (p *ExecuteCodeResponse) GetStatus() (v string) {
	return p.Status
}

func (p *ExecuteCodeResponse) GetMessage() (v string) {
	return p.Message
}

var ExecuteCodeResponse_CompileResult__DEFAULT []*ExecuteCommandResult_

func (p *ExecuteCodeResponse) GetCompileResult_() (v []*ExecuteCommandResult_) {
	if !p.IsSetCompileResult_() {
		return ExecuteCodeResponse_CompileResult__DEFAULT
	}
	return p.CompileResult_
}

var ExecuteCodeResponse_RunResult__DEFAULT []*ExecuteCommandResult_

func (p *ExecuteCodeResponse) GetRunResult_() (v []*ExecuteCommandResult_) {
	if !p.IsSetRunResult_() {
		return ExecuteCodeResponse_RunResult__DEFAULT
	}
	return p.RunResult_
}

func (p *ExecuteCodeResponse) GetExecutorPodName() (v string) {
	return p.ExecutorPodName
}

func (p *ExecuteCodeResponse) GetFiles() (v map[string]string) {
	return p.Files
}

var ExecuteCodeResponse_ErrorCode_DEFAULT string

func (p *ExecuteCodeResponse) GetErrorCode() (v string) {
	if !p.IsSetErrorCode() {
		return ExecuteCodeResponse_ErrorCode_DEFAULT
	}
	return *p.ErrorCode
}

var ExecuteCodeResponse_ErrorMessage_DEFAULT string

func (p *ExecuteCodeResponse) GetErrorMessage() (v string) {
	if !p.IsSetErrorMessage() {
		return ExecuteCodeResponse_ErrorMessage_DEFAULT
	}
	return *p.ErrorMessage
}

var ExecuteCodeResponse_RequestID_DEFAULT string

func (p *ExecuteCodeResponse) GetRequestID() (v string) {
	if !p.IsSetRequestID() {
		return ExecuteCodeResponse_RequestID_DEFAULT
	}
	return *p.RequestID
}

var ExecuteCodeResponse_BaseResp_DEFAULT *base.BaseResp

func (p *ExecuteCodeResponse) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return ExecuteCodeResponse_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *ExecuteCodeResponse) SetStatus(val string) {
	p.Status = val
}
func (p *ExecuteCodeResponse) SetMessage(val string) {
	p.Message = val
}
func (p *ExecuteCodeResponse) SetCompileResult_(val []*ExecuteCommandResult_) {
	p.CompileResult_ = val
}
func (p *ExecuteCodeResponse) SetRunResult_(val []*ExecuteCommandResult_) {
	p.RunResult_ = val
}
func (p *ExecuteCodeResponse) SetExecutorPodName(val string) {
	p.ExecutorPodName = val
}
func (p *ExecuteCodeResponse) SetFiles(val map[string]string) {
	p.Files = val
}
func (p *ExecuteCodeResponse) SetErrorCode(val *string) {
	p.ErrorCode = val
}
func (p *ExecuteCodeResponse) SetErrorMessage(val *string) {
	p.ErrorMessage = val
}
func (p *ExecuteCodeResponse) SetRequestID(val *string) {
	p.RequestID = val
}
func (p *ExecuteCodeResponse) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_ExecuteCodeResponse = map[int16]string{
	1:   "Status",
	2:   "Message",
	3:   "CompileResult",
	4:   "RunResult",
	5:   "ExecutorPodName",
	6:   "Files",
	7:   "ErrorCode",
	8:   "ErrorMessage",
	9:   "RequestID",
	255: "BaseResp",
}

func (p *ExecuteCodeResponse) IsSetCompileResult_() bool {
	return p.CompileResult_ != nil
}

func (p *ExecuteCodeResponse) IsSetRunResult_() bool {
	return p.RunResult_ != nil
}

func (p *ExecuteCodeResponse) IsSetErrorCode() bool {
	return p.ErrorCode != nil
}

func (p *ExecuteCodeResponse) IsSetErrorMessage() bool {
	return p.ErrorMessage != nil
}

func (p *ExecuteCodeResponse) IsSetRequestID() bool {
	return p.RequestID != nil
}

func (p *ExecuteCodeResponse) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *ExecuteCodeResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ExecuteCodeResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ExecuteCodeResponse) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Status = _field
	return nil
}
func (p *ExecuteCodeResponse) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Message = _field
	return nil
}
func (p *ExecuteCodeResponse) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*ExecuteCommandResult_, 0, size)
	values := make([]ExecuteCommandResult_, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.CompileResult_ = _field
	return nil
}
func (p *ExecuteCodeResponse) ReadField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*ExecuteCommandResult_, 0, size)
	values := make([]ExecuteCommandResult_, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.RunResult_ = _field
	return nil
}
func (p *ExecuteCodeResponse) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ExecutorPodName = _field
	return nil
}
func (p *ExecuteCodeResponse) ReadField6(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_key = v
		}

		var _val string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_val = v
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.Files = _field
	return nil
}
func (p *ExecuteCodeResponse) ReadField7(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ErrorCode = _field
	return nil
}
func (p *ExecuteCodeResponse) ReadField8(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ErrorMessage = _field
	return nil
}
func (p *ExecuteCodeResponse) ReadField9(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.RequestID = _field
	return nil
}
func (p *ExecuteCodeResponse) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *ExecuteCodeResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ExecuteCodeResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ExecuteCodeResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Status", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Status); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ExecuteCodeResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Message", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Message); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ExecuteCodeResponse) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetCompileResult_() {
		if err = oprot.WriteFieldBegin("CompileResult", thrift.LIST, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.CompileResult_)); err != nil {
			return err
		}
		for _, v := range p.CompileResult_ {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *ExecuteCodeResponse) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetRunResult_() {
		if err = oprot.WriteFieldBegin("RunResult", thrift.LIST, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.RunResult_)); err != nil {
			return err
		}
		for _, v := range p.RunResult_ {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *ExecuteCodeResponse) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ExecutorPodName", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ExecutorPodName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *ExecuteCodeResponse) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Files", thrift.MAP, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.Files)); err != nil {
		return err
	}
	for k, v := range p.Files {
		if err := oprot.WriteString(k); err != nil {
			return err
		}
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteMapEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *ExecuteCodeResponse) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetErrorCode() {
		if err = oprot.WriteFieldBegin("ErrorCode", thrift.STRING, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ErrorCode); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}
func (p *ExecuteCodeResponse) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetErrorMessage() {
		if err = oprot.WriteFieldBegin("ErrorMessage", thrift.STRING, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ErrorMessage); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}
func (p *ExecuteCodeResponse) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetRequestID() {
		if err = oprot.WriteFieldBegin("RequestID", thrift.STRING, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.RequestID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}
func (p *ExecuteCodeResponse) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBaseResp() {
		if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.BaseResp.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *ExecuteCodeResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ExecuteCodeResponse(%+v)", *p)

}

func (p *ExecuteCodeResponse) DeepEqual(ano *ExecuteCodeResponse) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Status) {
		return false
	}
	if !p.Field2DeepEqual(ano.Message) {
		return false
	}
	if !p.Field3DeepEqual(ano.CompileResult_) {
		return false
	}
	if !p.Field4DeepEqual(ano.RunResult_) {
		return false
	}
	if !p.Field5DeepEqual(ano.ExecutorPodName) {
		return false
	}
	if !p.Field6DeepEqual(ano.Files) {
		return false
	}
	if !p.Field7DeepEqual(ano.ErrorCode) {
		return false
	}
	if !p.Field8DeepEqual(ano.ErrorMessage) {
		return false
	}
	if !p.Field9DeepEqual(ano.RequestID) {
		return false
	}
	if !p.Field255DeepEqual(ano.BaseResp) {
		return false
	}
	return true
}

func (p *ExecuteCodeResponse) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Status, src) != 0 {
		return false
	}
	return true
}
func (p *ExecuteCodeResponse) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Message, src) != 0 {
		return false
	}
	return true
}
func (p *ExecuteCodeResponse) Field3DeepEqual(src []*ExecuteCommandResult_) bool {

	if len(p.CompileResult_) != len(src) {
		return false
	}
	for i, v := range p.CompileResult_ {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *ExecuteCodeResponse) Field4DeepEqual(src []*ExecuteCommandResult_) bool {

	if len(p.RunResult_) != len(src) {
		return false
	}
	for i, v := range p.RunResult_ {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *ExecuteCodeResponse) Field5DeepEqual(src string) bool {

	if strings.Compare(p.ExecutorPodName, src) != 0 {
		return false
	}
	return true
}
func (p *ExecuteCodeResponse) Field6DeepEqual(src map[string]string) bool {

	if len(p.Files) != len(src) {
		return false
	}
	for k, v := range p.Files {
		_src := src[k]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *ExecuteCodeResponse) Field7DeepEqual(src *string) bool {

	if p.ErrorCode == src {
		return true
	} else if p.ErrorCode == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ErrorCode, *src) != 0 {
		return false
	}
	return true
}
func (p *ExecuteCodeResponse) Field8DeepEqual(src *string) bool {

	if p.ErrorMessage == src {
		return true
	} else if p.ErrorMessage == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ErrorMessage, *src) != 0 {
		return false
	}
	return true
}
func (p *ExecuteCodeResponse) Field9DeepEqual(src *string) bool {

	if p.RequestID == src {
		return true
	} else if p.RequestID == nil || src == nil {
		return false
	}
	if strings.Compare(*p.RequestID, *src) != 0 {
		return false
	}
	return true
}
func (p *ExecuteCodeResponse) Field255DeepEqual(src *base.BaseResp) bool {

	if !p.BaseResp.DeepEqual(src) {
		return false
	}
	return true
}

type UploadFileRequest struct {
	SandboxInentifier *SandboxInentifier `thrift:"SandboxInentifier,1,required" frugal:"1,required,SandboxInentifier" json:"SandboxInentifier"`
	FilePath          string             `thrift:"FilePath,2" frugal:"2,default,string" json:"FilePath"`
	FileContent       []byte             `thrift:"FileContent,3" frugal:"3,default,binary" json:"FileContent"`
	WorkDir           string             `thrift:"WorkDir,4" frugal:"4,default,string" json:"WorkDir"`
	Base              *base.Base         `thrift:"Base,255,optional" frugal:"255,optional,base.Base" json:"Base,omitempty"`
}

func NewUploadFileRequest() *UploadFileRequest {
	return &UploadFileRequest{}
}

func (p *UploadFileRequest) InitDefault() {
}

var UploadFileRequest_SandboxInentifier_DEFAULT *SandboxInentifier

func (p *UploadFileRequest) GetSandboxInentifier() (v *SandboxInentifier) {
	if !p.IsSetSandboxInentifier() {
		return UploadFileRequest_SandboxInentifier_DEFAULT
	}
	return p.SandboxInentifier
}

func (p *UploadFileRequest) GetFilePath() (v string) {
	return p.FilePath
}

func (p *UploadFileRequest) GetFileContent() (v []byte) {
	return p.FileContent
}

func (p *UploadFileRequest) GetWorkDir() (v string) {
	return p.WorkDir
}

var UploadFileRequest_Base_DEFAULT *base.Base

func (p *UploadFileRequest) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return UploadFileRequest_Base_DEFAULT
	}
	return p.Base
}
func (p *UploadFileRequest) SetSandboxInentifier(val *SandboxInentifier) {
	p.SandboxInentifier = val
}
func (p *UploadFileRequest) SetFilePath(val string) {
	p.FilePath = val
}
func (p *UploadFileRequest) SetFileContent(val []byte) {
	p.FileContent = val
}
func (p *UploadFileRequest) SetWorkDir(val string) {
	p.WorkDir = val
}
func (p *UploadFileRequest) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_UploadFileRequest = map[int16]string{
	1:   "SandboxInentifier",
	2:   "FilePath",
	3:   "FileContent",
	4:   "WorkDir",
	255: "Base",
}

func (p *UploadFileRequest) IsSetSandboxInentifier() bool {
	return p.SandboxInentifier != nil
}

func (p *UploadFileRequest) IsSetBase() bool {
	return p.Base != nil
}

func (p *UploadFileRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSandboxInentifier bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSandboxInentifier = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSandboxInentifier {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UploadFileRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_UploadFileRequest[fieldId]))
}

func (p *UploadFileRequest) ReadField1(iprot thrift.TProtocol) error {
	_field := NewSandboxInentifier()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.SandboxInentifier = _field
	return nil
}
func (p *UploadFileRequest) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.FilePath = _field
	return nil
}
func (p *UploadFileRequest) ReadField3(iprot thrift.TProtocol) error {

	var _field []byte
	if v, err := iprot.ReadBinary(); err != nil {
		return err
	} else {
		_field = []byte(v)
	}
	p.FileContent = _field
	return nil
}
func (p *UploadFileRequest) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.WorkDir = _field
	return nil
}
func (p *UploadFileRequest) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *UploadFileRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("UploadFileRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *UploadFileRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SandboxInentifier", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.SandboxInentifier.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *UploadFileRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("FilePath", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.FilePath); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *UploadFileRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("FileContent", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBinary([]byte(p.FileContent)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *UploadFileRequest) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("WorkDir", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.WorkDir); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *UploadFileRequest) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *UploadFileRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UploadFileRequest(%+v)", *p)

}

func (p *UploadFileRequest) DeepEqual(ano *UploadFileRequest) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SandboxInentifier) {
		return false
	}
	if !p.Field2DeepEqual(ano.FilePath) {
		return false
	}
	if !p.Field3DeepEqual(ano.FileContent) {
		return false
	}
	if !p.Field4DeepEqual(ano.WorkDir) {
		return false
	}
	if !p.Field255DeepEqual(ano.Base) {
		return false
	}
	return true
}

func (p *UploadFileRequest) Field1DeepEqual(src *SandboxInentifier) bool {

	if !p.SandboxInentifier.DeepEqual(src) {
		return false
	}
	return true
}
func (p *UploadFileRequest) Field2DeepEqual(src string) bool {

	if strings.Compare(p.FilePath, src) != 0 {
		return false
	}
	return true
}
func (p *UploadFileRequest) Field3DeepEqual(src []byte) bool {

	if bytes.Compare(p.FileContent, src) != 0 {
		return false
	}
	return true
}
func (p *UploadFileRequest) Field4DeepEqual(src string) bool {

	if strings.Compare(p.WorkDir, src) != 0 {
		return false
	}
	return true
}
func (p *UploadFileRequest) Field255DeepEqual(src *base.Base) bool {

	if !p.Base.DeepEqual(src) {
		return false
	}
	return true
}

type UploadFileResponse struct {
	Path     string         `thrift:"Path,1" frugal:"1,default,string" json:"Path"`
	BaseResp *base.BaseResp `thrift:"BaseResp,255,optional" frugal:"255,optional,base.BaseResp" json:"BaseResp,omitempty"`
}

func NewUploadFileResponse() *UploadFileResponse {
	return &UploadFileResponse{}
}

func (p *UploadFileResponse) InitDefault() {
}

func (p *UploadFileResponse) GetPath() (v string) {
	return p.Path
}

var UploadFileResponse_BaseResp_DEFAULT *base.BaseResp

func (p *UploadFileResponse) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return UploadFileResponse_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *UploadFileResponse) SetPath(val string) {
	p.Path = val
}
func (p *UploadFileResponse) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_UploadFileResponse = map[int16]string{
	1:   "Path",
	255: "BaseResp",
}

func (p *UploadFileResponse) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *UploadFileResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UploadFileResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *UploadFileResponse) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Path = _field
	return nil
}
func (p *UploadFileResponse) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *UploadFileResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("UploadFileResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *UploadFileResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Path", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Path); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *UploadFileResponse) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBaseResp() {
		if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.BaseResp.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *UploadFileResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UploadFileResponse(%+v)", *p)

}

func (p *UploadFileResponse) DeepEqual(ano *UploadFileResponse) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Path) {
		return false
	}
	if !p.Field255DeepEqual(ano.BaseResp) {
		return false
	}
	return true
}

func (p *UploadFileResponse) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Path, src) != 0 {
		return false
	}
	return true
}
func (p *UploadFileResponse) Field255DeepEqual(src *base.BaseResp) bool {

	if !p.BaseResp.DeepEqual(src) {
		return false
	}
	return true
}

type DownloadFileRequest struct {
	SandboxInentifier *SandboxInentifier `thrift:"SandboxInentifier,1,required" frugal:"1,required,SandboxInentifier" json:"SandboxInentifier"`
	FilePath          string             `thrift:"FilePath,2" frugal:"2,default,string" json:"FilePath"`
	WorkDir           string             `thrift:"WorkDir,3" frugal:"3,default,string" json:"WorkDir"`
	Base              *base.Base         `thrift:"Base,255,optional" frugal:"255,optional,base.Base" json:"Base,omitempty"`
}

func NewDownloadFileRequest() *DownloadFileRequest {
	return &DownloadFileRequest{}
}

func (p *DownloadFileRequest) InitDefault() {
}

var DownloadFileRequest_SandboxInentifier_DEFAULT *SandboxInentifier

func (p *DownloadFileRequest) GetSandboxInentifier() (v *SandboxInentifier) {
	if !p.IsSetSandboxInentifier() {
		return DownloadFileRequest_SandboxInentifier_DEFAULT
	}
	return p.SandboxInentifier
}

func (p *DownloadFileRequest) GetFilePath() (v string) {
	return p.FilePath
}

func (p *DownloadFileRequest) GetWorkDir() (v string) {
	return p.WorkDir
}

var DownloadFileRequest_Base_DEFAULT *base.Base

func (p *DownloadFileRequest) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return DownloadFileRequest_Base_DEFAULT
	}
	return p.Base
}
func (p *DownloadFileRequest) SetSandboxInentifier(val *SandboxInentifier) {
	p.SandboxInentifier = val
}
func (p *DownloadFileRequest) SetFilePath(val string) {
	p.FilePath = val
}
func (p *DownloadFileRequest) SetWorkDir(val string) {
	p.WorkDir = val
}
func (p *DownloadFileRequest) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_DownloadFileRequest = map[int16]string{
	1:   "SandboxInentifier",
	2:   "FilePath",
	3:   "WorkDir",
	255: "Base",
}

func (p *DownloadFileRequest) IsSetSandboxInentifier() bool {
	return p.SandboxInentifier != nil
}

func (p *DownloadFileRequest) IsSetBase() bool {
	return p.Base != nil
}

func (p *DownloadFileRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSandboxInentifier bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSandboxInentifier = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSandboxInentifier {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DownloadFileRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DownloadFileRequest[fieldId]))
}

func (p *DownloadFileRequest) ReadField1(iprot thrift.TProtocol) error {
	_field := NewSandboxInentifier()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.SandboxInentifier = _field
	return nil
}
func (p *DownloadFileRequest) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.FilePath = _field
	return nil
}
func (p *DownloadFileRequest) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.WorkDir = _field
	return nil
}
func (p *DownloadFileRequest) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *DownloadFileRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("DownloadFileRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DownloadFileRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SandboxInentifier", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.SandboxInentifier.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *DownloadFileRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("FilePath", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.FilePath); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *DownloadFileRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("WorkDir", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.WorkDir); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *DownloadFileRequest) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *DownloadFileRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DownloadFileRequest(%+v)", *p)

}

func (p *DownloadFileRequest) DeepEqual(ano *DownloadFileRequest) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SandboxInentifier) {
		return false
	}
	if !p.Field2DeepEqual(ano.FilePath) {
		return false
	}
	if !p.Field3DeepEqual(ano.WorkDir) {
		return false
	}
	if !p.Field255DeepEqual(ano.Base) {
		return false
	}
	return true
}

func (p *DownloadFileRequest) Field1DeepEqual(src *SandboxInentifier) bool {

	if !p.SandboxInentifier.DeepEqual(src) {
		return false
	}
	return true
}
func (p *DownloadFileRequest) Field2DeepEqual(src string) bool {

	if strings.Compare(p.FilePath, src) != 0 {
		return false
	}
	return true
}
func (p *DownloadFileRequest) Field3DeepEqual(src string) bool {

	if strings.Compare(p.WorkDir, src) != 0 {
		return false
	}
	return true
}
func (p *DownloadFileRequest) Field255DeepEqual(src *base.Base) bool {

	if !p.Base.DeepEqual(src) {
		return false
	}
	return true
}

type DownloadFileResponse struct {
	FilePath    string         `thrift:"FilePath,1" frugal:"1,default,string" json:"FilePath"`
	FileContent []byte         `thrift:"FileContent,2" frugal:"2,default,binary" json:"FileContent"`
	BaseResp    *base.BaseResp `thrift:"BaseResp,255,optional" frugal:"255,optional,base.BaseResp" json:"BaseResp,omitempty"`
}

func NewDownloadFileResponse() *DownloadFileResponse {
	return &DownloadFileResponse{}
}

func (p *DownloadFileResponse) InitDefault() {
}

func (p *DownloadFileResponse) GetFilePath() (v string) {
	return p.FilePath
}

func (p *DownloadFileResponse) GetFileContent() (v []byte) {
	return p.FileContent
}

var DownloadFileResponse_BaseResp_DEFAULT *base.BaseResp

func (p *DownloadFileResponse) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return DownloadFileResponse_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *DownloadFileResponse) SetFilePath(val string) {
	p.FilePath = val
}
func (p *DownloadFileResponse) SetFileContent(val []byte) {
	p.FileContent = val
}
func (p *DownloadFileResponse) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_DownloadFileResponse = map[int16]string{
	1:   "FilePath",
	2:   "FileContent",
	255: "BaseResp",
}

func (p *DownloadFileResponse) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *DownloadFileResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DownloadFileResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DownloadFileResponse) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.FilePath = _field
	return nil
}
func (p *DownloadFileResponse) ReadField2(iprot thrift.TProtocol) error {

	var _field []byte
	if v, err := iprot.ReadBinary(); err != nil {
		return err
	} else {
		_field = []byte(v)
	}
	p.FileContent = _field
	return nil
}
func (p *DownloadFileResponse) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *DownloadFileResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("DownloadFileResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DownloadFileResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("FilePath", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.FilePath); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *DownloadFileResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("FileContent", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBinary([]byte(p.FileContent)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *DownloadFileResponse) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBaseResp() {
		if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.BaseResp.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *DownloadFileResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DownloadFileResponse(%+v)", *p)

}

func (p *DownloadFileResponse) DeepEqual(ano *DownloadFileResponse) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.FilePath) {
		return false
	}
	if !p.Field2DeepEqual(ano.FileContent) {
		return false
	}
	if !p.Field255DeepEqual(ano.BaseResp) {
		return false
	}
	return true
}

func (p *DownloadFileResponse) Field1DeepEqual(src string) bool {

	if strings.Compare(p.FilePath, src) != 0 {
		return false
	}
	return true
}
func (p *DownloadFileResponse) Field2DeepEqual(src []byte) bool {

	if bytes.Compare(p.FileContent, src) != 0 {
		return false
	}
	return true
}
func (p *DownloadFileResponse) Field255DeepEqual(src *base.BaseResp) bool {

	if !p.BaseResp.DeepEqual(src) {
		return false
	}
	return true
}
