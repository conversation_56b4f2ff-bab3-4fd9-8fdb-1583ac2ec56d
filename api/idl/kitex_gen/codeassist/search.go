// Code generated by thriftgo (0.4.2). DO NOT EDIT.

package codeassist

import (
	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/base"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type SearchImagesRequest struct {
	Queries       []string   `thrift:"Queries,1,required" frugal:"1,required,list<string>" json:"Queries"`
	ArtifactID    int64      `thrift:"ArtifactID,2,required" frugal:"2,required,i64" json:"ArtifactID"`
	Limit         *int64     `thrift:"Limit,4,optional" frugal:"4,optional,i64" json:"Limit,omitempty"`
	SearchContext *string    `thrift:"SearchContext,5,optional" frugal:"5,optional,string" json:"SearchContext,omitempty"`
	Base          *base.Base `thrift:"Base,255,optional" frugal:"255,optional,base.Base" json:"Base,omitempty"`
}

func NewSearchImagesRequest() *SearchImagesRequest {
	return &SearchImagesRequest{}
}

func (p *SearchImagesRequest) InitDefault() {
}

func (p *SearchImagesRequest) GetQueries() (v []string) {
	return p.Queries
}

func (p *SearchImagesRequest) GetArtifactID() (v int64) {
	return p.ArtifactID
}

var SearchImagesRequest_Limit_DEFAULT int64

func (p *SearchImagesRequest) GetLimit() (v int64) {
	if !p.IsSetLimit() {
		return SearchImagesRequest_Limit_DEFAULT
	}
	return *p.Limit
}

var SearchImagesRequest_SearchContext_DEFAULT string

func (p *SearchImagesRequest) GetSearchContext() (v string) {
	if !p.IsSetSearchContext() {
		return SearchImagesRequest_SearchContext_DEFAULT
	}
	return *p.SearchContext
}

var SearchImagesRequest_Base_DEFAULT *base.Base

func (p *SearchImagesRequest) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return SearchImagesRequest_Base_DEFAULT
	}
	return p.Base
}
func (p *SearchImagesRequest) SetQueries(val []string) {
	p.Queries = val
}
func (p *SearchImagesRequest) SetArtifactID(val int64) {
	p.ArtifactID = val
}
func (p *SearchImagesRequest) SetLimit(val *int64) {
	p.Limit = val
}
func (p *SearchImagesRequest) SetSearchContext(val *string) {
	p.SearchContext = val
}
func (p *SearchImagesRequest) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_SearchImagesRequest = map[int16]string{
	1:   "Queries",
	2:   "ArtifactID",
	4:   "Limit",
	5:   "SearchContext",
	255: "Base",
}

func (p *SearchImagesRequest) IsSetLimit() bool {
	return p.Limit != nil
}

func (p *SearchImagesRequest) IsSetSearchContext() bool {
	return p.SearchContext != nil
}

func (p *SearchImagesRequest) IsSetBase() bool {
	return p.Base != nil
}

func (p *SearchImagesRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetQueries bool = false
	var issetArtifactID bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetQueries = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetArtifactID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetQueries {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetArtifactID {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SearchImagesRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_SearchImagesRequest[fieldId]))
}

func (p *SearchImagesRequest) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Queries = _field
	return nil
}
func (p *SearchImagesRequest) ReadField2(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ArtifactID = _field
	return nil
}
func (p *SearchImagesRequest) ReadField4(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Limit = _field
	return nil
}
func (p *SearchImagesRequest) ReadField5(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.SearchContext = _field
	return nil
}
func (p *SearchImagesRequest) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *SearchImagesRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("SearchImagesRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SearchImagesRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Queries", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.Queries)); err != nil {
		return err
	}
	for _, v := range p.Queries {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *SearchImagesRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ArtifactID", thrift.I64, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.ArtifactID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *SearchImagesRequest) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetLimit() {
		if err = oprot.WriteFieldBegin("Limit", thrift.I64, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.Limit); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *SearchImagesRequest) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetSearchContext() {
		if err = oprot.WriteFieldBegin("SearchContext", thrift.STRING, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.SearchContext); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *SearchImagesRequest) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *SearchImagesRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SearchImagesRequest(%+v)", *p)

}

func (p *SearchImagesRequest) DeepEqual(ano *SearchImagesRequest) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Queries) {
		return false
	}
	if !p.Field2DeepEqual(ano.ArtifactID) {
		return false
	}
	if !p.Field4DeepEqual(ano.Limit) {
		return false
	}
	if !p.Field5DeepEqual(ano.SearchContext) {
		return false
	}
	if !p.Field255DeepEqual(ano.Base) {
		return false
	}
	return true
}

func (p *SearchImagesRequest) Field1DeepEqual(src []string) bool {

	if len(p.Queries) != len(src) {
		return false
	}
	for i, v := range p.Queries {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *SearchImagesRequest) Field2DeepEqual(src int64) bool {

	if p.ArtifactID != src {
		return false
	}
	return true
}
func (p *SearchImagesRequest) Field4DeepEqual(src *int64) bool {

	if p.Limit == src {
		return true
	} else if p.Limit == nil || src == nil {
		return false
	}
	if *p.Limit != *src {
		return false
	}
	return true
}
func (p *SearchImagesRequest) Field5DeepEqual(src *string) bool {

	if p.SearchContext == src {
		return true
	} else if p.SearchContext == nil || src == nil {
		return false
	}
	if strings.Compare(*p.SearchContext, *src) != 0 {
		return false
	}
	return true
}
func (p *SearchImagesRequest) Field255DeepEqual(src *base.Base) bool {

	if !p.Base.DeepEqual(src) {
		return false
	}
	return true
}

type SearchImagesResponse struct {
	Data     *ImagesSearchResult_ `thrift:"data,1" frugal:"1,default,ImagesSearchResult_" json:"data"`
	BaseResp *base.BaseResp       `thrift:"BaseResp,255,optional" frugal:"255,optional,base.BaseResp" json:"BaseResp,omitempty"`
}

func NewSearchImagesResponse() *SearchImagesResponse {
	return &SearchImagesResponse{}
}

func (p *SearchImagesResponse) InitDefault() {
}

var SearchImagesResponse_Data_DEFAULT *ImagesSearchResult_

func (p *SearchImagesResponse) GetData() (v *ImagesSearchResult_) {
	if !p.IsSetData() {
		return SearchImagesResponse_Data_DEFAULT
	}
	return p.Data
}

var SearchImagesResponse_BaseResp_DEFAULT *base.BaseResp

func (p *SearchImagesResponse) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return SearchImagesResponse_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *SearchImagesResponse) SetData(val *ImagesSearchResult_) {
	p.Data = val
}
func (p *SearchImagesResponse) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_SearchImagesResponse = map[int16]string{
	1:   "data",
	255: "BaseResp",
}

func (p *SearchImagesResponse) IsSetData() bool {
	return p.Data != nil
}

func (p *SearchImagesResponse) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *SearchImagesResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SearchImagesResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *SearchImagesResponse) ReadField1(iprot thrift.TProtocol) error {
	_field := NewImagesSearchResult_()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Data = _field
	return nil
}
func (p *SearchImagesResponse) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *SearchImagesResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("SearchImagesResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SearchImagesResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("data", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Data.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *SearchImagesResponse) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBaseResp() {
		if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.BaseResp.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *SearchImagesResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SearchImagesResponse(%+v)", *p)

}

func (p *SearchImagesResponse) DeepEqual(ano *SearchImagesResponse) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Data) {
		return false
	}
	if !p.Field255DeepEqual(ano.BaseResp) {
		return false
	}
	return true
}

func (p *SearchImagesResponse) Field1DeepEqual(src *ImagesSearchResult_) bool {

	if !p.Data.DeepEqual(src) {
		return false
	}
	return true
}
func (p *SearchImagesResponse) Field255DeepEqual(src *base.BaseResp) bool {

	if !p.BaseResp.DeepEqual(src) {
		return false
	}
	return true
}

type ImagesSearchResult_ struct {
	Images []*SearchedImage `thrift:"images,1" frugal:"1,default,list<SearchedImage>" json:"images"`
}

func NewImagesSearchResult_() *ImagesSearchResult_ {
	return &ImagesSearchResult_{}
}

func (p *ImagesSearchResult_) InitDefault() {
}

func (p *ImagesSearchResult_) GetImages() (v []*SearchedImage) {
	return p.Images
}
func (p *ImagesSearchResult_) SetImages(val []*SearchedImage) {
	p.Images = val
}

var fieldIDToName_ImagesSearchResult_ = map[int16]string{
	1: "images",
}

func (p *ImagesSearchResult_) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ImagesSearchResult_[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ImagesSearchResult_) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*SearchedImage, 0, size)
	values := make([]SearchedImage, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Images = _field
	return nil
}

func (p *ImagesSearchResult_) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ImagesSearchResult"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ImagesSearchResult_) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("images", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Images)); err != nil {
		return err
	}
	for _, v := range p.Images {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ImagesSearchResult_) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ImagesSearchResult_(%+v)", *p)

}

func (p *ImagesSearchResult_) DeepEqual(ano *ImagesSearchResult_) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Images) {
		return false
	}
	return true
}

func (p *ImagesSearchResult_) Field1DeepEqual(src []*SearchedImage) bool {

	if len(p.Images) != len(src) {
		return false
	}
	for i, v := range p.Images {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type SearchedImage struct {
	URL       string       `thrift:"URL,1" frugal:"1,default,string" json:"URL"`
	Width     int64        `thrift:"Width,2" frugal:"2,default,i64" json:"Width"`
	Height    int64        `thrift:"Height,3" frugal:"3,default,i64" json:"Height"`
	ThumbNail *ImageDetail `thrift:"ThumbNail,4,optional" frugal:"4,optional,ImageDetail" json:"ThumbNail,omitempty"`
}

func NewSearchedImage() *SearchedImage {
	return &SearchedImage{}
}

func (p *SearchedImage) InitDefault() {
}

func (p *SearchedImage) GetURL() (v string) {
	return p.URL
}

func (p *SearchedImage) GetWidth() (v int64) {
	return p.Width
}

func (p *SearchedImage) GetHeight() (v int64) {
	return p.Height
}

var SearchedImage_ThumbNail_DEFAULT *ImageDetail

func (p *SearchedImage) GetThumbNail() (v *ImageDetail) {
	if !p.IsSetThumbNail() {
		return SearchedImage_ThumbNail_DEFAULT
	}
	return p.ThumbNail
}
func (p *SearchedImage) SetURL(val string) {
	p.URL = val
}
func (p *SearchedImage) SetWidth(val int64) {
	p.Width = val
}
func (p *SearchedImage) SetHeight(val int64) {
	p.Height = val
}
func (p *SearchedImage) SetThumbNail(val *ImageDetail) {
	p.ThumbNail = val
}

var fieldIDToName_SearchedImage = map[int16]string{
	1: "URL",
	2: "Width",
	3: "Height",
	4: "ThumbNail",
}

func (p *SearchedImage) IsSetThumbNail() bool {
	return p.ThumbNail != nil
}

func (p *SearchedImage) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SearchedImage[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *SearchedImage) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.URL = _field
	return nil
}
func (p *SearchedImage) ReadField2(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Width = _field
	return nil
}
func (p *SearchedImage) ReadField3(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Height = _field
	return nil
}
func (p *SearchedImage) ReadField4(iprot thrift.TProtocol) error {
	_field := NewImageDetail()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.ThumbNail = _field
	return nil
}

func (p *SearchedImage) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("SearchedImage"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SearchedImage) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("URL", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.URL); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *SearchedImage) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Width", thrift.I64, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.Width); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *SearchedImage) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Height", thrift.I64, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.Height); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *SearchedImage) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetThumbNail() {
		if err = oprot.WriteFieldBegin("ThumbNail", thrift.STRUCT, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.ThumbNail.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *SearchedImage) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SearchedImage(%+v)", *p)

}

func (p *SearchedImage) DeepEqual(ano *SearchedImage) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.URL) {
		return false
	}
	if !p.Field2DeepEqual(ano.Width) {
		return false
	}
	if !p.Field3DeepEqual(ano.Height) {
		return false
	}
	if !p.Field4DeepEqual(ano.ThumbNail) {
		return false
	}
	return true
}

func (p *SearchedImage) Field1DeepEqual(src string) bool {

	if strings.Compare(p.URL, src) != 0 {
		return false
	}
	return true
}
func (p *SearchedImage) Field2DeepEqual(src int64) bool {

	if p.Width != src {
		return false
	}
	return true
}
func (p *SearchedImage) Field3DeepEqual(src int64) bool {

	if p.Height != src {
		return false
	}
	return true
}
func (p *SearchedImage) Field4DeepEqual(src *ImageDetail) bool {

	if !p.ThumbNail.DeepEqual(src) {
		return false
	}
	return true
}

type ImageDetail struct {
	URL    string `thrift:"URL,1" frugal:"1,default,string" json:"URL"`
	Format string `thrift:"Format,4" frugal:"4,default,string" json:"Format"`
	Width  int64  `thrift:"Width,2" frugal:"2,default,i64" json:"Width"`
	Height int64  `thrift:"Height,3" frugal:"3,default,i64" json:"Height"`
}

func NewImageDetail() *ImageDetail {
	return &ImageDetail{}
}

func (p *ImageDetail) InitDefault() {
}

func (p *ImageDetail) GetURL() (v string) {
	return p.URL
}

func (p *ImageDetail) GetFormat() (v string) {
	return p.Format
}

func (p *ImageDetail) GetWidth() (v int64) {
	return p.Width
}

func (p *ImageDetail) GetHeight() (v int64) {
	return p.Height
}
func (p *ImageDetail) SetURL(val string) {
	p.URL = val
}
func (p *ImageDetail) SetFormat(val string) {
	p.Format = val
}
func (p *ImageDetail) SetWidth(val int64) {
	p.Width = val
}
func (p *ImageDetail) SetHeight(val int64) {
	p.Height = val
}

var fieldIDToName_ImageDetail = map[int16]string{
	1: "URL",
	4: "Format",
	2: "Width",
	3: "Height",
}

func (p *ImageDetail) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ImageDetail[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ImageDetail) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.URL = _field
	return nil
}
func (p *ImageDetail) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Format = _field
	return nil
}
func (p *ImageDetail) ReadField2(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Width = _field
	return nil
}
func (p *ImageDetail) ReadField3(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Height = _field
	return nil
}

func (p *ImageDetail) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ImageDetail"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ImageDetail) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("URL", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.URL); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ImageDetail) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Format", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Format); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *ImageDetail) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Width", thrift.I64, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.Width); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ImageDetail) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Height", thrift.I64, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.Height); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ImageDetail) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ImageDetail(%+v)", *p)

}

func (p *ImageDetail) DeepEqual(ano *ImageDetail) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.URL) {
		return false
	}
	if !p.Field4DeepEqual(ano.Format) {
		return false
	}
	if !p.Field2DeepEqual(ano.Width) {
		return false
	}
	if !p.Field3DeepEqual(ano.Height) {
		return false
	}
	return true
}

func (p *ImageDetail) Field1DeepEqual(src string) bool {

	if strings.Compare(p.URL, src) != 0 {
		return false
	}
	return true
}
func (p *ImageDetail) Field4DeepEqual(src string) bool {

	if strings.Compare(p.Format, src) != 0 {
		return false
	}
	return true
}
func (p *ImageDetail) Field2DeepEqual(src int64) bool {

	if p.Width != src {
		return false
	}
	return true
}
func (p *ImageDetail) Field3DeepEqual(src int64) bool {

	if p.Height != src {
		return false
	}
	return true
}
