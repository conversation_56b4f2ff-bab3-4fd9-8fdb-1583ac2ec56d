// Code generated by thriftgo (0.4.2). DO NOT EDIT.

package codeassist

import (
	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/base"
	"database/sql"
	"database/sql/driver"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type CotMode int64

const (
	CotMode_CotModeClose CotMode = 1
	CotMode_CotModeAuto  CotMode = 2
	CotMode_CotModeOpen  CotMode = 3
)

func (p CotMode) String() string {
	switch p {
	case CotMode_CotModeClose:
		return "CotModeClose"
	case CotMode_CotModeAuto:
		return "CotModeAuto"
	case CotMode_CotModeOpen:
		return "CotModeOpen"
	}
	return "<UNSET>"
}

func CotModeFromString(s string) (CotMode, error) {
	switch s {
	case "CotModeClose":
		return CotMode_CotModeClose, nil
	case "CotModeAuto":
		return CotMode_CotModeAuto, nil
	case "CotModeOpen":
		return CotMode_CotModeOpen, nil
	}
	return CotMode(0), fmt.Errorf("not a valid CotMode string")
}

func CotModePtr(v CotMode) *CotMode { return &v }
func (p *CotMode) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = CotMode(result.Int64)
	return
}

func (p *CotMode) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type ConversationType int64

const (
	ConversationType_Unknown ConversationType = 0
	ConversationType_Single  ConversationType = 1
	ConversationType_Group   ConversationType = 2
)

func (p ConversationType) String() string {
	switch p {
	case ConversationType_Unknown:
		return "Unknown"
	case ConversationType_Single:
		return "Single"
	case ConversationType_Group:
		return "Group"
	}
	return "<UNSET>"
}

func ConversationTypeFromString(s string) (ConversationType, error) {
	switch s {
	case "Unknown":
		return ConversationType_Unknown, nil
	case "Single":
		return ConversationType_Single, nil
	case "Group":
		return ConversationType_Group, nil
	}
	return ConversationType(0), fmt.Errorf("not a valid ConversationType string")
}

func ConversationTypePtr(v ConversationType) *ConversationType { return &v }
func (p *ConversationType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = ConversationType(result.Int64)
	return
}

func (p *ConversationType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type ChatMessageRole int64

const (
	ChatMessageRole_SYSTEM    ChatMessageRole = 0
	ChatMessageRole_USER      ChatMessageRole = 1
	ChatMessageRole_ASSISTANT ChatMessageRole = 2
)

func (p ChatMessageRole) String() string {
	switch p {
	case ChatMessageRole_SYSTEM:
		return "SYSTEM"
	case ChatMessageRole_USER:
		return "USER"
	case ChatMessageRole_ASSISTANT:
		return "ASSISTANT"
	}
	return "<UNSET>"
}

func ChatMessageRoleFromString(s string) (ChatMessageRole, error) {
	switch s {
	case "SYSTEM":
		return ChatMessageRole_SYSTEM, nil
	case "USER":
		return ChatMessageRole_USER, nil
	case "ASSISTANT":
		return ChatMessageRole_ASSISTANT, nil
	}
	return ChatMessageRole(0), fmt.Errorf("not a valid ChatMessageRole string")
}

func ChatMessageRolePtr(v ChatMessageRole) *ChatMessageRole { return &v }
func (p *ChatMessageRole) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = ChatMessageRole(result.Int64)
	return
}

func (p *ChatMessageRole) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type ContextBlockType int64

const (
	ContextBlockType_FILE_BLOCK      ContextBlockType = 1
	ContextBlockType_DIRECTORY_BLOCK ContextBlockType = 2
	ContextBlockType_REPO_BLOCK      ContextBlockType = 3
	ContextBlockType_MODELGEN_BLOCK  ContextBlockType = 4
	ContextBlockType_IMAGE_BLOCK     ContextBlockType = 5
)

func (p ContextBlockType) String() string {
	switch p {
	case ContextBlockType_FILE_BLOCK:
		return "FILE_BLOCK"
	case ContextBlockType_DIRECTORY_BLOCK:
		return "DIRECTORY_BLOCK"
	case ContextBlockType_REPO_BLOCK:
		return "REPO_BLOCK"
	case ContextBlockType_MODELGEN_BLOCK:
		return "MODELGEN_BLOCK"
	case ContextBlockType_IMAGE_BLOCK:
		return "IMAGE_BLOCK"
	}
	return "<UNSET>"
}

func ContextBlockTypeFromString(s string) (ContextBlockType, error) {
	switch s {
	case "FILE_BLOCK":
		return ContextBlockType_FILE_BLOCK, nil
	case "DIRECTORY_BLOCK":
		return ContextBlockType_DIRECTORY_BLOCK, nil
	case "REPO_BLOCK":
		return ContextBlockType_REPO_BLOCK, nil
	case "MODELGEN_BLOCK":
		return ContextBlockType_MODELGEN_BLOCK, nil
	case "IMAGE_BLOCK":
		return ContextBlockType_IMAGE_BLOCK, nil
	}
	return ContextBlockType(0), fmt.Errorf("not a valid ContextBlockType string")
}

func ContextBlockTypePtr(v ContextBlockType) *ContextBlockType { return &v }
func (p *ContextBlockType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = ContextBlockType(result.Int64)
	return
}

func (p *ContextBlockType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type ChatEventType int64

const (
	ChatEventType_DEFAULT ChatEventType = 0
	ChatEventType_OUTPUT  ChatEventType = 1
	ChatEventType_SUGGEST ChatEventType = 2
	ChatEventType_Verbose ChatEventType = 100
)

func (p ChatEventType) String() string {
	switch p {
	case ChatEventType_DEFAULT:
		return "DEFAULT"
	case ChatEventType_OUTPUT:
		return "OUTPUT"
	case ChatEventType_SUGGEST:
		return "SUGGEST"
	case ChatEventType_Verbose:
		return "Verbose"
	}
	return "<UNSET>"
}

func ChatEventTypeFromString(s string) (ChatEventType, error) {
	switch s {
	case "DEFAULT":
		return ChatEventType_DEFAULT, nil
	case "OUTPUT":
		return ChatEventType_OUTPUT, nil
	case "SUGGEST":
		return ChatEventType_SUGGEST, nil
	case "Verbose":
		return ChatEventType_Verbose, nil
	}
	return ChatEventType(0), fmt.Errorf("not a valid ChatEventType string")
}

func ChatEventTypePtr(v ChatEventType) *ChatEventType { return &v }
func (p *ChatEventType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = ChatEventType(result.Int64)
	return
}

func (p *ChatEventType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type ChatContentType int64

const (
	ChatContentType_DEFAULT                      ChatContentType = 0
	ChatContentType_TXT                          ChatContentType = 1
	ChatContentType_Image                        ChatContentType = 2
	ChatContentType_Audio                        ChatContentType = 3
	ChatContentType_Video                        ChatContentType = 4
	ChatContentType_Link                         ChatContentType = 6
	ChatContentType_Music                        ChatContentType = 7
	ChatContentType_Tako                         ChatContentType = 8
	ChatContentType_File                         ChatContentType = 9
	ChatContentType_Card                         ChatContentType = 50
	ChatContentType_BotCard                      ChatContentType = 51
	ChatContentType_Widget                       ChatContentType = 52
	ChatContentType_APP                          ChatContentType = 100
	ChatContentType_OutputSearchResult           ChatContentType = 200
	ChatContentType_OutputMultiStream            ChatContentType = 201
	ChatContentType_SearchIntentionResult        ChatContentType = 300
	ChatContentType_BlockText                    ChatContentType = 10000
	ChatContentType_BlockResearchProcessCard     ChatContentType = 10001
	ChatContentType_BlockBanner                  ChatContentType = 10002
	ChatContentType_BlockHeaderText              ChatContentType = 10003
	ChatContentType_BlockSearchQuery             ChatContentType = 10004
	ChatContentType_BlockSearchResult            ChatContentType = 10005
	ChatContentType_BlockLinkerReader            ChatContentType = 10006
	ChatContentType_BlockVideoReader             ChatContentType = 10007
	ChatContentType_BlockCode                    ChatContentType = 10008
	ChatContentType_BlockLocalLife               ChatContentType = 10009
	ChatContentType_BlockGenImage                ChatContentType = 10010
	ChatContentType_BlockResearch                ChatContentType = 10011
	ChatContentType_BlockImage                   ChatContentType = 10012
	ChatContentType_BlockResearchWebpage         ChatContentType = 10013
	ChatContentType_BlockResearchStatus          ChatContentType = 10014
	ChatContentType_BlockSupertask               ChatContentType = 10015
	ChatContentType_BlockSupertaskTool           ChatContentType = 10016
	ChatContentType_BlockSupertaskProductionFile ChatContentType = 10017
	ChatContentType_BlockCreationLoading         ChatContentType = 10018
	ChatContentType_BlockFileOperation           ChatContentType = 10019
	ChatContentType_BlockFile                    ChatContentType = 10020
	ChatContentType_BlockArtifactCodeFile        ChatContentType = 10021
	ChatContentType_BlockCodeAssistProcess       ChatContentType = 10022
	ChatContentType_BlockArtifact                ChatContentType = 10030
)

func (p ChatContentType) String() string {
	switch p {
	case ChatContentType_DEFAULT:
		return "DEFAULT"
	case ChatContentType_TXT:
		return "TXT"
	case ChatContentType_Image:
		return "Image"
	case ChatContentType_Audio:
		return "Audio"
	case ChatContentType_Video:
		return "Video"
	case ChatContentType_Link:
		return "Link"
	case ChatContentType_Music:
		return "Music"
	case ChatContentType_Tako:
		return "Tako"
	case ChatContentType_File:
		return "File"
	case ChatContentType_Card:
		return "Card"
	case ChatContentType_BotCard:
		return "BotCard"
	case ChatContentType_Widget:
		return "Widget"
	case ChatContentType_APP:
		return "APP"
	case ChatContentType_OutputSearchResult:
		return "OutputSearchResult"
	case ChatContentType_OutputMultiStream:
		return "OutputMultiStream"
	case ChatContentType_SearchIntentionResult:
		return "SearchIntentionResult"
	case ChatContentType_BlockText:
		return "BlockText"
	case ChatContentType_BlockResearchProcessCard:
		return "BlockResearchProcessCard"
	case ChatContentType_BlockBanner:
		return "BlockBanner"
	case ChatContentType_BlockHeaderText:
		return "BlockHeaderText"
	case ChatContentType_BlockSearchQuery:
		return "BlockSearchQuery"
	case ChatContentType_BlockSearchResult:
		return "BlockSearchResult"
	case ChatContentType_BlockLinkerReader:
		return "BlockLinkerReader"
	case ChatContentType_BlockVideoReader:
		return "BlockVideoReader"
	case ChatContentType_BlockCode:
		return "BlockCode"
	case ChatContentType_BlockLocalLife:
		return "BlockLocalLife"
	case ChatContentType_BlockGenImage:
		return "BlockGenImage"
	case ChatContentType_BlockResearch:
		return "BlockResearch"
	case ChatContentType_BlockImage:
		return "BlockImage"
	case ChatContentType_BlockResearchWebpage:
		return "BlockResearchWebpage"
	case ChatContentType_BlockResearchStatus:
		return "BlockResearchStatus"
	case ChatContentType_BlockSupertask:
		return "BlockSupertask"
	case ChatContentType_BlockSupertaskTool:
		return "BlockSupertaskTool"
	case ChatContentType_BlockSupertaskProductionFile:
		return "BlockSupertaskProductionFile"
	case ChatContentType_BlockCreationLoading:
		return "BlockCreationLoading"
	case ChatContentType_BlockFileOperation:
		return "BlockFileOperation"
	case ChatContentType_BlockFile:
		return "BlockFile"
	case ChatContentType_BlockArtifactCodeFile:
		return "BlockArtifactCodeFile"
	case ChatContentType_BlockCodeAssistProcess:
		return "BlockCodeAssistProcess"
	case ChatContentType_BlockArtifact:
		return "BlockArtifact"
	}
	return "<UNSET>"
}

func ChatContentTypeFromString(s string) (ChatContentType, error) {
	switch s {
	case "DEFAULT":
		return ChatContentType_DEFAULT, nil
	case "TXT":
		return ChatContentType_TXT, nil
	case "Image":
		return ChatContentType_Image, nil
	case "Audio":
		return ChatContentType_Audio, nil
	case "Video":
		return ChatContentType_Video, nil
	case "Link":
		return ChatContentType_Link, nil
	case "Music":
		return ChatContentType_Music, nil
	case "Tako":
		return ChatContentType_Tako, nil
	case "File":
		return ChatContentType_File, nil
	case "Card":
		return ChatContentType_Card, nil
	case "BotCard":
		return ChatContentType_BotCard, nil
	case "Widget":
		return ChatContentType_Widget, nil
	case "APP":
		return ChatContentType_APP, nil
	case "OutputSearchResult":
		return ChatContentType_OutputSearchResult, nil
	case "OutputMultiStream":
		return ChatContentType_OutputMultiStream, nil
	case "SearchIntentionResult":
		return ChatContentType_SearchIntentionResult, nil
	case "BlockText":
		return ChatContentType_BlockText, nil
	case "BlockResearchProcessCard":
		return ChatContentType_BlockResearchProcessCard, nil
	case "BlockBanner":
		return ChatContentType_BlockBanner, nil
	case "BlockHeaderText":
		return ChatContentType_BlockHeaderText, nil
	case "BlockSearchQuery":
		return ChatContentType_BlockSearchQuery, nil
	case "BlockSearchResult":
		return ChatContentType_BlockSearchResult, nil
	case "BlockLinkerReader":
		return ChatContentType_BlockLinkerReader, nil
	case "BlockVideoReader":
		return ChatContentType_BlockVideoReader, nil
	case "BlockCode":
		return ChatContentType_BlockCode, nil
	case "BlockLocalLife":
		return ChatContentType_BlockLocalLife, nil
	case "BlockGenImage":
		return ChatContentType_BlockGenImage, nil
	case "BlockResearch":
		return ChatContentType_BlockResearch, nil
	case "BlockImage":
		return ChatContentType_BlockImage, nil
	case "BlockResearchWebpage":
		return ChatContentType_BlockResearchWebpage, nil
	case "BlockResearchStatus":
		return ChatContentType_BlockResearchStatus, nil
	case "BlockSupertask":
		return ChatContentType_BlockSupertask, nil
	case "BlockSupertaskTool":
		return ChatContentType_BlockSupertaskTool, nil
	case "BlockSupertaskProductionFile":
		return ChatContentType_BlockSupertaskProductionFile, nil
	case "BlockCreationLoading":
		return ChatContentType_BlockCreationLoading, nil
	case "BlockFileOperation":
		return ChatContentType_BlockFileOperation, nil
	case "BlockFile":
		return ChatContentType_BlockFile, nil
	case "BlockArtifactCodeFile":
		return ChatContentType_BlockArtifactCodeFile, nil
	case "BlockCodeAssistProcess":
		return ChatContentType_BlockCodeAssistProcess, nil
	case "BlockArtifact":
		return ChatContentType_BlockArtifact, nil
	}
	return ChatContentType(0), fmt.Errorf("not a valid ChatContentType string")
}

func ChatContentTypePtr(v ChatContentType) *ChatContentType { return &v }
func (p *ChatContentType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = ChatContentType(result.Int64)
	return
}

func (p *ChatContentType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type HomePageItemType int64

const (
	HomePageItemType_None               HomePageItemType = 0
	HomePageItemType_CodeGenerate       HomePageItemType = 1
	HomePageItemType_CodeDebug          HomePageItemType = 2
	HomePageItemType_CodeKnowledge      HomePageItemType = 3
	HomePageItemType_CodeBase           HomePageItemType = 4
	HomePageItemType_CodeArtifacts      HomePageItemType = 5
	HomePageItemType_PromptTemplate     HomePageItemType = 6
	HomePageItemType_AdvertisingArticle HomePageItemType = 7
)

func (p HomePageItemType) String() string {
	switch p {
	case HomePageItemType_None:
		return "None"
	case HomePageItemType_CodeGenerate:
		return "CodeGenerate"
	case HomePageItemType_CodeDebug:
		return "CodeDebug"
	case HomePageItemType_CodeKnowledge:
		return "CodeKnowledge"
	case HomePageItemType_CodeBase:
		return "CodeBase"
	case HomePageItemType_CodeArtifacts:
		return "CodeArtifacts"
	case HomePageItemType_PromptTemplate:
		return "PromptTemplate"
	case HomePageItemType_AdvertisingArticle:
		return "AdvertisingArticle"
	}
	return "<UNSET>"
}

func HomePageItemTypeFromString(s string) (HomePageItemType, error) {
	switch s {
	case "None":
		return HomePageItemType_None, nil
	case "CodeGenerate":
		return HomePageItemType_CodeGenerate, nil
	case "CodeDebug":
		return HomePageItemType_CodeDebug, nil
	case "CodeKnowledge":
		return HomePageItemType_CodeKnowledge, nil
	case "CodeBase":
		return HomePageItemType_CodeBase, nil
	case "CodeArtifacts":
		return HomePageItemType_CodeArtifacts, nil
	case "PromptTemplate":
		return HomePageItemType_PromptTemplate, nil
	case "AdvertisingArticle":
		return HomePageItemType_AdvertisingArticle, nil
	}
	return HomePageItemType(0), fmt.Errorf("not a valid HomePageItemType string")
}

func HomePageItemTypePtr(v HomePageItemType) *HomePageItemType { return &v }
func (p *HomePageItemType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = HomePageItemType(result.Int64)
	return
}

func (p *HomePageItemType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type ChatRequest struct {
	UserMessage     *Message   `thrift:"UserMessage,1,required" frugal:"1,required,Message" json:"UserMessage"`
	HistoryMessages []*Message `thrift:"HistoryMessages,2" frugal:"2,default,list<Message>" json:"HistoryMessages"`
	UserID          int64      `thrift:"UserID,3" frugal:"3,default,i64" json:"UserID"`
	AbVariables     *string    `thrift:"AbVariables,4,optional" frugal:"4,optional,string" json:"AbVariables,omitempty"`
	AppID           int64      `thrift:"AppID,5" frugal:"5,default,i64" json:"AppID"`
	UseDeepThink    *bool      `thrift:"UseDeepThink,6,optional" frugal:"6,optional,bool" json:"UseDeepThink,omitempty"`
	UseAutoCot      *CotMode   `thrift:"UseAutoCot,7,optional" frugal:"7,optional,CotMode" json:"UseAutoCot,omitempty"`
	Base            *base.Base `thrift:"Base,255,optional" frugal:"255,optional,base.Base" json:"Base,omitempty"`
}

func NewChatRequest() *ChatRequest {
	return &ChatRequest{}
}

func (p *ChatRequest) InitDefault() {
}

var ChatRequest_UserMessage_DEFAULT *Message

func (p *ChatRequest) GetUserMessage() (v *Message) {
	if !p.IsSetUserMessage() {
		return ChatRequest_UserMessage_DEFAULT
	}
	return p.UserMessage
}

func (p *ChatRequest) GetHistoryMessages() (v []*Message) {
	return p.HistoryMessages
}

func (p *ChatRequest) GetUserID() (v int64) {
	return p.UserID
}

var ChatRequest_AbVariables_DEFAULT string

func (p *ChatRequest) GetAbVariables() (v string) {
	if !p.IsSetAbVariables() {
		return ChatRequest_AbVariables_DEFAULT
	}
	return *p.AbVariables
}

func (p *ChatRequest) GetAppID() (v int64) {
	return p.AppID
}

var ChatRequest_UseDeepThink_DEFAULT bool

func (p *ChatRequest) GetUseDeepThink() (v bool) {
	if !p.IsSetUseDeepThink() {
		return ChatRequest_UseDeepThink_DEFAULT
	}
	return *p.UseDeepThink
}

var ChatRequest_UseAutoCot_DEFAULT CotMode

func (p *ChatRequest) GetUseAutoCot() (v CotMode) {
	if !p.IsSetUseAutoCot() {
		return ChatRequest_UseAutoCot_DEFAULT
	}
	return *p.UseAutoCot
}

var ChatRequest_Base_DEFAULT *base.Base

func (p *ChatRequest) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return ChatRequest_Base_DEFAULT
	}
	return p.Base
}
func (p *ChatRequest) SetUserMessage(val *Message) {
	p.UserMessage = val
}
func (p *ChatRequest) SetHistoryMessages(val []*Message) {
	p.HistoryMessages = val
}
func (p *ChatRequest) SetUserID(val int64) {
	p.UserID = val
}
func (p *ChatRequest) SetAbVariables(val *string) {
	p.AbVariables = val
}
func (p *ChatRequest) SetAppID(val int64) {
	p.AppID = val
}
func (p *ChatRequest) SetUseDeepThink(val *bool) {
	p.UseDeepThink = val
}
func (p *ChatRequest) SetUseAutoCot(val *CotMode) {
	p.UseAutoCot = val
}
func (p *ChatRequest) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_ChatRequest = map[int16]string{
	1:   "UserMessage",
	2:   "HistoryMessages",
	3:   "UserID",
	4:   "AbVariables",
	5:   "AppID",
	6:   "UseDeepThink",
	7:   "UseAutoCot",
	255: "Base",
}

func (p *ChatRequest) IsSetUserMessage() bool {
	return p.UserMessage != nil
}

func (p *ChatRequest) IsSetAbVariables() bool {
	return p.AbVariables != nil
}

func (p *ChatRequest) IsSetUseDeepThink() bool {
	return p.UseDeepThink != nil
}

func (p *ChatRequest) IsSetUseAutoCot() bool {
	return p.UseAutoCot != nil
}

func (p *ChatRequest) IsSetBase() bool {
	return p.Base != nil
}

func (p *ChatRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetUserMessage bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetUserMessage = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetUserMessage {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ChatRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ChatRequest[fieldId]))
}

func (p *ChatRequest) ReadField1(iprot thrift.TProtocol) error {
	_field := NewMessage()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.UserMessage = _field
	return nil
}
func (p *ChatRequest) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*Message, 0, size)
	values := make([]Message, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.HistoryMessages = _field
	return nil
}
func (p *ChatRequest) ReadField3(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UserID = _field
	return nil
}
func (p *ChatRequest) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.AbVariables = _field
	return nil
}
func (p *ChatRequest) ReadField5(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AppID = _field
	return nil
}
func (p *ChatRequest) ReadField6(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.UseDeepThink = _field
	return nil
}
func (p *ChatRequest) ReadField7(iprot thrift.TProtocol) error {

	var _field *CotMode
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := CotMode(v)
		_field = &tmp
	}
	p.UseAutoCot = _field
	return nil
}
func (p *ChatRequest) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *ChatRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ChatRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ChatRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("UserMessage", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.UserMessage.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ChatRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("HistoryMessages", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.HistoryMessages)); err != nil {
		return err
	}
	for _, v := range p.HistoryMessages {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ChatRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("UserID", thrift.I64, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.UserID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *ChatRequest) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetAbVariables() {
		if err = oprot.WriteFieldBegin("AbVariables", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.AbVariables); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *ChatRequest) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AppID", thrift.I64, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.AppID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *ChatRequest) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetUseDeepThink() {
		if err = oprot.WriteFieldBegin("UseDeepThink", thrift.BOOL, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.UseDeepThink); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *ChatRequest) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetUseAutoCot() {
		if err = oprot.WriteFieldBegin("UseAutoCot", thrift.I32, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.UseAutoCot)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}
func (p *ChatRequest) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *ChatRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ChatRequest(%+v)", *p)

}

func (p *ChatRequest) DeepEqual(ano *ChatRequest) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.UserMessage) {
		return false
	}
	if !p.Field2DeepEqual(ano.HistoryMessages) {
		return false
	}
	if !p.Field3DeepEqual(ano.UserID) {
		return false
	}
	if !p.Field4DeepEqual(ano.AbVariables) {
		return false
	}
	if !p.Field5DeepEqual(ano.AppID) {
		return false
	}
	if !p.Field6DeepEqual(ano.UseDeepThink) {
		return false
	}
	if !p.Field7DeepEqual(ano.UseAutoCot) {
		return false
	}
	if !p.Field255DeepEqual(ano.Base) {
		return false
	}
	return true
}

func (p *ChatRequest) Field1DeepEqual(src *Message) bool {

	if !p.UserMessage.DeepEqual(src) {
		return false
	}
	return true
}
func (p *ChatRequest) Field2DeepEqual(src []*Message) bool {

	if len(p.HistoryMessages) != len(src) {
		return false
	}
	for i, v := range p.HistoryMessages {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *ChatRequest) Field3DeepEqual(src int64) bool {

	if p.UserID != src {
		return false
	}
	return true
}
func (p *ChatRequest) Field4DeepEqual(src *string) bool {

	if p.AbVariables == src {
		return true
	} else if p.AbVariables == nil || src == nil {
		return false
	}
	if strings.Compare(*p.AbVariables, *src) != 0 {
		return false
	}
	return true
}
func (p *ChatRequest) Field5DeepEqual(src int64) bool {

	if p.AppID != src {
		return false
	}
	return true
}
func (p *ChatRequest) Field6DeepEqual(src *bool) bool {

	if p.UseDeepThink == src {
		return true
	} else if p.UseDeepThink == nil || src == nil {
		return false
	}
	if *p.UseDeepThink != *src {
		return false
	}
	return true
}
func (p *ChatRequest) Field7DeepEqual(src *CotMode) bool {

	if p.UseAutoCot == src {
		return true
	} else if p.UseAutoCot == nil || src == nil {
		return false
	}
	if *p.UseAutoCot != *src {
		return false
	}
	return true
}
func (p *ChatRequest) Field255DeepEqual(src *base.Base) bool {

	if !p.Base.DeepEqual(src) {
		return false
	}
	return true
}

type Message struct {
	Role             ChatMessageRole `thrift:"Role,1" frugal:"1,default,ChatMessageRole" json:"Role"`
	Content          string          `thrift:"Content,2" frugal:"2,default,string" json:"Content"`
	ContextBlocks    []*ContextBlock `thrift:"ContextBlocks,3,optional" frugal:"3,optional,list<ContextBlock>" json:"ContextBlocks,omitempty"`
	ContextVariables *string         `thrift:"ContextVariables,4,optional" frugal:"4,optional,string" json:"ContextVariables,omitempty"`
	SectionID        *string         `thrift:"SectionID,5,optional" frugal:"5,optional,string" json:"SectionID,omitempty"`
	ContentType      ChatContentType `thrift:"ContentType,6" frugal:"6,default,ChatContentType" json:"ContentType"`
	BizInfo          *BizInfo        `thrift:"BizInfo,7" frugal:"7,default,BizInfo" json:"BizInfo"`
}

func NewMessage() *Message {
	return &Message{}
}

func (p *Message) InitDefault() {
}

func (p *Message) GetRole() (v ChatMessageRole) {
	return p.Role
}

func (p *Message) GetContent() (v string) {
	return p.Content
}

var Message_ContextBlocks_DEFAULT []*ContextBlock

func (p *Message) GetContextBlocks() (v []*ContextBlock) {
	if !p.IsSetContextBlocks() {
		return Message_ContextBlocks_DEFAULT
	}
	return p.ContextBlocks
}

var Message_ContextVariables_DEFAULT string

func (p *Message) GetContextVariables() (v string) {
	if !p.IsSetContextVariables() {
		return Message_ContextVariables_DEFAULT
	}
	return *p.ContextVariables
}

var Message_SectionID_DEFAULT string

func (p *Message) GetSectionID() (v string) {
	if !p.IsSetSectionID() {
		return Message_SectionID_DEFAULT
	}
	return *p.SectionID
}

func (p *Message) GetContentType() (v ChatContentType) {
	return p.ContentType
}

var Message_BizInfo_DEFAULT *BizInfo

func (p *Message) GetBizInfo() (v *BizInfo) {
	if !p.IsSetBizInfo() {
		return Message_BizInfo_DEFAULT
	}
	return p.BizInfo
}
func (p *Message) SetRole(val ChatMessageRole) {
	p.Role = val
}
func (p *Message) SetContent(val string) {
	p.Content = val
}
func (p *Message) SetContextBlocks(val []*ContextBlock) {
	p.ContextBlocks = val
}
func (p *Message) SetContextVariables(val *string) {
	p.ContextVariables = val
}
func (p *Message) SetSectionID(val *string) {
	p.SectionID = val
}
func (p *Message) SetContentType(val ChatContentType) {
	p.ContentType = val
}
func (p *Message) SetBizInfo(val *BizInfo) {
	p.BizInfo = val
}

var fieldIDToName_Message = map[int16]string{
	1: "Role",
	2: "Content",
	3: "ContextBlocks",
	4: "ContextVariables",
	5: "SectionID",
	6: "ContentType",
	7: "BizInfo",
}

func (p *Message) IsSetContextBlocks() bool {
	return p.ContextBlocks != nil
}

func (p *Message) IsSetContextVariables() bool {
	return p.ContextVariables != nil
}

func (p *Message) IsSetSectionID() bool {
	return p.SectionID != nil
}

func (p *Message) IsSetBizInfo() bool {
	return p.BizInfo != nil
}

func (p *Message) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_Message[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *Message) ReadField1(iprot thrift.TProtocol) error {

	var _field ChatMessageRole
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = ChatMessageRole(v)
	}
	p.Role = _field
	return nil
}
func (p *Message) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Content = _field
	return nil
}
func (p *Message) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*ContextBlock, 0, size)
	values := make([]ContextBlock, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ContextBlocks = _field
	return nil
}
func (p *Message) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ContextVariables = _field
	return nil
}
func (p *Message) ReadField5(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.SectionID = _field
	return nil
}
func (p *Message) ReadField6(iprot thrift.TProtocol) error {

	var _field ChatContentType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = ChatContentType(v)
	}
	p.ContentType = _field
	return nil
}
func (p *Message) ReadField7(iprot thrift.TProtocol) error {
	_field := NewBizInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BizInfo = _field
	return nil
}

func (p *Message) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("Message"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *Message) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Role", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.Role)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *Message) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Content", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Content); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *Message) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetContextBlocks() {
		if err = oprot.WriteFieldBegin("ContextBlocks", thrift.LIST, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ContextBlocks)); err != nil {
			return err
		}
		for _, v := range p.ContextBlocks {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *Message) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetContextVariables() {
		if err = oprot.WriteFieldBegin("ContextVariables", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ContextVariables); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *Message) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetSectionID() {
		if err = oprot.WriteFieldBegin("SectionID", thrift.STRING, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.SectionID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *Message) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ContentType", thrift.I32, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.ContentType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *Message) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BizInfo", thrift.STRUCT, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.BizInfo.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *Message) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Message(%+v)", *p)

}

func (p *Message) DeepEqual(ano *Message) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Role) {
		return false
	}
	if !p.Field2DeepEqual(ano.Content) {
		return false
	}
	if !p.Field3DeepEqual(ano.ContextBlocks) {
		return false
	}
	if !p.Field4DeepEqual(ano.ContextVariables) {
		return false
	}
	if !p.Field5DeepEqual(ano.SectionID) {
		return false
	}
	if !p.Field6DeepEqual(ano.ContentType) {
		return false
	}
	if !p.Field7DeepEqual(ano.BizInfo) {
		return false
	}
	return true
}

func (p *Message) Field1DeepEqual(src ChatMessageRole) bool {

	if p.Role != src {
		return false
	}
	return true
}
func (p *Message) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Content, src) != 0 {
		return false
	}
	return true
}
func (p *Message) Field3DeepEqual(src []*ContextBlock) bool {

	if len(p.ContextBlocks) != len(src) {
		return false
	}
	for i, v := range p.ContextBlocks {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *Message) Field4DeepEqual(src *string) bool {

	if p.ContextVariables == src {
		return true
	} else if p.ContextVariables == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ContextVariables, *src) != 0 {
		return false
	}
	return true
}
func (p *Message) Field5DeepEqual(src *string) bool {

	if p.SectionID == src {
		return true
	} else if p.SectionID == nil || src == nil {
		return false
	}
	if strings.Compare(*p.SectionID, *src) != 0 {
		return false
	}
	return true
}
func (p *Message) Field6DeepEqual(src ChatContentType) bool {

	if p.ContentType != src {
		return false
	}
	return true
}
func (p *Message) Field7DeepEqual(src *BizInfo) bool {

	if !p.BizInfo.DeepEqual(src) {
		return false
	}
	return true
}

type BizInfo struct {
	MessageID        *int64            `thrift:"MessageID,1,optional" frugal:"1,optional,i64" json:"MessageID,omitempty"`
	ConversationID   *int64            `thrift:"ConversationID,2,optional" frugal:"2,optional,i64" json:"ConversationID,omitempty"`
	SectionID        *int64            `thrift:"SectionID,3,optional" frugal:"3,optional,i64" json:"SectionID,omitempty"`
	ConversationType *ConversationType `thrift:"ConversationType,4,optional" frugal:"4,optional,ConversationType" json:"ConversationType,omitempty"`
	AnswerID         *int64            `thrift:"AnswerID,5,optional" frugal:"5,optional,i64" json:"AnswerID,omitempty"`
}

func NewBizInfo() *BizInfo {
	return &BizInfo{}
}

func (p *BizInfo) InitDefault() {
}

var BizInfo_MessageID_DEFAULT int64

func (p *BizInfo) GetMessageID() (v int64) {
	if !p.IsSetMessageID() {
		return BizInfo_MessageID_DEFAULT
	}
	return *p.MessageID
}

var BizInfo_ConversationID_DEFAULT int64

func (p *BizInfo) GetConversationID() (v int64) {
	if !p.IsSetConversationID() {
		return BizInfo_ConversationID_DEFAULT
	}
	return *p.ConversationID
}

var BizInfo_SectionID_DEFAULT int64

func (p *BizInfo) GetSectionID() (v int64) {
	if !p.IsSetSectionID() {
		return BizInfo_SectionID_DEFAULT
	}
	return *p.SectionID
}

var BizInfo_ConversationType_DEFAULT ConversationType

func (p *BizInfo) GetConversationType() (v ConversationType) {
	if !p.IsSetConversationType() {
		return BizInfo_ConversationType_DEFAULT
	}
	return *p.ConversationType
}

var BizInfo_AnswerID_DEFAULT int64

func (p *BizInfo) GetAnswerID() (v int64) {
	if !p.IsSetAnswerID() {
		return BizInfo_AnswerID_DEFAULT
	}
	return *p.AnswerID
}
func (p *BizInfo) SetMessageID(val *int64) {
	p.MessageID = val
}
func (p *BizInfo) SetConversationID(val *int64) {
	p.ConversationID = val
}
func (p *BizInfo) SetSectionID(val *int64) {
	p.SectionID = val
}
func (p *BizInfo) SetConversationType(val *ConversationType) {
	p.ConversationType = val
}
func (p *BizInfo) SetAnswerID(val *int64) {
	p.AnswerID = val
}

var fieldIDToName_BizInfo = map[int16]string{
	1: "MessageID",
	2: "ConversationID",
	3: "SectionID",
	4: "ConversationType",
	5: "AnswerID",
}

func (p *BizInfo) IsSetMessageID() bool {
	return p.MessageID != nil
}

func (p *BizInfo) IsSetConversationID() bool {
	return p.ConversationID != nil
}

func (p *BizInfo) IsSetSectionID() bool {
	return p.SectionID != nil
}

func (p *BizInfo) IsSetConversationType() bool {
	return p.ConversationType != nil
}

func (p *BizInfo) IsSetAnswerID() bool {
	return p.AnswerID != nil
}

func (p *BizInfo) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_BizInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *BizInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.MessageID = _field
	return nil
}
func (p *BizInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ConversationID = _field
	return nil
}
func (p *BizInfo) ReadField3(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.SectionID = _field
	return nil
}
func (p *BizInfo) ReadField4(iprot thrift.TProtocol) error {

	var _field *ConversationType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := ConversationType(v)
		_field = &tmp
	}
	p.ConversationType = _field
	return nil
}
func (p *BizInfo) ReadField5(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.AnswerID = _field
	return nil
}

func (p *BizInfo) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("BizInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *BizInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetMessageID() {
		if err = oprot.WriteFieldBegin("MessageID", thrift.I64, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.MessageID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *BizInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetConversationID() {
		if err = oprot.WriteFieldBegin("ConversationID", thrift.I64, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.ConversationID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *BizInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetSectionID() {
		if err = oprot.WriteFieldBegin("SectionID", thrift.I64, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.SectionID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *BizInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetConversationType() {
		if err = oprot.WriteFieldBegin("ConversationType", thrift.I32, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.ConversationType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *BizInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetAnswerID() {
		if err = oprot.WriteFieldBegin("AnswerID", thrift.I64, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.AnswerID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *BizInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BizInfo(%+v)", *p)

}

func (p *BizInfo) DeepEqual(ano *BizInfo) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.MessageID) {
		return false
	}
	if !p.Field2DeepEqual(ano.ConversationID) {
		return false
	}
	if !p.Field3DeepEqual(ano.SectionID) {
		return false
	}
	if !p.Field4DeepEqual(ano.ConversationType) {
		return false
	}
	if !p.Field5DeepEqual(ano.AnswerID) {
		return false
	}
	return true
}

func (p *BizInfo) Field1DeepEqual(src *int64) bool {

	if p.MessageID == src {
		return true
	} else if p.MessageID == nil || src == nil {
		return false
	}
	if *p.MessageID != *src {
		return false
	}
	return true
}
func (p *BizInfo) Field2DeepEqual(src *int64) bool {

	if p.ConversationID == src {
		return true
	} else if p.ConversationID == nil || src == nil {
		return false
	}
	if *p.ConversationID != *src {
		return false
	}
	return true
}
func (p *BizInfo) Field3DeepEqual(src *int64) bool {

	if p.SectionID == src {
		return true
	} else if p.SectionID == nil || src == nil {
		return false
	}
	if *p.SectionID != *src {
		return false
	}
	return true
}
func (p *BizInfo) Field4DeepEqual(src *ConversationType) bool {

	if p.ConversationType == src {
		return true
	} else if p.ConversationType == nil || src == nil {
		return false
	}
	if *p.ConversationType != *src {
		return false
	}
	return true
}
func (p *BizInfo) Field5DeepEqual(src *int64) bool {

	if p.AnswerID == src {
		return true
	} else if p.AnswerID == nil || src == nil {
		return false
	}
	if *p.AnswerID != *src {
		return false
	}
	return true
}

type ContextBlock struct {
	Type           ContextBlockType `thrift:"Type,1" frugal:"1,default,ContextBlockType" json:"Type"`
	FileBlock      *FileBlock       `thrift:"FileBlock,2,optional" frugal:"2,optional,FileBlock" json:"FileBlock,omitempty"`
	DirectoryBlock *DirectoryBlock  `thrift:"DirectoryBlock,3,optional" frugal:"3,optional,DirectoryBlock" json:"DirectoryBlock,omitempty"`
	RepoBlock      *RepoBlock       `thrift:"RepoBlock,4,optional" frugal:"4,optional,RepoBlock" json:"RepoBlock,omitempty"`
	ModelGenBlock  *ModelGenBlock   `thrift:"ModelGenBlock,5,optional" frugal:"5,optional,ModelGenBlock" json:"ModelGenBlock,omitempty"`
	ImageBlock     *ImageBlock      `thrift:"ImageBlock,6,optional" frugal:"6,optional,ImageBlock" json:"ImageBlock,omitempty"`
}

func NewContextBlock() *ContextBlock {
	return &ContextBlock{}
}

func (p *ContextBlock) InitDefault() {
}

func (p *ContextBlock) GetType() (v ContextBlockType) {
	return p.Type
}

var ContextBlock_FileBlock_DEFAULT *FileBlock

func (p *ContextBlock) GetFileBlock() (v *FileBlock) {
	if !p.IsSetFileBlock() {
		return ContextBlock_FileBlock_DEFAULT
	}
	return p.FileBlock
}

var ContextBlock_DirectoryBlock_DEFAULT *DirectoryBlock

func (p *ContextBlock) GetDirectoryBlock() (v *DirectoryBlock) {
	if !p.IsSetDirectoryBlock() {
		return ContextBlock_DirectoryBlock_DEFAULT
	}
	return p.DirectoryBlock
}

var ContextBlock_RepoBlock_DEFAULT *RepoBlock

func (p *ContextBlock) GetRepoBlock() (v *RepoBlock) {
	if !p.IsSetRepoBlock() {
		return ContextBlock_RepoBlock_DEFAULT
	}
	return p.RepoBlock
}

var ContextBlock_ModelGenBlock_DEFAULT *ModelGenBlock

func (p *ContextBlock) GetModelGenBlock() (v *ModelGenBlock) {
	if !p.IsSetModelGenBlock() {
		return ContextBlock_ModelGenBlock_DEFAULT
	}
	return p.ModelGenBlock
}

var ContextBlock_ImageBlock_DEFAULT *ImageBlock

func (p *ContextBlock) GetImageBlock() (v *ImageBlock) {
	if !p.IsSetImageBlock() {
		return ContextBlock_ImageBlock_DEFAULT
	}
	return p.ImageBlock
}
func (p *ContextBlock) SetType(val ContextBlockType) {
	p.Type = val
}
func (p *ContextBlock) SetFileBlock(val *FileBlock) {
	p.FileBlock = val
}
func (p *ContextBlock) SetDirectoryBlock(val *DirectoryBlock) {
	p.DirectoryBlock = val
}
func (p *ContextBlock) SetRepoBlock(val *RepoBlock) {
	p.RepoBlock = val
}
func (p *ContextBlock) SetModelGenBlock(val *ModelGenBlock) {
	p.ModelGenBlock = val
}
func (p *ContextBlock) SetImageBlock(val *ImageBlock) {
	p.ImageBlock = val
}

var fieldIDToName_ContextBlock = map[int16]string{
	1: "Type",
	2: "FileBlock",
	3: "DirectoryBlock",
	4: "RepoBlock",
	5: "ModelGenBlock",
	6: "ImageBlock",
}

func (p *ContextBlock) IsSetFileBlock() bool {
	return p.FileBlock != nil
}

func (p *ContextBlock) IsSetDirectoryBlock() bool {
	return p.DirectoryBlock != nil
}

func (p *ContextBlock) IsSetRepoBlock() bool {
	return p.RepoBlock != nil
}

func (p *ContextBlock) IsSetModelGenBlock() bool {
	return p.ModelGenBlock != nil
}

func (p *ContextBlock) IsSetImageBlock() bool {
	return p.ImageBlock != nil
}

func (p *ContextBlock) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ContextBlock[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ContextBlock) ReadField1(iprot thrift.TProtocol) error {

	var _field ContextBlockType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = ContextBlockType(v)
	}
	p.Type = _field
	return nil
}
func (p *ContextBlock) ReadField2(iprot thrift.TProtocol) error {
	_field := NewFileBlock()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.FileBlock = _field
	return nil
}
func (p *ContextBlock) ReadField3(iprot thrift.TProtocol) error {
	_field := NewDirectoryBlock()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.DirectoryBlock = _field
	return nil
}
func (p *ContextBlock) ReadField4(iprot thrift.TProtocol) error {
	_field := NewRepoBlock()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.RepoBlock = _field
	return nil
}
func (p *ContextBlock) ReadField5(iprot thrift.TProtocol) error {
	_field := NewModelGenBlock()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.ModelGenBlock = _field
	return nil
}
func (p *ContextBlock) ReadField6(iprot thrift.TProtocol) error {
	_field := NewImageBlock()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.ImageBlock = _field
	return nil
}

func (p *ContextBlock) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ContextBlock"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ContextBlock) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Type", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.Type)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ContextBlock) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetFileBlock() {
		if err = oprot.WriteFieldBegin("FileBlock", thrift.STRUCT, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.FileBlock.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ContextBlock) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetDirectoryBlock() {
		if err = oprot.WriteFieldBegin("DirectoryBlock", thrift.STRUCT, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.DirectoryBlock.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *ContextBlock) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetRepoBlock() {
		if err = oprot.WriteFieldBegin("RepoBlock", thrift.STRUCT, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.RepoBlock.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *ContextBlock) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetModelGenBlock() {
		if err = oprot.WriteFieldBegin("ModelGenBlock", thrift.STRUCT, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.ModelGenBlock.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *ContextBlock) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetImageBlock() {
		if err = oprot.WriteFieldBegin("ImageBlock", thrift.STRUCT, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.ImageBlock.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *ContextBlock) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ContextBlock(%+v)", *p)

}

func (p *ContextBlock) DeepEqual(ano *ContextBlock) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Type) {
		return false
	}
	if !p.Field2DeepEqual(ano.FileBlock) {
		return false
	}
	if !p.Field3DeepEqual(ano.DirectoryBlock) {
		return false
	}
	if !p.Field4DeepEqual(ano.RepoBlock) {
		return false
	}
	if !p.Field5DeepEqual(ano.ModelGenBlock) {
		return false
	}
	if !p.Field6DeepEqual(ano.ImageBlock) {
		return false
	}
	return true
}

func (p *ContextBlock) Field1DeepEqual(src ContextBlockType) bool {

	if p.Type != src {
		return false
	}
	return true
}
func (p *ContextBlock) Field2DeepEqual(src *FileBlock) bool {

	if !p.FileBlock.DeepEqual(src) {
		return false
	}
	return true
}
func (p *ContextBlock) Field3DeepEqual(src *DirectoryBlock) bool {

	if !p.DirectoryBlock.DeepEqual(src) {
		return false
	}
	return true
}
func (p *ContextBlock) Field4DeepEqual(src *RepoBlock) bool {

	if !p.RepoBlock.DeepEqual(src) {
		return false
	}
	return true
}
func (p *ContextBlock) Field5DeepEqual(src *ModelGenBlock) bool {

	if !p.ModelGenBlock.DeepEqual(src) {
		return false
	}
	return true
}
func (p *ContextBlock) Field6DeepEqual(src *ImageBlock) bool {

	if !p.ImageBlock.DeepEqual(src) {
		return false
	}
	return true
}

type Image struct {
	URL  *string `thrift:"URL,1,optional" frugal:"1,optional,string" json:"URL,omitempty"`
	URI  *string `thrift:"URI,2,optional" frugal:"2,optional,string" json:"URI,omitempty"`
	Name *string `thrift:"Name,3,optional" frugal:"3,optional,string" json:"Name,omitempty"`
	MD5  *string `thrift:"MD5,4,optional" frugal:"4,optional,string" json:"MD5,omitempty"`
}

func NewImage() *Image {
	return &Image{}
}

func (p *Image) InitDefault() {
}

var Image_URL_DEFAULT string

func (p *Image) GetURL() (v string) {
	if !p.IsSetURL() {
		return Image_URL_DEFAULT
	}
	return *p.URL
}

var Image_URI_DEFAULT string

func (p *Image) GetURI() (v string) {
	if !p.IsSetURI() {
		return Image_URI_DEFAULT
	}
	return *p.URI
}

var Image_Name_DEFAULT string

func (p *Image) GetName() (v string) {
	if !p.IsSetName() {
		return Image_Name_DEFAULT
	}
	return *p.Name
}

var Image_MD5_DEFAULT string

func (p *Image) GetMD5() (v string) {
	if !p.IsSetMD5() {
		return Image_MD5_DEFAULT
	}
	return *p.MD5
}
func (p *Image) SetURL(val *string) {
	p.URL = val
}
func (p *Image) SetURI(val *string) {
	p.URI = val
}
func (p *Image) SetName(val *string) {
	p.Name = val
}
func (p *Image) SetMD5(val *string) {
	p.MD5 = val
}

var fieldIDToName_Image = map[int16]string{
	1: "URL",
	2: "URI",
	3: "Name",
	4: "MD5",
}

func (p *Image) IsSetURL() bool {
	return p.URL != nil
}

func (p *Image) IsSetURI() bool {
	return p.URI != nil
}

func (p *Image) IsSetName() bool {
	return p.Name != nil
}

func (p *Image) IsSetMD5() bool {
	return p.MD5 != nil
}

func (p *Image) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_Image[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *Image) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.URL = _field
	return nil
}
func (p *Image) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.URI = _field
	return nil
}
func (p *Image) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Name = _field
	return nil
}
func (p *Image) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.MD5 = _field
	return nil
}

func (p *Image) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("Image"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *Image) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetURL() {
		if err = oprot.WriteFieldBegin("URL", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.URL); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *Image) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetURI() {
		if err = oprot.WriteFieldBegin("URI", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.URI); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *Image) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetName() {
		if err = oprot.WriteFieldBegin("Name", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Name); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *Image) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetMD5() {
		if err = oprot.WriteFieldBegin("MD5", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.MD5); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *Image) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Image(%+v)", *p)

}

func (p *Image) DeepEqual(ano *Image) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.URL) {
		return false
	}
	if !p.Field2DeepEqual(ano.URI) {
		return false
	}
	if !p.Field3DeepEqual(ano.Name) {
		return false
	}
	if !p.Field4DeepEqual(ano.MD5) {
		return false
	}
	return true
}

func (p *Image) Field1DeepEqual(src *string) bool {

	if p.URL == src {
		return true
	} else if p.URL == nil || src == nil {
		return false
	}
	if strings.Compare(*p.URL, *src) != 0 {
		return false
	}
	return true
}
func (p *Image) Field2DeepEqual(src *string) bool {

	if p.URI == src {
		return true
	} else if p.URI == nil || src == nil {
		return false
	}
	if strings.Compare(*p.URI, *src) != 0 {
		return false
	}
	return true
}
func (p *Image) Field3DeepEqual(src *string) bool {

	if p.Name == src {
		return true
	} else if p.Name == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Name, *src) != 0 {
		return false
	}
	return true
}
func (p *Image) Field4DeepEqual(src *string) bool {

	if p.MD5 == src {
		return true
	} else if p.MD5 == nil || src == nil {
		return false
	}
	if strings.Compare(*p.MD5, *src) != 0 {
		return false
	}
	return true
}

type ImageBlock struct {
	Image *Image `thrift:"Image,1" frugal:"1,default,Image" json:"Image"`
}

func NewImageBlock() *ImageBlock {
	return &ImageBlock{}
}

func (p *ImageBlock) InitDefault() {
}

var ImageBlock_Image_DEFAULT *Image

func (p *ImageBlock) GetImage() (v *Image) {
	if !p.IsSetImage() {
		return ImageBlock_Image_DEFAULT
	}
	return p.Image
}
func (p *ImageBlock) SetImage(val *Image) {
	p.Image = val
}

var fieldIDToName_ImageBlock = map[int16]string{
	1: "Image",
}

func (p *ImageBlock) IsSetImage() bool {
	return p.Image != nil
}

func (p *ImageBlock) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ImageBlock[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ImageBlock) ReadField1(iprot thrift.TProtocol) error {
	_field := NewImage()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Image = _field
	return nil
}

func (p *ImageBlock) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ImageBlock"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ImageBlock) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Image", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Image.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ImageBlock) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ImageBlock(%+v)", *p)

}

func (p *ImageBlock) DeepEqual(ano *ImageBlock) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Image) {
		return false
	}
	return true
}

func (p *ImageBlock) Field1DeepEqual(src *Image) bool {

	if !p.Image.DeepEqual(src) {
		return false
	}
	return true
}

type FileBlock struct {
	Identifiers []*ContextIdentifier `thrift:"Identifiers,1" frugal:"1,default,list<ContextIdentifier>" json:"Identifiers"`
}

func NewFileBlock() *FileBlock {
	return &FileBlock{}
}

func (p *FileBlock) InitDefault() {
}

func (p *FileBlock) GetIdentifiers() (v []*ContextIdentifier) {
	return p.Identifiers
}
func (p *FileBlock) SetIdentifiers(val []*ContextIdentifier) {
	p.Identifiers = val
}

var fieldIDToName_FileBlock = map[int16]string{
	1: "Identifiers",
}

func (p *FileBlock) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FileBlock[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *FileBlock) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*ContextIdentifier, 0, size)
	values := make([]ContextIdentifier, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Identifiers = _field
	return nil
}

func (p *FileBlock) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("FileBlock"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *FileBlock) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Identifiers", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Identifiers)); err != nil {
		return err
	}
	for _, v := range p.Identifiers {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *FileBlock) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FileBlock(%+v)", *p)

}

func (p *FileBlock) DeepEqual(ano *FileBlock) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Identifiers) {
		return false
	}
	return true
}

func (p *FileBlock) Field1DeepEqual(src []*ContextIdentifier) bool {

	if len(p.Identifiers) != len(src) {
		return false
	}
	for i, v := range p.Identifiers {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type DirectoryBlock struct {
	Identifier *ContextIdentifier `thrift:"Identifier,1" frugal:"1,default,ContextIdentifier" json:"Identifier"`
}

func NewDirectoryBlock() *DirectoryBlock {
	return &DirectoryBlock{}
}

func (p *DirectoryBlock) InitDefault() {
}

var DirectoryBlock_Identifier_DEFAULT *ContextIdentifier

func (p *DirectoryBlock) GetIdentifier() (v *ContextIdentifier) {
	if !p.IsSetIdentifier() {
		return DirectoryBlock_Identifier_DEFAULT
	}
	return p.Identifier
}
func (p *DirectoryBlock) SetIdentifier(val *ContextIdentifier) {
	p.Identifier = val
}

var fieldIDToName_DirectoryBlock = map[int16]string{
	1: "Identifier",
}

func (p *DirectoryBlock) IsSetIdentifier() bool {
	return p.Identifier != nil
}

func (p *DirectoryBlock) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DirectoryBlock[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DirectoryBlock) ReadField1(iprot thrift.TProtocol) error {
	_field := NewContextIdentifier()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Identifier = _field
	return nil
}

func (p *DirectoryBlock) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("DirectoryBlock"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DirectoryBlock) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Identifier", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Identifier.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DirectoryBlock) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DirectoryBlock(%+v)", *p)

}

func (p *DirectoryBlock) DeepEqual(ano *DirectoryBlock) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Identifier) {
		return false
	}
	return true
}

func (p *DirectoryBlock) Field1DeepEqual(src *ContextIdentifier) bool {

	if !p.Identifier.DeepEqual(src) {
		return false
	}
	return true
}

type RepoBlock struct {
	Identifier *ContextIdentifier `thrift:"Identifier,1" frugal:"1,default,ContextIdentifier" json:"Identifier"`
}

func NewRepoBlock() *RepoBlock {
	return &RepoBlock{}
}

func (p *RepoBlock) InitDefault() {
}

var RepoBlock_Identifier_DEFAULT *ContextIdentifier

func (p *RepoBlock) GetIdentifier() (v *ContextIdentifier) {
	if !p.IsSetIdentifier() {
		return RepoBlock_Identifier_DEFAULT
	}
	return p.Identifier
}
func (p *RepoBlock) SetIdentifier(val *ContextIdentifier) {
	p.Identifier = val
}

var fieldIDToName_RepoBlock = map[int16]string{
	1: "Identifier",
}

func (p *RepoBlock) IsSetIdentifier() bool {
	return p.Identifier != nil
}

func (p *RepoBlock) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_RepoBlock[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *RepoBlock) ReadField1(iprot thrift.TProtocol) error {
	_field := NewContextIdentifier()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Identifier = _field
	return nil
}

func (p *RepoBlock) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("RepoBlock"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *RepoBlock) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Identifier", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Identifier.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *RepoBlock) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RepoBlock(%+v)", *p)

}

func (p *RepoBlock) DeepEqual(ano *RepoBlock) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Identifier) {
		return false
	}
	return true
}

func (p *RepoBlock) Field1DeepEqual(src *ContextIdentifier) bool {

	if !p.Identifier.DeepEqual(src) {
		return false
	}
	return true
}

type ModelGenBlock struct {
	Content  string `thrift:"Content,1" frugal:"1,default,string" json:"Content"`
	Language string `thrift:"Language,2" frugal:"2,default,string" json:"Language"`
}

func NewModelGenBlock() *ModelGenBlock {
	return &ModelGenBlock{}
}

func (p *ModelGenBlock) InitDefault() {
}

func (p *ModelGenBlock) GetContent() (v string) {
	return p.Content
}

func (p *ModelGenBlock) GetLanguage() (v string) {
	return p.Language
}
func (p *ModelGenBlock) SetContent(val string) {
	p.Content = val
}
func (p *ModelGenBlock) SetLanguage(val string) {
	p.Language = val
}

var fieldIDToName_ModelGenBlock = map[int16]string{
	1: "Content",
	2: "Language",
}

func (p *ModelGenBlock) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ModelGenBlock[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ModelGenBlock) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Content = _field
	return nil
}
func (p *ModelGenBlock) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Language = _field
	return nil
}

func (p *ModelGenBlock) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ModelGenBlock"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModelGenBlock) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Content", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Content); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ModelGenBlock) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Language", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Language); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ModelGenBlock) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModelGenBlock(%+v)", *p)

}

func (p *ModelGenBlock) DeepEqual(ano *ModelGenBlock) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Content) {
		return false
	}
	if !p.Field2DeepEqual(ano.Language) {
		return false
	}
	return true
}

func (p *ModelGenBlock) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Content, src) != 0 {
		return false
	}
	return true
}
func (p *ModelGenBlock) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Language, src) != 0 {
		return false
	}
	return true
}

type StreamChatResponse struct {
	EventType    ChatEventType     `thrift:"EventType,1" frugal:"1,default,ChatEventType" json:"EventType"`
	EventContent *ChatEventContent `thrift:"EventContent,2,optional" frugal:"2,optional,ChatEventContent" json:"EventContent,omitempty"`
	IsFinish     bool              `thrift:"IsFinish,3" frugal:"3,default,bool" json:"IsFinish"`
	ErrorMessage string            `thrift:"ErrorMessage,4" frugal:"4,default,string" json:"ErrorMessage"`
	ErrorCode    int32             `thrift:"ErrorCode,5" frugal:"5,default,i32" json:"ErrorCode"`
	BaseResp     *base.BaseResp    `thrift:"BaseResp,255,optional" frugal:"255,optional,base.BaseResp" json:"BaseResp,omitempty"`
}

func NewStreamChatResponse() *StreamChatResponse {
	return &StreamChatResponse{}
}

func (p *StreamChatResponse) InitDefault() {
}

func (p *StreamChatResponse) GetEventType() (v ChatEventType) {
	return p.EventType
}

var StreamChatResponse_EventContent_DEFAULT *ChatEventContent

func (p *StreamChatResponse) GetEventContent() (v *ChatEventContent) {
	if !p.IsSetEventContent() {
		return StreamChatResponse_EventContent_DEFAULT
	}
	return p.EventContent
}

func (p *StreamChatResponse) GetIsFinish() (v bool) {
	return p.IsFinish
}

func (p *StreamChatResponse) GetErrorMessage() (v string) {
	return p.ErrorMessage
}

func (p *StreamChatResponse) GetErrorCode() (v int32) {
	return p.ErrorCode
}

var StreamChatResponse_BaseResp_DEFAULT *base.BaseResp

func (p *StreamChatResponse) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return StreamChatResponse_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *StreamChatResponse) SetEventType(val ChatEventType) {
	p.EventType = val
}
func (p *StreamChatResponse) SetEventContent(val *ChatEventContent) {
	p.EventContent = val
}
func (p *StreamChatResponse) SetIsFinish(val bool) {
	p.IsFinish = val
}
func (p *StreamChatResponse) SetErrorMessage(val string) {
	p.ErrorMessage = val
}
func (p *StreamChatResponse) SetErrorCode(val int32) {
	p.ErrorCode = val
}
func (p *StreamChatResponse) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_StreamChatResponse = map[int16]string{
	1:   "EventType",
	2:   "EventContent",
	3:   "IsFinish",
	4:   "ErrorMessage",
	5:   "ErrorCode",
	255: "BaseResp",
}

func (p *StreamChatResponse) IsSetEventContent() bool {
	return p.EventContent != nil
}

func (p *StreamChatResponse) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *StreamChatResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_StreamChatResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *StreamChatResponse) ReadField1(iprot thrift.TProtocol) error {

	var _field ChatEventType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = ChatEventType(v)
	}
	p.EventType = _field
	return nil
}
func (p *StreamChatResponse) ReadField2(iprot thrift.TProtocol) error {
	_field := NewChatEventContent()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.EventContent = _field
	return nil
}
func (p *StreamChatResponse) ReadField3(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.IsFinish = _field
	return nil
}
func (p *StreamChatResponse) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ErrorMessage = _field
	return nil
}
func (p *StreamChatResponse) ReadField5(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ErrorCode = _field
	return nil
}
func (p *StreamChatResponse) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *StreamChatResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("StreamChatResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *StreamChatResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EventType", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.EventType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *StreamChatResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetEventContent() {
		if err = oprot.WriteFieldBegin("EventContent", thrift.STRUCT, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.EventContent.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *StreamChatResponse) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("IsFinish", thrift.BOOL, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.IsFinish); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *StreamChatResponse) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ErrorMessage", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ErrorMessage); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *StreamChatResponse) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ErrorCode", thrift.I32, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.ErrorCode); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *StreamChatResponse) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBaseResp() {
		if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.BaseResp.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *StreamChatResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("StreamChatResponse(%+v)", *p)

}

func (p *StreamChatResponse) DeepEqual(ano *StreamChatResponse) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.EventType) {
		return false
	}
	if !p.Field2DeepEqual(ano.EventContent) {
		return false
	}
	if !p.Field3DeepEqual(ano.IsFinish) {
		return false
	}
	if !p.Field4DeepEqual(ano.ErrorMessage) {
		return false
	}
	if !p.Field5DeepEqual(ano.ErrorCode) {
		return false
	}
	if !p.Field255DeepEqual(ano.BaseResp) {
		return false
	}
	return true
}

func (p *StreamChatResponse) Field1DeepEqual(src ChatEventType) bool {

	if p.EventType != src {
		return false
	}
	return true
}
func (p *StreamChatResponse) Field2DeepEqual(src *ChatEventContent) bool {

	if !p.EventContent.DeepEqual(src) {
		return false
	}
	return true
}
func (p *StreamChatResponse) Field3DeepEqual(src bool) bool {

	if p.IsFinish != src {
		return false
	}
	return true
}
func (p *StreamChatResponse) Field4DeepEqual(src string) bool {

	if strings.Compare(p.ErrorMessage, src) != 0 {
		return false
	}
	return true
}
func (p *StreamChatResponse) Field5DeepEqual(src int32) bool {

	if p.ErrorCode != src {
		return false
	}
	return true
}
func (p *StreamChatResponse) Field255DeepEqual(src *base.BaseResp) bool {

	if !p.BaseResp.DeepEqual(src) {
		return false
	}
	return true
}

type ChatEventContent struct {
	Content     string            `thrift:"Content,1" frugal:"1,default,string" json:"Content"`
	ContentType ChatContentType   `thrift:"ContentType,2" frugal:"2,default,ChatContentType" json:"ContentType"`
	Ext         map[string]string `thrift:"Ext,3" frugal:"3,default,map<string:string>" json:"Ext"`
	ID          string            `thrift:"ID,4" frugal:"4,default,string" json:"ID"`
}

func NewChatEventContent() *ChatEventContent {
	return &ChatEventContent{}
}

func (p *ChatEventContent) InitDefault() {
}

func (p *ChatEventContent) GetContent() (v string) {
	return p.Content
}

func (p *ChatEventContent) GetContentType() (v ChatContentType) {
	return p.ContentType
}

func (p *ChatEventContent) GetExt() (v map[string]string) {
	return p.Ext
}

func (p *ChatEventContent) GetID() (v string) {
	return p.ID
}
func (p *ChatEventContent) SetContent(val string) {
	p.Content = val
}
func (p *ChatEventContent) SetContentType(val ChatContentType) {
	p.ContentType = val
}
func (p *ChatEventContent) SetExt(val map[string]string) {
	p.Ext = val
}
func (p *ChatEventContent) SetID(val string) {
	p.ID = val
}

var fieldIDToName_ChatEventContent = map[int16]string{
	1: "Content",
	2: "ContentType",
	3: "Ext",
	4: "ID",
}

func (p *ChatEventContent) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ChatEventContent[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ChatEventContent) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Content = _field
	return nil
}
func (p *ChatEventContent) ReadField2(iprot thrift.TProtocol) error {

	var _field ChatContentType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = ChatContentType(v)
	}
	p.ContentType = _field
	return nil
}
func (p *ChatEventContent) ReadField3(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_key = v
		}

		var _val string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_val = v
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.Ext = _field
	return nil
}
func (p *ChatEventContent) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ID = _field
	return nil
}

func (p *ChatEventContent) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ChatEventContent"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ChatEventContent) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Content", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Content); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ChatEventContent) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ContentType", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.ContentType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ChatEventContent) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Ext", thrift.MAP, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.Ext)); err != nil {
		return err
	}
	for k, v := range p.Ext {
		if err := oprot.WriteString(k); err != nil {
			return err
		}
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteMapEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *ChatEventContent) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ID", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ChatEventContent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ChatEventContent(%+v)", *p)

}

func (p *ChatEventContent) DeepEqual(ano *ChatEventContent) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Content) {
		return false
	}
	if !p.Field2DeepEqual(ano.ContentType) {
		return false
	}
	if !p.Field3DeepEqual(ano.Ext) {
		return false
	}
	if !p.Field4DeepEqual(ano.ID) {
		return false
	}
	return true
}

func (p *ChatEventContent) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Content, src) != 0 {
		return false
	}
	return true
}
func (p *ChatEventContent) Field2DeepEqual(src ChatContentType) bool {

	if p.ContentType != src {
		return false
	}
	return true
}
func (p *ChatEventContent) Field3DeepEqual(src map[string]string) bool {

	if len(p.Ext) != len(src) {
		return false
	}
	for k, v := range p.Ext {
		_src := src[k]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *ChatEventContent) Field4DeepEqual(src string) bool {

	if strings.Compare(p.ID, src) != 0 {
		return false
	}
	return true
}

type PromptMessage struct {
	Role     string  `thrift:"Role,1" frugal:"1,default,string" json:"Role"`
	Content  string  `thrift:"Content,2" frugal:"2,default,string" json:"Content"`
	MetaData *string `thrift:"MetaData,3,optional" frugal:"3,optional,string" json:"MetaData,omitempty"`
}

func NewPromptMessage() *PromptMessage {
	return &PromptMessage{}
}

func (p *PromptMessage) InitDefault() {
}

func (p *PromptMessage) GetRole() (v string) {
	return p.Role
}

func (p *PromptMessage) GetContent() (v string) {
	return p.Content
}

var PromptMessage_MetaData_DEFAULT string

func (p *PromptMessage) GetMetaData() (v string) {
	if !p.IsSetMetaData() {
		return PromptMessage_MetaData_DEFAULT
	}
	return *p.MetaData
}
func (p *PromptMessage) SetRole(val string) {
	p.Role = val
}
func (p *PromptMessage) SetContent(val string) {
	p.Content = val
}
func (p *PromptMessage) SetMetaData(val *string) {
	p.MetaData = val
}

var fieldIDToName_PromptMessage = map[int16]string{
	1: "Role",
	2: "Content",
	3: "MetaData",
}

func (p *PromptMessage) IsSetMetaData() bool {
	return p.MetaData != nil
}

func (p *PromptMessage) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PromptMessage[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *PromptMessage) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Role = _field
	return nil
}
func (p *PromptMessage) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Content = _field
	return nil
}
func (p *PromptMessage) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.MetaData = _field
	return nil
}

func (p *PromptMessage) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("PromptMessage"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *PromptMessage) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Role", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Role); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *PromptMessage) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Content", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Content); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *PromptMessage) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetMetaData() {
		if err = oprot.WriteFieldBegin("MetaData", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.MetaData); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *PromptMessage) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PromptMessage(%+v)", *p)

}

func (p *PromptMessage) DeepEqual(ano *PromptMessage) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Role) {
		return false
	}
	if !p.Field2DeepEqual(ano.Content) {
		return false
	}
	if !p.Field3DeepEqual(ano.MetaData) {
		return false
	}
	return true
}

func (p *PromptMessage) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Role, src) != 0 {
		return false
	}
	return true
}
func (p *PromptMessage) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Content, src) != 0 {
		return false
	}
	return true
}
func (p *PromptMessage) Field3DeepEqual(src *string) bool {

	if p.MetaData == src {
		return true
	} else if p.MetaData == nil || src == nil {
		return false
	}
	if strings.Compare(*p.MetaData, *src) != 0 {
		return false
	}
	return true
}

type PromptsRenderRequest struct {
	Messages  []*PromptMessage `thrift:"Messages,1,required" frugal:"1,required,list<PromptMessage>" json:"Messages"`
	Variables string           `thrift:"Variables,2" frugal:"2,default,string" json:"Variables"`
	Base      *base.Base       `thrift:"Base,255,optional" frugal:"255,optional,base.Base" json:"Base,omitempty"`
}

func NewPromptsRenderRequest() *PromptsRenderRequest {
	return &PromptsRenderRequest{}
}

func (p *PromptsRenderRequest) InitDefault() {
}

func (p *PromptsRenderRequest) GetMessages() (v []*PromptMessage) {
	return p.Messages
}

func (p *PromptsRenderRequest) GetVariables() (v string) {
	return p.Variables
}

var PromptsRenderRequest_Base_DEFAULT *base.Base

func (p *PromptsRenderRequest) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return PromptsRenderRequest_Base_DEFAULT
	}
	return p.Base
}
func (p *PromptsRenderRequest) SetMessages(val []*PromptMessage) {
	p.Messages = val
}
func (p *PromptsRenderRequest) SetVariables(val string) {
	p.Variables = val
}
func (p *PromptsRenderRequest) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_PromptsRenderRequest = map[int16]string{
	1:   "Messages",
	2:   "Variables",
	255: "Base",
}

func (p *PromptsRenderRequest) IsSetBase() bool {
	return p.Base != nil
}

func (p *PromptsRenderRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetMessages bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetMessages = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetMessages {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PromptsRenderRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_PromptsRenderRequest[fieldId]))
}

func (p *PromptsRenderRequest) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*PromptMessage, 0, size)
	values := make([]PromptMessage, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Messages = _field
	return nil
}
func (p *PromptsRenderRequest) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Variables = _field
	return nil
}
func (p *PromptsRenderRequest) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *PromptsRenderRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("PromptsRenderRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *PromptsRenderRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Messages", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Messages)); err != nil {
		return err
	}
	for _, v := range p.Messages {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *PromptsRenderRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Variables", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Variables); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *PromptsRenderRequest) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *PromptsRenderRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PromptsRenderRequest(%+v)", *p)

}

func (p *PromptsRenderRequest) DeepEqual(ano *PromptsRenderRequest) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Messages) {
		return false
	}
	if !p.Field2DeepEqual(ano.Variables) {
		return false
	}
	if !p.Field255DeepEqual(ano.Base) {
		return false
	}
	return true
}

func (p *PromptsRenderRequest) Field1DeepEqual(src []*PromptMessage) bool {

	if len(p.Messages) != len(src) {
		return false
	}
	for i, v := range p.Messages {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *PromptsRenderRequest) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Variables, src) != 0 {
		return false
	}
	return true
}
func (p *PromptsRenderRequest) Field255DeepEqual(src *base.Base) bool {

	if !p.Base.DeepEqual(src) {
		return false
	}
	return true
}

type PromptsRenderResponse struct {
	Messages []*PromptMessage `thrift:"Messages,1" frugal:"1,default,list<PromptMessage>" json:"Messages"`
	BaseResp *base.BaseResp   `thrift:"BaseResp,255,optional" frugal:"255,optional,base.BaseResp" json:"BaseResp,omitempty"`
}

func NewPromptsRenderResponse() *PromptsRenderResponse {
	return &PromptsRenderResponse{}
}

func (p *PromptsRenderResponse) InitDefault() {
}

func (p *PromptsRenderResponse) GetMessages() (v []*PromptMessage) {
	return p.Messages
}

var PromptsRenderResponse_BaseResp_DEFAULT *base.BaseResp

func (p *PromptsRenderResponse) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return PromptsRenderResponse_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *PromptsRenderResponse) SetMessages(val []*PromptMessage) {
	p.Messages = val
}
func (p *PromptsRenderResponse) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_PromptsRenderResponse = map[int16]string{
	1:   "Messages",
	255: "BaseResp",
}

func (p *PromptsRenderResponse) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *PromptsRenderResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PromptsRenderResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *PromptsRenderResponse) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*PromptMessage, 0, size)
	values := make([]PromptMessage, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Messages = _field
	return nil
}
func (p *PromptsRenderResponse) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *PromptsRenderResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("PromptsRenderResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *PromptsRenderResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Messages", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Messages)); err != nil {
		return err
	}
	for _, v := range p.Messages {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *PromptsRenderResponse) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBaseResp() {
		if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.BaseResp.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *PromptsRenderResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PromptsRenderResponse(%+v)", *p)

}

func (p *PromptsRenderResponse) DeepEqual(ano *PromptsRenderResponse) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Messages) {
		return false
	}
	if !p.Field255DeepEqual(ano.BaseResp) {
		return false
	}
	return true
}

func (p *PromptsRenderResponse) Field1DeepEqual(src []*PromptMessage) bool {

	if len(p.Messages) != len(src) {
		return false
	}
	for i, v := range p.Messages {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *PromptsRenderResponse) Field255DeepEqual(src *base.BaseResp) bool {

	if !p.BaseResp.DeepEqual(src) {
		return false
	}
	return true
}

type CaseInfo struct {
	Id                   string `thrift:"id,1" frugal:"1,default,string" json:"id"`
	Query                string `thrift:"query,2" frugal:"2,default,string" json:"query"`
	RepoName             string `thrift:"repo_name,3" frugal:"3,default,string" json:"repo_name"`
	Tags                 string `thrift:"tags,4" frugal:"4,default,string" json:"tags"`
	FilePath             string `thrift:"file_path,5" frugal:"5,default,string" json:"file_path"`
	SelectCodeStartLine  string `thrift:"select_code_start_line,6" frugal:"6,default,string" json:"select_code_start_line"`
	SelectCodeEndLine    string `thrift:"select_code_end_line,7" frugal:"7,default,string" json:"select_code_end_line"`
	SelectCodeContent    string `thrift:"select_code_content,8" frugal:"8,default,string" json:"select_code_content"`
	VisibleCodeStartLine string `thrift:"visible_code_start_line,9" frugal:"9,default,string" json:"visible_code_start_line"`
	VisibleCodeEndLine   string `thrift:"visible_code_end_line,10" frugal:"10,default,string" json:"visible_code_end_line"`
	ResourceKey          string `thrift:"resource_key,11" frugal:"11,default,string" json:"resource_key"`
	Language             string `thrift:"language,12" frugal:"12,default,string" json:"language"`
	UserId               string `thrift:"user_id,13" frugal:"13,default,string" json:"user_id"`
	SelectFileUri        string `thrift:"select_file_uri,14" frugal:"14,default,string" json:"select_file_uri"`
	FileContent          string `thrift:"file_content,15" frugal:"15,default,string" json:"file_content"`
	EmbeddingTopN        string `thrift:"embedding_top_n,16" frugal:"16,default,string" json:"embedding_top_n"`
	EmbeddingRecallNums  string `thrift:"embedding_recall_nums,17" frugal:"17,default,string" json:"embedding_recall_nums"`
	RerankRecallNums     string `thrift:"rerank_recall_nums,18" frugal:"18,default,string" json:"rerank_recall_nums"`
	ChatHistory          string `thrift:"chat_history,19" frugal:"19,default,string" json:"chat_history"`
	AppName              string `thrift:"app_name,20" frugal:"20,default,string" json:"app_name"`
	CommandName          string `thrift:"command_name,21" frugal:"21,default,string" json:"command_name"`
	ReferenceContent     string `thrift:"reference_content,22" frugal:"22,default,string" json:"reference_content"`
	ReferenceLanguage    string `thrift:"reference_language,23" frugal:"23,default,string" json:"reference_language"`
}

func NewCaseInfo() *CaseInfo {
	return &CaseInfo{}
}

func (p *CaseInfo) InitDefault() {
}

func (p *CaseInfo) GetId() (v string) {
	return p.Id
}

func (p *CaseInfo) GetQuery() (v string) {
	return p.Query
}

func (p *CaseInfo) GetRepoName() (v string) {
	return p.RepoName
}

func (p *CaseInfo) GetTags() (v string) {
	return p.Tags
}

func (p *CaseInfo) GetFilePath() (v string) {
	return p.FilePath
}

func (p *CaseInfo) GetSelectCodeStartLine() (v string) {
	return p.SelectCodeStartLine
}

func (p *CaseInfo) GetSelectCodeEndLine() (v string) {
	return p.SelectCodeEndLine
}

func (p *CaseInfo) GetSelectCodeContent() (v string) {
	return p.SelectCodeContent
}

func (p *CaseInfo) GetVisibleCodeStartLine() (v string) {
	return p.VisibleCodeStartLine
}

func (p *CaseInfo) GetVisibleCodeEndLine() (v string) {
	return p.VisibleCodeEndLine
}

func (p *CaseInfo) GetResourceKey() (v string) {
	return p.ResourceKey
}

func (p *CaseInfo) GetLanguage() (v string) {
	return p.Language
}

func (p *CaseInfo) GetUserId() (v string) {
	return p.UserId
}

func (p *CaseInfo) GetSelectFileUri() (v string) {
	return p.SelectFileUri
}

func (p *CaseInfo) GetFileContent() (v string) {
	return p.FileContent
}

func (p *CaseInfo) GetEmbeddingTopN() (v string) {
	return p.EmbeddingTopN
}

func (p *CaseInfo) GetEmbeddingRecallNums() (v string) {
	return p.EmbeddingRecallNums
}

func (p *CaseInfo) GetRerankRecallNums() (v string) {
	return p.RerankRecallNums
}

func (p *CaseInfo) GetChatHistory() (v string) {
	return p.ChatHistory
}

func (p *CaseInfo) GetAppName() (v string) {
	return p.AppName
}

func (p *CaseInfo) GetCommandName() (v string) {
	return p.CommandName
}

func (p *CaseInfo) GetReferenceContent() (v string) {
	return p.ReferenceContent
}

func (p *CaseInfo) GetReferenceLanguage() (v string) {
	return p.ReferenceLanguage
}
func (p *CaseInfo) SetId(val string) {
	p.Id = val
}
func (p *CaseInfo) SetQuery(val string) {
	p.Query = val
}
func (p *CaseInfo) SetRepoName(val string) {
	p.RepoName = val
}
func (p *CaseInfo) SetTags(val string) {
	p.Tags = val
}
func (p *CaseInfo) SetFilePath(val string) {
	p.FilePath = val
}
func (p *CaseInfo) SetSelectCodeStartLine(val string) {
	p.SelectCodeStartLine = val
}
func (p *CaseInfo) SetSelectCodeEndLine(val string) {
	p.SelectCodeEndLine = val
}
func (p *CaseInfo) SetSelectCodeContent(val string) {
	p.SelectCodeContent = val
}
func (p *CaseInfo) SetVisibleCodeStartLine(val string) {
	p.VisibleCodeStartLine = val
}
func (p *CaseInfo) SetVisibleCodeEndLine(val string) {
	p.VisibleCodeEndLine = val
}
func (p *CaseInfo) SetResourceKey(val string) {
	p.ResourceKey = val
}
func (p *CaseInfo) SetLanguage(val string) {
	p.Language = val
}
func (p *CaseInfo) SetUserId(val string) {
	p.UserId = val
}
func (p *CaseInfo) SetSelectFileUri(val string) {
	p.SelectFileUri = val
}
func (p *CaseInfo) SetFileContent(val string) {
	p.FileContent = val
}
func (p *CaseInfo) SetEmbeddingTopN(val string) {
	p.EmbeddingTopN = val
}
func (p *CaseInfo) SetEmbeddingRecallNums(val string) {
	p.EmbeddingRecallNums = val
}
func (p *CaseInfo) SetRerankRecallNums(val string) {
	p.RerankRecallNums = val
}
func (p *CaseInfo) SetChatHistory(val string) {
	p.ChatHistory = val
}
func (p *CaseInfo) SetAppName(val string) {
	p.AppName = val
}
func (p *CaseInfo) SetCommandName(val string) {
	p.CommandName = val
}
func (p *CaseInfo) SetReferenceContent(val string) {
	p.ReferenceContent = val
}
func (p *CaseInfo) SetReferenceLanguage(val string) {
	p.ReferenceLanguage = val
}

var fieldIDToName_CaseInfo = map[int16]string{
	1:  "id",
	2:  "query",
	3:  "repo_name",
	4:  "tags",
	5:  "file_path",
	6:  "select_code_start_line",
	7:  "select_code_end_line",
	8:  "select_code_content",
	9:  "visible_code_start_line",
	10: "visible_code_end_line",
	11: "resource_key",
	12: "language",
	13: "user_id",
	14: "select_file_uri",
	15: "file_content",
	16: "embedding_top_n",
	17: "embedding_recall_nums",
	18: "rerank_recall_nums",
	19: "chat_history",
	20: "app_name",
	21: "command_name",
	22: "reference_content",
	23: "reference_language",
}

func (p *CaseInfo) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField14(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField15(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 16:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField16(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 17:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField17(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 18:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField18(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 19:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField19(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 20:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField20(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 21:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField21(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 22:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField22(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 23:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField23(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CaseInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *CaseInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Id = _field
	return nil
}
func (p *CaseInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Query = _field
	return nil
}
func (p *CaseInfo) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RepoName = _field
	return nil
}
func (p *CaseInfo) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Tags = _field
	return nil
}
func (p *CaseInfo) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.FilePath = _field
	return nil
}
func (p *CaseInfo) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SelectCodeStartLine = _field
	return nil
}
func (p *CaseInfo) ReadField7(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SelectCodeEndLine = _field
	return nil
}
func (p *CaseInfo) ReadField8(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SelectCodeContent = _field
	return nil
}
func (p *CaseInfo) ReadField9(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.VisibleCodeStartLine = _field
	return nil
}
func (p *CaseInfo) ReadField10(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.VisibleCodeEndLine = _field
	return nil
}
func (p *CaseInfo) ReadField11(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ResourceKey = _field
	return nil
}
func (p *CaseInfo) ReadField12(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Language = _field
	return nil
}
func (p *CaseInfo) ReadField13(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UserId = _field
	return nil
}
func (p *CaseInfo) ReadField14(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SelectFileUri = _field
	return nil
}
func (p *CaseInfo) ReadField15(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.FileContent = _field
	return nil
}
func (p *CaseInfo) ReadField16(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EmbeddingTopN = _field
	return nil
}
func (p *CaseInfo) ReadField17(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EmbeddingRecallNums = _field
	return nil
}
func (p *CaseInfo) ReadField18(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RerankRecallNums = _field
	return nil
}
func (p *CaseInfo) ReadField19(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ChatHistory = _field
	return nil
}
func (p *CaseInfo) ReadField20(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AppName = _field
	return nil
}
func (p *CaseInfo) ReadField21(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CommandName = _field
	return nil
}
func (p *CaseInfo) ReadField22(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ReferenceContent = _field
	return nil
}
func (p *CaseInfo) ReadField23(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ReferenceLanguage = _field
	return nil
}

func (p *CaseInfo) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("CaseInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
		if err = p.writeField14(oprot); err != nil {
			fieldId = 14
			goto WriteFieldError
		}
		if err = p.writeField15(oprot); err != nil {
			fieldId = 15
			goto WriteFieldError
		}
		if err = p.writeField16(oprot); err != nil {
			fieldId = 16
			goto WriteFieldError
		}
		if err = p.writeField17(oprot); err != nil {
			fieldId = 17
			goto WriteFieldError
		}
		if err = p.writeField18(oprot); err != nil {
			fieldId = 18
			goto WriteFieldError
		}
		if err = p.writeField19(oprot); err != nil {
			fieldId = 19
			goto WriteFieldError
		}
		if err = p.writeField20(oprot); err != nil {
			fieldId = 20
			goto WriteFieldError
		}
		if err = p.writeField21(oprot); err != nil {
			fieldId = 21
			goto WriteFieldError
		}
		if err = p.writeField22(oprot); err != nil {
			fieldId = 22
			goto WriteFieldError
		}
		if err = p.writeField23(oprot); err != nil {
			fieldId = 23
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CaseInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("id", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Id); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *CaseInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("query", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Query); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *CaseInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("repo_name", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RepoName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *CaseInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("tags", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Tags); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *CaseInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("file_path", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.FilePath); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *CaseInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("select_code_start_line", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SelectCodeStartLine); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *CaseInfo) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("select_code_end_line", thrift.STRING, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SelectCodeEndLine); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}
func (p *CaseInfo) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("select_code_content", thrift.STRING, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SelectCodeContent); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}
func (p *CaseInfo) writeField9(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("visible_code_start_line", thrift.STRING, 9); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.VisibleCodeStartLine); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}
func (p *CaseInfo) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("visible_code_end_line", thrift.STRING, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.VisibleCodeEndLine); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}
func (p *CaseInfo) writeField11(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("resource_key", thrift.STRING, 11); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ResourceKey); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}
func (p *CaseInfo) writeField12(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("language", thrift.STRING, 12); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Language); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}
func (p *CaseInfo) writeField13(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("user_id", thrift.STRING, 13); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.UserId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}
func (p *CaseInfo) writeField14(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("select_file_uri", thrift.STRING, 14); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SelectFileUri); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 end error: ", p), err)
}
func (p *CaseInfo) writeField15(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("file_content", thrift.STRING, 15); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.FileContent); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 end error: ", p), err)
}
func (p *CaseInfo) writeField16(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("embedding_top_n", thrift.STRING, 16); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.EmbeddingTopN); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 end error: ", p), err)
}
func (p *CaseInfo) writeField17(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("embedding_recall_nums", thrift.STRING, 17); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.EmbeddingRecallNums); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 end error: ", p), err)
}
func (p *CaseInfo) writeField18(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("rerank_recall_nums", thrift.STRING, 18); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RerankRecallNums); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 18 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 18 end error: ", p), err)
}
func (p *CaseInfo) writeField19(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("chat_history", thrift.STRING, 19); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ChatHistory); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 19 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 19 end error: ", p), err)
}
func (p *CaseInfo) writeField20(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("app_name", thrift.STRING, 20); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AppName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 20 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 20 end error: ", p), err)
}
func (p *CaseInfo) writeField21(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("command_name", thrift.STRING, 21); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CommandName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 21 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 21 end error: ", p), err)
}
func (p *CaseInfo) writeField22(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("reference_content", thrift.STRING, 22); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ReferenceContent); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 22 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 22 end error: ", p), err)
}
func (p *CaseInfo) writeField23(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("reference_language", thrift.STRING, 23); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ReferenceLanguage); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 23 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 23 end error: ", p), err)
}

func (p *CaseInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CaseInfo(%+v)", *p)

}

func (p *CaseInfo) DeepEqual(ano *CaseInfo) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Id) {
		return false
	}
	if !p.Field2DeepEqual(ano.Query) {
		return false
	}
	if !p.Field3DeepEqual(ano.RepoName) {
		return false
	}
	if !p.Field4DeepEqual(ano.Tags) {
		return false
	}
	if !p.Field5DeepEqual(ano.FilePath) {
		return false
	}
	if !p.Field6DeepEqual(ano.SelectCodeStartLine) {
		return false
	}
	if !p.Field7DeepEqual(ano.SelectCodeEndLine) {
		return false
	}
	if !p.Field8DeepEqual(ano.SelectCodeContent) {
		return false
	}
	if !p.Field9DeepEqual(ano.VisibleCodeStartLine) {
		return false
	}
	if !p.Field10DeepEqual(ano.VisibleCodeEndLine) {
		return false
	}
	if !p.Field11DeepEqual(ano.ResourceKey) {
		return false
	}
	if !p.Field12DeepEqual(ano.Language) {
		return false
	}
	if !p.Field13DeepEqual(ano.UserId) {
		return false
	}
	if !p.Field14DeepEqual(ano.SelectFileUri) {
		return false
	}
	if !p.Field15DeepEqual(ano.FileContent) {
		return false
	}
	if !p.Field16DeepEqual(ano.EmbeddingTopN) {
		return false
	}
	if !p.Field17DeepEqual(ano.EmbeddingRecallNums) {
		return false
	}
	if !p.Field18DeepEqual(ano.RerankRecallNums) {
		return false
	}
	if !p.Field19DeepEqual(ano.ChatHistory) {
		return false
	}
	if !p.Field20DeepEqual(ano.AppName) {
		return false
	}
	if !p.Field21DeepEqual(ano.CommandName) {
		return false
	}
	if !p.Field22DeepEqual(ano.ReferenceContent) {
		return false
	}
	if !p.Field23DeepEqual(ano.ReferenceLanguage) {
		return false
	}
	return true
}

func (p *CaseInfo) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Id, src) != 0 {
		return false
	}
	return true
}
func (p *CaseInfo) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Query, src) != 0 {
		return false
	}
	return true
}
func (p *CaseInfo) Field3DeepEqual(src string) bool {

	if strings.Compare(p.RepoName, src) != 0 {
		return false
	}
	return true
}
func (p *CaseInfo) Field4DeepEqual(src string) bool {

	if strings.Compare(p.Tags, src) != 0 {
		return false
	}
	return true
}
func (p *CaseInfo) Field5DeepEqual(src string) bool {

	if strings.Compare(p.FilePath, src) != 0 {
		return false
	}
	return true
}
func (p *CaseInfo) Field6DeepEqual(src string) bool {

	if strings.Compare(p.SelectCodeStartLine, src) != 0 {
		return false
	}
	return true
}
func (p *CaseInfo) Field7DeepEqual(src string) bool {

	if strings.Compare(p.SelectCodeEndLine, src) != 0 {
		return false
	}
	return true
}
func (p *CaseInfo) Field8DeepEqual(src string) bool {

	if strings.Compare(p.SelectCodeContent, src) != 0 {
		return false
	}
	return true
}
func (p *CaseInfo) Field9DeepEqual(src string) bool {

	if strings.Compare(p.VisibleCodeStartLine, src) != 0 {
		return false
	}
	return true
}
func (p *CaseInfo) Field10DeepEqual(src string) bool {

	if strings.Compare(p.VisibleCodeEndLine, src) != 0 {
		return false
	}
	return true
}
func (p *CaseInfo) Field11DeepEqual(src string) bool {

	if strings.Compare(p.ResourceKey, src) != 0 {
		return false
	}
	return true
}
func (p *CaseInfo) Field12DeepEqual(src string) bool {

	if strings.Compare(p.Language, src) != 0 {
		return false
	}
	return true
}
func (p *CaseInfo) Field13DeepEqual(src string) bool {

	if strings.Compare(p.UserId, src) != 0 {
		return false
	}
	return true
}
func (p *CaseInfo) Field14DeepEqual(src string) bool {

	if strings.Compare(p.SelectFileUri, src) != 0 {
		return false
	}
	return true
}
func (p *CaseInfo) Field15DeepEqual(src string) bool {

	if strings.Compare(p.FileContent, src) != 0 {
		return false
	}
	return true
}
func (p *CaseInfo) Field16DeepEqual(src string) bool {

	if strings.Compare(p.EmbeddingTopN, src) != 0 {
		return false
	}
	return true
}
func (p *CaseInfo) Field17DeepEqual(src string) bool {

	if strings.Compare(p.EmbeddingRecallNums, src) != 0 {
		return false
	}
	return true
}
func (p *CaseInfo) Field18DeepEqual(src string) bool {

	if strings.Compare(p.RerankRecallNums, src) != 0 {
		return false
	}
	return true
}
func (p *CaseInfo) Field19DeepEqual(src string) bool {

	if strings.Compare(p.ChatHistory, src) != 0 {
		return false
	}
	return true
}
func (p *CaseInfo) Field20DeepEqual(src string) bool {

	if strings.Compare(p.AppName, src) != 0 {
		return false
	}
	return true
}
func (p *CaseInfo) Field21DeepEqual(src string) bool {

	if strings.Compare(p.CommandName, src) != 0 {
		return false
	}
	return true
}
func (p *CaseInfo) Field22DeepEqual(src string) bool {

	if strings.Compare(p.ReferenceContent, src) != 0 {
		return false
	}
	return true
}
func (p *CaseInfo) Field23DeepEqual(src string) bool {

	if strings.Compare(p.ReferenceLanguage, src) != 0 {
		return false
	}
	return true
}

type E2EPromptsRenderRequest struct {
	CaseInfo    string           `thrift:"CaseInfo,1" frugal:"1,default,string" json:"CaseInfo"`
	Messages    []*PromptMessage `thrift:"Messages,2" frugal:"2,default,list<PromptMessage>" json:"Messages"`
	PromptKey   string           `thrift:"PromptKey,3" frugal:"3,default,string" json:"PromptKey"`
	PromptLabel string           `thrift:"PromptLabel,4" frugal:"4,default,string" json:"PromptLabel"`
	Base        *base.Base       `thrift:"Base,255,optional" frugal:"255,optional,base.Base" json:"Base,omitempty"`
}

func NewE2EPromptsRenderRequest() *E2EPromptsRenderRequest {
	return &E2EPromptsRenderRequest{}
}

func (p *E2EPromptsRenderRequest) InitDefault() {
}

func (p *E2EPromptsRenderRequest) GetCaseInfo() (v string) {
	return p.CaseInfo
}

func (p *E2EPromptsRenderRequest) GetMessages() (v []*PromptMessage) {
	return p.Messages
}

func (p *E2EPromptsRenderRequest) GetPromptKey() (v string) {
	return p.PromptKey
}

func (p *E2EPromptsRenderRequest) GetPromptLabel() (v string) {
	return p.PromptLabel
}

var E2EPromptsRenderRequest_Base_DEFAULT *base.Base

func (p *E2EPromptsRenderRequest) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return E2EPromptsRenderRequest_Base_DEFAULT
	}
	return p.Base
}
func (p *E2EPromptsRenderRequest) SetCaseInfo(val string) {
	p.CaseInfo = val
}
func (p *E2EPromptsRenderRequest) SetMessages(val []*PromptMessage) {
	p.Messages = val
}
func (p *E2EPromptsRenderRequest) SetPromptKey(val string) {
	p.PromptKey = val
}
func (p *E2EPromptsRenderRequest) SetPromptLabel(val string) {
	p.PromptLabel = val
}
func (p *E2EPromptsRenderRequest) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_E2EPromptsRenderRequest = map[int16]string{
	1:   "CaseInfo",
	2:   "Messages",
	3:   "PromptKey",
	4:   "PromptLabel",
	255: "Base",
}

func (p *E2EPromptsRenderRequest) IsSetBase() bool {
	return p.Base != nil
}

func (p *E2EPromptsRenderRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_E2EPromptsRenderRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *E2EPromptsRenderRequest) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CaseInfo = _field
	return nil
}
func (p *E2EPromptsRenderRequest) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*PromptMessage, 0, size)
	values := make([]PromptMessage, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Messages = _field
	return nil
}
func (p *E2EPromptsRenderRequest) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.PromptKey = _field
	return nil
}
func (p *E2EPromptsRenderRequest) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.PromptLabel = _field
	return nil
}
func (p *E2EPromptsRenderRequest) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *E2EPromptsRenderRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("E2EPromptsRenderRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *E2EPromptsRenderRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CaseInfo", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CaseInfo); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *E2EPromptsRenderRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Messages", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Messages)); err != nil {
		return err
	}
	for _, v := range p.Messages {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *E2EPromptsRenderRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("PromptKey", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.PromptKey); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *E2EPromptsRenderRequest) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("PromptLabel", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.PromptLabel); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *E2EPromptsRenderRequest) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *E2EPromptsRenderRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("E2EPromptsRenderRequest(%+v)", *p)

}

func (p *E2EPromptsRenderRequest) DeepEqual(ano *E2EPromptsRenderRequest) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.CaseInfo) {
		return false
	}
	if !p.Field2DeepEqual(ano.Messages) {
		return false
	}
	if !p.Field3DeepEqual(ano.PromptKey) {
		return false
	}
	if !p.Field4DeepEqual(ano.PromptLabel) {
		return false
	}
	if !p.Field255DeepEqual(ano.Base) {
		return false
	}
	return true
}

func (p *E2EPromptsRenderRequest) Field1DeepEqual(src string) bool {

	if strings.Compare(p.CaseInfo, src) != 0 {
		return false
	}
	return true
}
func (p *E2EPromptsRenderRequest) Field2DeepEqual(src []*PromptMessage) bool {

	if len(p.Messages) != len(src) {
		return false
	}
	for i, v := range p.Messages {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *E2EPromptsRenderRequest) Field3DeepEqual(src string) bool {

	if strings.Compare(p.PromptKey, src) != 0 {
		return false
	}
	return true
}
func (p *E2EPromptsRenderRequest) Field4DeepEqual(src string) bool {

	if strings.Compare(p.PromptLabel, src) != 0 {
		return false
	}
	return true
}
func (p *E2EPromptsRenderRequest) Field255DeepEqual(src *base.Base) bool {

	if !p.Base.DeepEqual(src) {
		return false
	}
	return true
}

type E2EPromptsRenderResponse struct {
	Messages []*PromptMessage `thrift:"Messages,1" frugal:"1,default,list<PromptMessage>" json:"Messages"`
	Extra    string           `thrift:"Extra,2" frugal:"2,default,string" json:"Extra"`
	BaseResp *base.BaseResp   `thrift:"BaseResp,255,optional" frugal:"255,optional,base.BaseResp" json:"BaseResp,omitempty"`
}

func NewE2EPromptsRenderResponse() *E2EPromptsRenderResponse {
	return &E2EPromptsRenderResponse{}
}

func (p *E2EPromptsRenderResponse) InitDefault() {
}

func (p *E2EPromptsRenderResponse) GetMessages() (v []*PromptMessage) {
	return p.Messages
}

func (p *E2EPromptsRenderResponse) GetExtra() (v string) {
	return p.Extra
}

var E2EPromptsRenderResponse_BaseResp_DEFAULT *base.BaseResp

func (p *E2EPromptsRenderResponse) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return E2EPromptsRenderResponse_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *E2EPromptsRenderResponse) SetMessages(val []*PromptMessage) {
	p.Messages = val
}
func (p *E2EPromptsRenderResponse) SetExtra(val string) {
	p.Extra = val
}
func (p *E2EPromptsRenderResponse) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_E2EPromptsRenderResponse = map[int16]string{
	1:   "Messages",
	2:   "Extra",
	255: "BaseResp",
}

func (p *E2EPromptsRenderResponse) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *E2EPromptsRenderResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_E2EPromptsRenderResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *E2EPromptsRenderResponse) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*PromptMessage, 0, size)
	values := make([]PromptMessage, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Messages = _field
	return nil
}
func (p *E2EPromptsRenderResponse) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Extra = _field
	return nil
}
func (p *E2EPromptsRenderResponse) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *E2EPromptsRenderResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("E2EPromptsRenderResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *E2EPromptsRenderResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Messages", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Messages)); err != nil {
		return err
	}
	for _, v := range p.Messages {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *E2EPromptsRenderResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Extra", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Extra); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *E2EPromptsRenderResponse) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBaseResp() {
		if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.BaseResp.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *E2EPromptsRenderResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("E2EPromptsRenderResponse(%+v)", *p)

}

func (p *E2EPromptsRenderResponse) DeepEqual(ano *E2EPromptsRenderResponse) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Messages) {
		return false
	}
	if !p.Field2DeepEqual(ano.Extra) {
		return false
	}
	if !p.Field255DeepEqual(ano.BaseResp) {
		return false
	}
	return true
}

func (p *E2EPromptsRenderResponse) Field1DeepEqual(src []*PromptMessage) bool {

	if len(p.Messages) != len(src) {
		return false
	}
	for i, v := range p.Messages {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *E2EPromptsRenderResponse) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Extra, src) != 0 {
		return false
	}
	return true
}
func (p *E2EPromptsRenderResponse) Field255DeepEqual(src *base.BaseResp) bool {

	if !p.BaseResp.DeepEqual(src) {
		return false
	}
	return true
}

type HomePageRequest struct {
	AbVariables *string    `thrift:"AbVariables,1,optional" frugal:"1,optional,string" json:"AbVariables,omitempty"`
	Language    *string    `thrift:"Language,2,optional" frugal:"2,optional,string" json:"Language,omitempty"`
	Base        *base.Base `thrift:"Base,255,optional" frugal:"255,optional,base.Base" json:"Base,omitempty"`
}

func NewHomePageRequest() *HomePageRequest {
	return &HomePageRequest{}
}

func (p *HomePageRequest) InitDefault() {
}

var HomePageRequest_AbVariables_DEFAULT string

func (p *HomePageRequest) GetAbVariables() (v string) {
	if !p.IsSetAbVariables() {
		return HomePageRequest_AbVariables_DEFAULT
	}
	return *p.AbVariables
}

var HomePageRequest_Language_DEFAULT string

func (p *HomePageRequest) GetLanguage() (v string) {
	if !p.IsSetLanguage() {
		return HomePageRequest_Language_DEFAULT
	}
	return *p.Language
}

var HomePageRequest_Base_DEFAULT *base.Base

func (p *HomePageRequest) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return HomePageRequest_Base_DEFAULT
	}
	return p.Base
}
func (p *HomePageRequest) SetAbVariables(val *string) {
	p.AbVariables = val
}
func (p *HomePageRequest) SetLanguage(val *string) {
	p.Language = val
}
func (p *HomePageRequest) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_HomePageRequest = map[int16]string{
	1:   "AbVariables",
	2:   "Language",
	255: "Base",
}

func (p *HomePageRequest) IsSetAbVariables() bool {
	return p.AbVariables != nil
}

func (p *HomePageRequest) IsSetLanguage() bool {
	return p.Language != nil
}

func (p *HomePageRequest) IsSetBase() bool {
	return p.Base != nil
}

func (p *HomePageRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_HomePageRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *HomePageRequest) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.AbVariables = _field
	return nil
}
func (p *HomePageRequest) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Language = _field
	return nil
}
func (p *HomePageRequest) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *HomePageRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("HomePageRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *HomePageRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetAbVariables() {
		if err = oprot.WriteFieldBegin("AbVariables", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.AbVariables); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *HomePageRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetLanguage() {
		if err = oprot.WriteFieldBegin("Language", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Language); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *HomePageRequest) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *HomePageRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("HomePageRequest(%+v)", *p)

}

func (p *HomePageRequest) DeepEqual(ano *HomePageRequest) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.AbVariables) {
		return false
	}
	if !p.Field2DeepEqual(ano.Language) {
		return false
	}
	if !p.Field255DeepEqual(ano.Base) {
		return false
	}
	return true
}

func (p *HomePageRequest) Field1DeepEqual(src *string) bool {

	if p.AbVariables == src {
		return true
	} else if p.AbVariables == nil || src == nil {
		return false
	}
	if strings.Compare(*p.AbVariables, *src) != 0 {
		return false
	}
	return true
}
func (p *HomePageRequest) Field2DeepEqual(src *string) bool {

	if p.Language == src {
		return true
	} else if p.Language == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Language, *src) != 0 {
		return false
	}
	return true
}
func (p *HomePageRequest) Field255DeepEqual(src *base.Base) bool {

	if !p.Base.DeepEqual(src) {
		return false
	}
	return true
}

type CodebaseItems struct {
	CodebaseName        string           `thrift:"CodebaseName,1" frugal:"1,default,string" json:"CodebaseName"`
	CodebaseDescription string           `thrift:"CodebaseDescription,2" frugal:"2,default,string" json:"CodebaseDescription"`
	CodebaseURL         string           `thrift:"CodebaseURL,3" frugal:"3,default,string" json:"CodebaseURL"`
	CodebaseBranchName  string           `thrift:"CodebaseBranchName,4" frugal:"4,default,string" json:"CodebaseBranchName"`
	CodebaseID          string           `thrift:"CodebaseID,5" frugal:"5,default,string" json:"CodebaseID"`
	HomePageItemType    HomePageItemType `thrift:"HomePageItemType,6" frugal:"6,default,HomePageItemType" json:"HomePageItemType"`
	Query               string           `thrift:"Query,7" frugal:"7,default,string" json:"Query"`
	IconURL             string           `thrift:"IconURL,8" frugal:"8,default,string" json:"IconURL"`
	UseCache            bool             `thrift:"UseCache,9" frugal:"9,default,bool" json:"UseCache"`
	Answer              string           `thrift:"Answer,10" frugal:"10,default,string" json:"Answer"`
}

func NewCodebaseItems() *CodebaseItems {
	return &CodebaseItems{}
}

func (p *CodebaseItems) InitDefault() {
}

func (p *CodebaseItems) GetCodebaseName() (v string) {
	return p.CodebaseName
}

func (p *CodebaseItems) GetCodebaseDescription() (v string) {
	return p.CodebaseDescription
}

func (p *CodebaseItems) GetCodebaseURL() (v string) {
	return p.CodebaseURL
}

func (p *CodebaseItems) GetCodebaseBranchName() (v string) {
	return p.CodebaseBranchName
}

func (p *CodebaseItems) GetCodebaseID() (v string) {
	return p.CodebaseID
}

func (p *CodebaseItems) GetHomePageItemType() (v HomePageItemType) {
	return p.HomePageItemType
}

func (p *CodebaseItems) GetQuery() (v string) {
	return p.Query
}

func (p *CodebaseItems) GetIconURL() (v string) {
	return p.IconURL
}

func (p *CodebaseItems) GetUseCache() (v bool) {
	return p.UseCache
}

func (p *CodebaseItems) GetAnswer() (v string) {
	return p.Answer
}
func (p *CodebaseItems) SetCodebaseName(val string) {
	p.CodebaseName = val
}
func (p *CodebaseItems) SetCodebaseDescription(val string) {
	p.CodebaseDescription = val
}
func (p *CodebaseItems) SetCodebaseURL(val string) {
	p.CodebaseURL = val
}
func (p *CodebaseItems) SetCodebaseBranchName(val string) {
	p.CodebaseBranchName = val
}
func (p *CodebaseItems) SetCodebaseID(val string) {
	p.CodebaseID = val
}
func (p *CodebaseItems) SetHomePageItemType(val HomePageItemType) {
	p.HomePageItemType = val
}
func (p *CodebaseItems) SetQuery(val string) {
	p.Query = val
}
func (p *CodebaseItems) SetIconURL(val string) {
	p.IconURL = val
}
func (p *CodebaseItems) SetUseCache(val bool) {
	p.UseCache = val
}
func (p *CodebaseItems) SetAnswer(val string) {
	p.Answer = val
}

var fieldIDToName_CodebaseItems = map[int16]string{
	1:  "CodebaseName",
	2:  "CodebaseDescription",
	3:  "CodebaseURL",
	4:  "CodebaseBranchName",
	5:  "CodebaseID",
	6:  "HomePageItemType",
	7:  "Query",
	8:  "IconURL",
	9:  "UseCache",
	10: "Answer",
}

func (p *CodebaseItems) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CodebaseItems[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *CodebaseItems) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CodebaseName = _field
	return nil
}
func (p *CodebaseItems) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CodebaseDescription = _field
	return nil
}
func (p *CodebaseItems) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CodebaseURL = _field
	return nil
}
func (p *CodebaseItems) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CodebaseBranchName = _field
	return nil
}
func (p *CodebaseItems) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CodebaseID = _field
	return nil
}
func (p *CodebaseItems) ReadField6(iprot thrift.TProtocol) error {

	var _field HomePageItemType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = HomePageItemType(v)
	}
	p.HomePageItemType = _field
	return nil
}
func (p *CodebaseItems) ReadField7(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Query = _field
	return nil
}
func (p *CodebaseItems) ReadField8(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.IconURL = _field
	return nil
}
func (p *CodebaseItems) ReadField9(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UseCache = _field
	return nil
}
func (p *CodebaseItems) ReadField10(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Answer = _field
	return nil
}

func (p *CodebaseItems) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("CodebaseItems"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CodebaseItems) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CodebaseName", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CodebaseName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *CodebaseItems) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CodebaseDescription", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CodebaseDescription); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *CodebaseItems) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CodebaseURL", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CodebaseURL); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *CodebaseItems) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CodebaseBranchName", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CodebaseBranchName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *CodebaseItems) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CodebaseID", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CodebaseID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *CodebaseItems) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("HomePageItemType", thrift.I32, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.HomePageItemType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *CodebaseItems) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Query", thrift.STRING, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Query); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}
func (p *CodebaseItems) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("IconURL", thrift.STRING, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.IconURL); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}
func (p *CodebaseItems) writeField9(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("UseCache", thrift.BOOL, 9); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.UseCache); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}
func (p *CodebaseItems) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Answer", thrift.STRING, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Answer); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *CodebaseItems) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CodebaseItems(%+v)", *p)

}

func (p *CodebaseItems) DeepEqual(ano *CodebaseItems) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.CodebaseName) {
		return false
	}
	if !p.Field2DeepEqual(ano.CodebaseDescription) {
		return false
	}
	if !p.Field3DeepEqual(ano.CodebaseURL) {
		return false
	}
	if !p.Field4DeepEqual(ano.CodebaseBranchName) {
		return false
	}
	if !p.Field5DeepEqual(ano.CodebaseID) {
		return false
	}
	if !p.Field6DeepEqual(ano.HomePageItemType) {
		return false
	}
	if !p.Field7DeepEqual(ano.Query) {
		return false
	}
	if !p.Field8DeepEqual(ano.IconURL) {
		return false
	}
	if !p.Field9DeepEqual(ano.UseCache) {
		return false
	}
	if !p.Field10DeepEqual(ano.Answer) {
		return false
	}
	return true
}

func (p *CodebaseItems) Field1DeepEqual(src string) bool {

	if strings.Compare(p.CodebaseName, src) != 0 {
		return false
	}
	return true
}
func (p *CodebaseItems) Field2DeepEqual(src string) bool {

	if strings.Compare(p.CodebaseDescription, src) != 0 {
		return false
	}
	return true
}
func (p *CodebaseItems) Field3DeepEqual(src string) bool {

	if strings.Compare(p.CodebaseURL, src) != 0 {
		return false
	}
	return true
}
func (p *CodebaseItems) Field4DeepEqual(src string) bool {

	if strings.Compare(p.CodebaseBranchName, src) != 0 {
		return false
	}
	return true
}
func (p *CodebaseItems) Field5DeepEqual(src string) bool {

	if strings.Compare(p.CodebaseID, src) != 0 {
		return false
	}
	return true
}
func (p *CodebaseItems) Field6DeepEqual(src HomePageItemType) bool {

	if p.HomePageItemType != src {
		return false
	}
	return true
}
func (p *CodebaseItems) Field7DeepEqual(src string) bool {

	if strings.Compare(p.Query, src) != 0 {
		return false
	}
	return true
}
func (p *CodebaseItems) Field8DeepEqual(src string) bool {

	if strings.Compare(p.IconURL, src) != 0 {
		return false
	}
	return true
}
func (p *CodebaseItems) Field9DeepEqual(src bool) bool {

	if p.UseCache != src {
		return false
	}
	return true
}
func (p *CodebaseItems) Field10DeepEqual(src string) bool {

	if strings.Compare(p.Answer, src) != 0 {
		return false
	}
	return true
}

type ArtifactsItems struct {
	ArtifactsImageURL         string           `thrift:"ArtifactsImageURL,1" frugal:"1,default,string" json:"ArtifactsImageURL"`
	ArtifactsImageDescription string           `thrift:"ArtifactsImageDescription,2" frugal:"2,default,string" json:"ArtifactsImageDescription"`
	ArtifactsID               string           `thrift:"ArtifactsID,3" frugal:"3,default,string" json:"ArtifactsID"`
	HomePageItemType          HomePageItemType `thrift:"HomePageItemType,5" frugal:"5,default,HomePageItemType" json:"HomePageItemType"`
	UseCache                  bool             `thrift:"UseCache,4" frugal:"4,default,bool" json:"UseCache"`
}

func NewArtifactsItems() *ArtifactsItems {
	return &ArtifactsItems{}
}

func (p *ArtifactsItems) InitDefault() {
}

func (p *ArtifactsItems) GetArtifactsImageURL() (v string) {
	return p.ArtifactsImageURL
}

func (p *ArtifactsItems) GetArtifactsImageDescription() (v string) {
	return p.ArtifactsImageDescription
}

func (p *ArtifactsItems) GetArtifactsID() (v string) {
	return p.ArtifactsID
}

func (p *ArtifactsItems) GetHomePageItemType() (v HomePageItemType) {
	return p.HomePageItemType
}

func (p *ArtifactsItems) GetUseCache() (v bool) {
	return p.UseCache
}
func (p *ArtifactsItems) SetArtifactsImageURL(val string) {
	p.ArtifactsImageURL = val
}
func (p *ArtifactsItems) SetArtifactsImageDescription(val string) {
	p.ArtifactsImageDescription = val
}
func (p *ArtifactsItems) SetArtifactsID(val string) {
	p.ArtifactsID = val
}
func (p *ArtifactsItems) SetHomePageItemType(val HomePageItemType) {
	p.HomePageItemType = val
}
func (p *ArtifactsItems) SetUseCache(val bool) {
	p.UseCache = val
}

var fieldIDToName_ArtifactsItems = map[int16]string{
	1: "ArtifactsImageURL",
	2: "ArtifactsImageDescription",
	3: "ArtifactsID",
	5: "HomePageItemType",
	4: "UseCache",
}

func (p *ArtifactsItems) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ArtifactsItems[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ArtifactsItems) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ArtifactsImageURL = _field
	return nil
}
func (p *ArtifactsItems) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ArtifactsImageDescription = _field
	return nil
}
func (p *ArtifactsItems) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ArtifactsID = _field
	return nil
}
func (p *ArtifactsItems) ReadField5(iprot thrift.TProtocol) error {

	var _field HomePageItemType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = HomePageItemType(v)
	}
	p.HomePageItemType = _field
	return nil
}
func (p *ArtifactsItems) ReadField4(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UseCache = _field
	return nil
}

func (p *ArtifactsItems) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ArtifactsItems"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ArtifactsItems) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ArtifactsImageURL", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ArtifactsImageURL); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ArtifactsItems) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ArtifactsImageDescription", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ArtifactsImageDescription); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ArtifactsItems) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ArtifactsID", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ArtifactsID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *ArtifactsItems) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("HomePageItemType", thrift.I32, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.HomePageItemType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *ArtifactsItems) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("UseCache", thrift.BOOL, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.UseCache); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ArtifactsItems) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ArtifactsItems(%+v)", *p)

}

func (p *ArtifactsItems) DeepEqual(ano *ArtifactsItems) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ArtifactsImageURL) {
		return false
	}
	if !p.Field2DeepEqual(ano.ArtifactsImageDescription) {
		return false
	}
	if !p.Field3DeepEqual(ano.ArtifactsID) {
		return false
	}
	if !p.Field5DeepEqual(ano.HomePageItemType) {
		return false
	}
	if !p.Field4DeepEqual(ano.UseCache) {
		return false
	}
	return true
}

func (p *ArtifactsItems) Field1DeepEqual(src string) bool {

	if strings.Compare(p.ArtifactsImageURL, src) != 0 {
		return false
	}
	return true
}
func (p *ArtifactsItems) Field2DeepEqual(src string) bool {

	if strings.Compare(p.ArtifactsImageDescription, src) != 0 {
		return false
	}
	return true
}
func (p *ArtifactsItems) Field3DeepEqual(src string) bool {

	if strings.Compare(p.ArtifactsID, src) != 0 {
		return false
	}
	return true
}
func (p *ArtifactsItems) Field5DeepEqual(src HomePageItemType) bool {

	if p.HomePageItemType != src {
		return false
	}
	return true
}
func (p *ArtifactsItems) Field4DeepEqual(src bool) bool {

	if p.UseCache != src {
		return false
	}
	return true
}

type CodebaseTemplates struct {
	CodebaseItems []*CodebaseItems `thrift:"CodebaseItems,1" frugal:"1,default,list<CodebaseItems>" json:"CodebaseItems"`
}

func NewCodebaseTemplates() *CodebaseTemplates {
	return &CodebaseTemplates{}
}

func (p *CodebaseTemplates) InitDefault() {
}

func (p *CodebaseTemplates) GetCodebaseItems() (v []*CodebaseItems) {
	return p.CodebaseItems
}
func (p *CodebaseTemplates) SetCodebaseItems(val []*CodebaseItems) {
	p.CodebaseItems = val
}

var fieldIDToName_CodebaseTemplates = map[int16]string{
	1: "CodebaseItems",
}

func (p *CodebaseTemplates) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CodebaseTemplates[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *CodebaseTemplates) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*CodebaseItems, 0, size)
	values := make([]CodebaseItems, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.CodebaseItems = _field
	return nil
}

func (p *CodebaseTemplates) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("CodebaseTemplates"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CodebaseTemplates) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CodebaseItems", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.CodebaseItems)); err != nil {
		return err
	}
	for _, v := range p.CodebaseItems {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CodebaseTemplates) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CodebaseTemplates(%+v)", *p)

}

func (p *CodebaseTemplates) DeepEqual(ano *CodebaseTemplates) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.CodebaseItems) {
		return false
	}
	return true
}

func (p *CodebaseTemplates) Field1DeepEqual(src []*CodebaseItems) bool {

	if len(p.CodebaseItems) != len(src) {
		return false
	}
	for i, v := range p.CodebaseItems {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type ArtifactsTemplates struct {
	ArtifactsItems []*ArtifactsItems `thrift:"ArtifactsItems,1" frugal:"1,default,list<ArtifactsItems>" json:"ArtifactsItems"`
}

func NewArtifactsTemplates() *ArtifactsTemplates {
	return &ArtifactsTemplates{}
}

func (p *ArtifactsTemplates) InitDefault() {
}

func (p *ArtifactsTemplates) GetArtifactsItems() (v []*ArtifactsItems) {
	return p.ArtifactsItems
}
func (p *ArtifactsTemplates) SetArtifactsItems(val []*ArtifactsItems) {
	p.ArtifactsItems = val
}

var fieldIDToName_ArtifactsTemplates = map[int16]string{
	1: "ArtifactsItems",
}

func (p *ArtifactsTemplates) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ArtifactsTemplates[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ArtifactsTemplates) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*ArtifactsItems, 0, size)
	values := make([]ArtifactsItems, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ArtifactsItems = _field
	return nil
}

func (p *ArtifactsTemplates) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ArtifactsTemplates"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ArtifactsTemplates) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ArtifactsItems", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ArtifactsItems)); err != nil {
		return err
	}
	for _, v := range p.ArtifactsItems {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ArtifactsTemplates) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ArtifactsTemplates(%+v)", *p)

}

func (p *ArtifactsTemplates) DeepEqual(ano *ArtifactsTemplates) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ArtifactsItems) {
		return false
	}
	return true
}

func (p *ArtifactsTemplates) Field1DeepEqual(src []*ArtifactsItems) bool {

	if len(p.ArtifactsItems) != len(src) {
		return false
	}
	for i, v := range p.ArtifactsItems {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type HomePageResponse struct {
	HomePageContent       string         `thrift:"HomePageContent,1" frugal:"1,default,string" json:"HomePageContent"`
	PromptTemplateContent string         `thrift:"PromptTemplateContent,2" frugal:"2,default,string" json:"PromptTemplateContent"`
	BaseResp              *base.BaseResp `thrift:"BaseResp,255,optional" frugal:"255,optional,base.BaseResp" json:"BaseResp,omitempty"`
}

func NewHomePageResponse() *HomePageResponse {
	return &HomePageResponse{}
}

func (p *HomePageResponse) InitDefault() {
}

func (p *HomePageResponse) GetHomePageContent() (v string) {
	return p.HomePageContent
}

func (p *HomePageResponse) GetPromptTemplateContent() (v string) {
	return p.PromptTemplateContent
}

var HomePageResponse_BaseResp_DEFAULT *base.BaseResp

func (p *HomePageResponse) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return HomePageResponse_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *HomePageResponse) SetHomePageContent(val string) {
	p.HomePageContent = val
}
func (p *HomePageResponse) SetPromptTemplateContent(val string) {
	p.PromptTemplateContent = val
}
func (p *HomePageResponse) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_HomePageResponse = map[int16]string{
	1:   "HomePageContent",
	2:   "PromptTemplateContent",
	255: "BaseResp",
}

func (p *HomePageResponse) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *HomePageResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_HomePageResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *HomePageResponse) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.HomePageContent = _field
	return nil
}
func (p *HomePageResponse) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.PromptTemplateContent = _field
	return nil
}
func (p *HomePageResponse) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *HomePageResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("HomePageResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *HomePageResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("HomePageContent", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.HomePageContent); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *HomePageResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("PromptTemplateContent", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.PromptTemplateContent); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *HomePageResponse) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBaseResp() {
		if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.BaseResp.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *HomePageResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("HomePageResponse(%+v)", *p)

}

func (p *HomePageResponse) DeepEqual(ano *HomePageResponse) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.HomePageContent) {
		return false
	}
	if !p.Field2DeepEqual(ano.PromptTemplateContent) {
		return false
	}
	if !p.Field255DeepEqual(ano.BaseResp) {
		return false
	}
	return true
}

func (p *HomePageResponse) Field1DeepEqual(src string) bool {

	if strings.Compare(p.HomePageContent, src) != 0 {
		return false
	}
	return true
}
func (p *HomePageResponse) Field2DeepEqual(src string) bool {

	if strings.Compare(p.PromptTemplateContent, src) != 0 {
		return false
	}
	return true
}
func (p *HomePageResponse) Field255DeepEqual(src *base.BaseResp) bool {

	if !p.BaseResp.DeepEqual(src) {
		return false
	}
	return true
}
