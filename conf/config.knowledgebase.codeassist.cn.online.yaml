TCC:
  PSM: flow.codeassist.knowledgebase_config
DB:
  PSM: toutiao.mysql.codeassist_knowledgebase
  DBName: codeassist_knowledgebase
Abase:
  - Name: "metadata_abase_table"
    PSM: "bytedance.abase2.codeassist_knowledgebase"
    Table: "metadata"
    ReadTimeout: "10s"
    DialTimeout: "10s"
    WriteTimeout: "30s"
  - Name: "vector_cache_abase_table"
    PSM: "bytedance.abase2.codeassist_knowledgebase"
    Table: "vector_cache"
    ReadTimeout: "10s"
    DialTimeout: "10s"
    WriteTimeout: "30s"
ByteDoc:
  URI: "mongodb+consul+token://bytedance.bytedoc.codeassist_knowledgebase/codeassist_knowledgebase?connectTimeoutMS=2000"
  Database: "codeassist_knowledgebase"
Splitter:
  PSM: "flow.ide.splitter"
  Cluster: "faas-codeassist"
EmbeddingModels:
  - Name: doubao_ckg_bge_m3
    BatchSize: 32
    Endpoint: "smart_infra.doubao_repo.bge_m3_online.service.lf"
    Vendor: codekg
  - Name: doubao_ckg_bge_m3
    BatchSize: 32
    Endpoint: "smart_infra.doubao_repo.bge_m3_offline.service.lf"
    Offline: true
    Vendor: codekg
  - Name: text-embedding-ada-002
    Endpoint: "https://search.bytedance.net/gpt/openapi/online/v2/crawl"
    Vendor: azure
  - Name: ckg_embedding_OASIS_code
    BatchSize: 16
    Endpoint: "smart_infra.doubao_repo.online_embedding_oasis.service.hl"
    Vendor: codekg
  - Name: ckg_embedding_OASIS_code
    BatchSize: 16
    Endpoint: "smart_infra.doubao_repo.embedding_oasis"
    Offline: true
    Vendor: codekg
Redis:
  PSM: "toutiao.redis.codeassist_knowledgebase"
  DialTimeout: 300000
  ReadTimeout: 300000
  WriteTimeout: 300000
  AutoLoadConf: true
Datastores:
  - Name: "default-latest"
    DatastoreVersion: "v1"
    Type: "viking"
    Config:
      EmbeddingModelName: "doubao_ckg_bge_m3"
      Viking:
        DSN: "viking://devgpt_1730895377__codeassist_knowledgebase:5ba0b754e6becac4c51642a95240536a@/?region=CN"
        Index: "v02"
        Version: "v02"
        ModelName: "dim1024_nobias"
      Abase:
        Name: "vikingindexer_abase_table"
        PSM: "bytedance.abase2.codeassist_knowledgebase"
        Table: "vikingindexer"
        ReadTimeout: "10s"
        DialTimeout: "10s"
        WriteTimeout: "30s"
  - Name: "oasis-embedding-latest"
    DatastoreVersion: "v1"
    Type: "viking"
    Config:
      EmbeddingModelName: "ckg_embedding_OASIS_code"
      Viking:
        DSN: "viking://devgpt_1744880488__codeassist_knowledgebase_oasis:32105a87aeb2c425a2eb3d084ff14622@/?region=CN"
        Index: "v01"
        Version: "v01"
        ModelName: "dim1536_nobias"
      Abase:
        Name: "vikingindexer_abase_table"
        PSM: "bytedance.abase2.codeassist_knowledgebase"
        Table: "vikingindexer_oasis"
        ReadTimeout: "10s"
        DialTimeout: "10s"
        WriteTimeout: "30s"
EventCollector:
  Enabled: false
Codebase:
  Enabled: false
EnableLatest:
  Enabled: true
  EnableFornax: true
  OfflineDatasetID: 5000000
Patrol:
  DisableRecycleDatasets: true
  PruneNoReferenceSegmentTable: "codebase.dwd_codeassist_kb_no_reference_segment"
  PatrolIndexerSegmentTable: "codebase.dwd_codeassist_kb_indexer_patrol_segment"
  PruneEmbeddingIndexerSegmentTable: "codebase.dwd_codeassist_kb_embeddingindex_prune_vectorset"
  PruneEmbeddingIndexerVectorTable: "codebase.dwd_codeassist_kb_embeddingindex_prune_vector"
  PruneEmbeddingIndexerVikingTable: "codebase.dwd_codeassist_kb_embeddingindex_prune_viking"
