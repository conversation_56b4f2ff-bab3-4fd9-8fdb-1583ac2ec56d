DBConfig:
  PSM: toutiao.mysql.devai
  DBName: devai
  LogLevel: info

TCCConfig:
  PSM: flow.devai.assistant

AuthConfig:
  EnabledAuthOrigins: [ "byte_cloud" ]
  DisabledAuthenticationMethods:
    - "Ping"
    - "SharePreviewCallback"
    - "LarkAuth"

KnowledgeServiceConfig:
  PSM: "flow.devai.knowledge"
  Cluster: ""

LaplaceRankConfig:
  LarkRanker:
    PSM: "lark.ai.sentence_embedding_bernard"
    Cluster: "lark_ranker"
    IDC: ""
    ModelName: "lark.question_answering.lark_ranker"

  FlowRanker:
    PSM: "smart_infra.rc.rerank"
    Cluster: "flow_ranker"
    IDC: "hl"
    ModelName: "flow.xmemory.flow_ranker"


EmbeddingConfig:
  APIBaseURL: "https://bits-ai-online-embedding.byted.org/m3e"

LLMOpsConfig:
  PromptHubPSM: "kiwis.llminfra.foundation_prompt"

LarkRedirectConfig:
  RedirectURI: "https://bitsai.bytedance.net/api/assistant/v1/lark/auth"

RedisConfig:
  PSM: "toutiao.redis.devai"
  DialTimeout: 500
  ReadTimeout: 500
  WriteTimeout: 500

NextCodeConfig:
  BaseURL: "https://codebase-api.byted.org/v2/"

Kani:
  Namespace: kani_7216
  BaseURL: "https://kani-v2-openapi-cn.byted.org/v1"
  ProtegoBaseURL: "https://protego-api.byted.org"
  WorkflowBaseURL: "https://kani.bytedance.net/approval/workflow"
  Location: "r:cn"