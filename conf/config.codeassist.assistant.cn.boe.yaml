DBConfig:
  PSM: toutiao.mysql.codeassist
  DBName: codeassist
  LogLevel: info
  WithReadReplicas: true
  ClientFoundRows: false # important! use db as lock in some case, this must be false

TCCConfig:
  PSM: "flow.codeassist.assistant"

DoubaoTCCConfig:
  PSM: ocean.cloud.alice

KnowledgeBaseConfig:
  PSM: "flow.codeassist.knowledgebase"

LLMOpsConfig:
  PromptHubPSM: "kiwis.llminfra.foundation_prompt"

ContextConfig:
  FileFetchSizeLimit: 2097152

ContextEventBusConfig:
  EventName: "marscode.codeassist.context.consume"
  ConsumerGroup: "codeassist_context_consumer"
  ConsumerPSM: "flow.codeassist.context_consumer"

DoubaoStreamClientConfig:
  PSM: "flow.alice.resource"
  StreamWindowSize: 268435456

EventStreamClientConfig:
  PSM: "flowpc.chat.eventstream"

JobSchedulerConfig:
  PSM: "flowpc.job.scheduler"

Redis:
  PSM: "toutiao.redis.codeassist_knowledgebase"
  DialTimeout: 3000
  ReadTimeout: 3000
  WriteTimeout: 3000

MetricsEventMQConfig:
  Cluster: "test_common4"
  Topic: "codeassist_event"

SandboxConfig:
  BaseURL: "https://code-sandbox.byted.org/api/v1/sandbox/"
  TransportDomain: "code-sandbox.byted.org:443"

CodeAssistAgentConfig:
  MessageAbaseConfig:
    PSM: "bytedance.abase2.codeassist_agent"
    Table: "message"
  ToolAbaseConfig:
    PSM: "bytedance.abase2.codeassist_agent"
    Table: "tool_observation"

DKMSConfig:
  SecurityKey: "flow.doubao.codeassist"
