TCCConfig:
  PSM: flow.agentsphere.config

DBConfig:
  PSM: toutiao.mysql.codeagent
  DBName: codeagent

AuthConfig:
  EnabledAuthOrigins: [ "codebase", "byte_cloud" ]
  EnableSignCodebaseJWT: true
  SignCodebaseJWTScopes: [ "repos.contents:fetch", "repos.contents:push" ]
  SignNextCodeJWTScopes: [ "repo:read", "repo:list","repo:write" ]
  IncludeCodebaseUserDetails: true

CodebaseConfig:
  APIBaseURL: "https://codebase-api.byted.org"
  V1APIBaseURL: "https://code.byted.org/_"

MQConfig:
  RuntimeOrchestrator:
    Cluster: "web_common4"
    Topic: "codeagent"
    ConsumerGroup: "runtime_orchestrator"
    SwimlaneV2: true
  AssignmentMonitor:
    Cluster: "web_common4"
    Topic: "codeagent"
    ConsumerGroup: "assignment_monitor"
    SwimlaneV2: true
  NextRuntimeOrchestrator:
    Cluster: "web_common4"
    Topic: "codeagent"
    ConsumerGroup: "next_runtime_orchestrator"
    SwimlaneV2: true
  NextSessionMonitor:
    Cluster: "web_common4"
    Topic: "aime_monitor"
    ConsumerGroup: "next_session_monitor"
    SwimlaneV2: true
  Knowledgebase:
    Cluster: "web_common4"
    Topic: "aime_monitor"
    ConsumerGroup: "knowledgebase"
    SwimlaneV2: true
    WorkerCount: 2

RedisConfig:
  PSM: toutiao.redis.codeagent
  DialTimeout: 500
  ReadTimeout: 2000
  WriteTimeout: 1000
  AutoLoadConf: true

RuntimeAPIConfig:
  APIBaseURL: "https://aime.bytedance.net"
  APIPrefix: "/api/agents/v2"
KnowledgebaseConfig:
  ElasticSearchPSM: "byte.es.agent_knowledgebase.service.lf"
  ElasticSearchCluster: "data"
  VikingName: "devgpt_1750318656__agentsphere_knowledgebase"
  ElasticSearchRecallIndex: "agentsphere_knowledgebase"
