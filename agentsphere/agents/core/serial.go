package agents

import (
	"context"
	"fmt"
	"reflect"
	"time"

	"github.com/pkg/errors"
	"github.com/samber/lo"

	controltool "code.byted.org/devgpt/kiwis/agentsphere/agents/actions/control"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris/agentrace"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris/telemetry"
	"code.byted.org/devgpt/kiwis/lib/metrics"
	"code.byted.org/devgpt/kiwis/lib/util"
)

type SerialStage struct {
	// If true then this stage will be executed, defaults to DefaultCanRunStage
	If func(*iris.AgentRunContext) bool

	// used to do initialization & post step cleanup etc.
	// can be orchestrated at any place and should not create any step.
	// NOTE: if a stage has both Run and Agent, run will be called after each NextStep.
	Run func(*iris.AgentRunContext) error
	// Action and Agent are mutually exclusive
	// Action directly gives a thought and action to execute
	Action func(*iris.AgentRunContext) (*iris.Thought, iris.Action, error)
	Agent  func(*iris.AgentRunContext) iris.Agent
}

type SerialAgentStore struct {
	StageIdx int `json:"stage_idx"`
}

// SerialAgent is a helper struct
// action inputs are always outputs of the previous step
// each step can be an action or a nested agent
type SerialAgent struct {
	iris.AgentInfo
	Stages             []SerialStage
	ToolFormat         func(action iris.Action, inputs map[string]any) string
	Summarizer         func(run *iris.AgentRunContext, step *iris.AgentRunStep) string
	ReportErrorMessage bool

	agents map[int]iris.Agent
}

// SerialAgent can be used as child agent in an orchestration
// or as the outermost agent that implements `Run`
var _ iris.Agent = &SerialAgent{}
var _ iris.RunnableAgent = &SerialAgent{}

type SerialAgentOption func(*SerialAgent)

func NewSerialAgent(info iris.AgentInfo, opt ...SerialAgentOption) *SerialAgent {
	agent := &SerialAgent{
		AgentInfo: info,
		Stages:    []SerialStage{},
		agents:    make(map[int]iris.Agent),
	}
	for _, o := range opt {
		o(agent)
	}
	return agent
}

func WithReportErrorMessage() SerialAgentOption {
	return func(o *SerialAgent) {
		o.ReportErrorMessage = true
	}
}

func WithSerialToolMessageFormat(format func(action iris.Action, inputs map[string]any) string) SerialAgentOption {
	return func(o *SerialAgent) {
		o.ToolFormat = format
	}
}

func WithSerialSummarizer(summarizer func(run *iris.AgentRunContext, step *iris.AgentRunStep) string) SerialAgentOption {
	return func(o *SerialAgent) {
		o.Summarizer = summarizer
	}
}

func WithSerialStages(stages []SerialStage) SerialAgentOption {
	return func(o *SerialAgent) {
		o.Stages = lo.Map(stages, func(s SerialStage, i int) SerialStage {
			if s.If == nil {
				s.If = DefaultCanRunStage
			}
			return s
		})
	}
}

func (a *SerialAgent) NextStep(ctx context.Context, run *iris.AgentRunContext) (step *iris.AgentRunStep, err error) {
	store := iris.RetrieveStoreByKey[SerialAgentStore](run, a.Name())
	defer func() {
		iris.UpdateStoreByKey(run, a.Name(), store)
	}()

	return a.nextStep(ctx, run, &store)
}

func (a *SerialAgent) nextStep(ctx context.Context, run *iris.AgentRunContext, store *SerialAgentStore) (opt *iris.AgentRunStep, err error) {
	logger := run.GetLogger()
	// a nextStep call may advance store.StageIdx up to len(a.Stages) until it returns a step
	for ; store.StageIdx < len(a.Stages); store.StageIdx++ {
		lastStep := run.State.LastStep()
		stage := a.Stages[store.StageIdx]

		// there might be stages to run even if agent run is completed (e.g. to cleanup resources)
		skip := stage.If != nil && !stage.If(run)
		if skip {
			logger.Debugf("agent run status: %s, last step: %v", run.State.Status, lastStep)
			logger.Infof("skipped stage %d", store.StageIdx)
			continue
		}

		var (
			step *iris.AgentRunStep
			err  error
		)

		// child agent returns:
		// 1. unwraps completion: child agent completed(no matter with/without error), go to next stage
		// 1. err != nil:
		//   - err is recoverable: return to parent, keep stage idx, so next round will be handled by the same agent
		//   - err is not recoverable: child agent finished with error, go to next stage
		// 2. step == nil: child agent finished, stage idx++ and go to next stage
		// 3. step != nil: child agent is running, keep stage idx inside the child agent
		if stage.Agent != nil {
			if a.agents[store.StageIdx] == nil {
				a.agents[store.StageIdx] = stage.Agent(run)
			}
			step, err = a.agents[store.StageIdx].NextStep(ctx, run)
		}

		// we expect stage.Agent != nil || stage.Action != nil
		// if both are nil, go to next stage
		if stage.Action != nil {
			thought, action, err := stage.Action(run)
			if thought == nil && action == nil {
				continue
			}
			// execute this step
			step = run.CreateStep(nil)
			step.Action = action
			step.Error = err
			if thought != nil {
				run.UpdateThought(thought)
				step.Inputs = thought.Parameters
			} else if lastStep != nil {
				step.Inputs = lastStep.Outputs
			} else {
				step.Message = util.First(run.State.Conversation.GetMessages())
			}
			store.StageIdx++
		}

		// runs the stage, which will not create any new steps
		if stage.Run != nil {
			err = stage.Run(run)
			if err != nil && iris.IsRecoverable(err) {
				err = nil
			}
		}
		// if Run is used without Agent or Action, go to next stage
		if stage.Run != nil && (stage.Agent == nil && stage.Action == nil) {
			store.StageIdx++
		}
		// if Agent returned no step, go to next stage
		if stage.Agent != nil && step == nil && err == nil {
			continue
		}

		if err != nil && iris.IsCompleted(err) {
			err = errors.Unwrap(err)
			store.StageIdx++
		}

		return step, err
	}

	lastStep := run.State.LastStep()
	if lastStep != nil && lastStep.Error != nil {
		if a.ReportErrorMessage {
			run.GetPublisher().ReportMessage(iris.EventAgentMessage{
				Content: fmt.Sprintf("Failed to process task: %s", lastStep.Error),
			})
		}
		return nil, lastStep.Error
	}
	return nil, iris.NewCompleted(nil)
}

type RateRecord struct {
	SucceedCnt int
	FailedCnt  int
}

func (a *SerialAgent) Run(run *iris.AgentRunContext) (err error) {
	store := iris.RetrieveStoreByKey[SerialAgentStore](run, a.Name())
	store.StageIdx = 0
	iris.UpdateStoreByKey(run, a.Name(), store)

	publisher, logger := run.GetPublisher(), run.GetLogger()
	if a.agents == nil {
		a.agents = make(map[int]iris.Agent)
	}

	actionRecords := map[string]RateRecord{}
	defer func() {
		logger.Infof("action succeed rate: %+v", actionRecords)
	}()

	for {
		if run.Err() != nil {
			return iris.ErrCanceled
		}
		if run.State.Status.IsStopped() {
			return nil
		}

		span, ctx := agentrace.GetRuntimeTracerFromContext(run).
			StartCustomSpan(
				run,
				agentrace.SpanTypeStep,
				"serial_agent_run_step",
			)
		subRun := run.WithContext(ctx)

		run.State.CurrentStep = nil
		step, err := a.NextStep(ctx, subRun)
		if step == nil && run.State.CurrentStep != nil {
			logger.Warnf("agent should return created step %s, but not returned", run.State.CurrentStep.StepID)
			step = run.State.CurrentStep
		}
		logger.Debugf("next step: %+v, err: %+v", step, err)
		// new step is created, we need to report its status
		if step != nil {
			switch true {
			case err != nil && iris.IsRecoverable(err):
				step.Status = iris.AgentRunStepStatusError
			case err != nil:
				step.Status = iris.AgentRunStepStatusFailed
			default:
				step.Status = iris.AgentRunStepStatusRunning
			}
			step.Error = err
			run.State.Steps = append(run.State.Steps, step)
			publisher.ReportStep(step)
		}
		// then check if there is any unrecoverable error
		if err != nil {
			agentrace.AddErrorTag(span, err)
			span.Finish()
			if iris.IsRecoverable(err) {
				logger.Warnf("agent reported recoverable error during think, skip execute action: %v", err)
				continue
			}
			if iris.IsCompleted(err) {
				err = errors.Unwrap(err)
			}
			return err
		}
		if step == nil {
			span.Finish()
			continue
		}

		// execute action given by agent
		var toolCallCost time.Duration
		if step.Action != nil && step.Status == iris.AgentRunStepStatusRunning {
			toolCallSpan, toolCallCtx := agentrace.GetRuntimeTracerFromContext(subRun).
				StartCustomSpan(
					subRun,
					agentrace.SpanTypeToolCall,
					step.Action.Name(),
					agentrace.WithObjectSpanData(map[string]any{
						"name":   step.Action.Name(),
						"inputs": step.Inputs,
					}),
				)
			toolCallRun := subRun.WithContext(toolCallCtx)
			summary := a.FormatToolMessage(toolCallRun, step) // summarize the tool call description only once
			publisher.ReportToolCall(step, iris.ToolCallStatusStarted, summary)

			startTime := time.Now()
			err = step.Action.Execute(toolCallRun, step)
			toolCallCost = time.Since(startTime)

			agentrace.AddErrorTag(toolCallSpan, err)
			toolCallData := map[string]any{
				"outputs": step.Outputs,
			}
			if err != nil {
				toolCallData["error"] = err
			}
			toolCallSpan.UpdateData(agentrace.NewObjectSpanData(toolCallData))
			toolCallSpan.Finish()

			if step.Action.Name() == controltool.ToolConclusion {
				step.Conclusion = step.Outputs
			}

			publisher.ReportToolCall(step, iris.ToolCallStatusCompleted, summary)
		}
		// if action is not recoverable, we need to stop the agent
		logger.Debugf("step %s finished(status: %s), err: %+v, outputs: %+v", step.StepID, step.Status, step.Error, step.Outputs)
		if err != nil {
			step.Error = err
			step.Status = lo.Ternary(iris.IsRecoverable(err), iris.AgentRunStepStatusError, iris.AgentRunStepStatusFailed)
		} else {
			step.Status = iris.AgentRunStepStatusSuccess
		}
		if step.Action != nil {
			// report tool call metrics
			_ = metrics.AR.ToolCallThroughput.WithTags(&metrics.ToolCallTag{
				Tool:   step.Action.Name(),
				Status: string(step.Status),
			}).Add(1)
			_ = metrics.AR.ToolCallCost.WithTags(&metrics.ToolCallTag{
				Tool:   step.Action.Name(),
				Status: string(step.Status),
			}).Observe(float64(toolCallCost.Milliseconds()))
			telemetry.EmitToolUsage(run, step.Action.Name(), string(step.Status), toolCallCost)
		}
		publisher.ReportStep(step)
		span.Finish()
	}
}

func (a *SerialAgent) FormatToolMessage(run *iris.AgentRunContext, step *iris.AgentRunStep) (message string) {
	if step.Action == nil {
		return ""
	}
	if a.Summarizer != nil {
		run.GetLogger().Infof("[debug] FormatToolMessage. Step %+v", step)
		summary := a.Summarizer(run, step)
		if summary != "" {
			return summary
		}
	}
	if a.ToolFormat != nil {
		message = a.ToolFormat(step.Action, step.Inputs)
	}
	if message != "" {
		return message
	}
	return fmt.Sprintf("Using tool `%s`...", step.Action.Name())
}

func isEmptyArray(v any) bool {
	value := reflect.ValueOf(v)

	if value.Kind() == reflect.Array || value.Kind() == reflect.Slice {
		return value.Len() == 0
	}
	return false
}
