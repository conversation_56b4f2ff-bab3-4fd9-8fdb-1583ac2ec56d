package prompt_test

import (
	_ "embed"
	"math/rand"
	"strconv"
	"testing"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/prompt"
	"github.com/stretchr/testify/require"
)

var (
	//go:embed testdata/repetition.md
	repetition string
	//go:embed testdata/repetition.html
	repetitionHTML string
)

func TestDetectRepetition(t *testing.T) {
	rand.Seed(42) // set seed to a fixed value for deterministic test
	tests := []struct {
		input string
		want  bool
	}{
		{
			input: "An n-gram is a sequence of n adjacent symbols in particular order.[1] The symbols may be n adjacent letters (including punctuation marks and blanks), syllables, or rarely whole words found in a language dataset; or adjacent phonemes extracted from a speech-recording dataset, or adjacent base pairs extracted from a genome. They are collected from a text corpus or speech corpus.",
			want:  false,
		},
		{
			input: "#Action (create file) ```pattern1 pattern2 pattern3 pattern4 pattern5 pattern6 pattern7 pattern8 pattern9```",
			want:  false,
		},
		{
			input: repetitionHTML,
			want:  false,
		},
		{
			// 中文
			input: "我是您的珠宝行业分析师 Aime。基于您提供的市场调研报告，我将为您详细分析如何以最高性价比购买培育钻石和定制钻戒。本报告将客观中立地分析各种渠道和方式的利弊，并提供切实可行的操作指南。",
			want:  false,
		},
		{
			// 英文
			input: "5.  **Data Loading**: Does the data seem to have loaded correctly into the charts? (e.g. charts are not showing \"No data available\" messages unless the underlying data is indeed empty). Please provide a summary of your findings, highlighting any issues.",
			want:  false,
		},
		// word repetition
		{
			input: "hello hello hello hello hello hello hello hello hello hello",
			want:  true,
		},
		// character repetition
		{
			input: "aaaaaaaaaaaaaaaaaaaaaa",
			want:  true,
		},
		{
			input: "abcd abcd abcd abcd abcd abcd abcd abcd abcd abcd",
			want:  true,
		},
		// incomplete repetition end
		{
			input: "rrrrr abcd abcd abcd abcd abcd abcd abcd abcd abcd abcd abcd abcd abcd abcd abc",
			want:  true,
		},
		{
			input: "MBERE_P_S_P_S_P_S_P_S_P_S_P_S_P_S_P_S_P_S_P_S_P_S_P_S_P_S_P_S_P_S_P_S_P_S_P_S_P_S_P_S_P_S_P_S_P_S_P_S_P_S_P_S_P_S_P_S_P_S_P",
			want:  true,
		},
		{
			input: repetition,
			want:  true,
		},
		{
			input: repetition[len(repetition)-1024:],
			want:  true,
		},
	}

	for idx, tt := range tests {
		t.Run(strconv.Itoa(idx), func(t *testing.T) {
			got := prompt.DetectRepetition(tt.input)
			require.Equal(t, tt.want, got, "input: %v", tt.input)
		})
	}
}
