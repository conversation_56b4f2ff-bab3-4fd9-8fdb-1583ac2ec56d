package actions

import (
	"bytes"
	"context"
	_ "embed"
	"encoding/json"
	"fmt"
	"reflect"

	"github.com/samber/lo"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/prompt"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/util"
	"code.byted.org/devgpt/kiwis/copilotstack/agent/framework"
)

type Tool struct {
	iris.ActionInfo
	impl        func(ctx *iris.AgentRunContext, state *iris.AgentRunStep) (map[string]any, error)
	observation func(inputs, outputs map[string]any, err error) string
	in          reflect.Type
	out         reflect.Type
}

// Execute implements iris.Action.
func (t *Tool) Execute(ctx *iris.AgentRunContext, step *iris.AgentRunStep) error {
	if t.impl == nil {
		return fmt.Errorf("tool %s is not implemented", t.Name())
	}

	outputs, err := t.impl(ctx, step)
	step.Finish(util.ValueToMap(outputs), err)
	return err
}

// Run implements framework.Action.
func (t *Tool) Run(ctx context.Context, input framework.ActionInput) (*framework.IO, error) {
	panic("unimplemented")
}

var _ iris.Action = &Tool{}

func (t Tool) InputSpec() framework.Schema {
	if t.ActionInfo.ActionInputSpec.Type != "" {
		return t.ActionInfo.ActionInputSpec
	}
	in := t.in
	if in != nil {
		return util.TypeToJSONSchema(in)
	}

	return framework.Schema{Type: framework.TypeNull}
}

func (t Tool) OutputSpec() framework.Schema {
	if t.ActionInfo.ActionOutputSpec.Type != "" {
		return t.ActionInfo.ActionOutputSpec
	}
	out := t.out
	if out != nil {
		return util.TypeToJSONSchema(out)
	}

	return framework.Schema{Type: framework.TypeNull}
}

type observation struct {
	Outputs map[string]any `json:"outputs" yaml:"outputs"`
	Error   string         `json:"error" yaml:"error"`
}

type observationPromptParams struct {
	Tool        string
	Observation observation
}

var (
	//go:embed prompts/observation.go.tmpl
	observationPrompt         string
	ObservationPromptTemplate = prompt.MustGetTemplate("observation_prompt", observationPrompt)
)

func (t Tool) Observation(inputs, outputs map[string]any, e error) string {
	if t.observation != nil {
		return t.observation(inputs, outputs, e)
	}
	buf := &bytes.Buffer{}
	err := ObservationPromptTemplate.Execute(buf, &observationPromptParams{
		Tool: t.Name(),
		Observation: observation{
			Outputs: outputs,
			Error:   lo.TernaryF(e != nil, func() string { return e.Error() }, func() string { return "none" }),
		},
	})
	if err != nil {
		return ""
	}
	return buf.String()
}

func (a *Tool) Serialize() ([]byte, error) {
	return json.Marshal(a)
}

func (a *Tool) Deserialize(data string) error {
	return json.Unmarshal([]byte(data), a)
}

type NewToolOption struct {
	Name        string
	Description string
	Input       framework.Schema
	Output      framework.Schema
	Impl        func(c *iris.AgentRunContext, step *iris.AgentRunStep) (map[string]any, error)
}

func NewTool(opt NewToolOption) *Tool {
	task := &Tool{
		ActionInfo: iris.ActionInfo{
			ActionName:        opt.Name,
			ActionDescription: opt.Description,
			ActionInputSpec:   opt.Input,
			ActionOutputSpec:  opt.Output,
		},
		impl: opt.Impl,
	}
	return task
}

// In and Out should tag with mapstructure tags
func ToTool[In any, Out any](name string, description string, impl func(c *iris.AgentRunContext, input In) (Out, error)) *Tool {
	var in In
	var out Out
	return &Tool{
		ActionInfo: iris.ActionInfo{
			ActionName:        name,
			ActionDescription: description,
		},
		impl: func(c *iris.AgentRunContext, step *iris.AgentRunStep) (map[string]any, error) {
			var in In
			jsonStr, err := json.Marshal(step.Inputs)
			if err != nil {
				return nil, iris.NewRecoverable(fmt.Errorf("failed to parse arguments: %w", err))
			}
			err = json.Unmarshal(jsonStr, &in)
			if err != nil {
				// models can output json fields with non-matching types, causing unmarshal failure
				return nil, iris.NewRecoverable(fmt.Errorf("failed to parse arguments: %w", err))
			}
			out, err := impl(c, in)

			// FIXME(lhj): mapstructure ignores nested slice and leave outputs of those fields PascalCase
			// mitchellh's mapstructure stops maintaining and go viper's mapstructure haven't merged the fix
			// we temporarily use json to encode outputs
			// SEE: https://github.com/mitchellh/mapstructure/issues/249
			// AND https://github.com/go-viper/mapstructure/issues/19
			outputs := make(map[string]any)
			data, _ := json.Marshal(out)
			unmarshalErr := json.Unmarshal(data, &outputs)
			if unmarshalErr != nil {
				c.GetLogger().Error("failed to unmarshal tool output", "error", unmarshalErr)
			}

			return outputs, err
		},
		in:  reflect.TypeOf(in),
		out: reflect.TypeOf(out),
	}
}

func WithPostRunHook(tool *Tool, hook func(c *iris.AgentRunContext, step *iris.AgentRunStep, output map[string]any) error) *Tool {
	if hook == nil {
		return tool
	}
	return &Tool{
		ActionInfo: tool.ActionInfo,
		impl: func(run *iris.AgentRunContext, step *iris.AgentRunStep) (map[string]any, error) {
			err := tool.Execute(run, step)
			if hook != nil {
				hErr := hook(run, step, step.Outputs)
				if hErr != nil {
					return nil, hErr
				}
			}

			return step.Outputs, err
		},
		observation: tool.observation,
		in:          tool.in,
		out:         tool.out,
	}
}

// ActionProxy 提供一个灵活的代理结构，可选择性地覆盖 iris.Action 接口的方法
type ActionProxy struct {
	iris.Action

	// override for iris.Action
	nameFunc        func() string
	descriptionFunc func() string
	// more overrides for iris.Action
}

// NewActionProxy 创建一个新的 ActionProxy
func NewActionProxy(underlying iris.Action) *ActionProxy {
	return &ActionProxy{
		Action: underlying,
	}
}

// WithName 覆盖 Name 方法
func (p *ActionProxy) WithName(nameFunc func() string) *ActionProxy {
	p.nameFunc = nameFunc
	return p
}

// WithDescription 覆盖 Description 方法
func (p *ActionProxy) WithDescription(descFunc func() string) *ActionProxy {
	p.descriptionFunc = descFunc
	return p
}

func (p *ActionProxy) Name() string {
	if p.nameFunc != nil {
		return p.nameFunc()
	}
	return p.Action.Name()
}

func (p *ActionProxy) Description() string {
	if p.descriptionFunc != nil {
		return p.descriptionFunc()
	}
	return p.Action.Description()
}
