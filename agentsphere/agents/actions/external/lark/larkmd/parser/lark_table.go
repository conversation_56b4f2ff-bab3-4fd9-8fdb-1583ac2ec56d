package parser

import (
	"bytes"
	"regexp"
	"strings"

	gast "github.com/yuin/goldmark/ast"
	"github.com/yuin/goldmark/extension/ast"
	"github.com/yuin/goldmark/parser"
	"github.com/yuin/goldmark/text"
	"github.com/yuin/goldmark/util"
)

// LarkTableParagraphTransformer is a custom table paragraph transformer for Lark Markdown
type LarkTableParagraphTransformer struct {
}

// NewLarkTableParagraphTransformer returns a new LarkTableParagraphTransformer
func NewLarkTableParagraphTransformer() parser.ParagraphTransformer {
	return &LarkTableParagraphTransformer{}
}

func isTableDelim(bs []byte) bool {
	if w, _ := util.IndentWidth(bs, 0); w > 3 {
		return false
	}
	for _, b := range bs {
		if !(util.IsSpace(b) || b == '-' || b == '|' || b == ':') {
			return false
		}
	}
	return true
}

var tableDelimLeft = regexp.MustCompile(`^\s*\:\-+\s*$`)
var tableDelimRight = regexp.MustCompile(`^\s*\-+\:\s*$`)
var tableDelimCenter = regexp.MustCompile(`^\s*\:\-+\:\s*$`)
var tableDelimNone = regexp.MustCompile(`^\s*\-+\s*$`)

func (b *LarkTableParagraphTransformer) Transform(node *gast.Paragraph, reader text.Reader, pc parser.Context) {
	lines := node.Lines()
	if lines.Len() < 2 {
		return
	}
	for i := 1; i < lines.Len(); i++ {
		alignments := b.parseDelimiter(lines.At(i), reader)
		if alignments == nil {
			continue
		}
		header := b.parseRow(lines.At(i-1), alignments, true, reader, pc)
		if header == nil || len(alignments) != header.ChildCount() {
			return
		}
		table := ast.NewTable()
		table.Alignments = alignments
		table.AppendChild(table, ast.NewTableHeader(header))
		for j := i + 1; j < lines.Len(); j++ {
			table.AppendChild(table, b.parseRow(lines.At(j), alignments, false, reader, pc))
		}
		node.Lines().SetSliced(0, i-1)
		node.Parent().InsertAfter(node.Parent(), node, table)
		if node.Lines().Len() == 0 {
			node.Parent().RemoveChild(node.Parent(), node)
		} else {
			last := node.Lines().At(i - 2)
			last.Stop = last.Stop - 1 // trim last newline(\n)
			node.Lines().Set(i-2, last)
		}
	}
}

// processCellContent processes cell content by converting <br> to newlines and unescaping |
func (b *LarkTableParagraphTransformer) processCellContent(content string) string {
	// Convert <br> to newlines
	content = strings.ReplaceAll(content, "<br>", "\n")
	content = strings.ReplaceAll(content, "<br/>", "\n")
	content = strings.ReplaceAll(content, "<br />", "\n")

	// Unescape | characters
	content = strings.ReplaceAll(content, "\\|", "|")

	// Handle other common escape sequences that might appear in Lark tables
	content = strings.ReplaceAll(content, "\\\\", "\\")

	// Special handling for single "-" to prevent it from being parsed as a list item
	if strings.TrimSpace(content) == "-" {
		// Add a zero-width space (U+200B) before the "-" to prevent list parsing
		content = "\u200B-"
	}

	return content
}

func (b *LarkTableParagraphTransformer) parseRow(segment text.Segment,
	alignments []ast.Alignment, isHeader bool, reader text.Reader, pc parser.Context) *ast.TableRow {
	source := reader.Source()
	segment = segment.TrimLeftSpace(source)
	segment = segment.TrimRightSpace(source)
	line := segment.Value(source)
	pos := 0
	limit := len(line)
	row := ast.NewTableRow(alignments)
	if len(line) > 0 && line[pos] == '|' {
		pos++
	}
	if len(line) > 0 && line[limit-1] == '|' {
		limit--
	}
	i := 0
	for ; pos < limit; i++ {
		alignment := ast.AlignNone
		if i >= len(alignments) {
			if !isHeader {
				return row
			}
		} else {
			alignment = alignments[i]
		}

		node := ast.NewTableCell()
		node.Alignment = alignment
		closure := pos
		cellContent := strings.Builder{}

		// Find the end of this cell (next unescaped |)
		for ; closure < limit; closure++ {
			if line[closure] == '|' {
				// Check if this | is escaped
				if closure == 0 || line[closure-1] != '\\' {
					break // Found unescaped |, end of cell
				}
			}
			cellContent.WriteByte(line[closure])
		}

		// Process cell content: handle <br> and escaped pipes
		cellContentStr := strings.TrimSpace(cellContent.String())
		processedContent := b.processCellContent(cellContentStr)

		// Store the processed content as an attribute for rendering
		node.SetAttributeString("processed_content", processedContent)

		// Create a simple text segment for the original cell content
		if pos < closure {
			cellStart := segment.Start + pos
			cellEnd := segment.Start + closure
			cellSegment := text.NewSegment(cellStart, cellEnd)
			node.Lines().Append(cellSegment)
		}

		row.AppendChild(row, node)
		pos = closure + 1
	}
	for ; i < len(alignments); i++ {
		emptyCell := ast.NewTableCell()
		emptyCell.Alignment = alignments[i]
		row.AppendChild(row, emptyCell)
	}
	return row
}

func (b *LarkTableParagraphTransformer) parseDelimiter(segment text.Segment, reader text.Reader) []ast.Alignment {
	line := segment.Value(reader.Source())
	if !isTableDelim(line) {
		return nil
	}
	cols := bytes.Split(line, []byte{'|'})
	if util.IsBlank(cols[0]) {
		cols = cols[1:]
	}
	if len(cols) > 0 && util.IsBlank(cols[len(cols)-1]) {
		cols = cols[:len(cols)-1]
	}

	var alignments []ast.Alignment
	for _, col := range cols {
		if tableDelimLeft.Match(col) {
			alignments = append(alignments, ast.AlignLeft)
		} else if tableDelimRight.Match(col) {
			alignments = append(alignments, ast.AlignRight)
		} else if tableDelimCenter.Match(col) {
			alignments = append(alignments, ast.AlignCenter)
		} else if tableDelimNone.Match(col) {
			alignments = append(alignments, ast.AlignNone)
		} else {
			return nil
		}
	}
	return alignments
}
