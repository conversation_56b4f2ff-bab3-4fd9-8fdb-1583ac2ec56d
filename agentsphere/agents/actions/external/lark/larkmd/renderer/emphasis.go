package renderer

import (
	"encoding/json"

	"github.com/yuin/goldmark/ast"
	"github.com/yuin/goldmark/renderer"
	"github.com/yuin/goldmark/renderer/html"
	"github.com/yuin/goldmark/util"

	"code.byted.org/devgpt/kiwis/port/lark"
)

// NewEmphasisHTMLRenderer returns a new TextRenderer for emphasis nodes
func NewEmphasisHTMLRenderer(opts ...html.Option) renderer.NodeRenderer {
	r := &TextRenderer{
		Config: html.NewConfig(),
	}
	for _, opt := range opts {
		opt.SetHTMLOption(&r.Config)
	}
	return r
}

// renderEmphasis renders an emphasis node (italic text)
func (r *TextRenderer) renderEmphasis(w util.BufWriter, source []byte, node ast.Node, entering bool) (ast.WalkStatus, error) {
	if entering {
		n := node.(*ast.Emphasis)

		// Generate text block with emphasis style
		textBlock := r.generateEmphasisTextBlock(n, source)
		blockID := generateBlockID()

		// Generate and render the text element as JSON
		jsonData, err := json.Marshal(&lark.DocxBlock{
			BlockID:   blockID,
			BlockType: lark.DocxBlockTypeText,
			Text:      textBlock,
		})
		if err == nil {
			w.Write(jsonData)
		}
		w.WriteString(",")
		node.SetAttributeString("block_id", blockID)

		// Skip further processing since we've handled all children
		return ast.WalkSkipChildren, nil
	}

	return ast.WalkContinue, nil
}

// generateEmphasisTextElement creates a DocxTextElement from an emphasis node
func (r *TextRenderer) generateEmphasisTextElement(n *ast.Emphasis, source []byte) []*lark.DocxTextElement {
	elements := []*lark.DocxTextElement{}
	for c := n.FirstChild(); c != nil; c = c.NextSibling() {
		elements = append(elements, r.NodeToTextElements(c, source)...)
	}
	for _, element := range elements {
		if element.TextRun != nil {
			if element.TextRun.TextElementStyle == nil {
				element.TextRun.TextElementStyle = &lark.DocxTextElementStyle{}
			}
			element.TextRun.TextElementStyle.Bold = true
		}
	}

	return elements
}

// generateEmphasisTextBlock creates a DocxBlockText from an emphasis node
func (r *TextRenderer) generateEmphasisTextBlock(n *ast.Emphasis, source []byte) *lark.DocxBlockText {
	textElements := r.generateEmphasisTextElement(n, source)

	// Create DocxBlockText with the emphasis element
	return &lark.DocxBlockText{
		Elements: textElements,
		Style:    &lark.DocxTextStyle{},
	}
}
