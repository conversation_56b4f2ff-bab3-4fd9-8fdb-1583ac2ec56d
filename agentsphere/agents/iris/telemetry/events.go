package telemetry

import (
	"fmt"
	"strings"
	"time"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
)

// Aime 事件埋点规范
// https://bytedance.larkoffice.com/docx/T5yfdMgCWo0tLSxvx2rcXcSmnwl?from=from_copylink
//
//  1. 所有事件都以 aime_ 开头
//  2. 新增事件需要到 ByteIO 平台注册，否则无法在 Tea 上查询
//     https://data.bytedance.net/byteio/event/schema?subAppId=739695
//  3. AgentCommonParam 是所有事件的公共参数，注册时需要添加
//  4. 事件参数仅有 integer, float, string, boolean 四种类型

const (
	// 适用于仅统计 PV/UV 的公共场景，没有额外参数，无需单独注册事件
	EventRuntimeOperation = "aime_runtime_operation"
	// 用户通过 Aime 创建了合并请求
	EventSubmitMergeRequest = "aime_runtime_submit_mr"
	// 某个工具被使用
	EventToolUsage = "aime_runtime_tool_usage"
	// 某个 actor 执行结束，统计耗时、持有的工具列表
	EventActorExecution = "aime_runtime_actor_execution"
)

func EmitRuntimeOperation(run *iris.AgentRunContext, operation string, duration time.Duration) {
	run.GetTelemetry().Collect(EventRuntimeOperation, map[string]any{
		"operation": operation,
		"duration":  duration.Milliseconds(),
	})
}

type MergeRequestType string

const (
	MergeRequestTypeCodebase MergeRequestType = "codebase"
	MergeRequestTypeBits     MergeRequestType = "bits"
)

func EmitSubmitMergeRequest(run *iris.AgentRunContext, repoName string, mrType MergeRequestType, mrID string) {
	run.GetTelemetry().Collect(EventSubmitMergeRequest, map[string]any{
		"repo_name": repoName,
		"mr_type":   mrType,
		"mr_id":     mrID,
	})
}

func EmitToolUsage(run *iris.AgentRunContext, tool string, status string, duration time.Duration) {
	run.GetTelemetry().Collect(EventToolUsage, map[string]any{
		"tool":     tool,
		"status":   status,
		"duration": duration.Milliseconds(),
	})
}

func EmitActorExecution(run *iris.AgentRunContext, actor string, toolsets []string, status string, duration time.Duration) {
	run.GetTelemetry().Collect(EventActorExecution, map[string]any{
		"actor_name": actor,
		"toolsets":   fmt.Sprintf(":%s:", strings.Join(toolsets, ":")), // 在前后加上额外的冒号，方便后续分析时使用 :toolset_name: 进行过滤并且与用户自定义的 mcp 工具进行区分
		"status":     status,
		"duration":   duration.Milliseconds(),
	})
}
