package telemetry

import (
	"context"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/devgpt/kiwis/port/eventcollector"
)

const (
	CALLER = "flow.agentsphere.runtime"
	APP_ID = 739695 // Aime
)

// "iris_session_id": string        session id
// "iris_cube_id": string           stratocube id
// "iris_host_env": string          TCE_HOST_ENV, boe, online, etc.
// "iris_env": string               TCE_ENV, ppe_xxx, boe_xxx, etc.
type AgentCommonParam map[string]string

type Client struct {
	cli    *eventcollector.HTTPClient
	logger iris.Logger
}

var _ iris.Telemetry = &Client{}

type NewCollectorOption struct {
	SessionID string
	User      string
	Environ   iris.RunEnviron
}

func NewCollector(logger iris.Logger, opt NewCollectorOption) *Client {
	cli := eventcollector.NewHTTPClient(eventcollector.NewHTTPCollectorOptions{
		UserID: opt.User,
		AppID:  APP_ID,
		BaseParams: map[string]any{
			"iris_session_id": opt.SessionID,
			"iris_cube_id":    opt.Environ.Map[entity.RuntimeEnvironCubeID],
			"iris_host_env":   opt.Environ.Map[entity.RuntimeTCEHostEnv],
			"iris_env":        opt.Environ.Map[entity.RuntimeTCEEnv],
			// TODO: wait the server to implement dispatching corresponding config to the runtime
			"iris_config_type":    "",
			"iris_config_version": "",
			"iris_experiment_id":  "",
			"iris_is_prod":        0,
		},
	})
	return &Client{
		cli: cli,
	}
}

func (t *Client) Collect(event string, params map[string]interface{}) {
	err := t.cli.Collect(context.Background(), event, params)
	if err != nil {
		t.logger.Errorf("failed to collect tea event, %s", err.Error())
	}
}
