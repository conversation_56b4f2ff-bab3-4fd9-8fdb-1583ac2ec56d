package iris

import (
	"encoding/json"
	"fmt"
	"strings"

	"github.com/samber/lo"

	"code.byted.org/devgpt/kiwis/agentsphere/entity"
)

type MentionType string

const (
	MentionTypeCodebase   MentionType = "codebase"
	MentionTypeLarkDoc    MentionType = "lark_doc"
	MentionTypeAttachment MentionType = "attachment"
	MentionTypeAeolus     MentionType = "aeolus"
)

type Mention interface {
	GetID() string
	GetType() MentionType
	PromptString() string  // for agent
	DisplayString() string // for display preprocessing items to the user
}

type BaseMention struct {
	ID   string      `json:"id" mapstructure:"id"`
	Type MentionType `json:"type" mapstructure:"type"`
}

func (m BaseMention) GetID() string {
	return m.ID
}

func (m BaseMention) GetType() MentionType {
	return m.Type
}

func (m BaseMention) PromptString() string {
	return fmt.Sprintf("@[%s](%s)", m.Type, m.ID)
}

func (m BaseMention) DisplayString() string {
	return fmt.Sprintf("@%s", m.ID)
}

// mention a codebase repository
type CodebaseMention struct {
	BaseMention `json:",inline"`
	RawURL      string `json:"raw_url" mapstructure:"raw_url"` // raw repo url if parsed from query
	RepoName    string `json:"repo_name" mapstructure:"repo_name"`

	// one of the following fields, or none for default branch
	Branch   string `json:"branch" mapstructure:"branch"`
	Tag      string `json:"tag" mapstructure:"tag"`
	CommitID string `json:"commit_id" mapstructure:"commit_id"`

	// mentions a specific file or directory in the codebase
	Path string `json:"path" mapstructure:"path"`
}

var _ Mention = &CodebaseMention{}

func (m CodebaseMention) PromptString() string {
	buf := strings.Builder{}
	buf.WriteString(lo.Ternary(
		m.RawURL != "",
		fmt.Sprintf("@[%s](%s)", m.RepoName, m.RawURL),
		fmt.Sprintf("@[%s](https://code.byted.org/%s)", m.RepoName, m.RepoName),
	))
	if m.Branch != "" {
		buf.WriteString(fmt.Sprintf(" branch: %s", m.Branch))
	}
	if m.Tag != "" {
		buf.WriteString(fmt.Sprintf(" tag: %s", m.Tag))
	}
	if m.CommitID != "" {
		buf.WriteString(fmt.Sprintf(" commit: %s", m.CommitID))
	}
	if m.Path != "" {
		buf.WriteString(fmt.Sprintf(" path: %s", m.Path))
	}
	return buf.String()
}

func (m CodebaseMention) DisplayString() string {
	return fmt.Sprintf("@%s", m.RepoName)
}

// mention a lark document or knowledge
type LarkDocMention struct {
	BaseMention     `json:",inline"`
	DocID           string `json:"doc_id" mapstructure:"doc_id"`
	Title           string `json:"title" mapstructure:"title"`
	URL             string `json:"url" mapstructure:"url"`
	KnowledgeBaseID string `json:"knowledge_base_id" mapstructure:"knowledge_base_id"`
}

var _ Mention = &LarkDocMention{}

func (m LarkDocMention) PromptString() string {
	return fmt.Sprintf("@[%s](%s)", lo.Ternary(m.Title != "", m.Title, m.URL), m.URL)
}

func (m LarkDocMention) DisplayString() string {
	return fmt.Sprintf("@%s", lo.Ternary(m.Title != "", m.Title, m.URL))
}

// mention an user uploaded attachment
type AttachmentMention struct {
	BaseMention `json:",inline"`
	ArtifactID  string `json:"artifact_id" mapstructure:"artifact_id"`
	Path        string `json:"path" mapstructure:"path"`
}

var _ Mention = &AttachmentMention{}

func (m AttachmentMention) PromptString() string {
	return fmt.Sprintf("@[%s](file://%s)", m.Path, m.Path)
}

func (m AttachmentMention) DisplayString() string {
	return fmt.Sprintf("@%s", m.Path)
}

// mention an Aeolus dashboard or data query
type AeolusMention struct {
	BaseMention `json:",inline"`
	URL         string `json:"url" mapstructure:"url"`
}

var _ Mention = &AeolusMention{}

func (m AeolusMention) PromptString() string {
	return fmt.Sprintf("@[%s](%s)", m.URL, m.URL)
}

func (m AeolusMention) DisplayString() string {
	return fmt.Sprintf("@%s", m.URL)
}

func UnmarshalMention(mention string) Mention {
	var m BaseMention
	err := json.Unmarshal([]byte(mention), &m)
	if err != nil {
		return nil
	}
	switch m.Type {
	case MentionTypeCodebase:
		var codebaseMention CodebaseMention
		err = json.Unmarshal([]byte(mention), &codebaseMention)
		if err != nil {
			return nil
		}
		return &codebaseMention
	case MentionTypeLarkDoc:
		var larkDocMention LarkDocMention
		err = json.Unmarshal([]byte(mention), &larkDocMention)
		if err != nil {
			return nil
		}
		return &larkDocMention
	case MentionTypeAttachment:
		var attachmentMention AttachmentMention
		err = json.Unmarshal([]byte(mention), &attachmentMention)
		if err != nil {
			return nil
		}
		return &attachmentMention
	case MentionTypeAeolus:
		var aeolusMention AeolusMention
		err = json.Unmarshal([]byte(mention), &aeolusMention)
		if err != nil {
			return nil
		}
		return &aeolusMention
	}

	// return the base mention if the type is not supported
	return &m
}

func GetMention(mention entity.Mention) Mention {
	// the `type` field in frontend-server is not the same `type` we use in agent
	// use fields to determine the type
	switch true {
	case mention.CodebaseMention != nil:
		return &CodebaseMention{
			BaseMention: BaseMention{
				ID:   mention.ID,
				Type: MentionTypeCodebase,
			},
			Tag:      mention.CodebaseMention.Tag,
			RepoName: mention.CodebaseMention.RepoName,
			Branch:   mention.CodebaseMention.Branch,
			Path:     mention.CodebaseMention.Path,
		}
	case mention.LarkDocMention != nil:
		return &LarkDocMention{
			BaseMention: BaseMention{
				ID:   mention.ID,
				Type: MentionTypeLarkDoc,
			},
			DocID:           mention.LarkDocMention.DocID,
			Title:           mention.LarkDocMention.Title,
			URL:             mention.LarkDocMention.URL,
			KnowledgeBaseID: mention.LarkDocMention.KnowledgeBaseID,
		}
	case mention.AttachmentMention != nil:
		return &AttachmentMention{
			BaseMention: BaseMention{
				ID:   mention.ID,
				Type: MentionTypeAttachment,
			},
			ArtifactID: mention.AttachmentMention.ArtifactID,
			Path:       mention.AttachmentMention.Path,
		}
	case mention.AeolusMention != nil:
		return &AeolusMention{
			BaseMention: BaseMention{
				ID:   mention.ID,
				Type: MentionTypeAeolus,
			},
			URL: mention.AeolusMention.URL,
		}
	default:
		return nil
	}
}
