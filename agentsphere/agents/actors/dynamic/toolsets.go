package dynamicactor

import (
	"fmt"
	"strings"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions"
	"github.com/samber/lo"
	"github.com/spf13/cast"

	controltool "code.byted.org/devgpt/kiwis/agentsphere/agents/actions/control"
	websearchtool "code.byted.org/devgpt/kiwis/agentsphere/agents/actions/external/websearch"
	llmtool "code.byted.org/devgpt/kiwis/agentsphere/agents/actions/llm"
	mcptool "code.byted.org/devgpt/kiwis/agentsphere/agents/actions/mcp"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/workspace"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actors/browseract"
	codebaseactor "code.byted.org/devgpt/kiwis/agentsphere/agents/actors/codebase"
	websearchactor "code.byted.org/devgpt/kiwis/agentsphere/agents/actors/websearch"
	larkagenttool "code.byted.org/devgpt/kiwis/agentsphere/agents/agenttool/lark"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/knowledges"
)

type Toolset struct {
	Identifier  string
	Description string
	New         func(run *iris.AgentRunContext, step *iris.AgentRunStep) ([]iris.Action, error)
}

func GetToolsets(run *iris.AgentRunContext, step *iris.AgentRunStep) ([]Toolset, error) {
	toolsets := []Toolset{
		{
			Identifier:  "files",
			Description: "filesystem operations like reading files, save data/notes, update files, search in directory, etc.",
			New:         NewEditorToolset,
		},
		{
			Identifier:  "terminal",
			Description: "execute commands in terminal",
			New:         NewTerminalToolset,
		},
		{
			Identifier:  "research",
			Description: "a toolset that conducts a comprehensive investigation on a complex topic by exploring multiple facets concurrently and generating a single, synthesized report. Use this for broad, open-ended questions that can be broken down into parallel, independent sub-tasks (e.g., 'impact of AI on healthcare'). This tool is slow but delivers a high-density, accurate report. Do NOT use this for tasks with sequential dependencies where one step requires the output from a previous step. For such tasks, use the `search` tool multiple times in sequence. Do NOT use this for simple or urgent queries where a quick answer is sufficient.",
			New:         NewResearchToolset,
		},
		{
			Identifier:  "search",
			Description: "a toolset that performs a quick, real-time search using search engines(Web Online&Bytedance Internal). It is ideal for two main scenarios: (1) Finding a single, specific, and time-sensitive piece of information (e.g., a fact, a number, a date, latest news). (2) Executing one step in a multi-step query that has sequential dependencies. If a task requires finding information A before you can search for information B, you must use this tool for each step. This tool is very fast (low latency) but returns a list of raw, unprocessed search snippets. The output can be verbose, noisy, and may have a high token count. Do NOT use this for complex questions that require analysis, synthesis, or a deep understanding of a subject.",
			New:         NewSearchToolset,
		},
		{
			Identifier: "browser",
			Description: `visit specific URLs/html pages, navigate websites, interact with elements, and extract content. Use this to validate generated html pages before deploying.
Limitation: Cannot extract multi pages, this toolset is slow for broad searching and too main actions, can not open binary files like docx, pptx, xlsx, etc. Can only be used for a specific URL.
**CRITICAL RESTRICTION**: This tool is STRICTLY FORBIDDEN from accessing Feishu/Lark document links under ANY circumstances. Do NOT attempt to use browser tool with any Feishu or Lark URLs.`,
			New: NewBrowserToolset,
		},
		{
			Identifier:  "git",
			Description: "clone and work on repositories from codebase(code.byted.org and bits.bytedance.net/code), github with user ssh keys",
			New:         NewGitToolset,
		},
		{
			Identifier:  mcptool.Codebase.ID,
			Description: mcptool.Codebase.Description,
			New:         NewCodebaseToolset,
		},
		{
			Identifier:  "vision",
			Description: "use VLM models and tools to comprehend images",
			New:         llmtool.NewVisionToolset,
		},
		{
			Identifier:  "deploy",
			Description: "when the task is completed, deploy static files, HTML, and web pages so the user can visit",
			New:         NewDeployToolset,
		},
		// {
		// 	Identifier:  mcptool.Lark.ID,
		// 	Description: mcptool.Lark.Description,
		// 	New:         NewLarkAgentToolset,
		// },
	}
	toolsets = append(toolsets, lo.FilterMap(mcptool.ProviderRegistry.Providers, func(provider *mcptool.MCPProvider, _ int) (Toolset, bool) {
		if provider.Type == mcptool.MCPSourceUserDefine {
			// 用户自定义的不放这里
			return Toolset{}, false
		}
		// if provider.ID == mcptool.Lark.ID {
		// 	// lark变成agenttool
		// 	return Toolset{}, false
		// }
		// codebase 的创建 MR 工具替换成了内置实现，创建 MR 时带上 Aime
		if provider.ID == mcptool.Codebase.ID {
			return Toolset{}, false
		}

		return Toolset{
			Identifier:  provider.ID,
			Description: provider.Description,
			New: func(run *iris.AgentRunContext, step *iris.AgentRunStep) ([]iris.Action, error) {
				return NewMCPToolset(run, provider.ID)
			},
		}, true
	})...)

	return toolsets, nil
}

func GetUserDefineToolsets(run *iris.AgentRunContext) ([]Toolset, error) {
	toolsets := []Toolset{}
	toolsets = append(toolsets, lo.FilterMap(mcptool.ProviderRegistry.Providers, func(provider *mcptool.MCPProvider, _ int) (Toolset, bool) {
		if provider.Type != mcptool.MCPSourceUserDefine {
			return Toolset{}, false
		}
		return Toolset{
			Identifier:  provider.ID,
			Description: provider.Description,
			New: func(run *iris.AgentRunContext, step *iris.AgentRunStep) ([]iris.Action, error) {
				return NewMCPToolset(run, provider.ID)
			},
		}, true
	})...)
	return toolsets, nil
}

// GetBuildInMCP Dynamic内置的MCP
func GetBuildInMCP(run *iris.AgentRunContext) []Toolset {
	toolsets := []Toolset{
		{
			Identifier:  mcptool.ToolHelper.ID,
			Description: mcptool.ToolHelper.Description,
			New: func(run *iris.AgentRunContext, step *iris.AgentRunStep) ([]iris.Action, error) {
				return mcptool.ToolHelper.Tools, nil
			},
		},
	}
	return toolsets
}

func NewEditorToolset(run *iris.AgentRunContext, step *iris.AgentRunStep) ([]iris.Action, error) {
	return []iris.Action{
		workspace.NewGrepSearchAction(workspace.GrepSearchConfig{}),
		workspace.NewGlobSearchAction(),
		workspace.NewReadFileActionV2(workspace.ReadFileActionOption{MaxLines: 500}), // 500 lines for claude and gemini, 100 lines for doubao
		workspace.NewCreateFileAction(workspace.CreateFileOption{}),
		workspace.NewReadMarkDownFilesAction(),
		workspace.NewAppendFileAction(),
		workspace.NewRawPatchFileV2Action(),
		workspace.NewDeployAction(workspace.DeployOption{ProcessHTML: true}),
		workspace.NewExcelPreviewTool(),
		workspace.NewGeneralDataFilePreviewTool(),
	}, nil
}

func NewLarkAgentToolset(run *iris.AgentRunContext, step *iris.AgentRunStep) ([]iris.Action, error) {
	return []iris.Action{
		larkagenttool.NewLarkAgentTool(run, step),
	}, nil
}

func NewTerminalToolset(run *iris.AgentRunContext, step *iris.AgentRunStep) ([]iris.Action, error) {
	return []iris.Action{
		workspace.NewExecuteCommandActionWithDefaultEnv(run, workspace.NewExecuteCommandActionOption{
			MaxColWidth:        20480, // provide a large col width (in characters) to avoid truncating lines
			ScrollbackLines:    300,
			MaxTokens:          20480,
			DisableStdioStream: true,
		}),
	}, nil
}
func init() {
	websearchtool.SearchAgentCreator = func(run *iris.AgentRunContext) websearchtool.InternalAISearcher {
		enableInternalSearch := cast.ToBool(run.Parameters[websearchactor.EnableInternalSearchParameterKey])
		run.GetLogger().Infof("enable_internal_search: %v", enableInternalSearch)
		return websearchactor.New(websearchactor.CreateOption{EnableBytedSearch: enableInternalSearch})
	}
}
func NewResearchToolset(run *iris.AgentRunContext, step *iris.AgentRunStep) ([]iris.Action, error) {
	return []iris.Action{
		websearchtool.NewQuickResearchTool(),
	}, nil
}

func NewSearchToolset(run *iris.AgentRunContext, step *iris.AgentRunStep) ([]iris.Action, error) {
	return []iris.Action{
		websearchtool.NewSearchTool(),
	}, nil
}

func NewBrowserToolset(run *iris.AgentRunContext, step *iris.AgentRunStep) ([]iris.Action, error) {
	tools, err := browseract.NewBrowserToolset(run)
	if err != nil {
		return nil, err
	}
	return tools, nil
}

func NewGitToolset(run *iris.AgentRunContext, step *iris.AgentRunStep) ([]iris.Action, error) {
	return []iris.Action{
		codebaseactor.NewGitCloneAction(true),
	}, nil
}

func NewCodebaseToolset(run *iris.AgentRunContext, step *iris.AgentRunStep) ([]iris.Action, error) {
	tools, err := mcptool.ProviderRegistry.ListTools(run, mcptool.Codebase.ID)
	if err != nil {
		return []iris.Action{}, err
	}
	tools = append(tools, codebaseactor.NewSubmitMergeRequestAction(true))
	tool, err := getBitsMRInfoTool(run)
	if err != nil {
		run.GetLogger().Errorf("failed to get bits mr info tool: %v", err)
	} else {
		tools = append(tools, tool)
	}
	return tools, nil
}

func getBitsMRInfoTool(run *iris.AgentRunContext) (iris.Action, error) {
	bitsTools, err := mcptool.ProviderRegistry.ListTools(run, mcptool.BitsWorkFlow.ID)
	if err != nil {
		return nil, err
	}
	bitsMRInfoTool, b := lo.Find(bitsTools, func(item iris.Action) bool {
		return strings.Contains(item.Name(), "bits_workflow_get_mr_info")
	})
	if !b {
		return nil, fmt.Errorf("bits_workflow_get_mr_info tool not found")
	}
	return actions.NewActionProxy(bitsMRInfoTool).WithDescription(func() string {
		return "Retrieve MR detailed information by a Bits MR URL (only use this tool when the URL strictly follows the format: https://bits.bytedance.net/devops/{project_id}/code/detail/{cr_id})," +
			" including associated code repositories, branches, commits, and other data."
	}), nil
}

// Basic tools that are always available for every kind of task
func NewDefaultToolset(run *iris.AgentRunContext, step *iris.AgentRunStep) []iris.Action {
	kg := knowledges.NewSemanticKnowledgebase(run, knowledges.LoadKnowledge(), run.GetConfig().GetModelByScene("retrieve_knowledge"))
	tools := []iris.Action{
		// create notes and reports
		workspace.NewCreateFileAction(workspace.CreateFileOption{}),
		// command execution as a last resort
		workspace.NewExecuteCommandActionWithDefaultEnv(run, workspace.NewExecuteCommandActionOption{
			MaxColWidth:        20480, // provide a large col width (in characters) to avoid truncating lines
			ScrollbackLines:    300,
			MaxTokens:          20480,
			DisableStdioStream: true,
		}),
		// use progress thinking tool to review the current progress, determine the next step, retreive related knowledge
		llmtool.NewProgressThinkingTool(
			kg,
			knowledges.RetrieveParam{
				Agent:        Identifier,
				Variant:      run.GetConfig().GetVariantByScene(Identifier),
				Tools:        []string{},
				WithCitation: false,
			},
		),
		// finish the task
		controltool.NewProgreActConclusionTool(),
	}
	return tools
}

func NewMCPToolset(run *iris.AgentRunContext, providerID string) ([]iris.Action, error) {
	tools, err := mcptool.ProviderRegistry.ListTools(run, providerID)
	if err != nil {
		return []iris.Action{}, err
	}
	return tools, nil
}

func NewDeployToolset(run *iris.AgentRunContext, step *iris.AgentRunStep) ([]iris.Action, error) {
	return []iris.Action{
		workspace.NewDeployAction(workspace.DeployOption{ProcessHTML: true}),
	}, nil
}

func NewToolsRetrievalToolset(run *iris.AgentRunContext) []iris.Action {
	return []iris.Action{}
}

// NewPlaceholderToolset provides no tools, but can be used to hint the planner to assign tasks or used to retrieve knowledge
func NewPlaceholderToolset(run *iris.AgentRunContext) []iris.Action {
	return []iris.Action{}
}

func BuildTools(run *iris.AgentRunContext, toolsets []Toolset, step *iris.AgentRunStep) []iris.Action {
	tools := []iris.Action{}
	for _, toolset := range toolsets {
		toolsets, err := toolset.New(run, step)
		if err != nil {
			run.GetLogger().Warnf("failed to create toolset %s: %v", toolset.Identifier, err)
			continue
		}
		tools = append(tools, toolsets...)
	}
	tools = append(tools, NewDefaultToolset(run, step)...)
	tools = lo.UniqBy(tools, func(tool iris.Action) string {
		return tool.Name()
	})
	return tools
}
