package response

import (
	"embed"
	"fmt"
	"strings"

	dynamicactor "code.byted.org/devgpt/kiwis/agentsphere/agents/actors/dynamic"
	agents "code.byted.org/devgpt/kiwis/agentsphere/agents/core"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris/agentrace"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/prompt"
	"code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/devgpt/kiwis/copilotstack/agent/framework"
	"code.byted.org/devgpt/kiwis/lib/conv"
	"code.byted.org/devgpt/kiwis/lib/streamparser"
	"github.com/samber/lo"
	"github.com/sourcegraph/conc/panics"
)

var (
	MessageResponse = "response_ack"
	MessageResult   = "response_conclude"

	//go:embed ack_system.go.tmpl
	ackSystemPrompt         string
	AckSystemPromptTemplate = prompt.MustGetTemplate("AckSystemPrompt", ackSystemPrompt)
	//go:embed ack_user.go.tmpl
	ackUserPrompt         string
	AckUserPromptTemplate = prompt.MustGetTemplate("AckUserPrompt", ackUserPrompt)

	//go:embed explain_system.go.tmpl
	explainSystemPrompt         string
	ExplainSystemPromptTemplate = prompt.MustGetTemplate("ExplainSystemPrompt", explainSystemPrompt)
	//go:embed explain_user.go.tmpl
	explainUserPrompt         string
	ExplainUserPromptTemplate = prompt.MustGetTemplate("ExplainUserPrompt", explainUserPrompt)

	//go:embed conclusion_system.go.tmpl
	conclusionSystemPrompt         string
	ConclusionSystemPromptTemplate = prompt.MustGetTemplate("ConclusionSystemPrompt", conclusionSystemPrompt)
	//go:embed conclusion_user.go.tmpl
	conclusionUserPrompt         string
	ConclusionUserPromptTemplate = prompt.MustGetTemplate("ConclusionUserPrompt", conclusionUserPrompt)

	//go:embed thought.go.tmpl
	thoughtPrompt         string
	ThoughtPromptTemplate = prompt.MustGetTemplate("ThoughtPrompt", thoughtPrompt)

	//go:embed *.go.tmpl
	prompts   embed.FS
	promptset = prompt.MustNewPromptSet(prompts, ".")
)

func Ack(run *iris.AgentRunContext) (intercepted bool, replyTo string, response string) {
	messages := run.State.Conversation.GetMessages()
	logger := run.GetLogger()
	lastUserMessage, _, ok := lo.FindLastIndexOf(messages, func(msg *iris.Message) bool {
		return msg.From == iris.MessageFromUser
	})
	var (
		evaluateStep *iris.AgentRunStep
		detectResult = make(chan error)
	)
	if ok {
		replyTo = lastUserMessage.ID
		aimeAgent := NewDetectAgent()
		go func() {
			defer close(detectResult)
			if err := panics.Try(func() {
				step, err := aimeAgent.Run(run, lastUserMessage.Content)
				evaluateStep = step
				detectResult <- err
			}).AsError(); err != nil {
				detectResult <- err
				run.GetLogger().Errorf("detect agent panic: %v", err)
			}
		}()
	} else {
		logger.Warn("no user message found")
		detectResult <- fmt.Errorf("no user message found")
		close(detectResult)
	}

	step := run.CreateStep(&iris.CreateStepOption{
		ExecutorAgent: "response_ack",
	})
	span, ctx := run.GetTracer(run).
		StartCustomSpan(run, agentrace.SpanTypeStep, "response_ack",
			agentrace.WithSpanID(step.StepID),
			agentrace.WithObjectSpanData(map[string]any{"last_user_message": lastUserMessage}),
		)
	run = run.WithContext(ctx)
	defer func() {
		span.UpdateData(agentrace.NewObjectSpanData(map[string]any{
			"intercepted": intercepted,
			"reply_to":    replyTo,
			"response":    response,
		}))
		span.Finish()
	}()
	message := agents.ComposeMessage(run, agents.ComposerOptions{
		Templates: prompt.TemplateSet{
			SystemTmpl:  AckSystemPromptTemplate,
			UserTmpl:    AckUserPromptTemplate,
			ThoughtTmpl: ThoughtPromptTemplate,
		},
		SystemPromptVariables: map[string]any{
			"Language": agents.GetUserLanguage(run.Parameters),
		},
		UserPromptVariables: map[string]any{
			"Conversations": lo.Reduce(messages, func(agg string, msg *iris.Message, index int) string {
				return agg + lo.Ternary(msg.From == iris.MessageFromUser || msg.From == MessageResponse, fmt.Sprintf("%s:\n%s\n\n", msg.From, msg.Content), "")
			}, ""),
		},
	})
	content, _ := agents.Think(run, "response_ack", message,
		agents.ThinkOption{
			StreamFilter: streamparser.StreamFilter{
				MaxCheckSize: 10,
				FlushSize:    1,
				AheadSize:    10,
				StartTokens:  []string{},
				EndTokens:    []string{},
				BreakTokens:  []string{},
			},
		},
	)
	step.Thought = &iris.Thought{
		Content: content.Content,
		LLMCall: iris.LLMCall{
			ModelName:    content.Model,
			Temperature:  content.Temperature,
			Prompt:       message,
			Usage:        content.Usage,
			FinishReason: content.FinishReason,
			TraceID:      content.TraceID,
		},
	}
	// run detect and ack in parallel
	// if detect matches a scene, discard ack result
	select {
	case err := <-detectResult:
		if err == nil {
			// 1. evaluate if the request should be intercepted
			matchScene := conv.DefaultAny[string](evaluateStep.Conclusion["match_scene"])
			answer := conv.DefaultAny[string](evaluateStep.Conclusion["answer"])
			if !strings.Contains(matchScene, "none_match") && answer != "" {
				run.State.Conversation.MarkDeprecated(lastUserMessage.ID)
				// 2. if related, responde with aime's answer
				return true, replyTo, answer
			}
		} else {
			run.GetLogger().Errorf("detect agent error: %v", err)
		}
	case <-run.Done():
		return false, replyTo, ""
	}
	run.GetPublisher().ReportThought(step)
	step.Conclusion["conclusion"] = content
	step.Finish(step.Conclusion, nil)

	run.GetPublisher().ReportStep(step)
	run.GetLogger().Infof("%s step finished: %s, step status %s,parent: %s, %s", step.ExecutorAgent, step.Status, step.StepID, step.Parent, step.ExecutorAgent)

	return false, replyTo, content.Content
}

func Conclude(run *iris.AgentRunContext, result string) (conluded string) {
	span, ctx := run.GetTracer(run).
		StartCustomSpan(run, agentrace.SpanTypeStep, "response_conclude",
			agentrace.WithObjectSpanData(map[string]any{"result": result}),
		)
	run = run.WithContext(ctx)
	defer func() {
		span.UpdateData(agentrace.NewObjectSpanData(map[string]any{
			"concluded": conluded,
		}))
		span.Finish()
	}()
	step := run.CreateStep(&iris.CreateStepOption{
		ExecutorAgent: "response_conclude",
	})
	message := agents.ComposeMessage(run, agents.ComposerOptions{
		Templates: prompt.TemplateSet{
			SystemTmpl:  ConclusionSystemPromptTemplate,
			UserTmpl:    ConclusionUserPromptTemplate,
			ThoughtTmpl: ThoughtPromptTemplate,
		},
		SystemPromptVariables: map[string]any{
			"Language": agents.GetUserLanguage(run.Parameters),
		},
		UserPromptVariables: map[string]any{
			"Conversations": lo.Reduce(run.State.Conversation.GetMessages(), func(agg string, msg *iris.Message, index int) string {
				return agg + lo.Ternary(msg.From == iris.MessageFromUser || msg.From == MessageResult, fmt.Sprintf("%s:\n%s\n\n", msg.From, msg.Content), "")
			}, ""),
			"Results": result,
		},
	})
	content, _ := agents.Think(run, "response_conclude", message,
		agents.ThinkOption{
			StreamFilter: streamparser.StreamFilter{
				MaxCheckSize: 10,
				FlushSize:    1,
				AheadSize:    10,
				StartTokens:  []string{},
				EndTokens:    []string{},
				BreakTokens:  []string{},
			},
		},
	)
	step.Conclusion["conclusion"] = content
	step.Finish(step.Conclusion, nil)
	return content.Content
}

func AckAndRespond(run *iris.AgentRunContext) (harmful bool, intercepted bool, replyTo string, response string) {
	messages := run.State.Conversation.GetMessages()
	lastUserMessage, _, ok := lo.FindLastIndexOf(messages, func(msg *iris.Message) bool {
		return msg.From == iris.MessageFromUser
	})
	if ok {
		replyTo = lastUserMessage.ID
	} else {
		run.GetLogger().Warn("no user message found")
		// todo: then what?
	}

	step := run.CreateStep(&iris.CreateStepOption{
		ExecutorAgent: "explain",
	})
	span, ctx := run.GetTracer(run).
		StartCustomSpan(run, agentrace.SpanTypeStep, "explain",
			agentrace.WithSpanID(step.StepID),
			agentrace.WithObjectSpanData(map[string]any{"last_user_message": lastUserMessage}),
		)
	run = run.WithContext(ctx)
	defer func() {
		span.UpdateData(agentrace.NewObjectSpanData(map[string]any{
			"harmful":     harmful,
			"intercepted": intercepted,
			"reply_to":    replyTo,
			"response":    response,
		}))
		span.Finish()
	}()

	userDefineToolsets, err := dynamicactor.GetUserDefineToolsets(run)
	if err != nil {
		run.GetLogger().Errorf("failed to get user define toolsets, err: %s", err)
	}
	toolsets, err := dynamicactor.GetToolsets(run, step)
	if err != nil {
		run.GetLogger().Errorf("failed to get toolsets, err: %s", err)
	}
	message := prepExplainPrompt(ExplainPromptOption{
		Conversation:       run.State.Conversation,
		Parameters:         run.Parameters,
		UserDefineToolsets: userDefineToolsets,
		Toolsets:           toolsets,
	})
	content, _ := agents.Think(run, "intent_detect", message,
		agents.ThinkOption{
			StreamFilter: streamparser.StreamFilter{
				MaxCheckSize: 10,
				FlushSize:    1,
				AheadSize:    10,
				StartTokens:  []string{},
				EndTokens:    []string{},
				BreakTokens:  []string{},
			},
		},
	)
	step.Thought = &iris.Thought{
		Content: content.Content,
		LLMCall: iris.LLMCall{
			ModelName:    content.Model,
			Temperature:  content.Temperature,
			Prompt:       message,
			Usage:        content.Usage,
			FinishReason: content.FinishReason,
			TraceID:      content.TraceID,
		},
	}

	run.GetPublisher().ReportThought(step)
	step.Conclusion["conclusion"] = content
	step.Finish(step.Conclusion, nil)

	run.GetPublisher().ReportStep(step)
	run.GetLogger().Infof("%s step finished: %s, step status %s,parent: %s, %s", step.ExecutorAgent, step.Status, step.StepID, step.Parent, step.ExecutorAgent)

	// 这种时候不直接用 rawResponse，而是用一个兜底 msg，因为可能被攻击了
	fallbackResponse := "ok~"

	lines := strings.SplitN(content.Content, "\n", 2)
	if len(lines) < 2 {
		return false, false, replyTo, response
	}
	detection := strings.Split(strings.TrimSpace(lines[0]), ":")
	if len(detection) != 3 {
		return false, false, replyTo, fallbackResponse
	}
	decision := detection[2]

	switch decision {
	case "pause":
		intercepted = true
		response = lines[1]
	case "continue":
		intercepted = false
		response = lines[1]
	default:
		intercepted = false // todo: 这里要不要写true
		response = "ok~"    // 这种时候不直接用rawResponse，而是用一个兜底msg，因为可能被攻击到了？
	}
	if strings.Contains(content.Content, "ask_for_hack") {
		harmful = true
	}

	mentions := lo.FlatMap(messages, func(msg *iris.Message, _ int) []iris.Mention {
		if msg.From == iris.MessageFromUser {
			return msg.Mentions
		}
		return nil
	})
	// if model outputs mention prompt string, replace it with display string as it's readable to the user
	lo.ForEach(mentions, func(mention iris.Mention, _ int) {
		response = strings.ReplaceAll(response, mention.PromptString(), mention.DisplayString())
	})

	return harmful, intercepted, replyTo, response
}

type ExplainPromptOption struct {
	Conversation       *iris.Conversation
	Parameters         map[entity.RuntimeParameterKey]any
	UserDefineToolsets []dynamicactor.Toolset
	Toolsets           []dynamicactor.Toolset
}

func prepExplainPrompt(opt ExplainPromptOption) []*framework.ChatMessage {
	messages, _ := prompt.ComposeVaryMessages([]prompt.ComposeVaryMessageOption{
		prompt.WithSystemMessage(ExplainSystemPromptTemplate, map[string]any{
			"Language":           agents.GetUserLanguage(opt.Parameters),
			"UserDefineToolsets": opt.UserDefineToolsets,
			"Toolsets":           opt.Toolsets,
		}),
		prompt.WithUserMessage(ExplainUserPromptTemplate, map[string]any{
			"Conversations": lo.Reduce(opt.Conversation.GetMessages(), func(agg string, msg *iris.Message, index int) string {
				return agg + lo.Ternary(msg.From == iris.MessageFromUser || msg.From == MessageResponse, fmt.Sprintf("%s:\n%s\n\n", msg.From, msg.Content), "")
			}, ""),
			"UserQueryAttachments": getUserQueryAttachmentsDesc(opt.Conversation),
		}),
	})
	return messages
}

// getUserQueryAttachmentsDesc 参考(a *PlanActAgent) updatePlannerRequirements实现的，todo：搬到server的话要换数据源
func getUserQueryAttachmentsDesc(conversation *iris.Conversation) (userQueryAttachments string) {
	newMessages := conversation.GetDirtyMessage()
	if len(newMessages) == 0 {
		return ""
	}

	for _, message := range newMessages {
		for _, attachment := range message.Attachments {
			userQueryAttachments += attachment.Path + "\n"
		}
	}

	return userQueryAttachments
}
