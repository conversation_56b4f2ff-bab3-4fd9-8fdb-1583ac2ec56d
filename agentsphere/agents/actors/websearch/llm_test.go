package websearch

import (
	"context"
	"math"
	"testing"

	"github.com/sirupsen/logrus"
	"gotest.tools/v3/assert"
)

func Test_llmUnmarshal(t *testing.T) {
	r := struct {
		Queries []string `json:"queries"`
		Think   string   `json:"think"`
	}{}
	a := &Agent{
		logger: logrus.StandardLogger(),
	}
	err := a.llmUnmarshal(`{
  "queries": [
    "喜茶财务指标异常情况",
    "喜茶各产品线毛利率",
    "喜茶财务指标历史变化",
    "喜茶vs奈雪财务指标对比",
    "2025年March喜茶财务数据",
    "Heytea financial indicators lang:en",
    "喜茶财务指标虚假证据"
  ],
  "think": "我来给您说说为啥选这些搜索查询哈。表面意图就是想了解喜茶的财务指标，像毛利率、营收、净利润、现金流这些。实用意图呢，就是想通过这些指标评估喜茶的经营状况和盈利能力。情感上，可能是对喜茶的商业表现感兴趣，或者有投资意向，所以会有期待和好奇。社会意图方面，了解喜茶财务情况可以在社交场合有谈资，也能显示自己对商业的关注。身份意图就是把自己定位成关注商业动态、有投资眼光的人。禁忌意图可能是不想面对喜茶财务不佳的情况。影子意图也许是潜意识里想通过喜茶的成功来激励自己。

基于这些意图，专家怀疑者会关注喜茶财务指标的异常情况，看看有没有隐藏的问题；细节分析者会深入到各产品线的毛利率，了解更精确的信息；历史研究者会追踪喜茶财务指标的历史变化，把握发展趋势；比较思考者会将喜茶和奈雪进行对比，评估相对优势；时间语境者会关注2025年3月的最新数据，保证信息的时效性；全球化者考虑到英文在商业信息传播中的权威性，用英文搜索喜茶财务指标；现实怀疑论者会主动寻找喜茶财务指标虚假的证据，挑战常规认知。"
}`, &r)
	assert.NilError(t, err)
	assert.DeepEqual(t, r.Think, "我来给您说说为啥选这些搜索查询哈。表面意图就是想了解喜茶的财务指标，像毛利率、营收、净利润、现金流这些。实用意图呢，就是想通过这些指标评估喜茶的经营状况和盈利能力。情感上，可能是对喜茶的商业表现感兴趣，或者有投资意向，所以会有期待和好奇。社会意图方面，了解喜茶财务情况可以在社交场合有谈资，也能显示自己对商业的关注。身份意图就是把自己定位成关注商业动态、有投资眼光的人。禁忌意图可能是不想面对喜茶财务不佳的情况。影子意图也许是潜意识里想通过喜茶的成功来激励自己。基于这些意图，专家怀疑者会关注喜茶财务指标的异常情况，看看有没有隐藏的问题；细节分析者会深入到各产品线的毛利率，了解更精确的信息；历史研究者会追踪喜茶财务指标的历史变化，把握发展趋势；比较思考者会将喜茶和奈雪进行对比，评估相对优势；时间语境者会关注2025年3月的最新数据，保证信息的时效性；全球化者考虑到英文在商业信息传播中的权威性，用英文搜索喜茶财务指标；现实怀疑论者会主动寻找喜茶财务指标虚假的证据，挑战常规认知。")
}

func Test_bgeMiniCpm(t *testing.T) {
	scores, err := rankByBgeMiniCpm(context.Background(), "What is cx", []string{"cx standards for chenxiang", "test", "cx", "cx", "cx", "cx", "cx", "cx", "cx standards for chenxiang", "test"})
	assert.NilError(t, err)
	assert.Assert(t, len(scores) == 10)
	assert.Assert(t, math.Abs(scores[0]-scores[8]) < 0.05)
	assert.Assert(t, math.Abs(scores[1]-scores[9]) < 0.05)
	assert.Assert(t, math.Abs(scores[3]-scores[4]) < 0.05)
}
