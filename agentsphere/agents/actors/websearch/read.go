package websearch

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"math"
	"net/http"
	"net/url"
	"regexp"
	"strings"
	"time"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris/agentrace"

	"code.byted.org/devgpt/kiwis/lib/metrics"

	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/devai"
	"code.byted.org/devgpt/kiwis/lib/hertz"

	url_parse "net/url"

	htmltomarkdown "github.com/JohannesKaufmann/html-to-markdown/v2"

	"github.com/pkg/errors"
)

type readURLResult struct {
	//Title   string
	URL     string
	Content string
}

func (a *Agent) readWebURL(ctx context.Context, url string) (_ *readURLResult, err error) {
	span, ctx := agentrace.GetRuntimeTracerFromContext(ctx).StartCustomSpan(ctx, agentrace.SpanTypeStep, "read_web",
		agentrace.WithObjectSpanData(map[string]any{"url": url}))
	defer func() {
		agentrace.AddErrorTag(span, err)
		span.Finish()
	}()
	cachedPage, ok := a.readURLCache.Load(url)
	span.UpdateData(agentrace.NewObjectSpanData(map[string]any{"hit_cache": ok}))
	if ok {
		a.logger.Infof("cached page for %s", url)
		return cachedPage.(*readURLResult), nil
	}
	// 去url中的空格
	url = url_parse.QueryEscape(url)
	start := time.Now()
	defer func() {
		duration := time.Since(start)
		if duration > time.Second*30 {
			a.logger.Warnf("slow read url %s cost %s", url, duration.String())
			return
		}
		a.logger.Infof("read url %s cost %s", url, duration.String())
	}()
	defer func() {
		_ = metrics.AR.ActorWebSearcherPhraseCost.WithTags(&metrics.AgentActorWebSearcherPhraseTag{
			Phrase: "read_url",
		}).Observe(float64(time.Since(start).Milliseconds()))
	}()
	// readURL might be blocked.
	ctx, cancel := context.WithTimeout(ctx, time.Minute)
	defer cancel()
	client := &http.Client{
		Timeout: time.Second * 60,
	}
	resp, err := client.Get(fmt.Sprintf("http://127.0.0.1:9226?url=%s", url))
	if err != nil {
		return nil, errors.WithMessage(err, "failed to call browser mcp")
	}
	if resp.StatusCode != 200 {
		return nil, errors.Errorf("failed to call browser mcp. code %d", resp.StatusCode)
	}

	defer resp.Body.Close()
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to read body from browser mcp")
	}
	type RespContent struct {
		Content string `json:"content"`
		Error   string `json:"error"`
	}
	content := &RespContent{}
	err = json.Unmarshal(body, content)
	if err != nil {
		return nil, err
	}
	span.UpdateData(agentrace.NewObjectSpanData(map[string]any{"html_content": content.Content, "error": content.Error}))
	if content.Error != "" {
		return nil, errors.WithMessagef(errors.New(content.Error), "failed to call browser mcp")
	}

	if len(content.Content) == 0 {
		return nil, errors.Errorf("empty response from browser mcp")
	}
	textContent := content.Content
	// TODO: some info might lost, maybe extract content from html
	markdown, err := htmltomarkdown.ConvertString(textContent)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to convert html to markdown")
	}
	markdown = removeBase64Images(markdown)
	span.UpdateData(agentrace.NewObjectSpanData(map[string]any{"markdown_content": markdown}))
	r := &readURLResult{
		URL:     url,
		Content: markdown,
	}
	a.readURLCache.Store(url, r)
	return r, nil
}

func (a *Agent) readLarkURL(ctx context.Context, url string, documentID string) (_ *readURLResult, err error) {
	span, ctx := agentrace.GetRuntimeTracerFromContext(ctx).StartCustomSpan(ctx, agentrace.SpanTypeStep, "read_lark",
		agentrace.WithObjectSpanData(map[string]any{"document": documentID, "url": url}))
	defer func() {
		agentrace.AddErrorTag(span, err)
		span.Finish()
	}()
	body := &devai.AgentReadLarkRequest{
		LarkToken:  a.larkToken,
		URL:        url,
		DocumentID: documentID,
	}
	response := &devai.AgentReadLarkResponse{}
	_, err = searchClient.DoJSONReq(ctx, http.MethodPost, "/openapi/knowledge/v1/agent/read_lark", hertz.ReqOption{
		ExpectedCode:    http.StatusOK,
		Body:            body,
		Result:          response,
		Notes:           "devai_read_lark_url",
		Headers:         map[string]string{"x-jwt-token": a.cloudToken},
		Timeout:         time.Second * 30,
		SetTTEnvHeaders: true,
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return &readURLResult{
		URL:     url,
		Content: response.Content,
	}, nil
}

func (a *Agent) readAndExtract(ctx context.Context, question string, url *SearchItem, targetScore float64) (string, error) {
	span, ctx := agentrace.GetRuntimeTracerFromContext(ctx).StartCustomSpan(ctx, agentrace.SpanTypeStep, "read")
	span.SetTags(url.GetTraceTag())
	defer span.Finish()
	a.logger.Infof("start to read url: %s", url.URL)
	var (
		readContent *readURLResult
		err         error
	)
	if url.Source == larkSearchSource || url.Source == bytecloudSource || url.Source == arcositeSource {
		readContent, err = a.readLarkURL(ctx, url.URL, url.DocumentID)
		if err != nil {
			return "", errors.WithMessage(err, "failed to read lark url")
		}
	} else {
		_ = metrics.AR.ActorWebSearcherReadWeb.Add(1)
		readContent, err = a.readWebURL(ctx, url.URL)
		if err != nil {
			_ = metrics.AR.ActorWebSearcherReadWebError.Add(1)
			return "", errors.WithMessage(err, "failed to read web url")
		}
	}
	a.ctxInfof(ctx, "fetch content: %s", readContent)
	start := time.Now()
	extractedContent, err := a.llmExtractFromPage(ctx, question, readContent.Content)
	if err != nil {
		_ = metrics.AR.ActorWebSearcherLLMExtract.WithTags(&metrics.AgentActorWebSearcherLLMExtractTag{Conclusion: "error"}).Add(1)
		return "", errors.WithMessage(err, "failed to extract content")
	}
	a.logger.Infof("read url %s cost %s", url.URL, time.Since(start).String())
	a.ctxInfof(ctx, "extracted content: %s", extractedContent)
	extractedContent = strings.TrimSpace(extractedContent)
	if len(extractedContent) == 0 {
		_ = metrics.AR.ActorWebSearcherLLMExtract.WithTags(&metrics.AgentActorWebSearcherLLMExtractTag{Conclusion: "no_content"}).Add(1)
		return "", errors.Errorf("empty extracted content")
	}
	if extractedContent == "no_relevant_passage" {
		_ = metrics.AR.ActorWebSearcherLLMExtract.WithTags(&metrics.AgentActorWebSearcherLLMExtractTag{Conclusion: "no_relevant_passage"}).Add(1)
		return "", errors.Errorf("no relevant passage")
	}
	rankScores, err := rankByBgeMiniCpm(ctx, question, []string{extractedContent})
	if err != nil {
		a.logger.Errorf("failed to rank content: %v", err)
		return "", errors.WithMessage(err, "failed to rank content")
	}
	if len(rankScores) == 0 {
		return "", errors.WithMessage(err, "no rank scores")
	}
	a.ctxInfof(ctx, "rank scores: %v", rankScores)
	rankScore := rankScores[0]
	span.UpdateData(agentrace.NewObjectSpanData(map[string]any{"score": rankScore}))
	if rankScore < targetScore {
		_ = metrics.AR.ActorWebSearcherLLMExtract.WithTags(&metrics.AgentActorWebSearcherLLMExtractTag{Conclusion: "filtered_out"}).Add(1)
		a.logger.Warnf("drop content %+v: %s", rankScore, extractedContent)
		return extractedContent, errors.Errorf("rank score %f is lower than %f", rankScore, targetScore)
	}
	_ = metrics.AR.ActorWebSearcherLLMExtract.WithTags(&metrics.AgentActorWebSearcherLLMExtractTag{Conclusion: "ok"}).Add(1)
	return extractedContent, nil
}

func (a *Agent) llmExtractFromPage(ctx context.Context, question string, content string) (_ string, err error) {
	span, ctx := agentrace.GetRuntimeTracerFromContext(ctx).StartCustomSpan(ctx, agentrace.SpanTypeStep, "extract")
	defer func() {
		agentrace.AddErrorTag(span, err)
		span.Finish()
	}()
	start := time.Now()
	defer func() {
		_ = metrics.AR.ActorWebSearcherPhraseCost.WithTags(&metrics.AgentActorWebSearcherPhraseTag{
			Phrase: "llm_extract_from_web",
		}).Observe(float64(time.Since(start).Milliseconds()))
	}()
	system := `
**Role:** Precise Web Content Extractor

**Objective:** Identify and extract verbatim passages from the provided Markdown web content that directly answer the User Question.

**Procedure:**
1. Analyze the User Question to determine the exact information needed.
2. Thoroughly examine the Markdown Web Content.
3. Locate the specific sentence(s) or paragraph(s) containing the answer.
4. Extract these relevant passages *exactly* as they appear in the content.
5. If multiple distinct passages answer the question, extract all of them.
6. Ensure extracted content is strictly from the provided Markdown. No summaries or external info.
7. **If no relevant passages are found, output no_relevant_passage.**
`
	user := fmt.Sprintf(`User question: %s. 
Markdown - formatted web content: 
%s`, question, content)
	content, err = a.generate(ctx, []tool{evaluateChunkTool}, system, user)
	if err != nil {
		return "", err
	}
	span.UpdateData(agentrace.NewObjectSpanData(map[string]any{"content": content}))
	return content, err
}

func (a *Agent) cherryPick(ctx context.Context, question string, longContext string) (string, error) {
	const (
		snippetLength       = 3000
		chunkSize           = 200
		maxTokensPerRequest = 8192
		tokensPerCharacter  = 0.5
	)
	runes := []rune(longContext)
	contextSnippets := float64(len(runes)) / float64(snippetLength)
	numSnippets := int(math.Max(2, math.Min(5, math.Floor(contextSnippets))))

	if len(runes) < snippetLength*2 {
		return longContext, nil
	}

	chunks := make([]string, 0)
	for i := 0; i < len(runes); i += chunkSize {
		end := i + chunkSize
		if end > len(runes) {
			end = len(runes)
		}
		chunks = append(chunks, string(runes[i:end]))
	}
	a.logger.Infof("late chunking enabled! num chunks: %d", len(chunks))
	estimatedTokensPerChunk := int(math.Ceil(float64(chunkSize) * tokensPerCharacter))
	chunksPerBatch := maxTokensPerRequest / estimatedTokensPerChunk
	chunkBatches := make([][]string, 0)
	for i := 0; i < len(chunks); i += chunksPerBatch {
		end := i + chunksPerBatch
		if end > len(chunks) {
			end = len(chunks)
		}
		chunkBatches = append(chunkBatches, chunks[i:end])
	}

	a.logger.Infof("total length %d split %d chunks into %d batches of ~%d chunks each",
		len(runes), len(chunks), len(chunkBatches), chunksPerBatch)

	allChunkEmbeddings := make([][]float64, 0)
	for batchIndex, batch := range chunkBatches {
		a.logger.Infof("processing batch %d/%d with %d chunks", batchIndex+1, len(chunkBatches), len(batch))

		embeddings, err := a.embeddingsWithRetry(ctx, batch)
		if err != nil {
			return "", errors.WithStack(err)
		}

		allChunkEmbeddings = append(allChunkEmbeddings, embeddings...)
	}

	questionEmbeddings, err := a.embeddingsWithRetry(ctx, []string{question})
	if err != nil {
		return "", errors.WithStack(err)
	}

	questionEmbedding := questionEmbeddings[0]
	if len(allChunkEmbeddings) != len(chunks) {
		a.logger.Warnf("got %d embeddings for %d chunks", len(allChunkEmbeddings), len(chunks))
	}
	similarities := make([]float64, len(allChunkEmbeddings))
	for i, chunkEmbed := range allChunkEmbeddings {
		similarities[i] = cosineSimilarity(questionEmbedding, chunkEmbed)
	}

	chunksPerSnippet := int(math.Ceil(float64(snippetLength) / float64(chunkSize)))
	snippets := make([]string, 0, numSnippets)
	similaritiesCopy := make([]float64, len(similarities))
	copy(similaritiesCopy, similarities)

	for i := 0; i < numSnippets; i++ {
		bestStartIndex := 0
		bestScore := math.Inf(-1)
		for j := 0; j <= len(similarities)-chunksPerSnippet; j++ {
			windowScores := similaritiesCopy[j : j+chunksPerSnippet]
			windowScore := 0.0
			for _, score := range windowScores {
				windowScore += score
			}
			windowScore /= float64(len(windowScores))

			if windowScore > bestScore {
				bestScore = windowScore
				bestStartIndex = j
			}
		}
		startIndex := bestStartIndex * chunkSize
		endIndex := startIndex + snippetLength
		if endIndex > len(runes) {
			endIndex = len(runes)
		}
		snippets = append(snippets, string(runes[startIndex:endIndex]))
		for k := bestStartIndex; k < bestStartIndex+chunksPerSnippet && k < len(similaritiesCopy); k++ {
			similaritiesCopy[k] = math.Inf(-1)
		}
	}

	var result strings.Builder
	for i, snippet := range snippets {
		if i > 0 {
			result.WriteString("\n\n")
		}
		result.WriteString(fmt.Sprintf("<snippet-%d>\n\n%s\n\n</snippet-%d>",
			i+1, snippet, i+1))
	}

	return result.String(), nil
}

var snippetReg = regexp.MustCompile(`<snippet-\d+>|</snippet-\d+>`)

func replaceSnippetTag(text string) string {
	return snippetReg.ReplaceAllString(text, " ")
}

var domainMap = map[string]bool{
	"github.com":    true,
	"arxiv.org":     true,
	"wikipedia.org": true,
}

func trustedDomain(u string) bool {
	parsedURL, err := url.Parse(u)
	if err != nil {
		return false
	}
	host := strings.ToLower(parsedURL.Host)
	domainSeps := strings.Split(host, ".")
	for i := len(domainSeps) - 1; i >= 0; i-- {
		join := strings.Join(domainSeps[i:], ".")
		if _, exists := domainMap[join]; exists {
			return true
		}
	}
	return false
}
