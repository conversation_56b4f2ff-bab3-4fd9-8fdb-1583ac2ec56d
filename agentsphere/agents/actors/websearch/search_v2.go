package websearch

import (
	"context"
	"fmt"
	"math"
	"sort"
	"strings"
	"sync"
	"time"

	"code.byted.org/gopkg/logs/v2"

	"github.com/cenkalti/backoff/v4"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"golang.org/x/sync/errgroup"

	"code.byted.org/gopkg/logs/v2/log"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris/agentrace"
)

// AggregatedURLInfo holds consolidated information for a unique URL.
type AggregatedURLInfo struct {
	CanonicalURL string
	Description  string
	Title        string
	Snippets     []string
	Appearances  []struct {
		Engine        string
		OriginalQuery string
		OriginalRank  int
	}

	// Calculated Features
	AppearanceCount   int
	EngineDiversity   int
	BestWebEngineRank int
	AvgRank           float64

	RRFScore             float64
	InitialCombinedScore float64 `json:"ics"` // ICS

	// Phase 4 Info (if crawled)
	WasCrawled     bool
	CrawledContent string // Might store extracted text or leave empty if too large

	TraceID string
}

func normalizeScore(score, min, max float64) float64 {
	if max == min {
		return 0.5 // 避免除零，返回中间值
	}
	normalized := (score - min) / (max - min)
	if normalized < 0 {
		return 0
	}
	if normalized > 1 {
		return 1
	}
	return normalized
}

// --- Constants ---
const rrfK = 60.0 // Constant for Reciprocal Rank Fusion

func calculateRRFScore(aggInfo *AggregatedURLInfo) float64 {
	score := 0.0
	for _, app := range aggInfo.Appearances {
		score += 1.0 / (rrfK + float64(app.OriginalRank))
	}
	return score
}

func (a *Agent) WebEngineItemsRank(ctx context.Context, task string, allEngineItems []map[string][]SearchItem) ([]*SearchItem, error) {
	maxPossibleRRFScore := 0.0
	minPossibleRRFScore := math.MaxFloat64

	maxPossibleAppearanceCount := 0.0 // If max 3*n*10 results, max appearances = 3*n
	minPossibleAppearanceCount := math.MaxFloat64
	maxPossibleEngineDiversity := 0.0
	minPossibleEngineDiversity := math.MaxFloat64

	// Define weights (Tune these based on experiments!)
	w1Rrf := 2.5
	w2Appearance := 1.0
	w3Diversity := 0.5

	aggregated := make(map[string]*AggregatedURLInfo)
	for _, sourceItems := range allEngineItems {
		for query, items := range sourceItems {
			for _, item := range items {
				canonicalURL := item.NormalizedURL
				if _, exists := aggregated[canonicalURL]; !exists {
					aggregated[canonicalURL] = &AggregatedURLInfo{
						CanonicalURL: canonicalURL,
						Title:        item.Title,
						Snippets:     make([]string, 0),
						Appearances: make([]struct {
							Engine        string
							OriginalQuery string
							OriginalRank  int
						}, 0),
						BestWebEngineRank: math.MaxInt32, // Initialize best rank to high value
						TraceID:           item.TraceID,
					}
				}
				if len(item.Description) > len(aggregated[canonicalURL].Description) {
					aggregated[canonicalURL].Description = item.Description
				}
				aggInfo := aggregated[canonicalURL]
				aggInfo.Snippets = append(aggInfo.Snippets, item.Snippet)
				aggInfo.Appearances = append(aggInfo.Appearances, struct {
					Engine        string
					OriginalQuery string
					OriginalRank  int
				}{item.WebEngine, query, item.IndexInSearchEngine})
				// Update calculated features incrementally
				aggInfo.AppearanceCount++
				if item.IndexInSearchEngine < aggInfo.BestWebEngineRank {
					aggInfo.BestWebEngineRank = item.IndexInSearchEngine
				}
			}
		}
	}
	for _, aggInfo := range aggregated {
		engines := make(map[string]bool)
		var rankSum float64 = 0

		for _, app := range aggInfo.Appearances {
			engines[app.Engine] = true
			rankSum += float64(app.OriginalRank)
		}

		aggInfo.EngineDiversity = len(engines)
		if aggInfo.AppearanceCount > 0 {
			aggInfo.AvgRank = rankSum / float64(aggInfo.AppearanceCount)
		}
		// Handle case where BestRank wasn't updated (no appearances)
		if aggInfo.BestWebEngineRank == math.MaxInt32 {
			aggInfo.BestWebEngineRank = 0 // Or some other indicator of no rank
		}
	}

	for _, aggInfo := range aggregated {
		aggInfo.RRFScore = calculateRRFScore(aggInfo)
		if aggInfo.RRFScore > maxPossibleRRFScore {
			maxPossibleRRFScore = aggInfo.RRFScore
		}
		if aggInfo.RRFScore < minPossibleRRFScore {
			minPossibleRRFScore = aggInfo.RRFScore
		}
		if float64(aggInfo.AppearanceCount) > maxPossibleAppearanceCount {
			maxPossibleAppearanceCount = float64(aggInfo.AppearanceCount)
		}
		if float64(aggInfo.AppearanceCount) < minPossibleAppearanceCount {
			minPossibleAppearanceCount = float64(aggInfo.AppearanceCount)
		}
		if float64(aggInfo.EngineDiversity) > maxPossibleEngineDiversity {
			maxPossibleEngineDiversity = float64(aggInfo.EngineDiversity)
		}
		if float64(aggInfo.EngineDiversity) < minPossibleEngineDiversity {
			minPossibleEngineDiversity = float64(aggInfo.EngineDiversity)
		}
	}

	for _, aggInfo := range aggregated {
		normRRF := normalizeScore(aggInfo.RRFScore, minPossibleRRFScore, maxPossibleRRFScore)
		normAppearance := normalizeScore(float64(aggInfo.AppearanceCount), minPossibleAppearanceCount, maxPossibleAppearanceCount)
		normDiversity := normalizeScore(float64(aggInfo.EngineDiversity), minPossibleEngineDiversity, maxPossibleEngineDiversity)
		ics := (w1Rrf * normRRF) + (w2Appearance * normAppearance) + (w3Diversity * normDiversity)
		aggInfo.InitialCombinedScore = ics
	}
	rankedList := make([]*AggregatedURLInfo, 0, len(aggregated))
	for _, v := range aggregated {
		rankedList = append(rankedList, v)
	}
	sort.SliceStable(rankedList, func(i, j int) bool {
		return rankedList[i].InitialCombinedScore > rankedList[j].InitialCombinedScore
	})
	var needCrawlList []*AggregatedURLInfo
	for idx, item := range rankedList {
		if idx < 3 || len(item.Description) < 500 {
			needCrawlList = append(needCrawlList, item)
		}
		if len(needCrawlList) >= 5 {
			break
		}
	}

	readURLGroup := &errgroup.Group{}

	for _, agg := range needCrawlList {
		agg := agg
		readURLGroup.Go(func() error {
			defer func() {
				if r := recover(); r != nil {
					logs.CtxError(ctx, "Recovered from panic: %v", r)
				}
			}()
			agg.WasCrawled = true
			ctx := a.newContext(ctx)
			a.ctxInfof(ctx, "data_trace_id: %s, reading url: %s", agg.TraceID, agg.CanonicalURL)
			start := time.Now()
			readContent, err := a.readWebURL(ctx, agg.CanonicalURL)
			if err != nil {
				a.logger.Errorf("failed to read and extract content from %s: %v", agg.CanonicalURL, err)
				return nil
			}
			a.logger.Infof("read url %s cost %s", agg.CanonicalURL, time.Since(start).String())
			a.ctxInfof(ctx, "read content: %s", readContent)
			if len(readContent.Content) > 4*1024*1024 {
				log.V1.CtxWarn(ctx, "content too long, only take the first 4MiB. uri: %s", readContent.URL)
				readContent.Content = readContent.Content[:4*1024*1024]
			}
			if !isHTML(readContent.Content) {
				agg.CrawledContent = readContent.Content
				return nil
			}
			readContent.Content, err = convertToMarkdown(ctx, readContent.Content, readContent.URL)
			if err != nil {
				log.V1.CtxWarn(ctx, "[WebEngineItemsRank] convertToMarkdown err. uri: %s", readContent.URL)
				return nil
			}

			readContent.Content = webContentSimplePostprocess(ctx, readContent.Content)

			agg.CrawledContent = readContent.Content

			return nil
		})
	}
	_ = readURLGroup.Wait()

	var result []*SearchItem
	rankedList = lo.Slice(rankedList, 0, 20)
	var descriptions []string
	for _, agg := range rankedList {
		description = agg.Description
		if agg.WasCrawled && len(agg.CrawledContent) > len(description) {
			description = agg.CrawledContent
		}
		descriptions = append(descriptions, description)
	}

	rankScores, err := rankByBgeMiniCpm(ctx, task, descriptions)
	if err != nil || len(rankScores) != len(rankedList) {
		a.logger.Errorf("[WebEngineItemsRank] rankByBgeMiniCpm err: %v", err)
		rankedList = lo.Slice(rankedList, 0, 10)
		for _, agg := range rankedList {
			description := agg.Description
			if agg.WasCrawled && agg.CrawledContent != "" {
				description = agg.CrawledContent
			}
			result = append(result, &SearchItem{
				Description:   description,
				NormalizedURL: agg.CanonicalURL,
				URL:           agg.CanonicalURL,
				Title:         agg.Title,
			})
		}
		return result, nil
	}

	for idx, agg := range rankedList {
		if rankScores[idx] < -2.5 {
			continue
		}
		description := agg.Description
		if agg.WasCrawled && agg.CrawledContent != "" {
			description = agg.CrawledContent
		}
		result = append(result, &SearchItem{
			Description:         description,
			NormalizedURL:       agg.CanonicalURL,
			URL:                 agg.CanonicalURL,
			Title:               agg.Title,
			IndexInSearchEngine: idx,
			RankScore:           agg.InitialCombinedScore,
			Query2AnswerScore:   rankScores[idx],
		})
	}

	return result, nil
}

func (a *Agent) LarkSearchItemsRank(ctx context.Context, task string, larkSearchItems map[string][]SearchItem) ([]*SearchItem, error) {
	var larkItems []*SearchItem

	group := errgroup.Group{}
	group.SetLimit(5)
	resultChan := make(chan []SearchItem, len(larkSearchItems))

	for query, items := range larkSearchItems {
		query := query
		items := items
		group.Go(func() error {
			defer func() {
				if r := recover(); r != nil {
					logs.CtxError(ctx, "Recovered from panic: %v", r)
				}
			}()
			items = a.evaluateSearch(ctx, query, items, 99999)
			resultChan <- items
			return nil
		})
	}
	group.Wait()

	close(resultChan)
	for r := range resultChan {
		for _, item := range r {
			larkItems = append(larkItems, &item)
		}
	}

	sortedItems := a.larkSourceRank(ctx, larkItems)

	var crawlItems []*SearchItem
	seen := make(map[string]*SearchItem)
	for _, item := range sortedItems {
		normalizedURL := item.NormalizedURL
		seenItem, exists := seen[normalizedURL]
		if exists {
			if strings.Contains(seenItem.Description, item.Description) {
				seenItem.Description = fmt.Sprintf("%s\n\n%s", seenItem.Description, item.Description)
			}
			if seenItem.RankScore < item.RankScore {
				seenItem.RankScore = item.RankScore
			}
			continue
		}
		crawlItems = append(crawlItems, item)
		item.NeedFullContext = true
		seen[normalizedURL] = item
		if len(crawlItems) == 5 {
			break
		}
	}

	readURLGroup := &errgroup.Group{}

	for _, item := range crawlItems {
		item := item
		readURLGroup.Go(func() error {
			defer func() {
				if r := recover(); r != nil {
					logs.CtxError(ctx, "Recovered from panic: %v", r)
				}
			}()
			ctx := a.newContext(ctx)
			a.ctxInfof(ctx, "data_trace_id: %s, reading url: %s", item.TraceID, item.URL)
			start := time.Now()
			readContent, err := a.readLarkURL(ctx, item.URL, item.DocumentID)
			if err != nil {
				a.logger.Errorf("failed to read and extract content from %s: %v", item.URL, err)
				return nil
			}
			a.logger.Infof("read url %s cost %s", item.URL, time.Since(start).String())
			a.ctxInfof(ctx, "read content: %s", readContent)
			if len(readContent.Content) > 4*8*1024 {
				log.V1.CtxWarn(ctx, "content too long. uri: %s", readContent.URL)
				extractedWebContent, err := a.readAndExtract(ctx, task, item, -2.5)
				if err != nil {
					a.logger.Errorf("failed to read and extract content from %s: %v", item.URL, err)
					readContent.Content = readContent.Content[:4*8*1024]
					return nil
				}
				if len(extractedWebContent) == 0 {
					// Might be something error with LLM.
					a.logger.Errorf("extracted content is empty")
					readContent.Content = readContent.Content[:4*8*1024]
					return nil
				}
				readContent.Content = extractedWebContent
			}
			item.Description = readContent.Content
			return nil
		})
	}
	_ = readURLGroup.Wait()

	return crawlItems, nil
}

func (a *Agent) ByteCloudAndArcositeRank(ctx context.Context, byteCloudItems map[string][]SearchItem, arcositeItems map[string][]SearchItem) ([]*SearchItem, error) {
	var byteItems []*SearchItem
	group := errgroup.Group{}
	group.SetLimit(5)
	resultChan := make(chan []SearchItem, len(byteCloudItems)+len(arcositeItems))

	for query, items := range byteCloudItems {
		query := query
		items := items
		group.Go(func() error {
			defer func() {
				if r := recover(); r != nil {
					logs.CtxError(ctx, "Recovered from panic: %v", r)
				}
			}()
			items = a.evaluateSearch(ctx, query, items, 99999)
			resultChan <- items
			return nil
		})
	}

	for query, items := range arcositeItems {
		query := query
		items := items
		group.Go(func() error {
			defer func() {
				if r := recover(); r != nil {
					logs.CtxError(ctx, "Recovered from panic: %v", r)
				}
			}()
			items = a.evaluateSearch(ctx, query, items, 99999)
			resultChan <- items
			return nil
		})
	}
	group.Wait()

	close(resultChan)

	for r := range resultChan {
		for _, item := range r {
			byteItems = append(byteItems, &item)
		}
	}

	sort.SliceStable(byteItems, func(i, j int) bool { return byteItems[i].RankScore > byteItems[j].RankScore })

	if len(byteItems) == 0 {
		return nil, nil
	}

	var crawlItems []*SearchItem
	seen := make(map[string]*SearchItem)
	for _, item := range byteItems {
		seenItem, exists := seen[item.NormalizedURL]
		if exists {
			if strings.Contains(seenItem.Description, item.Description) {
				seenItem.Description = fmt.Sprintf("%s\n\n%s", seenItem.Description, item.Description)
			}
			if seenItem.RankScore < item.RankScore {
				seenItem.RankScore = item.RankScore
			}
			continue
		}
		crawlItems = append(crawlItems, item)
		item.NeedFullContext = true
		seen[item.NormalizedURL] = item
		if len(crawlItems) == 5 {
			break
		}
	}

	readURLGroup := &errgroup.Group{}

	for _, item := range crawlItems {
		item := item
		readURLGroup.Go(func() error {
			ctx := a.newContext(ctx)
			a.ctxInfof(ctx, "data_trace_id: %s, reading url: %s", item.TraceID, item.URL)
			defer func() {
				if r := recover(); r != nil {
					logs.CtxError(ctx, "Recovered from panic: %v", r)
				}
			}()
			start := time.Now()
			readContent, err := a.readLarkURL(ctx, item.URL, item.DocumentID)
			if err != nil {
				a.logger.Errorf("failed to read and extract content from %s: %v", item.URL, err)
				return nil
			}
			a.logger.Infof("read url %s cost %s", item.URL, time.Since(start).String())
			a.ctxInfof(ctx, "read content: %s", readContent)
			if len(readContent.Content) > 4*1024*1024 {
				log.V1.CtxWarn(ctx, "content too long, only take the first 4MiB. uri: %s", readContent.URL)
				readContent.Content = readContent.Content[:4*1024*1024]
			}
			if len(readContent.Content) > len(item.Description) {
				item.Description = readContent.Content
			}
			return nil
		})
	}
	_ = readURLGroup.Wait()

	return crawlItems, nil
}

func (a *Agent) ToutiaoAndToutiaoHybridRank(ctx context.Context, allItems []map[string][]SearchItem) ([]*SearchItem, error) {
	var (
		aiSearchItems     []*SearchItem
		aiSearchItemsLock = &sync.Mutex{}
	)
	group := errgroup.Group{}
	group.SetLimit(5)
	for _, singleSourceItems := range allItems {
		for query, items := range singleSourceItems {
			query := query
			items := items
			group.Go(func() error {
				defer func() {
					if r := recover(); r != nil {
						logs.CtxError(ctx, "Recovered from panic: %v", r)
					}
				}()
				items = a.evaluateSearch(ctx, query, items, 99999)
				aiSearchItemsLock.Lock()
				defer aiSearchItemsLock.Unlock()
				for _, item := range items {
					aiSearchItems = append(aiSearchItems, &item)
				}
				return nil
			})
		}
	}
	err := group.Wait()
	if err != nil {
		return nil, err
	}

	sort.SliceStable(aiSearchItems, func(i, j int) bool { return aiSearchItems[i].RankScore > aiSearchItems[j].RankScore })

	var results []*SearchItem
	seen := make(map[string]bool)
	for _, item := range aiSearchItems {
		_, exists := seen[item.NormalizedURL]
		if exists {
			continue
		}
		results = append(results, item)
		seen[item.NormalizedURL] = true
		if len(results) == 5 {
			break
		}
	}
	return results, nil
}

func (a *Agent) groupRank(ctx context.Context, task string,
	toutiaoItems map[string][]SearchItem,
	toutiaoHybridItems map[string][]SearchItem,
	byteCloudItems map[string][]SearchItem,
	arcositeItems map[string][]SearchItem,
	larkSearchItems map[string][]SearchItem,
	youItems map[string][]SearchItem,
	googleItems map[string][]SearchItem,
	braveItems map[string][]SearchItem,
	isOnlyInternal bool) ([]*SearchItem, error) {
	group := errgroup.Group{}
	var byteItems []*SearchItem
	var webItems []*SearchItem
	var aiSearchItems []*SearchItem
	var larkItems []*SearchItem
	group.Go(func() error {
		defer func() {
			if r := recover(); r != nil {
				logs.CtxError(ctx, "Recovered from panic: %v", r)
			}
		}()
		var err error
		byteItems, err = a.ByteCloudAndArcositeRank(ctx, byteCloudItems, arcositeItems)
		if err != nil {
			a.logger.Errorf("[groupRank] LarkSearchItemsRank err: %v", err)
		}
		return nil
	})
	if !isOnlyInternal {
		group.Go(func() error {
			defer func() {
				if r := recover(); r != nil {
					logs.CtxError(ctx, "Recovered from panic: %v", r)
				}
			}()
			var err error
			webItems, err = a.WebEngineItemsRank(ctx, task, []map[string][]SearchItem{youItems, googleItems, braveItems})
			if err != nil {
				a.logger.Errorf("[groupRank] LarkSearchItemsRank err: %v", err)
			}
			return nil
		})

		group.Go(func() error {
			defer func() {
				if r := recover(); r != nil {
					logs.CtxError(ctx, "Recovered from panic: %v", r)
				}
			}()
			var err error
			aiSearchItems, err = a.ToutiaoAndToutiaoHybridRank(ctx, []map[string][]SearchItem{toutiaoItems, toutiaoHybridItems})
			if err != nil {
				a.logger.Errorf("[groupRank] LarkSearchItemsRank err: %v", err)
			}
			return nil
		})
	}

	group.Go(func() error {
		defer func() {
			if r := recover(); r != nil {
				logs.CtxError(ctx, "Recovered from panic: %v", r)
			}
		}()
		var err error
		larkItems, err = a.LarkSearchItemsRank(ctx, task, larkSearchItems)
		if err != nil {
			a.logger.Errorf("[groupRank] LarkSearchItemsRank err: %v", err)
		}
		return nil
	})
	_ = group.Wait()
	var results []*SearchItem

	var highSmiScoreItems []*SearchItem
	var middleSmiScoreItems []*SearchItem
	var baseLineSmiScoreItems []*SearchItem

	for _, item := range byteItems {
		if item.Query2AnswerScore > 1.5 {
			highSmiScoreItems = append(highSmiScoreItems, item)
			continue
		}
		if item.Query2AnswerScore > 0.5 {
			middleSmiScoreItems = append(middleSmiScoreItems, item)
			continue
		}
		baseLineSmiScoreItems = append(baseLineSmiScoreItems, item)
	}

	for _, item := range larkItems {
		if item.Query2AnswerScore > 1.5 {
			highSmiScoreItems = append(highSmiScoreItems, item)
			continue
		}
		if item.Query2AnswerScore > 0.5 {
			middleSmiScoreItems = append(middleSmiScoreItems, item)
			continue
		}
		baseLineSmiScoreItems = append(baseLineSmiScoreItems, item)
	}

	for _, item := range aiSearchItems {
		if item.Query2AnswerScore > 1.5 {
			highSmiScoreItems = append(highSmiScoreItems, item)
			continue
		}
		if item.Query2AnswerScore > 0.5 {
			middleSmiScoreItems = append(middleSmiScoreItems, item)
			continue
		}
		baseLineSmiScoreItems = append(baseLineSmiScoreItems, item)
	}

	// 内场知识&&旅游攻略等
	if len(highSmiScoreItems) > 10 {
		results = append(results, highSmiScoreItems[0:10]...)
		if len(webItems) > 0 {
			endIndex := 5
			if len(webItems) < 5 {
				endIndex = len(webItems)
			}
			results = append(results, webItems[0:endIndex]...)
		}
		return results, nil
	}

	// 外场知识
	endIndex := 10
	if len(webItems) < 10 {
		endIndex = len(webItems)
	}
	results = append(results, webItems[0:endIndex]...)

	results = append(results, highSmiScoreItems...)

	if len(results) < 15 {
		results = append(results, middleSmiScoreItems...)
	}
	if len(results) < 15 {
		results = append(results, baseLineSmiScoreItems...)
	}

	return lo.Slice(results, 0, 15), nil
}

func (a *Agent) SearchV2(ctx context.Context, task string, queries []string, isOnlyInternal bool) ([]*SearchItem, error) {
	group := errgroup.Group{}
	var items []*SearchItem
	searchResults := make([]*searchResult, 0)
	searchResultsLock := sync.Mutex{}
	for _, query := range queries {
		ctx := a.newContext(ctx)
		a.ctxInfof(ctx, "search query: %s", query)
		query := query
		group.Go(func() error {
			defer func() {
				if r := recover(); r != nil {
					logs.CtxError(ctx, "Recovered from panic: %v", r)
				}
			}()
			span, ctx := agentrace.GetRuntimeTracerFromContext(ctx).StartCustomSpan(ctx, agentrace.SpanTypeStep, "search", agentrace.WithObjectSpanData(map[string]any{"query": query}))
			defer span.Finish()
			searchResult, err := a.devAISearch(ctx, query)
			if err != nil {
				a.logger.Errorf("failed for query %q: %v", query, err)
				return nil
			}
			searchResult.Items = a.cherrypickSearch(ctx, query, searchResult.Items)
			lo.ForEach(searchResult.Items, func(item SearchItem, _ int) {
				a.ctxInfof(ctx, "data_trace_id: %s, cherry pick result: %s", item.TraceID, indentJSON(item))
			})
			lo.ForEach(searchResult.Items, func(item SearchItem, _ int) {
				a.ctxInfof(ctx, "data_trace_id: %s, evaluate result: %s", item.TraceID, indentJSON(item))
			})
			searchResultsLock.Lock()
			searchResults = append(searchResults, searchResult)
			searchResultsLock.Unlock()
			return nil
		})
		group.Go(func() error {
			defer func() {
				if r := recover(); r != nil {
					logs.CtxError(ctx, "Recovered from panic: %v", r)
				}
			}()
			span, ctx := agentrace.GetRuntimeTracerFromContext(ctx).StartCustomSpan(ctx, agentrace.SpanTypeStep, "search", agentrace.WithObjectSpanData(map[string]any{"query": query}))
			defer span.Finish()
			searchResult, err := a.knowledgeBaseSearch(ctx, query)
			if err != nil {
				a.logger.Errorf("failed for query %q: %v", query, err)
				return nil
			}
			searchResult.Items = a.cherrypickSearch(ctx, query, searchResult.Items)
			lo.ForEach(searchResult.Items, func(item SearchItem, _ int) {
				a.ctxInfof(ctx, "data_trace_id: %s, cherry pick result: %s", item.TraceID, indentJSON(item))
			})
			lo.ForEach(searchResult.Items, func(item SearchItem, _ int) {
				a.ctxInfof(ctx, "data_trace_id: %s, evaluate result: %s", item.TraceID, indentJSON(item))
			})
			searchResultsLock.Lock()
			searchResults = append(searchResults, searchResult)
			searchResultsLock.Unlock()
			return nil
		})
		group.Go(func() error {
			defer func() {
				if r := recover(); r != nil {
					logs.CtxError(ctx, "Recovered from panic: %v", r)
				}
			}()
			var err error
			var toutiaoSearchResult *searchResult
			err = backoff.Retry(func() error {
				toutiaoSearchResult, err = a.toutiaoHybridSearch(ctx, query, false)
				if err != nil {
					return err
				}
				return nil
			}, backoff.WithMaxRetries(backoff.NewExponentialBackOff(), 3))
			if err != nil {
				return nil
			}
			searchResultsLock.Lock()
			searchResults = append(searchResults, toutiaoSearchResult)
			searchResultsLock.Unlock()
			return nil
		})
	}
	err := group.Wait()
	if err != nil {
		return nil, errors.WithStack(err)
	}
	// 内场搜索
	// 飞书 AI 搜
	larkSearchItems := make(map[string][]SearchItem)
	// 内场
	byteCloudItems := make(map[string][]SearchItem)
	arcositeItems := make(map[string][]SearchItem)

	// 外场搜索
	// 外场通用搜索引擎
	googleItems := make(map[string][]SearchItem)
	youItems := make(map[string][]SearchItem)
	braveItems := make(map[string][]SearchItem)
	// 头条站内搜索
	toutiaoItems := make(map[string][]SearchItem)

	// AI Search
	toutiaoHybridItems := make(map[string][]SearchItem)
	for _, r := range searchResults {
		googleIndex := 1
		braveIndex := 1
		youIndex := 1
		toutiaoIndex := 1
		toutiaoHybridIndex := 1
		arcositeIndex := 1
		byteCloudIndex := 1
		larkSearchIndex := 1
		for _, resultItem := range r.Items {
			switch resultItem.Source {
			case webSearchSource:
				switch resultItem.WebEngine {
				case "brave":
					if _, ok := braveItems[r.Query]; !ok {
						braveItems[r.Query] = make([]SearchItem, 0)
					}
					resultItem.IndexInSearchEngine = braveIndex
					braveItems[r.Query] = append(braveItems[r.Query], resultItem)
					braveIndex++
				case "google":
					if _, ok := googleItems[r.Query]; !ok {
						googleItems[r.Query] = make([]SearchItem, 0)
					}
					resultItem.IndexInSearchEngine = googleIndex
					googleItems[r.Query] = append(googleItems[r.Query], resultItem)
					googleIndex++
				case "you":
					if _, ok := youItems[r.Query]; !ok {
						youItems[r.Query] = make([]SearchItem, 0)
					}
					resultItem.IndexInSearchEngine = youIndex
					youItems[r.Query] = append(youItems[r.Query], resultItem)
					youIndex++
				case "toutiao":
					if _, ok := toutiaoItems[r.Query]; !ok {
						toutiaoItems[r.Query] = make([]SearchItem, 0)
					}
					resultItem.IndexInSearchEngine = toutiaoIndex
					toutiaoItems[r.Query] = append(toutiaoItems[r.Query], resultItem)
					toutiaoIndex++
				}
			case toutiaoHybridSource:
				if _, ok := toutiaoHybridItems[r.Query]; !ok {
					toutiaoHybridItems[r.Query] = make([]SearchItem, 0)
				}
				resultItem.IndexInSearchEngine = toutiaoHybridIndex
				toutiaoHybridItems[r.Query] = append(toutiaoHybridItems[r.Query], resultItem)
				toutiaoHybridIndex++
			case arcositeSource:
				if _, ok := arcositeItems[r.Query]; !ok {
					arcositeItems[r.Query] = make([]SearchItem, 0)
				}
				resultItem.IndexInSearchEngine = arcositeIndex
				arcositeItems[r.Query] = append(arcositeItems[r.Query], resultItem)
				arcositeIndex++
			case bytecloudSource:
				if _, ok := byteCloudItems[r.Query]; !ok {
					byteCloudItems[r.Query] = make([]SearchItem, 0)
				}
				resultItem.IndexInSearchEngine = byteCloudIndex
				byteCloudItems[r.Query] = append(byteCloudItems[r.Query], resultItem)
				byteCloudIndex++
			case larkSearchSource:
				if _, ok := larkSearchItems[r.Query]; !ok {
					larkSearchItems[r.Query] = make([]SearchItem, 0)
				}
				resultItem.IndexInSearchEngine = larkSearchIndex
				larkSearchItems[r.Query] = append(larkSearchItems[r.Query], resultItem)
				larkSearchIndex++
			}
			if isOnlyInternal && (resultItem.Source == webSearchSource || resultItem.Source == toutiaoHybridSource) {
				continue
			}
			items = append(items, &resultItem)
		}
	}
	rankedItems, err := a.groupRank(ctx, task, toutiaoItems, toutiaoHybridItems, byteCloudItems, arcositeItems, larkSearchItems, youItems, googleItems, braveItems, isOnlyInternal)
	if err != nil {
		return nil, err
	}
	// Block here until get the lock.
	// Until now, frontend display search result when fetched search result.
	// So the created & running status is not important.
	a.toolCallLock.Lock()
	stepReporter := a.createAndRunningStep(map[string]any{"SearchRequests": queries})
	stepReportDesc := fmt.Sprintf("正在搜索：%v", task)
	searchToolCallFinished := stepReporter.ReportSearchToolCall(ctx, stepReportDesc, queries)
	searchToolCallFinished(selectSearchItems(rankedItems, items, 12)) // Only display fixed number of items.
	a.toolCallLock.Unlock()
	return rankedItems, nil

}

func selectSearchItems(rankedItems, fullItems []*SearchItem, maxCount int) []*SearchItem {
	seen := make(map[string]bool)
	var result []*SearchItem

	for _, item := range rankedItems {
		if len(result) >= maxCount {
			break
		}
		if !seen[item.NormalizedURL] {
			result = append(result, item)
			seen[item.NormalizedURL] = true
		}
	}

	for _, item := range fullItems {
		if len(result) >= maxCount {
			break
		}
		// 先跳过所有 lark 的文档，本意是跳过 aime 创建的文档。但是这个接口调用层级太深，不能直接使用。这里再调用一遍也没有太大必要。
		if item.Source == larkSearchSource {
			continue
		}
		if !seen[item.NormalizedURL] {
			result = append(result, item)
			seen[item.NormalizedURL] = true
		}
	}
	return result
}
