package websearch

import (
	"fmt"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"golang.org/x/exp/slices"
)

func RecallKnowledgebase(ctx *iris.AgentRunContext, query string, maxCount int) ([]string, error) {
	datasetID := cast.ToString(ctx.Parameters[entity.RuntimeParametersDatasetID])
	ctx.GetLogger().Infof("datasetID: %s,query: %s", datasetID, query)
	if len(datasetID) == 0 {
		return nil, nil
	}
	segments, err := ctx.GetAPIClient().RecallKnowledgeBase(ctx, datasetID, query, int64(maxCount)*2)
	if err != nil {
		return nil, err
	}
	rankScore, err := rankByBgeMiniCpm(ctx, query, lo.Map(segments, func(segment *nextagent.RecallSegment, _ int) string {
		return fmt.Sprintf("Title: %s\nContent: %s\n", segment.Title, segment.Content)
	}))
	if err != nil {
		return nil, err
	}
	var threshold = -3.5
	filteredSegments := lo.Filter(segments, func(segment *nextagent.RecallSegment, index int) bool {
		return rankScore[index] > threshold
	})
	pos := make([]int, 0, len(filteredSegments))
	for i := 0; i < len(filteredSegments); i++ {
		pos = append(pos, i)
	}
	slices.SortFunc(pos, func(i, j int) int {
		if rankScore[pos[i]] > rankScore[pos[j]] {
			return -1
		}
		if rankScore[pos[i]] < rankScore[pos[j]] {
			return 1
		}
		return 0
	})
	topSegments := lo.Slice(pos, 0, maxCount)
	result := lo.Map(topSegments, func(segmentIdx int, _ int) string {
		segment := filteredSegments[segmentIdx]
		return fmt.Sprintf("Title: %s\nContent Snippet: %s\nFull Document in URL: %s\n", segment.Title, segment.Content, segment.URL)
	})
	return result, nil
}
