package agenttool

import (
	"encoding/json"
	"fmt"
	"reflect"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions"
	controltool "code.byted.org/devgpt/kiwis/agentsphere/agents/actions/control"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actors"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/knowledges"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/prompt"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/util"
	"code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/devgpt/kiwis/lib/conv"
)

type AgentToolOption struct {
	//定义和描述
	Name        string `json:"name" mapstructure:"name"`
	Description string `json:"description" mapstructure:"description"`

	//Sytstem Prompt
	Persona          string                     `json:"persona" mapstructure:"persona"`
	SystemKnowledges []knowledges.KnowledgeItem `json:"system_knowledges" mapstructure:"system_knowledges"`

	//可用工具集
	Toolsets []string `json:"toolsets" mapstructure:"toolsets"`

	//候选知识
	CandidateKnowledges []knowledges.KnowledgeItem `json:"candidate_knowledges" mapstructure:"candidate_knowledges"`

	//可视化展示
	ParentStep *iris.AgentRunStep  `json:"parent_step" mapstructure:"parent_step"`
	LLMHook    *prompt.LLMCallHook `json:"llm_hook" mapstructure:"llm_hook"`
}

func NewAgentTool[T any](
	input func(run *iris.AgentRunContext, args T) string,
	option func(run *iris.AgentRunContext, args T) actors.RunOptions,
	opt AgentToolOption) iris.Action {
	return actions.ToTool(opt.Name, opt.Description, func(c *iris.AgentRunContext, args T) (controltool.ConclusionOutput, error) {
		agentTool := CreateAgentTool(c, opt)
		return agentTool.Run(c, input(c, args), option(c, args)), nil
	})
}

func NewAgentToolV2[T any](input func(run *iris.AgentRunContext, args T) string, opt AgentToolOption) iris.Action {
	var in T
	var out controltool.ConclusionOutput
	task := actions.NewTool(actions.NewToolOption{
		Name:        opt.Name,
		Description: opt.Description,
		Input:       util.TypeToJSONSchema(reflect.TypeOf(in)),
		Output:      util.TypeToJSONSchema(reflect.TypeOf(out)),
		Impl: func(ctx *iris.AgentRunContext, step *iris.AgentRunStep) (map[string]any, error) {
			jsonStr, err := json.Marshal(step.Inputs)
			if err != nil {
				return nil, iris.NewRecoverable(fmt.Errorf("failed to parse agent tool arguments: %w", err))
			}
			err = json.Unmarshal(jsonStr, &in)
			if err != nil {
				// models can output json fields with non-matching types, causing unmarshal failure
				return nil, iris.NewRecoverable(fmt.Errorf("failed to parse agent tool arguments: %w", err))
			}
			agentTool := CreateAgentTool(ctx, opt)

			out = agentTool.Run(ctx, input(ctx, in), actors.RunOptions{ParentExecutor: step.ExecutorAgent})

			outputs := make(map[string]any)
			data, _ := json.Marshal(out)
			unmarshalErr := json.Unmarshal(data, &outputs)
			if unmarshalErr != nil {
				ctx.GetLogger().Error("failed to unmarshal tool output", "error", unmarshalErr)
			}
			return outputs, nil
		},
	})
	return task
}

func CreateAgentTool(c *iris.AgentRunContext, opt AgentToolOption) *Agent {
	return New(c, CreateOption{
		Name:                opt.Name,
		Variant:             c.GetConfig().GetVariantByScene(opt.Name),
		Persona:             opt.Persona,
		Toolsets:            opt.Toolsets,
		ExecutionTrace:      "",
		SystemKnowledges:    opt.SystemKnowledges,
		CandidateKnowledges: opt.CandidateKnowledges,
		LLMHooks:            opt.LLMHook,
		DisableSummarizer:   conv.DefaultAny[bool](c.Parameters[entity.RuntimeParametersDisableToolSummarizer]),
		ActorStep:           opt.ParentStep,
	})
}
