package larkagenttool

import (
	"fmt"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/workspace"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actors"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/agenttool"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/knowledges"
	planactentity "code.byted.org/devgpt/kiwis/agentsphere/agents/planact/entity"
	"github.com/samber/lo"
)

type LarkToolArgs struct {
	Persona   string `json:"persona" mapstructure:"persona" description:"The persona of the lark agent. The description should give out background context and the specific goals, but not detailed datapoints or libraries to use."`
	Task      string `json:"task" mapstructure:"task" description:"A self contained task prompt that can be completed by the lark agent. The description should give out background context and the specific goals, but not detailed datapoints or libraries to use."`
	Reference string `json:"reference" mapstructure:"reference" description:"the important results (files, images, deployed url, etc.) to be refered, in markdown format"`
}

func larkInputFunc(run *iris.AgentRunContext, args LarkToolArgs) string {
	input := fmt.Sprintf("task: %s\nreference: %s\n", args.Task, args.Reference)
	refs := iris.ParseReference(args.Reference)
	ws := workspace.GetWorkspace(run)
	files, _ := ws.Editor.ReadMarkdownFiles(workspace.ReadMarkdownFilesArgs{
		Paths: lo.Map(refs, func(ref iris.ReferenceItem, _ int) string {
			return ref.URI
		}),
	})
	if len(files.Files) > 0 {
		input += "Here are some related files, fell free to read another files if needed:\n"
		for _, file := range files.Files {
			input += fmt.Sprintf("\n[file path: %s]\n%s\n", file.FileName, file.Content)
		}
	}
	return input
}

func larkOptionFunc(run *iris.AgentRunContext, args LarkToolArgs) actors.RunOptions {
	return actors.RunOptions{DynamicPersona: args.Persona}
}

func NewLarkAgentTool(run *iris.AgentRunContext, step *iris.AgentRunStep) iris.Action {
	sysKnowledgesID := []string{
		"dynamic_10_lark_creation",
		"dynamic_5",
		"dynamic_25",
	}
	referenceStore := iris.RetrieveStoreByKey[planactentity.ReferenceStore](run, planactentity.ReferenceStoreKey)
	if len(referenceStore.SearchedRef.List) > 0 {
		sysKnowledgesID = []string{
			"dynamic_10_lark_creation",
			"dynamic_5_citation",
			"dynamic_25",
		}
	}
	return agenttool.NewAgentTool[LarkToolArgs](
		larkInputFunc,
		larkOptionFunc,
		agenttool.AgentToolOption{
			Name: "lark",
			Description: fmt.Sprintf(`Lark can do the following things:
1. Lark document/sheet downloader
2. Generate a Lark/Feishu document from a html file or markdown file
3. Convert csv、xlsx、xls files to Lark/Feishu sheets/table or Lark/Feishu Base. (将csv、xlsx、xls文件转换为飞书表格或多维表格)
### Hints ###
You have no need to repeat the whole content of files, just use the reference links to refer to the files. For example,
%sparam="reference"
- [file_a](./aaa.md)
- [file_b](./bbb.md)
%s
%sparam="task"
Please create a Feishu document according to the given files
%s
`, "```", "```", "```", "```"),
			Toolsets: []string{
				"files",
				"terminal",
				"deploy",
				"lark",
			},
			CandidateKnowledges: knowledges.LoadKnowledge(),
			SystemKnowledges:    knowledges.FilterKnowledgeItems(knowledges.LoadKnowledge(), sysKnowledgesID),
			ParentStep:          step,
			Persona:             "你是一位专业的报告撰写人，精通各行各业各种场景的报告撰写，你可能会拿到一些零散的信息，比如markdown文件，请不要原封不动的生成飞书文档，要发挥你的特长，将零散的信息整合成结构清晰、论点明确、图文并茂的深度分析报告。你注重报告的专业性和可读性，力求为读者提供最佳的阅读体验。",
		})
}
