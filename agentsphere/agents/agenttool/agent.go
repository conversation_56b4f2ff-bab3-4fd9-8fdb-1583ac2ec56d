package agenttool

import (
	"embed"
	"fmt"
	"strings"

	"github.com/samber/lo"

	controltool "code.byted.org/devgpt/kiwis/agentsphere/agents/actions/control"
	action_prompts "code.byted.org/devgpt/kiwis/agentsphere/agents/actions/prompts"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actors"
	agents "code.byted.org/devgpt/kiwis/agentsphere/agents/core"
	agententity "code.byted.org/devgpt/kiwis/agentsphere/agents/entity"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris/agentrace"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/knowledges"
	planactentity "code.byted.org/devgpt/kiwis/agentsphere/agents/planact/entity"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/prompt"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/util"
	condenser "code.byted.org/devgpt/kiwis/agentsphere/memory/condenser/impl"
	"code.byted.org/devgpt/kiwis/copilotstack/agent/framework"
	"code.byted.org/devgpt/kiwis/lib/mapstructure"
)

var (
	Identifier = "mewtwo_run"
	parameters = []actors.ParameterDescription{
		{
			Name:        "task",
			Description: "A self contained task prompt that can be completed by the agent. The description should give out background context and the specific goals, but not detailed datapoints or libraries to use.",
		},
		{
			Name:        "persona",
			Description: "Persona to put at the very first of the system prompt. Describe the agent's role, capabilities, and any other relevant information",
		},
		{
			Name:        "tools_selection_thought",
			Description: "Iterate through the toolset list ONE BY ONE, consider each toolset's suitability for the task. Format: `<toolset_name>:yes/maybe/nice to have/no`. Do not skip any toolset.",
		},
		{
			Name:        "tools",
			Description: "Tools given to the created agent in comma separated strings, e.g. `tool1,tool2`. Include all toolsets possible to be used by the agent",
		},
	}
	//go:embed prompts
	prompts   embed.FS
	promptset = prompt.MustNewPromptSet(prompts, "prompts")

	_ = lo.Must(promptset.ExecutePrompt("description", "", nil))
)

type DynamicAgentStore struct {
	CurrentTask string
}

type Agent struct {
	actors.BaseActor
	ProgreAct *agents.ProgreActAgent
	Condenser *condenser.AutoSummarizer
	Variant   string

	Persona             string
	SystemKnowledges    []knowledges.KnowledgeItem
	CandidateKnowledges []knowledges.KnowledgeItem

	ContextParts ContextParts

	Toolsets []Toolset

	PromptCache *agents.PromptCache
}

var _ actors.Actor = &Agent{}

type CreateOption struct {
	Name      string
	MaxSteps  int
	LLMHooks  *prompt.LLMCallHook
	ActorStep *iris.AgentRunStep

	// dynamically decided by model
	Variant             string
	Persona             string
	SystemKnowledges    []knowledges.KnowledgeItem
	CandidateKnowledges []knowledges.KnowledgeItem
	Toolsets            []string
	TaskContext         string
	ExecutionTrace      string

	DisableSummarizer bool // whether to summarize tool calls, defaults to true, disabled for testing&benchmarking
}

func New(run *iris.AgentRunContext, opt CreateOption) *Agent {
	toolsets, _ := GetToolsets(run)
	userDefineToolsets, _ := GetUserDefineToolsets(run)
	plannerStore := iris.RetrieveStoreByKey[planactentity.PlannerStore](run, planactentity.PlannerStoreKey)
	toolsets = append(toolsets, userDefineToolsets...)
	// toolsets和userDefineToolsets一起放进去过滤
	tools := lo.Filter(append(toolsets, userDefineToolsets...), func(toolset Toolset, _ int) bool {
		_, ok := lo.Find(opt.Toolsets, func(name string) bool {
			return toolset.Identifier == name
		})
		return ok
	})

	tools = append(tools, GetBuildInMCP(run)...)
	info := iris.AgentInfo{
		Identifier: lo.Ternary(opt.Name == "", Identifier, opt.Name),
		Desc: lo.Must(promptset.ExecutePrompt("description", opt.Variant, map[string]any{
			"Toolsets":           toolsets,
			"UserDefineToolsets": userDefineToolsets,
		})),
	}
	reactor := agents.NewProgreActAgent(agents.ProgreActAgentConfig{
		Info:              info,
		Tools:             BuildTools(run, tools, opt.CandidateKnowledges),
		MaxSteps:          opt.MaxSteps,
		MaxFailures:       3,
		DisableSummarizer: opt.DisableSummarizer,
		LLMHook:           opt.LLMHooks,
	})
	agent := &Agent{
		BaseActor: actors.BaseActor{
			AgentInfo:  info,
			Parameters: parameters,
		},
		Variant:             opt.Variant,
		Persona:             opt.Persona,
		SystemKnowledges:    opt.SystemKnowledges,
		CandidateKnowledges: opt.CandidateKnowledges,
		ContextParts: ContextParts{
			TaskContext:    opt.TaskContext,
			ExecutionTrace: opt.ExecutionTrace,
			Locale:         agents.GetUserLanguage(run.Parameters),
			UserRequest:    strings.TrimSpace(plannerStore.Requirements),
		},
		Condenser: condenser.NewAutoSummarizer(&condenser.NewAutoSummarizerConfig{
			Name:    Identifier,
			LLM:     run.GetLLM(),
			Config:  run.Config,
			Variant: opt.Variant,
		}),
		Toolsets: tools,
	}
	reactor.Composer = agent.Compose
	reactor.Condenser = agent.Condenser
	agent.ProgreAct = reactor

	agent.ActorStep = opt.ActorStep

	if opt.Variant == agententity.VariantExpert {
		agent.PromptCache = &agents.PromptCache{
			MaxCachePoints:       4,
			CacheType:            "ephemeral",
			NoCacheLastNMessages: 1,
			NewCachePointChars:   5000,
			CachePoints:          map[int]bool{},
		}
	}

	return agent
}

func (a *Agent) Compose(run *iris.AgentRunContext, previousSteps []*iris.AgentRunStep, state *agents.ProgreActState) []*framework.ChatMessage {
	systemMessages, err := prompt.ComposeVaryMessages([]prompt.ComposeVaryMessageOption{
		prompt.WithSystemMessage(promptset.GetTemplate("system", a.Variant), util.ValueToMap(a.GetSystemParts(run))),
		prompt.WithUserMessage(promptset.GetTemplate("user", a.Variant), util.ValueToMap(a.ContextParts)),
	})
	cacheType := ""
	// Use ephemeral cache for agent using claude.
	if a.Variant == agententity.VariantExpert {
		cacheType = "ephemeral"
	}
	if err != nil {
		run.GetLogger().Errorf("failed to compose messages: %s", err)
	}

	a.Condenser.SetPrefix(run, systemMessages) // 压缩时估算前序消息 token 数防止超 token
	workspaceStructure := GetWorkspaceStructure(run)
	messages, err := prompt.ComposeVaryMessages([]prompt.ComposeVaryMessageOption{
		prompt.WithSystemMessage(promptset.GetTemplate("system", a.Variant), util.ValueToMap(a.GetSystemParts(run))),
		prompt.WithPromptCache(
			prompt.WithUserMessage(promptset.GetTemplate("user", a.Variant), util.ValueToMap(a.ContextParts)),
			cacheType,
		),
		action_prompts.WithReActToolHistory(run, previousSteps, promptset.GetTemplate("thought", a.Variant), promptset.GetTemplate("user", a.Variant), nil, a.Condenser),
		prompt.WithUserMessage(promptset.GetTemplate("last_message", a.Variant), map[string]any{
			"Workspace":             workspaceStructure.Workspace,
			"WorkspaceRepositories": workspaceStructure.WorkspaceRepositories,
			"RootStructure":         workspaceStructure.RootStructure,
		}),
	})
	if err != nil {
		run.GetLogger().Errorf("failed to compose messages: %s", err)
	}
	if a.PromptCache != nil {
		a.PromptCache.UpdateCachePoints(messages)
	}
	return messages
}

func (a *Agent) Run(run *iris.AgentRunContext, input string, options actors.RunOptions) (result controltool.ConclusionOutput) {
	if options.ParentExecutor != "" {
		actorSteps := lo.Filter(run.State.Steps, func(step *iris.AgentRunStep, _ int) bool {
			return step.ExecutorAgent == options.ParentExecutor
		})
		composer := action_prompts.WithReActToolHistory(run, actorSteps, promptset.GetTemplate("thought", a.Variant), promptset.GetTemplate("user", a.Variant), nil, nil)
		messages, _ := composer()
		input += fmt.Sprintf("\n\n%v", messages)
		// TODO:
	}

	span, ctx := agentrace.GetRuntimeTracerFromContext(run).
		StartCustomSpan(
			run,
			agentrace.SpanTypeStep,
			a.Name(),
			agentrace.WithObjectSpanData(
				map[string]any{
					"input": input,
				},
			),
		)
	a.ContextParts.TaskContext = input
	if len(options.DynamicPersona) > 0 {
		a.Persona = options.DynamicPersona
	}
	run = run.WithContext(ctx)
	defer func() {
		span.UpdateData(agentrace.NewObjectSpanData(
			map[string]any{
				"result": result,
			},
		))
		span.Finish()
	}()

	err := a.ProgreAct.RunWithOption(run, &agents.ProgreActAgentRunOption{
		Step: a.ActorStep,
	})
	if err != nil {
		// the actor need to return a iris.NewFatal(err) with a non-nil error to send a fatal error message to user
		isFatal := iris.IsFatal(err)
		result = controltool.ConclusionOutput{
			Content:    err.Error(),
			Evaluation: lo.Ternary(isFatal, controltool.ConclusionEvaluationFatal, controltool.ConclusionEvaluationFailed),
		}
	}

	lastStep := run.State.LastStep()
	mapstructure.Decode(lastStep.Outputs, &result)
	return result
}
