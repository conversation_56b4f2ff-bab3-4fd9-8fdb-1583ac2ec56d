package checkpointagenttool

import (
	"fmt"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/workspace"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actors"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/agenttool"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/knowledges"
	planactentity "code.byted.org/devgpt/kiwis/agentsphere/agents/planact/entity"
	"github.com/samber/lo"
)

type CheckpointToolArgs struct {
	Requirements string `json:"requirements" mapstructure:"requirements" description:"The user requirements or task description that needs to be analyzed for generating checkpoints"`
	Context      string `json:"context" mapstructure:"context" description:"Additional context information about the task, including background, constraints, and environment details"`
	Reference    string `json:"reference" mapstructure:"reference" description:"Reference materials, files, or documentation that should be considered when generating checkpoints, in markdown format"`
}

func checkpointInputFunc(run *iris.AgentRunContext, args CheckpointToolArgs) string {
	input := fmt.Sprintf("requirements: %s\ncontext: %s\nreference: %s\n", args.Requirements, args.Context, args.Reference)

	// Parse reference files if provided
	refs := iris.ParseReference(args.Reference)
	ws := workspace.GetWorkspace(run)
	files, _ := ws.Editor.ReadMarkdownFiles(workspace.ReadMarkdownFilesArgs{
		Paths: lo.Map(refs, func(ref iris.ReferenceItem, _ int) string {
			return ref.URI
		}),
	})

	if len(files.Files) > 0 {
		input += "Here are some related reference files:\n"
		for _, file := range files.Files {
			input += fmt.Sprintf("\n[file path: %s]\n%s\n", file.FileName, file.Content)
		}
	}

	return input
}

func checkpointOptionFunc(run *iris.AgentRunContext, args CheckpointToolArgs) actors.RunOptions {
	return actors.RunOptions{
		DynamicPersona: "你是一位专业的任务分析专家，擅长将复杂的用户需求分解为具体的、可验证的检查项。你的目标是确保任务规划和执行过程中不遗漏关键要点。",
	}
}

func NewCheckpointAgentTool(run *iris.AgentRunContext, step *iris.AgentRunStep) iris.Action {
	sysKnowledgesID := []string{
		"dynamic_5",
		"dynamic_25",
	}

	referenceStore := iris.RetrieveStoreByKey[planactentity.ReferenceStore](run, planactentity.ReferenceStoreKey)
	if len(referenceStore.SearchedRef.List) > 0 {
		sysKnowledgesID = []string{
			"dynamic_5_citation",
			"dynamic_25",
		}
	}

	return agenttool.NewAgentTool[CheckpointToolArgs](
		checkpointInputFunc,
		checkpointOptionFunc,
		agenttool.AgentToolOption{
			Name: "checkpoint",
			Description: fmt.Sprintf(`Checkpoint tool analyzes user requirements and generates comprehensive checkpoints for task planning and review. It can:

1. **Requirement Analysis**: Break down complex user requirements into specific, actionable items
2. **Checkpoint Generation**: Create detailed verification points for each phase of task execution
3. **Quality Assurance**: Ensure no critical aspects are missed during planning and execution
4. **Review Guidelines**: Provide structured criteria for task completion verification

### Usage Examples ###

%sparam="requirements"
Create a web application with user authentication and data visualization
%s

%sparam="context"
The application should be built using React frontend and Node.js backend, deployed on AWS
%s

%sparam="reference"
- [API specification](./api-spec.md)
- [Design mockups](./designs/)
%s

The tool will generate:
- **Planning Checkpoints**: Key considerations for initial planning phase
- **Implementation Checkpoints**: Critical verification points during development
- **Testing Checkpoints**: Quality assurance and validation criteria
- **Deployment Checkpoints**: Production readiness verification points
- **Completion Checkpoints**: Final delivery and acceptance criteria

These checkpoints help Planner agents ensure comprehensive task coverage and provide reviewers with clear verification criteria.`, "```", "```", "```", "```", "```", "```"),
			Toolsets: []string{
				"files",
				"terminal",
			},
			CandidateKnowledges: knowledges.LoadKnowledge(),
			SystemKnowledges:    knowledges.FilterKnowledgeItems(knowledges.LoadKnowledge(), sysKnowledgesID),
			ParentStep:          step,
			Persona: `你是一位专业的任务分析和质量保证专家，具有以下核心能力：

## 核心职责
1. **需求分析专家**: 深入理解用户需求，识别显性和隐性要求
2. **检查点设计师**: 创建全面、具体、可验证的检查项
3. **质量保证顾问**: 确保任务执行过程中的质量控制
4. **风险识别专家**: 预见潜在问题并提供预防措施

## 工作原则
- **全面性**: 覆盖任务的所有关键方面，不遗漏重要环节
- **具体性**: 每个检查项都应该明确、可操作、可验证
- **层次性**: 按照任务执行的不同阶段组织检查项
- **实用性**: 检查项应该对实际执行有指导意义

## 输出格式
生成的检查项应该包含：
- **规划阶段检查项**: 确保前期准备充分
- **执行阶段检查项**: 保证实施过程质量
- **验证阶段检查项**: 确认交付物符合要求
- **完成阶段检查项**: 验证最终目标达成

每个检查项都应该包含：
- 检查内容描述
- 验证标准
- 预期结果
- 风险提示（如适用）`,
		})
}
