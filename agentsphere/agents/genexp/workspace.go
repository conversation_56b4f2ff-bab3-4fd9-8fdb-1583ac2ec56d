package genexp

import (
	"fmt"
	"io/fs"
	"os"
	"path/filepath"
	"sort"
	"strings"
)

type WorkspaceSumOption struct {
	MaxFiles    int  // 每个目录下最大文件数量
	MaxDepth    int  // 最大层级深度
	ShowGitDirs bool // 是否展示 Git 目录详情
}

// DefaultOption 返回默认配置
func DefaultOption() WorkspaceSumOption {
	return WorkspaceSumOption{
		MaxFiles:    20,
		MaxDepth:    4,
		ShowGitDirs: false,
	}
}

// GetWorkspaceStructureSummary 生成工作目录结构摘要
func GetWorkspaceStructureSummary(dir string, opt WorkspaceSumOption) (string, error) {
	if opt.MaxFiles <= 0 {
		opt.MaxFiles = 20
	}
	if opt.MaxDepth <= 0 {
		opt.MaxDepth = 4
	}

	absDir, err := filepath.Abs(dir)
	if err != nil {
		return "", fmt.Errorf("failed to get abs path %w", err)
	}

	info, err := os.Stat(absDir)
	if err != nil {
		return "", fmt.Errorf("failed to stat dir: %w", err)
	}

	if !info.IsDir() {
		return "", fmt.Errorf("not a dir: %s", absDir)
	}

	var result strings.Builder
	result.WriteString(fmt.Sprintf("Directory Structure Summary: %s\n", dir))
	result.WriteString(strings.Repeat("=", 50) + "\n")

	err = buildStructure(&result, absDir, "", 0, opt)
	if err != nil {
		return "", fmt.Errorf("failed to get workspace structure summary: %w", err)
	}

	return result.String(), nil
}

// buildStructure 递归构建目录结构
func buildStructure(result *strings.Builder, currentDir, prefix string, depth int, opt WorkspaceSumOption) error {
	if depth >= opt.MaxDepth {
		result.WriteString(prefix + "└── ... (too many levels)\n")
		return nil
	}

	entries, err := os.ReadDir(currentDir)
	if err != nil {
		result.WriteString(prefix + "└── [failed to read dir: " + err.Error() + "]\n")
		return nil
	}

	// 检查是否为 Git 目录
	if containsGitDir(entries) && !opt.ShowGitDirs {
		result.WriteString(prefix + "└── [Git Repository Directory]\n")
		return nil
	}

	// 分离文件和目录
	var dirs, files []fs.DirEntry
	for _, entry := range entries {
		// if strings.HasPrefix(entry.Name(), ".") && entry.Name() != ".gitignore" {
		// 	continue // 跳过隐藏文件（除了 .gitignore）
		// }
		if entry.IsDir() {
			dirs = append(dirs, entry)
		} else {
			files = append(files, entry)
		}
	}

	// 排序
	sort.Slice(dirs, func(i, j int) bool { return dirs[i].Name() < dirs[j].Name() })
	sort.Slice(files, func(i, j int) bool { return files[i].Name() < files[j].Name() })

	allEntries := append(dirs, files...)
	totalCount := len(allEntries)

	// 检查文件数量限制
	if totalCount > opt.MaxFiles {
		allEntries = allEntries[:opt.MaxFiles]
		result.WriteString(prefix + fmt.Sprintf("├── [First %d entries, total %d entries]\n", opt.MaxFiles, totalCount))
	}

	for i, entry := range allEntries {
		isLast := i == len(allEntries)-1
		var connector, nextPrefix string

		if isLast {
			connector = "└── "
			nextPrefix = prefix + "    "
		} else {
			connector = "├── "
			nextPrefix = prefix + "│   "
		}

		if entry.IsDir() {
			result.WriteString(prefix + connector + entry.Name() + "/\n")
			subDir := filepath.Join(currentDir, entry.Name())
			buildStructure(result, subDir, nextPrefix, depth+1, opt)
		} else {
			result.WriteString(prefix + connector + entry.Name() + "\n")
		}
	}

	// 如果有被截断的文件，添加提示
	if totalCount > opt.MaxFiles {
		result.WriteString(prefix + "└── ... ( " + fmt.Sprintf("%d", totalCount-opt.MaxFiles) + " entries remained)\n")
	}

	return nil
}

// containsGitDir 检查目录列表是否包含 .git 目录
func containsGitDir(entries []fs.DirEntry) bool {
	for _, entry := range entries {
		if entry.Name() == ".git" && entry.IsDir() {
			return true
		}
	}
	return false
}
