package genexp

import (
	"context"
	"strings"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/prompt"
	"code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/devgpt/kiwis/copilotstack/agent/framework"

	"github.com/pkg/errors"
)

type GenerateUserQueryTemplateOption struct {
	Trajectory []TaskTrajectoryNode
	ResultDir  string
	Model      iris.SceneModelConfig
	LLM        framework.LLM
}

func composeUserQueryTemplatePrompt(tr []TaskTrajectoryNode) ([]*framework.ChatMessage, error) {
	opts := []prompt.ComposeVaryMessageOption{
		prompt.WithSystemMessage(mustGetPromptTemplate(promptGenTmplUserQuerySystem), map[string]any{}),
	}
	for _, item := range tr {
		switch {
		case item.UserMessage != nil:
			opts = append(opts, prompt.WithUserMessage(mustGetPromptTemplate(promptGenTmplExpSOPUserInstruct), map[string]any{
				"Message": item.UserMessage,
			}))
		case item.Round != nil:
			opts = append(opts, prompt.WithAssistantMessage(mustGetPromptTemplate(promptGenTmplExpTrajPlan), map[string]any{
				"Plan": item.Round.Plan,
			}))
			opts = append(opts, prompt.WithUserMessage(mustGetPromptTemplate(promptGenTmplExpTrajExec), map[string]any{
				"Execution": item.Round.Execution,
			}))
		}
	}
	opts = append(opts,
		prompt.WithAssistantMessage(mustGetPromptTemplate("Agent has ended the task."), map[string]any{}),
		prompt.WithUserMessage(mustGetPromptTemplate(promptGenTmplUserQueryUser), map[string]any{}),
	)
	msgs, err := prompt.ComposeVaryMessages(opts)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to compose messages")
	}
	return msgs, nil
}

func GenerateUserQueryTemplate(ctx context.Context, opt GenerateUserQueryTemplateOption) (*entity.ExpUserQueryTemplate, error) {
	msgs, err := composeUserQueryTemplatePrompt(opt.Trajectory)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to compose user query template prompt")
	}

	// saveIntermediateResult(opt, "exp_sop_prompt.xml", GetMessagesPromptString(msgs))

	res, err := opt.LLM.ChatCompletion(context.Background(), msgs, framework.LLMCompletionOption{
		Model:       opt.Model.Model,
		Temperature: opt.Model.Temperature,
		MaxTokens:   opt.Model.MaxTokens,
		Tag:         "gen_exp_user_query_template",
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to call llm to summarize exp sop")
	}

	// saveIntermediateResult(opt, "exp_sop_llm_result.xml", res.Content)

	return parseUserQueryTemplate(ctx, res.Content)
}

func parseUserQueryTemplate(ctx context.Context, content string) (*entity.ExpUserQueryTemplate, error) {
	exp := &entity.ExpUserQueryTemplate{}
	tags, err := prompt.ParseTopTagsV2(content)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to parse top tags")
	}
	if len(tags) == 0 {
		return nil, errors.New("no tags found")
	}
	sopTag := tags[0]
	for _, attr := range sopTag.Attr {
		if attr.Name.Local == "name" {
			exp.Name = attr.Value
		}
	}

	tags, err = prompt.ParseTopTagsV2(sopTag.Content)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to parse inside tags")
	}

	for _, tag := range tags {
		switch tag.XMLName.Local {
		case "user_query_template":
			exp.UserQuery = strings.TrimSpace(tag.Content)
			tags, err := prompt.ParseTopTagsV2(tag.Content)
			if err != nil {
				return nil, errors.WithMessage(err, "failed to parse user query template top tags")
			}
			for _, tag := range tags {
				switch tag.XMLName.Local {
				case "template":
					exp.UserQuery = strings.TrimSpace(tag.Content)
				case "placeholder":
					ph := entity.Placeholder{}
					for _, attr := range tag.Attr {
						switch attr.Name.Local {
						case "name":
							ph.Name = attr.Value
						case "default":
							ph.Default = attr.Value
						case "description":
							ph.Description = attr.Value
						case "ref_attachments":
							ph.RefAattachments = strings.ToLower(attr.Value) == "true"
						}
					}
					exp.UserQueryPlaceholders = append(exp.UserQueryPlaceholders, ph)
				}
			}
		}
	}

	return exp, nil
}
