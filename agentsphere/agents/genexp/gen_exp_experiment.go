package genexp

import (
	_ "embed"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/prompt"
	"code.byted.org/devgpt/kiwis/copilotstack/agent/framework"
)

var (
	//go:embed prompts/gen_exp_reusable_workflow_system.go.tmpl
	promptGenReusableWorkflowSystem string
	//go:embed prompts/gen_exp_reusable_workflow_user.go.tmpl
	promptGenReusableWorkflowUser string
)

func ComposeGenReusableWorkflowPrompt(round *PlanActRound) ([]*framework.ChatMessage, error) {
	opts := []prompt.ComposeVaryMessageOption{
		prompt.WithSystemMessage(mustGetPromptTemplate(promptGenReusableWorkflowSystem), map[string]any{}),
		prompt.WithUserMessage(mustGetPromptTemplate(promptGenReusableWorkflowUser), map[string]any{
			"Plan":      round.Plan,
			"Execution": round.Execution,
		}),
	}
	return prompt.ComposeVaryMessages(opts)
}
