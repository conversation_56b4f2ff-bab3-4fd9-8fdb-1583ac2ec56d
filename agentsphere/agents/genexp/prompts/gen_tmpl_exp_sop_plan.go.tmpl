{{ if .Plan.Rationale -}}
## Rationale
{{ .Plan.Rationale }}
{{- end }}

{{ if .Plan.ProgressPlan -}}
## Progress
{{ .Plan.ProgressPlan }}
{{- end }}

## Assigned this step to Actor
{{ .Plan.Actor }}

{{ if .Plan.Persona -}}
## Actor's Persona
{{ .Plan.Persona }}
{{- end }}

{{ if ne (len .Plan.Tools) 0 -}}
## Assigned toolsets for the actor
{{ range .Plan.Tools -}}
{{.}},
{{- end -}}
{{- end }}

## Task description for the actor
{{ .Plan.TaskDescription -}}
