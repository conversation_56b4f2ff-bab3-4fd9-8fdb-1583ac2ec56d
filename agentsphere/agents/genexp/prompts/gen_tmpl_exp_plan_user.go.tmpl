Here's the user-plan pairs:

{{ range .Rounds }}
{{ if .UserMessage }}
<user>
{{ .UserMessage.Content }}
</user>
{{ end }}
{{ if .Round }}
<plan>
{{ .Round.Plan.ProgressPlan }}
</plan>
{{ if .Round.Execution.Detail }}
<task_assignment>
<agent>{{ .Round.Plan.Actor }}</agent>
{{ .Round.Execution.Detail.Task }}
</task_assignment>
{{ end }}
{{ end }}
{{ end }}

Please generate a SOP inside `<sop>`, without any other text or wrapper.