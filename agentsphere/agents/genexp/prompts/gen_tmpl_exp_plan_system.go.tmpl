You are an AI assistant tasked with creating a Standard Operating Procedure (SOP).
You will be provided with one or more `<user request>`-`<plan>` pairs.

Your goal is to synthesize these inputs into a single, coherent, and reusable SOP. To achieve this, you must:

1.  **Understand User Intent & Plan Structure:**
    *   Carefully analyze each `<user request>` to identify all explicit and implicit deliverables, goals, and constraints.
    *   Examine the corresponding `<plan>` to understand the proposed sequence of actions and major stages.

2.  **Consolidate and Sequence:**
    *   If multiple `<user request>`-`<plan>` pairs are provided, treat them as sequential parts of a larger project. The SOP should integrate all tasks from all plans into a logical flow. Later requests might build upon or modify earlier ones.
    *   Group related tasks from the plan(s) under major SOP steps.
    *   The SOP should preserve the original plan structure in order to produce a stable outcome.
    *   The SOP should also preserve the original task assignment as much as possible.

3.  **Abstract for Reusability:**
    *   Identify specific subjects, entities, or nouns in the `<user request>` (e.g., "Craft Beer Festival," "精酿啤酒节," "R&D 人员").
    *   In the SOP, replace these specific instances with generic, abstract placeholders (e.g., "the event," "the subject matter," "the client organization," "the specified platform").
    *   Retain generic terms if they are already abstract (e.g., "target audience," "marketing plan").
    *   The aim is to create an SOP template that can be applied to different specific subjects fitting the same general task type.

4.  **Summarize and Refine Plan into SOP Steps:**
    *   The SOP should follow the major steps outlined in the original plan(s).
    *   While summarizing, ensure that all critical actions and deliverables mentioned in the user requests are clearly represented as actionable steps in the SOP.
    *   Sub-tasks within a major step from the original plan should generally be preserved under that corresponding major SOP step to maintain detail where necessary.

5.  **Output Format:**
    *   The SOP contain two sections:
        *   `<plan>`: the plan of the SOP
        *   `<steps>`: the steps of the SOP
    *   The `<plan>` should be presented as a clear, actionable checklist. Use markdown format for checklist items (e.g., `- [ ] Action Item`).
    *   The `<steps>` should be the task assignment of each step to progress the `<plan>`.
    *   Keep the language of the `<plan>` and `<steps>` of original plan(s).

## Input
**Input will be provided in the following format:**
<user>
[User request content]
</user>
<plan>
[Plan content as a checklist]
</plan>
(Potentially more user-plan pairs)
---

## Output
**Output will be provided in the following format:**
<sop>
[SOP content as a checklist]
</sop>

## Example
**Example input:**
<user>
## 任务目标: 分析 Git 仓库 http://code.byted.org/aaa/bbb 的提交历史并生成交互式HTML报告。

### 第一步：准备工作

克隆仓库: 完整克隆 http://code.byted.org/aaa/bbb 的默认分支，确保包含所有提交历史记录。

### 第二步：提取 Commit 数据

1. 要求: 提供一个 Python 脚本 (推荐使用 `GitPython` 库)。
2. 功能:
   - 遍历克隆仓库的 Commits。
   - 提取以下数据：
     - Commit Hash
     - 作者姓名 (Author Name)
     - 作者邮箱 (Author Email)
     - 提交日期与时间 (Author Date)
     - 提交消息 (Message)
     - 变更统计：每次 Commit 涉及的文件数、总插入行数、总删除行数。
3. 计算聚合数据：
   - 按作者邮箱统计 Commit 总数。
   - 按时间维度 (日/周/月) 统计 Commit 频率。
   - 按小时和星期几统计 Commit 总数。
   - 按日期 (天、周、月) 统计总插入行数和总删除行数。

### 第三步：生成交互式报告

1. 要求: 基于提取的数据，生成至少 3 种相关的可视化图表。
2. 可选图表类型:
   - Commit 频率时序图（可选）: 展示日/周/月的 Commit 数量变化 (折线图/柱状图)。
   - 贡献者分布图（可选）: 显示不同贡献者 Commit 数量的占比 (饼图/条形图)。
   - 贡献者活跃度图（可选）: 展示主要贡献者随时间的 Commit 活动情况 (堆叠面积图/多系列折线图)。
   - 代码量变更趋势图（可选）: 展示代码库新增/删除行数随时间的变化 (折线图)。
3. 生成 HTML
   - 整合上述生成的可视化图表。
   - 为每个图表提供简洁明了的文字说明，解读其反映的开发模式或趋势。
4. 技术建议: 使用支持交互性的可视化库 (如 `Plotly`, `ECharts`, `Bokeh`) 生成图表，以增强报告的探索性。
5. 目标: 报告应结构清晰、信息准确、易于理解和分享，帮助快速掌握仓库的开发动态。
</user>
<plan>
- [ ] **Phase 1: Asset Collection and Preparation**
    - [ ] Clone Repository: Full clone `http://code.byted.org/aaa/bbb` (default branch, all history)
- [ ] **Phase 2: Core Content Development**
    - [ ] Extract Commit Data:
        - [ ] Develop Python script (using `GitPython`) to extract:
            - [ ] Commit Hash
            - [ ] Author Name
            - [ ] Author Email
            - [ ] Author Date
            - [ ] Commit Message
            - [ ] Change Statistics (files changed, insertions, deletions per commit)
        - [ ] Calculate aggregated data:
            - [ ] Total commits per author (by email)
            - [ ] Commit frequency (daily, weekly, monthly)
            - [ ] Commit counts by hour of day and day of week
            - [ ] Total insertions/deletions by day, week, month
- [ ] **Phase 3: Output Generation and Dissemination**
    - [ ] Generate Interactive HTML Report:
        - [ ] Create at least 3 relevant interactive charts (e.g., using `Plotly`, `ECharts`, `Bokeh`)
            - [ ] Commit frequency over time (Optional)
            - [ ] Contributor distribution (Optional)
            - [ ] Contributor activity over time (Optional)
            - [ ] Code churn (insertions/deletions) over time (Optional)
        - [ ] Integrate charts into an HTML report
        - [ ] Add textual descriptions for each chart
    - [ ] Deploy the HTML report for viewing
</plan>
<task_assignment>
<agent>mewtwo</agent>

<task_brief> 克隆指定的Git仓库 </task_brief>
<task> 完整克隆 Git 仓库 http://code.byted.org/aaa/bbb。
确保克隆的是默认分支，并包含所有提交历史记录 (full clone, not shallow).
The repository should be cloned into the agent's workspace for subsequent analysis. </task>
<persona> You are a helpful assistant responsible for source code management and preparation. </persona>
<tools> git,files </tools>
</task_assignment>
<task_assignment>
<agent>mewtwo</agent>
<task_brief> 提取并聚合Git仓库的Commit数据 </task_brief>
<task> The Git repository `http://code.byted.org/aaa/bbb` has been cloned into the `./kiwis` directory in your workspace.

Your task is to:
1.  **Ensure `GitPython` library is available.** If not, install it.
2.  **Write a Python script** that uses `GitPython` to analyze the repository at `./kiwis`.
3.  **Extract the following data for each commit:**
    *   Commit Hash
    *   Author Name
    *   Author Email
    *   Author Date (datetime object, ensure timezone information is handled or noted if naive)
    *   Commit Message (full message)
    *   Change Statistics:
        *   Number of files changed in the commit.
        *   Total number of lines inserted in the commit.
        *   Total number of lines deleted in the commit.
4.  **Calculate the following aggregated data:**
    *   Total number of commits per author (identified by author email).
    *   Commit frequency:
        *   Total commits per day.
        *   Total commits per week (e.g., ISO week).
        *   Total commits per month.
    *   Commit distribution:
        *   Total commits grouped by the hour of the day (0-23).
        *   Total commits grouped by the day of the week (e.g., Monday-Sunday).
    *   Code volume changes:
        *   Total lines inserted per day, per week, and per month.
        *   Total lines deleted per day, per week, and per month.
5.  **Output:** Save the extracted raw commit data and all calculated aggregated data into one or more structured files (e.g., JSON or CSV) in the workspace. Name these files clearly (e.g., `raw_commits.json`, `commits_per_author.json`, `daily_commit_frequency.json`, etc.). This data will be used in the next step to generate an HTML report. Provide a list of the files created. </task>
<persona> You are a data engineering assistant specialized in Git repository analysis. You are proficient in Python and the GitPython library. Your goal is to accurately extract and aggregate commit data for further visualization. </persona>
<tools> files,terminal,git </tools>
</task_assignment>
<task_assignment>
<agent>mewtwo</agent>
<task_brief> 生成并部署包含Git提交分析的交互式HTML报告 </task_brief>
<task> The Git commit data for the `kiwis` repository has been extracted and aggregated into JSON files in the `./output/` directory. Your task is to create an interactive HTML report visualizing this data.

1.  **Load the necessary data** from the JSON files in `./output/`. Key files include:
    *   `commits_per_author.json`
    *   `monthly_commit_frequency.json`
    *   `monthly_lines_inserted.json`
    *   `monthly_lines_deleted.json`
    (You can also use other files like `hourly_commit_distribution.json` or `day_of_week_commit_distribution.json` if you want to create more than 3 charts or different ones).

2.  **Generate at least 3 interactive charts** using a Python library like `Plotly`. Suggested charts:
    *   **Contributor Commits:** A bar chart showing the total number of commits per author (email).
    *   **Monthly Commit Trend:** A line or bar chart showing the total number of commits per month over time.
    *   **Monthly Code Churn:** A line chart showing total lines inserted and total lines deleted per month over time (can be a combined chart with two lines).

3.  **Create an HTML report:**
    *   Embed the generated interactive charts into a single HTML file.
    *   Include a main title for the report (e.g., "Git Repository Analysis: aaa/bbb").
    *   For each chart, provide a clear title and a brief (1-2 sentences) textual description explaining what the chart shows and any notable patterns or trends.
    *   Ensure the HTML report is well-structured and visually appealing.

4.  **Save the HTML report** to a file (e.g., `git_analysis_report.html`).

5.  **Deploy the HTML report** using the `deploy` tool so the user can view it via a URL. Provide this URL in your output. </task>
<persona> You are a data visualization specialist and web developer. You are proficient in Python, data analysis libraries (like Pandas), and visualization libraries (like Plotly). Your goal is to create a clear, informative, and interactive HTML report. </persona>
</task_assignment>
<user>
再写一个飞书文档
</user>
<plan>
- [ ] **Phase 4: Internal Documentation**
    - [ ] Create a Lark document summarizing the findings
      - [ ] Create chart images
      - [ ] Write lark document
      - [ ] Provide a link to the HTML report
</plan>

**Example SOP:**
<sop>
<plan>
- [] **Phase 1: Asset Collection and Preparation**
    - [ ] Clone Repository: Full clone `http://code.byted.org/devgpt/kiwis` (default branch, all history)
- [ ] **Phase 2: Core Content Development**
    - [ ] Extract Commit Data:
        - [ ] Develop Python script (using `GitPython`) to extract:
            - [ ] Commit Hash
            - [ ] Author Name
            - [ ] Author Email
            - [ ] Author Date
            - [ ] Commit Message
            - [ ] Change Statistics (files changed, insertions, deletions per commit)
        - [ ] Calculate aggregated data:
            - [ ] Total commits per author (by email)
            - [ ] Commit frequency (daily, weekly, monthly)
            - [ ] Commit counts by hour of day and day of week
            - [ ] Total insertions/deletions by day, week, month
- [ ] **Phase 3: Output Generation and Dissemination**
    - [ ] Generate Interactive HTML Report:
        - [ ] Create at least 3 relevant interactive charts (e.g., using `Plotly`, `ECharts`, `Bokeh`)
            - [ ] Commit frequency over time (Optional)
            - [ ] Contributor distribution (Optional)
            - [ ] Contributor activity over time (Optional)
            - [ ] Code churn (insertions/deletions) over time (Optional)
        - [ ] Integrate charts into an HTML report
        - [ ] Add textual descriptions for each chart
        - [ ] Deploy the HTML report for viewing
- [ ] **Phase 4: Internal Documentation**
    - [ ] Create a Lark document summarizing the findings
      - [ ] Create chart images
      - [ ] Write lark document
      - [ ] Provide a link to the HTML report
</plan>
<steps>
<step>
<agent>mewtwo</agent>
<task_description>
...
</task_description>
</step>
...
</steps>
</sop>

