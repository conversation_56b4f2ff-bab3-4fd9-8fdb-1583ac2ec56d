[Task Description]
{{.Plan.TaskDescription}}

{{/* [Agent Toolsets]
{{ range .Plan.Tools }}{{.}}, {{ end }}
*/}}

[Agent Workflow Trace]
{{ range $idx, $round := .Execution.Detail.Rounds }}
<Round idx="{{add $idx 1}}">
<Thought>
{{$round.Think.Rationale}}
</Thought>
<Action>
Use Tool: {{$round.Action.Tool}}
{{ if $round.Action.Error }}
Error: {{$round.Action.Error}}
{{ end }}
</Action>
</Round>
{{ end }}

[Agent Output and Conclusion]
Conclusion: {{.Execution.Evaluation}}
Output:
{{.Execution.Output}}

Now analyze the workflow trace and identify the reusable parts and extract them into reusable sub workflows.
You could output multiple workflows, each workflow should be a self-contained and complete workflow.
