package genexp

import (
	"context"
	"fmt"
	"strings"
	"time"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/prompt"
	"code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/devgpt/kiwis/copilotstack/agent/framework"
	"github.com/pkg/errors"
	"github.com/samber/lo"
)

type TemplateExpPlanGenUserPromptVars struct {
	Rounds []TaskTrajectoryNode
}

type GenerateTemplateExpProgressPlanOption struct {
	Trajectory []TaskTrajectoryNode
	ResultDir  string
	Model      iris.SceneModelConfig
	LLM        framework.LLM
}

func GenerateTemplateExpProgressPlan(ctx context.Context, opt GenerateTemplateExpProgressPlanOption) (plan *entity.ProgressPlan, err error) {
	defer func(now time.Time) {
		if err != nil {
			fmt.Printf("GenerateProgressPlan took: %v\n", time.Since(now))
		}
	}(time.Now())
	plan, err = generateTemplateExpProgressPlan(ctx, opt.Trajectory, opt)
	if err != nil {
		return nil, err
	}
	return plan, nil
}

// Generate a simple SOP progress plan from the trajectory
func generateTemplateExpProgressPlan(ctx context.Context, trajectory []TaskTrajectoryNode, opt GenerateTemplateExpProgressPlanOption) (plan *entity.ProgressPlan, err error) {
	messages, err := prompt.ComposeVaryMessages([]prompt.ComposeVaryMessageOption{
		prompt.WithSystemMessage(mustGetPromptTemplate(promptGenTmplExpPlanSystem), map[string]any{}),
		prompt.WithUserMessage(mustGetPromptTemplate(promptGenTmplExpPlanUser), map[string]any{
			"Rounds": trajectory,
		}),
	})
	if err != nil {
		return nil, err
	}
	resp, err := opt.LLM.ChatCompletion(ctx, messages, framework.LLMCompletionOption{
		Model:       opt.Model.Model,
		Temperature: opt.Model.Temperature,
		MaxTokens:   opt.Model.MaxTokens,
		Tag:         "gen_exp_progress_plan",
	})
	if err != nil {
		return nil, err
	}

	plan = &entity.ProgressPlan{}
	tags, err := prompt.ParseTopTagsV2(resp.Content)
	if err != nil {
		return nil, err
	}
	if len(tags) == 0 {
		return nil, errors.New("no tags found")
	}

	// parse the root sop tag
	sop, found := lo.Find(tags, func(tag prompt.Tag) bool {
		return tag.XMLName.Local == "sop"
	})
	if !found {
		return nil, errors.New("no sop found")
	}
	tags, err = prompt.ParseTopTagsV2(sop.Content)
	if err != nil {
		return nil, err
	}

	planTag, found := lo.Find(tags, func(tag prompt.Tag) bool {
		return tag.XMLName.Local == "plan"
	})
	if !found {
		return nil, errors.New("no plan found")
	}
	plan.Plan = strings.TrimSpace(planTag.Content)

	stepsContent, found := lo.Find(tags, func(tag prompt.Tag) bool {
		return tag.XMLName.Local == "steps"
	})
	if !found {
		return plan, errors.New("no steps found")
	}
	stepsTags, err := prompt.ParseTopTagsV2(stepsContent.Content)
	if err != nil {
		return plan, err
	}
	steps := lo.Filter(stepsTags, func(tag prompt.Tag, _ int) bool {
		return tag.XMLName.Local == "step"
	})
	plan.Steps = lo.Map(steps, func(tag prompt.Tag, _ int) entity.ProgressPlanStep {
		stepTags, err := prompt.ParseTopTagsV2(tag.Content)
		if err != nil {
			return entity.ProgressPlanStep{
				Task: strings.TrimSpace(tag.Content),
			}
		}
		agentTag, _ := lo.Find(stepTags, func(child prompt.Tag) bool {
			return child.XMLName.Local == "agent"
		})
		return entity.ProgressPlanStep{
			Agent: agentTag.Content,
			Task:  strings.TrimSpace(tag.Content),
		}
	})

	return plan, nil
}
