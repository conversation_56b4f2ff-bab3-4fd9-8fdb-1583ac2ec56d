package genexp

import (
	"context"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/prompt"
	"code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/devgpt/kiwis/copilotstack/agent/framework"
	"code.byted.org/gopkg/pkg/errors"
)

type GenerateLarkTemplateSOPOption struct {
	Tasks map[string]any
	LLM   framework.LLM
	Model iris.SceneModelConfig
}

func GenerateLarkTemplateSOP(ctx context.Context, opt GenerateLarkTemplateSOPOption) (sop *entity.ExpSOP, err error) {
	opts := []prompt.ComposeVaryMessageOption{
		prompt.WithSystemMessage(mustGetPromptTemplate(promptGenLarkTemplateSOP), map[string]any{}),
		prompt.WithUserMessage(mustGetPromptTemplate(promptGenLarkTemplateSOPUser), opt.Tasks),
	}
	msgs, err := prompt.ComposeVaryMessages(opts)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to compose messages")
	}

	res, err := opt.LLM.ChatCompletion(ctx, msgs, framework.LLMCompletionOption{
		Model:       opt.Model.Model,
		Temperature: opt.Model.Temperature,
		MaxTokens:   opt.Model.MaxTokens,
		Tag:         "gen_template_sop",
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to call llm to summarize exp sop")
	}

	return ParseTemplateGenExpSOP(ctx, GenerateTemplateExpSOPOption{}, res.Content)
}
