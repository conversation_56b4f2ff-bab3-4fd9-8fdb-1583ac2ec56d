---
ID: "coder_5"
Title: Simple Web App Development Workflow
EnabledIf: "agent in ['repository_developer', 'mewtwo']"
UsedWhen: when developing frontend web applications with UI components from scratch or creating presentation websites for reports, content showcase and data visualization
---

This note explains best practices when it comes to creating web applications from scratch for users who don't have advanced requirements. Such as:
- Not having an existing repo
- No specifying a particular tech stack like Vue etc.
  
For developing frontend web applications with UI components from scratch or creating presentation websites for reports, content showcase, html visual report and data visualization, you should follow these guidelines:

1. Preparation
- Use any pre-prepared resources available: research documents, datasets, images, analysis reports, or design materials. 
- Always carefully review and utilize the provided resources
- Read all referenced files thoroughly and incorporate their content appropriately
- Use provided images in the website design - they are meant to be displayed and integrated
- Transform documents and data files into meaningful web content
- Never ignore provided resources - they are essential components for the final website
2. Development
- You must use shell cmd `aime_create_react_app {app name}` to create the app. This will setup a Vite + React Typescript app with Tailwind CSS already setup and shadcn/ui pre-installed.
- The `aime_create_react_app` command is already installed and you can use it directly.
- If you face import errors after creating the frontend this way DO NOT PANIC. You can just check the "src/components/ui" directory to see which pre-built components are available and create or install any missing ones. You must not use a different way to create the app.
- Since this is a simple app and everything's already set up, you should only change `src/App.tsx` and leave the rest of the code as is.

You must follow these guidelines while developing the app:
- Use Tailwind classes for styling. DO NOT USE ARBITRARY VALUES (e.g. h-[600px]). When you're really unsure, you can look up the Tailwind docs.
- Use lucide for icons e.g. import { Camera } from "lucid-react" & <Camera color="red" size=48 />
- The recharts charting library is available to be imported, e.g. import { LineChart, XAxis, ... } from "recharts" & <LineChart ...><XAxis dataKey="name"> ...
- Use prebuilt components from the shadcn/ui library. They are already pre-installed but you need to import them: import { alert, AlertDescription, AlertTitle, AlertDialog, AlertDialogAction } from '@/components/ui/alert';
- **For images, use `import` to convert images into variable references, otherwise images won't be included in the build.** E.g. `import logoImage from './assets/logo.png'` then use `<img src={logoImage} />` instead of `<img src="./assets/logo.png" />`
- If environment variables are needed, you should use a .env file in the frontend directory and load them from there.
- The default `App.css` in the Vite + React Typescript app includes a restrictive rule for #root:
```css
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
```
- **You must override it with the following before you begin coding:**
```css
  max-width: none;
  width: 100%;
  margin: 0;
  padding: 0;
  text-align: left;
```
3. Testing and validation
- DO NOT DEPLOY UNTESTED CODE.
- Your testing should verify both functionality and sleek UI design. Make improvements if you find any issues.
- Test the frontend locally by running `npm run dev` (bash timeout=-1 to run it in background) and navigating to http://localhost:5173 in your browser. This will auto-reload as you make changes. Iterate until the whole app works as expected and make any frontend changes needed. Also make sure the UI is sleek and polished but don't do unnecessary work.
- Do testing via `browser_use` tools:
  - Initial checks: Load page, check console errors, and validate assets & layout.
  - Functionality & UI Test: Test forms (enter real data), navigation, and styling elements. 
  - Interactive Element Testing: Validate form elements (input fields, buttons, dropdowns, checkboxes; enter test data to avoid confusing placeholders with input) and navigation components (links, menus, breadcrumbs, pagination).
- When issues are discovered during testing:
  - Immediate fix: Address and fix the specific problem found
  - Targeted re-validation: Re-test ONLY the fixed component/functionality, not the entire page
- If you make updates to the app, rebuild and then redeploy the app by using the `deploy` tool again.
4. Deployment
- Before deploying, you must build the app using `npm run build`.
- To deploy the app, use the `deploy` tool. E.g. using Vite, the build_dir is "dist" inside your app's folder.
- It's okay to deploy even if the user didn't specifically ask for it if their request was very open-ended. If the user's request was very detailed or specifically forbids deploying, you should not deploy until you can clarify with the user.

  

IMPORTANT:
- Using your browser, you can visit websites, click, type, and scroll. If testing a website requires more complex interactions, you should ask the user to test it themselves.
- Use `aime_create_react_app` in bash to quickly set up the project structure.
- **CRITICAL: Always import images using `import`. DO NOT USE `<img/>` directly.**