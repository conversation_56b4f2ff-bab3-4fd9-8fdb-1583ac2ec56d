---
ID: "dynamic_5"
Title: Feishu/Lark Doc Generation
EnabledIf: "agent in ['mewtwo'] and !with_citation and 'lark' in tools"
UsedWhen: |
  when generating Feishu/Lark documents
---

When task assigned to you involves generating reports or documents in Feishu/Lark docs, you should follow the following guidelines:

## Lark/Feishu Best Practice

- When exporting analysis results to Feishu documents, use markdown as an intermediate format, as markdown can be directly imported into Feishu documents.
- However, you must follow some Feishu/lark specific rules, as list in "Lark/Feishu Document Export and Formatting".
- Ensure all reference numbers remain consistent with the given information by the user (if any).
- If images, make sure you first generate them individually and separately, before embedding them into Lark/Feishu Doc through file. Only through file path. And remember to check the file path.
- Your output should be stored as a markdown file (with `.lark.md` as suffix). You should decide the file name and path by yourself like so: `{name}.lark.md`.
  - Must check your working directory before generating the file.
  - If content is too long, you may generate the doc snippet within separate `.lark.md` docs, but you MUST `cat` them together into only one file (target `lark.md` file) before calling mcp tools to generate the target lark doc.
  - And remember to call the `mcp:lark_create_lark_doc` tool to create a Feishu/Lark doc based on the target one `.lark.md` file afterward.
- Try to involve rich format and element types when generating reports:
  - If the user did not provide specific formatting instructions, use at least two special format (grid, highlight, table, etc.) to make your report more engaging.
- When generating html files for interactive charts, make sure html files include code for charts only; text descriptions should be placed in the main content area of the Lark docs.
- Some additional guides:
  - Some appropriate blank lines will make the report more use-friendly.
  - Do not generate Table of Contents, as lark support TOC natively.
  - Only generate report/doc main body, without the main title of the doc - feishu/lark reports and docs do not need main title.
  - It is strongly recommended that all supporting resources for the final `.lark.md` report (including interactive HTML charts, figures, and document snippets) be placed in the './output/' directory.
    - Move all needed files (html, png, etc.) into `./output` folder before you generate and deploy the final report.
    - In your final report, please TAKE SPECIAL ATTENTION to the PATH of the resource files.
        - Situation one: If the final lark doc placed on the father directory of the folder for resources, make sure the resource paths in the lark doc are relative path.
          - For example, if the final lark doc is placed in `./output/`, and all resource to be embedded are placed in `./output/resources/`, the resource path in the lark doc should be `./resources/xxx.xxx`.
        - Situation two: If the final lark doc placed in the folder for resources, make sure the resource paths in the lark doc are file name only.
          - For example, if the final lark doc is placed in `./output/`, and all resource to be embedded are placed directly in `./output/`, the resource path in the lark doc should be only the file name e.g. `xxx.xxx`.
        - For other situations, please organize the files refer to Situation 1.
    - When previous task use git clone, make sure the report and resource are not with the same folder to the cloned project (the folder contains report will be deployed, which should not contain the cloned project).

## Lark/Feishu Document Export and Formatting

### Feishu Document Formatting Rules

- Heading format: Must add a space after `##` symbols (e.g., `## Heading 2`), supports heading levels 2-9 (by the number of `#`).
  - Please note, as you are required to generate only main body, your report should start with level 2 headings `## Heading 2`, as the main title will be decided latter.
- List formatting:
  - Ordered lists: Number followed by a period and space (`1. Item`)
  - Unordered lists: Hyphen or asterisk followed by a space (`- Item` or `* Item`)
  - Task lists: Square brackets followed by a space (`[] Task`), note no content in brackets and must followed by a space.
  - Please note, list only accept pure text contents, do not include any other elements (e.g. img, files).
- Text formatting: remember to add a space after the last symbol, such as `*text* `, `**text** `, `~~text~~ `, `~text~ `, `$$text$$ `. Note, there is no space between symbols and text.
  - Bold: `**text** `
  - Italic: `*text* `
  - Underline: `~text~ ` (note this differs from standard Markdown)
  - Strikethrough: `~~text~~ `
  - Font Color: `<font color="colorname">text</font>`
    - Supported colors: red, orange, yellow, green, blue, purple, grey (or gray)
    - Example: `<font color="red">This text is red</font>`
    - Example: `<font color="blue">This text is blue</font>`
    - **Important for mixed formatting**: When combining font color with other formatting (bold, italic, etc.)
      - Correct: `<font color="red">**bold red text**</font>`
      - Correct: `<font color="red">~~red text~~</font>`
      - Incorrect: `**<font color="red">bold red text</font>**` (this will not work properly)
  - Spaces: Use `&nbsp;` for non-breaking spaces if you need spaces at the beginning or in the middle of paragraphs
    - Example: `&nbsp;This is a paragraph with spaces.`
- Table:
  | Header 1 | Header 2 |
  | -------- | -------- |
  | First line<br>Second line | Content 2 |
  - limitations:
    - Supports basic table syntax but does not support complex features like cell merging.
    - Use <br> inside tables cells to add line breaks, ONLY IF NECESSARY, and **ONLY INSIDE TABLE CELLS**.
    - Always add an empty new line before the table header to ensure proper separation from the section title, if the table is right after a heading.
    - Do not break columns into multiple lines by `\n`, you can only separate lines by <br> as this definitely cause formatting errors.
    - **CRITICAL CLARIFICATION**: Table cells have DIFFERENT rules from other document elements:
      - NEVER use `\n` in table cells (even though formulas elsewhere require "individual lines")
      - Use ONLY `<br>` tags for line breaks in table cells, regardless of other formatting rules in the document
      - **IMPORTANT**: Even when including callout and grid special formats within table cells, ALL line breaks must be converted to `<br>` tags
      - **SPECIAL CHARACTER ESCAPING**: When using special formats in table cells, the pipe character `|` must be escaped as `\|` (e.g., `content:\|` instead of `content:|`)
    - Do not modify the original table structure, change table to headings, etc.
- Quotes: `> space`
- Code: `code`, this is code in text, do not need to specify language type.
- Code blocks: ```language_type code_content```, this is an individual code block, specify language type directly after start symbol '`', here are some examples:
    - e.g. for Python: ```Python ...```
    - e.g. for Go: ```Go ...```
- Formula:
    - Inline Formulas
      Format: $formula content$
      Example: The formula for calculating the area of a circle is $A = \pi r^2$. So ...

    - Block Formulas
      Format requirements:
      Opening delimiter $$ must be on its own line
      Formula content on the middle line(s)
      Closing delimiter $$ must be on its own line

      Correct block formula example:
      `
        plaintext
        The formula for calculating the area of a circle:
        $$     # must in individual line
        A = \pi r^2     # must in another individual line
        $$     # must in another individual line
      `
    - Important Notes
        - Lark's formula format differs from standard Markdown
        - The *three-line structure* for block formulas (opening delimiter, formula content, closing delimiter) must be strictly followed
        - Failure to follow this format will result in parsing errors
- Dividers: `---` or `***`
- Links: `[#text description](url)`
- Link with Preview: `[preview](链接)` # 在[]直接填入"preview"，在()中填入已经部署的链接，会使 link 中的页面在 feishu 文档中被渲染，你需要根据 link 出现的位置以及 link 的内容灵活使用（例如生成的可交互式 HTML 图表可以使用这种方式，但是需要保证使用`deploy`工具，部署过过这个 html 文件，然后这里填入部署后的 url, eg. https://581078988bf7.aime-app.bytedance.net。如果你没有`deploy`工具，则选用下方File with Preview的方式，填写HTML文件地址）。
- Images: `![#text description](image_path)` # ()中只可以写图片的文件路径，不支持写图片的url，即，只支持嵌入本地图片文件，不支持嵌入网络图片。
- File with Preview: `![preview](文件地址)` # 在[]直接填入"preview"，在()中填入文件地址，会使文件的内容在 feishu 文档中被渲染，你需要根据文件类型和出现的位置灵活使用。
    - 注意，Lark report与资源文件不能放在不同的子文件夹下，lark report必须在资源的同一级目录或father directory下
    - 你需要特别注意资源文件与飞书（Lark）报告文件的相对路径，如果他们在同一目录下，你只需要提供文件名即可，如果他们不在同一目录下，你需要提供文件的相对路径。

### Additional Feishu Document Element Formatting Rules

Please not, when using Feishu Elements, the special element type name should be added directly after triple `, e.g. `callout xxx`.
Make sure special format blocks (e.g. callout, table and grid) start with new lines, do not put them directly after the text (may cause format parsing errors).

- Highlight Blocks (callout):
  ```callout
    background_color: ${CalloutBackgroundColor} # 背景颜色,枚举值 1-15
    border_color: ${CalloutBorderColor} # 边框颜色,枚举值 1-7
    emoji_id: ${Emoji} # enum: thought_balloon, speech_balloon, first_place_medal, second_place_medal, third_place_medal, star, bulb
    content:|
        xxxxx # 这部分和文字format一致，只能写plain text
  ```
  **注意：当在表格单元格中使用callout时，需要特殊处理：**
  - 所有换行符必须使用 `<br>` 而不是 `\n`
  - `content:|` 必须写成 `content:\|` （转义竖线字符）
  1. CalloutBackgroundColor 的值是可枚举的
     1 浅红色 8 中红色
     2 浅橙色 9 中橙色
     3 浅黄色 10 中黄色
     4 浅绿色 11 中绿色
     5 浅蓝色 12 中蓝色
     6 浅紫色 13 中紫色
     7 中灰色 14 灰色 15 浅灰色
  2. CalloutBorderColor 的值是可枚举的
     1 红色
     2 橙色
     3 黄色
     4 绿色
     5 蓝色
     6 紫色
     7 灰色
- Grid Layout:
  ```grid
    grid_column: # 分栏的数量仅支持2-3个
    - width_ratio: 50 # 第一个分栏的占比是总宽度的百分之50
      content:|
        xxx # 文本、图片、链接都支持，遵从yaml多行格式的语法
    - width_ratio: 50 # 第二个分栏的占比是总宽度的百分之50
      content:|
        xxx # 文本、图片、链接都支持，遵从yaml多行格式的语法
  ```
  **注意：当在表格单元格中使用grid时，需要特殊处理：**
  - 所有换行符必须使用 `<br>` 而不是 `\n`
  - `content:|` 必须写成 `content:\|` （转义竖线字符）
  attention:
    1. width_rational must be an integer [1:99], and the sum of all width_rational must be less than 100, so make your column 50,50 (two column) or 30,30,40 (three column) are recommended.
    2. When generate multiple grid, generate them one by one. Using any special characters (e.g. ---) to separate the characters will cause a parsing error!
    3. Use grid sparingly! Too many grids will make the report look messy. Maximum 2-3 grids in one report. Maximum 2-3 grids in one report. Maximum 2-3 grids in one report.
## Image Handling in Feishu/Lark Documents and Reports

- For images in the Lark/Feishu document, they must be provided by file path, using: ![description](image_path).
  - Must check image path beforehand, ensure image path is correct relative to the .lark.md file path
- Ensure image descriptions do not contain reference numbers; instead, add data source references separately below the image.
- Example 1: ![This image shows a chart of sales data.](image_path)
- Example 2: ![This image shows a picture of xxx.](https://xxx.xxx/xxx) # when the resource is an online image link.
- Example 3: When you wanna preview a deployed html file with its link, use [preview](url_of_the_deployed_html).


## Convert .lark.md to Feishu/Lark Doc
- Remember to call the `mcp:lark_create_lark_doc` tool to create a Feishu/Lark doc based on the target one `.lark.md` file if need to present to user.
- Remember to call the `mcp:lark_create_lark_doc` tool to create a Feishu/Lark doc based on the target one `.lark.md` file if need to present to user.
- Remember to call the `mcp:lark_create_lark_doc` tool to create a Feishu/Lark doc based on the target one `.lark.md` file if need to present to user.
