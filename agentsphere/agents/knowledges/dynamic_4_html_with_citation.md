---
ID: "dynamic_4_citation"
Title: html generation principles (citation needed)
EnabledIf: "agent in ['lark'] and with_citation"
UsedWhen: when generating HTML reports or data visualization pages (explicitly NOT for frontend web development projects or applications)
---

## HTML Settings

When writing massive textual content (e.g. html research reports) or data visualization pages, apply the following default settings:

### Fundamentals

- Dynamic Data: Do not format HTML directly using python f-strings. Instead load dynamically with JSON.
- Error Handling: Implement basic error handling for async operations like data fetching. On failure, prevent the entire page from crashing and log the error to the console. Optionally, display a simple, non-intrusive error message if feasible within the UI context.
- File Creation:
  - Always break files down into smaller parts (.css, .js, .html snippets (keep each snippet for 100~500 lines), etc.), then concatenate these `part_xx.html` files together with python script to compose a comprehensive `index.html`. You may need to overwrite the existing `index.html` file to update entrypoint.
  - If you see you created an incomplete html file, it's due to network unstable or other issues. Write the missing parts in the next file and merge them together.
  - Put your files inside a seperate `./output` folder so only necessary files are uploaded to the user.
  - The entrypoint of final deployment should always be `index.html`, other forms will not be recognized.
- Syntax: HTML does not support markdown syntax, so inside parameter block, switch to using HTML tags like `<a>`,`<strong>`,`<img>` instead of markdown.

### Styling

- Fallback Style: If the user's request provides _no_ specific stylistic direction (e.g., the request is purely functional, describes generic content, or explicitly asks for a basic layout), default to a colorful, flat, and modern UI as a baseline.
- Primary Styling Directive: **Prioritize interpreting the user's request.** Actively analyze the user's prompt (keywords used, described content/purpose, any examples given) to infer the desired aesthetic, mood, or theme. Based on this interpretation, creatively apply distinct and appropriate UI styles (e.g., retro, futuristic, minimalistic, brutalist, playful, elegant, corporate, etc.) and advanced components (floating widgets, parallax, glassmorphism, blur, data visualizations, interactive infographics, etc.). **Strive for a stylized and visually engaging result that directly reflects the user's likely intent, even if not explicitly stated.** Don't be afraid to be bold or unconventional if the context seems appropriate.
- Color: Choose appropriate chromatic tones and saturation based on the content.
- Emphasis: Emphasize headings, keywords, and numbers in the main content by adding accent colors.
- Engaging: Interactive elements and animations are encouraged.
  - Transition: Add animations and transitions whenever possible.
  - Interactivity: Create interactive elements when it fits.
  - Layout: Use attractive layouts like enlarged headings with hero images (when images are available), bento grids, mansory, multi columns, poster or magazine style when it fits.

### Libraries

- CSS: Use Tailwind CSS for layout. // <script src="https://cdn.tailwindcss.com">
- Font: Noto Sans or Noto Serif, depending on situation // e.g. <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@100..900&display=swap" rel="stylesheet">
- Icons: Material Symbols // <link href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined" rel="stylesheet" />
- Syntax Highlighting: Use Prism.js for code blocks.
- Math: Use KaTeX for mathematical expressions.
- Charts: Echarts.js, Plotly.js, D3.js...
- Advanced Animation: Anime.js, Lottie.js, GSAP...
- Images: If google image search or unsplash tool is given to you, search for images for your web page.

### Layout

- Spaces: Add padding and margin to all components so that they don't touch the edges of the screen.
  - Pangu: Correctly add spaces between Chinese and English words.
- Responsitivity: Make sure elements are responsive, aligned and centered where they should be.

## Groundings

- Citations/References needed: When references or citations are given in the trace (format is ::cite[xx]), you must include the reference number in your contents (include content in individual files e.g. report, python scripts for figures).
- Citations for numeric data is extremely important, make sure all your data are with reference number.
- Do not generate an individual "References" section.

### Citations

- While both cited context and citation information provided (format is text content followed by numbers of ::cite[xx]), you must add reference number when generate the HTML report (include HTML snippets), at appropriate location.
- Make sure you revise the format of citations in your HTML code, from ::cite[xx] to [xx].
- Make your reference accurate to and respect the original given information.
- In your report (include diagrams, tables and texts etc.), content must come with references (use format: [reference number]) immediately, e.g.:

  - in HTML and Table: add reference number at the end of sentence/element, e.g.:

    ```
        <div>这项研究表明深度学习模型在图像识别任务中取得了显著进展 [7]。</div>

        <table>
          <tr>
            <td>模型</td>
            <td>准确率</td>
            <td>解释</td>
          </tr>
          <tr>
            <td>ResNet-50</td>
            <td>82.4% [30]</td>
            <td>原因是... [53]</td>
          </tr>
        </table>

        <p>该方法主要关注两个方面 [7][10]。</p>
    ```

  - in diagram (include script based): add reference by additional text outside the figure, never directly put the reference number in chart area, e.g.:
    ```
        <script> ... # features-chart code, do not use reference numbers in this area </script>
        <section id="...">
        ...
            <div class="..." id="features-chart"></div>
            <h>数据来源[4][39]</h> # the reference must be provided, at and only at the final report main content
        </section>
        ...
    ```
  - if python for diagram:

    ```
        //in python: generate chart, but please skip reference number.
        import matplotlib.pyplot as plt
        ...

        # 准备数据
        years = [2018, 2019, 2020, 2021, 2022, 2023]
        ...

        # 创建图表 - 注意此python中不要添加任何引用编号，包括title中
        plt.figure(figsize=(10, 6))
        ...
        plt.savefig('xxx.png')
        plt.close()
    ```

    ```
        <!-- in html: add related reference by additional text outside the figure -->
        <div class="...">
        ...
            <div class="chart-container">
                <img src="xxx.png" ...>
                <p class="source-reference">数据来源[54][78]</p> <!-- 引用必须在最终报告的主要内容中提供 -->
            </div>
            ...
        </div>
    ```

  - no redundant, if reference already in context, do not add additional individual “数据来源” again.

- When multiple reference number used, you must follow the format: one number per square bracket, e.g. [1][2][3].
- Keeping Citations In Your Outcomes Is Very Important.

### Result Delivery

- Remember to deploy result files & pages.
- Remember to use `html_vision` tool to validate the html file and deployed link immediately, for those VLMs will help to you make sure no error occurs neither rendering issues, even there is no coding issue in your html files.

### Citation Is Important

You must include citations in your HTML report (including fun stuff like travel plan), the format is [xx] e.g. 数据来源[54][78], you must make your citation number consistent to the information in the execution trace.
You must include citations in your HTML report (including fun stuff like travel plan), the format is [xx] e.g. 数据来源[2][90], you must make your citation number consistent to the information in the execution trace.
You must include citations in your HTML report (including fun stuff like travel plan), the format is [xx] e.g. 数据来源[4], you must make your citation number consistent to the information in the execution trace.
