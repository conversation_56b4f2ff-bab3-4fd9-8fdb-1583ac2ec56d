---
ID: "dynamic_14"
Title: Web App Development Workflow
EnabledIf: "agent in ['dynamic_planner']"
UsedWhen: when developing frontend web applications with UI components from scratch or creating presentation websites for reports, content showcase and data visualization
---

## Core Principle
**Frontend projects must follow: Development → Testing → Deployment. No phase can be skipped.**

## Workflow

### 0. Preparation (When Needed)
- **Research & Analysis**: Complete necessary research and requirement analysis for complex projects
- **Image Collection**: Download and prepare images when visual content integration is required (from Unsplash or Google Images)
- **Data & Content**: Gather research reports, analysis documents, data files when creating content-rich or analytical pages
- **Resource Organization**: Ensure relevant resources are accessible when needed

### 1. Development

- Develop the app base on user requirements
- For web applications when the user doesn't specify a particular tech stack, use command `aime_create_react_app {app name}` to create the app
  - This will setup a Vite + React Typescript app with Tailwind CSS already setup and shadcn/ui pre-installed.
  - Prefer use `aime_create_react_app` command to init the app intead of pure html/css/js
- Otherwise, use the tech stack specified by the user
- For images, always use Unsplash/Google Images. You must actually download the images. DO NOT fabricate image URLs.
- Require `browser` toolset to test locally

### 2. Testing

- Verify all functionality and visual layout using `browser` toolset
- Ensure basic functionality, visual design, responsiveness, and user interaction
- Key testing focus:
  - Interactive elements: buttons, forms, dropdowns, inputs (enter real data, not just placeholders)
  - Image: Verify all images load properly without broken placeholders or missing files 

### 3. Deployment

- Deploy to production using `deploy` toolset

## Planning Requirements

- **Sequential Process**: Must follow the three phases in order and plan it as one comprehensive task
- **Setup**: Use `aime_create_react_app` in bash to quickly set up the project structure.
- **Local Testing is Mandatory**: Cannot deploy without local validation
- **Task Assignment**: Assign the three phases into one single agent and in the same Action (mewtwo)