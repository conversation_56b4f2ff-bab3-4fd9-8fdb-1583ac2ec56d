package entity

import (
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
)

type PlannerStore struct {
	InitialRequirements        string
	Requirements               string
	EnhancedRequirements       string
	RelatedKnowledge           string
	RelatedWorkspaceKnowledges []string
	ExperienceProgressPlan     string
	ActorsDesc                 any
	CurRound                   int
}

type HistoryTask struct {
	Task   string `json:"task"`
	Result string `json:"result"`
}

const (
	PlannerStoreKey    = "planact_planner_store"
	PlanStoreKey       = "planact_plan_store"
	ReferenceStoreKey  = "planact_reference_store"
	PatchToolUploadKey = "planact_patch_upload_store"
)

type PlanStore struct {
	Plans map[int]iris.EventPlanUpdated
}

type Reference struct {
	List    iris.Reference
	IndexOf map[string]int
}

type ReferenceStore struct {
	SearchedRef Reference
	CreatedRef  Reference
}

type ReferenceType string

const (
	ReferenceTypeSearched ReferenceType = "search"
	ReferenceTypeCreated  ReferenceType = "create"
)

type PatchToolUploadStore struct {
	Artifact *iris.ResultArtifact
	Files    map[string]struct{}
}
