[User Information]
Username: {{ .User.Username }}
Current DateTime: {{ date }}

[Problem Statement]
{{ .Requirements }}

{{ if .PreprocessedItems }}
[Mentions]
Here are stuffs mentioned by the user, and they are already preprocessed to the workspace.
{{ range .PreprocessedItems }}
[{{ .ID }}]: {{ .Content }}
{{ end -}}
{{ end }}

{{ if .RelatedKnowledge }}
[Related Knowledge]
{{ .RelatedKnowledge }}
{{ end }}
{{ if .RelatedWorkspaceKnowledges }}
[Related User Private Knowledge]
{{ .RelatedWorkspaceKnowledges }}
{{ end }}

{{ if .ExperienceProgressPlan }}
[SOP]
Here is a SOP from previous satisfying tasks, reuse the SOP to complete the task with the same outcome:

{{ .ExperienceProgressPlan }}

# If you found any conflicts or paradox or unclear description of this SOP, ask user immediately
{{ end }}