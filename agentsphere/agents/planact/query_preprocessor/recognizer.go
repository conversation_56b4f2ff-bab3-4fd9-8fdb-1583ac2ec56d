package queryrecognizer

import (
	"encoding/json"
	"net/http"
	"regexp"
	"strings"
	"time"

	"github.com/samber/lo"

	codebaseactor "code.byted.org/devgpt/kiwis/agentsphere/agents/actors/codebase"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/entity"
)

// QueryRecognizer is used to recognize the query and parse information for later planning
type QueryRecognizer interface {
	Recognize(run *iris.AgentRunContext, messages []*iris.Message) error
}

type URIRecognizer struct{}

var (
	httpURLRegex           = regexp.MustCompile(`(?i)https?://[^\s<>"'{}|\\^` + "`" + `\[\]，。；！？、（）《》〈〉「」『』【】]+`)
	gitURLRegex            = regexp.MustCompile(`(?i)ssh://[^\s<>"'{}|\\^` + "`" + `\[\]，。；！？、（）《》〈〉「」『』【】]+|git@[^\s<>"'{}|\\^` + "`" + `\[\]，。；！？、（）《》〈〉「」『』【】]+[:/][^\s<>"'{}|\\^` + "`" + `\[\]，。；！？、（）《》〈〉「」『』【】]+`)
	larkDocumentURLPattern = regexp.MustCompile(`^https://(bytedance|bytedance\.sg|bytedance\.us)\.larkoffice\.com/(doc|docx|sheet|bitable|wiki)/([a-zA-Z0-9_-]+)$`)
	aeolusDashboardPattern = regexp.MustCompile(`^https://data\.bytedance\.net/aeolus/pages/dashboard/`)
	aeolusDataQueryPattern = regexp.MustCompile(`^https://data\.bytedance\.net/aeolus/pages/dataQuery`)
)

func ExtractURIs(s string) []string {
	uris := make([]string, 0)

	httpFound := httpURLRegex.FindAllString(s, -1)
	for _, f := range httpFound {
		// trim common trailing punctuations
		f = strings.Trim(f, "<>.,;!?()[]{}\"'\n")
		uris = append(uris, f)
	}

	gitFound := gitURLRegex.FindAllString(s, -1)
	for _, f := range gitFound {
		// trim common trailing punctuations
		f = strings.Trim(f, "<>.,;!?()[]{}\"'\n")
		uris = append(uris, f)
	}

	return uris
}

func (e *URIRecognizer) Recognize(run *iris.AgentRunContext, messages []*iris.Message) error {
	logger := run.GetLogger()
	for _, msg := range messages {
		uris := ExtractURIs(msg.Content)
		logger.Infof("[recognizer] found %d uris in message: %s", len(uris), strings.Join(uris, "\n"))
		mentions := make([]iris.Mention, 0)
		mentions = append(mentions, ConvertCodebaseURLsToMentions(run, uris)...)
		mentions = append(mentions, ConvertLarkDocumentURLsToMentions(run, uris)...)
		mentions = append(mentions, ConvertAeolusURLsToMentions(run, uris)...)
		for _, att := range msg.Attachments {
			if att.ArtifactID == "" {
				continue
			}
			mentions = append(mentions, &iris.AttachmentMention{
				BaseMention: iris.BaseMention{
					Type: iris.MentionTypeAttachment,
					ID:   att.ArtifactID,
				},
				ArtifactID: att.ArtifactID,
				Path:       att.Path,
			})
		}

		// deduplicate mentions
		existing := map[string]bool{}
		for _, m := range msg.Mentions {
			existing[m.GetID()] = true
		}
		for _, m := range mentions {
			if _, ok := existing[m.GetID()]; ok {
				continue
			}
			msg.Mentions = append(msg.Mentions, m)
			existing[m.GetID()] = true
		}
	}
	return nil
}

func ConvertCodebaseURLsToMentions(run *iris.AgentRunContext, uris []string) []iris.Mention {
	mentions := make([]iris.Mention, 0)
	lo.ForEach(uris, func(uri string, _ int) {
		repo, err := codebaseactor.ParseRepoCloneInfo(run, uri)
		if err != nil || repo == nil {
			run.GetLogger().Errorf("[recognizer] failed to parse codebase url: %s, err: %v", uri, err)
			return
		}
		mentions = append(mentions, &iris.CodebaseMention{
			BaseMention: iris.BaseMention{
				Type: iris.MentionTypeCodebase,
				ID:   repo.RepoName,
			},
			RawURL:   uri,
			RepoName: repo.RepoName,
			Branch:   repo.Ref,
		})
	})
	return mentions
}

func ConvertLarkDocumentURLsToMentions(run *iris.AgentRunContext, uris []string) []iris.Mention {
	mentions := make([]iris.Mention, 0)
	lo.ForEach(uris, func(uri string, _ int) {
		matches := larkDocumentURLPattern.FindStringSubmatch(uri)
		if len(matches) != 4 {
			return
		}
		mentions = append(mentions, &iris.LarkDocMention{
			BaseMention: iris.BaseMention{
				Type: iris.MentionTypeLarkDoc,
				ID:   matches[3],
			},
			DocID: matches[3],
			URL:   uri,
		})
	})
	return mentions
}

func ConvertAeolusURLsToMentions(run *iris.AgentRunContext, uris []string) []iris.Mention {
	logger := run.GetLogger()

	// Check cookie validity first
	if !isAeolusCookieValid(run) {
		logger.Info("[recognizer] Aeolus cookie is invalid or expired, skipping Aeolus URL conversion")
		return []iris.Mention{}
	}

	mentions := make([]iris.Mention, 0)
	lo.ForEach(uris, func(uri string, _ int) {
		// Check if it's an Aeolus dashboard or dataQuery URL
		if aeolusDashboardPattern.MatchString(uri) || aeolusDataQueryPattern.MatchString(uri) {
			mentions = append(mentions, &iris.AeolusMention{
				BaseMention: iris.BaseMention{
					Type: iris.MentionTypeAeolus,
					ID:   uri,
				},
				URL: uri,
			})
		}
	})
	return mentions
}

// CookieInfo represents a single cookie in the response
type CookieInfo struct {
	Name    string  `json:"name"`
	Value   string  `json:"value"`
	Path    string  `json:"path"`
	Domain  string  `json:"domain"`
	Expires float64 `json:"expires"`
	// Other fields omitted for brevity
}

// CookieResponse represents the response from strato-keystore API
type CookieResponse struct {
	Code    int          `json:"code"`
	Data    []CookieInfo `json:"data"`
	Message string       `json:"message"`
}

// isAeolusCookieValid checks if the Aeolus cookie is valid by calling strato-keystore API
func isAeolusCookieValid(run *iris.AgentRunContext) bool {
	logger := run.GetLogger()

	// Get JWT token from environment
	jwtToken := run.GetEnv(entity.RuntimeEnvironUserCloudJWT)
	if jwtToken == "" {
		logger.Warn("[recognizer] No JWT token found in environment, cannot check cookie validity")
		return false
	}

	// Create HTTP request
	req, err := http.NewRequest("GET", "https://strato-keystore.bytedance.net/api/v1/cookie", nil)
	if err != nil {
		logger.Errorf("[recognizer] Failed to create HTTP request: %v", err)
		return false
	}

	req.Header.Set("x-jwt-token", jwtToken)

	// Make HTTP request
	client := &http.Client{
		Timeout: 10 * time.Second,
	}
	resp, err := client.Do(req)
	if err != nil {
		logger.Errorf("[recognizer] Failed to call strato-keystore API: %v", err)
		return false
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		logger.Errorf("[recognizer] strato-keystore API returned non-200 status: %d", resp.StatusCode)
		return false
	}

	// Parse response
	var cookieResp CookieResponse
	if err := json.NewDecoder(resp.Body).Decode(&cookieResp); err != nil {
		logger.Errorf("[recognizer] Failed to decode cookie response: %v", err)
		return false
	}

	if cookieResp.Code != 0 {
		logger.Errorf("[recognizer] strato-keystore API returned error code: %d, message: %s", cookieResp.Code, cookieResp.Message)
		return false
	}

	// Find titan_passport_id cookie
	now := time.Now().Unix()
	for _, cookie := range cookieResp.Data {
		if cookie.Name == "titan_passport_id" {
			// Check if cookie is expired
			if cookie.Expires > 0 && int64(cookie.Expires) < now {
				logger.Infof("[recognizer] titan_passport_id cookie is expired, expires: %f, current: %d", cookie.Expires, now)
				return false
			}
			// Cookie exists and is not expired
			return true
		}
	}

	logger.Info("[recognizer] titan_passport_id cookie not found")
	return false
}
