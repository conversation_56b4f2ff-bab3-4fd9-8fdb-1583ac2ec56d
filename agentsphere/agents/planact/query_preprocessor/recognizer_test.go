package queryrecognizer

import (
	"reflect"
	"testing"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
)

func TestExtractURIs(t *testing.T) {
	type args struct {
		s string
	}
	tests := []struct {
		name string
		args args
		want []string
	}{
		{
			name: "test",
			args: args{
				s: `帮我看看 https://bytedance.larkoffice.com/wiki/wikcnFGqc9NUyO0wLMoDcnE16Hh 这个文档`,
			},
			want: []string{"https://bytedance.larkoffice.com/wiki/wikcnFGqc9NUyO0wLMoDcnE16Hh"},
		},
		{
			name: "repo home",
			args: args{
				s: `clone https://code.byted.org/devgpt/kiwis 仓库`,
			},
			want: []string{"https://code.byted.org/devgpt/kiwis"},
		},
		{
			name: "repo http",
			args: args{
				s: `clone https://code.byted.org/devgpt/kiwis.git 仓库`,
			},
			want: []string{"https://code.byted.org/devgpt/kiwis.git"},
		},
		{
			name: "repo git protocol",
			args: args{
				s: `clone ******************/devgpt/kiwis.git 仓库`,
			},
			want: []string{"******************/devgpt/kiwis.git"},
		},
		{
			name: "repo ssh protocol",
			args: args{
				s: `clone ssh://******************:devgpt/kiwis.git 仓库`,
			},
			want: []string{"ssh://******************:devgpt/kiwis.git"},
		},
		{
			name: "repo merge request url",
			args: args{
				s: `帮我 review 这个 mr https://code.byted.org/devgpt/kiwis/merge_requests/8665?to_version=2`,
			},
			want: []string{"https://code.byted.org/devgpt/kiwis/merge_requests/8665?to_version=2"},
		},
		{
			name: "multiple urls",
			args: args{
				s: `我们有新同学加入 aime 的研发项目组，根据下面的文档，生成一个for 新同学的 Onboard 学习文档（两周的计划）

## 参考资料
- 模版（最终生成的文档可以参考这个模版）：https://bytedance.larkoffice.com/wiki/CzEUwnO6ii2fa5k6i6bcPqiWnXp
- aime 学习资料和项目介绍：https://bytedance.larkoffice.com/docx/H7kAdc9jjoKOkFx7CEicxYJ0nEQ
- aime 项目背景：https://bytedance.larkoffice.com/wiki/XxvZwyuyUiTlDEkrEHacSMJKnFe
- aime 规划和里程碑：https://bytedance.larkoffice.com/docx/FoG6dbmeUo1emlxESqEcIqTwnhb、https://bytedance.larkoffice.com/docx/UX0UdQQdsooI0wxL3ZDcqjuHnke
- 代码仓库（不需要分析代码仓库，把这个地址附到文档中方便新同学申请权限）：https://bits.bytedance.net/code/devgpt/kiwis/tree/master/agentsphere?ref_type=heads`,
			},
			want: []string{
				"https://bytedance.larkoffice.com/wiki/CzEUwnO6ii2fa5k6i6bcPqiWnXp",
				"https://bytedance.larkoffice.com/docx/H7kAdc9jjoKOkFx7CEicxYJ0nEQ",
				"https://bytedance.larkoffice.com/wiki/XxvZwyuyUiTlDEkrEHacSMJKnFe",
				"https://bytedance.larkoffice.com/docx/FoG6dbmeUo1emlxESqEcIqTwnhb",
				"https://bytedance.larkoffice.com/docx/UX0UdQQdsooI0wxL3ZDcqjuHnke",
				"https://bits.bytedance.net/code/devgpt/kiwis/tree/master/agentsphere?ref_type=heads",
			},
		},
		{
			name: "aeolus dashboard",
			args: args{
				s: `帮我分析看板: https://data.bytedance.net/aeolus/pages/dashboard/778706?appId=1002611&sheetId=1717228`,
			},
			want: []string{"https://data.bytedance.net/aeolus/pages/dashboard/778706?appId=1002611&sheetId=1717228"},
		},
		{
			name: "aeolus data query",
			args: args{
				s: `帮我分析：https://data.bytedance.net/aeolus/pages/dataQuery?appId=2608&id=1750582733001260&isDefault=1&reportQuerySchemaKey=b41fa126-8a04-4b84-b55f-986e101dadad&rid=4171757&sid=1880064`,
			},
			want: []string{"https://data.bytedance.net/aeolus/pages/dataQuery?appId=2608&id=1750582733001260&isDefault=1&reportQuerySchemaKey=b41fa126-8a04-4b84-b55f-986e101dadad&rid=4171757&sid=1880064"},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := ExtractURIs(tt.args.s); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ExtractURIs() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestConvertLarkDocumentURLsToMentions(t *testing.T) {
	type args struct {
		run  *iris.AgentRunContext
		uris []string
	}
	tests := []struct {
		name string
		args args
		want []iris.Mention
	}{
		{
			name: "lark doc",
			args: args{
				run:  nil,
				uris: []string{"https://bytedance.larkoffice.com/wiki/wikcnFGqc9NUyO0wLMoDcnE16Hh"},
			},
			want: []iris.Mention{&iris.LarkDocMention{
				BaseMention: iris.BaseMention{
					Type: iris.MentionTypeLarkDoc,
					ID:   "wikcnFGqc9NUyO0wLMoDcnE16Hh",
				},
				URL:   "https://bytedance.larkoffice.com/wiki/wikcnFGqc9NUyO0wLMoDcnE16Hh",
				DocID: "wikcnFGqc9NUyO0wLMoDcnE16Hh",
			}},
		},
		{
			name: "sg lark doc",
			args: args{
				run:  nil,
				uris: []string{"https://bytedance.sg.larkoffice.com/docx/CknAdEs0HoSIu4x9jjalG4KEgrb"},
			},
			want: []iris.Mention{&iris.LarkDocMention{
				BaseMention: iris.BaseMention{
					Type: iris.MentionTypeLarkDoc,
					ID:   "CknAdEs0HoSIu4x9jjalG4KEgrb",
				},
				URL:   "https://bytedance.sg.larkoffice.com/docx/CknAdEs0HoSIu4x9jjalG4KEgrb",
				DocID: "CknAdEs0HoSIu4x9jjalG4KEgrb",
			}},
		},
		{
			name: "us lark doc",
			args: args{
				run:  nil,
				uris: []string{"https://bytedance.us.larkoffice.com/docx/CknAdEs0HoSIu4x9jjalG4KEgrb"},
			},
			want: []iris.Mention{&iris.LarkDocMention{
				BaseMention: iris.BaseMention{
					Type: iris.MentionTypeLarkDoc,
					ID:   "CknAdEs0HoSIu4x9jjalG4KEgrb",
				},
				URL:   "https://bytedance.us.larkoffice.com/docx/CknAdEs0HoSIu4x9jjalG4KEgrb",
				DocID: "CknAdEs0HoSIu4x9jjalG4KEgrb",
			}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := ConvertLarkDocumentURLsToMentions(tt.args.run, tt.args.uris); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ConvertLarkDocumentURLsToMentions() = %+v, want %+v", got, tt.want)
			}
		})
	}
}
