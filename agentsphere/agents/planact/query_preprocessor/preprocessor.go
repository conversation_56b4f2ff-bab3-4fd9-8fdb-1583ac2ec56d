package queryrecognizer

import (
	"context"
	"fmt"
	"path"
	"time"

	"github.com/cenk/backoff"
	"github.com/mark3labs/mcp-go/mcp"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"github.com/sourcegraph/conc"
	"github.com/sourcegraph/conc/iter"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/background/browser"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/external/lark"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actors/browseract"
	codebaseactor "code.byted.org/devgpt/kiwis/agentsphere/agents/actors/codebase"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/devgpt/kiwis/lib/metrics"
)

type PreprocessedMentionItem struct {
	ID      string `json:"id"`
	Content string `json:"content"`
	Cost    int64  `json:"cost"` // cost in milliseconds
}

type MentionPreprocessor interface {
	Preprocess(run *iris.AgentRunContext, mentions []iris.Mention) error
	// prompt given to the LLM on how to use the preprocessed data
	GetPreprocessedItems(run *iris.AgentRunContext) []PreprocessedMentionItem
}

type ProcessedResult[T iris.Mention] struct {
	Mention T
	Path    string
	Cost    int64
}

const (
	CodebaseMentionStoreKey = "codebase_mention_store"
	LarkDocMentionStoreKey  = "lark_doc_mention_store"
	AeolusMentionStoreKey   = "aeolus_mention_store"
)

type CodebaseMentionPreprocessor struct{}

type CodebaseMentionStore struct {
	Downloaded map[string]ProcessedResult[iris.CodebaseMention] `json:"downloaded"`
}

func (p *CodebaseMentionPreprocessor) GetPreprocessedItems(run *iris.AgentRunContext) []PreprocessedMentionItem {
	store := iris.RetrieveStoreByKey[CodebaseMentionStore](run, CodebaseMentionStoreKey)
	if store.Downloaded == nil {
		return []PreprocessedMentionItem{}
	}
	return lo.MapToSlice(store.Downloaded, func(k string, v ProcessedResult[iris.CodebaseMention]) PreprocessedMentionItem {
		return PreprocessedMentionItem{
			ID:      fmt.Sprintf("@codebase:%s", v.Mention.RepoName),
			Content: fmt.Sprintf("downloaded to %s", v.Path),
			Cost:    v.Cost,
		}
	})
}

func (p *CodebaseMentionPreprocessor) Preprocess(run *iris.AgentRunContext, mentions []iris.Mention) error {
	logger := run.GetLogger()
	store := iris.RetrieveStoreByKey[CodebaseMentionStore](run, CodebaseMentionStoreKey)
	defer func() {
		iris.UpdateStoreByKey(run, CodebaseMentionStoreKey, store)
	}()
	if store.Downloaded == nil {
		store.Downloaded = make(map[string]ProcessedResult[iris.CodebaseMention])
	}

	wg := conc.NewWaitGroup()
	lo.ForEach(mentions, func(m iris.Mention, _ int) {
		wg.Go(func() {
			var mention *iris.CodebaseMention
			var ok bool
			if mention, ok = m.(*iris.CodebaseMention); !ok {
				return
			}
			// already downloaded
			if _, ok := store.Downloaded[mention.RepoName]; ok {
				return
			}

			start := time.Now()
			defer func() {
				metrics.AR.QueryPreprocessorCost.WithTags(&metrics.QueryPreprocessorTag{
					Type: string(mention.Type),
				}).Observe(float64(time.Since(start).Milliseconds()))
			}()

			dir := path.Base(mention.RepoName)
			logger.Infof("[preprocessor] cloning codebase repo %s(%s) to %s", mention.RepoName, mention.RawURL, dir)
			_, err := codebaseactor.GitClone(run, codebaseactor.GitCloneArgs{
				Directory:     dir,
				RepoPath:      lo.Ternary(mention.RawURL != "", mention.RawURL, mention.RepoName),
				Platform:      lo.Ternary(mention.RawURL != "", "", entity.GitPlatformCodebase), // leave empty to detect from repo url
				ReferenceName: lo.Ternary(mention.CommitID != "", mention.CommitID, lo.Ternary(mention.Tag != "", mention.Tag, "")),
				UnShallow:     mention.Branch == "" && mention.CommitID == "" && mention.Tag == "", // full clone if branch/commit/tag are not specified, as it may be a history analysis task
			})
			if err != nil {
				logger.Errorf("[preprocessor] failed to preprocess codebase repo %s to %s, err: %v", mention.RepoName, dir, err)
				return
			}
			cost := time.Since(start).Milliseconds()
			store.Downloaded[mention.RepoName] = ProcessedResult[iris.CodebaseMention]{
				Mention: *mention,
				Path:    dir,
				Cost:    cost,
			}
			logger.Infof("[preprocessor] cloned codebase repo %s to %s", mention.RepoName, dir)
		})
	})
	wg.Wait()
	return nil
}

type LarkDocMentionPreprocessor struct{}

type LarkDocMentionStore struct {
	Downloaded map[string]ProcessedResult[iris.LarkDocMention] `json:"downloaded"`
}

func (p *LarkDocMentionPreprocessor) Preprocess(run *iris.AgentRunContext, mentions []iris.Mention) error {
	logger := run.GetLogger()
	store := iris.RetrieveStoreByKey[LarkDocMentionStore](run, LarkDocMentionStoreKey)
	defer func() {
		iris.UpdateStoreByKey(run, LarkDocMentionStoreKey, store)
	}()
	if store.Downloaded == nil {
		store.Downloaded = make(map[string]ProcessedResult[iris.LarkDocMention])
	}

	// lark openapi has a rate limit of 5 requests per second
	it := iter.Iterator[iris.Mention]{
		MaxGoroutines: 2,
	}
	it.ForEach(mentions, func(m *iris.Mention) {
		var mention *iris.LarkDocMention
		var ok bool
		if mention, ok = (*m).(*iris.LarkDocMention); !ok || len(mention.URL) == 0 {
			return
		}
		if _, ok := store.Downloaded[mention.URL]; ok {
			return
		}

		start := time.Now()
		defer func() {
			metrics.AR.QueryPreprocessorCost.WithTags(&metrics.QueryPreprocessorTag{
				Type: string(mention.Type),
			}).Observe(float64(time.Since(start).Milliseconds()))
		}()
		var result *lark.DownloadLarkContentResult
		var err error
		exponentialBackoff := &backoff.ExponentialBackOff{
			InitialInterval:     1 * time.Second, // at least 1 second between retries, as lark rate limit is 5 requests per second
			RandomizationFactor: 0.5,
			Multiplier:          1.5,
			MaxInterval:         5 * time.Second,
			MaxElapsedTime:      1 * time.Minute, // should not take effect as we have a max retries, but needed to be set
			Clock:               backoff.SystemClock,
		}
		exponentialBackoff.Reset() // reset need to be called before using it
		backoff.Retry(func() error {
			result, err = lark.DownloadContentFromLark(run, lark.LarkToolArgs{
				DocumentURL: mention.URL,
			})
			return err
		}, backoff.WithContext(backoff.WithMaxRetries(exponentialBackoff, 3), run))
		if err != nil || result == nil {
			logger.Errorf("[preprocessor] failed to download lark doc %s, err: %v", mention.URL, err)
			return
		}
		store.Downloaded[mention.URL] = ProcessedResult[iris.LarkDocMention]{
			Mention: *mention,
			Path:    result.FilePath,
			Cost:    time.Since(start).Milliseconds(),
		}
		logger.Infof("[preprocessor] downloaded lark doc %s to %s, cost: %dms", mention.URL, result.FilePath, time.Since(start).Milliseconds())
	})
	return nil
}

func (p *LarkDocMentionPreprocessor) GetPreprocessedItems(run *iris.AgentRunContext) []PreprocessedMentionItem {
	store := iris.RetrieveStoreByKey[LarkDocMentionStore](run, LarkDocMentionStoreKey)
	if store.Downloaded == nil {
		return []PreprocessedMentionItem{}
	}
	return lo.MapToSlice(store.Downloaded, func(k string, v ProcessedResult[iris.LarkDocMention]) PreprocessedMentionItem {
		return PreprocessedMentionItem{
			ID:      fmt.Sprintf("@lark_doc:%s", v.Mention.URL),
			Content: fmt.Sprintf("downloaded to %s", v.Path),
			Cost:    v.Cost,
		}
	})
}

type AeolusMentionPreprocessor struct{}

type AeolusMentionStore struct {
	Downloaded map[string]ProcessedResult[iris.AeolusMention] `json:"downloaded"`
}

func (p *AeolusMentionPreprocessor) Preprocess(run *iris.AgentRunContext, mentions []iris.Mention) error {
	logger := run.GetLogger()
	store := iris.RetrieveStoreByKey[AeolusMentionStore](run, AeolusMentionStoreKey)
	defer func() {
		iris.UpdateStoreByKey(run, AeolusMentionStoreKey, store)
	}()
	if store.Downloaded == nil {
		store.Downloaded = make(map[string]ProcessedResult[iris.AeolusMention])
	}

	browserClient, err := browser.NewBrowserMCPClient(run)
	if err != nil {
		run.GetLogger().Errorf("failed to new a browser mcp service")
		return errors.WithMessage(err, "failed to new a browser mcp service")
	}

	// Process mentions sequentially
	for _, m := range mentions {
		var mention *iris.AeolusMention
		var ok bool
		if mention, ok = m.(*iris.AeolusMention); !ok || len(mention.URL) == 0 {
			continue
		}
		if _, ok := store.Downloaded[mention.URL]; ok {
			continue
		}

		logger.Infof("[preprocessor] processing aeolus url %s", mention.URL)

		start := time.Now()
		err = executeAeolusScript(run, browserClient, mention.URL)
		if err != nil {
			logger.Errorf("[preprocessor] failed to execute script for aeolus url %s, err: %v", mention.URL, err)
			continue
		}

		store.Downloaded[mention.URL] = ProcessedResult[iris.AeolusMention]{
			Mention: *mention,
			Cost:    time.Since(start).Milliseconds(),
		}
		logger.Infof("[preprocessor] processed aeolus url %s", mention.URL)
	}
	return nil
}

func (p *AeolusMentionPreprocessor) GetPreprocessedItems(run *iris.AgentRunContext) []PreprocessedMentionItem {
	store := iris.RetrieveStoreByKey[AeolusMentionStore](run, AeolusMentionStoreKey)
	if store.Downloaded == nil {
		return []PreprocessedMentionItem{}
	}
	return lo.MapToSlice(store.Downloaded, func(k string, v ProcessedResult[iris.AeolusMention]) PreprocessedMentionItem {
		return PreprocessedMentionItem{
			ID:   fmt.Sprintf("@aeolus:%s", v.Mention.URL),
			Cost: v.Cost,
		}
	})
}

// executeAeolusScript executes the Aeolus monitoring script via browser MCP client
func executeAeolusScript(run *iris.AgentRunContext, browserClient *browser.MCPClient, url string) error {
	ctx, cancel := context.WithTimeout(run, 10*time.Minute)
	defer cancel()

	// First goto the URL
	gotoRequest := mcp.CallToolRequest{}
	gotoRequest.Params.Name = browseract.ToolGoto
	gotoRequest.Params.Arguments = map[string]any{
		"url": url,
	}
	_, err := browserClient.Client.CallTool(ctx, gotoRequest)
	if err != nil {
		run.GetLogger().Errorf("Failed to goto url: %v", err)
		return errors.WithMessage(err, "failed to goto url")
	}

	// Then execute the script
	scriptRequest := mcp.CallToolRequest{}
	scriptRequest.Params.Name = browseract.ToolBrowserExecuteScript
	scriptRequest.Params.Arguments = map[string]any{
		"script": "from playwright.async_api import Page\nimport os\n\nasync def run(page: Page):\n    return await monitor_aeolus_dashboard_and_download_reports(page)",
	}
	_, err = browserClient.Client.CallTool(ctx, scriptRequest)
	if err != nil {
		return errors.WithMessage(err, "failed to execute script")
	}

	return nil
}
