package entity

import (
	"fmt"
	"time"
)

type Conversation struct {
	ID        string
	Title     string
	CreatedAt time.Time
	UpdatedAt time.Time
}

type MentionType string

const (
	MentionTypeCodebase   MentionType = "codebase"
	MentionTypeLarkDoc    MentionType = "lark_doc"
	MentionTypeAttachment MentionType = "attachment"
	MentionTypeAeolus     MentionType = "aeolus"
)

type CodebaseMention struct {
	RepoName string `json:"repo_name" mapstructure:"repo_name"`
	Branch   string `json:"branch" mapstructure:"branch"`
	Tag      string `json:"tag" mapstructure:"tag"`
	Path     string `json:"path" mapstructure:"path"` // 文件路径/目录路径
}

func (m *CodebaseMention) PromptString() string {
	if m == nil {
		return ""
	}
	s := fmt.Sprintf("%s", m.RepoName)
	if m.Branch != "" {
		s += fmt.Sprintf(":%s", m.Branch)
	}
	if m.Tag != "" {
		s += fmt.Sprintf(":%s", m.Tag)
	}
	if m.Path != "" {
		s += fmt.Sprintf(":%s", m.Path)
	}
	return s
}

type LarkDocMention struct {
	DocID           string `json:"doc_id" mapstructure:"doc_id"`
	Title           string `json:"title" mapstructure:"title"`
	URL             string `json:"url" mapstructure:"url"`
	KnowledgeBaseID string `json:"knowledge_base_id" mapstructure:"knowledge_base_id"`
}

func (m *LarkDocMention) PromptString() string {
	if m == nil {
		return ""
	}
	return m.Title
}

type AttachmentMention struct {
	ArtifactID string `json:"artifact_id" mapstructure:"artifact_id"`
	Path       string `json:"path" mapstructure:"path"`
}

func (m *AttachmentMention) PromptString() string {
	if m == nil {
		return ""
	}
	return m.Path
}

type AeolusMention struct {
	URL string `json:"url" mapstructure:"url"`
}

type Mention struct {
	ID   string      `json:"id" mapstructure:"id"`
	Type MentionType `json:"type" mapstructure:"type"`

	CodebaseMention   *CodebaseMention   `json:"codebase_mention" mapstructure:"codebase_mention"`
	LarkDocMention    *LarkDocMention    `json:"lark_doc_mention" mapstructure:"lark_doc_mention"`
	AttachmentMention *AttachmentMention `json:"attachment_mention" mapstructure:"attachment_mention"`
	AeolusMention     *AeolusMention     `json:"aeolus_mention" mapstructure:"aeolus_mention"`
}

func (m *Mention) PromptString() string {
	if m == nil {
		return ""
	}
	var s string
	switch m.Type {
	case MentionTypeCodebase:
		s = m.CodebaseMention.PromptString()
	case MentionTypeLarkDoc:
		s = m.LarkDocMention.PromptString()
	case MentionTypeAttachment:
		s = m.AttachmentMention.PromptString()
	}
	return fmt.Sprintf("@%s ", s)
}

type Message struct {
	ID             string
	ConversationID string
	Number         int
	Type           MessageType
	Creator        User
	ParentID       string
	Content        MessageContent
	Attachments    []AttachmentMeta
	Mentions       []*Mention
	Options        MessageOptions
	CreatedAt      time.Time
	UpdatedAt      time.Time
}

type MessageType string

const (
	MessageTypeNormal MessageType = "normal"
	MessageTypeNotice MessageType = "notice"
)

type MessageContent struct {
	Content string `json:"content"`
}

type MessageOptions struct {
	Locale string `json:"locale" mapstructure:"locale"`
}

type AttachmentMeta struct {
	ArtifactID string `json:"artifact_id"`
	Filename   string `json:"filename"`
}
