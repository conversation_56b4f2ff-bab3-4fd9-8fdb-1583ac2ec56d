package dal

import (
	"context"
	"errors"
	"fmt"

	"gorm.io/datatypes"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal/po"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
)

// CreateMCP 创建一个 MCP 工具
func (dao *DAO) CreateMCP(ctx context.Context, mcp *entity.MCP) (*entity.MCP, error) {
	if mcp == nil {
		return nil, fmt.Errorf("mcp is nil")
	}

	mcpPO := &po.MCPPO{
		MCPID:         mcp.MCPID,
		Name:          mcp.Name,
		EnName:        mcp.EnName,
		Description:   mcp.Description,
		EnDescription: mcp.EnDescription,
		IconURL:       mcp.IconURL,
		Config:        datatypes.NewJSONType(mcp.Config),
		Creator:       mcp.Creator,
		Source:        mcp.Source,
		Type:          mcp.Type,
		ForceActive:   mcp.ForceActive,
		SessionRoles:  datatypes.NewJSONType(mcp.SessionRoles),
		SourceSpaceID: mcp.SourceSpaceID,
		Scope:         mcp.Scope,
	}

	// 创建记录
	if err := dao.Conn.NewRequest(ctx).Create(mcpPO).Error; err != nil {
		return nil, err
	}

	// 将 PO 转换回 entity
	return poToMCP(mcpPO), nil
}

// GetMCPByID 根据 ID 获取 MCP 工具
func (dao *DAO) GetMCPByID(ctx context.Context, id string, source entity.MCPSource) (*entity.MCP, error) {
	var mcpPO po.MCPPO
	if err := dao.Conn.NewRequest(ctx).Where("mcp_id = ? and mcp_source = ?", id, source).First(&mcpPO).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}

	return poToMCP(&mcpPO), nil
}

// UpdateMCPOption 更新MCP的选项
type UpdateMCPOption struct {
	MCPID         string                  // MCP工具ID
	Source        entity.MCPSource        // 来源
	Name          *string                 // MCP工具名称
	EnName        *string                 // MCP工具英文名称
	Description   *string                 // MCP工具描述
	EnDescription *string                 // MCP工具英文描述
	IconURL       *string                 // MCP工具图标URL
	Type          *entity.MCPType         // MCP工具类型
	ForceActive   *bool                   // 是否强制激活
	Config        *entity.MCPConfig       // MCP工具配置结构
	SessionRoles  []nextagent.SessionRole // 支持的SessionRole列表 (二期新增)
	Scope         entity.MCPScope         // 可见范围
}

// UpdateMCP 更新 MCP 工具信息
func (dao *DAO) UpdateMCP(ctx context.Context, opt *UpdateMCPOption) error {
	if opt == nil {
		return fmt.Errorf("update option is nil")
	}

	var mcpPO po.MCPPO
	if err := dao.Conn.NewRequest(ctx).Where("mcp_id = ? and mcp_source = ?", opt.MCPID, opt.Source).First(&mcpPO).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("mcp not found")
		}
		return err
	}

	// 更新字段
	updates := make(map[string]interface{})
	if opt.Name != nil {
		updates["name"] = *opt.Name
	}
	if opt.EnName != nil {
		updates["en_name"] = *opt.EnName
	}
	if opt.Description != nil {
		updates["description"] = *opt.Description
	}
	if opt.EnDescription != nil {
		updates["en_description"] = *opt.EnDescription
	}
	if opt.IconURL != nil {
		updates["icon_url"] = *opt.IconURL
	}
	if opt.Type != nil {
		updates["type"] = *opt.Type
	}
	if opt.ForceActive != nil {
		updates["force_active"] = *opt.ForceActive
	}
	// 检查Config是否有值
	if opt.Config != nil {
		updates["config"] = datatypes.NewJSONType(*opt.Config)
	}
	if opt.Scope > 0 {
		updates["scope"] = opt.Scope
	}
	// 直接设置JSONType，nil切片会在数据库中表示为null，此字段为强制更新
	updates["session_roles"] = datatypes.NewJSONType(opt.SessionRoles)

	// 更新记录
	if err := dao.Conn.NewRequest(ctx).Model(&mcpPO).Where("id = ?", mcpPO.ID).Updates(updates).Error; err != nil {
		return err
	}

	return nil
}

// ListMCPsOption 列出 MCP 工具的选项
type ListMCPsOption struct {
	Name        *string            // 按名称搜索
	Sources     []entity.MCPSource // 按来源列表搜索
	Types       []entity.MCPType   // 按类型列表搜索
	Creator     *string            // 按创建者搜索，用于筛选个人MCP
	SpaceID     *string            // 按空间ID搜索
	Scope       *entity.MCPScope   // 按可见范围搜索
	StartID     *int64             // 起始ID，用于分页
	ForceActive *bool              // 按强制激活状态搜索
	Condition   string             // 自定义查询条件
	MCPIDs      []string           // 按MCP ID列表搜索
}

func (opt *ListMCPsOption) addConditions(query *gorm.DB) *gorm.DB {
	if opt == nil {
		return query
	}
	// 名称过滤中英文
	if opt.Name != nil && *opt.Name != "" {
		query = query.Where("name LIKE ? or en_name LIKE ?", "%"+*opt.Name+"%", "%"+*opt.Name+"%")
	}
	if len(opt.Sources) > 0 {
		query = query.Where("mcp_source IN ?", opt.Sources)
	}
	if len(opt.Types) > 0 {
		query = query.Where("type IN ?", opt.Types)
	}
	if opt.Creator != nil {
		query = query.Where("creator = ?", *opt.Creator)
	}
	if opt.SpaceID != nil {
		query = query.Where("source_space_id = ?", *opt.SpaceID)
	}
	if opt.Scope != nil {
		query = query.Where("scope =?", *opt.Scope)
	}
	if opt.StartID != nil {
		query = query.Where("id > ?", *opt.StartID)
	}
	if opt.ForceActive != nil {
		query = query.Where("force_active = ?", *opt.ForceActive)
	}
	if opt.Condition != "" {
		query = query.Where(opt.Condition)
	}
	if len(opt.MCPIDs) > 0 {
		query = query.Where("mcp_id IN (?)", opt.MCPIDs)
	}
	return query
}

// ListMCPs 列出 MCP 工具
func (dao *DAO) ListMCPs(ctx context.Context, opt *ListMCPsOption) ([]*entity.MCP, int32, error) {
	query := dao.Conn.NewRequest(ctx).Model(&po.MCPPO{})

	// 应用过滤条件
	query = opt.addConditions(query)

	// 计算总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 查询结果
	var mcpPOs []po.MCPPO
	if err := query.Find(&mcpPOs).Error; err != nil {
		return nil, 0, err
	}

	// 转换结果
	mcps := make([]*entity.MCP, 0, len(mcpPOs))
	for i := range mcpPOs {
		mcps = append(mcps, poToMCP(&mcpPOs[i]))
	}

	return mcps, int32(total), nil
}

// ListMCPActives 只列出 MCP 激活相关的字段
func (dao *DAO) ListMCPActives(ctx context.Context, opt *ListMCPsOption) ([]*entity.MCP, error) {
	query := dao.Conn.NewRequest(ctx).Model(&po.MCPPO{})

	// 应用过滤条件
	query = opt.addConditions(query)

	// 查询结果
	var mcpPOs []po.MCPPO
	if err := query.Select("mcp_id", "mcp_source", "force_active").Find(&mcpPOs).Error; err != nil {
		return nil, err
	}
	// 转换结果
	mcps := make([]*entity.MCP, 0, len(mcpPOs))
	for i := range mcpPOs {
		mcps = append(mcps, poToMCP(&mcpPOs[i]))
	}

	return mcps, nil
}

func (dao *DAO) CountMCPs(ctx context.Context, opt *ListMCPsOption) (int64, error) {
	query := dao.Conn.NewRequest(ctx).Model(&po.MCPPO{})

	// 应用过滤条件
	query = opt.addConditions(query)
	// 计算总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return 0, err
	}
	return total, nil
}

// GetUserActivatedMCPs 获取用户激活的所有 MCP 工具，返回的只有mcp_id/source/isactive
func (dao *DAO) GetUserActivatedMCPs(ctx context.Context, username, spaceID string) ([]*entity.MCP, error) {
	var userMCPs []po.UserMCPPO
	// 查询用户激活的 MCP ID，只返回激活状态的
	req := dao.Conn.NewRequest(ctx).Where("username = ? AND status = ?", username, entity.ActiveStatusActivate)
	if spaceID != "" {
		req = req.Where("space_id =?", spaceID)
	}
	if err := req.Find(&userMCPs).Error; err != nil {
		return nil, err
	}

	if len(userMCPs) == 0 {
		return []*entity.MCP{}, nil
	}

	// 转换结果，并标记为已激活
	mcps := make([]*entity.MCP, 0, len(userMCPs))
	for i := range userMCPs {
		mcp := &entity.MCP{
			MCPKey: entity.MCPKey{
				MCPID:  userMCPs[i].MCPID,
				Source: userMCPs[i].MCPSource,
			},
			IsActive: true,
		}
		mcps = append(mcps, mcp)
	}

	return mcps, nil
}

// ModifyMCPActivation 修改MCP工具的激活状态（激活或取消激活）
func (dao *DAO) ModifyMCPActivation(ctx context.Context, username string, mcpID string, status entity.ActiveStatus, source entity.MCPSource,
	spaceID string) (*entity.MCP, error) {
	// 检查 MCP 是否存在
	mcp, err := dao.GetMCPByID(ctx, mcpID, source)
	if err != nil {
		return nil, err
	}
	if mcp == nil {
		return nil, errors.New("mcp not found")
	}

	// 构建用户-MCP关联
	userMCP := &po.UserMCPPO{
		Username:  username,
		MCPID:     mcpID,
		Status:    status,
		MCPSource: mcp.Source, // 保存MCP的来源
		SpaceID:   spaceID,
	}

	// 使用Upsert方式，如果记录存在则更新status和updated_at
	err = dao.Conn.NewRequest(ctx).Clauses(
		clause.OnConflict{
			Columns:   []clause.Column{{Name: "username"}, {Name: "mcp_id"}},
			DoUpdates: clause.AssignmentColumns([]string{"status", "updated_at"}),
		},
	).Create(userMCP).Error

	if err != nil {
		return nil, err
	}

	// 设置激活状态标志
	mcp.IsActive = (status == entity.ActiveStatusActivate)
	return mcp, nil
}

// GetUserDeactivatedAIMEMCPs 获取用户取消激活的AIME来源 MCP 工具ID列表
func (dao *DAO) GetUserDeactivatedAIMEMCPs(ctx context.Context, username string) ([]string, error) {
	var userMCPs []po.UserMCPPO
	// 查询用户取消激活的 MCP ID，只返回取消激活状态且来源为AIME的
	if err := dao.Conn.NewRequest(ctx).
		Where("username = ? AND status = ? AND mcp_source = ?",
			username, entity.ActiveStatusDeactivate, entity.MCPSourceAIME).
		Find(&userMCPs).Error; err != nil {
		return nil, err
	}

	if len(userMCPs) == 0 {
		return []string{}, nil
	}

	// 提取 MCP ID
	mcpIDs := make([]string, 0, len(userMCPs))
	for _, userMCP := range userMCPs {
		mcpIDs = append(mcpIDs, userMCP.MCPID)
	}

	return mcpIDs, nil
}

// GetMCPsByIDsAndSource 根据MCP ID列表和来源批量获取MCP工具
func (dao *DAO) GetMCPsByIDsAndSource(ctx context.Context, mcpIDs []string, source entity.MCPSource) ([]*entity.MCP, error) {
	if len(mcpIDs) == 0 {
		return []*entity.MCP{}, nil
	}

	var mcpPOs []po.MCPPO
	if err := dao.Conn.NewRequest(ctx).
		Where("mcp_source = ? AND mcp_id IN ?", source, mcpIDs).
		Find(&mcpPOs).Error; err != nil {
		return nil, err
	}

	// 转换结果
	mcps := make([]*entity.MCP, 0, len(mcpPOs))
	for i := range mcpPOs {
		mcps = append(mcps, poToMCP(&mcpPOs[i]))
	}

	return mcps, nil
}

// poToMCP 将 PO 转换为 entity
func poToMCP(p *po.MCPPO) *entity.MCP {
	if p == nil {
		return nil
	}

	mcp := &entity.MCP{
		ID: p.ID,
		MCPKey: entity.MCPKey{
			MCPID:  p.MCPID,
			Source: p.Source,
		},
		Name:          p.Name,
		EnName:        p.EnName,
		Description:   p.Description,
		EnDescription: p.EnDescription,
		IconURL:       p.IconURL,
		Config:        p.Config.Data(),
		Creator:       p.Creator,
		Type:          p.Type,
		ForceActive:   p.ForceActive,
		CreatedAt:     p.CreatedAt,
		UpdatedAt:     p.UpdatedAt,
		IsActive:      p.ForceActive,
		SessionRoles:  p.SessionRoles.Data(),
		SourceSpaceID: p.SourceSpaceID,
		Scope:         p.Scope,
	}

	return mcp
}
