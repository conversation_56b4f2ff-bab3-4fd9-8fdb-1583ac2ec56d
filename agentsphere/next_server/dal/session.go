package dal

import (
	"context"
	"time"

	"github.com/AlekSi/pointer"
	"github.com/bytedance/sonic"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"gorm.io/datatypes"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/plugin/dbresolver"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal/po"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	"code.byted.org/devgpt/kiwis/lib/metrics"
	"code.byted.org/gopkg/logs/v2"
)

type CreateSessionOption struct {
	ID                 string
	Creator            string
	Status             entity.SessionStatus
	Context            entity.SessionContext
	Role               *entity.SessionRole
	RuntimeMetadata    entity.SessionRuntimeMetadata
	TemplateID         string
	SourceSpaceID      string
	CanResume          bool
	CanNotResumeReason entity.SessionCanNotResumeReason
}

func (d *DAO) CreateSession(ctx context.Context, opt CreateSessionOption) (*entity.Session, error) {
	p := &po.NextSessionPO{
		Uid:                opt.ID,
		Status:             string(opt.Status),
		Context:            datatypes.NewJSONType(opt.Context),
		RuntimeMetadata:    datatypes.NewJSONType(opt.RuntimeMetadata),
		Creator:            opt.Creator,
		SourceSpaceID:      opt.SourceSpaceID,
		CanResume:          lo.Ternary(opt.CanResume, 1, 0),
		CanNotResumeReason: int(opt.CanNotResumeReason),
	}
	if opt.Role != nil {
		p.Role = pointer.To(int(*opt.Role))
	}
	if opt.TemplateID != "" {
		p.TemplateID = opt.TemplateID
	}
	res := d.Conn.NewRequest(ctx).Create(p)

	if res.Error != nil {
		return nil, res.Error
	}
	return getSessionFromPO(p), nil
}

type CreateSessionWithMessageOption struct {
	SessionID       string
	MessageID       string
	Creator         string
	Status          entity.SessionStatus
	RuntimeMetadata entity.SessionRuntimeMetadata
	Context         entity.SessionContext
	Content         entity.MessageContent
	Role            entity.MessageRole
	Attachments     []*entity.Attachment
}

func (d *DAO) CreateSessionWithMessage(ctx context.Context, opt CreateSessionWithMessageOption) (*entity.Session, *entity.Message, error) {
	sessionPO := &po.NextSessionPO{
		Uid:             opt.SessionID,
		Status:          string(opt.Status),
		Context:         datatypes.NewJSONType(opt.Context),
		RuntimeMetadata: datatypes.NewJSONType(opt.RuntimeMetadata),
		Creator:         opt.Creator,
	}

	messagePO := &po.NextMessagePO{
		Uid:         opt.MessageID,
		SessionID:   opt.SessionID,
		Role:        string(opt.Role),
		Content:     datatypes.NewJSONType(opt.Content),
		Creator:     opt.Creator,
		Attachments: datatypes.NewJSONType(opt.Attachments),
	}

	err := d.Conn.NewRequest(ctx).Transaction(func(tx *gorm.DB) error {
		if err := tx.Create(sessionPO).Error; err != nil {
			return err
		}
		if err := tx.Create(messagePO).Error; err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		return nil, nil, errors.WithMessage(err, "failed to create session with message")
	}

	return getSessionFromPO(sessionPO), getMessageFromPO(messagePO), nil
}

type GetPreparedSessionOption struct {
	Creator       string
	Context       entity.SessionContext
	Role          *entity.SessionRole
	TemplateID    string
	SourceSpaceID string
	LogID         string
	New           bool // 兼容新版本逻辑，后续可以删除
}

func (d *DAO) GetPreparedSession(ctx context.Context, opt GetPreparedSessionOption) (*entity.Session, error) {
	var session *entity.Session
	err := d.Conn.NewRequest(ctx).Transaction(func(tx *gorm.DB) error {
		p := &po.NextSessionPO{}
		var err error
		if opt.New {
			err = tx.Clauses(clause.Locking{Strength: "UPDATE"}).Where("status = ? and role = ?", entity.SessionStatusPrepared, *opt.Role).Take(p).Error
		} else {
			err = tx.Clauses(clause.Locking{Strength: "UPDATE"}).Where("status = ? and role is null", entity.SessionStatusPrepared).Take(p).Error
		}

		if err != nil {
			return err
		}
		p.Status = string(entity.SessionStatusBound)
		p.Creator = opt.Creator
		p.Context = datatypes.NewJSONType(opt.Context)
		if opt.Role != nil {
			p.Role = pointer.To(int(*opt.Role))
		}
		if opt.TemplateID != "" {
			p.TemplateID = opt.TemplateID
		}
		if opt.SourceSpaceID != "" {
			p.SourceSpaceID = opt.SourceSpaceID
		}
		if opt.LogID != "" {
			metadata := p.RuntimeMetadata.Data()
			metadata.LogID = opt.LogID
			p.RuntimeMetadata = datatypes.NewJSONType(metadata)
		}
		err = tx.Save(p).Error
		if err != nil {
			return err
		}
		session = getSessionFromPO(p)
		return nil
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get prepared session")
	}
	return session, nil
}

func getSessionFromPO(p *po.NextSessionPO) *entity.Session {
	var role *entity.SessionRole
	if p.Role != nil {
		role = lo.ToPtr(entity.SessionRole(lo.FromPtr(p.Role)))
	}
	session := &entity.Session{
		ID:                  p.Uid,
		Status:              entity.SessionStatus(p.Status),
		Context:             p.Context.Data(),
		RuntimeMetaData:     p.RuntimeMetadata.Data(),
		Title:               p.Title,
		Creator:             p.Creator,
		Role:                role,
		TemplateID:          p.TemplateID,
		SourceSpaceID:       p.SourceSpaceID,
		LastMessageAt:       p.LastMessageAt,
		CreatedAt:           p.CreatedAt,
		UpdatedAt:           p.UpdatedAt,
		CanResume:           lo.Ternary(p.CanResume == 1, true, false),
		CanNotResumeReason:  entity.SessionCanNotResumeReason(p.CanNotResumeReason),
		LatestAgentResumeAt: p.LatestAgentResumeAt,
	}
	if p.StartedAt > 0 {
		session.StartedAt = time.UnixMilli(p.StartedAt)
	}
	return session
}

type UpdateSessionOption struct {
	ID                  string
	Status              *entity.SessionStatus
	Title               *string
	RuntimeMetadata     *entity.SessionRuntimeMetadata
	StartedAt           *time.Time
	LastMessageAt       *time.Time
	CanResume           *bool
	CanNotResumeReason  *entity.SessionCanNotResumeReason
	LatestAgentResumeAt *time.Time
}

func (d *DAO) UpdateSession(ctx context.Context, opt UpdateSessionOption) (session *entity.Session, err error) {
	if opt.ID == "" {
		return nil, errors.New("session id is empty")
	}

	query := d.Conn.NewRequest(ctx).Model(&po.NextSessionPO{}).Where("uid = ?", opt.ID)
	query = query.Unscoped()

	updater := map[string]any{}
	if opt.Status != nil {
		updater["status"] = string(*opt.Status)
	}
	if opt.Title != nil {
		updater["title"] = *opt.Title
	}
	if opt.RuntimeMetadata != nil {
		updater["runtime_metadata"] = datatypes.NewJSONType(opt.RuntimeMetadata)
	}
	if opt.StartedAt != nil {
		updater["started_at"] = (*opt.StartedAt).UnixMilli()
	}
	if opt.LastMessageAt != nil {
		updater["last_message_at"] = *opt.LastMessageAt
	}
	if opt.CanResume != nil {
		updater["can_resume"] = lo.Ternary(*opt.CanResume, 1, 0)
	}
	if opt.CanNotResumeReason != nil {
		updater["can_not_resume_reason"] = int(*opt.CanNotResumeReason)
	}
	if opt.LatestAgentResumeAt != nil {
		updater["latest_agent_resume_at"] = *opt.LatestAgentResumeAt
	}

	res := query.Updates(updater)
	if res.Error != nil {
		return nil, res.Error
	}
	if res.RowsAffected == 0 {
		optJSON, _ := sonic.Marshal(opt)
		logs.V1.CtxWarn(ctx, "no rows affected when update session: %s", optJSON)
	} else {
		// db更新成功，打一个status变化的metrics点, 做状态变更的监控. 因为update status调用地方逻辑比较散，先在这里打点，后续收敛后放到service内
		if opt.Status != nil {
			tags := &metrics.NextServerSessionStatusTag{
				Status: string(lo.FromPtr(opt.Status)),
			}
			_ = metrics.NSM.SessionStatusRate.WithTags(tags).Add(1)
		}
	}

	return d.GetSession(ctx, GetSessionOption{
		ID:   opt.ID,
		Sync: true,
	})
}

type GetSessionOption struct {
	ID   string
	Sync bool
}

func (d *DAO) GetSessionWithDeleted(ctx context.Context, opt GetSessionOption) (*entity.Session, error) {
	p := &po.NextSessionPO{}
	req := d.Conn.NewRequest(ctx).Where("uid = ?", opt.ID)
	if opt.Sync {
		req = req.Clauses(dbresolver.Write)
	}

	req = req.Unscoped()

	res := req.First(p)
	if res.Error != nil {
		return nil, res.Error
	}
	return getSessionFromPO(p), nil
}

func (d *DAO) GetSession(ctx context.Context, opt GetSessionOption) (*entity.Session, error) {
	p := &po.NextSessionPO{}
	req := d.Conn.NewRequest(ctx).Where("uid = ?", opt.ID)
	if opt.Sync {
		req = req.Clauses(dbresolver.Write)
	}
	res := req.First(p)
	if res.Error != nil {
		return nil, res.Error
	}
	return getSessionFromPO(p), nil
}

func (d *DAO) DeleteSession(ctx context.Context, id string) error {
	res := d.Conn.NewRequest(ctx).Where("uid = ?", id).Delete(&po.NextSessionPO{})
	if res.Error != nil {
		return res.Error
	}
	return nil
}

type ListSessionsOption struct {
	Creator              *string
	Status               *entity.SessionStatus
	SessionIDs           []string
	SourceSpaceID        *string
	Limit                int
	Offset               int
	OrderByLastMessageAt *bool
	LastMessageBefore    *time.Time
	Search               *string
}

func (d *DAO) ListSessions(ctx context.Context, opt ListSessionsOption) (int64, []*entity.Session, error) {
	p := &po.NextSessionPO{}
	req := d.Conn.NewRequest(ctx).Model(p)
	if opt.Creator != nil {
		req.Where("creator = ?", *opt.Creator)
	}
	if opt.Status != nil {
		req.Where("status = ?", string(*opt.Status))
	}
	if len(opt.SessionIDs) > 0 {
		req.Where("uid in ?", opt.SessionIDs)
	}
	if opt.SourceSpaceID != nil {
		req.Where("source_space_id = ?", *opt.SourceSpaceID)
	}
	if opt.LastMessageBefore != nil {
		req.Where("last_message_at <= ?", *opt.LastMessageBefore)
	}
	if opt.Search != nil && *opt.Search != "" {
		req.Where("title like ?", "%"+*opt.Search+"%")
	}

	var total int64
	if err := req.Count(&total).Error; err != nil {
		return 0, nil, err
	}

	pos := make([]*po.NextSessionPO, 0)

	if opt.OrderByLastMessageAt != nil && *opt.OrderByLastMessageAt {
		req.Order("last_message_at desc")
	} else {
		req.Order("id desc")
	}
	req = req.Offset(opt.Offset).Limit(opt.Limit)
	if err := req.Find(&pos).Error; err != nil {
		return 0, nil, err
	}

	return total, lo.Map(pos, func(s *po.NextSessionPO, _ int) *entity.Session {
		return getSessionFromPO(s)
	}), nil
}

type CountSessionsOption struct {
	Creator             *string
	Status              []entity.SessionStatus
	CreatedAt           *time.Time
	Role                *entity.SessionRole
	LatestAgentResumeAt *time.Time
	SourceSpaceID       *string
	Sync                bool
	Unscoped            bool
}

func (d *DAO) CountSessions(ctx context.Context, opt CountSessionsOption) (int64, error) {
	p := &po.NextSessionPO{}
	req := d.Conn.NewRequest(ctx).Model(p)
	if opt.Creator != nil {
		req.Where("creator = ?", *opt.Creator)
	}
	if opt.CreatedAt != nil {
		req.Where("created_at > ?", *opt.CreatedAt)
	}
	if opt.LatestAgentResumeAt != nil {
		req.Where("latest_agent_resume_at > ?", *opt.LatestAgentResumeAt)
	}
	if opt.Role != nil {
		req.Where("role = ?", *opt.Role)
	}
	if len(opt.Status) > 0 {
		req.Where("status in ?", opt.Status)
	}
	if opt.SourceSpaceID != nil {
		req.Where("source_space_id = ?", *opt.SourceSpaceID)
	}
	if opt.Sync {
		req = req.Clauses(dbresolver.Write)
	}
	// 包括删除的 session
	if opt.Unscoped {
		req = req.Unscoped()
	}

	var total int64
	if err := req.Count(&total).Error; err != nil {
		return 0, err
	}

	return total, nil
}

type ListSessionPartialsOption struct {
	Creator    *string
	Status     []entity.SessionStatus
	SessionIDs []string
	Sync       bool
	Unscoped   bool
}

func (d *DAO) ListSessionPartials(ctx context.Context, opt ListSessionPartialsOption) ([]*entity.SessionPartial, error) {
	p := &po.NextSessionPO{}
	req := d.Conn.NewRequest(ctx).Model(p)
	if opt.Creator != nil {
		req.Where("creator = ?", *opt.Creator)
	}
	if len(opt.Status) > 0 {
		req.Where("status in ?", opt.Status)
	}
	if opt.SessionIDs != nil {
		req.Where("uid in ?", opt.SessionIDs)
	}
	if opt.Sync {
		req = req.Clauses(dbresolver.Write)
	}
	// 包括删除的 session
	if opt.Unscoped {
		req = req.Unscoped()
	}

	var sessions []*po.NextSessionPO
	if err := req.Select("uid, status, title").Find(&sessions).Error; err != nil {
		return nil, err
	}
	return lo.Map(sessions, func(s *po.NextSessionPO, _ int) *entity.SessionPartial {
		return &entity.SessionPartial{
			ID:     s.Uid,
			Title:  s.Title,
			Status: entity.SessionStatus(s.Status),
		}
	}), nil
}
