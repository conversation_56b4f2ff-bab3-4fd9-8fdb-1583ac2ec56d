package dal

import (
	"context"
	"time"

	"gorm.io/plugin/dbresolver"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"gorm.io/datatypes"
	"gorm.io/gorm"

	agententity "code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal/po"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
)

// CreateTemplateVersion 创建模板版本
func (d *DAO) CreateTemplateVersion(ctx context.Context, version *entity.TemplateVersion) (*entity.TemplateVersion, error) {
	p := getTemplateVersionPOFromEntity(version)
	res := d.Conn.NewRequest(ctx).Create(p)
	if res.Error != nil {
		return nil, res.Error
	}
	return getTemplateVersionFromPO(p), nil
}

// GetTemplateVersion 根据ID获取模板版本
func (d *DAO) GetTemplateVersion(ctx context.Context, id string, sync bool) (*entity.TemplateVersion, error) {
	var p po.TemplateVersionPO
	var req = d.Conn.NewRequest(ctx)
	if sync {
		req = req.Clauses(dbresolver.Write)
	}
	res := req.Where("uid = ?", id).First(&p)
	if res.Error != nil {
		return nil, res.Error
	}
	return getTemplateVersionFromPO(&p), nil
}

// GetTemplateVersionByTemplateID 根据模板 ID 获取最新的可用版本
func (d *DAO) GetTemplateVersionByTemplateID(ctx context.Context, templateID string) (*entity.TemplateVersion, error) {
	var p po.TemplateVersionPO
	res := d.Conn.NewRequest(ctx).Where("template_id = ?", templateID).Last(&p)
	if res.Error != nil {
		return nil, res.Error
	}
	return getTemplateVersionFromPO(&p), nil
}

// IsTemplateIDExists 根据模板 ID 判断模板是否存在
func (d *DAO) IsTemplateIDExists(ctx context.Context, templateID string) (bool, error) {
	var count int64
	res := d.Conn.NewRequest(ctx).Model(&po.TemplateVersionPO{}).Where("template_id = ?", templateID).Count(&count)
	if res.Error != nil {
		return false, res.Error
	}
	return count > 0, nil
}

// GetTemplateNormalVersionByTemplateID 根据模板 ID 获取最新的可用版本
func (d *DAO) GetTemplateNormalVersionByTemplateID(ctx context.Context, templateID string) (*entity.TemplateVersion, error) {
	var p po.TemplateVersionPO
	res := d.Conn.NewRequest(ctx).Where("template_id = ? AND status != ?", templateID, entity.TemplateStatusDraft).Last(&p)
	if res.Error != nil {
		return nil, res.Error
	}
	return getTemplateVersionFromPO(&p), nil
}

// GetTemplateDraftVersionByTemplateID 根据模板 ID 获取最新的草稿版本
func (d *DAO) GetTemplateDraftVersionByTemplateID(ctx context.Context, templateID string) (*entity.TemplateVersion, error) {
	var p po.TemplateVersionPO
	res := d.Conn.NewRequest(ctx).Where("template_id = ? AND status = ?", templateID, entity.TemplateStatusDraft).Last(&p)
	if res.Error != nil {
		return nil, res.Error
	}
	return getTemplateVersionFromPO(&p), nil
}

// GetTemplateDraftVersionBySessionIDAndEventTimestamp 根据session id 和 event 时间戳获取模板版本
func (d *DAO) GetTemplateDraftVersionBySessionIDAndEventTimestamp(ctx context.Context, sessionID string, eventTimestamp int64) (*entity.TemplateVersion, error) {
	var p po.TemplateVersionPO
	res := d.Conn.NewRequest(ctx).Where("session_id = ? AND event_timestamp = ? AND status = ?", sessionID, eventTimestamp, entity.TemplateStatusDraft).First(&p)
	if res.Error != nil {
		return nil, res.Error
	}
	return getTemplateVersionFromPO(&p), nil
}

type ListTemplateVersionsOptions struct {
	VersionIDs  []string // 版本ID列表
	TemplateID  string   // 模板ID
	TemplateIDs []string // 模板ID列表
	Statuses    []string // 模板状态
	Category    string   // 模板标签
	Search      string   // 搜索关键字
	Creator     string   // 创建者
	Scope       string   // 作用域
	Limit       int      // 限制返回数量
	Offset      int      // 偏移量
	OrderBy     string   // 排序字段
	OrderDesc   bool     // 是否降序排序
	Condition   string   // 自定义条件
	Label       string
	SessionID   string // 哪些模版是基于这个 session 生成的
	SpaceID     string
}

func (o *ListTemplateVersionsOptions) addConditions(req *gorm.DB) *gorm.DB {
	if o.TemplateID != "" {
		req = req.Where("template_id = ?", o.TemplateID)
	}
	if len(o.TemplateIDs) > 0 {
		req = req.Where("template_id in (?)", o.TemplateIDs)
	}
	if len(o.VersionIDs) > 0 {
		req = req.Where("uid in (?)", o.VersionIDs)
	}
	if len(o.Statuses) > 0 {
		req = req.Where("status in (?)", o.Statuses)
	}
	if o.Category != "" {
		req = req.Where("category = ?", o.Category)
	}
	if o.Creator != "" {
		req = req.Where("creator = ?", o.Creator)
	}
	if o.Scope != "" {
		req = req.Where("scope = ?", o.Scope)
	}
	if o.Search != "" {
		req = req.Where("name like ?", "%"+o.Search+"%")
	}
	if o.SessionID != "" {
		req = req.Where("session_id = ?", o.SessionID)
	}
	if len(o.Condition) > 0 {
		req = req.Where(o.Condition)
	}
	if o.Label != "" {
		req = req.Where("label = ?", o.Label)
	}
	if o.SpaceID != "" {
		req = req.Where("source_space_id = ?", o.SpaceID)
	}
	return req
}

// ListTemplateVersions 列出模板版本，支持多种过滤条件和排序
func (d *DAO) ListTemplateVersions(ctx context.Context, opt ListTemplateVersionsOptions) ([]*entity.TemplateVersion, error) {
	req := d.Conn.NewRequest(ctx).Model(&po.TemplateVersionPO{})
	// 添加过滤条件
	req = opt.addConditions(req)

	// 添加排序
	orderBy := "updated_at"
	if opt.OrderBy != "" {
		orderBy = opt.OrderBy
	}
	if opt.OrderDesc {
		orderBy += " DESC"
	} else {
		orderBy += " ASC"
	}
	req = req.Order(orderBy)

	// 添加分页
	if opt.Limit > 0 {
		req = req.Limit(opt.Limit)
	}
	if opt.Offset > 0 {
		req = req.Offset(opt.Offset)
	}

	var pos []*po.TemplateVersionPO
	if err := req.Find(&pos).Error; err != nil {
		return nil, errors.WithMessage(err, "failed to list template versions")
	}

	return lo.Map(pos, func(item *po.TemplateVersionPO, _ int) *entity.TemplateVersion {
		return getTemplateVersionFromPO(item)
	}), nil
}

// ListPartialTemplateVersions 列出模板版本部分字段，支持多种过滤条件
func (d *DAO) ListPartialTemplateVersions(ctx context.Context, opt ListTemplateVersionsOptions) (entity.TemplateVersionPartials, error) {
	req := d.Conn.NewRequest(ctx).Model(&po.TemplateVersionPO{})
	// 添加过滤条件
	req = opt.addConditions(req)

	var pos []*po.TemplateVersionPO
	if err := req.Select("id, template_id, scope, star_count, updated_at").Find(&pos).Error; err != nil {
		return nil, errors.WithMessage(err, "failed to list template versions")
	}

	return lo.Map(pos, func(item *po.TemplateVersionPO, _ int) *entity.TemplateVersionPartial {
		return getTemplateVersionPartialFromPO(item)
	}), nil
}

func (d *DAO) IncrTemplateStarCount(ctx context.Context, templateID string) error {
	res := d.Conn.NewRequest(ctx).Model(&po.TemplateVersionPO{}).
		Where("template_id = ?", templateID).
		Update("star_count", gorm.Expr("star_count + ?", 1))
	if res.Error != nil {
		return res.Error
	}
	return nil
}

// DecrTemplateStarCount 减少模板收藏数, 如果收藏数为0, 则不更新
func (d *DAO) DecrTemplateStarCount(ctx context.Context, templateID string) error {
	res := d.Conn.NewRequest(ctx).Model(&po.TemplateVersionPO{}).
		Where("template_id = ? AND star_count > 0", templateID).
		Update("star_count", gorm.Expr("star_count - ?", 1))
	if res.Error != nil {
		return res.Error
	}
	return nil
}

// CountTemplateVersions 统计符合条件的模板版本数量
func (d *DAO) CountTemplateVersions(ctx context.Context, opt ListTemplateVersionsOptions) (int64, error) {
	req := d.Conn.NewRequest(ctx).Model(&po.TemplateVersionPO{})
	// 添加过滤条件
	req = opt.addConditions(req)

	var count int64
	if err := req.Count(&count).Error; err != nil {
		return 0, errors.WithMessage(err, "failed to count template versions")
	}

	return count, nil
}

type UpdateTemplateVersionOptions struct {
	Status          *string // 模板状态
	Category        *string // 模板类型
	Label           *string // 模板职能标签
	Scope           *string // 作用域
	Name            *string
	Prompt          *string
	Expired         *bool
	Edited          *bool
	PromptVariables []*entity.TemplateVariableSchema
	ExpSOP          *agententity.ExpSOP
	Plan            *string
	PlanSteps       []string
	SupportMCPs     []*entity.MCPKey
}

// UpdateTemplateVersion 更新模板版本
func (d *DAO) UpdateTemplateVersion(ctx context.Context, id string, opt UpdateTemplateVersionOptions) (*entity.TemplateVersion, error) {
	updateMap := map[string]any{}
	if opt.Status != nil {
		updateMap["status"] = *opt.Status
	}
	if opt.Category != nil {
		updateMap["category"] = *opt.Category
	}
	if opt.Scope != nil {
		updateMap["scope"] = *opt.Scope
	}
	if opt.Name != nil {
		updateMap["name"] = *opt.Name
	}
	if opt.Prompt != nil {
		updateMap["prompt_content"] = *opt.Prompt
	}
	if opt.Expired != nil {
		updateMap["expired"] = *opt.Expired
	}
	if opt.Edited != nil {
		updateMap["edited"] = *opt.Edited
	}
	if opt.PromptVariables != nil {
		updateMap["prompt_variables"] = datatypes.NewJSONType(opt.PromptVariables)
	}
	if opt.Plan != nil {
		updateMap["plan"] = *opt.Plan
	}
	if opt.PlanSteps != nil {
		updateMap["plan_steps"] = datatypes.NewJSONType(opt.PlanSteps)
	}
	if opt.ExpSOP != nil {
		e := datatypes.NewJSONType(*opt.ExpSOP)
		updateMap["exp_sop"] = &e
	}
	if opt.Label != nil {
		updateMap["label"] = *opt.Label
	}
	if opt.SupportMCPs != nil {
		updateMap["support_mcps"] = datatypes.NewJSONType(opt.SupportMCPs)
	}
	if len(updateMap) == 0 {
		return nil, nil
	}

	res := d.Conn.NewRequest(ctx).Model(&po.TemplateVersionPO{}).Where("uid = ?", id).Updates(updateMap)
	if res.Error != nil {
		return nil, errors.WithMessage(res.Error, "failed to update template version")
	}

	// 重新获取更新后的模板版本
	return d.GetTemplateVersion(ctx, id, true)
}

// UpdateTemplateVersionStatus 更新模板版本状态
func (d *DAO) UpdateTemplateVersionStatus(ctx context.Context, id, status string) error {
	res := d.Conn.NewRequest(ctx).Model(&po.TemplateVersionPO{}).
		Where("uid = ?", id).
		Updates(map[string]interface{}{
			"status":     status,
			"updated_at": time.Now(),
		})

	if res.Error != nil {
		return errors.WithMessage(res.Error, "failed to update template version status")
	}

	if res.RowsAffected == 0 {
		return errors.New("template version not found")
	}

	return nil
}

// DeleteTemplateVersion 删除模板版本
func (d *DAO) DeleteTemplateVersion(ctx context.Context, id string) error {
	res := d.Conn.NewRequest(ctx).Delete(&po.TemplateVersionPO{}, "uid = ?", id)
	if res.Error != nil {
		return errors.WithMessage(res.Error, "failed to delete template version")
	}

	if res.RowsAffected == 0 {
		return errors.New("template version not found")
	}

	return nil
}

// ResetTemplateVersionExperience 删除模板版本的经验并将 expired 标识置为 false
func (d *DAO) ResetTemplateVersionExperience(ctx context.Context, id string) error {
	res := d.Conn.NewRequest(ctx).Model(&po.TemplateVersionPO{}).
		Where("uid = ?", id).
		Updates(map[string]interface{}{
			"plan":       nil,
			"plan_steps": nil,
			"exp_sop":    nil,
			"expired":    false,
			"edited":     false,
		})

	if res.Error != nil {
		return errors.WithMessage(res.Error, "failed to delete template version experience")
	}

	if res.RowsAffected == 0 {
		return errors.New("template version not found")
	}

	return nil
}

// DeleteTemplateVersionsByTemplateID 根据模板ID删除所有模板版本
func (d *DAO) DeleteTemplateVersionsByTemplateID(ctx context.Context, templateID string) error {
	res := d.Conn.NewRequest(ctx).Delete(&po.TemplateVersionPO{}, "template_id = ?", templateID)
	if res.Error != nil {
		return errors.WithMessage(res.Error, "failed to delete template versions")
	}

	return nil
}

// DeleteDraftTemplateVersionsBySessionID 根据会话ID删除当前会话所有的模板草稿版本
func (d *DAO) DeleteDraftTemplateVersionsBySessionID(ctx context.Context, sessionID string) error {
	res := d.Conn.NewRequest(ctx).Delete(&po.TemplateVersionPO{}, "session_id = ? AND status = ?", sessionID, entity.TemplateStatusDraft)
	if res.Error != nil {
		if errors.Is(res.Error, gorm.ErrRecordNotFound) {
			return nil
		}
		return errors.WithMessage(res.Error, "failed to delete draft template versions")
	}

	return nil
}

// DeleteTemplateWithRelation 使用事务删除模板及其所有文件
func (d *DAO) DeleteTemplateWithRelation(ctx context.Context, templateID string) error {
	return d.Conn.NewRequest(ctx).Transaction(func(tx *gorm.DB) error {
		// 删除模板版本
		if err := tx.Delete(&po.TemplateVersionPO{}, "template_id = ?", templateID).Error; err != nil {
			return errors.WithMessage(err, "failed to delete template versions")
		}

		// 删除模板文件
		if err := tx.Delete(&po.TemplateFilePO{}, "template_id = ?", templateID).Error; err != nil {
			return errors.WithMessage(err, "failed to delete template files")
		}
		var shareIDs []string
		if err := tx.Model(&po.TemplateSharePO{}).Where("template_id =?", templateID).Pluck("uid", &shareIDs).Error; err != nil {
			return errors.WithMessage(err, "failed to get template share ids")
		}
		// 删除模板分享
		if err := tx.Delete(&po.TemplateSharePO{}, "template_id =?", templateID).Error; err != nil {
			return errors.WithMessage(err, "failed to delete template shares")
		}
		// 删除模板分享用户
		if err := tx.Delete(&po.TemplateShareTargetPO{}, "share_id IN (?)", shareIDs).Error; err != nil {
			return errors.WithMessage(err, "failed to delete template share targets")
		}
		// 删除模板收藏
		if err := tx.Delete(&po.TemplateStarPO{}, "template_id =?", templateID).Error; err != nil {
			return errors.WithMessage(err, "failed to delete template stars")
		}

		return nil
	})
}

// getTemplateVersionFromPO 将PO对象转换为实体对象
func getTemplateVersionFromPO(po *po.TemplateVersionPO) *entity.TemplateVersion {
	var expSop *agententity.ExpSOP
	if po.ExpSop != nil {
		sop := po.ExpSop.Data()
		expSop = &sop
	}
	e := &entity.TemplateVersion{
		ID:              po.ID,
		VersionID:       po.Uid,
		TemplateID:      po.TemplateID,
		Name:            po.Name,
		Status:          po.Status,
		Version:         po.Version,
		Scope:           po.Scope,
		Category:        po.Category,
		Label:           po.Label,
		Creator:         po.Creator,
		SessionID:       po.SessionID,
		EventTimestamp:  po.EventTimestamp,
		PromptContent:   lo.FromPtr(po.PromptContent),
		PromptVariables: nil,
		Plan:            lo.FromPtr(po.Plan),
		PlanSteps:       nil,
		ExpSop:          expSop,
		SupportMCPs:     po.SupportMcps.Data(),
		Expired:         po.Expired,
		Edited:          po.Edited,
		StarCount:       int(po.StarCount),
		SourceSpaceID:   po.SourceSpaceID,
		CreatedAt:       po.CreatedAt,
		UpdatedAt:       po.UpdatedAt,
	}
	if po.PromptVariables != nil {
		e.PromptVariables = po.PromptVariables.Data()
	}
	if po.PlanSteps != nil {
		e.PlanSteps = po.PlanSteps.Data()
	}
	return e
}

func getTemplateVersionPartialFromPO(po *po.TemplateVersionPO) *entity.TemplateVersionPartial {
	return &entity.TemplateVersionPartial{
		ID:         po.ID,
		TemplateID: po.TemplateID,
		Scope:      po.Scope,
		UpdatedAt:  po.UpdatedAt,
		StarCount:  int(po.StarCount),
	}
}

// getTemplateVersionPOFromEntity 将实体对象转换为PO对象
func getTemplateVersionPOFromEntity(e *entity.TemplateVersion) *po.TemplateVersionPO {
	var expSop *datatypes.JSONType[agententity.ExpSOP]
	if e.ExpSop != nil {
		sop := datatypes.NewJSONType(*e.ExpSop)
		expSop = &sop
	}
	var planSteps *datatypes.JSONType[[]string]
	if e.PlanSteps != nil {
		steps := datatypes.NewJSONType(e.PlanSteps)
		planSteps = &steps
	}
	var variables *datatypes.JSONType[entity.TemplateVariableSchemaList]
	if e.PromptVariables != nil {
		v := datatypes.NewJSONType(e.PromptVariables)
		variables = &v
	}

	return &po.TemplateVersionPO{
		Uid:             e.VersionID,
		TemplateID:      e.TemplateID,
		Name:            e.Name,
		Status:          e.Status,
		Version:         e.Version,
		Scope:           e.Scope,
		Category:        e.Category,
		Label:           e.Label,
		Creator:         e.Creator,
		SessionID:       e.SessionID,
		EventTimestamp:  e.EventTimestamp,
		PromptContent:   &e.PromptContent,
		PromptVariables: variables,
		Plan:            &e.Plan,
		PlanSteps:       planSteps,
		ExpSop:          expSop,
		SupportMcps:     datatypes.NewJSONType(e.SupportMCPs),
		Expired:         e.Expired,
		Edited:          e.Edited,
		StarCount:       uint(e.StarCount),
		SourceSpaceID:   e.SourceSpaceID,
	}
}
