package dal

import (
	"context"

	"github.com/samber/lo"
	"gorm.io/plugin/dbresolver"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal/po"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
)

// CreateTemplateStar 创建模板收藏
func (d *DAO) CreateTemplateStar(ctx context.Context, star *entity.TemplateStar) (*entity.TemplateStar, error) {
	p := getTemplateStarPOFromEntity(star)
	res := d.Conn.NewRequest(ctx).Create(p)
	if res.Error != nil {
		return nil, res.Error
	}
	return getTemplateStarFromPO(p), nil
}

// GetTemplateStarByKey 根据模板ID和用户名和空间 ID 获取模板收藏
func (d *DAO) GetTemplateStarByKey(ctx context.Context, templateID, username, spaceID string, sync bool) (*entity.TemplateStar, error) {
	var p po.TemplateStarPO
	var req = d.Conn.NewRequest(ctx)
	if sync {
		req = req.Clauses(dbresolver.Write)
	}
	res := req.Where("template_id = ? AND username = ? AND space_id = ?", templateID, username, spaceID).First(&p)
	if res.Error != nil {
		return nil, res.Error
	}
	return getTemplateStarFromPO(&p), nil
}

// ListTemplateStarsByUsername 根据用户名获取模板收藏列表
func (d *DAO) ListTemplateStarsByUsername(ctx context.Context, username string, limit, offset int) ([]*entity.TemplateStar, error) {
	var pos []*po.TemplateStarPO
	req := d.Conn.NewRequest(ctx).Where("username = ?", username).Order("created_at DESC")
	if limit > 0 {
		req = req.Limit(limit)
	}
	if offset > 0 {
		req = req.Offset(offset)
	}
	res := req.Find(&pos)
	if res.Error != nil {
		return nil, res.Error
	}
	return lo.Map(pos, func(p *po.TemplateStarPO, _ int) *entity.TemplateStar {
		return getTemplateStarFromPO(p)
	}), nil
}

// ListTemplateStarsByUsernameAndSpaceID 根据用户名和空间 ID 获取模板收藏列表
func (d *DAO) ListTemplateStarsByUsernameAndSpaceID(ctx context.Context, username, spaceID string, limit, offset int) ([]*entity.TemplateStar, error) {
	var pos []*po.TemplateStarPO
	req := d.Conn.NewRequest(ctx).Joins("LEFT JOIN template_version on template_version.template_id=template_star.template_id").
		Where("template_version.source_space_id = ?", spaceID).
		Where("template_star.username = ?", username).
		Order("template_star.created_at DESC")
	if limit > 0 {
		req = req.Limit(limit)
	}
	if offset > 0 {
		req = req.Offset(offset)
	}
	res := req.Find(&pos)
	if res.Error != nil {
		return nil, res.Error
	}
	return lo.Map(pos, func(p *po.TemplateStarPO, _ int) *entity.TemplateStar {
		return getTemplateStarFromPO(p)
	}), nil
}

// CountTemplateStarsByUsername 统计用户收藏的模板数量
func (d *DAO) CountTemplateStarsByUsername(ctx context.Context, username string) (int64, error) {
	var count int64
	res := d.Conn.NewRequest(ctx).Model(&po.TemplateStarPO{}).Where("username = ?", username).Count(&count)
	if res.Error != nil {
		return 0, res.Error
	}
	return count, nil
}

// CountTemplateStarsByUsernameAndSpaceID 统计用户在指定空间下收藏的模板数量
func (d *DAO) CountTemplateStarsByUsernameAndSpaceID(ctx context.Context, username, spaceID string) (int64, error) {
	var count int64
	res := d.Conn.NewRequest(ctx).Model(&po.TemplateStarPO{}).
		Where("space_id = ?", spaceID).
		Where("username = ?", username).
		Count(&count)
	if res.Error != nil {
		return 0, res.Error
	}
	return count, nil
}

// DeleteTemplateStar 删除模板收藏
func (d *DAO) DeleteTemplateStar(ctx context.Context, templateID, username, spaceID string) error {
	res := d.Conn.NewRequest(ctx).Where("template_id = ? AND username = ? AND space_id = ?", templateID, username, spaceID).Delete(&po.TemplateStarPO{})
	if res.Error != nil {
		return res.Error
	}
	return nil
}

// ListStarTemplateIDsByUsername 根据用户名获取收藏的模板ID列表
func (d *DAO) ListStarTemplateIDsByUsername(ctx context.Context, username string) ([]string, error) {
	var templateIDs []string
	res := d.Conn.NewRequest(ctx).Model(&po.TemplateStarPO{}).Where("username = ?", username).Pluck("template_id", &templateIDs)
	if res.Error != nil {
		return nil, res.Error
	}
	return templateIDs, nil
}

// ListStarTemplateIDsByUsernameAndSpaceID 根据用户名和空间 ID 获取指定空间下收藏的模板ID列表
func (d *DAO) ListStarTemplateIDsByUsernameAndSpaceID(ctx context.Context, username, spaceID string) ([]string, error) {
	var templateIDs []string
	res := d.Conn.NewRequest(ctx).Model(&po.TemplateStarPO{}).
		Where("space_id = ?", spaceID).
		Where("username = ?", username).
		Pluck("template_id", &templateIDs)
	if res.Error != nil {
		return nil, res.Error
	}
	return templateIDs, nil
}

// getTemplateStarFromPO 将PO对象转换为实体对象
func getTemplateStarFromPO(p *po.TemplateStarPO) *entity.TemplateStar {
	return &entity.TemplateStar{
		ID:         p.Uid,
		TemplateID: p.TemplateID,
		Username:   p.Username,
		SpaceID:    p.SpaceID,
		CreatedAt:  p.CreatedAt,
		UpdatedAt:  p.UpdatedAt,
	}
}

// getTemplateStarPOFromEntity 将实体对象转换为PO对象
func getTemplateStarPOFromEntity(e *entity.TemplateStar) *po.TemplateStarPO {
	return &po.TemplateStarPO{
		Uid:        e.ID,
		TemplateID: e.TemplateID,
		Username:   e.Username,
		SpaceID:    e.SpaceID,
	}
}
