package dal

import (
	"context"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"gorm.io/plugin/dbresolver"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal/po"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
)

// CreateTemplateShareTarget 创建模板分享用户
func (d *DAO) CreateTemplateShareTarget(ctx context.Context, shareUser *entity.TemplateShareTarget) (*entity.TemplateShareTarget, error) {
	p := getTemplateShareTargetPOFromEntity(shareUser)
	res := d.Conn.NewRequest(ctx).Create(p)
	if res.Error != nil {
		return nil, res.Error
	}
	return getTemplateShareTargetFromPO(p), nil
}

// GetTemplateShareTargetByKey 根据模板ID和用户名获取模板分享用户
func (d *DAO) GetTemplateShareTargetByKey(ctx context.Context, shareID, username string, sync bool) (*entity.TemplateShareTarget, error) {
	var p po.TemplateShareTargetPO
	var req = d.Conn.NewRequest(ctx)
	if sync {
		req = req.Clauses(dbresolver.Write)
	}
	res := req.Where("share_id = ? AND username = ?", shareID, username).First(&p)
	if res.Error != nil {
		return nil, res.Error
	}
	return getTemplateShareTargetFromPO(&p), nil
}

// GetTemplateShareTargetByTemplateIDAndUsername 根据模板ID和用户名获取模板分享目标用户记录
func (d *DAO) GetTemplateShareTargetByTemplateIDAndUsername(ctx context.Context, templateID, username string) (*entity.TemplateShareTarget, error) {
	var shareIDs []string
	res := d.Conn.NewRequest(ctx).Model(&po.TemplateSharePO{}).Where("template_id = ?", templateID).Pluck("uid", &shareIDs)
	if res.Error != nil {
		return nil, res.Error
	}
	if len(shareIDs) == 0 {
		return nil, nil
	}
	var p po.TemplateShareTargetPO
	res = d.Conn.NewRequest(ctx).Where("share_id IN (?) AND username = ?", shareIDs, username).First(&p)
	if res.Error != nil {
		return nil, res.Error
	}
	return getTemplateShareTargetFromPO(&p), nil
}

// RestoreTemplateShareTargetByShareIDAndUsername 根据分享ID和用户名恢复模板分享目标用户记录
func (d *DAO) RestoreTemplateShareTargetByShareIDAndUsername(ctx context.Context, shareID, username string) (*entity.TemplateShareTarget, error) {
	res := d.Conn.NewRequest(ctx).Model(&po.TemplateShareTargetPO{}).Where("share_id = ? AND username =?", shareID, username).
		Unscoped().Update("deleted_at", nil)
	if res.Error != nil {
		return nil, res.Error
	}
	if res.RowsAffected == 0 {
		return nil, errors.New("template share target not found or failed to restore")
	}
	var p po.TemplateShareTargetPO
	res = d.Conn.NewRequest(ctx).Where("share_id =? AND username =?", shareID, username).First(&p)
	if res.Error != nil {
		return nil, res.Error
	}
	return getTemplateShareTargetFromPO(&p), nil
}

// ListSharedTemplateIDsByTargetUsername 根据用户名获取分享给该用户的模板ID列表
func (d *DAO) ListSharedTemplateIDsByTargetUsername(ctx context.Context, username string) ([]string, error) {
	var shareIDs []string
	res := d.Conn.NewRequest(ctx).Model(&po.TemplateShareTargetPO{}).Where("username = ?", username).Pluck("share_id", &shareIDs)
	if res.Error != nil {
		return nil, res.Error
	}
	var templateIDs []string
	res = d.Conn.NewRequest(ctx).Model(&po.TemplateSharePO{}).Where("uid IN (?)", shareIDs).Pluck("template_id", &templateIDs)
	if res.Error != nil {
		return nil, res.Error
	}
	return lo.Uniq(templateIDs), nil
}

// ListSharedTemplateIDsByTargetUsernameAndSpaceID 根据用户名和空间ID获取分享给该用户的模板ID列表
func (d *DAO) ListSharedTemplateIDsByTargetUsernameAndSpaceID(ctx context.Context, username, spaceID string) ([]string, error) {
	var shareIDs []string
	res := d.Conn.NewRequest(ctx).Model(&po.TemplateShareTargetPO{}).Where("username = ?", username).Pluck("share_id", &shareIDs)
	if res.Error != nil {
		return nil, res.Error
	}
	var templateIDs []string
	res = d.Conn.NewRequest(ctx).Model(&po.TemplateSharePO{}).
		Joins("LEFT JOIN template_version on template_version.template_id=template_share.template_id").
		Where("template_share.uid IN (?)", shareIDs).
		Where("template_version.source_space_id = ?", spaceID).
		Pluck("template_version.template_id", &templateIDs)
	if res.Error != nil {
		return nil, res.Error
	}
	return lo.Uniq(templateIDs), nil
}

// DeleteTemplateShareTarget 删除模板分享用户
func (d *DAO) DeleteTemplateShareTarget(ctx context.Context, shareID, username string) error {
	res := d.Conn.NewRequest(ctx).Where("share_id = ? AND username = ?", shareID, username).Delete(&po.TemplateShareTargetPO{})
	if res.Error != nil {
		return res.Error
	}
	if res.RowsAffected == 0 {
		return errors.New("template share user not found")
	}
	return nil
}

// DeleteTemplateShareTargetsByShareID 根据模板ID删除所有模板分享用户
func (d *DAO) DeleteTemplateShareTargetsByShareID(ctx context.Context, shareID string) error {
	res := d.Conn.NewRequest(ctx).Where("share_id = ?", shareID).Delete(&po.TemplateShareTargetPO{})
	if res.Error != nil {
		return res.Error
	}
	return nil
}

// getTemplateShareTargetFromPO 将PO对象转换为实体对象
func getTemplateShareTargetFromPO(p *po.TemplateShareTargetPO) *entity.TemplateShareTarget {
	return &entity.TemplateShareTarget{
		ID:        p.Uid,
		ShareID:   p.ShareID,
		Username:  p.Username,
		CreatedAt: p.CreatedAt,
		UpdatedAt: p.UpdatedAt,
	}
}

// getTemplateShareTargetPOFromEntity 将实体对象转换为PO对象
func getTemplateShareTargetPOFromEntity(e *entity.TemplateShareTarget) *po.TemplateShareTargetPO {
	return &po.TemplateShareTargetPO{
		Uid:      e.ID,
		ShareID:  e.ShareID,
		Username: e.Username,
	}
}
