package dal

import (
	"context"
	"testing"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	"github.com/samber/lo"
	"github.com/stretchr/testify/require"
)

func TestDAO_Permission(t *testing.T) {
	dao := NewMockDAO(t)
	ctx := context.Background()
	resource1, err := dao.CreateResource(ctx, CreateResourceOption{
		ID:         "11111111-1111-1111-1111-11111111111",
		Type:       entity.ResourceTypeSession,
		ExternalID: "22222222-2222-2222-2222-22222222222",
		Owner:      "test-user",
		Status:     entity.ResourceStatusPublic,
		GroupID:    lo.ToPtr("33333333-3333-3333-3333-33333333333"),
	})
	require.NoError(t, err)
	require.Equal(t, "11111111-1111-1111-1111-11111111111", resource1.ID)
	resource2, err := dao.CreateResource(ctx, CreateResourceOption{
		ID:         "11111111-1111-1111-1111-11111111112",
		Type:       entity.ResourceTypeSession,
		ExternalID: "22222222-2222-2222-2222-22222222223",
		Owner:      "test-user",
		Status:     entity.ResourceStatusPrivate,
		GroupID:    lo.ToPtr("33333333-3333-3333-3333-33333333333"),
	})
	require.NoError(t, err)
	require.Equal(t, "11111111-1111-1111-1111-11111111112", resource2.ID)

	resources, err := dao.BatchGetResources(ctx, BatchGetResourceOption{IDs: []string{"11111111-1111-1111-1111-11111111111", "11111111-1111-1111-1111-11111111112"}})
	require.NoError(t, err)
	require.Equal(t, 2, len(resources))

	group, err := dao.ListGroupResourceRelation(ctx, ListGroupResourceRelationOption{
		GroupID: lo.ToPtr("33333333-3333-3333-3333-33333333333"),
	})
	require.NoError(t, err)
	require.Len(t, group, 2)

	res1, err := dao.GetResource(ctx, GetResourceOption{
		ID: lo.ToPtr("11111111-1111-1111-1111-11111111111"),
	})
	require.NoError(t, err)
	require.Equal(t, "11111111-1111-1111-1111-11111111111", res1.ID)

	res2, err := dao.GetResource(ctx, GetResourceOption{
		ExternalID: lo.ToPtr("22222222-2222-2222-2222-22222222222"),
		Type:       lo.ToPtr(entity.ResourceTypeSession),
	})
	require.NoError(t, err)
	require.Equal(t, "22222222-2222-2222-2222-22222222222", res2.ExternalID)

	total, res3, err := dao.ListResource(ctx, ListResourceOption{
		Owner:  lo.ToPtr("test-user"),
		Offset: 0,
		Limit:  1,
	})
	require.NoError(t, err)
	require.Equal(t, int64(2), total)
	require.Len(t, res3, 1)
	require.Equal(t, "11111111-1111-1111-1111-11111111111", res3[0].ID)

	total, res4, err := dao.ListResource(ctx, ListResourceOption{
		Owner:  lo.ToPtr("test-user"),
		Offset: 0,
		Limit:  10,
	})
	require.NoError(t, err)
	require.Equal(t, int64(2), total)
	require.Len(t, res4, 2)
	require.Equal(t, "11111111-1111-1111-1111-11111111112", res4[1].ID)

	total, res5, err := dao.ListResource(ctx, ListResourceOption{
		Owner:  lo.ToPtr("test-user"),
		Status: lo.ToPtr(entity.ResourceStatusPublic),
		Offset: 0,
		Limit:  10,
	})
	require.NoError(t, err)
	require.Equal(t, int64(1), total)
	require.Len(t, res5, 1)
	require.Equal(t, "11111111-1111-1111-1111-11111111111", res5[0].ID)

	total, res6, err := dao.ListResource(ctx, ListResourceOption{
		GroupID: lo.ToPtr("33333333-3333-3333-3333-33333333333"),
		Offset:  0,
		Limit:   10,
	})
	require.NoError(t, err)
	require.Equal(t, int64(2), total)
	require.Len(t, res6, 2)
	require.Equal(t, "11111111-1111-1111-1111-11111111111", res6[0].ID)

	// 创建权限
	permission1, err := dao.CreatePermission(ctx, CreatePermissionOption{
		ID:           "44444444-4444-4444-4444-44444444",
		ResourceID:   resource1.ID,
		ResourceType: resource1.Type,
		Type:         entity.PermissionTypeUser,
		ExternalID:   "test-user",
		Role:         entity.PermissionRoleAdmin,
	})
	require.NoError(t, err)
	require.Equal(t, "44444444-4444-4444-4444-44444444", permission1.ID)

	permission2, err := dao.CreatePermission(ctx, CreatePermissionOption{
		ID:           "55555555-5555-5555-5555-55555555",
		ResourceID:   resource2.ID,
		ResourceType: resource2.Type,
		Type:         entity.PermissionTypeDepartment,
		ExternalID:   "产品研发和工程架构-Dev Infra-Codebase",
		Role:         entity.PermissionRoleVisitor,
	})
	require.NoError(t, err)
	require.Equal(t, "55555555-5555-5555-5555-55555555", permission2.ID)

	permission3, err := dao.CreatePermission(ctx, CreatePermissionOption{
		ID:           "66666666-6666-6666-6666-66666666",
		ResourceID:   resource2.ID,
		ResourceType: resource2.Type,
		Type:         entity.PermissionTypeUser,
		ExternalID:   "test-user",
		Role:         entity.PermissionRoleMember,
	})
	require.NoError(t, err)
	require.Equal(t, "66666666-6666-6666-6666-66666666", permission3.ID)

	// 查询权限
	per1, err := dao.GetPermissionByID(ctx, permission1.ID, true)
	require.NoError(t, err)
	require.Equal(t, permission1.ID, per1.ID)

	per2, err := dao.FindPermissions(ctx, FindPermissionOption{
		ResourceID: resource2.ID,
		PermissionMetas: []entity.PermissionMeta{
			{
				Type:       entity.PermissionTypeDepartment,
				ExternalID: "产品研发和工程架构-Dev Infra-Codebase",
				Role:       entity.PermissionRoleVisitor,
			},
		},
	})
	require.NoError(t, err)
	require.Len(t, per2, 1)
	require.Equal(t, permission2.ID, per2[0].ID)

	per2, err = dao.FindPermissions(ctx, FindPermissionOption{
		ResourceID: resource2.ID,
		PermissionMetas: []entity.PermissionMeta{
			{
				Type:       entity.PermissionTypeDepartment,
				ExternalID: "产品研发和工程架构-Dev Infra-Codebase",
				Role:       entity.PermissionRoleVisitor,
			},
			{
				Type:       entity.PermissionTypeUser,
				ExternalID: "test-user",
				Role:       entity.PermissionRoleMember,
			},
		},
	})
	require.NoError(t, err)
	require.Len(t, per2, 2)

	per3, err := dao.ListPermission(ctx, ListPermissionOption{
		ResourceIDs: []string{resource2.ID},
	})
	require.NoError(t, err)
	require.Len(t, per3, 2)
	require.Equal(t, "产品研发和工程架构-Dev Infra-Codebase", per3[0].ExternalID)
	require.Equal(t, "test-user", per3[1].ExternalID)

	per4, err := dao.ListPermission(ctx, ListPermissionOption{
		ResourceType: lo.ToPtr(entity.ResourceTypeSession),
		ExternalID:   lo.ToPtr("test-user"),
		Type:         lo.ToPtr(entity.PermissionTypeUser),
	})
	require.NoError(t, err)
	require.Len(t, per4, 2)
	require.Equal(t, entity.PermissionRoleAdmin, per4[0].Role)
	require.Equal(t, entity.PermissionRoleMember, per4[1].Role)

	per4, err = dao.ListPermission(ctx, ListPermissionOption{
		ResourceType: lo.ToPtr(entity.ResourceTypeSession),
		ExternalID:   lo.ToPtr("test-user"),
		Type:         lo.ToPtr(entity.PermissionTypeUser),
		Roles:        []entity.PermissionRole{entity.PermissionRoleMember},
	})
	require.NoError(t, err)
	require.Len(t, per4, 1)
	require.Equal(t, entity.PermissionRoleMember, per4[0].Role)

	// 删除权限
	err = dao.DeletePermission(ctx, DeletePermissionOption{
		ID: permission1.ID,
	})
	require.NoError(t, err)

	per5, err := dao.ListPermission(ctx, ListPermissionOption{
		ExternalID: lo.ToPtr("test-user"),
		Type:       lo.ToPtr(entity.PermissionTypeUser),
	})
	require.NoError(t, err)
	require.Len(t, per5, 1)
	require.Equal(t, entity.PermissionRoleMember, per5[0].Role)

	err = dao.DeleteResource(ctx, DeleteResourceOption{
		ResourceID: resource2.ID,
	})
	require.NoError(t, err)

	per6, err := dao.ListPermission(ctx, ListPermissionOption{
		ExternalID: lo.ToPtr("test-user"),
		Type:       lo.ToPtr(entity.PermissionTypeUser),
	})
	require.NoError(t, err)
	require.Len(t, per6, 0)

	group, err = dao.ListGroupResourceRelation(ctx, ListGroupResourceRelationOption{
		ResourceID: &resource2.ID,
	})
	require.NoError(t, err)
	require.Len(t, group, 0)

	res7, err := dao.UpdateResource(ctx, UpdateResourceOption{
		ID:     resource1.ID,
		Owner:  lo.ToPtr("test-user-2"),
		Status: lo.ToPtr(entity.ResourceStatusPrivate),
	})
	require.NoError(t, err)
	require.Equal(t, "test-user-2", res7.Owner)
	require.Equal(t, entity.ResourceStatusPrivate, res7.Status)
}
