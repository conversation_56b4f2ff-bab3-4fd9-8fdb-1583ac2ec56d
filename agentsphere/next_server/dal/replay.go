package dal

import (
	"context"
	"time"

	"github.com/samber/lo"
	"gorm.io/datatypes"
	"gorm.io/plugin/dbresolver"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal/po"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	"code.byted.org/gopkg/logs/v2"
)

const (
	replayIDCacheKeyPrefix = "nextserver:replay:id:"
	replayCacheTTL         = 600 * time.Second
)

func getReplayCacheKey(id string) string {
	return replayIDCacheKeyPrefix + id
}

type GetReplayOption struct {
	ID    string
	Sync  bool
	Cache bool
}

func (d *DAO) GetReplay(ctx context.Context, opt GetReplayOption) (*entity.Replay, error) {
	if opt.Cache {
		var replay entity.Replay
		cacheKey := getReplayCacheKey(opt.ID)
		err := d.GetCache(ctx, cacheKey, &replay)
		if err == nil {
			logs.V1.CtxInfo(ctx, "cache hit for replay %s, key: %s", opt.ID, cacheKey)
			return &replay, nil
		}
	}

	p := &po.NextReplayPO{}
	req := d.Conn.NewRequest(ctx).Where("uid = ?", opt.ID)
	if opt.Sync {
		req = req.Clauses(dbresolver.Write)
	}

	res := req.First(p)
	if res.Error != nil {
		return nil, res.Error
	}

	replay := getReplayFromPO(p)

	// Set cache if enabled
	if opt.Cache {
		err := d.SetCache(ctx, getReplayCacheKey(opt.ID), replay, WithTTL(replayCacheTTL))
		if err != nil {
			logs.V1.CtxWarn(ctx, "failed to set cache for replay %s: %v", opt.ID, err)
		}
	}

	return replay, nil
}

type ListReplayOption struct {
	SessionID string
	Limit     int
	Offset    int
	Sync      bool
}

func (d *DAO) ListReplays(ctx context.Context, opt ListReplayOption) ([]*entity.Replay, error) {
	var replayPOs []*po.NextReplayPO
	req := d.Conn.NewRequest(ctx).Where("session_id = ?", opt.SessionID).Limit(opt.Limit).Offset(opt.Offset)
	if opt.Sync {
		req = req.Clauses(dbresolver.Write)
	}

	res := req.Find(&replayPOs)
	if res.Error != nil {
		return nil, res.Error
	}

	replays := lo.Map(replayPOs, func(p *po.NextReplayPO, _ int) *entity.Replay {
		return getReplayFromPO(p)
	})

	return replays, nil
}

func getReplayFromPO(p *po.NextReplayPO) *entity.Replay {
	// 处理snapshot_meta可能为空
	var snapshotMeata *entity.ReplaySnapshotMeta
	if p.SnapshotMeta != nil {
		snapshotMeata = lo.ToPtr(entity.ReplaySnapshotMeta(p.SnapshotMeta.Data()))
	}

	return &entity.Replay{
		ID:           p.Uid,
		SessionID:    p.SessionID,
		CreatedAt:    p.CreatedAt,
		UpdatedAt:    p.UpdatedAt,
		SnapshotMeta: snapshotMeata,
	}
}

type CreateReplayOption struct {
	ID           string
	SessionID    string
	SnapshotMeta entity.ReplaySnapshotMeta
}

func (d *DAO) CreateReplay(ctx context.Context, opt CreateReplayOption) (*entity.Replay, error) {
	p := &po.NextReplayPO{
		Uid:          opt.ID,
		SessionID:    opt.SessionID,
		SnapshotMeta: lo.ToPtr(datatypes.NewJSONType(opt.SnapshotMeta)),
	}
	res := d.Conn.NewRequest(ctx).Create(p)

	if res.Error != nil {
		return nil, res.Error
	}

	replay := getReplayFromPO(p)

	// Delete cache using delay double deletion
	err := d.DelCache(ctx, []string{
		getReplayCacheKey(opt.ID),
	})
	if err != nil {
		logs.V1.CtxError(ctx, "failed to delete cache for replay %s: %v", opt.ID, err)
	}

	return replay, nil
}
