package dal

import (
	"context"
	"fmt"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal/po"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	"code.byted.org/gopkg/logs/v2"
	"github.com/bytedance/sonic"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"gorm.io/gorm"
	"gorm.io/plugin/dbresolver"
)

type GetResourceOption struct {
	ID         *string
	ExternalID *string
	Type       *entity.ResourceType
	Sync       bool
}

func (d *DAO) GetResource(ctx context.Context, opt GetResourceOption) (*entity.Resource, error) {
	p := &po.NextResourcePO{}
	req := d.Conn.NewRequest(ctx)
	if opt.ID != nil {
		req = req.Where("uid = ?", opt.ID)
	} else if opt.ExternalID != nil && opt.Type != nil {
		req = req.Where("external_id = ? and type = ?", *opt.ExternalID, *opt.Type)
	} else {
		return nil, errors.New("no id or external_id or type is required")
	}
	if opt.Sync {
		req = req.Clauses(dbresolver.Write)
	}
	if err := req.Take(p).Error; err != nil {
		return nil, err
	}

	return getResourceFromPO(p), nil
}

type ListResourceOption struct {
	Owner   *string
	Type    *entity.ResourceType
	Status  *entity.ResourceStatus
	GroupID *string
	Offset  int
	Limit   int
}

func (d *DAO) ListResource(ctx context.Context, opt ListResourceOption) (int64, []*entity.Resource, error) {
	p := &po.NextResourcePO{}
	req := d.Conn.NewRequest(ctx).Model(p)
	if opt.Owner != nil {
		req.Where("owner = ?", *opt.Owner)
	}
	if opt.GroupID != nil {
		relations, err := d.ListGroupResourceRelation(ctx, ListGroupResourceRelationOption{
			GroupID: opt.GroupID,
		})
		if err != nil {
			return 0, nil, err
		}
		if len(relations) > 0 {
			req.Where("uid in ?", lo.Map(relations, func(item *entity.GroupResourceRelation, index int) string {
				return item.ResourceID
			}))
		} else {
			return 0, []*entity.Resource{}, nil
		}
	}
	if opt.Type != nil {
		req.Where("type = ?", *opt.Type)
	}
	if opt.Status != nil {
		req.Where("status = ?", *opt.Status)
	}
	var total int64
	if err := req.Count(&total).Error; err != nil {
		return 0, nil, err
	}

	pos := make([]*po.NextResourcePO, 0)
	if opt.Limit > 0 {
		req = req.Offset(opt.Offset).Limit(opt.Limit)
	}
	if err := req.Find(&pos).Error; err != nil {
		return 0, nil, err
	}

	return total, lo.Map(pos, func(s *po.NextResourcePO, _ int) *entity.Resource {
		return getResourceFromPO(s)
	}), nil
}

type CreateResourceOption struct {
	ID         string
	Type       entity.ResourceType
	ExternalID string
	Owner      string
	Status     entity.ResourceStatus

	GroupID *string
}

func (d *DAO) CreateResource(ctx context.Context, opt CreateResourceOption) (*entity.Resource, error) {
	p := &po.NextResourcePO{
		Uid:        opt.ID,
		Type:       string(opt.Type),
		ExternalID: opt.ExternalID,
		Owner:      opt.Owner,
		Status:     string(opt.Status),
	}

	err := d.Conn.NewRequest(ctx).Transaction(func(tx *gorm.DB) error {
		if err := tx.Create(p).Error; err != nil {
			return err
		}

		if opt.GroupID != nil {
			groupResourceRelationPO := &po.NextGroupResourceRelationPO{
				ResourceID: p.Uid,
				GroupID:    *opt.GroupID,
			}
			if err := tx.Create(groupResourceRelationPO).Error; err != nil {
				return err
			}
		}
		return nil
	})
	if err != nil {
		return nil, err
	}

	return getResourceFromPO(p), nil
}

type UpdateResourceOption struct {
	ID     string
	Owner  *string
	Status *entity.ResourceStatus
}

func (d *DAO) UpdateResource(ctx context.Context, opt UpdateResourceOption) (*entity.Resource, error) {
	if opt.ID == "" {
		return nil, errors.New("resource id is empty")
	}

	query := d.Conn.NewRequest(ctx).Model(&po.NextResourcePO{}).Where("uid = ?", opt.ID)

	updater := map[string]any{}
	if opt.Status != nil {
		updater["status"] = string(*opt.Status)
	}
	if opt.Owner != nil {
		updater["owner"] = *opt.Owner
	}

	res := query.Updates(updater)
	if res.Error != nil {
		return nil, res.Error
	}
	if res.RowsAffected == 0 {
		optJSON, _ := sonic.Marshal(opt)
		logs.V1.CtxWarn(ctx, "no rows affected when update resource: %s", optJSON)
	}

	return d.GetResource(ctx, GetResourceOption{
		ID:   &opt.ID,
		Sync: true,
	})
}

type DeleteResourceOption struct {
	ResourceID string
}

func (d *DAO) DeleteResource(ctx context.Context, opt DeleteResourceOption) error {
	return d.Conn.NewRequest(ctx).Transaction(func(tx *gorm.DB) error {
		// 1. 删除 Resource 数据
		if err := tx.Where("uid = ?", opt.ResourceID).Delete(&po.NextResourcePO{}).Error; err != nil {
			return err
		}

		// 2. 删除 ResourceGroupRelation 数据
		if err := tx.Where("resource_id = ?", opt.ResourceID).Delete(&po.NextGroupResourceRelationPO{}).Error; err != nil {
			return err
		}

		// 3. 删除 Permission 数据
		if err := tx.Where("resource_id = ?", opt.ResourceID).Delete(&po.NextPermissionPO{}).Error; err != nil {
			return err
		}
		return nil
	})
}

type CreatePermissionOption struct {
	ID           string
	ResourceID   string
	ResourceType entity.ResourceType
	Type         entity.PermissionType
	ExternalID   string
	Role         entity.PermissionRole
}

func (d *DAO) CreatePermission(ctx context.Context, opt CreatePermissionOption) (*entity.Permission, error) {
	p := &po.NextPermissionPO{
		Uid:          opt.ID,
		ResourceID:   opt.ResourceID,
		ResourceType: string(opt.ResourceType),
		Type:         string(opt.Type),
		ExternalID:   opt.ExternalID,
		Role:         int(opt.Role),
	}
	if err := d.Conn.NewRequest(ctx).Create(p).Error; err != nil {
		return nil, err
	}
	return getPermissionFromPO(p), nil
}

type BatchCreatePermissionOption struct {
	IDs             []string
	ResourceID      string
	ResourceType    entity.ResourceType
	PermissionMetas []entity.PermissionMeta
}

func (d *DAO) BatchCreatePermission(ctx context.Context, opt BatchCreatePermissionOption) ([]*entity.Permission, error) {
	if len(opt.PermissionMetas) == 0 {
		return nil, nil
	}
	if len(opt.IDs) != len(opt.PermissionMetas) {
		return nil, errors.New("invalid permission metas")
	}
	permissionPOs := make([]*po.NextPermissionPO, 0, len(opt.PermissionMetas))
	for i, meta := range opt.PermissionMetas {
		permissionPOs = append(permissionPOs, &po.NextPermissionPO{
			Uid:          opt.IDs[i],
			ResourceID:   opt.ResourceID,
			ResourceType: string(opt.ResourceType),
			Type:         string(meta.Type),
			ExternalID:   meta.ExternalID,
			Role:         int(meta.Role),
		})
	}
	if err := d.Conn.NewRequest(ctx).Create(&permissionPOs).Error; err != nil {
		return nil, err
	}

	return lo.Map(permissionPOs, func(item *po.NextPermissionPO, index int) *entity.Permission {
		return getPermissionFromPO(item)
	}), nil
}

func (d *DAO) GetPermissionByID(ctx context.Context, id string, sync bool) (*entity.Permission, error) {
	p := &po.NextPermissionPO{}
	req := d.Conn.NewRequest(ctx)
	req = req.Where("uid = ?", id)
	if sync {
		req = req.Clauses(dbresolver.Write)
	}
	if err := req.Take(p).Error; err != nil {
		return nil, err
	}

	return getPermissionFromPO(p), nil
}

type FindPermissionOption struct {
	ResourceID      string
	PermissionMetas []entity.PermissionMeta
	Sync            bool
}

func (d *DAO) FindPermissions(ctx context.Context, opt FindPermissionOption) ([]*entity.Permission, error) {
	if len(opt.PermissionMetas) == 0 {
		return nil, nil
	}
	pos := make([]*po.NextPermissionPO, 0)
	req := d.Conn.NewRequest(ctx)
	query := fmt.Sprintf("resource_id = '%s' and ((type = '%s' and external_id = '%s' and role = %d)", opt.ResourceID, opt.PermissionMetas[0].Type, opt.PermissionMetas[0].ExternalID, opt.PermissionMetas[0].Role)
	if len(opt.PermissionMetas) > 1 {
		for i := 1; i < len(opt.PermissionMetas); i++ {
			query += fmt.Sprintf(" or (type = '%s' and external_id = '%s' and role = %d)", opt.PermissionMetas[i].Type, opt.PermissionMetas[i].ExternalID, opt.PermissionMetas[i].Role)
		}
	}
	query = query + ")"
	req = req.Where(query)

	if opt.Sync {
		req = req.Clauses(dbresolver.Write)
	}
	if err := req.Find(&pos).Error; err != nil {
		return nil, err
	}

	return lo.Map(pos, func(item *po.NextPermissionPO, index int) *entity.Permission {
		return getPermissionFromPO(item)
	}), nil
}

type DeletePermissionOption struct {
	ID string
}

func (d *DAO) DeletePermission(ctx context.Context, opt DeletePermissionOption) error {
	req := d.Conn.NewRequest(ctx)
	if err := req.Where("uid = ?", opt.ID).Delete(&po.NextPermissionPO{}).Error; err != nil {
		return err
	}
	return nil
}

type BatchDeletePermissionsOption struct {
	IDs []string
}

func (d *DAO) BatchDeletePermissions(ctx context.Context, opt BatchDeletePermissionsOption) error {
	req := d.Conn.NewRequest(ctx)
	if err := req.Where("uid in ?", opt.IDs).Delete(&po.NextPermissionPO{}).Error; err != nil {
		return err
	}
	return nil
}

type ListPermissionOption struct {
	ResourceIDs  []string               // 获取指定一批资源的权限
	ResourceType *entity.ResourceType   // 和 type + external id 结合，获取某个用户对某种类型的资源的所有权限
	Type         *entity.PermissionType // 获取某个用户 / 部门有的权限
	ExternalID   *string
	Roles        []entity.PermissionRole
	Sync         bool
}

func (d *DAO) ListPermission(ctx context.Context, opt ListPermissionOption) ([]*entity.Permission, error) {
	p := &po.NextPermissionPO{}
	req := d.Conn.NewRequest(ctx).Model(p)
	if len(opt.ResourceIDs) > 0 {
		req.Where("resource_id in (?)", opt.ResourceIDs)
	}
	if opt.ResourceType != nil {
		req.Where("resource_type = ?", *opt.ResourceType)
	}
	if opt.ExternalID != nil && opt.Type != nil {
		req.Where("external_id = ? and type = ?", *opt.ExternalID, *opt.Type)
	}
	if len(opt.Roles) > 0 {
		req.Where("role in (?)", opt.Roles)
	}
	if opt.Sync {
		req = req.Clauses(dbresolver.Write)
	}

	pos := make([]*po.NextPermissionPO, 0)
	if err := req.Find(&pos).Error; err != nil {
		return nil, err
	}

	return lo.Map(pos, func(s *po.NextPermissionPO, _ int) *entity.Permission {
		return getPermissionFromPO(s)
	}), nil
}

type ListGroupResourceRelationOption struct {
	ResourceID *string
	GroupID    *string
}

func (d *DAO) ListGroupResourceRelation(ctx context.Context, opt ListGroupResourceRelationOption) ([]*entity.GroupResourceRelation, error) {
	p := &po.NextGroupResourceRelationPO{}
	req := d.Conn.NewRequest(ctx).Model(p)
	if opt.ResourceID != nil {
		req.Where("resource_id = ?", *opt.ResourceID)
	}
	if opt.GroupID != nil {
		req.Where("group_id = ?", *opt.GroupID)
	}

	pos := make([]*po.NextGroupResourceRelationPO, 0)
	if err := req.Find(&pos).Error; err != nil {
		return nil, err
	}

	return lo.Map(pos, func(s *po.NextGroupResourceRelationPO, _ int) *entity.GroupResourceRelation {
		return getGroupResourceRelationPO(s)
	}), nil
}

type BatchGetResourceOption struct {
	IDs []string
}

func (d *DAO) BatchGetResources(ctx context.Context, opt BatchGetResourceOption) ([]*entity.Resource, error) {
	if len(opt.IDs) == 0 {
		return []*entity.Resource{}, nil
	}
	p := &po.NextResourcePO{}
	req := d.Conn.NewRequest(ctx).Model(p)
	req.Where("uid in (?)", opt.IDs)

	pos := make([]*po.NextResourcePO, 0)
	if err := req.Find(&pos).Error; err != nil {
		return nil, err
	}

	return lo.Map(pos, func(s *po.NextResourcePO, _ int) *entity.Resource {
		return getResourceFromPO(s)
	}), nil
}

type BatchGetGroupResourceRelationsOption struct {
	ResourceIDs []string
}

func (d *DAO) BatchGetGroupResourceRelations(ctx context.Context, opt BatchGetGroupResourceRelationsOption) ([]*entity.GroupResourceRelation, error) {
	if len(opt.ResourceIDs) == 0 {
		return []*entity.GroupResourceRelation{}, nil
	}
	p := &po.NextGroupResourceRelationPO{}
	req := d.Conn.NewRequest(ctx).Model(p)
	req.Where("resource_id in (?)", opt.ResourceIDs)

	pos := make([]*po.NextGroupResourceRelationPO, 0)
	if err := req.Find(&pos).Error; err != nil {
		return nil, err
	}

	return lo.Map(pos, func(item *po.NextGroupResourceRelationPO, index int) *entity.GroupResourceRelation {
		return getGroupResourceRelationPO(item)
	}), nil
}

func getGroupResourceRelationPO(p *po.NextGroupResourceRelationPO) *entity.GroupResourceRelation {
	return &entity.GroupResourceRelation{
		ResourceID: p.ResourceID,
		GroupID:    p.GroupID,
	}
}

func getPermissionFromPO(p *po.NextPermissionPO) *entity.Permission {
	return &entity.Permission{
		ID:           p.Uid,
		ResourceID:   p.ResourceID,
		ResourceType: entity.ResourceType(p.ResourceType),
		Type:         entity.PermissionType(p.Type),
		ExternalID:   p.ExternalID,
		Role:         entity.PermissionRole(p.Role),
		CreatedAt:    p.CreatedAt,
		UpdatedAt:    p.UpdatedAt,
	}
}

func getResourceFromPO(p *po.NextResourcePO) *entity.Resource {
	return &entity.Resource{
		ID:         p.Uid,
		Type:       entity.ResourceType(p.Type),
		ExternalID: p.ExternalID,
		Owner:      p.Owner,
		Status:     entity.ResourceStatus(p.Status),
		CreatedAt:  p.CreatedAt,
		UpdatedAt:  p.UpdatedAt,
	}
}
