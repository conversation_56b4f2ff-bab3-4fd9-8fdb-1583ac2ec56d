// Code generated by SQL PO generator, DO NOT EDIT.
// This src file contains PO(persistent object) struct definition for database table `document`.
package po

import (
	datatypes "gorm.io/datatypes"
	softdelete "gorm.io/plugin/soft_delete"
	"time"
)

// DocumentPO is space document.
type DocumentPO struct {
	// ID is ID.
	ID int64 `gorm:"column:id;primaryKey"`
	// Uid is unique string.
	//
	// index: uk_uid, priority: 1.
	//
	Uid string `gorm:"column:uid;size:40"`
	// DatasetID is dataset ID.
	//
	// index: idx_dataset_id, priority: 1.
	//
	DatasetID string `gorm:"column:dataset_id;size:40"`
	// Creator is creator.
	Creator string `gorm:"column:creator;size:64"`
	// SourceUid is source uid.
	//
	// index: idx_source_uid, priority: 1.
	//
	SourceUid string `gorm:"column:source_uid;size:128"`
	// SourceType is source type:lark_wiki/lark_doc/lark_docx.
	SourceType string `gorm:"column:source_type;size:40"`
	// Title is title.
	Title string `gorm:"column:title;size:256"`
	// Content is content tos key.
	Content *string `gorm:"column:content;size:64"`
	// Owner is owner.
	//
	// index: idx_owner, priority: 1.
	//
	Owner string `gorm:"column:owner;size:64"`
	// CreatedAt is create timestamp.
	CreatedAt time.Time `gorm:"column:created_at"`
	// UpdatedAt is last update timestamp.
	UpdatedAt time.Time `gorm:"column:updated_at"`
	// LastUpdatedAt is doc last update timestamp.
	LastUpdatedAt time.Time `gorm:"column:last_updated_at"`
	// DeletedAt is deleted at.
	DeletedAt softdelete.DeletedAt `gorm:"column:deleted_at;softDelete:nano"`
	// ProcessStatus is doc last process status.
	ProcessStatus string `gorm:"column:process_status;size:64"`
	// FailedReason is doc process failed reason.
	FailedReason *datatypes.JSONType[DocumentFailedReason] `gorm:"column:failed_reason"`
	// ContentType is content type:doc/docx/sheet.
	ContentType string `gorm:"column:content_type;size:40"`
}

// TableName returns PO's corresponding DB table name: `document`.
func (*DocumentPO) TableName() string {
	return "document"
}
