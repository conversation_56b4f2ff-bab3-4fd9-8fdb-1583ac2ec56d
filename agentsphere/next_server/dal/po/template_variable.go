// Code generated by SQL PO generator, DO NOT EDIT.
// This src file contains PO(persistent object) struct definition for database table `next_template_variable`.
package po

import (
	datatypes "gorm.io/datatypes"
	softdelete "gorm.io/plugin/soft_delete"
	"time"
)

// NextTemplateVariablePO is template_variable.
type NextTemplateVariablePO struct {
	// ID is ID.
	ID int64 `gorm:"column:id;primaryKey"`
	// Uid is unique string ID, usually UUID.
	//
	// index: idx_uid, priority: 1.
	//
	Uid string `gorm:"column:uid;size:40"`
	// FormKey is key.
	//
	// index: uk_template_id_creator_key, priority: 3.
	//
	FormKey string `gorm:"column:form_key;size:64"`
	// FormValue is value.
	FormValue datatypes.JSONType[NextTemplateVariableFormValue] `gorm:"column:form_value"`
	// TemplateID is template ID, usually UUID.
	//
	// index: uk_template_id_creator_key, priority: 1.
	//
	TemplateID string `gorm:"column:template_id;size:40"`
	// Creator is creator.
	//
	// index: uk_template_id_creator_key, priority: 2.
	//
	Creator string `gorm:"column:creator;size:64"`
	// SpaceID is space id.
	SpaceID string `gorm:"column:space_id;size:40"`
	// CreatedAt is create timestamp.
	CreatedAt time.Time `gorm:"column:created_at"`
	// UpdatedAt is last update timestamp.
	UpdatedAt time.Time `gorm:"column:updated_at"`
	// DeletedAt is deleted at.
	DeletedAt softdelete.DeletedAt `gorm:"column:deleted_at;softDelete:nano"`
}

// TableName returns PO's corresponding DB table name: `next_template_variable`.
func (*NextTemplateVariablePO) TableName() string {
	return "next_template_variable"
}
