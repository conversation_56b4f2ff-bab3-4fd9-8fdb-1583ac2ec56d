// Code generated by SQL PO generator, DO NOT EDIT.
// This src file contains PO(persistent object) struct definition for database table `next_space`.
package po

import (
	softdelete "gorm.io/plugin/soft_delete"
	"time"
)

// NextSpacePO is space table.
type NextSpacePO struct {
	// ID is ID.
	ID int64 `gorm:"column:id;primaryKey"`
	// Uid is unique string ID, usually UUID.
	//
	// index: uk_uid, priority: 1.
	//
	Uid string `gorm:"column:uid;size:40"`
	// Name is space name.
	Name string `gorm:"column:name;size:255"`
	// NameEn is space name.
	NameEn string `gorm:"column:name_en;size:255"`
	// Description is space description.
	Description string `gorm:"column:description"`
	// Creator is space creator.
	//
	// index: idx_creator_created_at, priority: 1.
	//
	Creator string `gorm:"column:creator;size:64"`
	// Type is space type.
	Type string `gorm:"column:type;size:32"`
	// Status is space status.
	//
	// index: idx_status, priority: 1.
	//
	Status string `gorm:"column:status;size:64"`
	// CreatedAt is create timestamp.
	//
	// index: idx_creator_created_at, priority: 2.
	//
	CreatedAt time.Time `gorm:"column:created_at"`
	// UpdatedAt is last update timestamp.
	UpdatedAt time.Time `gorm:"column:updated_at"`
	// DeletedAt is deleted at.
	DeletedAt softdelete.DeletedAt `gorm:"column:deleted_at;softDelete:nano"`
}

// TableName returns PO's corresponding DB table name: `next_space`.
func (*NextSpacePO) TableName() string {
	return "next_space"
}
