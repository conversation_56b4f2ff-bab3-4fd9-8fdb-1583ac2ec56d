package po

import (
	"time"

	"gorm.io/datatypes"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
)

// MCPPO 定义 MCP 工具的持久化对象
type MCPPO struct {
	// ID 是主键
	ID int64 `gorm:"column:id;primaryKey"`
	// Name 是 MCP 工具名称
	Name string `gorm:"column:name;size:255;not null"`
	// EnName 是 MCP 英文工具名称
	EnName string `gorm:"column:en_name;size:255;not null"`
	// MCPID 是 MCP的ID，同来源下id唯一
	MCPID string `gorm:"column:mcp_id;size255;not null"`
	// Description 是 MCP 工具描述
	Description string `gorm:"column:description;type:text"`
	// EnDescription 是 MCP 工具描述
	EnDescription string `gorm:"column:en_description;type:text"`
	// IconURL 是 MCP 工具图标
	IconURL string `gorm:"column:icon_url;type:text"`
	// Config 是 MCP 工具配置，JSON 格式
	Config datatypes.JSONType[entity.MCPConfig] `gorm:"column:config;type:json"`
	// Creator 是创建者
	Creator string `gorm:"column:creator;size:255"`
	// Source 是来源 1-AIME 2-UserDefine 3-Cloud
	Source entity.MCPSource `gorm:"column:mcp_source;type:smallint;not null"`
	// Type 是类型 1-STDIO 2-SSE 3-StreamableHTTP 4-CloudSDK
	Type entity.MCPType `gorm:"column:type;type:smallint;not null;default:1"`
	// ForceActive 是否强制激活
	ForceActive bool `gorm:"column:force_active;not null;default:false"`
	// SessionRoles 支持的SessionRole列表，JSON格式存储，nil切片表示支持所有Role (二期新增)
	SessionRoles datatypes.JSONType[[]nextagent.SessionRole] `gorm:"column:session_roles;type:json"`
	// SourceSpaceID 是空间ID
	SourceSpaceID string `gorm:"column:source_space_id;size:40"`
	// Scope 是 MCP 工具的作用域 1-Private 2-Public
	Scope entity.MCPScope `gorm:"column:scope;type:smallint;not null"`
	// CreatedAt 是创建时间
	CreatedAt time.Time `gorm:"column:created_at;autoCreateTime"`
	// UpdatedAt 是更新时间
	UpdatedAt time.Time `gorm:"column:updated_at;autoUpdateTime"`
}

// TableName 返回对应的数据库表名
func (*MCPPO) TableName() string {
	return "mcp"
}

// UserMCPPO 定义用户-MCP 关联的持久化对象
type UserMCPPO struct {
	// ID 是主键
	ID int64 `gorm:"column:id;primaryKey;autoIncrement"`
	// Username 是用户名
	Username string `gorm:"column:username;size:255;not null;index:idx_username_mcp,priority:1,unique"`
	// MCPID 是关联的 MCP 工具 ID
	MCPID string `gorm:"column:mcp_id;size:255;not null;index:idx_username_mcp,priority:2,unique"`
	// Status 激活状态 1-激活 2-取消激活
	Status entity.ActiveStatus `gorm:"column:status;type:smallint;not null;default:1"`
	// MCPSource 记录MCP的来源，方便后续根据来源不同设置默认激活规则
	MCPSource entity.MCPSource `gorm:"column:mcp_source;type:smallint;not null;default:1"`
	// SpaceID 是空间ID
	SpaceID string `gorm:"column:space_id;size:40"`
	// CreatedAt 是创建时间
	CreatedAt time.Time `gorm:"column:created_at;autoCreateTime"`
	// UpdatedAt 是更新时间
	UpdatedAt time.Time `gorm:"column:updated_at;autoUpdateTime"`
}

// TableName 返回对应的数据库表名
func (*UserMCPPO) TableName() string {
	return "user_mcp"
}
