// Code generated by SQL PO generator, DO NOT EDIT.
// This src file contains PO(persistent object) struct definition for database table `template_version`.
package po

import (
	datatypes "gorm.io/datatypes"
	softdelete "gorm.io/plugin/soft_delete"
	"time"
)

// TemplateVersionPO is template version.
type TemplateVersionPO struct {
	// ID is ID.
	ID int64 `gorm:"column:id;primaryKey"`
	// Uid is unique string ID, usually UUID.
	//
	// index: idx_uid, priority: 1.
	//
	Uid string `gorm:"column:uid;size:40"`
	// TemplateID is template id.
	//
	// index: uk_template_id_version, priority: 1.
	//
	TemplateID string `gorm:"column:template_id;size:40"`
	// Name is template name.
	Name string `gorm:"column:name;size:1024"`
	// Status is template status.
	//
	// index: idx_status_category, priority: 1.
	//
	Status string `gorm:"column:status;size:64"`
	// Version is template version.
	//
	// index: uk_template_id_version, priority: 2.
	//
	Version string `gorm:"column:version;size:64"`
	// Scope is private/public.
	Scope string `gorm:"column:scope;size:64"`
	// Category is category, such as 代码开发、数据分析.
	//
	// index: idx_status_category, priority: 2.
	//
	Category string `gorm:"column:category;size:64"`
	// Label is label, such as 前端、客户端、服务端.
	Label string `gorm:"column:label;size:64"`
	// Creator is creator.
	Creator string `gorm:"column:creator;size:64"`
	// SessionID is session id, optional.
	//
	// index: idx_session_id_event_timestamp, priority: 1.
	//
	SessionID string `gorm:"column:session_id;size:40"`
	// EventTimestamp is latest event timestamp.
	//
	// index: idx_session_id_event_timestamp, priority: 2.
	//
	EventTimestamp int64 `gorm:"column:event_timestamp"`
	// PromptContent is prompt_content.
	PromptContent *string `gorm:"column:prompt_content"`
	// PromptVariables is prompt_variables.
	PromptVariables *datatypes.JSONType[TemplateVersionPromptVariables] `gorm:"column:prompt_variables"`
	// Plan is plan.
	Plan *string `gorm:"column:plan"`
	// PlanSteps is plan steps.
	PlanSteps *datatypes.JSONType[TemplateVersionPlanSteps] `gorm:"column:plan_steps"`
	// ExpSop is exp sop.
	ExpSop *datatypes.JSONType[TemplateVersionExpSop] `gorm:"column:exp_sop"`
	// SupportMcps is support mcps.
	SupportMcps datatypes.JSONType[TemplateVersionSupportMcps] `gorm:"column:support_mcps"`
	// Expired is if expired, exp_sop should by expired.
	Expired bool `gorm:"column:expired"`
	// Edited is whether user edited template.
	Edited bool `gorm:"column:edited"`
	// StarCount is star count.
	StarCount uint `gorm:"column:star_count"`
	// SourceSpaceID is source space id.
	SourceSpaceID string `gorm:"column:source_space_id;size:40"`
	// CreatedAt is create timestamp.
	CreatedAt time.Time `gorm:"column:created_at"`
	// UpdatedAt is last update timestamp.
	UpdatedAt time.Time `gorm:"column:updated_at"`
	// DeletedAt is deleted at.
	DeletedAt softdelete.DeletedAt `gorm:"column:deleted_at;softDelete:nano"`
}

// TableName returns PO's corresponding DB table name: `template_version`.
func (*TemplateVersionPO) TableName() string {
	return "template_version"
}
