// Code generated by SQL PO generator, DO NOT EDIT.
// This src file contains PO(persistent object) struct definition for database table `template_star`.
package po

import (
	softdelete "gorm.io/plugin/soft_delete"
	"time"
)

// TemplateStarPO is template star.
type TemplateStarPO struct {
	// ID is ID.
	ID int64 `gorm:"column:id;primaryKey"`
	// Uid is unique string ID, usually UUID.
	Uid string `gorm:"column:uid;size:40"`
	// TemplateID is template id.
	//
	// index: idx_template_id, priority: 1.
	//
	TemplateID string `gorm:"column:template_id;size:40"`
	// Username is username.
	//
	// index: idx_username, priority: 1.
	//
	Username string `gorm:"column:username;size:64"`
	// SpaceID is space id.
	SpaceID string `gorm:"column:space_id;size:40"`
	// CreatedAt is create timestamp.
	CreatedAt time.Time `gorm:"column:created_at"`
	// UpdatedAt is last update timestamp.
	UpdatedAt time.Time `gorm:"column:updated_at"`
	// DeletedAt is deleted at.
	DeletedAt softdelete.DeletedAt `gorm:"column:deleted_at;softDelete:nano"`
}

// TableName returns PO's corresponding DB table name: `template_star`.
func (*TemplateStarPO) TableName() string {
	return "template_star"
}
