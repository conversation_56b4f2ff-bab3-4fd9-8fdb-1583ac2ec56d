// Code generated by SQL PO generator, DO NOT EDIT.
// This src file contains PO(persistent object) struct definition for database table `next_group_resource_relation`.
package po

// NextGroupResourceRelationPO is group resource relation.
type NextGroupResourceRelationPO struct {
	// ID is ID.
	ID int64 `gorm:"column:id;primaryKey"`
	// ResourceID is resource id.
	//
	// index: uk_group_id_resource_id, priority: 2.
	//
	// index: idx_resource_id, priority: 1.
	//
	ResourceID string `gorm:"column:resource_id;size:40"`
	// GroupID is group id.
	//
	// index: uk_group_id_resource_id, priority: 1.
	//
	GroupID string `gorm:"column:group_id;size:40"`
}

// TableName returns PO's corresponding DB table name: `next_group_resource_relation`.
func (*NextGroupResourceRelationPO) TableName() string {
	return "next_group_resource_relation"
}
