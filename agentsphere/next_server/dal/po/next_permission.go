// Code generated by SQL PO generator, DO NOT EDIT.
// This src file contains PO(persistent object) struct definition for database table `next_permission`.
package po

import (
	softdelete "gorm.io/plugin/soft_delete"
	"time"
)

// NextPermissionPO is permission.
type NextPermissionPO struct {
	// ID is ID.
	ID int64 `gorm:"column:id;primaryKey"`
	// Uid is unique string ID, usually UUID.
	//
	// index: uk_uid, priority: 1.
	//
	Uid string `gorm:"column:uid;size:40"`
	// ResourceID is resource id.
	//
	// index: uk_resource_id_type_external_id_role_deleted_at, priority: 1.
	//
	// index: idx_resource_id_resource_type, priority: 1.
	//
	ResourceID string `gorm:"column:resource_id;size:40"`
	// ResourceType is resource type.
	//
	// index: idx_resource_id_resource_type, priority: 2.
	//
	// index: idx_external_id_type_resource_type, priority: 3.
	//
	ResourceType string `gorm:"column:resource_type;size:40"`
	// Type is permission type.
	//
	// index: uk_resource_id_type_external_id_role_deleted_at, priority: 2.
	//
	// index: idx_external_id_type_resource_type, priority: 2.
	//
	Type string `gorm:"column:type;size:40"`
	// ExternalID is external id maybe username, space id, department id.
	//
	// index: uk_resource_id_type_external_id_role_deleted_at, priority: 3.
	//
	// index: idx_external_id_type_resource_type, priority: 1.
	//
	ExternalID string `gorm:"column:external_id;size:64"`
	// Role is permission role, 1000 vistor, 2000 member, 3000 manager.
	//
	// index: uk_resource_id_type_external_id_role_deleted_at, priority: 4.
	//
	Role int `gorm:"column:role"`
	// CreatedAt is create timestamp.
	CreatedAt time.Time `gorm:"column:created_at"`
	// UpdatedAt is last update timestamp.
	UpdatedAt time.Time `gorm:"column:updated_at"`
	// DeletedAt is deleted at.
	//
	// index: uk_resource_id_type_external_id_role_deleted_at, priority: 5.
	//
	DeletedAt softdelete.DeletedAt `gorm:"column:deleted_at;softDelete:nano"`
}

// TableName returns PO's corresponding DB table name: `next_permission`.
func (*NextPermissionPO) TableName() string {
	return "next_permission"
}
