package serverhandler

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	permissionservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/permission"
	spaceservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/space"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/common"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	"code.byted.org/devgpt/kiwis/lib/hertz"
	"code.byted.org/devgpt/kiwis/lib/util"
	"code.byted.org/devgpt/kiwis/port/redis"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/middleware/hertz/pkg/app"
	"github.com/AlekSi/pointer"
	"github.com/samber/lo"
)

func (h *Handler) GetUserFeatures(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.GetUserFeaturesRequest](ctx, c)
	if req == nil {
		return
	}

	user, _ := h.AuthM.GetAccount(ctx, c)
	if user == nil {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "no auth")
		return
	}

	result := h.UserService.GetUserFeatures(ctx, user)
	log.V1.CtxInfo(ctx, "get user features result: %v", util.ToJson(result))

	// 优先从缓存里面获取 use space 结果，缓存时间 2min
	key := fmt.Sprintf("use_space:%s", user.Username)
	var useSpace bool
	useSpaceResult, err := h.RedisClient.Get(ctx, key)
	getFromSource := false
	if err != nil {
		getFromSource = true
		if !redis.IsNil(err) {
			log.V1.CtxError(ctx, "failed get use space info from redis: %v", err)
		}
	} else {
		if useSpaceResult == "true" || useSpaceResult == "false" {
			useSpace = lo.Ternary(useSpaceResult == "true", true, false)
		} else {
			getFromSource = true
		}
	}

	if getFromSource {
		// 从权限获取实际数据
		resources, err := h.PermissionService.GetUserPermissions(ctx, permissionservice.GetUserPermissionsOption{
			Account:      *user,
			ResourceType: entity.ResourceTypeSpace,
		})
		if err != nil {
			log.V1.CtxError(ctx, "failed get use space permission: %v", err)
			hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "internal server error")
			return
		}
		spaces, err := h.SpaceService.ListSpacesByIDs(ctx, spaceservice.ListSpacesByIDOption{
			ID: lo.Map(resources, func(item *entity.Resource, index int) string {
				return item.ExternalID
			}),
		})
		if err != nil {
			log.V1.CtxError(ctx, "failed list spaces by id: %v", err)
			hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "internal server error")
			return
		}
		useSpace = lo.ContainsBy(spaces, func(item *entity.Space) bool {
			return item.Type.IsProject()
		})

		err = h.RedisClient.Set(ctx, key, lo.Ternary(useSpace, "true", "false"), 2*time.Minute)
		if err != nil {
			log.V1.CtxWarn(ctx, "failed set redis space permission: %v", err)
		}
	}

	c.JSON(http.StatusOK, nextagent.GetUserFeaturesResponse{
		Invited:        result.Invited,
		SessionLimit:   result.SessionLimit,
		MessageLimit:   result.MessageLimit,
		MessageWarning: result.MessageWarning,
		RoleConfigs: lo.Map(result.Roles, func(item entity.RoleConfig, index int) *nextagent.SessionRoleConfig {
			return &nextagent.SessionRoleConfig{
				Role:            pointer.Get(item.Role.ToIDL()),
				UseInternalTool: item.AllowUseInternalTool,
				SessionLimit:    item.SessionLimit,
				MessageLimit:    item.MessageLimit,
				MessageWarning:  item.MessageWarning,
			}
		}),
		UseSpace: &useSpace,
	})
}

// GetGlobalFeature implements the GetGlobalFeature API
func (h *Handler) GetGlobalFeatures(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.GetGlobalFeaturesRequest](ctx, c)
	if req == nil {
		return
	}

	activity, err := h.ActivityService.GetCurrentActivity(ctx)
	if err != nil {
		log.V1.CtxError(ctx, "failed to get current activity: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "count remain invitation code err")
		return
	}
	if activity == nil {
		c.JSON(http.StatusOK, &nextagent.GetGlobalFeaturesResponse{})
		return
	}

	youngTalentConfig, err := h.AgentService.GetAgentConfigVersionByRole(ctx, lo.ToPtr(entity.SessionRoleYoungTalent), "")
	if err != nil {
		log.V1.CtxError(ctx, "failed to get young talent config: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get young talent config")
		return
	}

	lateralHireConfig, err := h.AgentService.GetAgentConfigVersionByRole(ctx, lo.ToPtr(entity.SessionRoleLateralHire), "")
	if err != nil {
		log.V1.CtxError(ctx, "failed to get lateral hire config: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get lateral hire config")
		return
	}

	industryVeteranConfig, err := h.AgentService.GetAgentConfigVersionByRole(ctx, lo.ToPtr(entity.SessionRoleIndustryVeteran), "")
	if err != nil {
		log.V1.CtxError(ctx, "failed to get industry veteran config: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get industry veteran config")
		return
	}

	resp := &nextagent.GetGlobalFeaturesResponse{
		CurrentActivity: &nextagent.Activity{
			ID:          activity.ID,
			Activited:   activity.IsActivated(),
			HaveNoPrize: activity.HaveNoPrize,
		},
		RoleFeatures: []*nextagent.RoleFeature{
			{
				Role:              nextagent.SessionRole_YoungTalent,
				MaxResumeDays:     formatTimeUnit(ctx, youngTalentConfig.RuntimeConfig.OrchestrationConfig.RuntimeDeleteTimeout, "d"),
				SleepingTimeHours: formatTimeUnit(ctx, youngTalentConfig.RuntimeConfig.OrchestrationConfig.RuntimeStopTimeout, "h"),
			},
			{
				Role:              nextagent.SessionRole_LateralHire,
				MaxResumeDays:     formatTimeUnit(ctx, lateralHireConfig.RuntimeConfig.OrchestrationConfig.RuntimeDeleteTimeout, "d"),
				SleepingTimeHours: formatTimeUnit(ctx, lateralHireConfig.RuntimeConfig.OrchestrationConfig.RuntimeStopTimeout, "h"),
			},
			{
				Role:              nextagent.SessionRole_IndustryVeteran,
				MaxResumeDays:     formatTimeUnit(ctx, industryVeteranConfig.RuntimeConfig.OrchestrationConfig.RuntimeDeleteTimeout, "d"),
				SleepingTimeHours: formatTimeUnit(ctx, industryVeteranConfig.RuntimeConfig.OrchestrationConfig.RuntimeStopTimeout, "h"),
			},
		},
	}
	c.JSON(http.StatusOK, resp)
}

// formatTimeUnit 完成 hour 到 day 时间单位的互转
func formatTimeUnit(ctx context.Context, t string, unit string) int32 {
	if len(t) < 2 {
		return 0
	}
	var result int64
	newT := t[:len(t)-1]
	result, err := strconv.ParseInt(newT, 10, 64)
	if err != nil {
		log.V1.CtxError(ctx, "failed to parse time value: %v", err)
		return 0
	}

	switch unit {
	case "d":
		switch t[len(t)-1] {
		case 'h':
			// 向上取整
			return int32((result-1)/24 + 1)
		case 'd':
			return int32(result)
		default:
			return 0
		}
	case "h":
		switch t[len(t)-1] {
		case 'h':
			return int32(result)
		case 'd':
			return int32(result * 24)
		default:
			return 0
		}
	default:
		return 0
	}
}
