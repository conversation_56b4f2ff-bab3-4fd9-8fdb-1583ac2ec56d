package serverhandler

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"net/url"
	"time"

	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/middleware/hertz/pkg/app"
	purl "code.byted.org/security/go-polaris/url"
	"github.com/samber/lo"

	serverservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service"
	sessionservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/session"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/common"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	"code.byted.org/devgpt/kiwis/lib/hertz"
)

type State struct {
	Next     string `json:"next"`     // 重定向地址
	Username string `json:"username"` // 用户名
	Email    string `json:"email"`    // 邮箱
}

const ErrAuthorizationDenied = "access_denied"

func (h *Handler) LarkAuth(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.LarkAuthRequest](ctx, c)
	if req == nil || (req.GetCode() == "" && req.GetState() == "" && req.GetError() == "") {
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "request param is required")
		return
	}
	if req.GetCode() == "" && req.GetError() == "" {
		log.V1.CtxError(ctx, "code or error is required")
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "code or error is required")
		return
	}
	if req.GetError() != "" && req.GetError() != ErrAuthorizationDenied {
		log.V1.CtxError(ctx, "lark err: %v", req.GetError())
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), req.GetError())
		return
	}
	var state State
	stateStr, err := url.QueryUnescape(req.GetState())
	if err != nil {
		log.V1.CtxError(ctx, "failed to decode state value: %s", req.GetState())
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), err.Error())
		return
	}
	err = json.Unmarshal([]byte(stateStr), &state)
	if err != nil {
		log.V1.CtxError(ctx, "failed to decode state value: %s", req.GetState())
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), err.Error())
		return
	}
	if state.Username == "" {
		log.V1.CtxError(ctx, "username is required")
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "username is required")
		return
	}

	// 检查重定向地址是否合法
	parsedURL, err := url.Parse(h.LarkConf.GetValue().RedirectURI)
	if err != nil {
		log.V1.CtxError(ctx, "failed to parse redirect uri: %s", h.LarkConf.GetValue().RedirectURI)
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrInternal), err.Error())
		return
	}
	urlChecker := purl.NewHTTPRedirectChecker(parsedURL.Host)
	if ok, _ := urlChecker.CheckStr(state.Next); !ok {
		log.V1.CtxError(ctx, "invalid redirect uri: %s", state.Next)
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "invalid redirect uri")
		return
	}
	_, err = h.LarkService.LarkAuth(ctx, state.Username, state.Email, req.GetCode(), req.GetError() == ErrAuthorizationDenied)
	if err != nil {
		log.V1.CtxError(ctx, "failed to lark auth: %+v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), err.Error())
		return
	}
	c.Redirect(http.StatusTemporaryRedirect, []byte(state.Next))
}

func (h *Handler) CheckLarkAuth(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.CheckLarkAuthRequest](ctx, c)
	if req == nil {
		logs.CtxInfo(ctx, "[CheckLarkAuth] request is nil")
		return
	}
	if req.URL == "" {
		logs.CtxError(ctx, "[CheckLarkAuth] url is required")
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "url is required")
		return
	}
	account, _ := h.AuthM.GetAccount(ctx, c)
	if account == nil {
		logs.CtxInfo(ctx, "[CheckLarkAuth] account is nil")
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "account is required")
		return
	}
	authorizationDenied, authorization, err := h.LarkService.CheckLarkAuth(ctx, account.Username)
	if err != nil {
		logs.CtxError(ctx, "[CheckLarkAuth] failed to check lark auth err: %v, username: %v", err, account.Username)
	}
	state := State{
		Next:     req.GetURL(),
		Username: account.Username,
		Email:    account.Email,
	}
	stateBytes, err := json.Marshal(state)
	if err != nil {
		logs.CtxError(ctx, "[CheckLarkAuth] failed to marshal state err: %v, state %v", err, state)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), err.Error())
		return
	}
	stateParam := url.QueryEscape(string(stateBytes))
	//新版本
	authURL := fmt.Sprintf("https://accounts.feishu.cn/open-apis/authen/v1/authorize?client_id=%s&redirect_uri=%s&scope=%s&state=%s",
		h.LarkConf.GetValue().AppID, h.LarkConf.GetValue().RedirectURI, h.LarkConf.GetValue().Scope, stateParam)

	logs.CtxInfo(ctx, "[CheckLarkAuth] Current time is %v, user is %v, authorization is %v, authorizationDenied is %v, authURL is %s", time.Now(), account.Username, authorization, authorizationDenied, authURL)

	c.JSON(http.StatusOK, nextagent.CheckLarkAuthResponse{
		Authorization:       authorization,
		RedirectURL:         &authURL,
		AuthorizationDenied: &authorizationDenied,
	})
}

func (h *Handler) GetLarkTicket(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.GetLarkTicketRequest](ctx, c)
	if req == nil {
		logs.CtxInfo(ctx, "[GetLarkTicket] request is nil")
		return
	}

	account, _ := h.AuthM.GetAccount(ctx, c)
	if account == nil {
		logs.CtxInfo(ctx, "[GetLarkTicket] account is nil")
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "account is required")
		return
	}

	// 如果没有给飞书授权，返回错误
	// authorizationDenied, authorization, err := h.LarkService.CheckLarkAuth(ctx, account.Username)
	ticket, err := h.LarkService.GetLarkTicket(ctx, account.Username, account.Email)
	if err != nil {
		logs.CtxError(ctx, "[GetLarkTicket] failed to get lark ticket err: %v, email: %v", err, account.Email)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), err.Error())
		return
	}

	c.JSON(http.StatusOK, nextagent.GetLarkTicketResponse{
		Authorization: ticket.Authorization,
		JSApiTicket: &nextagent.JSApiTicket{
			Ticket:   ticket.JSApiTicket.Ticket,
			ExpireIn: ticket.JSApiTicket.ExpireIn,
		},
		OpenID:    ticket.OpenID,
		AppID:     ticket.AppID,
		Signature: lo.ToPtr(""),
	})
}

func (h *Handler) SendLarkReplayLinkMessage(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.SendLarkReplayLinkMessageRequest](ctx, c)
	if req == nil {
		return
	}
	account, _ := h.AuthM.GetAccount(ctx, c)
	if account == nil {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "account is required")
		return
	}
	if req.Username == "" {
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "username is required")
		return
	}
	if req.ReplayLink == "" {
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "replay_link is required")
		return
	}
	if req.TaskName == "" {
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "task_name is required")
		return
	}
	if req.ToType != "user" && req.ToType != "group" {
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "to_type is required")
		return
	}
	err := h.LarkService.SendLarkReplayLinkMessage(ctx, req.Username, req.ReplayLink, req.TaskName, req.ToType)
	if err != nil {
		log.V1.CtxError(ctx, "failed to send lark replay link message. err: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), err.Error())
		return
	}
	c.JSON(http.StatusOK, nextagent.SendLarkReplayLinkMessageResponse{})
}

func (h *Handler) GetUserLarkURL(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.GetUserLarkURLRequest](ctx, c)
	if req == nil {
		logs.CtxInfo(ctx, "[GetUserLarkURL] request is nil")
		return
	}
	account, _ := h.AuthM.GetAccount(ctx, c)
	if account == nil {
		logs.CtxInfo(ctx, "[GetUserLarkURL] account is nil")
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "account is required")
		return
	}

	sessionID := req.GetSessionID()
	session, err := h.SessionService.GetSession(ctx, sessionservice.GetSessionOption{
		SessionID: sessionID,
		Sync:      false,
	})
	if err != nil {
		logs.CtxWarn(ctx, "[GetUserLarkURL] failed to get session: %v, session_id: %s", err, sessionID)
		if errors.Is(err, serverservice.ErrSessionNotFound) {
			hertz.JSONMessage(c, http.StatusNotFound, int(common.ErrorCode_ErrRecordNotFound), err.Error())
			return
		}
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), err.Error())
		return
	}

	if session.Creator != account.Username {
		logs.CtxWarn(ctx, "[GetUserLarkURL] session.creator is not equal to username, cannot transfer lark owner")
		// hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "cannot get lark url")
		c.JSON(http.StatusOK, nextagent.GetUserLarkURLResponse{
			LarkURL: req.LarkURL,
		})
		return
	}

	larkURL, err := h.LarkService.GetUserLarkURL(ctx, req.LarkURL, account.Username, account.Email)
	if err != nil {
		// 这里记录错误，但是不返回错误，把url返回给用户
		logs.CtxError(ctx, "[GetUserLarkURL] failed to get user lark url err: %v", err)
		// hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), err.Error())
		// return
	}

	// 如果larkURL为空，使用请求中的larkURL，否则使用转移所有者后得到的larkURL（目前2个链接相同）
	larkURL = lo.Ternary(larkURL == "", req.LarkURL, larkURL)

	c.JSON(http.StatusOK, nextagent.GetUserLarkURLResponse{
		LarkURL: larkURL,
	})
}

func (h *Handler) GetLarkDocxBlocks(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.GetLarkDocxBlocksRequest](ctx, c)
	if req == nil {
		logs.CtxInfo(ctx, "[GetLarkDocxBlocks] request is nil")
		return
	}
	account, _ := h.AuthM.GetAccount(ctx, c)
	if account == nil {
		logs.CtxInfo(ctx, "[GetLarkDocxBlocks] account is nil")
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "account is required")
		return
	}

	if account.Username != "testai" {
		logs.CtxInfo(ctx, "[GetLarkDocxBlocks] account has no permission")
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "account has no permission")
		return
	}

	blocks, err := h.LarkService.GetLarkDocxBlocks(ctx, req.DocumentID)
	if err != nil {
		logs.CtxError(ctx, "[GetLarkDocxBlocks] failed to get user lark url err: %v", err)
		hertz.JSONMessage(c, http.StatusNotFound, int(common.ErrorCode_ErrRecordNotFound), err.Error())
		return
	}

	c.JSON(http.StatusOK, blocks)
}
