package serverhandler

import (
	"context"
	"net/http"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/common"
	"code.byted.org/devgpt/kiwis/lib/hertz"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/middleware/hertz/pkg/app"
	"github.com/pkg/errors"
	"github.com/samber/lo"

	mentionservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/mention"
)

func (h *Handler) SearchMentions(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.SearchMentionsRequest](ctx, c)
	if req == nil {
		return
	}
	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist || user == nil {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not auth")
		return
	}

	result, err := h.MentionService.SearchMentions(ctx, mentionservice.SearchMentionsOptions{
		User: *user,
		Type: lo.TernaryF(req.Type != nil,
			func() entity.MentionType {
				return entity.MentionType(*req.Type)
			},
			func() entity.MentionType {
				return entity.MentionTypeUnknown
			}),
		Query:            req.Query,
		SpaceID:          req.SpaceID,
		NextID:           req.NextID,
		RepositoryID:     req.RepositoryID,
		RepositoryBranch: req.RepositoryBranch,
		Limit:            req.Limit,
	})
	if err != nil {
		if errors.Is(err, mentionservice.ErrMustHaveRepositoryID) {
			hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "must have repository id")
			return
		}
		log.V1.CtxError(ctx, "failed to search mentions: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "search mentions error")
		return
	}

	resp := &nextagent.SearchMentionsResponse{
		Type:    nextagent.MentionType(result.Type),
		NextID:  result.NextID,
		HasMore: result.HasMore,
	}

	switch resp.Type {
	case nextagent.MentionTypeRepository:
		resp.Repositories = lo.Map(result.Repositories, func(item *entity.Repository, index int) *nextagent.Repository {
			return item.ToIDL()
		})
	case nextagent.MentionTypeRepositoryBranch:
		resp.Branches = lo.Map(result.Branches, func(item *entity.Branch, index int) *nextagent.Branch {
			return item.ToIDL()
		})
	case nextagent.MentionTypeRepositoryFile:
		resp.Files = lo.Map(result.Files, func(item *entity.File, index int) *nextagent.File {
			return item.ToIDL()
		})
	case nextagent.MentionTypeRepositoryDir:
		resp.Dirs = lo.Map(result.Dirs, func(item *entity.Dir, index int) *nextagent.Dir {
			return item.ToIDL()
		})
	case nextagent.MentionTypeKnowledgeBase:
		resp.KnowledgeBaseDocuments = lo.Map(result.KnowledgeBaseDocuments, func(item *entity.Document, index int) *nextagent.KnowledgeBaseDocument {
			return getDocumentFromEntity(item)
		})
	}
	c.JSON(http.StatusOK, resp)
}
