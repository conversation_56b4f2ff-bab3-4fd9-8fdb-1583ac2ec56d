package serverhandler

import (
	"context"
	"net/http"

	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/middleware/hertz/pkg/app"
	"github.com/pkg/errors"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/pack"
	serverservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service"
	mcpservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/mcp"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/common"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	"code.byted.org/devgpt/kiwis/lib/hertz"
	"code.byted.org/devgpt/kiwis/lib/metrics"
)

// CreateTemplateStar 创建模板收藏
func (h *Handler) CreateTemplateStar(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.CreateTemplateStarRequest](ctx, c)
	if req == nil {
		return
	}
	log.V1.CtxInfo(ctx, "create template star request: %+v", req)

	reportMetricsFunc := metrics.NSM.ReportModuleMetrics(metrics.NextServerModuleTemplate, "CreateTemplateStar")
	var err error
	defer func() {
		reportMetricsFunc(err != nil, "")
	}()

	// 获取用户信息
	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}

	// 创建模板收藏
	_, err = h.TemplateService.CreateTemplateStar(ctx, req.TemplateID, user.Username, req.GetSpaceID())
	if err != nil {
		log.V1.CtxError(ctx, "failed to create template star: %v", err)
		if errors.Is(err, serverservice.ErrTemplateNotFound) {
			hertz.JSONMessage(c, http.StatusNotFound, int(common.ErrorCode_ErrRecordNotFound), "template not found")
			return
		}
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to create template star")
		return
	}

	c.JSON(http.StatusOK, nextagent.CreateTemplateStarResponse{})
}

// DeleteTemplateStar 删除模板收藏
func (h *Handler) DeleteTemplateStar(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.DeleteTemplateStarRequest](ctx, c)
	if req == nil {
		return
	}
	log.V1.CtxInfo(ctx, "delete template star request: %+v", req)

	reportMetricsFunc := metrics.NSM.ReportModuleMetrics(metrics.NextServerModuleTemplate, "DeleteTemplateStar")
	var err error
	defer func() {
		reportMetricsFunc(err != nil, "")
	}()

	// 获取用户信息
	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}

	// 删除模板收藏
	err = h.TemplateService.DeleteTemplateStar(ctx, req.TemplateID, user.Username, req.GetSpaceID())
	if err != nil {
		log.V1.CtxError(ctx, "failed to delete template star: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to delete template star")
		return
	}

	c.JSON(http.StatusOK, nextagent.DeleteTemplateStarResponse{})
}

// CreateShareTemplate 创建模板分享
func (h *Handler) CreateShareTemplate(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.CreateShareTemplateRequest](ctx, c)
	if req == nil {
		return
	}
	log.V1.CtxInfo(ctx, "create share template request: %+v", req)

	reportMetricsFunc := metrics.NSM.ReportModuleMetrics(metrics.NextServerModuleTemplate, "CreateShareTemplate")
	var err error
	defer func() {
		reportMetricsFunc(err != nil, "")
	}()

	// 获取用户信息
	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}

	// 创建模板分享
	share, err := h.TemplateService.CreateShareTemplate(ctx, req.TemplateID, user.Username)
	if err != nil {
		log.V1.CtxError(ctx, "failed to create share template: %v", err)
		if errors.Is(err, serverservice.ErrTemplateNotFound) {
			hertz.JSONMessage(c, http.StatusNotFound, int(common.ErrorCode_ErrRecordNotFound), "template not found")
			return
		}
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to create share template")
		return
	}

	c.JSON(http.StatusOK, nextagent.CreateShareTemplateResponse{
		ShareID: share.ID,
	})
}

// CreateUserShareTemplate 将模板分享给指定用户
func (h *Handler) CreateUserShareTemplate(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.CreateUserShareTemplateRequest](ctx, c)
	if req == nil {
		return
	}
	log.V1.CtxInfo(ctx, "create user share template request: %+v", req)

	reportMetricsFunc := metrics.NSM.ReportModuleMetrics(metrics.NextServerModuleTemplate, "CreateUserShareTemplate")
	var err error
	defer func() {
		reportMetricsFunc(err != nil, "")
	}()

	// 获取用户信息
	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}

	share, err := h.TemplateService.GetTemplateShareByID(ctx, req.ShareID)
	if err != nil {
		if errors.Is(err, serverservice.ErrTemplateShareNotFound) {
			hertz.JSONMessage(c, http.StatusNotFound, int(common.ErrorCode_ErrRecordNotFound), "template share not found")
			return
		}
		log.V1.CtxError(ctx, "failed to get template share: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get template share")
		return
	}
	// 获取模板信息
	template, err := h.TemplateService.GetTemplate(ctx, share.TemplateID)
	if err != nil {
		if errors.Is(err, serverservice.ErrTemplateNotFound) {
			hertz.JSONMessage(c, http.StatusNotFound, int(common.ErrorCode_ErrRecordNotFound), "template not found")
			return
		}
		log.V1.CtxError(ctx, "failed to get template: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get template")
		return
	}
	star, err := h.TemplateService.IsTemplateStarred(ctx, template.TemplateID, user.Username, "")
	if err != nil {
		log.V1.CtxError(ctx, "failed to get is template started: %v", err)
	} else {
		template.Starred = &star
	}
	mcps, err := h.MCPService.GetMCPsByKeys(ctx, &mcpservice.GetMCPsByKeysOption{MCPKeys: template.SupportMCPs})
	if err != nil {
		log.V1.CtxError(ctx, "failed to get MCPs: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get MCPs")
		return
	}
	template.MCPs = mcps

	// 如果是私有模板且不是自己的分享链接，则创建模板分享与目标用户关联
	if share.Username != user.Username && template.Scope == entity.TemplateScopePrivate {
		// 创建模板分享用户
		_, err = h.TemplateService.CreateTemplateShareTarget(ctx, share, user.Username)
		if err != nil {
			if errors.Is(err, serverservice.ErrTemplateNotFound) {
				hertz.JSONMessage(c, http.StatusNotFound, int(common.ErrorCode_ErrRecordNotFound), "template not found")
				return
			}
			log.V1.CtxError(ctx, "failed to create user share template: %v", err)
			hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to create user share template")
			return
		}
		template.ShareID = &share.ID
		template.Scope = entity.TemplateScopeShared
	}
	// TODO(dingbo): 从权限服务获取模板权限

	c.JSON(http.StatusOK, nextagent.CreateUserShareTemplateResponse{
		Template:    pack.ConvertTemplateEntityToDTO(template, true),
		Permissions: pack.GetTemplatePermissionsDTO(user.Username, template),
	})
}

// DeleteUserShareTemplate 删除模板分享用户
func (h *Handler) DeleteUserShareTemplate(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.DeleteUserShareTemplateRequest](ctx, c)
	if req == nil {
		return
	}
	log.V1.CtxInfo(ctx, "delete user share template request: %+v", req)

	reportMetricsFunc := metrics.NSM.ReportModuleMetrics(metrics.NextServerModuleTemplate, "DeleteUserShareTemplate")
	var err error
	defer func() {
		reportMetricsFunc(err != nil, "")
	}()

	// 获取用户信息
	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}

	// 删除模板分享用户
	err = h.TemplateService.DeleteTemplateShareTarget(ctx, req.ShareID, user.Username)
	if err != nil {
		log.V1.CtxError(ctx, "failed to delete user share template: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to delete user share template")
		return
	}
	share, err := h.TemplateService.GetTemplateShareByID(ctx, req.ShareID)
	if err != nil {
		log.V1.CtxError(ctx, "failed to get template share: %v", err)
	}
	// 如果收藏了分享的模板，则取消收藏
	if share != nil {
		err = h.TemplateService.DeleteTemplateStar(ctx, share.TemplateID, user.Username, "")
		if err != nil {
			log.V1.CtxError(ctx, "failed to delete user share template: %v", err)
		}
	}
	c.JSON(http.StatusOK, nextagent.DeleteUserShareTemplateResponse{})
}

func (h *Handler) SaveTemplateShareFormData(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.SaveTemplateShareFormDataRequest](ctx, c)
	if req == nil {
		return
	}
	log.V1.CtxInfo(ctx, "save template share form data request: %+v", req)
	reportMetricsFunc := metrics.NSM.ReportModuleMetrics(metrics.NextServerModuleTemplate, "SaveTemplateShareFormData")
	var err error
	defer func() {
		reportMetricsFunc(err != nil, "")
	}()
	// 保存模板分享表单数据
	id, err := h.TemplateService.SaveTemplateShareFormData(ctx, pack.ConvertTemplateFormValueDetailToEntity(req.FormData))
	if err != nil {
		log.V1.CtxError(ctx, "failed to save template share form data: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to save template share form data")
		return
	}
	c.JSON(http.StatusOK, nextagent.SaveTemplateShareFormDataResponse{ID: id})
}

func (h *Handler) GetTemplateShareFormData(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.GetTemplateShareFormDataRequest](ctx, c)
	if req == nil {
		return
	}
	log.V1.CtxInfo(ctx, "get template share form data request: %+v", req)
	reportMetricsFunc := metrics.NSM.ReportModuleMetrics(metrics.NextServerModuleTemplate, "GetTemplateShareFormData")
	var err error
	defer func() {
		reportMetricsFunc(err != nil, "")
	}()
	// 获取模板分享表单数据
	formData, err := h.TemplateService.GetTemplateShareFormData(ctx, req.GetID())
	if err != nil {
		log.V1.CtxError(ctx, "failed to get template share form data: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get template share form data")
		return
	}
	c.JSON(http.StatusOK, nextagent.GetTemplateShareFormDataResponse{
		FormData: pack.ConvertTemplateFormValueDetailToDTO(*formData),
	})
}
