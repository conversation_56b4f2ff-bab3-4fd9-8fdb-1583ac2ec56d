package serverhandler

import (
	"context"
	"net/http"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	permissionservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/permission"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/common"
	"code.byted.org/devgpt/kiwis/lib/hertz"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/middleware/hertz/pkg/app"
	"github.com/pkg/errors"
)

func (h *Handler) GetUserResourcePermissions(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.GetUserResourcePermissionRequest](ctx, c)
	if req == nil {
		return
	}

	user, _ := h.AuthM.GetAccount(ctx, c)
	if user == nil {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "no auth")
		return
	}

	var resourceType *entity.ResourceType
	if req.Type != nil {
		resourceType = entity.ParseResourceTypeIDLToEntity(*req.Type).Ptr()
	}
	resource, err := h.PermissionService.GetUserResourcePermission(ctx, permissionservice.GetUserResourcePermissionOption{
		Account:            *user,
		ResourceID:         req.ResourceID,
		ResourceExternalID: req.ExternalID,
		ResourceType:       resourceType,
	})
	if err != nil {
		if errors.Is(err, permissionservice.ErrResourceNotFound) {
			hertz.JSONMessage(c, http.StatusNotFound, int(common.ErrorCode_ErrRecordNotFound), "resource not found")
			return
		}
		log.V1.CtxError(ctx, "get user resource permission err: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "internal server error")
		return
	}
	c.JSON(http.StatusOK, nextagent.GetUserResourcePermissionResponse{
		Resource: resource.ToIDL(),
	})
}
