package serverhandler

import (
	"context"
	"fmt"
	"net/http"
	"strings"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	sessionservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/session"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/service/template"
	"code.byted.org/devgpt/kiwis/lib/hertz"
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/middleware/hertz/pkg/app"
	"code.byted.org/middleware/hertz/pkg/common/utils"
)

type OpsListTemplatesRequest struct {
	TemplateID   *string `query:"template_id"`
	Creator      *string `query:"creator"`
	Name         *string `query:"name"`
	ShareID      *string `query:"share_id"`
	SessionID    *string `query:"session_id"`
	RunSessionID *string `query:"run_session_id"`

	Page     int `query:"page"`
	PageSize int `query:"page_size"`
}

func (h *Handler) OpsListTemplates(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[OpsListTemplatesRequest](ctx, c)
	if req == nil {
		return
	}
	var (
		templates []*entity.TemplateVersion
		err       error
	)
	switch {
	case req.RunSessionID != nil:
		session, err := h.SessionService.GetSession(ctx, sessionservice.GetSessionOption{
			SessionID: *req.RunSessionID,
			Sync:      false,
		})
		if err != nil {
			break
		}
		req.TemplateID = &session.TemplateID
		fallthrough
	case req.TemplateID != nil:
		var template *entity.TemplateVersion
		template, err = h.TemplateService.GetTemplate(ctx, *req.TemplateID)
		templates = append(templates, template)
	case req.ShareID != nil:
		var (
			share    *entity.TemplateShare
			template *entity.TemplateVersion
		)
		share, err = h.TemplateService.GetTemplateShareByID(ctx, *req.ShareID)
		if err == nil {
			template, err = h.TemplateService.GetTemplate(ctx, share.TemplateID)
			templates = append(templates, template)
		}
	default:
		templates, err = h.TemplateService.OpsListTemplates(ctx, template.OpsListTemplatesOption{
			PageNum:    req.Page,
			PageSize:   req.PageSize,
			SearchName: req.Name,
			Creator:    req.Creator,
			SessionID:  req.SessionID,
		})
	}
	if err != nil {
		hertz.JSONMessage(c, http.StatusBadRequest, -1, err.Error())
		return
	}
	c.JSON(http.StatusOK, utils.H{
		"templates": templates,
	})
}

type OpsEditTemplateRequest struct {
	TemplateID                string  `path:"template_id"`
	Name                      *string `json:"name"`
	ExpProgressPlan           *string `json:"progress_plan"`
	ExpSOP                    *string `json:"exp_sop"`
	Expired                   *bool   `json:"expired"`
	Edited                    *bool   `json:"edited"`
	QueryTemplate             *string `json:"query_template"`
	QueryTemplatePlaceholders *string `json:"query_template_placeholders"`
	Scope                     *string `json:"scope"`
	SupportMCPs               *string `json:"support_mcps"`
}

func (h *Handler) OpsEditTemplate(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[OpsEditTemplateRequest](ctx, c)
	if req == nil {
		return
	}
	tmpl, err := h.TemplateService.GetTemplate(ctx, req.TemplateID)
	if err != nil {
		hertz.JSONMessage(c, http.StatusBadRequest, -1, err.Error())
		return
	}
	tmpl, err = h.TemplateService.UpdateTemplate(ctx, template.UpdateTemplateOptions{
		TemplateID: req.TemplateID,
		OpsModifyTemplate: &entity.OpsModifyTemplate{
			Name:            req.Name,
			ExpProgressPlan: req.ExpProgressPlan,
			ExpSOP:          req.ExpSOP,
			Expired:         req.Expired,
			Edited:          req.Edited,
			QueryTemplate:   req.QueryTemplate,
			Scope:           req.Scope,
			SupportMCPs:     req.SupportMCPs,
		},
	})
	if err != nil {
		hertz.JSONMessage(c, http.StatusBadRequest, -1, err.Error())
		return
	}
	c.JSON(http.StatusOK, utils.H{
		"template": tmpl,
	})
}

type OpsBatchFixTemplatesRequest struct {
	TemplateIDs []string `json:"template_ids"`
	// FixType     string   `json:"fix_type"`
}

func (h *Handler) OpsBatchFixTemplates(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[OpsBatchFixTemplatesRequest](ctx, c)
	if req == nil {
		return
	}
	msgs := strings.Builder{}
	for _, id := range req.TemplateIDs {
		tmpl, err := h.TemplateService.GetTemplate(ctx, id)
		if err != nil {
			logs.V1.CtxWarn(ctx, "OpsBatchUpdateTemplates GetTemplate failed, err: %v", err)
			msgs.WriteString("模版" + id + "获取失败: " + err.Error() + "\n")
			continue
		}
		if tmpl.ExpSop == nil {
			msgs.WriteString(fmt.Sprintf("模版 %s 没有经验SOP\n", id))
			continue
		}
		updated := false
		for _, step := range tmpl.ExpSop.PlanSteps {
			for idx, tool := range step.Toolsets {
				if strings.HasPrefix(tool, "SlardarApp_") {
					step.Toolsets[idx] = "SlardarApp"
					msgs.WriteString(fmt.Sprintf("模版%s第%d步工具集%s修复为SlardarApp\n", id, idx, tool))
					updated = true
				}
			}
		}
		if !updated {
			msgs.WriteString(fmt.Sprintf("模版 %s 没有需要修复的工具集\n", id))
			continue
		}
		err = h.TemplateService.UpdateTemplateExperienceSOP(ctx, template.UpdateTemplateExperienceOption{
			TemplateID: id,
			ExpSOP:     tmpl.ExpSop,
		})
		if err != nil {
			logs.V1.CtxWarn(ctx, "OpsBatchUpdateTemplates UpdateTemplateExperienceSOP failed, err: %v", err)
			msgs.WriteString(fmt.Sprintf("模版 %s 更新失败: %s\n", id, err.Error()))
		} else {
			msgs.WriteString(fmt.Sprintf("模版 %s 更新成功\n", id))
		}
	}
	c.JSON(http.StatusOK, utils.H{
		"msg": msgs.String(),
	})
}
