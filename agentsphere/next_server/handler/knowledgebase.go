package serverhandler

import (
	"context"
	"net/http"

	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/middleware/hertz/pkg/app"
	"github.com/pkg/errors"
	"github.com/samber/lo"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	knowledgebaseservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/knowledgebase"
	permissionservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/permission"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/common"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	devaiutil "code.byted.org/devgpt/kiwis/devai/common/util"
	"code.byted.org/devgpt/kiwis/lib/hertz"
	"code.byted.org/devgpt/kiwis/lib/page"
	"code.byted.org/devgpt/kiwis/port/lark"
)

func (h *Handler) UploadDocuments(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.UploadDocumentsRequest](ctx, c)
	if req == nil {
		return
	}
	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}
	// 权限检查
	result, err := h.PermissionService.CheckPermission(ctx, permissionservice.CheckPermissionOption{
		Account:            lo.FromPtr(user),
		ResourceExternalID: &req.DatasetID,
		ResourceType:       entity.ResourceTypeKnowledgebase.Ptr(),
		Action:             entity.PermissionActionKnowledgebaseUpdate,
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to check permission: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to check permission")
		return
	}
	if !result.Allowed {
		hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrNoAuth), "permission denied")
		return
	}
	opts := make([]knowledgebaseservice.UploadDocumentOption, 0, len(req.GetDocumentURLs()))
	for _, link := range req.GetDocumentURLs() {
		docType, sourceKey := devaiutil.ParseLarkDocURL(ctx, link)
		if docType == "" || sourceKey == "" {
			logs.V1.CtxError(ctx, "[UploadDocuments] failed to parse url, link: %s", link)
			hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "invalid url")
			return
		}
		documentSourceType, isSupported := entity.GetDocumentSourceType(docType)
		if !isSupported {
			logs.V1.CtxError(ctx, "[UploadDocuments] failed to parse url, link: %s", link)
			hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrDocumentTypeNotSupport), "invalid url")
			return
		}
		opts = append(opts, knowledgebaseservice.UploadDocumentOption{
			SourceType: documentSourceType,
			SourceUid:  sourceKey,
		})
	}
	docs, err := h.KnowledgebaseService.UploadDocuments(ctx, req.GetDatasetID(), user.Name, opts)
	if err != nil {
		logs.V1.CtxError(ctx, "[UploadDocuments] failed to upload documents, err: %v", err)
		if errors.Is(err, lark.ErrLarkAuthFailed) || errors.Is(err, lark.ErrPermissionDenied) {
			hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "lark auth failed")
			return
		}
		if errors.Is(err, lark.ErrResourceNotFound) {
			hertz.JSONMessage(c, http.StatusNotFound, int(common.ErrorCode_ErrDocumentNotExist), "document not found")
			return
		}
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to upload documents")
		return
	}
	resp := &nextagent.UploadDocumentsResponse{
		Documents: lo.Map(docs, func(doc *entity.Document, _ int) *nextagent.KnowledgeBaseDocument {
			return getDocumentFromEntity(doc)
		}),
	}
	c.JSON(http.StatusOK, resp)
}

func (h *Handler) DeleteDocument(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.DeleteDocumentRequest](ctx, c)
	if req == nil {
		return
	}
	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}
	// 权限检查
	result, err := h.PermissionService.CheckPermission(ctx, permissionservice.CheckPermissionOption{
		Account:            lo.FromPtr(user),
		ResourceExternalID: &req.DatasetID,
		ResourceType:       entity.ResourceTypeKnowledgebase.Ptr(),
		Action:             entity.PermissionActionKnowledgebaseDelete,
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to check permission: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to check permission")
		return
	}
	if !result.Allowed {
		hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrNoAuth), "permission denied")
		return
	}
	err = h.KnowledgebaseService.DeleteDocument(ctx, req.GetDatasetID(), req.GetDocumentID())
	if err != nil {
		logs.V1.CtxError(ctx, "[DeleteDocument] failed to delete document, err: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to delete document")
		return
	}
	c.JSON(http.StatusOK, &nextagent.DeleteDocumentResponse{})
}

func (h *Handler) UpdateDocument(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.UpdateDocumentRequest](ctx, c)
	if req == nil {
		return
	}
	account, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}
	// 权限检查
	result, err := h.PermissionService.CheckPermission(ctx, permissionservice.CheckPermissionOption{
		Account:            lo.FromPtr(account),
		ResourceExternalID: &req.DatasetID,
		ResourceType:       entity.ResourceTypeKnowledgebase.Ptr(),
		Action:             entity.PermissionActionKnowledgebaseUpdate,
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to check permission: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to check permission")
		return
	}
	if !result.Allowed {
		hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrNoAuth), "permission denied")
		return
	}
	err = h.KnowledgebaseService.UpdateDocument(ctx, req.GetDatasetID(), req.GetDocumentID(), account.Username)
	if err != nil {
		logs.V1.CtxError(ctx, "[UpdateDocument] failed to update document, err: %v", err)
		if errors.Is(err, lark.ErrLarkAuthFailed) || errors.Is(err, lark.ErrPermissionDenied) {
			hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "lark auth failed")
			return
		}
		if errors.Is(err, lark.ErrResourceNotFound) {
			hertz.JSONMessage(c, http.StatusNotFound, int(common.ErrorCode_ErrDocumentNotExist), "document not found")
			return
		}
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to update document")
		return
	}
	c.JSON(http.StatusOK, &nextagent.UpdateDocumentResponse{})
}

func (h *Handler) GetDocument(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.GetDocumentRequest](ctx, c)
	if req == nil {
		return
	}
	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}
	// 权限检查
	result, err := h.PermissionService.CheckPermission(ctx, permissionservice.CheckPermissionOption{
		Account:            lo.FromPtr(user),
		ResourceExternalID: &req.DatasetID,
		ResourceType:       entity.ResourceTypeKnowledgebase.Ptr(),
		Action:             entity.PermissionActionKnowledgebaseRead,
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to check permission: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to check permission")
		return
	}
	if !result.Allowed {
		hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrNoAuth), "permission denied")
		return
	}
	doc, err := h.KnowledgebaseService.GetDocument(ctx, req.GetDatasetID(), req.GetDocumentID())
	if err != nil {
		logs.V1.CtxError(ctx, "[GetDocument] failed to get document, err: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get document")
		return
	}
	c.JSON(http.StatusOK, &nextagent.GetDocumentResponse{
		Document: getDocumentFromEntity(doc),
	})
}

func getDocumentFromEntity(doc *entity.Document) *nextagent.KnowledgeBaseDocument {
	ret := &nextagent.KnowledgeBaseDocument{
		ID:        doc.ID,
		Title:     doc.Title,
		Creator:   doc.Creator,
		URL:       doc.GetDocumentURL(),
		UpdatedAt: doc.UpdatedAt.Format("2006-01-02 15:04:05"),
		Owner:     doc.Owner,
		CreatedAt: doc.CreatedAt.Format("2006-01-02 15:04:05"),
		DatasetID: doc.DatasetID,
	}
	ret.ContentType = doc.ContentType.ToIDL()
	if doc.DocumentContent != nil {
		ret.Content = doc.DocumentContent
	}
	if doc.Heat != nil {
		ret.Heat = doc.Heat
	}
	return ret
}

func (h *Handler) SearchLarkDocuments(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.SearchLarkDocumentsRequest](ctx, c)
	if req == nil {
		return
	}
	account, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}
	searchPassages, err := h.KnowledgebaseService.SearchDocsFromLark(ctx, account.Username, req.Query, req.DatasetID)
	if err != nil {
		logs.V1.CtxError(ctx, "[SearchLarkDocuments] failed to search lark document, err: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to search lark document")
		return
	}

	c.JSON(http.StatusOK, &nextagent.SearchLarkDocumentsResponse{
		Documents: lo.Map(searchPassages, func(searchPassage *entity.LarkDocument, _ int) *nextagent.LarkDocument {
			return getLarkDocumentFromEntity(searchPassage)
		}),
	})
}

func (h *Handler) RecallDataset(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.RecallDatasetRequest](ctx, c)
	if req == nil {
		return
	}
	logs.V1.CtxInfo(ctx, "recall dataset: %v", req)
	recallSegments, err := h.KnowledgebaseService.RecallDataset(ctx, &knowledgebaseservice.RecallDatasetOption{
		DatasetID: req.GetDatasetID(),
		Query:     req.GetQuery(),
		TopK:      int(req.GetTopK()),
	})
	if err != nil {
		logs.V1.CtxError(ctx, "[RecallDataset] failed to recall, err: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to recall")
		return
	}
	c.JSON(http.StatusOK, &nextagent.RecallDatasetResponse{
		Segments: lo.Map(recallSegments, func(segment *entity.Segment, _ int) *nextagent.RecallSegment {
			return &nextagent.RecallSegment{
				DatasetID:  segment.DatasetID,
				DocumentID: segment.DocumentID,
				Content:    segment.Content.Text,
				Title:      segment.Document.Title,
				URL:        segment.Document.GetDocumentURL(),
			}
		}),
	})
}

func (h *Handler) RecommendDocuments(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.RecommendDocumentsRequest](ctx, c)
	if req == nil {
		return
	}
	account, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}
	referenceDocuments := lo.Map(req.GetReferenceDocuments(), func(refDoc *nextagent.ReferenceDocument, _ int) *knowledgebaseservice.ReferenceDocument {
		return &knowledgebaseservice.ReferenceDocument{
			Title: refDoc.Title,
			URL:   refDoc.URL,
		}
	})
	searchPassages, err := h.KnowledgebaseService.RecommendDocuments(ctx, account.Username, req.DatasetID, referenceDocuments)
	if err != nil {
		logs.V1.CtxError(ctx, "[RecommendDocuments] failed to recommend documents, err: %v", err)
		if errors.Is(err, lark.ErrLarkAuthFailed) || errors.Is(err, lark.ErrPermissionDenied) {
			hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "lark auth failed")
			return
		}
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to recommend documents")
		return
	}
	c.JSON(http.StatusOK, &nextagent.RecommendDocumentsResponse{
		Documents: lo.Map(searchPassages, func(searchPassage *entity.LarkDocument, _ int) *nextagent.LarkDocument {
			return getLarkDocumentFromEntity(searchPassage)
		}),
	})
}

func getLarkDocumentFromEntity(doc *entity.LarkDocument) *nextagent.LarkDocument {
	return &nextagent.LarkDocument{
		Title:       doc.Title,
		Content:     doc.Content,
		URL:         doc.URL,
		IsUploaded:  doc.IsUploaded,
		ContentType: doc.ContentType,
		OwnerName:   doc.OwnerName,
	}
}

func (h *Handler) ListDocuments(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.ListDocumentsRequest](ctx, c)
	if req == nil {
		return
	}
	offset, limit := page.GetOffsetAndLimitFromPage(int(req.PageNum), int(req.PageSize))
	docs, totalCount, err := h.KnowledgebaseService.ListDocuments(ctx, req.GetDatasetID(), &knowledgebaseservice.ListDocumentsOption{
		Query:       req.Query,
		Creators:    req.Creators,
		DescOrderBy: req.DescOrderBy,
		Offset:      offset,
		Limit:       limit,
	})
	if err != nil {
		logs.V1.CtxError(ctx, "[ListDocuments] failed to list documents, err: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to list documents")
		return
	}
	c.JSON(http.StatusOK, &nextagent.ListDocumentsResponse{
		Documents: lo.Map(docs, func(doc *entity.Document, _ int) *nextagent.KnowledgeBaseDocument {
			return getDocumentFromEntity(doc)
		}),
		Total: totalCount,
	})
}

func (h *Handler) CountDocuments(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.CountDocumentsRequest](ctx, c)
	if req == nil {
		return
	}
	account, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}
	allTotal, myTotal, err := h.KnowledgebaseService.CountDocuments(ctx, req.GetDatasetID(), account.Username)
	if err != nil {
		logs.V1.CtxError(ctx, "[CountDocuments] failed to count documents, err: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to count documents")
		return
	}
	c.JSON(http.StatusOK, &nextagent.CountDocumentsResponse{
		AllTotal: int64(allTotal),
		MyTotal:  int64(myTotal),
	})
}
