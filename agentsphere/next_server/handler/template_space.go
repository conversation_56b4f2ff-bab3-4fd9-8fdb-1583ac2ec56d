package serverhandler

import (
	"context"
	"net/http"
	"strconv"

	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/middleware/hertz/pkg/app"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/pack"
	templateservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/template"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/common"
	"code.byted.org/devgpt/kiwis/lib/hertz"
	"code.byted.org/devgpt/kiwis/lib/metrics"
	"code.byted.org/devgpt/kiwis/lib/util"
)

func (h *Handler) ListSpaceTemplates(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.ListSpaceTemplatesRequest](ctx, c)
	if req == nil {
		return
	}
	reportMetricsFunc := metrics.NSM.ReportModuleMetrics(metrics.NextServerModuleTemplate, "ListSpaceTemplates")
	var (
		err       error
		templates []*entity.TemplateVersion
		nextID    int64
	)
	defer func() {
		reportMetricsFunc(err != nil, "")
	}()
	// 获取用户信息
	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}
	// 查询空间下的模板列表
	startID := util.FromString(req.GetNextID())
	templates, nextID, err = h.TemplateService.ListSpaceTemplates(ctx, templateservice.ListSpaceTemplatesOptions{
		SpaceID:  req.GetSpaceID(),
		Category: req.Category,
		Search:   req.Search,
		Source:   pack.ConvertTemplateSourceToEntity(req.Source),
		User:     user,
		Label:    req.Label,
		Limit:    req.GetLimit(),
		StartID:  &startID,
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to list space templates: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to list space templates")
		return
	}
	c.JSON(http.StatusOK, nextagent.ListSpaceTemplatesResponse{
		Templates: pack.ConvertTemplatesEntityToDTO(templates),
		HasMore:   nextID > 0,
		NextID:    strconv.FormatInt(nextID, 10),
	})
}

func (h *Handler) CountSpaceTemplates(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.CountSpaceTemplatesRequest](ctx, c)
	if req == nil {
		return
	}
	reportMetricsFunc := metrics.NSM.ReportModuleMetrics(metrics.NextServerModuleTemplate, "CountSpaceTemplates")
	var (
		myTotal, starTotal, allTotal int64
		err                          error
	)
	defer func() {
		reportMetricsFunc(err != nil, "")
	}()
	// 获取用户信息
	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}

	myTotal, err = h.TemplateService.CountSpaceTemplates(ctx, templateservice.CountSpaceTemplatesOptions{
		Source:  entity.TemplateSourceMy,
		User:    user,
		SpaceID: req.GetSpaceID(),
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to count templates: %v", err)
	}
	starTotal, err = h.TemplateService.CountSpaceTemplates(ctx, templateservice.CountSpaceTemplatesOptions{
		Source:  entity.TemplateSourceStar,
		User:    user,
		SpaceID: req.GetSpaceID(),
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to count templates: %v", err)
	}
	allTotal, err = h.TemplateService.CountSpaceTemplates(ctx, templateservice.CountSpaceTemplatesOptions{
		Source:  entity.TemplateSourceAll,
		User:    user,
		SpaceID: req.GetSpaceID(),
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to count templates: %v", err)
	}

	c.JSON(http.StatusOK, nextagent.CountSpaceTemplatesResponse{
		MyTotal:   myTotal,
		StarTotal: starTotal,
		AllTotal:  allTotal,
	})
}
