package serverhandler

import (
	"context"
	"errors"
	"fmt"
	"net/http"

	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/middleware/hertz/pkg/app"
	"github.com/AlekSi/pointer"
	"github.com/bytedance/sonic"
	"github.com/samber/lo"

	agententity "code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/pack"
	serverservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service"
	activityservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/activity"
	agentservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/agent"
	artifactservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/artifact"
	deploymentservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/deployment"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/service/knowledgebase"
	larkservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/lark"
	mcpservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/mcp"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/service/mention"
	monitorservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/monitor"
	permissionservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/permission"
	promptservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/prompt"
	replayservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/replay"
	sessionservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/session"
	sessioncollectionservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/session_collection"
	shareservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/share"
	spaceservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/space"
	templateservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/template"
	traceservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/trace"
	userservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/user"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/common"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	authentity "code.byted.org/devgpt/kiwis/copilotstack/common/auth/entity"
	"code.byted.org/devgpt/kiwis/copilotstack/common/auth/handler"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/hertz"
	"code.byted.org/devgpt/kiwis/lib/metrics"
	"code.byted.org/devgpt/kiwis/lib/tcc"
	"code.byted.org/devgpt/kiwis/lib/util"
	"code.byted.org/devgpt/kiwis/port/hulkcloud"
	"code.byted.org/devgpt/kiwis/port/lark"
	"code.byted.org/devgpt/kiwis/port/redis"
	"code.byted.org/devgpt/kiwis/port/rocketmq"
)

type Handler struct {
	AuthM                            *handler.AuthMiddleware
	LarkService                      *larkservice.Service
	ArtifactService                  *artifactservice.Service
	DeploymentService                *deploymentservice.Service
	ReplayService                    *replayservice.Service
	UserService                      *userservice.Service
	SessionCollectionService         *sessioncollectionservice.Service
	SessionService                   *sessionservice.Service
	ShareService                     *shareservice.Service
	TraceService                     *traceservice.Service
	AgentService                     *agentservice.Service
	PromptService                    *promptservice.Service
	ActivityService                  *activityservice.Service
	MCPService                       mcpservice.Service
	SpaceService                     *spaceservice.Service
	PermissionService                *permissionservice.Service
	LarkClient                       lark.Client
	HulkCloudClient                  hulkcloud.Client
	RedisClient                      redis.Client
	LarkConf                         *tcc.GenericConfig[config.LarkAppConfig] `name:"neuma"`
	ImagexConf                       *tcc.GenericConfig[config.ImageXTCCConfig]
	NextAgentSessionCollectionConfig *tcc.GenericConfig[config.NextAgentSessionCollectionConfig]
	NextAgentUserFeaturesConfig      *tcc.GenericConfig[config.NextAgentUserFeaturesConfig]
	NextAgentUploadLarkFileConfig    *tcc.GenericConfig[config.NextAgentUploadLarkFileConfig]
	SessionMonitorMQ                 rocketmq.Client `name:"next_session_monitor_mq"`
	KnowledgebaseMQ                  rocketmq.Client `name:"knowledgebase_mq"`
	MonitorService                   *monitorservice.Service
	NextAgentActivityConfig          *tcc.GenericConfig[config.NextAgentActivityConfig]
	NextAgentGrantAccessConfig       *tcc.GenericConfig[config.NextAgentGrantAccessConfig]
	TemplateService                  *templateservice.Service
	KnowledgebaseService             *knowledgebase.Service
	MentionService                   *mention.Service
}

const (
	internalCallKey = "internal_call"
)

func (h *Handler) ListShowcases(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.ListShowcaseRequest](ctx, c)
	if req == nil {
		return
	}

	var err error
	reportMetricsFunc := metrics.NSM.ReportModuleMetrics(metrics.NextServerModuleReplay, "ListShowcases")
	defer func() {
		reportMetricsFunc(err != nil, serverservice.ErrorToErrorReason(err))
	}()

	showcases, err := h.UserService.ListShowcases(ctx, userservice.ListShowcaseOptions{
		Locale: req.Locale,
	})

	if err != nil {
		if errors.Is(err, serverservice.ErrorShowcaseNotFound) {
			log.V1.CtxWarn(ctx, "showcases not found: %v", err)
			hertz.JSONMessage(c, http.StatusNotFound, int(common.ErrorCode_ErrRecordNotFound), "showcases not found")
			return
		}
		log.V1.CtxError(ctx, "failed to list showcases: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to list showcases")
		return
	}
	c.JSON(http.StatusOK, nextagent.ListShowcaseResponse{
		Showcases: showcases,
	})
}

func (h *Handler) GetUserRoles(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.GetUserRolesRequest](ctx, c)
	if req == nil {
		return
	}

	user, _ := h.AuthM.GetAccount(ctx, c)
	roles := h.UserService.GetUserRoles(user)

	c.JSON(http.StatusOK, nextagent.GetUserRolesResponse{
		Roles: roles,
	})
}

func (h *Handler) CreateMessage(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.CreateMessageRequest](ctx, c)
	if req == nil {
		return
	}
	log.V1.CtxInfo(ctx, "create message request: %+v", req)

	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}
	message := h.createMessage(ctx, c, user, createMessageOption{
		SessionID:   req.SessionID,
		Content:     req.Content,
		ToolCalls:   req.ToolCalls,
		Attachments: req.Attachments,
		Options:     req.Options,
		EventOffset: req.EventOffset,
		SpaceID:     req.GetSpaceID(),
		Mentions:    req.Mentions,
	})
	if message == nil {
		return
	}

	c.JSON(http.StatusOK, nextagent.CreateMessageResponse{
		Message: getMessagesFromEntity(message),
	})
}

type createMessageOption struct {
	SessionID         string
	Content           string
	ToolCalls         []*nextagent.ToolCall
	Attachments       []*nextagent.AttachmentRequired
	Options           *string
	EventOffset       int64
	TemplateID        string
	TemplateVariables map[string]*entity.TemplateVariableValue
	SpaceID           string
	Mentions          []*nextagent.Mention
}

func (h *Handler) createMessage(ctx context.Context, c *app.RequestContext, user *authentity.Account, opt createMessageOption) *entity.Message {
	messageOptions := entity.MessageOptions{}
	if opt.Options != nil {
		err := sonic.Unmarshal([]byte(*opt.Options), &messageOptions)
		if err != nil {
			log.V1.CtxError(ctx, "failed to unmarshal message options: %v", err)
			hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrInternal), "failed to unmarshal message options")
			return nil
		}
	}

	userFeature := h.UserService.GetUserFeatures(ctx, user)
	if !userFeature.Invited {
		hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "permission denied")
		return nil
	}
	session, err := h.SessionService.GetSession(ctx, sessionservice.GetSessionOption{
		SessionID: opt.SessionID,
		Sync:      true,
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to get session: %v", err)
		if errors.Is(err, serverservice.ErrSessionNotFound) {
			hertz.JSONMessage(c, http.StatusNotFound, int(common.ErrorCode_ErrRecordNotFound), "session not found")
			return nil
		}
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get session")
		return nil
	}

	if user.Username != session.Creator {
		hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "permission denied")
		return nil
	}

	// 休眠状态，且 Session 可以唤醒
	isResumeStatus := false
	if session.Status.IsStopped() {
		isStopped, err := h.SessionService.GetRuntimeStatus(ctx, session.ID)
		if err != nil {
			log.V1.CtxError(ctx, "failed to get runtime status: %v", err)
			hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to check cube status")
			return nil
		}
		if session.CanResume && isStopped {
			// 检查是否可以创建 session， 根据唤醒时间，当天的唤醒总 session 次数不超过 xx
			result, err := h.SessionService.CanCreateSession(ctx, sessionservice.CanCreateSessionOption{
				Account:         user,
				Roles:           []entity.SessionRole{pointer.Get(session.Role)},
				UseInternalTool: session.Context.UseInternalTool,
			})
			if err != nil {
				hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to check create session")
				return nil
			}
			r, ok := lo.Find(result.Results, func(item entity.CreateSessionAllowed) bool {
				return item.Role == pointer.Get(session.Role)
			})
			if !ok {
				hertz.JSONMessage(c, http.StatusNotFound, int(common.ErrorCode_ErrRecordNotFound), "session role not found")
				return nil
			}
			if !r.Allowed {
				hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrNextAgentReachedUpperLimit), "maximum sessions reached")
				return nil
			}
			isResumeStatus = true
		} else {
			// 无法唤醒，直接拒绝
			hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "this session is not allowed resume")
			return nil
		}

	}

	allowed, err := h.SessionService.CanCreateMessage(ctx, sessionservice.CanCreateMessageOption{
		Session: *session,
		Account: *user,
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to check create session: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to check create session")
		return nil
	}
	if !allowed {
		hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrNextAgentReachedUpperLimit), "maximum messages reached")
		return nil
	}

	mentions := h.convertMentionsToEntity(ctx, opt.Mentions)
	status := lo.Ternary(isResumeStatus, entity.MessageStatusWait, entity.MessageStatusSent)
	message, err := h.SessionService.CreateMessage(ctx, sessionservice.CreateMessageOption{
		SessionID: opt.SessionID,
		Role:      entity.MessageRoleUser,
		Content: entity.MessageContent{
			Content: opt.Content,
			ToolCalls: lo.Map(opt.ToolCalls, func(t *nextagent.ToolCall, _ int) *entity.ToolCall {
				return &entity.ToolCall{
					ID:      t.ToolCallID,
					Name:    t.Name,
					Content: t.Content,
				}
			}),
		},
		User: user,
		Attachments: lo.Map(opt.Attachments, func(a *nextagent.AttachmentRequired, _ int) *entity.Attachment {
			return &entity.Attachment{
				ID:       a.ID,
				FileName: a.FileName,
			}
		}),
		EventOffset:       opt.EventOffset,
		Options:           messageOptions,
		AgentVersion:      c.Request.Header.Get(debugAgentVersionHeaderKey),
		Status:            status,
		TemplateID:        opt.TemplateID,
		TemplateVariables: opt.TemplateVariables,
		SpaceID:           opt.SpaceID,
		Mentions:          mentions,
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to create message : %v", err)
		if errors.Is(err, serverservice.ErrSessionStopped) {
			hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrNextSessionStopped), "session is stopped")
			return nil
		} else if errors.Is(err, serverservice.ErrGlobalMaximumRunningSessionsReached) {
			hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrNextAgentReachedGlobalLimit), "global maximum sessions reached")
			return nil
		}
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to create message")
		return nil
	}

	return message
}

func sharePreviewCallbackResponseFromEntity(variables *shareservice.PreviewCardTemplateVariable) *nextagent.SharePreviewCallbackResponse {
	templateVariable := &nextagent.PreviewCardTemplateVariable{
		Title:   variables.Title,
		Content: variables.Content,
		URL:     variables.URL,
		Creator: fmt.Sprintf("<at id=\"%<EMAIL>\"></at>", variables.Creator),
	}

	return &nextagent.SharePreviewCallbackResponse{
		Inline: &nextagent.PreviewInline{
			I18nTitle: &nextagent.PreviewTitle{
				ZhCN: variables.URLTitle,
			},
		},
		Card: &nextagent.PreviewCard{
			Type: "template",
			Data: &nextagent.PreviewCardData{
				TemplateID:       variables.TemplateID,
				TemplateVariable: templateVariable,
			},
		},
	}
}

func (h *Handler) GetUserInfo(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.GetUserInfoRequest](ctx, c)
	if req == nil {
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "request is empty")
		return
	}
	if req.GetUserName() == "" {
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "username is nil")
		return
	}
	username := req.GetUserName()

	cloudUser, err := h.HulkCloudClient.GetUserInfo(ctx, username)
	if err != nil {
		log.V1.CtxError(ctx, "GetUserInfo err: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get user info")
		return
	}

	nextAgentUser := &nextagent.User{
		ID:         lo.ToPtr(cloudUser.ID),
		Name:       cloudUser.Name,
		Username:   cloudUser.Username,
		EnName:     lo.ToPtr(cloudUser.EnName),
		AvatarURL:  lo.ToPtr(cloudUser.AvatarURL),
		Terminated: lo.ToPtr(cloudUser.Terminated),
	}

	c.JSON(http.StatusOK,
		nextagent.GetUserInfoResponse{
			User: nextAgentUser,
		},
	)
}

func (h *Handler) SubmitToolCall(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.SubmitToolCallRequest](ctx, c)
	if req == nil {
		return
	}
	log.V1.CtxInfo(ctx, "submit tool call request: %+v", util.ToJson(req))
	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}

	// 校验权限
	_, ok := h.checkSessionPermission(ctx, c, SessionPermissionCheckOption{
		Account:   user,
		SessionID: lo.ToPtr(req.SessionID),
		Action:    entity.PermissionActionSessionUpdate,
	})
	if !ok {
		return
	}

	session, err := h.SessionService.GetSession(ctx, sessionservice.GetSessionOption{
		SessionID: req.SessionID,
		Sync:      false,
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to get session: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get session")
		return
	}
	if session.Creator != user.Username {
		hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "permission denied")
		return
	}
	toolCalls := pack.ConvertToolCallsToEntities(req.ToolCalls)
	for _, toolCall := range toolCalls {
		err = h.SessionService.SubmitToolCall(ctx, session, toolCall, user, nil, nil)
		if err != nil {
			log.V1.CtxError(ctx, "failed to submit tool call : %v", err)
			hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to submit tool call")
			return
		}
	}
	_, err = h.SessionService.UpdateSession(ctx, sessionservice.UpdateSessionOption{
		SessionID: session.ID,
		Status:    lo.ToPtr(entity.SessionStatusRunning),
	})
	if err != nil {
		logs.V1.CtxError(ctx, "failed to update session status: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to update session status")
		return
	}

	c.JSON(http.StatusOK, nextagent.SubmitToolCallResponse{})
}

func (h *Handler) convertMentionsToEntity(ctx context.Context, mentions []*nextagent.Mention) []*agententity.Mention {
	if len(mentions) == 0 {
		return nil
	}
	var entities = make([]*agententity.Mention, 0, len(mentions))
	for _, mention := range mentions {
		if mention == nil {
			continue
		}
		var (
			knowledge *entity.Document
			err       error
		)
		if mention.KnowledgeMention != nil {
			knowledge, err = h.KnowledgebaseService.GetDocumentMetadata(ctx, mention.KnowledgeMention.KnowledgeBaseID, mention.KnowledgeMention.DocumentID)
			if err != nil {
				log.V1.CtxError(ctx, "failed to get document: %v", err)
				continue
			}
		}
		entities = append(entities, &agententity.Mention{
			ID:                mention.ID,
			Type:              pack.ConvertMentionTypeToEntity(mention.Type),
			CodebaseMention:   pack.ConvertCodebaseMentionToEntity(mention.CodebaseMention),
			LarkDocMention:    pack.ConvertKnowledgeMentionToEntity(mention.KnowledgeMention, knowledge),
			AttachmentMention: pack.ConvertAttachmentMentionToEntity(mention.AttachmentMention),
		})
	}
	return entities
}

func (h *Handler) SetInternalCall() app.HandlerFunc {
	return func(ctx context.Context, c *app.RequestContext) {
		c.Set(internalCallKey, true)
		c.Next(ctx)
	}
}
