package serverhandler

import (
	"context"
	"net/http"
	"time"

	"github.com/pkg/errors"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	serverservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service"
	replayservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/replay"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/common"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	"code.byted.org/devgpt/kiwis/lib/hertz"
	"code.byted.org/devgpt/kiwis/lib/metrics"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/middleware/hertz/pkg/app"
	"github.com/samber/lo"
)

type GetReplayResponse struct {
	Replay *nextagent.Replay      `thrift:"Replay,1,required" json:"replay"`
	Events []entity.EventData     `thrift:"Events,2,required" json:"events"`
	Role   *nextagent.SessionRole `json:"role,omitempty"`
}

func (h *Handler) GetReplay(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.GetReplayRequest](ctx, c)
	if req == nil {
		return
	}
	log.V1.CtxInfo(ctx, "get replay request: %+v", req)
	var err error

	reportMetricsFunc := metrics.NSM.ReportModuleMetrics(metrics.NextServerModuleReplay, "GetReplay")
	defer func() {
		reportMetricsFunc(err != nil, serverservice.ErrorToErrorReason(err))
	}()

	session, err := h.SessionService.GetSessionByReplay(ctx, req.ReplayID)
	if err != nil {
		log.V1.CtxError(ctx, "failed to get session by replay: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get session")
		return
	}
	events, err := h.SessionService.GetReplayEvents(ctx, req.ReplayID, session.ID)
	if err != nil {
		log.V1.CtxError(ctx, "failed to get session events: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get session events")
		return
	}
	events = lo.Filter(events, func(item *entity.Event, i int) bool {
		// 过滤掉中间状态的 error 事件和 session complete 事件
		if i < len(events)-1 && (item.EventName == nextagent.EventNameError || item.EventName == nextagent.EventNameSessionCompleted) {
			return false
		}
		return true
	})
	if session.CreatedAt.Unix() < 1743520496 { // 2025-04-01 23:14:56
		events = replaceEvents(events) // TODO: remove this
	}

	c.JSON(http.StatusOK, GetReplayResponse{
		Role: session.Role.ToIDL(),
		Replay: &nextagent.Replay{
			ID:        req.ReplayID,
			SessionID: session.ID,
			Title:     session.Title,
			CreatedAt: session.CreatedAt.Format(time.RFC3339),
			UpdatedAt: session.UpdatedAt.Format(time.RFC3339),
			Creator:   session.Creator,
		},
		Events: lo.Map(events, func(item *entity.Event, _ int) entity.EventData {
			return entity.EventData{
				Event: item.EventName,
				Data:  item.EventData.Data,
			}
		}),
	})
}

func (h *Handler) CreateReplay(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.CreateReplayRequest](ctx, c)
	if req == nil {
		return
	}
	log.V1.CtxInfo(ctx, "create replay request: %+v", req)

	var err error
	reportMetricsFunc := metrics.NSM.ReportModuleMetrics(metrics.NextServerModuleReplay, "CreateReplay")
	defer func() {
		reportMetricsFunc(err != nil, serverservice.ErrorToErrorReason(err))
	}()

	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}
	session, err := h.SessionService.GetSessionWithDeleted(ctx, req.SessionID, true)
	if err != nil {
		log.V1.CtxError(ctx, "failed to get session: %v", err)
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrInternal), "failed to get session")
		return
	}

	// 校验是否有创建replay的权限
	if _, ok := h.checkSessionPermission(ctx, c, SessionPermissionCheckOption{
		Account:   user,
		SessionID: lo.ToPtr(session.ID),
		Action:    entity.PermissionActionSessionShareCreate,
	}); !ok {
		return
	}

	// if (user.Username != session.Creator) && !h.UserService.IsGroupOperator(user) {
	// 	hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "permission denied")
	// 	return
	// }

	replay, err := h.ReplayService.CreateReplay(ctx, replayservice.CreateReplayOption{
		SessionID:        session.ID,
		EventOffsetStart: req.EventOffsetStart,
		EventOffsetEnd:   req.EventOffsetEnd,
		EventReadSync:    false,
		CreateFrom:       string(entity.ReplayFromUser),
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to create replay: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to create replay")
		return
	}

	c.JSON(http.StatusOK, nextagent.CreateReplayResponse{
		Replay: &nextagent.Replay{
			ID:        replay.ID,
			SessionID: replay.SessionID,
			Title:     session.Title,
			CreatedAt: replay.CreatedAt.Format(time.RFC3339),
			UpdatedAt: replay.UpdatedAt.Format(time.RFC3339),
			Creator:   session.Creator,
		},
	})
}

const (
	EventTypePreviewLink = "url.preview.get"
	EventTypeCardAction  = "card.action.trigger"
)

func (h *Handler) ShareReplayPreviewCallback(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.SharePreviewCallbackRequest](ctx, c)
	if req == nil {
		return
	}
	log.V1.CtxInfo(ctx, "share replay preview callback request: %+v", req)
	var err error
	reportMetricsFunc := metrics.NSM.ReportModuleMetrics(metrics.NextServerModuleReplay, "ShareReplayPreviewCallback")
	defer func() {
		reportMetricsFunc(err != nil, serverservice.ErrorToErrorReason(err))
	}()

	if req.GetType() == "url_verification" {
		c.JSON(http.StatusOK, nextagent.SharePreviewCallbackResponse{
			Challenge: req.Challenge,
		},
		)
		return
	}

	if req.GetEvent() == nil {
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "event is nil")
		return
	}

	switch req.GetHeader().GetEventType() {
	case EventTypePreviewLink:
		if req.GetEvent().GetContext() == nil {
			hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "context is nil")
			return
		}
		previewURL := req.GetEvent().GetContext().URL
		variable, err := h.ShareService.SharePreviewCallback(ctx, previewURL)
		if err != nil {
			if errors.Is(err, serverservice.ErrReplayNotFound) {
				c.JSON(http.StatusOK, nextagent.SharePreviewCallbackResponse{})
				return
			}
			log.V1.CtxError(ctx, "SharePreviewCallback err: %v", err)
			hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), err.Error())
			return
		}

		c.JSON(http.StatusOK, sharePreviewCallbackResponseFromEntity(variable))
	case EventTypeCardAction:
		err = h.LarkService.CardTrigger(ctx, req)
		if err != nil {
			log.V1.CtxError(ctx, "[lark card trigger] err: %v", err)
			hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), err.Error())
		}
		c.JSON(http.StatusOK, nextagent.SharePreviewCallbackResponse{Card: &nextagent.PreviewCard{
			Type: "raw",
		}})
	default:
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "event type is invalid")
		return
	}

}
