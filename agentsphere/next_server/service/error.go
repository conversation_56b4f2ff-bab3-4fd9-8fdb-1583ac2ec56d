package serverservice

import (
	"github.com/pkg/errors"
	"gorm.io/gorm"
)

var (
	// session
	ErrMaximumRunningSessionsReached          = errors.New("maximum running sessions reached")
	ErrGlobalMaximumRunningSessionsReached    = errors.New("global maximum running sessions reached")
	ErrMaximumRunningSessionCollectionReached = errors.New("maximum running session collection reached")
	ErrNotAllowedUseInternalTool              = errors.New("not allowed use internal tool")
	ErrAgentVersionNotFound                   = errors.New("agent version not found")
	ErrReplayNotFound                         = errors.New("replay not found")
	ErrSessionStopped                         = errors.New("session is stopped")
	ErrSessionNotFound                        = errors.New("session not found")
	ErrSessionOccurredError                   = errors.New("session occurred error")
	ErrorSessionCollectionNotFound            = errors.New("session collection not found")
	ErrorSessionCollectionExceededLineLimit   = errors.New("exceeded line limit")
	// artifact
	ErrorArtifactNotFound = errors.New("artifact not found")
	// deployment
	ErrorDeploymentNotFound = errors.New("deployment not found")
	ErrorDeploymentNoHTML   = errors.New("no html file found")
	// showcases
	ErrorShowcaseNotFound = errors.New("showcases not found")

	// activity
	ErrInvitationCodeAlreadyBind     = errors.New("invitation code already bind")
	ErrInvalidInvitationCode         = errors.New("invalid invitation code")
	ErrUserAlreadyBindInvitationCode = errors.New("user already bind invitation code")

	// template
	ErrTemplateNotFound      = errors.New("template not found")
	ErrTemplateFileNotFound  = errors.New("template file not found")
	ErrTemplateFileExists    = errors.New("template file exists")
	ErrTemplateShareNotFound = errors.New("template share not found")

	// space
	ErrSpaceNotFound         = errors.New("space not found")
	ErrSpaceUserNoAuth       = errors.New("space user has no auth")
	ErrorSpaceMemberNotFound = errors.New("space member not found")
	ErrNotAllowedSetToPublic = errors.New("not allowed set to public")

	// dataset
	ErrDatasetNotFound = errors.New("dataset not found")
)

func ErrorToErrorReason(err error) string {
	if err == nil {
		return ""
	}

	switch {
	case errors.Is(err, ErrMaximumRunningSessionsReached):
		return "maximum_running_sessions_reached"
	case errors.Is(err, ErrGlobalMaximumRunningSessionsReached):
		return "global_maximum_running_sessions_reached"
	case errors.Is(err, ErrMaximumRunningSessionCollectionReached):
		return "maximum_running_session_collection_reached"
	case errors.Is(err, ErrNotAllowedUseInternalTool):
		return "not_allowed_use_internal_tool"
	case errors.Is(err, ErrAgentVersionNotFound):
		return "agent_version_not_found"
	case errors.Is(err, ErrReplayNotFound):
		return "replay_not_found"
	case errors.Is(err, ErrSessionStopped):
		return "session_stopped"
	case errors.Is(err, ErrSessionNotFound):
		return "session_not_found"
	case errors.Is(err, ErrSessionOccurredError):
		return "session_occurred_error"
	case errors.Is(err, ErrorArtifactNotFound):
		return "artifact_not_found"
	case errors.Is(err, ErrorDeploymentNotFound):
		return "deployment_not_found"
	case errors.Is(err, ErrorDeploymentNoHTML):
		return "deployment_no_html"
	case errors.Is(err, ErrorShowcaseNotFound):
		return "showcase_not_found"
	case errors.Is(err, ErrInvalidInvitationCode):
		return "invalid_invitation_code"
	case errors.Is(err, ErrInvitationCodeAlreadyBind):
		return "invitation_code_already_bind"
	case errors.Is(err, gorm.ErrRecordNotFound):
		return "db_record_not_found"
	case errors.Is(err, gorm.ErrDuplicatedKey):
		return "db_duplicated_key"
	default:
		return "internal_error"
	}
}
