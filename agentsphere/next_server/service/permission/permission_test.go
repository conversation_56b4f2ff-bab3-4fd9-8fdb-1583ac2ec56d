package permission

import (
	"context"
	"testing"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	userservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/user"
	authentity "code.byted.org/devgpt/kiwis/copilotstack/common/auth/entity"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/tcc"
	"code.byted.org/devgpt/kiwis/lib/uuid"
	"github.com/samber/lo"
	"github.com/stretchr/testify/require"
)

func TestCreateAndDeleteResource(t *testing.T) {
	s := Service{
		dao:     dal.NewMockDAO(t),
		idGen:   uuid.GetDefaultGenerator(nil),
		tccConf: &config.AgentSphereTCCConfig{},
	}

	ctx := context.Background()

	// 创建带 Group 的，因为 Space Resource 没有创建，所以会报错
	resource, err := s.CreateResource(ctx, CreateResourceOption{
		Owner:         "test-user",
		Type:          entity.ResourceTypeSession,
		ExternalID:    "********-1111-1111-1111-********",
		Status:        lo.ToPtr(entity.ResourceStatusPrivate),
		GroupID:       lo.ToPtr("*************-2222-2222-********"),
		IsSpacePublic: lo.ToPtr(true),
	})
	require.Nil(t, resource)
	require.NotNil(t, err)
	require.ErrorContains(t, err, "failed to get space resource")

	// 先创建 Space Resource
	resourceSpace, err := s.CreateResource(ctx, CreateResourceOption{
		Owner:      "test-user",
		Type:       entity.ResourceTypeSpace,
		ExternalID: "*************-2222-2222-********",
		Status:     lo.ToPtr(entity.ResourceStatusPrivate),
	})
	require.Nil(t, err)
	require.Equal(t, "test-user", resourceSpace.Owner)

	// 再创建 Session Resource
	sessionResource, err := s.CreateResource(ctx, CreateResourceOption{
		Owner:         "test-user",
		Type:          entity.ResourceTypeSession,
		ExternalID:    "********-1111-1111-1111-********",
		Status:        lo.ToPtr(entity.ResourceStatusPrivate),
		GroupID:       lo.ToPtr("*************-2222-2222-********"),
		IsSpacePublic: lo.ToPtr(true),
	})
	require.Nil(t, err)
	require.Equal(t, "test-user", sessionResource.Owner)

	relation, err := s.dao.ListGroupResourceRelation(ctx, dal.ListGroupResourceRelationOption{
		ResourceID: lo.ToPtr(sessionResource.ID),
	})
	require.NoError(t, err)
	require.Len(t, relation, 1)

	permission, err := s.dao.ListPermission(ctx, dal.ListPermissionOption{
		ResourceIDs: []string{sessionResource.ID},
	})
	require.NoError(t, err)
	require.Len(t, permission, 2)

	err = s.DeleteResource(ctx, DeleteResourceOption{
		ResourceID: lo.ToPtr(sessionResource.ID),
	})
	require.NoError(t, err)

	relation, err = s.dao.ListGroupResourceRelation(ctx, dal.ListGroupResourceRelationOption{
		ResourceID: lo.ToPtr(sessionResource.ID),
	})
	require.NoError(t, err)
	require.Len(t, relation, 0)

	permission, err = s.dao.ListPermission(ctx, dal.ListPermissionOption{
		ResourceIDs: []string{sessionResource.ID},
	})
	require.NoError(t, err)
	require.Len(t, permission, 0)
}

func TestCreateAndDeletePermission(t *testing.T) {
	s := Service{
		dao:     dal.NewMockDAO(t),
		idGen:   uuid.GetDefaultGenerator(nil),
		tccConf: &config.AgentSphereTCCConfig{},
	}

	ctx := context.Background()

	resourceSpace, err := s.CreateResource(ctx, CreateResourceOption{
		Owner:      "test-user",
		Type:       entity.ResourceTypeSpace,
		ExternalID: "*************-2222-2222-********",
		Status:     lo.ToPtr(entity.ResourceStatusPrivate),
	})
	require.NoError(t, err)
	require.Equal(t, "test-user", resourceSpace.Owner)

	resource, err := s.AddResourcePermission(ctx, AddResourcePermissionOption{
		ResourceID: lo.ToPtr(resourceSpace.ID),
		PermissionMetas: []entity.PermissionMeta{
			{
				Type:       entity.PermissionTypeUser,
				ExternalID: "test-user-1",
				Role:       entity.PermissionRoleMember,
			},
			{
				Type:       entity.PermissionTypeDepartment,
				ExternalID: "department",
				Role:       entity.PermissionRoleMember,
			},
		},
	})
	require.NoError(t, err)
	require.Len(t, resource.Permissions, 2)
	require.Equal(t, "test-user-1", resource.Permissions[0].ExternalID)
	require.Equal(t, "department", resource.Permissions[1].ExternalID)
	require.NotEqual(t, resource.Permissions[0].ID, resource.Permissions[1].ID)

	permissions, err := s.dao.ListPermission(ctx, dal.ListPermissionOption{
		ResourceIDs: []string{resource.ID},
	})
	require.NoError(t, err)
	require.Len(t, permissions, 3)

	resource, err = s.RemoveResourcePermission(ctx, RemoveResourcePermissionOption{
		ResourceExternalID: lo.ToPtr(resource.ExternalID),
		ResourceType:       lo.ToPtr(resource.Type),
		PermissionMetas: []entity.PermissionMeta{
			{
				Type:       entity.PermissionTypeDepartment,
				ExternalID: "department",
				Role:       entity.PermissionRoleMember,
			},
			{
				Type:       entity.PermissionTypeUser,
				ExternalID: "test-user-1",
				Role:       entity.PermissionRoleMember,
			},
		},
	})
	require.NoError(t, err)

	permissions, err = s.dao.ListPermission(ctx, dal.ListPermissionOption{
		ResourceIDs: []string{resource.ID},
	})
	require.NoError(t, err)
	require.Len(t, permissions, 1)
}

func TestGetUserPermission(t *testing.T) {
	mockDao := dal.NewMockDAO(t)
	conf := mockConfig()
	userService, err := userservice.NewService(userservice.CreateServiceOption{
		DAO:     mockDao,
		TccConf: conf,
	})
	require.Nil(t, err)
	s := Service{
		dao:         mockDao,
		idGen:       uuid.GetDefaultGenerator(nil),
		tccConf:     conf,
		userService: userService,
	}

	mockPermissionData(t, mockDao)

	// department member test
	res, err := s.GetUserResourcePermission(context.Background(), GetUserResourcePermissionOption{
		Account: authentity.Account{
			Username:   "test-user",
			Department: "codebase",
		},
		ResourceID: lo.ToPtr("********-1111-1111-1111-********"),
	})
	require.Nil(t, err)
	require.Len(t, res.Permissions, 1)
	require.Equal(t, entity.PermissionRoleMember, res.Permissions[0].Role)
	require.Len(t, res.Permissions[0].PermissionActions, 4)

	// user + department + special role test
	res, err = s.GetUserResourcePermission(context.Background(), GetUserResourcePermissionOption{
		Account: authentity.Account{
			Username:   "test-user-1",
			Department: "codebase",
		},
		ResourceID: lo.ToPtr("********-1111-1111-1111-********"),
	})
	require.Nil(t, err)
	require.Len(t, res.Permissions, 3)

	// space public test
	res, err = s.GetUserResourcePermission(context.Background(), GetUserResourcePermissionOption{
		Account: authentity.Account{
			Username:   "test-user-5",
			Department: "codebase-test",
		},
		ResourceID: lo.ToPtr("********-1111-1111-1111-********"),
	})
	require.Nil(t, err)
	require.Len(t, res.Permissions, 2)
	require.Equal(t, entity.PermissionTypeSpace, res.Permissions[0].Type)
	require.Equal(t, entity.PermissionRoleVisitor, res.Permissions[0].Role)
	require.Equal(t, "*************-2222-2222-********", res.Permissions[0].ExternalID)
	require.Len(t, res.Permissions[0].PermissionActions, 3)

	// global public test
	res, err = s.GetUserResourcePermission(context.Background(), GetUserResourcePermissionOption{
		Account: authentity.Account{
			Username:   "no-user",
			Department: "codebase-test",
		},
		ResourceID: lo.ToPtr("********-1111-1111-1111-********"),
	})
	require.Nil(t, err)
	require.Len(t, res.Permissions, 1)
	require.Equal(t, entity.PermissionTypeUser, res.Permissions[0].Type)
	require.Equal(t, entity.PermissionRoleVisitor, res.Permissions[0].Role)
	require.Len(t, res.Permissions[0].PermissionActions, 3)

	// developer user
	res, err = s.GetUserResourcePermission(context.Background(), GetUserResourcePermissionOption{
		Account: authentity.Account{
			Username:   "test-user-2",
			Department: "codebase-test",
		},
		ResourceID: lo.ToPtr("********-1111-1111-1111-********"),
	})
	require.NoError(t, err)
	require.Len(t, res.Permissions, 3)

	// mcp partner
	res, err = s.GetUserResourcePermission(context.Background(), GetUserResourcePermissionOption{
		Account: authentity.Account{
			Username:   "test-user-3",
			Department: "codebase-test",
		},
		ResourceID: lo.ToPtr("********-1111-1111-1111-********"),
	})
	require.NoError(t, err)
	require.Len(t, res.Permissions, 1)
	require.Equal(t, entity.PermissionTypeUser, res.Permissions[0].Type)
	require.Equal(t, entity.PermissionRoleMCPPartner, res.Permissions[0].Role)
	require.Len(t, res.Permissions[0].PermissionActions, 1)

	// 用户所在的部门是有某个 Space 的权限
	res, err = s.GetUserResourcePermission(context.Background(), GetUserResourcePermissionOption{
		Account: authentity.Account{
			Username:   "test-user-10",
			Department: "codebase",
		},
		ResourceID: lo.ToPtr("********-1111-1111-1111-********"),
	})
	require.NoError(t, err)
	require.Len(t, res.Permissions, 2)
	require.Equal(t, entity.PermissionTypeSpace, res.Permissions[0].Type)
	require.Equal(t, entity.PermissionRoleVisitor, res.Permissions[0].Role)
}

func TestGetUserPermissions(t *testing.T) {
	mockDao := dal.NewMockDAO(t)
	conf := mockConfig()
	userService, err := userservice.NewService(userservice.CreateServiceOption{
		DAO:     mockDao,
		TccConf: conf,
	})
	require.Nil(t, err)
	s := Service{
		dao:         mockDao,
		idGen:       uuid.GetDefaultGenerator(nil),
		tccConf:     conf,
		userService: userService,
	}

	mockPermissionData(t, mockDao)

	// 查询空间类型，test-user-1 的权限，预期返回 1 个资源，admin，member 两个权限
	ctx := context.Background()
	resources, err := s.GetUserPermissions(ctx, GetUserPermissionsOption{
		Account: authentity.Account{
			Username:   "test-user-1",
			Department: "codebase",
		},
		ResourceType: entity.ResourceTypeSpace,
	})
	require.NoError(t, err)
	require.Len(t, resources, 1)
	require.Equal(t, entity.ResourceTypeSpace, resources[0].Type)
	require.Len(t, resources[0].Permissions, 2)
	require.Equal(t, entity.PermissionRoleAdmin, resources[0].Permissions[0].Role)
	require.Equal(t, entity.PermissionRoleMember, resources[0].Permissions[1].Role)

	// 查询 Session 类型，完全公开的一个资源，用户没有任何权限，预期返回一个资源，有 visitor 权限
	resources, err = s.GetUserPermissions(ctx, GetUserPermissionsOption{
		Account: authentity.Account{
			Username:   "no-user",
			Department: "no-department",
		},
		ResourceType: entity.ResourceTypeSession,
	})
	require.NoError(t, err)
	require.Len(t, resources, 1)
	require.Equal(t, entity.ResourceTypeSession, resources[0].Type)
	require.Len(t, resources[0].Permissions, 1)
	require.Equal(t, entity.PermissionRoleVisitor, resources[0].Permissions[0].Role)

	// 查询 Session 类型，用户有管理权限的 session，预期返回一个资源
	resources, err = s.GetUserPermissions(ctx, GetUserPermissionsOption{
		Account: authentity.Account{
			Username:   "test-user-2",
			Department: "no-department",
		},
		ResourceType: entity.ResourceTypeSession,
		Roles:        []entity.PermissionRole{entity.PermissionRoleAdmin},
	})
	require.NoError(t, err)
	require.Len(t, resources, 1)
	require.Equal(t, entity.ResourceTypeSession, resources[0].Type)
	require.Len(t, resources[0].Permissions, 1)
	require.Equal(t, entity.PermissionRoleAdmin, resources[0].Permissions[0].Role)

	// 查询 Session 类型，用户有读权限的 session，预期返回两个资源
	resources, err = s.GetUserPermissions(ctx, GetUserPermissionsOption{
		Account: authentity.Account{
			Username:   "test-user-2",
			Department: "no-department",
		},
		ResourceType: entity.ResourceTypeSession,
		Actions:      []entity.PermissionAction{entity.PermissionActionSessionRead},
	})
	require.NoError(t, err)
	require.Len(t, resources, 2)

	// 查询 Session 类型，用户只有公开资源的权限
	resources, err = s.GetUserPermissions(ctx, GetUserPermissionsOption{
		Account: authentity.Account{
			Username:   "no-user",
			Department: "no-department",
		},
		ResourceType: entity.ResourceTypeSession,
	})
	require.NoError(t, err)
	require.Len(t, resources, 1)
	require.Equal(t, entity.ResourceTypeSession, resources[0].Type)
	require.Len(t, resources[0].Permissions, 1)
	require.Equal(t, entity.PermissionRoleVisitor, resources[0].Permissions[0].Role)

	// 查询可读 Session 列表
	resources, err = s.GetUserPermissions(ctx, GetUserPermissionsOption{
		Account: authentity.Account{
			Username:   "test-user-5",
			Department: "no-department",
		},
		ResourceType: entity.ResourceTypeSession,
	})
	require.NoError(t, err)
	require.Len(t, resources, 3)
}

func TestCheckPermission(t *testing.T) {
	mockDao := dal.NewMockDAO(t)
	conf := mockConfig()
	userService, err := userservice.NewService(userservice.CreateServiceOption{
		DAO:     mockDao,
		TccConf: conf,
	})
	require.Nil(t, err)
	s := Service{
		dao:         mockDao,
		idGen:       uuid.GetDefaultGenerator(nil),
		tccConf:     conf,
		userService: userService,
	}

	mockPermissionData(t, mockDao)

	// 创建空间权限检查
	ctx := context.Background()
	res, err := s.CheckPermission(ctx, CheckPermissionOption{
		Account: authentity.Account{
			Username:   "test-user-3",
			Department: "codebase",
		},
		Action: entity.PermissionActionSpaceCreate,
	})
	require.NoError(t, err)
	require.Equal(t, false, res.Allowed)

	res, err = s.CheckPermission(ctx, CheckPermissionOption{
		Account: authentity.Account{
			Username:   "test-user-2",
			Department: "codebase",
		},
		Action: entity.PermissionActionSpaceCreate,
	})
	require.NoError(t, err)
	require.Equal(t, true, res.Allowed)

	// 其他资产创建权限检查
	res, err = s.CheckPermission(ctx, CheckPermissionOption{
		Account: authentity.Account{
			Username:   "test-user-2",
			Department: "no-department",
		},
		Action:  entity.PermissionActionSessionCreate,
		GroupID: lo.ToPtr("*************-2222-2222-********"),
	})
	require.NoError(t, err)
	require.Equal(t, false, res.Allowed)

	res, err = s.CheckPermission(ctx, CheckPermissionOption{
		Account: authentity.Account{
			Username:   "test-user-2",
			Department: "codebase",
		},
		Action:  entity.PermissionActionKnowledgebaseCreate,
		GroupID: lo.ToPtr("*************-2222-2222-********"),
	})
	require.NoError(t, err)
	require.Equal(t, true, res.Allowed)

	// 正常资产权限
	res, err = s.CheckPermission(ctx, CheckPermissionOption{
		Account: authentity.Account{
			Username:   "test-user-3",
			Department: "no-department",
		},
		Action:     entity.PermissionActionSessionChat,
		ResourceID: lo.ToPtr("********-1111-1111-1111-********"),
	})
	require.NoError(t, err)
	require.Equal(t, false, res.Allowed)

	res, err = s.CheckPermission(ctx, CheckPermissionOption{
		Account: authentity.Account{
			Username:   "test-user-2",
			Department: "no-department",
		},
		Action:     entity.PermissionActionSessionRead,
		ResourceID: lo.ToPtr("********-1111-1111-1111-********"),
	})
	require.NoError(t, err)
	require.Equal(t, true, res.Allowed)

	res, err = s.CheckPermission(ctx, CheckPermissionOption{
		Account: authentity.Account{
			Username:   "no-user",
			Department: "no-department",
		},
		Action:     entity.PermissionActionSessionRead,
		ResourceID: lo.ToPtr("********-1111-1111-1111-********"),
	})
	require.NoError(t, err)
	require.Equal(t, true, res.Allowed)

	res, err = s.CheckPermission(ctx, CheckPermissionOption{
		Account: authentity.Account{
			Username:   "no-user",
			Department: "codebase",
		},
		Action:     entity.PermissionActionSessionRead,
		ResourceID: lo.ToPtr("********-1111-1111-1111-********"),
	})
	require.NoError(t, err)
	require.Equal(t, true, res.Allowed)
}

func TestGetResource(t *testing.T) {
	mockDao := dal.NewMockDAO(t)
	conf := mockConfig()
	userService, err := userservice.NewService(userservice.CreateServiceOption{
		DAO:     mockDao,
		TccConf: conf,
	})
	require.Nil(t, err)
	s := Service{
		dao:         mockDao,
		idGen:       uuid.GetDefaultGenerator(nil),
		tccConf:     conf,
		userService: userService,
	}

	mockPermissionData(t, mockDao)

	res, err := s.GetResource(context.Background(), GetResourceOption{
		ResourceID:     lo.ToPtr("********-1111-1111-1111-********"),
		NeedPermission: true,
	})
	require.NoError(t, err)
	require.Len(t, res.Permissions, 4)
	require.Len(t, res.GroupRelation, 0)

	res, err = s.GetResource(context.Background(), GetResourceOption{
		ResourceID:     lo.ToPtr("********-1111-1111-1111-********"),
		NeedPermission: true,
		NeedGroup:      true,
	})
	require.NoError(t, err)
	require.Len(t, res.Permissions, 2)
	require.Len(t, res.GroupRelation, 1)
}

func TestListResource(t *testing.T) {
	mockDao := dal.NewMockDAO(t)
	conf := mockConfig()
	userService, err := userservice.NewService(userservice.CreateServiceOption{
		DAO:     mockDao,
		TccConf: conf,
	})
	require.Nil(t, err)
	s := Service{
		dao:         mockDao,
		idGen:       uuid.GetDefaultGenerator(nil),
		tccConf:     conf,
		userService: userService,
	}

	mockPermissionData(t, mockDao)

	total, resources, err := s.ListResources(context.Background(), ListResourceOption{
		ResourceType: entity.ResourceTypeSession,
	})
	require.NoError(t, err)
	require.Equal(t, int64(3), total)
	require.Len(t, resources, 3)

	total, resources, err = s.ListResources(context.Background(), ListResourceOption{
		ResourceType: entity.ResourceTypeSession,
		Offset:       1,
		Limit:        2,
	})
	require.NoError(t, err)
	require.Equal(t, int64(3), total)
	require.Len(t, resources, 2)

	total, resources, err = s.ListResources(context.Background(), ListResourceOption{
		ResourceType: entity.ResourceTypeSpace,
		Owner:        lo.ToPtr("test-user-1"),
	})
	require.NoError(t, err)
	require.Equal(t, int64(1), total)
	require.Len(t, resources, 1)

	total, resources, err = s.ListResources(context.Background(), ListResourceOption{
		ResourceType:      entity.ResourceTypeSession,
		Status:            lo.ToPtr(entity.ResourceStatusPrivate),
		NeedGroupRelation: true,
	})
	require.NoError(t, err)
	require.Equal(t, int64(2), total)
	require.Len(t, resources, 2)
	require.Len(t, resources[0].GroupRelation, 1)

	total, resources, err = s.ListResources(context.Background(), ListResourceOption{
		ResourceType: entity.ResourceTypeSession,
		GroupID:      lo.ToPtr("*************-2222-2222-********"),
	})
	require.NoError(t, err)
	require.Equal(t, int64(3), total)
	require.Len(t, resources, 3)
}

func TestUpdateResource(t *testing.T) {
	mockDao := dal.NewMockDAO(t)
	conf := mockConfig()
	userService, err := userservice.NewService(userservice.CreateServiceOption{
		DAO:     mockDao,
		TccConf: conf,
	})
	require.Nil(t, err)
	s := Service{
		dao:         mockDao,
		idGen:       uuid.GetDefaultGenerator(nil),
		tccConf:     conf,
		userService: userService,
	}

	mockPermissionData(t, mockDao)

	// 修改 Owner
	res, err := s.UpdateResource(context.Background(), UpdateResourceOptions{
		ResourceID: lo.ToPtr("********-1111-1111-1111-********"),
		Owner:      lo.ToPtr("test-user-2"),
	})
	require.NoError(t, err)
	require.Equal(t, "test-user-2", res.Owner)
	require.True(t, lo.ContainsBy(res.Permissions, func(item *entity.Permission) bool {
		return item.Role == entity.PermissionRoleAdmin && item.ExternalID == "test-user-2"
	}))
	require.False(t, lo.ContainsBy(res.Permissions, func(item *entity.Permission) bool {
		return item.Role == entity.PermissionRoleAdmin && item.ExternalID == "test-user-1"
	}))
	res, err = s.GetUserResourcePermission(context.Background(), GetUserResourcePermissionOption{
		Account: authentity.Account{
			Username:   "test-user-2",
			Department: "no-department",
		},
		ResourceID:      lo.ToPtr("********-1111-1111-1111-********"),
		PermissionRoles: []entity.PermissionRole{entity.PermissionRoleAdmin},
	})
	require.NoError(t, err)
	require.Len(t, res.Permissions, 1)
	require.Equal(t, entity.PermissionRoleAdmin, res.Permissions[0].Role)
	require.Equal(t, "test-user-2", res.Permissions[0].ExternalID)

	// 修改 Session 为项目内公开
	// test-user-4 没有权限
	res, err = s.GetUserResourcePermission(context.Background(), GetUserResourcePermissionOption{
		Account: authentity.Account{
			Username:   "test-user-4",
			Department: "no-department",
		},
		ResourceID: lo.ToPtr("********-1111-1111-1111-********"),
	})
	require.NoError(t, err)
	require.Len(t, res.Permissions, 0)

	res, err = s.UpdateResource(context.Background(), UpdateResourceOptions{
		ResourceID:    lo.ToPtr("********-1111-1111-1111-********"),
		IsSpacePublic: lo.ToPtr(true),
	})
	require.NoError(t, err)
	require.True(t, lo.ContainsBy(res.Permissions, func(item *entity.Permission) bool {
		return item.ExternalID == "*************-2222-2222-********"
	}))
	// test-user-4 是 space 成员，有了 visitor 权限
	res, err = s.GetUserResourcePermission(context.Background(), GetUserResourcePermissionOption{
		Account: authentity.Account{
			Username:   "test-user-4",
			Department: "no-department",
		},
		ResourceID: lo.ToPtr("********-1111-1111-1111-********"),
	})
	require.NoError(t, err)
	require.Len(t, res.Permissions, 1)
	require.Equal(t, entity.PermissionRoleVisitor, res.Permissions[0].Role)
	require.Equal(t, "*************-2222-2222-********", res.Permissions[0].ExternalID)

	// 修改为项目内不公开
	res, err = s.UpdateResource(context.Background(), UpdateResourceOptions{
		ResourceID:    lo.ToPtr("********-1111-1111-1111-********"),
		IsSpacePublic: lo.ToPtr(false),
	})
	require.NoError(t, err)
	require.False(t, lo.ContainsBy(res.Permissions, func(item *entity.Permission) bool {
		return item.ExternalID == "*************-2222-2222-********"
	}))
	res, err = s.GetUserResourcePermission(context.Background(), GetUserResourcePermissionOption{
		Account: authentity.Account{
			Username:   "test-user-4",
			Department: "no-department",
		},
		ResourceID: lo.ToPtr("********-1111-1111-1111-********"),
	})
	require.NoError(t, err)
	require.Len(t, res.Permissions, 0)

	// 修改全局公开
	res, err = s.UpdateResource(context.Background(), UpdateResourceOptions{
		ResourceID: lo.ToPtr("********-1111-1111-1111-********"),
		Status:     lo.ToPtr(entity.ResourceStatusPublic),
	})
	require.NoError(t, err)
	res, err = s.GetUserResourcePermission(context.Background(), GetUserResourcePermissionOption{
		Account: authentity.Account{
			Username:   "test-user-4",
			Department: "no-department",
		},
		ResourceID: lo.ToPtr("********-1111-1111-1111-********"),
	})
	require.NoError(t, err)
	require.Len(t, res.Permissions, 1)
	require.Equal(t, entity.PermissionRoleVisitor, res.Permissions[0].Role)
	require.Equal(t, "test-user-4", res.Permissions[0].ExternalID)

	// 修改全局不公开
	res, err = s.UpdateResource(context.Background(), UpdateResourceOptions{
		ResourceID: lo.ToPtr("********-1111-1111-1111-********"),
		Status:     lo.ToPtr(entity.ResourceStatusPrivate),
	})
	require.NoError(t, err)
	res, err = s.GetUserResourcePermission(context.Background(), GetUserResourcePermissionOption{
		Account: authentity.Account{
			Username:   "test-user-4",
			Department: "no-department",
		},
		ResourceID: lo.ToPtr("********-1111-1111-1111-********"),
	})
	require.NoError(t, err)
	require.Len(t, res.Permissions, 0)
}

// 创建 space 资源 ********-1111-1111-1111-********，space id 为 *************-2222-2222-********， owner 为 test-user-1
// 创建 session 资源 ********-1111-1111-1111-********，owner 为 test-user-2 不公开
// 创建 session 资源 ********-1111-1111-1111-********， owner 为 test-user-3 公开
// 创建 session 资源 ********-1111-1111-1111-********， owner 为 test-user-4 不公开, 授予 test-user-5 访问角色
// 授予 space ********-1111-1111-1111-******** test-user-4 test-user-5 member, codebase member 权限
// 授予 session ********-1111-1111-1111-******** test-user-5 visitor 权限
// 授予 session ********-1111-1111-1111-******** space *************-2222-2222-******** visitor 权限

func mockPermissionData(t *testing.T, dao *dal.DAO) {
	ctx := context.Background()
	_, err := dao.CreateResource(ctx, dal.CreateResourceOption{
		ID:         "********-1111-1111-1111-********",
		Type:       entity.ResourceTypeSpace,
		ExternalID: "*************-2222-2222-********",
		Owner:      "test-user-1",
		Status:     entity.ResourceStatusPrivate,
	})
	require.NoError(t, err)
	_, err = dao.CreatePermission(ctx, dal.CreatePermissionOption{
		ID:           "********-1111-1111-1111-********",
		ResourceID:   "********-1111-1111-1111-********",
		ResourceType: entity.ResourceTypeSpace,
		Type:         entity.PermissionTypeUser,
		ExternalID:   "test-user-1",
		Role:         entity.PermissionRoleAdmin,
	})
	require.NoError(t, err)

	_, err = dao.CreateResource(ctx, dal.CreateResourceOption{
		ID:         "********-1111-1111-1111-********",
		Type:       entity.ResourceTypeSession,
		ExternalID: "33333333-3333-3333-3333-33333333",
		Owner:      "test-user-2",
		Status:     entity.ResourceStatusPrivate,
		GroupID:    lo.ToPtr("*************-2222-2222-********"),
	})
	require.NoError(t, err)
	_, err = dao.CreatePermission(ctx, dal.CreatePermissionOption{
		ID:           "********-1111-1111-1111-********",
		ResourceID:   "********-1111-1111-1111-********",
		ResourceType: entity.ResourceTypeSession,
		Type:         entity.PermissionTypeUser,
		ExternalID:   "test-user-2",
		Role:         entity.PermissionRoleAdmin,
	})
	require.NoError(t, err)

	_, err = dao.CreateResource(ctx, dal.CreateResourceOption{
		ID:         "********-1111-1111-1111-********",
		Type:       entity.ResourceTypeSession,
		ExternalID: "33333333-3333-3333-3333-33333334",
		Owner:      "test-user-3",
		Status:     entity.ResourceStatusPublic,
		GroupID:    lo.ToPtr("*************-2222-2222-********"),
	})
	require.NoError(t, err)
	_, err = dao.CreatePermission(ctx, dal.CreatePermissionOption{
		ID:           "********-1111-1111-1111-********",
		ResourceID:   "********-1111-1111-1111-********",
		ResourceType: entity.ResourceTypeSession,
		Type:         entity.PermissionTypeUser,
		ExternalID:   "test-user-3",
		Role:         entity.PermissionRoleAdmin,
	})
	require.NoError(t, err)

	_, err = dao.CreateResource(ctx, dal.CreateResourceOption{
		ID:         "********-1111-1111-1111-********",
		Type:       entity.ResourceTypeSession,
		ExternalID: "33333333-3333-3333-3333-33333335",
		Owner:      "test-user-4",
		Status:     entity.ResourceStatusPrivate,
		GroupID:    lo.ToPtr("*************-2222-2222-********"),
	})
	require.NoError(t, err)
	_, err = dao.CreatePermission(ctx, dal.CreatePermissionOption{
		ID:           "********-1111-1111-1111-11111121",
		ResourceID:   "********-1111-1111-1111-********",
		ResourceType: entity.ResourceTypeSession,
		Type:         entity.PermissionTypeUser,
		ExternalID:   "test-user-4",
		Role:         entity.PermissionRoleAdmin,
	})
	require.NoError(t, err)
	_, err = dao.CreatePermission(ctx, dal.CreatePermissionOption{
		ID:           "********-1111-1111-1111-11111122",
		ResourceID:   "********-1111-1111-1111-********",
		ResourceType: entity.ResourceTypeSession,
		Type:         entity.PermissionTypeUser,
		ExternalID:   "test-user-5",
		Role:         entity.PermissionRoleVisitor,
	})
	require.NoError(t, err)

	_, err = dao.CreatePermission(ctx, dal.CreatePermissionOption{
		ID:           "********-1111-1111-1111-11111115",
		ResourceID:   "********-1111-1111-1111-********",
		ResourceType: entity.ResourceTypeSpace,
		Type:         entity.PermissionTypeUser,
		ExternalID:   "test-user-4",
		Role:         entity.PermissionRoleMember,
	})
	require.NoError(t, err)

	_, err = dao.CreatePermission(ctx, dal.CreatePermissionOption{
		ID:           "********-1111-1111-1111-11111116",
		ResourceID:   "********-1111-1111-1111-********",
		ResourceType: entity.ResourceTypeSpace,
		Type:         entity.PermissionTypeUser,
		ExternalID:   "test-user-5",
		Role:         entity.PermissionRoleMember,
	})
	require.NoError(t, err)

	_, err = dao.CreatePermission(ctx, dal.CreatePermissionOption{
		ID:           "********-1111-1111-1111-11111117",
		ResourceID:   "********-1111-1111-1111-********",
		ResourceType: entity.ResourceTypeSpace,
		Type:         entity.PermissionTypeDepartment,
		ExternalID:   "codebase",
		Role:         entity.PermissionRoleMember,
	})
	require.NoError(t, err)

	_, err = dao.CreatePermission(ctx, dal.CreatePermissionOption{
		ID:           "********-1111-1111-1111-11111118",
		ResourceID:   "********-1111-1111-1111-********",
		ResourceType: entity.ResourceTypeSession,
		Type:         entity.PermissionTypeUser,
		ExternalID:   "test-user-5",
		Role:         entity.PermissionRoleVisitor,
	})
	require.NoError(t, err)

	_, err = dao.CreatePermission(ctx, dal.CreatePermissionOption{
		ID:           "********-1111-1111-1111-11111119",
		ResourceID:   "********-1111-1111-1111-********",
		ResourceType: entity.ResourceTypeSession,
		Type:         entity.PermissionTypeSpace,
		ExternalID:   "*************-2222-2222-********",
		Role:         entity.PermissionRoleVisitor,
	})
	require.NoError(t, err)
}

func mockConfig() *config.AgentSphereTCCConfig {
	conf := &config.AgentSphereTCCConfig{}
	permissionActionConf, _ := tcc.NewConfig[config.PermissionActionConfig](
		config.PermissionActionConfig{
			Roles: map[string][]string{
				entity.PermissionRoleAdmin.String(): {
					string(entity.PermissionActionSpaceRead),
					string(entity.PermissionActionSessionRead),
					string(entity.PermissionActionSessionCreate),
					string(entity.PermissionActionSessionDelete),
					string(entity.PermissionActionSessionUpdate),
					string(entity.PermissionActionSessionTemplateCreate),
					string(entity.PermissionActionKnowledgebaseRead),
					string(entity.PermissionActionKnowledgebaseCreate),
					string(entity.PermissionActionKnowledgebaseUpdate),
					string(entity.PermissionActionKnowledgebaseDelete),
					string(entity.PermissionActionMCPCreate),
					string(entity.PermissionActionMCPRead),
					string(entity.PermissionActionMCPUpdate),
					string(entity.PermissionActionMCPDelete),
					string(entity.PermissionActionTemplateCreate),
					string(entity.PermissionActionTemplateRead),
					string(entity.PermissionActionTemplateUpdate),
					string(entity.PermissionActionTemplateDelete),
					string(entity.PermissionActionSessionChat),
					string(entity.PermissionActionSessionDownload),
					string(entity.PermissionActionSessionFeedback),
					string(entity.PermissionActionSessionVisualization),
					string(entity.PermissionActionSessionShareCreate),
					string(entity.PermissionActionSessionVSCodeOpen),
				},
				entity.PermissionRoleMember.String(): {
					string(entity.PermissionActionSpaceRead),
					string(entity.PermissionActionSessionRead),
					string(entity.PermissionActionSessionCreate),
					string(entity.PermissionActionKnowledgebaseRead),
					string(entity.PermissionActionMCPCreate),
					string(entity.PermissionActionMCPRead),
					string(entity.PermissionActionTemplateCreate),
					string(entity.PermissionActionTemplateRead),
					string(entity.PermissionActionSessionChat),
					string(entity.PermissionActionSessionDownload),
					string(entity.PermissionActionSessionFeedback),
				},
				entity.PermissionRoleVisitor.String(): {
					string(entity.PermissionActionSpaceRead),
					string(entity.PermissionActionSessionRead),
					string(entity.PermissionActionKnowledgebaseRead),
					string(entity.PermissionActionMCPRead),
					string(entity.PermissionActionTemplateRead),
					string(entity.PermissionActionSessionChat),
					string(entity.PermissionActionSessionVisualization),
				},
				entity.PermissionRoleDeveloper.String(): {
					string(entity.PermissionActionSpaceCreate),
				},
				entity.PermissionRoleOperator.String(): {
					string(entity.PermissionActionSessionRead),
				},
				entity.PermissionRoleMCPPartner.String(): {
					string(entity.PermissionActionSessionRead),
				},
			},
		}, "", "", "", tcc.ConfigFormatYAML,
	)
	nextAgentUserRoleConfig, _ := tcc.NewConfig[config.NextAgentUserRoleConfig](
		config.NextAgentUserRoleConfig{
			Roles: []config.RoleConfig{
				{
					Name:       "CollectionOperator",
					UserGroups: []string{"Operator", "Developer"},
				},
				{
					Name:       "TraceSession",
					UserGroups: []string{"Developer"},
				},
				{
					Name:       "MCPPlayground",
					UserGroups: []string{"Developer", "Partner"},
				},
			},
			UserGroups: []config.UserGroupConfig{
				{
					Name:  "Operator",
					Users: []string{"test-user-1"},
				},
				{
					Name:  "Developer",
					Users: []string{"test-user-2"},
				},
				{
					Name:  "Partner",
					Users: []string{"test-user-3"},
				},
			},
		}, "", "", "", tcc.ConfigFormatYAML)
	conf.PermissionActionConfig = permissionActionConf
	conf.NextAgentUserRoleConfig = nextAgentUserRoleConfig

	return conf
}
