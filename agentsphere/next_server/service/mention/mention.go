package mention

import (
	"context"
	"net/url"
	"path/filepath"
	"sort"
	"strconv"
	"strings"

	"code.byted.org/codebase/sdk/v2"
	"code.byted.org/codebase/sdk/v2/types/repository"
	"code.byted.org/codebase/sdk/v2/types/vcs"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/service/knowledgebase"
	authentity "code.byted.org/devgpt/kiwis/copilotstack/common/auth/entity"
	"code.byted.org/devgpt/kiwis/port/nextcode"
	"code.byted.org/gopkg/lang/conv"
	"github.com/AlekSi/pointer"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/fx"
)

var (
	ErrMustHaveRepositoryID = errors.New("must have repository id")
)

type Service struct {
	nextCodeClient       nextcode.Client
	knowledgeBaseService *knowledgebase.Service
}

type CreateServiceOption struct {
	fx.In
	NextCodeClient       nextcode.Client
	KnowledgeBaseService *knowledgebase.Service
}

func NewService(opt CreateServiceOption) (*Service, error) {
	s := &Service{
		nextCodeClient:       opt.NextCodeClient,
		knowledgeBaseService: opt.KnowledgeBaseService,
	}
	return s, nil
}

type SearchMentionsOptions struct {
	User             authentity.Account
	Type             entity.MentionType
	Query            *string
	SpaceID          *string
	NextID           *string
	RepositoryID     *string
	RepositoryBranch *string
	Limit            *int32
}

func (s *Service) SearchMentions(ctx context.Context, opt SearchMentionsOptions) (*entity.Mention, error) {
	switch opt.Type {
	case entity.MentionTypeRepository:
		return s.searchRepositoryMentions(ctx, opt)
	case entity.MentionTypeRepositoryBranch:
		return s.searchRepositoryBranchMentions(ctx, opt)
	case entity.MentionTypeRepositoryDir, entity.MentionTypeRepositoryFile:
		return s.searchRepositoryFileOrDir(ctx, opt)
	case entity.MentionTypeKnowledgeBase:
		return s.searchKnowledgeBase(ctx, opt)
	default:
		// 默认返回知识库文档数据
		return s.searchKnowledgeBase(ctx, opt)
	}
}

func (s *Service) translatePageSizeAndNumber(sourceOffset string, sourceLimit int32) (int32, int32, error) {
	var offsetInt int64
	var err error
	if sourceOffset != "" {
		offsetInt, err = strconv.ParseInt(sourceOffset, 10, 64)
		if err != nil {
			return 0, 0, err
		}
	} else {
		offsetInt = 0
	}

	if sourceLimit <= 0 {
		sourceLimit = 10
	}
	if offsetInt < 0 {
		offsetInt = 0
	}

	return sourceLimit, int32(offsetInt)/sourceLimit + 1, nil
}

func (s *Service) withUserJwtOpts(account authentity.Account) []sdk.CallOption {
	// client 默认注入了 app 鉴权，这里使用用户身份，请求时主动移除 app 鉴权信息
	return []sdk.CallOption{sdk.WithAppAuth(0, ""), sdk.WithUserJWT(account.NextCodeUserJWT)}
}

func (s *Service) searchRepositoryMentions(ctx context.Context, opt SearchMentionsOptions) (*entity.Mention, error) {
	// next id 就是 offset
	// 转换 offset 和 limit 到 page size 和 page number
	pageSize, pageNumber, err := s.translatePageSizeAndNumber(pointer.Get(opt.NextID), pointer.Get(opt.Limit))
	if err != nil {
		return nil, errors.WithMessage(err, "failed translate page size and number")
	}

	repositories, err := s.nextCodeClient.ListRepositories(ctx, repository.ListRepositoriesRequest{
		Query:      opt.Query,
		PageNumber: &pageNumber,
		PageSize:   &pageSize,
	}, s.withUserJwtOpts(opt.User)...)
	if err != nil {
		return nil, errors.WithMessage(err, "failed list repository")
	}
	var hasMore *bool
	var nextID *string
	if len(repositories.Repositories) < int(pageSize) {
		hasMore = lo.ToPtr(false)
	} else {
		hasMore = lo.ToPtr(true)
		nextID = lo.ToPtr(strconv.FormatInt(int64(pageSize*pageNumber), 10))
	}

	return &entity.Mention{
		Type:    entity.MentionTypeRepository,
		NextID:  nextID,
		HasMore: hasMore,
		Repositories: lo.Map(repositories.Repositories, func(item *repository.Repository, index int) *entity.Repository {
			id, _ := strconv.ParseInt(item.Id, 10, 64)
			return &entity.Repository{
				ID:   id,
				Name: item.Path,
			}
		}),
	}, nil
}

func (s *Service) searchRepositoryBranchMentions(ctx context.Context, opt SearchMentionsOptions) (*entity.Mention, error) {
	repositoryID, err := s.resolveRepositoryID(ctx, opt)
	if err != nil {
		return nil, err
	}

	// next id 就是 offset
	// 转换 offset 和 limit 到 page size 和 page number
	pageSize, pageNumber, err := s.translatePageSizeAndNumber(pointer.Get(opt.NextID), pointer.Get(opt.Limit))
	if err != nil {
		return nil, errors.WithMessage(err, "failed translate page size and number")
	}
	branches, err := s.nextCodeClient.ListBranches(ctx, vcs.ListBranchesRequest{
		RepoId:     repositoryID,
		Type:       lo.ToPtr(vcs.BranchTypeAll),
		Query:      opt.Query,
		PageNumber: pageNumber,
		PageSize:   pageSize,
	}, s.withUserJwtOpts(opt.User)...)
	if err != nil {
		return nil, errors.WithMessage(err, "failed list branches")
	}

	var hasMore *bool
	var nextID *string
	if len(branches.Branches) < int(pageSize) {
		hasMore = lo.ToPtr(false)
	} else {
		hasMore = lo.ToPtr(true)
		nextID = lo.ToPtr(strconv.FormatInt(int64(pageSize*pageNumber), 10))
	}

	return &entity.Mention{
		Type:    entity.MentionTypeRepositoryBranch,
		NextID:  nextID,
		HasMore: hasMore,
		Branches: lo.Map(branches.Branches, func(item *vcs.Branch, index int) *entity.Branch {
			return &entity.Branch{
				Name:      item.Name,
				CommitSha: item.GetCommit().GetId(),
			}
		}),
	}, nil
}

func (s *Service) searchRepositoryFileOrDir(ctx context.Context, opt SearchMentionsOptions) (*entity.Mention, error) {
	repositoryID, err := s.resolveRepositoryID(ctx, opt)
	if err != nil {
		return nil, err
	}
	revision := pointer.Get(opt.RepositoryBranch)
	if revision == "" {
		defaultBranch, err := s.nextCodeClient.GetDefaultBranch(ctx, vcs.GetDefaultBranchRequest{
			RepoId: repositoryID,
		}, s.withUserJwtOpts(opt.User)...)
		if err != nil {
			return nil, errors.WithMessage(err, "failed get default branch")
		}
		revision = defaultBranch.GetDefaultBranch().Name
	}

	files, err := s.nextCodeClient.ListFilePaths(ctx, vcs.ListFilePathsRequest{
		RepoId:   repositoryID,
		Revision: revision,
		Query:    pointer.Get(opt.Query),
	}, s.withUserJwtOpts(opt.User)...)
	if err != nil {
		return nil, errors.WithMessage(err, "failed list file paths")
	}

	paths := files.Paths
	if opt.Type == entity.MentionTypeRepositoryDir {
		paths = s.getAllDirs(paths)
	}

	limit := int(pointer.Get(opt.Limit))
	if limit <= 0 {
		limit = 10
	}

	hasMore := lo.ToPtr(false)
	var nextID *string
	var result []string
	filter := true
	if pointer.Get(opt.NextID) == "" {
		filter = false
	}
	dirQuery := pointer.Get(opt.Query)
	for _, path := range paths {
		if opt.Type == entity.MentionTypeRepositoryDir && dirQuery != "" && !strings.Contains(path, dirQuery) {
			continue
		}
		if len(result) >= limit {
			hasMore = lo.ToPtr(true)
			nextID = lo.ToPtr(result[len(result)-1])
			break
		}
		if filter {
			if pointer.Get(opt.NextID) == path {
				filter = false
			}
			continue
		}
		result = append(result, path)
	}

	var resultFiles []*entity.File
	var resultDirs []*entity.Dir
	for _, path := range result {
		if opt.Type == entity.MentionTypeRepositoryDir {
			resultDirs = append(resultDirs, &entity.Dir{
				Path: path,
			})
		} else {
			resultFiles = append(resultFiles, &entity.File{
				Path: path,
			})
		}
	}

	return &entity.Mention{
		Type:    opt.Type,
		NextID:  nextID,
		HasMore: hasMore,
		Files:   resultFiles,
		Dirs:    resultDirs,
	}, nil
}

func (s *Service) getAllDirs(paths []string) []string {
	dirSet := make(map[string]struct{})
	for _, p := range paths {
		// 先获取文件的目录部分
		dir := filepath.Dir(p)
		// 递归往上取所有父目录，直到根目录
		for {
			if dir == "." || dir == "/" || dir == "" {
				// 根目录或无效目录，停止
				break
			}
			dirSet[dir] = struct{}{}
			// 取上一级目录
			parent := filepath.Dir(dir)
			if parent == dir {
				// 防止死循环
				break
			}
			dir = parent
		}
	}
	// 转成切片
	dirs := make([]string, 0, len(dirSet))
	for d := range dirSet {
		dirs = append(dirs, d)
	}
	// 按字典序排序
	sort.Strings(dirs)
	return dirs
}

func (s *Service) searchKnowledgeBase(ctx context.Context, opt SearchMentionsOptions) (*entity.Mention, error) {
	dataset, err := s.knowledgeBaseService.GetDatasetBySpaceID(ctx, lo.FromPtr(opt.SpaceID))
	if err != nil {
		return nil, errors.WithMessage(err, "failed get knowledge base dataset")
	}
	offsetInt := conv.IntDefault(lo.FromPtr(opt.NextID), 0)
	limit := int(pointer.Get(opt.Limit))
	documents, total, err := s.knowledgeBaseService.ListDocuments(ctx, dataset.ID, &knowledgebase.ListDocumentsOption{
		Query:       opt.Query,
		Offset:      offsetInt,
		Limit:       limit,
		DescOrderBy: lo.ToPtr(knowledgebase.DescOrderByHeat),
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed list documents")
	}
	hasMore := lo.Ternary(total > int64(offsetInt+limit), lo.ToPtr(true), lo.ToPtr(false))
	return &entity.Mention{
		Type:                   entity.MentionTypeKnowledgeBase,
		NextID:                 lo.ToPtr(strconv.Itoa(offsetInt + limit)),
		HasMore:                hasMore,
		KnowledgeBaseDocuments: documents,
	}, nil
}

func (s *Service) resolveRepositoryID(ctx context.Context, opt SearchMentionsOptions) (string, error) {
	strRepositoryID := pointer.Get(opt.RepositoryID)
	if strRepositoryID == "" {
		return "", ErrMustHaveRepositoryID
	}

	_, err := strconv.ParseInt(strRepositoryID, 10, 64)
	if err != nil {
		// 通过 path 获取
		path, err := url.PathUnescape(strRepositoryID)
		if err != nil {
			return "", err
		}
		repo, nerr := s.nextCodeClient.GetRepository(ctx, repository.GetRepositoryRequest{
			Path: &path,
		})
		if nerr != nil {
			return "", nerr
		}
		if repo == nil || repo.Repository == nil {
			return "", errors.New("failed get repository")
		}
		return repo.Repository.Id, nerr
	}

	return strRepositoryID, nil
}
