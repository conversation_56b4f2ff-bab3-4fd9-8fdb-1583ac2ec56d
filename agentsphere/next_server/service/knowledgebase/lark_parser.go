package knowledgebase

import (
	"context"
	"encoding/json"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"

	"code.byted.org/gopkg/logs"
	strip "github.com/grokify/html-strip-tags-go"
	larkdocx "github.com/larksuite/oapi-sdk-go/v3/service/docx/v1"
	larksheets "github.com/larksuite/oapi-sdk-go/v3/service/sheets/v3"
	"github.com/olekukonko/tablewriter"
	"github.com/pkg/errors"
	"github.com/samber/lo"

	"code.byted.org/devgpt/kiwis/devai/data/util"
	"code.byted.org/devgpt/kiwis/port/lark"
	larkbitable "github.com/larksuite/oapi-sdk-go/v3/service/bitable/v1"
	"github.com/spf13/cast"
)

type LarkParser struct {
	config          *Config
	ImgTokens       []string
	blockMap        map[string]*larkdocx.Block
	larkCli         lark.Client
	larkOptions     []lark.Option
	userOpenIDRegex *regexp.Regexp
}

type Config struct {
	enableImage      bool
	enableUserOpenID bool
	larkOptions      []lark.Option
}

type OptionFunc func(config *Config)

// WithEnableImage 是否解析图片，默认不解析
func WithEnableImage(enable bool) OptionFunc {
	return func(config *Config) {
		config.enableImage = enable
	}
}

// WithEnableUserOpenID 是否将用户 Open ID 解析为用户邮箱，默认不解析
func WithEnableUserOpenID(enable bool) OptionFunc {
	return func(config *Config) {
		config.enableUserOpenID = enable
	}
}

func WithLarkOptions(options []lark.Option) OptionFunc {
	return func(config *Config) {
		config.larkOptions = options
	}
}

const userOpenIDPattern = `ou_[a-f0-9]{32}`

func NewLarkParser(larkCli lark.Client, options ...OptionFunc) *LarkParser {
	config := &Config{
		enableImage:      false,
		enableUserOpenID: false,
	}

	for _, option := range options {
		option(config)
	}

	return &LarkParser{
		config:          config,
		ImgTokens:       make([]string, 0),
		blockMap:        make(map[string]*larkdocx.Block),
		larkCli:         larkCli,
		userOpenIDRegex: regexp.MustCompile(userOpenIDPattern),
		larkOptions:     config.larkOptions,
	}
}

// ParseDocxContent parse new lark document(docType docx)
func (p *LarkParser) ParseDocxContent(ctx context.Context, documentID string, blocks []*larkdocx.Block) string {
	for _, block := range blocks {
		p.blockMap[*block.BlockId] = block
	}

	entryBlock := p.blockMap[documentID]
	return p.ParseUserOpenID(ctx, p.ParseDocxBlock(entryBlock, 0))
}

func (p *LarkParser) ParseUserOpenID(ctx context.Context, content string) string {
	if !p.config.enableUserOpenID || p.larkCli == nil || len(content) == 0 {
		return content
	}
	// 匹配飞书用户 open id 的正则表达式
	openIDs := p.userOpenIDRegex.FindAllString(content, -1)
	if len(openIDs) == 0 {
		return content
	}
	users, err := p.larkCli.ListLarkUsers(ctx, lo.Uniq(openIDs), "")
	if err != nil {
		logs.CtxWarn(ctx, "failed to list lark users, err: %v", err)
		return content
	}
	usersMap := make(map[string]string, len(users))
	for _, user := range users {
		if user.OpenId != nil {
			usersMap[*user.OpenId] = *user.Email
		} else {
			logs.CtxWarn(ctx, "open id is nil, user: %v", user)
		}
	}
	// 将 open id 替换为 @用户邮箱前缀 的形式
	for openID, email := range usersMap {
		r := strings.Split(email, "@")
		if len(r) == 0 || r[0] == "" {
			continue
		}
		content = strings.ReplaceAll(content, openID, fmt.Sprintf("@%s", r[0]))
	}
	return content
}

func (p *LarkParser) ParseDocxBlock(b *larkdocx.Block, indentLevel int) string {
	if b == nil {
		return ""
	}
	buf := new(strings.Builder)
	buf.WriteString(strings.Repeat("\t", indentLevel))
	blockType := *b.BlockType
	switch blockType {
	case lark.DocxBlockTypePage:
		buf.WriteString(p.ParseDocxBlockContainer(b))
	case lark.DocxBlockTypeText:
		buf.WriteString(p.ParseDocxBlockText(b.Text))
		buf.WriteString(p.ParseDocxBlockContainer(b))
	case lark.DocxBlockTypeHeading1:
		buf.WriteString("# ")
		buf.WriteString(p.ParseDocxBlockText(b.Heading1))
		buf.WriteString(p.ParseDocxBlockContainer(b))
	case lark.DocxBlockTypeHeading2:
		buf.WriteString("## ")
		buf.WriteString(p.ParseDocxBlockText(b.Heading2))
		buf.WriteString(p.ParseDocxBlockContainer(b))
	case lark.DocxBlockTypeHeading3:
		buf.WriteString("### ")
		buf.WriteString(p.ParseDocxBlockText(b.Heading3))
		buf.WriteString(p.ParseDocxBlockContainer(b))
	case lark.DocxBlockTypeHeading4:
		buf.WriteString("#### ")
		buf.WriteString(p.ParseDocxBlockText(b.Heading4))
		buf.WriteString(p.ParseDocxBlockContainer(b))
	case lark.DocxBlockTypeHeading5:
		buf.WriteString("##### ")
		buf.WriteString(p.ParseDocxBlockText(b.Heading5))
		buf.WriteString(p.ParseDocxBlockContainer(b))
	case lark.DocxBlockTypeHeading6:
		buf.WriteString("###### ")
		buf.WriteString(p.ParseDocxBlockText(b.Heading6))
		buf.WriteString(p.ParseDocxBlockContainer(b))
	case lark.DocxBlockTypeHeading7:
		buf.WriteString("####### ")
		buf.WriteString(p.ParseDocxBlockText(b.Heading7))
		buf.WriteString(p.ParseDocxBlockContainer(b))
	case lark.DocxBlockTypeHeading8:
		buf.WriteString("######## ")
		buf.WriteString(p.ParseDocxBlockText(b.Heading8))
		buf.WriteString(p.ParseDocxBlockContainer(b))
	case lark.DocxBlockTypeHeading9:
		buf.WriteString("######### ")
		buf.WriteString(p.ParseDocxBlockText(b.Heading9))
		buf.WriteString(p.ParseDocxBlockContainer(b))
	case lark.DocxBlockTypeBullet:
		buf.WriteString(p.ParseDocxBlockBullet(b, indentLevel))
	case lark.DocxBlockTypeOrdered:
		buf.WriteString(p.ParseDocxBlockOrdered(b, indentLevel))
	case lark.DocxBlockTypeCode:
		if b.Code.Style.Language != nil {
			buf.WriteString("```" + lark.DocxCodeLang2MdStr[lark.DocxCodeLanguage(*b.Code.Style.Language)] + "\n")
		} else {
			buf.WriteString("```\n")
		}
		buf.WriteString(strings.TrimSpace(p.ParseDocxBlockText(b.Code)))
		buf.WriteString("\n```\n")
	case lark.DocxBlockTypeQuote:
		buf.WriteString("> ")
		buf.WriteString(p.ParseDocxBlockText(b.Quote))
	case lark.DocxBlockTypeEquation:
		buf.WriteString("$$\n")
		buf.WriteString(p.ParseDocxBlockText(b.Equation))
		buf.WriteString("\n$$\n")
	case lark.DocxBlockTypeTodo:
		if *b.Todo.Style.Done {
			buf.WriteString("- [x] ")
		} else {
			buf.WriteString("- [ ] ")
		}
		buf.WriteString(p.ParseDocxBlockText(b.Todo))
	case lark.DocxBlockTypeDivider:
		buf.WriteString("---\n")
	// TODO 如果是 image 类型的，就不写入
	case lark.DocxBlockTypeImage:
		if p.config.enableImage {
			buf.WriteString(p.ParseDocxBlockImage(b.Image))
		}
	case lark.DocxBlockTypeTableCell:
		buf.WriteString(p.ParseDocxBlockTableCell(b))
	case lark.DocxBlockTypeTable:
		//buf.WriteString(p.ParseDocxBlockTable(b.Table))
		buf.WriteString(p.ParseDocxBlockTableV2(b))
	case lark.DocxBlockTypeQuoteContainer:
		buf.WriteString(p.ParseDocxBlockQuoteContainer(b))
	case lark.DocxBlockTypeCallout:
		buf.WriteString(p.ParseDocxBlockCallout(b))
	case lark.DocxBlockTypeUndefined:
		buf.WriteString(p.ParseDocxBlockContainer(b))
	default:
	}
	return buf.String()
}

func (p *LarkParser) ParseDocxBlockContainer(b *larkdocx.Block) string {
	if b == nil {
		return ""
	}
	buf := new(strings.Builder)

	for _, childID := range b.Children {
		childBlock := p.blockMap[childID]
		buf.WriteString(p.ParseDocxBlock(childBlock, 0))
		buf.WriteString("\n")
	}

	return buf.String()
}

func (p *LarkParser) ParseDocxBlockText(b *larkdocx.Text) string {
	if b == nil {
		return ""
	}
	buf := new(strings.Builder)
	numElem := len(b.Elements)
	for _, e := range b.Elements {
		inline := numElem > 1
		buf.WriteString(p.ParseDocxTextElement(e, inline))
	}
	buf.WriteString("\n")
	return buf.String()
}

func (p *LarkParser) ParseDocxTextElement(e *larkdocx.TextElement, inline bool) string {
	if e == nil {
		return ""
	}
	buf := new(strings.Builder)
	if e.TextRun != nil {
		buf.WriteString(p.ParseDocxTextElementTextRun(e.TextRun))
	}
	if e.MentionUser != nil {
		buf.WriteString(*e.MentionUser.UserId)
	}
	if e.MentionDoc != nil && e.MentionDoc.Title != nil && e.MentionDoc.Url != nil {
		buf.WriteString(
			fmt.Sprintf("[%s](%s)", *e.MentionDoc.Title, util.UnescapeURL(*e.MentionDoc.Url)))
	}
	if e.Equation != nil && e.Equation.Content != nil {
		symbol := "$$"
		if inline {
			symbol = "$"
		}
		buf.WriteString(symbol + strings.TrimSuffix(*e.Equation.Content, "\n") + symbol)
	}
	return buf.String()
}

func (p *LarkParser) ParseDocxTextElementTextRun(tr *larkdocx.TextRun) string {
	if tr == nil {
		return ""
	}
	buf := new(strings.Builder)
	postWrite := ""
	if style := tr.TextElementStyle; style != nil {
		if *style.Bold {
			buf.WriteString("**")
			postWrite = "**"
		} else if *style.Italic {
			buf.WriteString("***")
			postWrite = "***"
		} else if *style.Strikethrough {
			buf.WriteString("~~")
			postWrite = "~~"
		} else if *style.Underline {
			buf.WriteString("<u>")
			postWrite = "</u>"
		} else if *style.InlineCode {
			buf.WriteString("`")
			postWrite = "`"
		} else if link := style.Link; link != nil {
			buf.WriteString("[")
			postWrite = fmt.Sprintf("](%s)", util.UnescapeURL(*link.Url))
		}
	}
	buf.WriteString(*tr.Content)
	buf.WriteString(postWrite)
	return buf.String()
}

func (p *LarkParser) ParseDocxBlockImage(img *larkdocx.Image) string {
	if img == nil {
		return ""
	}
	buf := new(strings.Builder)
	buf.WriteString(fmt.Sprintf("![图片](%s)", *img.Token))
	buf.WriteString("\n")
	p.ImgTokens = append(p.ImgTokens, *img.Token)
	return buf.String()
}

func (p *LarkParser) ParseDocxBlockBullet(b *larkdocx.Block, indentLevel int) string {
	if b == nil {
		return ""
	}
	buf := new(strings.Builder)

	buf.WriteString("- ")
	buf.WriteString(p.ParseDocxBlockText(b.Bullet))

	for _, childID := range b.Children {
		childBlock := p.blockMap[childID]
		buf.WriteString(p.ParseDocxBlock(childBlock, indentLevel+1))
	}

	return buf.String()
}

func (p *LarkParser) ParseDocxBlockOrdered(b *larkdocx.Block, indentLevel int) string {
	if b == nil {
		return ""
	}
	buf := new(strings.Builder)

	// calculate order and indent level
	parent := p.blockMap[*b.ParentId]
	order := 1
	for idx, child := range parent.Children {
		if child == *b.BlockId {
			for i := idx - 1; i >= 0; i-- {
				bType := p.blockMap[parent.Children[i]].BlockType
				if *bType == lark.DocxBlockTypeOrdered {
					order += 1
				} else {
					break
				}
			}
			break
		}
	}

	buf.WriteString(fmt.Sprintf("%d. ", order))
	buf.WriteString(p.ParseDocxBlockText(b.Ordered))

	for _, childID := range b.Children {
		childBlock := p.blockMap[childID]
		buf.WriteString(p.ParseDocxBlock(childBlock, indentLevel+1))
	}

	return buf.String()
}

func (p *LarkParser) ParseDocxBlockTableCell(b *larkdocx.Block) string {
	if b == nil {
		return ""
	}
	buf := new(strings.Builder)

	for _, child := range b.Children {
		block := p.blockMap[child]
		content := p.ParseDocxBlock(block, 0)
		buf.WriteString(content)
	}

	return buf.String()
}

func (p *LarkParser) ParseDocxBlockTable(t *larkdocx.Table) string {
	if t == nil {
		return ""
	}
	// - First row as header
	// - Ignore cell merging
	var rows [][]string
	for i, blockID := range t.Cells {
		block := p.blockMap[blockID]
		cellContent := p.ParseDocxBlock(block, 0)
		cellContent = strings.ReplaceAll(cellContent, "\n", "")
		rowIndex := i / *t.Property.ColumnSize
		if len(rows) < rowIndex+1 {
			rows = append(rows, []string{})
		}
		rows[rowIndex] = append(rows[rowIndex], cellContent)
	}

	buf := new(strings.Builder)
	buf.WriteString(renderMarkdownTable(rows))
	buf.WriteString("\n")
	return buf.String()
}

func renderMarkdownTable(data [][]string) string {
	builder := &strings.Builder{}
	table := tablewriter.NewWriter(builder)
	table.SetCenterSeparator("|")
	table.SetAutoWrapText(false)
	table.SetAutoFormatHeaders(false)
	table.SetAutoMergeCells(false)
	table.SetBorders(tablewriter.Border{Left: true, Top: false, Right: true, Bottom: false})
	table.SetHeader(data[0])
	table.AppendBulk(data[1:])
	table.Render()
	return builder.String()
}

func (p *LarkParser) cleanBlockTable(table [][]string) [][]string {
	var tableNew [][]string

	for _, innerList := range table {
		emptyFlag := true         // Use this flag to check if the row is empty
		var innerListNew []string // This will store the new cleaned row
		for _, e := range innerList {
			eNew := e
			if eNew == "" {
				eNew = "" // In Go, there's no need to explicitly convert nil to "", but we need to maintain logic.
			} else {
				emptyFlag = false // The row is not empty since we have found a non-empty cell
			}
			innerListNew = append(innerListNew, eNew) // Add the cleaned cell to the new row
		}
		if !emptyFlag {
			// If the row is not empty, add it to the new table
			tableNew = append(tableNew, innerListNew)
		}
	}
	return tableNew
}

func (p *LarkParser) deduplicateBlockTable(table [][]string) [][]string {
	var tableNew [][]string
	existValueMap := make(map[string]bool)

	for _, colData := range table {
		colDataStr := strings.Join(colData, "|")

		if strings.TrimSpace(colDataStr) == "" { // Skip empty rows
			continue
		}
		if _, exists := existValueMap[colDataStr]; exists { // Skip duplicate rows
			continue
		}
		tableNew = append(tableNew, colData)
		existValueMap[colDataStr] = true
	}
	return tableNew
}

func (p *LarkParser) splitBlockTable(table [][]string) string {
	table = p.deduplicateBlockTable(table)
	res := ""

	if len(table) == 0 {
		return res
	} else if len(table) == 1 {
		res = strings.Join(table[0], " ")
		return res
	}

	tableHead := table[0]
	for i := 1; i < len(table); i++ {
		colData := table[i]
		var colTextList []string
		for j, colValue := range colData {
			colValue = strings.TrimSpace(colValue)
			if colValue == "" || colValue == "-" {
				continue
			}
			colKey := strings.TrimSpace(tableHead[j])
			joinValue := colValue
			if colKey != "" && colKey != "-" {
				joinValue = colKey + "：" + colValue
			}

			if len(colTextList) == 0 || (len(colTextList) > 0 && joinValue != colTextList[len(colTextList)-1]) {
				colTextList = append(colTextList, joinValue)
			}
		}

		colText := strings.Join(colTextList, "；") + "。"
		res += colText + "\n"
	}

	res = strings.TrimSpace(res)
	return res
}

func (p *LarkParser) ParseDocxBlockTableV2(b *larkdocx.Block) string {
	if b == nil {
		return ""
	}
	// - First row as header
	// - Ignore cell merging
	var ColumnSize = *b.Table.Property.ColumnSize
	var RowSize = *b.Table.Property.RowSize

	// 回溯合并单元格
	tableMerge := make(map[string][2]int)
	for index, cell := range b.Table.Property.MergeInfo {
		rowIndex := index / ColumnSize
		columnIndex := index % ColumnSize
		realCell := [2]int{rowIndex, columnIndex}

		for rowIndex := 0; rowIndex < *cell.RowSpan; rowIndex++ {
			for colIndex := 0; colIndex < *cell.ColSpan; colIndex++ {
				if rowIndex == 0 && colIndex == 0 {
					continue
				}
				mergeKey := fmt.Sprintf("%d_%d", rowIndex+rowIndex, columnIndex+colIndex)
				tableMerge[mergeKey] = realCell
			}
		}

	}

	table := make([][]string, RowSize)
	for i := range table {
		table[i] = make([]string, ColumnSize)
	}
	index := 0
	for _, blockID := range b.Children {
		rowIndex := index / ColumnSize
		columnIndex := index % ColumnSize
		//tableText := strings.TrimSpace(parserDocxCell(c_id))

		block := p.blockMap[blockID]
		cellContent := p.ParseDocxBlock(block, 0)
		cellContent = strings.ReplaceAll(cellContent, "\n", "")
		table[rowIndex][columnIndex] = cellContent
		if cellContent == "" { // 解决单元格合并问题
			mergeKey := fmt.Sprintf("%d_%d", rowIndex, columnIndex)
			if realCell, exists := tableMerge[mergeKey]; exists {
				realRowIndex, realColumnIndex := realCell[0], realCell[1]
				table[rowIndex][columnIndex] = table[realRowIndex][realColumnIndex]
			}
		}
		index++
	}

	reNewline := regexp.MustCompile(`\n+`)
	for i, innerList := range table {
		for j, e := range innerList {
			if e == "" {
				continue
			}
			e = reNewline.ReplaceAllString(e, "\n")
			e = strings.TrimSpace(e)
			e = strings.Replace(e, "\n", " ", -1)
			table[i][j] = e
		}
	}

	table = p.cleanBlockTable(table)

	tableString := p.splitBlockTable(table)

	buf := new(strings.Builder)
	buf.WriteString(tableString)
	buf.WriteString("\n")
	return buf.String()
}

func (p *LarkParser) ParseDocxBlockQuoteContainer(b *larkdocx.Block) string {
	if b == nil {
		return ""
	}
	buf := new(strings.Builder)

	for _, child := range b.Children {
		block := p.blockMap[child]
		buf.WriteString("> ")
		buf.WriteString(p.ParseDocxBlock(block, 0))
	}

	return buf.String()
}

// ParseDocxBlockCallout 解析高亮块为引用
func (p *LarkParser) ParseDocxBlockCallout(b *larkdocx.Block) string {
	if b == nil {
		return ""
	}
	buf := new(strings.Builder)

	buf.WriteString("> ")

	for _, childID := range b.Children {
		childBlock := p.blockMap[childID]
		buf.WriteString(p.ParseDocxBlock(childBlock, 0))
		buf.WriteString("\n")
	}

	return buf.String()
}

/**
old doc parser
*/

// =============================================================
// Parse the old version of document (docs)
// =============================================================

func (p *LarkParser) ParseDocContent(ctx context.Context, docs *lark.DocContent) string {
	if docs == nil {
		return ""
	}
	buf := new(strings.Builder)
	buf.WriteString(p.ParseDocBody(docs.Body))
	return p.ParseUserOpenID(ctx, buf.String())
}

func (p *LarkParser) ParseDocParagraph(para *lark.DocParagraph) string {
	if para == nil {
		return ""
	}
	buf := new(strings.Builder)
	postWrite := ""
	if style := para.Style; style != nil {
		if style.HeadingLevel > 0 {
			buf.WriteString(strings.Repeat("#", int(style.HeadingLevel)))
			buf.WriteString(" ")
		} else if list := style.List; list != nil {
			indentLevel := 0
			if list.IndentLevel > 1 {
				indentLevel = list.IndentLevel - 1
			}
			buf.WriteString(strings.Repeat("  ", indentLevel))
			switch list.Type {
			case "number":
				buf.WriteString(strconv.Itoa(list.Number) + ".")
			case "bullet":
				buf.WriteString("-")
			case "checkBox":
				buf.WriteString("- [ ]")
			case "checkedBox":
				buf.WriteString("- [x]")
			}
			buf.WriteString(" ")
		} else if style.Quote {
			buf.WriteString("> ")
		} else {
			switch style.Align {
			case "right":
			case "center":
				buf.WriteString(fmt.Sprintf("<div style=\"text-align: %s\">", style.Align))
				postWrite += "</div>"
			default:
			}
		}
	}
	for _, e := range para.Elements {
		buf.WriteString(p.ParseDocParagraphElement(e))
	}
	buf.WriteString(postWrite)
	buf.WriteString("\n")
	return buf.String()
}

func (p *LarkParser) ParseDocBody(body *lark.DocBody) string {
	if body == nil {
		return ""
	}

	buf := new(strings.Builder)
	for _, b := range body.Blocks {
		buf.WriteString(p.ParseDocBlock(b))
		buf.WriteString("\n")
	}
	return buf.String()
}

func (p *LarkParser) ParseDocBlock(b *lark.DocBlock) string {
	if b == nil {
		return ""
	}
	switch b.Type {
	case lark.DocBlockTypeParagraph:
		return p.ParseDocParagraph(b.Paragraph)
	case lark.DocBlockTypeGallery:
		return p.ParseDocGallery(b.Gallery)
	case lark.DocBlockTypeCode:
		return p.ParseDocCode(b.Code)
	case lark.DocBlockTypeTable:
		return p.ParseDocTableV2(b)
	default:
		return ""
	}
}

func (p *LarkParser) ParseDocTableV2(b *lark.DocBlock) string {
	if b == nil || b.Table == nil {
		return ""
	}
	ColumnSize := b.Table.ColumnSize
	RowSize := b.Table.RowSize
	tableMerge := make(map[string][2]int)
	for _, cell := range b.Table.MergedCells {
		realCell := [2]int{cell.RowStartIndex, cell.ColumnStartIndex}
		for rowI := 0; rowI < cell.RowEndIndex; rowI++ {
			for colI := 0; colI < cell.ColumnEndIndex; colI++ {
				mergeKey := fmt.Sprintf("%d_%d", rowI, colI)
				tableMerge[mergeKey] = realCell
			}
		}
	}
	table := make([][]string, RowSize)
	for i := range table {
		table[i] = make([]string, ColumnSize)
	}
	rowIndex := 0
	for _, row := range b.Table.TableRows {
		columnIndex := 0
		for _, cell := range row.TableCells {
			tableText := ""
			bytes, err := json.Marshal(cell.Body)
			if err != nil {
				logs.Error("failed to marshal %v, err: %s\n", cell.Body, err)
				return ""
			}
			var body lark.DocBody
			err = json.Unmarshal(bytes, &body)
			if err != nil {
				logs.Error("failed to unmarshal '%s', err: %s\n", string(bytes), err)
				return ""
			}
			for _, block := range body.Blocks {
				if block.Type != "paragraph" {
					continue
				}
				if block.Paragraph == nil {
					continue
				}
				r := block.Paragraph.Elements
				for _, element := range r {
					tableText += p.ParseDocParagraphElement(element)
				}
			}
			table[rowIndex][columnIndex] = tableText
			if tableText == "" {
				mergeKey := fmt.Sprintf("%d_%d", rowIndex, columnIndex)
				val, exist := tableMerge[mergeKey]
				if exist {
					table[rowIndex][columnIndex] = table[val[0]][val[1]]
				}
			}
			columnIndex += 1
		}
		rowIndex += 1
	}
	table = p.cleanBlockTable(table)
	tableString := p.splitBlockTable(table)
	buf := new(strings.Builder)
	buf.WriteString(tableString)
	buf.WriteString("\n")
	return buf.String()
}

func (p *LarkParser) ParseDocParagraphElement(e *lark.DocParagraphElement) string {
	if e == nil {
		return ""
	}
	switch e.Type {
	case lark.DocParagraphElementTypeTextRun:
		return p.ParseDocTextRun(e.TextRun)
	case lark.DocParagraphElementTypeDocsLink:
		return p.ParseDocDocsLink(e.DocsLink)
	case lark.DocParagraphElementTypeEquation:
		return p.ParseDocEquation(e.Equation)
	default:
		return ""
	}
}

func (p *LarkParser) ParseDocTextRun(tr *lark.DocTextRun) string {
	if tr == nil {
		return ""
	}
	buf := new(strings.Builder)
	postWrite := ""
	if style := tr.Style; style != nil {
		if style.Bold {
			buf.WriteString("<strong>")
			postWrite = "</strong>"
		} else if style.Italic {
			buf.WriteString("<em>")
			postWrite = "</em>"
		} else if style.StrikeThrough {
			buf.WriteString("<del>")
			postWrite = "</del>"
		} else if style.Underline {
			buf.WriteString("<u>")
			postWrite = "</u>"
		} else if style.CodeInline {
			buf.WriteString("`")
			postWrite = "`"
		} else if link := style.Link; link != nil {
			buf.WriteString("[")
			postWrite = fmt.Sprintf("](%s)", util.UnescapeURL(link.URL))
		}
	}
	buf.WriteString(tr.Text)
	buf.WriteString(postWrite)
	return buf.String()
}

func (p *LarkParser) ParseDocDocsLink(l *lark.DocDocsLink) string {
	if l == nil {
		return ""
	}
	buf := new(strings.Builder)
	buf.WriteString(fmt.Sprintf("[](%s)", util.UnescapeURL(l.URL)))
	return buf.String()
}

func (p *LarkParser) ParseDocEquation(eq *lark.DocEquation) string {
	if eq == nil {
		return ""
	}
	buf := new(strings.Builder)
	buf.WriteString("$$" + eq.Equation + "$$")
	return buf.String()
}

func (p *LarkParser) ParseDocGallery(g *lark.DocGallery) string {
	if g == nil {
		return ""
	}
	buf := new(strings.Builder)
	for _, img := range g.ImageList {
		buf.WriteString(p.ParseDocImageItem(img))
	}
	return buf.String()
}

func (p *LarkParser) ParseDocImageItem(img *lark.DocImageItem) string {
	if img == nil {
		return ""
	}
	buf := new(strings.Builder)
	buf.WriteString(fmt.Sprintf("![](%s)", img.FileToken))
	buf.WriteString("\n")
	p.ImgTokens = append(p.ImgTokens, img.FileToken)
	return buf.String()
}

func (p *LarkParser) ParseDocCode(c *lark.DocCode) string {
	if c == nil {
		return ""
	}
	buf := new(strings.Builder)
	buf.WriteString("```")
	buf.WriteString(strings.ToLower(c.Language))
	buf.WriteString("\n")

	if c.Body != nil && c.Body.Blocks != nil {
		for _, b := range c.Body.Blocks {
			for _, elem := range b.Paragraph.Elements {
				switch elem.Type {
				case lark.DocParagraphElementTypeTextRun:
					buf.WriteString(elem.TextRun.Text)
				case lark.DocParagraphElementTypeDocsLink:
					buf.WriteString(elem.DocsLink.URL)
				}
			}
			buf.WriteString("\n")
		}
	}

	buf.WriteString("```\n")
	return buf.String()
}

func (p *LarkParser) ParseDocTableCell(cell *lark.DocTableCell) string {
	if cell == nil {
		return ""
	}
	// DocTableCell {
	//     "columnIndex": int,
	//     "zoneId": string,
	//     "body": {object(Body)}
	// }
	// convert body(interface{}) to DocBody
	bytes, err := json.Marshal(cell.Body)
	if err != nil {
		logs.Error("failed to marshal %v, err: %s\n", cell.Body, err)
		return ""
	}
	var body lark.DocBody
	err = json.Unmarshal(bytes, &body)
	if err != nil {
		logs.Error("failed to unmarshal '%s', err: %s\n", string(bytes), err)
		return ""
	}

	// flatten contents to one line
	var contents string
	for _, block := range body.Blocks {
		content := p.ParseDocBlock(block)
		if content == "" {
			continue
		}
		contents += content
	}
	contents = strings.Join(strings.Fields(strings.ReplaceAll(strings.TrimSpace(strip.StripTags(contents)), "\n",
		"<br/>")), " ")
	return contents
}

func (p *LarkParser) ParseDocTable(t *lark.DocTable) string {
	if t == nil {
		return ""
	}
	// - First row as header
	// - Ignore cell merging
	var rows [][]string
	for _, row := range t.TableRows {
		var cells []string
		for _, cell := range row.TableCells {
			cells = append(cells, p.ParseDocTableCell(cell))
		}
		rows = append(rows, cells)
	}

	buf := new(strings.Builder)
	buf.WriteString("\n")
	buf.WriteString(renderMarkdownTable(rows))
	buf.WriteString("\n")
	return buf.String()
}

func (p *LarkParser) formatBitableRow(fields []*larkbitable.AppTableFieldForList, values map[string]interface{}) (string, error) {
	buf := new(strings.Builder)
	for _, field := range fields {
		fieldName := lo.FromPtr(field.FieldName)
		v, ok := values[fieldName]
		if !ok {
			continue
		}
		buf.WriteString(fmt.Sprintf("%s: %v ", fieldName, p.formatBitableItem(field, v)))
	}
	return buf.String(), nil
}

func (p *LarkParser) ParseSheet(ctx context.Context, sheetMeta *larksheets.QuerySpreadsheetSheetRespData, sheetData *lark.SheetData) (string, error) {
	if len(sheetMeta.Sheets) != len(sheetData.ValueRanges) {
		return "", errors.New("invalid sheets length")
	}
	buf := new(strings.Builder)
	for idx, sheet := range sheetMeta.Sheets {
		buf.WriteString(fmt.Sprintf("## %s\n", lo.FromPtr(sheet.Title)))
		sheetValue := sheetData.ValueRanges[idx].Values
		if len(sheetValue) == 0 {
			continue
		}
		rowBegin, rowEnd, columnBegin, columnEnd := getDataMinRange(sheetValue)
		logs.CtxInfo(ctx, "rowBegin: %d, rowEnd: %d, columnBegin: %d, columnEnd: %d", rowBegin, rowEnd, columnBegin, columnEnd)
		sheetTitle := sheetValue[rowBegin]
		for i := rowBegin + 1; i <= rowEnd; i++ {
			row := sheetValue[i]
			isRowNull := true
			for _, cell := range row { // 过滤中间行为空的数据
				if cell != nil {
					isRowNull = false
					break
				}
			}
			if isRowNull {
				continue
			}
			for j := columnBegin; j <= columnEnd; j++ {
				rowValue := row[j]
				if rowValue == nil {
					rowValue = ""
				}
				buf.WriteString(fmt.Sprintf("%s: %v ", sheetTitle[j], rowValue))
			}
			buf.WriteString("\n")
		}
	}
	return buf.String(), nil
}

// formatBitableItem format single item from lark bitable to Markdown format.
func (p *LarkParser) formatBitableItem(field *larkbitable.AppTableFieldForList, item interface{}) string {
	// More detail: https://open.larkoffice.com/document/server-docs/docs/bitable-v1/bitable-structure
	switch lo.FromPtr(field.Type) {
	case larkbitable.TypeText:
		buf := new(strings.Builder)
		items := cast.ToSlice(item)
		for _, item := range items {
			item := cast.ToStringMap(item)
			elementType := cast.ToString(item["type"])
			switch elementType {
			case "text":
				buf.WriteString(cast.ToString(item["text"]))
			case "url":
				// Email is url type.
				buf.WriteString(fmt.Sprintf("[%s](%s)", cast.ToString(item["text"]), cast.ToString(item["link"])))
			default:
				// TODO: something wrong here, raise error
				marshal, err := json.Marshal(item)
				if err != nil {
					buf.WriteString(err.Error())
				}
				buf.WriteString(string(marshal))
			}
		}
		return buf.String()
	case larkbitable.TypeUrl:
		item := cast.ToStringMap(item)
		elementType := cast.ToString(item["type"])
		switch elementType {
		case "url":
			return fmt.Sprintf("[%s](%s)", cast.ToString(item["text"]), cast.ToString(item["link"]))
		default:
			// TODO: something wrong here, raise error
			marshal, err := json.Marshal(item)
			if err != nil {
				return err.Error()
			}
			return string(marshal)
		}
	case larkbitable.TypeNumber:
		// TODO: format placed in field.Property.Formatter
		return fmt.Sprintf("%.2f", cast.ToFloat64(item))
	case larkbitable.TypeSingleSelect:
		return cast.ToString(item)
	case larkbitable.TypeMultiSelect:
		selected := cast.ToSlice(item)
		var result string
		for _, s := range selected {
			result += fmt.Sprintf("%s,", s)
		}
		return result
	case larkbitable.TypeDateTime:
		timestamp := int64(cast.ToFloat64(item))
		if timestamp == 0 {
			return ""
		}
		t := time.UnixMicro(timestamp)
		// TODO: format placed in field.Property.DateFormatter.
		return t.Format("2006-01-02")
	case larkbitable.TypeCheckbox:
		return fmt.Sprintf("%t", cast.ToBool(item))
	case larkbitable.TypePhoneNumber:
		return cast.ToString(item)
	case larkbitable.TypeAttachment:
		item := cast.ToStringMap(item)
		// TODO: attachment can be much type
		return cast.ToString("file://" + cast.ToString(item["url"]))
	case larkbitable.TypeFormula:
		item := cast.ToStringMap(item)
		// TODO: formula can be much sub type
		return fmt.Sprintf("%v", item["value"])
	case larkbitable.TypeCreatedUser, larkbitable.TypeModifiedUser, larkbitable.TypeUser:
		item := cast.ToStringMap(item)
		return cast.ToString(item["name"])
	case larkbitable.TypeLocation:
		item := cast.ToStringMap(item)
		return fmt.Sprintf("%s %s %s", cast.ToString(item["address"]), cast.ToString(item["full_address"]), cast.ToString(item["location"]))
	default:
		// TODO: something wrong here, raise error
		marshal, err := json.Marshal(item)
		if err != nil {
			return err.Error()
		}
		return string(marshal)
	}
}

func (p *LarkParser) ParseSheetV2(ctx context.Context, sheet *larksheets.Sheet, sheetData *lark.SheetMarkdownData) (string, error) {
	if len(sheetData.ValueRanges) == 0 {
		return "", nil
	}
	buf := new(strings.Builder)
	buf.WriteString(fmt.Sprintf("## %s\n", lo.FromPtr(sheet.Title)))
	sheetValue := p.ParseSheetValue(ctx, sheetData.ValueRanges[0])
	if sheetValue == nil {
		return "", nil
	}
	rowBegin, rowEnd, columnBegin, columnEnd := getDataMinRange(sheetValue)
	logs.CtxInfo(ctx, "rowBegin: %d, rowEnd: %d, columnBegin: %d, columnEnd: %d", rowBegin, rowEnd, columnBegin, columnEnd)
	sheetTitle := sheetValue[rowBegin]
	for i := rowBegin + 1; i <= rowEnd; i++ {
		row := sheetValue[i]
		isRowNull := true
		for _, cell := range row { // 过滤中间行为空的数据
			if cell != nil && cell != "" {
				isRowNull = false
				break
			}
		}
		if isRowNull {
			continue
		}
		for j := columnBegin; j <= columnEnd; j++ {
			rowValue := row[j]
			if sheetTitle[j] == "" && rowValue == "" {
				continue
			}
			buf.WriteString(fmt.Sprintf("%s: %v ", sheetTitle[j], rowValue))
			if j != columnEnd {
				buf.WriteString(";")
			}
		}
		buf.WriteString("\n")
	}
	return buf.String(), nil
}

func (p *LarkParser) ParseSheetValue(ctx context.Context, values *lark.ValueRange) [][]any {
	if values == nil {
		return nil
	}
	if len(values.Values) == 0 {
		return nil
	}
	ret := make([][]any, 0, len(values.Values))
	for _, row := range values.Values {
		r := make([]any, 0, len(row))
		for _, cell := range row {
			r = append(r, p.ParseSheetCell(ctx, cell))
		}
		ret = append(ret, r)
	}
	return ret
}

func (p *LarkParser) ParseSheetCell(ctx context.Context, cells []lark.SheetCell) string {
	if len(cells) == 0 {
		return ""
	}
	str := strings.Builder{}
	for _, cell := range cells {
		switch cell.Type {
		case lark.SheetCellTypeText:
			str.WriteString(cell.Text.Text)
		case lark.SheetCellTypeLink:
		case lark.SheetCellTypeValue:
			str.WriteString(cell.Value.Value)
		case lark.SheetCellTypeImage:
			str.WriteString(fmt.Sprintf("![图片](%s)", cell.Image.ImageToken))
		case lark.SheetCellTypeFile:
			str.WriteString(fmt.Sprintf("[%s](%s)", cell.File.Name, cell.File.FileToken))
		case lark.SheetCellTypeReminder:
			str.WriteString(cell.Reminder.NotifyDateTime)
			str.WriteString("提醒：")
			users, err := p.larkCli.ListLarkUsers(ctx, lo.Uniq(cell.Reminder.NotifyUserID), "")
			if err != nil {
				logs.CtxWarn(ctx, "failed to list lark users, err: %v", err)
				continue
			}
			usersMap := make(map[string]string, len(users))
			for _, user := range users {
				r := strings.Split(*user.Email, "@")
				if len(r) == 0 || r[0] == "" {
					continue
				}
				if user.OpenId != nil {
					usersMap[*user.OpenId] = r[0]
				} else {
					logs.CtxWarn(ctx, "open id is nil, user: %v", user)
				}
			}
			for _, userID := range cell.Reminder.NotifyUserID {
				if username, ok := usersMap[userID]; ok {
					str.WriteString(fmt.Sprintf("@%s ", username))
				}
			}
			str.WriteString(cell.Reminder.NotifyText)
		case lark.SheetCellTypeFormula:
			str.WriteString(cell.Formula.FormulaValue)
		case lark.SheetCellTypeMentionUser:
			str.WriteString(fmt.Sprintf("@%s ", cell.MentionUser.Name))
		case lark.SheetCellTypeMentionDocument:
			str.WriteString(fmt.Sprintf("[%s](%s)", cell.MentionDocument.Title, cell.MentionDocument.Link))
		case lark.SheetCellTypeDateTime:
			str.WriteString(cell.DateTime.DateTime)
		}
	}
	return str.String()
}

func getDataMinRange(data [][]any) (rowBegin int, rowEnd int, columnBegin int, columnEnd int) {
	rows := len(data)
	if rows == 0 {
		return -1, -1, -1, -1
	}

	cols := len(data[0])
	if cols == 0 {
		return -1, -1, -1, -1
	}

	// 检查单元格是否非空
	isCellNonEmpty := func(row, col int) bool {
		return data[row][col] != nil && data[row][col] != ""
	}

	// 查找第一个满足条件的索引
	findFirst := func(total int, check func(int) bool) int {
		for i := 0; i < total; i++ {
			if check(i) {
				return i
			}
		}
		return -1
	}

	// 查找最后一个满足条件的索引
	findLast := func(start int, check func(int) bool) int {
		for i := start; i >= 0; i-- {
			if check(i) {
				return i
			}
		}
		return -1
	}

	// 查找第一个非空列
	columnBegin = findFirst(cols, func(col int) bool {
		for row := 0; row < rows; row++ {
			if isCellNonEmpty(row, col) {
				return true
			}
		}
		return false
	})

	if columnBegin == -1 {
		return -1, -1, -1, -1
	}

	// 查找最后一个非空列
	columnEnd = findLast(cols-1, func(col int) bool {
		for row := 0; row < rows; row++ {
			if isCellNonEmpty(row, col) {
				return true
			}
		}
		return false
	})

	// 查找第一个非空行
	rowBegin = findFirst(rows, func(row int) bool {
		for col := columnBegin; col <= columnEnd; col++ {
			if isCellNonEmpty(row, col) {
				return true
			}
		}
		return false
	})

	// 查找最后一个非空行
	rowEnd = findLast(rows-1, func(row int) bool {
		for col := columnBegin; col <= columnEnd; col++ {
			if isCellNonEmpty(row, col) {
				return true
			}
		}
		return false
	})

	return
}
