package knowledgebase

import (
	"context"
	"sync"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	"code.byted.org/gopkg/logs/v2"
	"github.com/pkg/errors"
	"github.com/samber/lo"
)

type recallerFunc func(ctx context.Context, datasetID string, query string, topK int) ([]int64, error)
type RecallDatasetOption struct {
	DatasetID string
	Query     string
	TopK      int
}

func (s *Service) RecallDataset(ctx context.Context, option *RecallDatasetOption) ([]*entity.Segment, error) {
	wg := &sync.WaitGroup{}
	var (
		recalledSegmentIDs     [][]int64
		recalledSegmentIDsLock sync.Mutex
	)
	for _, recaller := range s.recallers {
		wg.Add(1)
		recaller := recaller
		go func() {
			defer wg.Done()
			segmentIDs, err := recaller(ctx, option.DatasetID, option.Query, option.TopK)
			if err != nil {
				logs.V1.CtxError(ctx, "error Recall, err: %v", err)
				return
			}
			recalledSegmentIDsLock.Lock()
			recalledSegmentIDs = append(recalledSegmentIDs, segmentIDs)
			recalledSegmentIDsLock.Unlock()
		}()
	}
	wg.Wait()
	mergedSegmentIDs := mergeSegmentIDs(recalledSegmentIDs)
	segments, err := s.dao.MGetSegments(ctx, mergedSegmentIDs)
	documentIDs := make([]string, 0, len(segments))
	for _, segment := range segments {
		documentIDs = append(documentIDs, segment.DocumentID)
	}
	documents, err := s.dao.MGetDocument(ctx, documentIDs)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get document")
	}
	for _, segment := range segments {
		segment.Document = documents[segment.DocumentID]
	}
	lo.Filter(segments, func(item *entity.Segment, index int) bool {
		return item.Document != nil
	})
	return segments, err
}

func mergeSegmentIDs(recalledSegmentIDs [][]int64) []int64 {
	// Merge and deduplicate segment IDs
	merged := make([]int64, 0)
	seen := make(map[int64]bool)
	for _, segmentIDs := range recalledSegmentIDs {
		for _, segmentID := range segmentIDs {
			if !seen[segmentID] {
				seen[segmentID] = true
				merged = append(merged, segmentID)
			}
		}
	}
	return merged
}
