package knowledgebase

import (
	"context"
	"math"
	"math/rand"
	"time"

	"code.byted.org/gopkg/ctxvalues"
	"code.byted.org/gopkg/logid"
	"code.byted.org/gopkg/logs/v2"
	"github.com/pkg/errors"
	"github.com/robfig/cron/v3"
	"github.com/samber/lo"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
)

func (s *Service) StartCron() error {
	c := cron.New(cron.WithSeconds(),
		cron.With<PERSON>hain(cron.SkipIfStillRunning(cron.DefaultLogger)))
	_, err := c.AddFunc("0 0 22 * * *", func() {
		ctx := ctxvalues.SetLogID(context.Background(), logid.GenLogID())
		if err := s.FullUpdateDocument(ctx, false); err != nil {
			logs.V1.CtxError(ctx, "Failed to handle cron job update document", "err", err)
		}
	})
	if err != nil {
		return err
	}
	c.Start()
	return nil
}

func (s *Service) FullUpdateDocument(ctx context.Context, forceUpdate bool) error {
	logs.V1.CtxInfo(ctx, "[FullUpdateDocument] start to full update document")
	lockKey := "knowledgebase_full_update_document"
	r := rand.New(rand.NewSource(time.Now().UnixNano())) // 使用当前时间作为种子
	lockValue := r.Intn(math.MaxInt)
	ok, err := s.redis.SetNX(ctx, lockKey, lockValue, time.Hour*12)
	if err != nil {
		return errors.WithMessage(err, "failed to lock")
	}
	if !ok {
		return errors.New("failed to get lock")
	}
	defer s.redis.Cad(ctx, lockKey, lockValue)
	datasetList, err := s.dao.ListDatasets(ctx)
	if err != nil {
		logs.V1.CtxError(ctx, "[FullUpdateDocument] failed to list datasets: %v", err)
		return err
	}
	for _, dataset := range datasetList {
		docs, err := s.dao.ListDocumentsByDatasetID(ctx, dataset.ID)
		if err != nil {
			logs.V1.CtxError(ctx, "[FullUpdateDocument] failed to list documents: %v", err)
			continue
		}
		if len(docs) == 0 {
			logs.V1.CtxInfo(ctx, "[FullUpdateDocument] no documents found for dataset: %s", dataset.ID)
			continue
		}
		for _, doc := range docs {
			ctx = ctxvalues.SetLogID(context.Background(), logid.GenLogID())
			err := s.dao.UpdateDocument(ctx, doc.ID, &dal.UpdateDocumentOption{
				ProcessStatus: lo.ToPtr(entity.DocumentProcessStatusProcessing),
			})
			if err != nil {
				logs.V1.CtxError(ctx, "[FullUpdateDocument] failed to update document: %v, document id:%v", err, doc.ID)
				continue
			}
			err = s.SendBatchUpsertContentMessage(ctx, []*entity.Document{doc}, forceUpdate)
			if err != nil {
				logs.V1.CtxError(ctx, "[FullUpdateDocument] failed to send batch upsert content: %v, document id:%v", err, doc.ID)
			}
		}

	}
	return nil
}
