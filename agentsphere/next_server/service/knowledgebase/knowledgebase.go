package knowledgebase

import (
	"context"
	"encoding/json"
	"regexp"
	"sort"
	"strings"
	"sync"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	larkservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/lark"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/service/permission"
	devaiutil "code.byted.org/devgpt/kiwis/devai/common/util"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/idgenerator"
	"code.byted.org/devgpt/kiwis/lib/uuid"
	"code.byted.org/devgpt/kiwis/port/db"
	"code.byted.org/devgpt/kiwis/port/es"
	"code.byted.org/devgpt/kiwis/port/lark"
	"code.byted.org/devgpt/kiwis/port/redis"
	"code.byted.org/devgpt/kiwis/port/rocketmq"
	"code.byted.org/devgpt/kiwis/port/tos"
	"code.byted.org/devgpt/kiwis/port/viking"
	"code.byted.org/gopkg/logs/v2"
	poolsdk "github.com/panjf2000/ants/v2"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/fx"
)

type Service struct {
	idGen       uuid.Generator
	dao         *dal.DAO
	esCli       es.Client
	esIndex     string
	vikingCli   *viking.Client
	vikingIndex string
	larkCli     lark.Client
	larkService *larkservice.Service
	loader      DocumentLoader
	tosCli      tos.Client
	idIntGen    idgenerator.IDGenerator
	// recallers make it easy to test.
	recallers         []recallerFunc
	esIndexDocument   string
	knowledgebaseMQ   rocketmq.Client
	redis             redis.Client
	permissionService *permission.Service
}

type CreateServiceOption struct {
	fx.In

	DAO               *dal.DAO
	ESCli             es.Client
	LarkCli           lark.Client
	LarkService       *larkservice.Service
	TosCli            tos.Client
	IdIntGen          idgenerator.IDGenerator
	VikingClient      *viking.Client
	TccConf           *config.AgentSphereTCCConfig
	Conf              *config.AgentSphereConfig
	KnowledgebaseMQ   rocketmq.Client `name:"knowledgebase_mq"`
	Redis             redis.Client
	PermissionService *permission.Service
}

func NewService(opt CreateServiceOption) *Service {
	s := &Service{
		dao:               opt.DAO,
		esCli:             opt.ESCli,
		esIndex:           opt.Conf.KnowledgebaseConfig.ElasticSearchRecallIndex,
		idGen:             uuid.GetDefaultGenerator(nil),
		larkCli:           opt.LarkCli,
		larkService:       opt.LarkService,
		loader:            NewLarkLoader(opt.LarkCli),
		tosCli:            opt.TosCli,
		idIntGen:          opt.IdIntGen,
		vikingCli:         opt.VikingClient,
		vikingIndex:       opt.TccConf.NextAgentKnowledgeConfig.GetValue().VikingIndex,
		esIndexDocument:   opt.TccConf.NextAgentKnowledgeConfig.GetValue().ElasticSearchDocumentIndex,
		knowledgebaseMQ:   opt.KnowledgebaseMQ,
		redis:             opt.Redis,
		permissionService: opt.PermissionService,
	}
	s.recallers = []recallerFunc{
		s.recallSegmentsFromViking,
		s.recallSegmentsFromES,
	}
	return s
}

func (s *Service) GetDatasetBySpaceID(ctx context.Context, spaceID string) (*entity.Dataset, error) {
	return s.dao.GetDatasetBySpaceID(ctx, spaceID)
}

func (s *Service) MGetDatasetBySpaceIDs(ctx context.Context, spaceIDs []string) (map[string]*entity.Dataset, error) {
	return s.dao.MGetDatasetBySpaceIDs(ctx, spaceIDs)
}

// CreateDataset creates a new dataset with a unique ID.
func (s *Service) CreateDataset(ctx context.Context, spaceID, owner string) (*entity.Dataset, error) {
	id := s.idGen.NewID()
	dataset, err := s.dao.CreateDataset(ctx, dal.DatasetCreateOption{ID: id, SpaceID: spaceID})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to create dataset")
	}
	space, err := s.dao.GetSpace(ctx, spaceID, true)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get space")
	}
	var isSpacePublic *bool
	if space.Type.IsProject() { // 项目空间的知识库默认项目内公开
		isSpacePublic = lo.ToPtr(true)
	}
	_, err = s.permissionService.CreateResource(ctx, permission.CreateResourceOption{
		Owner:         owner,
		Type:          entity.ResourceTypeKnowledgebase,
		ExternalID:    id,
		Status:        lo.ToPtr(entity.ResourceStatusPrivate),
		GroupID:       &spaceID,
		IsSpacePublic: isSpacePublic,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to create resource for dataset")
	}
	return dataset, nil
}

// SearchDocsFromLark searches for documents in Lark (not aime knowledgebase) based on the provided query.
// It can be used to recommend documents from Lark based on the user's query.
func (s *Service) SearchDocsFromLark(ctx context.Context, username string, query string, datasetID string) ([]*entity.LarkDocument, error) {
	accessToken, err := s.larkService.GetUserAccessToken(ctx, username)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get lark user")
	}
	existDocs, _ := s.dao.ListDocumentsByDatasetID(ctx, datasetID)
	existDocMap := make(map[string]bool)
	for _, doc := range existDocs {
		existDocMap[doc.SourceUid] = true
	}
	query = strings.TrimSpace(query)
	sourceType, fileKey := devaiutil.ParseLarkDocURL(ctx, query)
	if len(sourceType) != 0 {
		_, isSupported := entity.GetDocumentSourceType(sourceType)
		if !isSupported {
			return nil, errors.WithMessagef(err, "invalid document sourceType: %s", sourceType)
		}
		fileMeta, err := s.larkCli.GetFilesMeta(ctx, []*lark.RequestDoc{
			{
				DocToken: fileKey,
				DocType:  sourceType,
			},
		}, accessToken, lark.Option{UserIDType: lark.UserIDTypeOpenID})
		if err == nil && len(fileMeta.Metas) > 0 {
			docType := entity.DocumentContentType(lo.FromPtr(fileMeta.Metas[0].DocType))
			if !docType.IsSupport() {
				return nil, errors.WithMessagef(err, "invalid document type: %s", docType)
			}
			users, err := s.larkCli.ListLarkUsers(ctx, []string{lo.FromPtr(fileMeta.Metas[0].OwnerId)}, "")
			if err != nil {
				logs.V1.CtxError(ctx, "failed to list lark users, err: %v", err)
			}
			var ownerName string
			if len(users) > 0 {
				r := strings.Split(lo.FromPtr(users[0].Email), "@")
				if len(r) != 0 {
					ownerName = r[0]
				}
			}
			return []*entity.LarkDocument{
				{
					Title:       lo.FromPtr(fileMeta.Metas[0].Title),
					URL:         query, //url与请求一致
					IsUploaded:  existDocMap[fileKey],
					ContentType: lo.FromPtr(fileMeta.Metas[0].DocType),
					OwnerName:   ownerName,
				},
			}, nil
		}
	}
	searchLarkObjectResp, err := s.larkCli.SearchLarkObject(ctx, &lark.SearchLarkObjectRequest{
		SearchKey: query,
		Count:     20,
		Offset:    0,
		DocsTypes: []string{"doc"},
	}, accessToken)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to search lark object")
	}
	docTokens := make([]string, 0, len(searchLarkObjectResp.DocsEntities))
	for _, docsEntity := range searchLarkObjectResp.DocsEntities {
		docTokens = append(docTokens, docsEntity.DocsType+"/"+docsEntity.DocsToken)
	}
	docs, err := s.larkCli.SearchLarkData(ctx, 10, query, accessToken, &lark.PassageDisableSearch{HelpdeskDisable: true, DocTokens: docTokens})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to search lark data")
	}
	fileKeyMap := make(map[string]bool)
	ret := make([]*entity.LarkDocument, 0, len(docs.Data.Passages))
	for _, doc := range docs.Data.Passages {
		extra := lark.SearchPassageExtra{}
		err = json.Unmarshal([]byte(doc.Extra), &extra)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to unmarshal extra")
		}
		contentType := entity.DocumentContentType(extra.GetContentType())
		if !contentType.IsSupport() {
			logs.Error("not support document type: %s, url:%s", contentType, doc.URL)
			continue
		}
		_, fileKey := devaiutil.ParseLarkDocURL(ctx, strings.TrimSpace(doc.URL))
		if _, ok := fileKeyMap[fileKey]; ok { // 文档去重
			continue
		}
		fileKeyMap[fileKey] = true
		doc.Content = postContent(doc.Content)
		ret = append(ret, &entity.LarkDocument{
			Title:       doc.Title,
			URL:         doc.URL,
			IsUploaded:  existDocMap[fileKey],
			Content:     doc.Content,
			ContentType: string(contentType),
			OwnerName:   extra.OwnerName,
		})
	}
	return ret, nil
}

type UploadDocumentOption struct {
	SourceType entity.DocumentSourceType
	SourceUid  string
}

const (
	uploadPoolSize = 5
)

func (s *Service) UploadDocuments(ctx context.Context, datasetID, username string, opts []UploadDocumentOption) ([]*entity.Document, error) {
	dataset, _ := s.dao.GetDataset(ctx, datasetID)
	if dataset == nil {
		return nil, errors.New("dataset not found")
	}
	userToken, err := s.larkService.GetUserAccessToken(ctx, username)
	if err != nil {
		if db.IsRecordNotFoundError(err) || errors.Is(err, larkservice.ErrRefreshTokenExpired) {
			return nil, lark.ErrLarkAuthFailed
		}
		return nil, err
	}
	// 过滤掉空间下已经存在的文档
	existDocs, _ := s.dao.ListDocumentsByDatasetIDSourceUids(ctx, datasetID, lo.Map(opts, func(opt UploadDocumentOption, index int) string {
		return opt.SourceUid
	}))
	docMap := make(map[string]bool)
	for _, doc := range existDocs {
		docMap[doc.SourceUid] = true
	}
	pool, err := poolsdk.NewPool(uploadPoolSize)
	if err != nil {
		logs.V1.CtxError(ctx, "[AsyncBatchUpdateContent] failed to create pool: %v", err)
		return nil, err
	}
	defer pool.Release()
	var lock sync.Mutex
	docs := make([]*entity.Document, 0, len(opts))
	wg := &sync.WaitGroup{}
	for _, opt := range opts {
		if docMap[opt.SourceUid] { // 已经存在的文档跳过
			logs.V1.CtxInfo(ctx, "source uid: %s already exists", opt.SourceUid)
			continue
		}
		opt := opt
		wg.Add(1)
		_ = pool.Submit(func() {
			defer wg.Done()
			meta, loadErr := s.loader.LoadMeta(ctx, username, userToken, MetaOption{
				SourceType: opt.SourceType,
				SourceUid:  opt.SourceUid,
			})
			if loadErr != nil {
				err = loadErr
				return
			}
			doc := &entity.Document{
				ID:            s.idGen.NewID(),
				DatasetID:     datasetID,
				Creator:       username,
				Title:         meta.Title,
				SourceType:    meta.SourceType,
				SourceUid:     meta.SourceUid,
				ContentType:   meta.ContentType,
				ProcessStatus: entity.DocumentProcessStatusProcessing,
				LastUpdatedAt: meta.UpdateTime,
				Owner:         meta.Owner,
				DocToken:      lo.ToPtr(meta.DocToken),
			}
			lock.Lock()
			docs = append(docs, doc)
			lock.Unlock()
		})
	}
	wg.Wait()
	if err != nil {
		return nil, err
	}
	if len(docs) == 0 {
		return nil, nil
	}
	err = s.saveDocumentsToES(ctx, docs)
	if err != nil {
		return nil, err
	}
	ret, err := s.dao.BatchCreateDocument(ctx, docs)
	if err != nil {
		return nil, err
	}
	err = s.SendBatchUpsertContentMessage(ctx, docs, false)
	if err != nil {
		logs.V1.CtxError(ctx, "failed to send batch upsert content: %v", err)
	}
	return ret, nil
}

func (s *Service) DeleteDocument(ctx context.Context, datasetID, documentID string) error {
	err := s.dao.DeleteDocument(ctx, datasetID, documentID)
	if err != nil {
		return err
	}
	err = s.deleteDocumentToES(ctx, documentID)
	if err != nil && !errors.Is(err, ErrNotFound) {
		return err
	}
	return s.DeleteDocumentContent(ctx, datasetID, documentID)
}

func (s *Service) UpdateDocument(ctx context.Context, datasetID, documentID, username string) error {
	doc, err := s.dao.GetDocument(ctx, datasetID, documentID)
	if err != nil {
		return err
	}
	userToken, err := s.larkService.GetUserAccessToken(ctx, username)
	if err != nil {
		if db.IsRecordNotFoundError(err) || errors.Is(err, larkservice.ErrRefreshTokenExpired) {
			return lark.ErrLarkAuthFailed
		}
		return err
	}
	meta, err := s.loader.LoadMeta(ctx, username, userToken, MetaOption{
		SourceType: doc.SourceType,
		SourceUid:  doc.SourceUid,
	})
	if err != nil {
		return err
	}

	err = s.dao.UpdateDocument(ctx, documentID, &dal.UpdateDocumentOption{
		ProcessStatus: lo.ToPtr(entity.DocumentProcessStatusProcessing),
		Title:         lo.ToPtr(meta.Title),
		Owner:         lo.ToPtr(meta.Owner),
		LastUpdatedAt: lo.ToPtr(meta.UpdateTime),
	})
	if err != nil {
		return err
	}
	doc.Title = meta.Title
	doc.Owner = meta.Owner
	doc.LastUpdatedAt = meta.UpdateTime
	doc.ContentType = meta.ContentType
	doc.DocToken = lo.ToPtr(meta.DocToken)
	doc.ProcessStatus = entity.DocumentProcessStatusProcessing
	err = s.updateDocumentToES(ctx, documentID, &updateDocumentToESOption{
		Title:         lo.ToPtr(meta.Title),
		Owner:         lo.ToPtr(meta.Owner),
		LastUpdatedAt: lo.ToPtr(meta.UpdateTime),
	})
	if err != nil {
		return err
	}
	err = s.SendBatchUpsertContentMessage(ctx, []*entity.Document{doc}, false)
	if err != nil {
		logs.V1.CtxError(ctx, "failed to send batch upsert content: %v", err)
	}
	return nil
}

func (s *Service) GetDocument(ctx context.Context, datasetID, documentID string) (*entity.Document, error) {
	document, err := s.dao.GetDocument(ctx, datasetID, documentID)
	if err != nil {
		return nil, err
	}
	content, err := s.getContentFromTOS(ctx, lo.FromPtr(document.Content))
	if err != nil {
		return nil, err
	}
	document.DocumentContent = lo.ToPtr(content)
	go func() {
		err = s.add1HitCountToES(ctx, documentID)
		if err != nil {
			logs.V1.CtxError(ctx, "[GetDocument] failed to upsert document to es: %v", err)
		}
	}()

	return document, nil
}

func (s *Service) GetDocumentMetadata(ctx context.Context, datasetID, documentID string) (*entity.Document, error) {
	document, err := s.dao.GetDocument(ctx, datasetID, documentID)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get document")
	}
	return document, nil
}

type ListDocumentsOption struct {
	Query       *string
	Creators    []string
	DescOrderBy *string
	Limit       int
	Offset      int
}

func (s *Service) ListDocuments(ctx context.Context, datasetID string, opt *ListDocumentsOption) ([]*entity.Document, int64, error) {
	docs, totalCount, err := s.searchDocumentsFromES(ctx, datasetID, &searchDocumentsOption{
		Query:       opt.Query,
		Creator:     opt.Creators,
		DescOrderBy: opt.DescOrderBy,
		Limit:       opt.Limit,
		Offset:      opt.Offset,
	})
	if err != nil {
		return nil, 0, err
	}
	if len(docs) == 0 {
		return nil, 0, nil
	}
	documentIDs := lo.Map(docs, func(doc *esDocumentDoc, index int) string {
		return lo.FromPtr(doc.ID)
	})
	documentMap, err := s.dao.MGetDocument(ctx, documentIDs)
	if err != nil {
		return nil, 0, err
	}
	ret := make([]*entity.Document, 0, len(documentMap))
	for _, doc := range docs {
		document, ok := documentMap[lo.FromPtr(doc.ID)]
		if !ok {
			logs.V1.CtxError(ctx, "[ListDocuments] document not found, docID: %s", lo.FromPtr(doc.ID))
			continue
		}
		ret = append(ret, &entity.Document{
			ID:            document.ID,
			DatasetID:     document.DatasetID,
			Creator:       document.Creator,
			Title:         document.Title,
			SourceType:    document.SourceType,
			SourceUid:     document.SourceUid,
			Owner:         document.Owner,
			CreatedAt:     document.CreatedAt,
			LastUpdatedAt: document.LastUpdatedAt,
			ProcessStatus: document.ProcessStatus,
			FaildReason:   document.FaildReason,
			Heat:          lo.ToPtr(doc.HitCount), // 目前热度为@搜索次数
			ContentType:   document.ContentType,
			UpdatedAt:     document.UpdatedAt,
		})
	}
	return ret, totalCount, nil
}

type ReferenceDocument struct {
	Title string
	URL   string
}

func (s *Service) RecommendDocuments(ctx context.Context, username, datasetID string, referenceDocuments []*ReferenceDocument) ([]*entity.LarkDocument, error) {
	userToken, err := s.larkService.GetUserAccessToken(ctx, username)
	if err != nil {
		if db.IsRecordNotFoundError(err) || errors.Is(err, larkservice.ErrRefreshTokenExpired) {
			return nil, lark.ErrLarkAuthFailed
		}
		return nil, err
	}
	existDocs, _ := s.dao.ListDocumentsByDatasetID(ctx, datasetID)
	fileKeyMap := make(map[string]bool)
	for _, doc := range existDocs {
		fileKeyMap[doc.SourceUid] = true
	}
	for _, doc := range referenceDocuments {
		_, referenceFileKey := devaiutil.ParseLarkDocURL(ctx, strings.TrimSpace(doc.URL))
		fileKeyMap[referenceFileKey] = true
	}
	topK := 10
	ret := make([]*entity.LarkDocument, 0, 10)

	for _, doc := range referenceDocuments {
		docs, err := s.larkCli.SearchLarkData(ctx, topK, doc.Title, userToken, &lark.PassageDisableSearch{HelpdeskDisable: true})
		if err != nil {
			logs.V1.CtxError(ctx, "[RecommendDocuments] failed to search documents: %v, search content:%v", err, doc.Title)
			continue
		}
		for _, tmp := range docs.Data.Passages {
			extra := lark.SearchPassageExtra{}
			err = json.Unmarshal([]byte(tmp.Extra), &extra)
			if err != nil {
				return nil, errors.WithMessage(err, "failed to unmarshal extra")
			}
			contentType := entity.DocumentContentType(extra.GetContentType())
			if extra.OwnerName == "Aime" { // 过滤掉Aime的文档
				continue
			}
			if !contentType.IsSupport() {
				logs.Error("not support document type: %s, url:%s", contentType, doc.URL)
				continue
			}
			_, fileKey := devaiutil.ParseLarkDocURL(ctx, strings.TrimSpace(tmp.URL))
			if _, ok := fileKeyMap[fileKey]; ok { // 文档去重
				continue
			}
			tmp.Content = postContent(tmp.Content)
			ret = append(ret, &entity.LarkDocument{
				Title:       tmp.Title,
				URL:         tmp.URL,
				IsUploaded:  fileKeyMap[fileKey],
				Content:     tmp.Content,
				ContentType: string(contentType),
				OwnerName:   extra.OwnerName,
				Score:       tmp.Score,
			})
			fileKeyMap[fileKey] = true
		}
		if len(ret) >= 20 {
			break
		}
	}
	sort.Slice(ret, func(i, j int) bool {
		return ret[i].Score > ret[j].Score
	})
	return ret, nil
}

func postContent(content string) string {
	content = strings.ReplaceAll(content, "[title]", "")
	content = strings.ReplaceAll(content, "[block_sep]", "\n")
	re := regexp.MustCompile(`\[heading\d*\]`)
	content = re.ReplaceAllString(content, "#")
	re = regexp.MustCompile(`(^|\n)\[content\]`)
	content = re.ReplaceAllString(content, "")
	content = strings.ReplaceAll(content, "[content]", "\n")
	return content
}

func (s *Service) CountDocuments(ctx context.Context, datasetID, username string) (int, int, error) {
	_, allTotal, err := s.searchDocumentsFromES(ctx, datasetID, nil)
	if err != nil {
		return 0, 0, err
	}
	_, myTotal, err := s.searchDocumentsFromES(ctx, datasetID, &searchDocumentsOption{Creator: []string{username}})
	if err != nil {
		return 0, 0, err
	}
	return int(allTotal), int(myTotal), nil
}
