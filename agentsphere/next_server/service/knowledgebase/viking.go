package knowledgebase

import (
	"context"
	"time"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	"code.byted.org/devgpt/kiwis/port/viking"
	"code.byted.org/gopkg/logs/v2"
	vikingclient "code.byted.org/lagrange/viking_go_client"
	"github.com/cenkalti/backoff/v4"
	"github.com/pkg/errors"
	"golang.org/x/sync/errgroup"
)

const dslFieldDatasetID = "dataset_id"

func (s *Service) saveSegmentsToViking(ctx context.Context, segments []*entity.Segment) error {
	group := errgroup.Group{}
	group.SetLimit(100)
	for _, segment := range segments {
		vikingData := &vikingclient.VikingDbData{
			LabelLower: uint64(segment.ID),
			Context:    []string{"default"},
			Attrs:      "",
			DslInfo:    map[string]interface{}{dslFieldDatasetID: segment.DatasetID},
		}
		vikingRawData := &vikingclient.RawData{
			RawData: map[string]interface{}{"text": segment.Content.Text},
		}
		group.Go(func() error {
			err := backoff.Retry(func() error {
				// Use batch save can cause the http entity too large.
				_, logID, err := s.vikingCli.AddRawData([]*vikingclient.RawData{vikingRawData}, []*vikingclient.VikingDbData{vikingData})
				if err != nil {
					logs.V1.CtxError(ctx, "failed to add vectors, log id :%s, err is %+v", logID, err)
				}
				return err
			}, backoff.WithMaxRetries(backoff.NewExponentialBackOff(), 3))
			if err != nil {
				return errors.WithMessage(err, "save segments to viking failed")
			}
			return nil
		})
	}
	return group.Wait()
}

func (s *Service) recallSegmentsFromViking(ctx context.Context, datasetID string, query string, topK int) ([]int64, error) {
	var vector []float32
	err := backoff.Retry(func() error {
		var response [][]float32
		response, _, err := s.vikingCli.RawEmbedding([]*vikingclient.RawData{
			{
				RawData: map[string]interface{}{"text": query},
			},
		})
		if err != nil {
			return errors.WithMessagef(err, "failed to embedding")
		}
		if len(response) == 0 {
			return errors.Errorf("viking return empty result")
		}
		vector = response[0]
		return nil
	}, backoff.WithMaxRetries(backoff.NewExponentialBackOff(), 3))
	if err != nil {
		return nil, errors.WithMessagef(err, "failed to embedding query content")
	}
	dslInfo := viking.MustDSL(dslFieldDatasetID, []interface{}{datasetID})

	var (
		resp  *vikingclient.RecallResp
		logID string
	)
	err = backoff.Retry(func() error {
		resp, logID, err = s.vikingCli.Recall(&vikingclient.RecallRequest{
			Embedding: vector,
			DslInfo:   dslInfo,
			Index:     s.vikingIndex,
			TopK:      int32(topK),
		})
		if err != nil {
			return err
		}
		return nil
	}, backoff.WithMaxRetries(backoff.NewConstantBackOff(time.Millisecond*50), 3))
	if err != nil {
		return nil, errors.WithMessagef(err, "failed to recall from viking")
	}
	if len(resp.Result) == 0 {
		logs.V1.CtxWarn(ctx, "no vectors recalled, logID: %s", logID)
		return nil, nil
	}
	ids := make([]int64, 0, len(resp.Result))
	for _, item := range resp.Result {
		ids = append(ids, int64(item.LabelLower64))
	}
	return ids, nil
}

func (s *Service) deleteSegmentsToViking(ctx context.Context, segmentIDs []int64) error {
	labels := make([]*vikingclient.LabelLowerAndUpper, 0, len(segmentIDs))
	for _, segmentID := range segmentIDs {
		label := &vikingclient.LabelLowerAndUpper{
			LabelLower64: uint64(segmentID),
			LabelUpper64: 0,
		}
		labels = append(labels, label)
	}
	err := backoff.Retry(func() error {
		_, logID, err := s.vikingCli.DeleteData(labels)
		if err != nil {
			logs.V1.CtxError(ctx, "failed to delete vectors, log id :%s, err is %+v", logID, err)
		}
		return err
	}, backoff.WithMaxRetries(backoff.NewExponentialBackOff(), 3))
	if err != nil {
		return errors.WithMessage(err, "delete segments from viking failed")
	}
	return nil
}
