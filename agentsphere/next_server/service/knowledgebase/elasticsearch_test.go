package knowledgebase

import (
	"context"
	"encoding/json"
	"slices"
	"testing"
	"time"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	"code.byted.org/devgpt/kiwis/lib/util"
	"code.byted.org/devgpt/kiwis/mock"
	"github.com/samber/lo"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

/**
Start ES local
docker run --rm -e discovery.type=single-node -p 9200:9200 hub.byted.org/ee/elasticsearch:7.8.0
*/

func TestService_saveSegmentsToES(t *testing.T) {
	util.SkipTestIfNotCICD(t)
	index := "agentsphere_knowledgebase"
	es := mock.NewMockES(t, &mock.ElasticInitIndex{
		Name: index,
		Body: `{
  "mappings": {
    "dynamic": "true",
    "properties": {
      "dataset_id": {
        "type": "keyword"
      },
      "document_id": {
        "type": "keyword"
      },
      "content": {
        "type": "text"
      }
    }
  }
}`,
	})
	s := &Service{esCli: es, esIndex: index}
	err := s.saveSegmentsToES(context.Background(), []*entity.Segment{
		{
			ID:         10,
			DatasetID:  "dataset_1",
			DocumentID: "document_1",
			Content:    &entity.SegmentContent{Text: "This is a test segment 1."},
		},
		{
			ID:         11,
			DatasetID:  "dataset_2",
			DocumentID: "document_2",
			Content:    &entity.SegmentContent{Text: "This is a test segment 1."},
		},
	})
	require.NoError(t, err)
	_, err = es.Refresh(index).Do(context.Background())
	require.NoError(t, err)
	result, err := es.Get().Index(index).Id("10").Do(context.Background())
	require.NoError(t, err)
	require.NotNil(t, result)
	require.Equal(t, string(result.Source), "{\"dataset_id\":\"dataset_1\",\"document_id\":\"document_1\",\"content\":\"This is a test segment 1.\"}")
	err = s.deleteSegmentsToES(context.Background(), []int64{10, 11})
	require.NoError(t, err)
	_, err = es.Refresh(index).Do(context.Background())
	result, _ = es.Get().Index(index).Id("10").Do(context.Background())
	require.Nil(t, result)
	result, _ = es.Get().Index(index).Id("11").Do(context.Background())
	require.Nil(t, result)
}

func TestService_DocumentToES(t *testing.T) {
	util.SkipTestIfNotCICD(t)
	index := "agentsphere_knowledgebase_document"
	es := mock.NewMockES(t, &mock.ElasticInitIndex{
		Name: index,
		Body: `{
  "mappings": {
"dynamic": "false",
"properties": {
  "content_type": {
	"type": "keyword"
  },
  "created_at": {
	"format": "epoch_second",
	"type": "date"
  },
  "creator": {
	"type": "keyword"
  },
  "dataset_id": {
	"type": "keyword"
  },
  "document_id": {
	"type": "keyword"
  },
  "heat": {
	"type": "long"
  },
  "hit_count": {
	"type": "long"
  },
  "last_updated_at": {
	"format": "epoch_second",
	"type": "date"
  },
  "owner": {
	"type": "keyword"
  },
  "source_type": {
	"type": "keyword"
  },
  "title": {
	"type": "text",
	"fields": {
	  "ngram": {
		"type": "text",
		"analyzer": "ngram_analyzer",
		"search_analyzer": "standard"
	  }
	}
  },
  "updated_at": {
	"format": "epoch_second",
	"type": "date"
  }
    }
  },
  "settings": {
    "analysis": {
      "analyzer": {
        "ngram_analyzer": {
          "tokenizer": "ngram_tokenizer",
          "filter": [
            "lowercase"
          ]
        }
      },
      "tokenizer": {
        "ngram_tokenizer": {
          "type": "ngram",
          "min_gram": 3,
          "max_gram": 4,
          "token_chars": [
            "letter",
            "digit"
          ]
        }
      }
    }
  }
}
`,
	})
	s := &Service{esCli: es, esIndexDocument: index}
	ctx := context.Background()
	err := s.saveDocumentsToES(ctx, []*entity.Document{
		{
			ID:            "1",
			DatasetID:     "dataset_1",
			Title:         "This is a test document 1.",
			Owner:         "user_1",
			Creator:       "user_1",
			LastUpdatedAt: time.Date(2020, 1, 10, 0, 0, 0, 0, time.UTC),
			UpdatedAt:     time.Date(2020, 1, 10, 0, 0, 0, 0, time.UTC),
		},
		{
			ID:            "2",
			DatasetID:     "dataset_1",
			Title:         "This is a test document 2.",
			Owner:         "user_2",
			Creator:       "user_2",
			LastUpdatedAt: time.Date(2020, 1, 11, 0, 0, 0, 0, time.UTC),
			UpdatedAt:     time.Date(2020, 1, 11, 0, 0, 0, 0, time.UTC),
		},
		{
			ID:            "3",
			DatasetID:     "dataset_3",
			Title:         "This is a test document 3.",
			Owner:         "user_3",
			Creator:       "user_3",
			LastUpdatedAt: time.Date(2020, 1, 12, 0, 0, 0, 0, time.UTC),
			UpdatedAt:     time.Date(2020, 1, 12, 0, 0, 0, 0, time.UTC),
		},
		{
			ID:            "3",
			DatasetID:     "dataset_4",
			Title:         "Aime OnePage.",
			Owner:         "user_3",
			Creator:       "user_3",
			LastUpdatedAt: time.Date(2020, 1, 12, 0, 0, 0, 0, time.UTC),
			UpdatedAt:     time.Date(2020, 1, 12, 0, 0, 0, 0, time.UTC),
		},
		{
			ID:            "3_1",
			DatasetID:     "dataset_4",
			Title:         "站点问答助手技术设计",
			Owner:         "user_3",
			Creator:       "user_3",
			LastUpdatedAt: time.Date(2020, 1, 12, 0, 0, 0, 0, time.UTC),
			UpdatedAt:     time.Date(2020, 1, 12, 0, 0, 0, 0, time.UTC),
		},
		{
			ID:            "4",
			DatasetID:     "dataset_5",
			Title:         "Aime OnePage.",
			Owner:         "zhujingyan.43",
			Creator:       "zhujingyan.43",
			LastUpdatedAt: time.Date(2020, 1, 12, 0, 0, 0, 0, time.UTC),
			UpdatedAt:     time.Date(2020, 1, 12, 0, 0, 0, 0, time.UTC),
		},
	})

	require.NoError(t, err)
	_, err = es.Refresh(index).Do(context.Background())
	require.NoError(t, err)
	docs, total, err := s.searchDocumentsFromES(ctx, "dataset_1", &searchDocumentsOption{Query: lo.ToPtr("test"), Limit: 2, Offset: 0})
	require.NoError(t, err)
	require.Equal(t, int64(2), total)
	require.Equal(t, 2, len(docs))
	searchedDocIDs := []string{lo.FromPtr(docs[0].ID), lo.FromPtr(docs[1].ID)}
	slices.Sort(searchedDocIDs)
	require.Equal(t, []string{"1", "2"}, searchedDocIDs)
	documents, count, err := s.searchDocumentsFromES(ctx, "dataset_4", &searchDocumentsOption{Query: lo.ToPtr("One"), Limit: 2, Offset: 0})
	require.NoError(t, err)
	require.Equal(t, int64(1), count)
	require.Equal(t, 1, len(documents))
	require.Equal(t, "3", lo.FromPtr(documents[0].ID))
	documents, count, err = s.searchDocumentsFromES(ctx, "dataset_4", &searchDocumentsOption{Query: lo.ToPtr("助手"), Limit: 2, Offset: 0})
	require.NoError(t, err)
	require.Equal(t, int64(1), count)
	require.Equal(t, 1, len(documents))
	require.Equal(t, "3_1", lo.FromPtr(documents[0].ID))
	t.Run("creator_search", func(t *testing.T) {
		documents, count, err := s.searchDocumentsFromES(ctx, "dataset_5", &searchDocumentsOption{Limit: 2, Offset: 0, Creator: []string{"zhujingyan.43"}})
		require.NoError(t, err)
		require.Equal(t, int64(1), count)
		require.Equal(t, 1, len(documents))
		require.Equal(t, "4", lo.FromPtr(documents[0].ID))
	})
	t.Run("update", func(t *testing.T) {
		err := s.updateDocumentToES(context.Background(), "4", &updateDocumentToESOption{
			Title:         lo.ToPtr("New Title"),
			Owner:         lo.ToPtr("new owner"),
			LastUpdatedAt: lo.ToPtr(time.Now()),
		})
		assert.Nil(t, err)
		result, _ := es.Get().Index(index).Id("4").Do(context.Background())
		_ = result
		d := &esDocumentDoc{}
		err = json.Unmarshal(result.Source, d)
		assert.Nil(t, err)
		assert.EqualValues(t, "new owner", d.Owner)
		assert.EqualValues(t, "New Title", d.Title)
		t.Logf("%+v", result)
	})
}
