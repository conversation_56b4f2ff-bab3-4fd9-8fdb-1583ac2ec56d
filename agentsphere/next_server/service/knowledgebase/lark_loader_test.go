package knowledgebase

import (
	"context"
	"fmt"
	"testing"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	larkmock "code.byted.org/devgpt/kiwis/port/lark/mock"
	larkcontact "github.com/larksuite/oapi-sdk-go/v3/service/contact/v3"
	larkdrive "github.com/larksuite/oapi-sdk-go/v3/service/drive/v1"
	"github.com/samber/lo"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func TestLarkLoader_BatchLoadMeta(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	larkCli := larkmock.NewMockClient(ctrl)
	loader := NewLarkLoader(larkCli)
	larkCli.EXPECT().GetFilesMeta(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&larkdrive.BatchQueryMetaRespData{
		Metas: []*larkdrive.Meta{{
			DocToken: lo.ToPtr("11111"),
			Title:    lo.ToPtr("title1"),
			OwnerId:  lo.ToPtr("123"),
			DocType:  lo.ToPtr("doc"),
		},
			{
				DocToken: lo.ToPtr("22222"),
				Title:    lo.ToPtr("title2"),
				OwnerId:  lo.ToPtr("456"),
				DocType:  lo.ToPtr("sheet"),
			},
		},
		FailedList: []*larkdrive.MetaFailed{
			{
				Code:  lo.ToPtr(11111),
				Token: lo.ToPtr("1"),
			},
			{
				Code:  lo.ToPtr(22222),
				Token: lo.ToPtr("2"),
			},
		},
	}, nil).AnyTimes()
	larkCli.EXPECT().ListLarkUsers(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*larkcontact.User{
		{
			OpenId: lo.ToPtr("123"),
			Email:  lo.ToPtr("xiaoming"),
		},
		{
			OpenId: lo.ToPtr("456"),
			Email:  lo.ToPtr("lili"),
		},
	}, nil).AnyTimes()
	metas, err := loader.BatchLoadMeta(context.Background(), "123", true, []MetaOption{
		{
			SourceType: entity.DocumentSourceTypeLarkWiki,
			SourceUid:  "1",
		},
		{
			SourceType: entity.DocumentSourceTypeLarkSheet,
			SourceUid:  "2",
		},
		{
			SourceType: entity.DocumentSourceTypeLarkWiki,
			SourceUid:  "3",
		},
		{
			SourceType: entity.DocumentSourceTypeLarkSheet,
			SourceUid:  "4",
		},
	})
	assert.Nil(t, err)
	assert.Equal(t, 2, len(metas))
	assert.Equal(t, fmt.Sprintf("3"), metas[0].SourceUid)
	assert.Equal(t, fmt.Sprintf("11111"), metas[0].DocToken)
	assert.Equal(t, entity.DocumentContentTypeDoc, metas[0].ContentType)
	assert.Equal(t, fmt.Sprintf("xiaoming"), metas[0].Owner)
	assert.Equal(t, fmt.Sprintf("4"), metas[1].SourceUid)
	assert.Equal(t, fmt.Sprintf("22222"), metas[1].DocToken)
	assert.Equal(t, entity.DocumentContentTypeSheet, metas[1].ContentType)
	assert.Equal(t, fmt.Sprintf("lili"), metas[1].Owner)
}
