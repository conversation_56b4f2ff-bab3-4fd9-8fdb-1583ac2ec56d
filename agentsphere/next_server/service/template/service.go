package template

import (
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal"
	monitorservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/monitor"
	permissionservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/permission"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/service/space"
	runtimeservice "code.byted.org/devgpt/kiwis/agentsphere/runtime/service"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/uuid"
	"code.byted.org/devgpt/kiwis/port/redis"
	"code.byted.org/devgpt/kiwis/port/tos"
	"go.uber.org/fx"
)

type Service struct {
	idGen             uuid.Generator
	dao               *dal.DAO
	tccConf           *config.AgentSphereTCCConfig
	runtime           *runtimeservice.Service
	tos               tos.Client
	redis             redis.Client
	permissionService *permissionservice.Service
	spaceService      *space.Service
	monitorService    *monitorservice.Service
}

type CreateServiceOption struct {
	fx.In
	DAO               *dal.DAO
	TccConf           *config.AgentSphereTCCConfig
	Runtime           *runtimeservice.Service
	Tos               tos.Client
	Redis             redis.Client
	PermissionService *permissionservice.Service
	SpaceService      *space.Service
	MonitorService    *monitorservice.Service
}

func NewService(opt CreateServiceOption) (*Service, error) {
	return &Service{
		idGen:             uuid.GetDefaultGenerator(nil),
		dao:               opt.DAO,
		tccConf:           opt.TccConf,
		runtime:           opt.Runtime,
		tos:               opt.Tos,
		redis:             opt.Redis,
		permissionService: opt.PermissionService,
		spaceService:      opt.SpaceService,
		monitorService:    opt.MonitorService,
	}, nil
}
