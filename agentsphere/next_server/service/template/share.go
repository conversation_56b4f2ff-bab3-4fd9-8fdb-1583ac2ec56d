package template

import (
	"context"

	permissionservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/permission"
	"code.byted.org/gopkg/logs/v2/log"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	serverservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service"
	"code.byted.org/devgpt/kiwis/port/db"
	"github.com/pkg/errors"
)

// CreateShareTemplate 创建模板分享
func (s *Service) CreateShareTemplate(ctx context.Context, templateID, username string) (*entity.TemplateShare, error) {
	// 参数校验
	if templateID == "" || username == "" {
		return nil, errors.New("template id and username are required")
	}

	// 检查模板是否存在并且用户是否有权限分享
	template, err := s.dao.GetTemplateNormalVersionByTemplateID(ctx, templateID)
	if err != nil {
		if db.IsRecordNotFoundError(err) {
			return nil, serverservice.ErrTemplateNotFound
		}
		return nil, errors.WithMessage(err, "failed to get template")
	}
	if template == nil {
		return nil, serverservice.ErrTemplateNotFound
	}

	// 检查用户是否有权限分享模板（不可分享由他人分享给自己的模板）
	shareUser, err := s.dao.GetTemplateShareTargetByKey(ctx, templateID, username, false)
	if err != nil && !db.IsRecordNotFoundError(err) {
		return nil, errors.WithMessage(err, "failed to check if template is already shared to user")
	}
	if shareUser != nil {
		return nil, errors.New("cannot share a shared template")
	}

	// 检查是否已经创建过分享
	existingShare, err := s.dao.GetTemplateShareByKey(ctx, templateID, username, false)
	if err != nil && !db.IsRecordNotFoundError(err) {
		return nil, errors.WithMessage(err, "failed to check if template is already shared")
	}
	if existingShare != nil {
		return existingShare, nil // 已经分享过，直接返回
	}

	// 创建分享记录
	share := &entity.TemplateShare{
		ID:         s.idGen.NewID(),
		TemplateID: templateID,
		Username:   username,
	}
	share, err = s.dao.CreateTemplateShare(ctx, share)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to create template share")
	}

	return share, nil
}

func (s *Service) GetTemplateShareByID(ctx context.Context, shareID string) (*entity.TemplateShare, error) {
	if shareID == "" {
		return nil, errors.New("share id is required")
	}
	// 获取分享信息
	share, err := s.dao.GetTemplateShareByID(ctx, shareID, false)
	if err != nil {
		if db.IsRecordNotFoundError(err) {
			return nil, serverservice.ErrTemplateShareNotFound
		}
		return nil, errors.WithMessage(err, "failed to get template share")
	}
	return share, nil
}

// CreateTemplateShareTarget 将模板分享给指定用户
func (s *Service) CreateTemplateShareTarget(ctx context.Context, share *entity.TemplateShare, targetUsername string) (*entity.TemplateShareTarget, error) {
	// 参数校验
	if share == nil || targetUsername == "" {
		return nil, errors.New("share and target username are required")
	}

	// 检查模板是否存在
	exists, err := s.dao.IsTemplateIDExists(ctx, share.TemplateID)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get template")
	}
	if !exists {
		return nil, serverservice.ErrTemplateNotFound
	}

	// 检查是否已经分享给该用户，同一个模版以第一次的分享为准
	existingShareUser, err := s.dao.GetTemplateShareTargetByTemplateIDAndUsername(ctx, share.TemplateID, targetUsername)
	if err != nil && !db.IsRecordNotFoundError(err) {
		return nil, errors.WithMessage(err, "failed to check if template is already shared to user")
	}
	if existingShareUser != nil {
		return existingShareUser, nil // 已经分享给该用户，直接返回
	}

	// 创建分享用户记录
	shareUser := &entity.TemplateShareTarget{
		ID:       s.idGen.NewID(),
		ShareID:  share.ID,
		Username: targetUsername,
	}
	shareUser, err = s.dao.CreateTemplateShareTarget(ctx, shareUser)
	if err != nil {
		if db.IsDuplicateKeyError(err) { // 重新创建一个已删除的分享记录，此时恢复已有记录
			shareUser, err = s.dao.RestoreTemplateShareTargetByShareIDAndUsername(ctx, share.ID, targetUsername)
			if err != nil {
				return nil, errors.WithMessage(err, "failed to restore template share target")
			}
			return shareUser, nil
		}
		return nil, errors.WithMessage(err, "failed to create template share target")
	}
	// 为模板分享目标用户添加模板访问权限 TODO：需要考虑如何添加分享时可删除分享关系的权限
	_, err = s.permissionService.AddResourcePermission(ctx, permissionservice.AddResourcePermissionOption{
		ResourceID:         nil,
		ResourceExternalID: &share.TemplateID,
		ResourceType:       entity.ResourceTypeTemplate.Ptr(),
		PermissionMetas: []entity.PermissionMeta{{
			Type:       entity.PermissionTypeUser,
			ExternalID: targetUsername,
			Role:       entity.PermissionRoleVisitor,
		}},
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to add template permission to user: %v", err)
		// ignore error
	}

	return shareUser, nil
}

// DeleteTemplateShareTarget 删除模板分享给指定用户的记录
func (s *Service) DeleteTemplateShareTarget(ctx context.Context, shareID, targetUsername string) error {
	// 参数校验
	if shareID == "" || targetUsername == "" {
		return errors.New("share id and target username are required")
	}

	// 删除分享用户记录
	err := s.dao.DeleteTemplateShareTarget(ctx, shareID, targetUsername)
	if err != nil {
		return errors.WithMessage(err, "failed to delete template share user")
	}

	return nil
}
