package template

import (
	"context"

	"code.byted.org/gopkg/logs/v2/log"
	"github.com/pkg/errors"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	serverservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service"
	"code.byted.org/devgpt/kiwis/port/db"
)

// CreateTemplateStar 创建模板收藏
func (s *Service) CreateTemplateStar(ctx context.Context, templateID, username, spaceID string) (*entity.TemplateStar, error) {
	// 参数校验
	if templateID == "" || username == "" {
		return nil, errors.New("template id and username are required")
	}

	// 检查模板是否存在
	template, err := s.dao.GetTemplateNormalVersionByTemplateID(ctx, templateID)
	if err != nil {
		if db.IsRecordNotFoundError(err) {
			return nil, serverservice.ErrTemplateNotFound
		}
		return nil, errors.WithMessage(err, "failed to get template")
	}
	if template == nil {
		return nil, serverservice.ErrTemplateNotFound
	}

	spaceID, err = s.spaceService.MustGetSpaceIDWithDefault(ctx, spaceID, username)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get space id")
	}
	// 检查是否已经收藏过
	existingStar, err := s.dao.GetTemplateStarByKey(ctx, templateID, username, spaceID, false)
	if err != nil && !db.IsRecordNotFoundError(err) {
		return nil, errors.WithMessage(err, "failed to check if template is already starred")
	}
	if existingStar != nil {
		return existingStar, nil // 已经收藏过，直接返回
	}

	// 创建收藏记录
	star := &entity.TemplateStar{
		ID:         s.idGen.NewID(),
		TemplateID: templateID,
		Username:   username,
		SpaceID:    spaceID,
	}
	star, err = s.dao.CreateTemplateStar(ctx, star)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to create template star")
	}
	err = s.dao.IncrTemplateStarCount(ctx, templateID)
	if err != nil {
		log.V1.CtxError(ctx, "failed to incr template star count: %v", err)
	}
	return star, nil
}

// DeleteTemplateStar 删除模板收藏
func (s *Service) DeleteTemplateStar(ctx context.Context, templateID, username, spaceID string) error {
	// 参数校验
	if templateID == "" || username == "" {
		return errors.New("template id and username are required")
	}
	spaceID, err := s.spaceService.MustGetSpaceIDWithDefault(ctx, spaceID, username)
	if err != nil {
		return errors.WithMessage(err, "failed to get space id")
	}
	// 删除收藏记录
	err = s.dao.DeleteTemplateStar(ctx, templateID, username, spaceID)
	if err != nil {
		return errors.WithMessage(err, "failed to delete template star")
	}
	err = s.dao.DecrTemplateStarCount(ctx, templateID)
	if err != nil {
		log.V1.CtxError(ctx, "failed to decr template star count: %v", err)
	}
	return nil
}

// IsTemplateStarred 检查模板是否被用户收藏
func (s *Service) IsTemplateStarred(ctx context.Context, templateID, username, spaceID string) (bool, error) {
	// 参数校验
	if templateID == "" || username == "" {
		return false, errors.New("template id and username are required")
	}
	spaceID, err := s.spaceService.MustGetSpaceIDWithDefault(ctx, spaceID, username)
	if err != nil {
		return false, errors.WithMessage(err, "failed to get space id")
	}
	// 查询收藏记录
	star, err := s.dao.GetTemplateStarByKey(ctx, templateID, username, spaceID, false)
	if err != nil {
		if db.IsRecordNotFoundError(err) {
			return false, nil
		}
		return false, errors.WithMessage(err, "failed to check if template is starred")
	}

	return star != nil, nil
}
