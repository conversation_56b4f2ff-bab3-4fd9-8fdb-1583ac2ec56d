package mcp

import (
	"context"
	"fmt"
	"strings"

	"github.com/pkg/errors"
	"github.com/samber/lo"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	permissionservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/permission"
	spaceservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/space"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	authentity "code.byted.org/devgpt/kiwis/copilotstack/common/auth/entity"
	"code.byted.org/gopkg/logs/v2/log"
)

// ListSpaceMCPOption 列出MCP工具的参数
type ListSpaceMCPOption struct {
	SpaceID       string                   // 空间ID
	Name          *string                  // 按名称搜索
	IsActive      *bool                    // 按添加状态搜索
	Types         []entity.MCPType         // 按类型列表搜索
	User          *authentity.Account      // 按创建者搜索，用于筛选个人MCP
	SessionRole   *nextagent.SessionRole   // 按SessionRole搜索 (二期新增)
	MCPSourceTabs []nextagent.MCPSourceTab // 来源tab
	Limit         int64
	StartID       *int64
}

func (s *ServiceImpl) getListCustomMCPsOptions(ctx context.Context, opt *ListSpaceMCPOption, space *entity.Space) (*dal.ListMCPsOption, error) {
	if opt == nil || space == nil {
		return nil, errors.New("invalid argument")
	}
	var (
		listOpts = &dal.ListMCPsOption{
			Name:    opt.Name,
			Types:   opt.Types,
			Sources: []entity.MCPSource{entity.MCPSourceUserDefine},
		}
		spaceID = space.ID
	)
	if space.Type.IsPersonal() { // 个人空间内取：个人创建的MCP
		listOpts.Condition = fmt.Sprintf("(source_space_id='%s' AND creator='%s')", spaceID, opt.User.Username)
	} else { // 项目空间内取：项目内公开和个人创建的MCP
		spaceMCPIDs, err := s.getPermissionSpaceMCPIDs(ctx, opt.User, spaceID)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to get permission space mcp ids")
		}
		if len(spaceMCPIDs) > 0 {
			mcpIDsStr := strings.Join(lo.Map(spaceMCPIDs, func(id string, _ int) string {
				return "'" + id + "'"
			}), ",")
			listOpts.Condition = fmt.Sprintf("((source_space_id='%s' AND creator='%s') OR mcp_id IN (%s))", spaceID, opt.User.Username, mcpIDsStr)
		} else {
			listOpts.Condition = fmt.Sprintf("(source_space_id='%s' AND creator='%s')", spaceID, opt.User.Username)
		}
	}
	return listOpts, nil
}

// ListSpaceMCP 列出空间下的 MCP 工具
func (s *ServiceImpl) ListSpaceMCP(ctx context.Context, opt *ListSpaceMCPOption) ([]*entity.MCP, int64, error) {
	spaceID, err := s.spaceService.MustGetSpaceIDWithDefault(ctx, opt.SpaceID, opt.User.Username)
	if err != nil {
		return nil, 0, errors.WithMessage(err, "failed to get space id")
	}
	space, err := s.spaceService.GetSpace(ctx, spaceservice.GetSpaceOption{SpaceID: spaceID})
	if err != nil {
		return nil, 0, err
	}

	if len(opt.MCPSourceTabs) == 0 {
		return nil, 0, errors.New("empty tabs is not allowed")
	}
	// 1. 按条件筛选出MCP
	var allMCPs []*entity.MCP
	if lo.Contains(opt.MCPSourceTabs, nextagent.MCPSourceTab_Custom) {
		listOpts, err := s.getListCustomMCPsOptions(ctx, opt, space)
		if err != nil {
			return nil, 0, errors.WithMessage(err, "failed to get list custom mcp options")
		}
		mcps, _, err := s.dao.ListMCPs(ctx, listOpts)
		if err != nil {
			return nil, 0, errors.WithMessage(err, "failed to list mcps")
		}
		allMCPs = append(allMCPs, mcps...)
	}
	if lo.Contains(opt.MCPSourceTabs, nextagent.MCPSourceTab_Builtin) {
		listOpts := &dal.ListMCPsOption{
			Name:    opt.Name,
			Types:   opt.Types,
			Sources: []entity.MCPSource{entity.MCPSourceAIME, entity.MCPSourceCloud},
		}
		mcps, _, err := s.dao.ListMCPs(ctx, listOpts)
		if err != nil {
			return nil, 0, errors.WithMessage(err, "failed to list mcps")
		}
		allMCPs = append(allMCPs, mcps...)
	}
	allMCPs = lo.UniqBy(allMCPs, func(mcp *entity.MCP) string {
		return mcp.MCPID
	})

	// 2. 设置激活状态
	mcps, err := s.setMCPsActivationStatus(ctx, allMCPs, opt.User.Username, spaceID)
	if err != nil {
		return nil, 0, errors.WithMessage(err, "failed to set mcp activation status")
	}

	// 3. 根据IsActive过滤结果
	if opt.IsActive != nil {
		filteredMCPs := make([]*entity.MCP, 0, len(mcps))
		filterValue := *opt.IsActive
		for _, mcp := range mcps {
			// 只保留激活状态与过滤条件匹配的MCP
			if mcp.IsActive == filterValue {
				filteredMCPs = append(filteredMCPs, mcp)
			}
		}
		// 更新结果集合和总数
		mcps = filteredMCPs
		log.V1.CtxInfo(ctx, "[ListSpaceMCP] Filtered by IsActive=%v: %d items remaining", filterValue, len(filteredMCPs))
	}

	// 4. 根据SessionRole过滤结果 (二期新增) - Service层过滤
	if opt.SessionRole != nil {
		filteredMCPs := entity.FilterMCPsBySessionRole(mcps, *opt.SessionRole)
		mcps = filteredMCPs
		log.V1.CtxInfo(ctx, "[ListSpaceMCP] Filtered by SessionRole=%v: %d items remaining", *opt.SessionRole, len(filteredMCPs))
	}

	// 5. 在Service层进行排序
	mcps = s.sortMCPsByPriority(mcps, int(opt.Limit), lo.FromPtr(opt.StartID))
	var nextID int64
	if len(mcps) > 0 && len(mcps) == int(opt.Limit) {
		nextID = mcps[len(mcps)-1].ID
	}

	// 6. 拼接权限信息
	resources, err := s.permissionService.GetUserPermissions(ctx, permissionservice.GetUserPermissionsOption{
		Account: lo.FromPtr(opt.User),
		Roles:   nil,
		Actions: []entity.PermissionAction{entity.PermissionActionMCPCreate, entity.PermissionActionMCPRead,
			entity.PermissionActionMCPUpdate, entity.PermissionActionMCPDelete},
		ResourceType: entity.ResourceTypeMCP,
		GroupID:      &space.ID,
	})
	if err != nil {
		log.V1.CtxError(ctx, "[ListSpaceMCP] failed to get user permission resources: %v", err)
		// 不返回错误，继续执行，忽略分享模板
	}
	resourcePermissionMap := make(map[string][]entity.PermissionAction)
	for _, resource := range resources {
		actions := make([]entity.PermissionAction, 0)
		for _, permission := range resource.Permissions {
			actions = append(actions, permission.PermissionActions...)
		}
		resourcePermissionMap[resource.ExternalID] = lo.Uniq(actions)
	}
	for _, mcp := range mcps {
		mcp.Permissions = resourcePermissionMap[mcp.MCPID]
	}
	log.V1.CtxInfo(ctx, "[ListSpaceMCP] success: total=%d", len(mcps))
	return mcps, nextID, nil
}

// CountSpaceMCP 统计空间下的 MCP 工具
func (s *ServiceImpl) CountSpaceMCP(ctx context.Context, opt *ListSpaceMCPOption) (*entity.MCPCount, error) {
	spaceID, err := s.spaceService.MustGetSpaceIDWithDefault(ctx, opt.SpaceID, opt.User.Username)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get space id")
	}
	space, err := s.spaceService.GetSpace(ctx, spaceservice.GetSpaceOption{SpaceID: spaceID})
	if err != nil {
		return nil, err
	}

	// 1. 统计自定义MCP数量
	var mcpCount = &entity.MCPCount{}
	customListOpts, err := s.getListCustomMCPsOptions(ctx, opt, space)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get list custom mcp options")
	}
	customMCPs, err := s.dao.ListMCPActives(ctx, customListOpts)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to list mcps")
	}
	mcpCount.CustomCount = int64(len(customMCPs))

	// 2. 统计公开MCP数量
	publicListOpts := &dal.ListMCPsOption{
		Name:    opt.Name,
		Types:   opt.Types,
		Sources: []entity.MCPSource{entity.MCPSourceAIME, entity.MCPSourceCloud},
	}
	publicMCPs, err := s.dao.ListMCPActives(ctx, publicListOpts)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to list mcps")
	}
	mcpCount.PublicCount = int64(len(publicMCPs))

	// 3. 统计已激活的MCP数量
	// 获取当前用户可见的所有MCP
	var mcps []*entity.MCP
	mcps = append(mcps, customMCPs...)
	mcps = append(mcps, publicMCPs...)
	mcps = lo.UniqBy(mcps, func(item *entity.MCP) string {
		return item.MCPID
	})
	// 设置激活状态
	mcps, err = s.setMCPsActivationStatus(ctx, mcps, opt.User.Username, spaceID)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to set mcp activation status")
	}
	mcpCount.ActivateCount = int64(lo.CountBy(mcps, func(item *entity.MCP) bool {
		return item.IsActive
	})) // 计算已激活的MCP总数
	mcpCount.ActivateLimit = entity.MCPActivateLimit
	return mcpCount, nil
}

// getPermissionSpaceMCPIDs 从权限表中获取该用户有权限访问的 MCP ID 列表
func (s *ServiceImpl) getPermissionSpaceMCPIDs(ctx context.Context, user *authentity.Account, spaceID string) ([]string, error) {
	resources, err := s.permissionService.GetUserPermissions(ctx, permissionservice.GetUserPermissionsOption{
		Account:      lo.FromPtr(user),
		Roles:        nil,
		Actions:      nil,
		ResourceType: entity.ResourceTypeMCP,
		GroupID:      &spaceID,
	})
	if err != nil {
		log.V1.CtxError(ctx, "[getPermissionSpaceMCPIDs] failed to get user permission resources: %v", err)
		return nil, err
	}
	var mcpIDs []string // 过滤出不是自己创建的MCP
	for _, resource := range resources {
		if resource.Owner == user.Username {
			continue
		}
		mcpIDs = append(mcpIDs, resource.ExternalID)
	}
	return mcpIDs, nil
}
