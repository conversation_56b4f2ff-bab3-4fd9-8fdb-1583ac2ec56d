package mcp

import (
	"fmt"
	"testing"
)

func Test_replaceSpecialChars(t *testing.T) {
	// 测试示例
	testCases := []string{
		"hello@world-!123",                       // 包含@和!
		"你好，世界！",                                 // 包含中文和中文标点
		"123#abc$测试",                             // 包含#和$
		"test_case 123",                          // 包含下划线和空格
		"special~chars%here",                     // 包含~和%
		"@potoo/next MCP.good.case (Streamable)", // 包含.
		"one+特征、mcp服务",                           // 包含+
	}

	for _, tc := range testCases {
		fmt.Printf("原字符串: %s\n", tc)
		fmt.Printf("替换后: %s\n", replaceSpecialChars(tc))
		fmt.Println("----------------")
	}
}
