package mcp

import (
	"context"
	"fmt"
	"regexp"
	"sort"

	authentity "code.byted.org/devgpt/kiwis/copilotstack/common/auth/entity"

	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/lang/gg/choose"
	"code.byted.org/lang/gg/gptr"
	"github.com/pkg/errors"
	"github.com/samber/lo"

	mcptool "code.byted.org/devgpt/kiwis/agentsphere/agents/actions/mcp"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	serverservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service"
	permissionservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/permission"
	spaceservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/space"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	"code.byted.org/devgpt/kiwis/lib/uuid"
)

// Service 是 MCP 服务接口
type Service interface {
	// CreateMCP 创建 MCP 工具
	CreateMCP(ctx context.Context, opt *CreateMCPOption) (*entity.MCP, error)
	// UpdateMCP 更新 MCP 工具
	UpdateMCP(ctx context.Context, opt *UpdateMCPOption) (*entity.MCP, error)
	// ListMCP 列出 MCP 工具
	ListMCP(ctx context.Context, opt *ListMCPOption) ([]*entity.MCP, int32, error)
	// ListSpaceMCP 列出空间下的 MCP 工具
	ListSpaceMCP(ctx context.Context, opt *ListSpaceMCPOption) ([]*entity.MCP, int64, error)
	// CountSpaceMCP 统计空间下的 MCP 工具
	CountSpaceMCP(ctx context.Context, opt *ListSpaceMCPOption) (*entity.MCPCount, error)
	// GetMCPsByKeys 根据MCPKeys获取MCP工具列表
	GetMCPsByKeys(ctx context.Context, opt *GetMCPsByKeysOption) ([]*entity.MCP, error)
	// ModifyMCPActivation 修改MCP工具的激活状态（激活或取消激活）
	ModifyMCPActivation(ctx context.Context, username string, mcpID string, source entity.MCPSource, status entity.ActiveStatus, spaceID string) (*entity.MCP, error)
	// GetMCPByID 根据ID获取MCP工具
	GetMCPByID(ctx context.Context, id string, source entity.MCPSource) (*entity.MCP, error)
	// GetUserDeactivatedAIMEMCPsMap 获取用户取消激活的AIME来源MCP ID映射
	GetUserDeactivatedAIMEMCPsMap(ctx context.Context, username string) (map[string]bool, error)
	// ValidateMCP 验证MCP工具配置是否有效，仅校验个人来源
	ValidateMCP(ctx context.Context, opt *ValidateMCPOption) bool
}

// CreateMCPOption 创建MCP工具的参数
type CreateMCPOption struct {
	UID            *string                 // 工具唯一id，注：来源不同id可能冲突
	Name           string                  // MCP工具名称
	EnName         string                  // MCP 英文名称
	Description    string                  // MCP工具描述
	EnDescription  string                  // MCP工具英文描述
	IconURL        string                  // MCP工具图标URL
	Config         *entity.MCPConfig       // MCP工具配置结构
	Creator        string                  // 创建者
	Source         entity.MCPSource        // 来源
	Type           entity.MCPType          // MCP工具类型
	ForceActive    bool                    // 是否强制激活
	Token          string                  // 创建mcp时需要token校验mcp
	LarkUserOpenID string                  // 验证mcp时需要lark user open id
	SessionRoles   []nextagent.SessionRole // 支持的SessionRole列表 (二期新增)
	SpaceID        string                  // 空间ID
	Scope          entity.MCPScope         // 可见范围
}

// UpdateMCPOption 更新MCP工具的参数
type UpdateMCPOption struct {
	MCPID          string           // MCP工具ID
	Source         entity.MCPSource // 来源
	Name           *string          // MCP工具名称
	EnName         *string
	Description    *string                 // MCP工具描述
	EnDescription  *string                 // MCP工具英文描述
	IconURL        *string                 // MCP工具图标URL
	Config         *entity.MCPConfig       // MCP工具配置结构
	Type           *entity.MCPType         // MCP工具类型
	ForceActive    *bool                   // 是否强制激活
	Token          string                  // 更新mcp时需要token校验mcp
	LarkUserOpenID string                  // 验证mcp时需要lark user open id
	SessionRoles   []nextagent.SessionRole // 支持的SessionRole列表 (二期新增)
	Scope          entity.MCPScope
	Username       string
}

// FromMCP 从现有MCP创建一个临时对象，应用更新选项的字段
func (opt *UpdateMCPOption) FromMCP(mcp *entity.MCP) *entity.MCP {
	// 创建MCP的副本
	newMCP := &entity.MCP{
		MCPKey: entity.MCPKey{
			MCPID:  mcp.MCPID,
			Source: mcp.Source,
		},
		Name:          mcp.Name,
		EnName:        mcp.EnName,
		Description:   mcp.Description,
		EnDescription: mcp.EnDescription,
		IconURL:       mcp.IconURL,
		Config:        mcp.Config,
		Creator:       mcp.Creator,
		Type:          mcp.Type,
		ForceActive:   mcp.ForceActive,
		CreatedAt:     mcp.CreatedAt,
		UpdatedAt:     mcp.UpdatedAt,
		IsActive:      mcp.IsActive,
		SessionRoles:  mcp.SessionRoles,
	}

	// 应用更新选项中的字段
	if opt.Name != nil {
		newMCP.Name = *opt.Name
	}
	if opt.EnName != nil {
		newMCP.EnName = *opt.EnName
	}
	if opt.Description != nil {
		newMCP.Description = *opt.Description
	}
	if opt.EnDescription != nil {
		newMCP.EnDescription = *opt.EnDescription
	}
	if opt.IconURL != nil {
		newMCP.IconURL = *opt.IconURL
	}
	if opt.Type != nil {
		newMCP.Type = *opt.Type
	}
	if opt.ForceActive != nil {
		newMCP.ForceActive = *opt.ForceActive
	}
	if opt.Config != nil {
		newMCP.Config = *opt.Config
	}
	if opt.Scope > 0 {
		newMCP.Scope = opt.Scope
	}
	newMCP.SessionRoles = opt.SessionRoles

	return newMCP
}

// ListMCPOption 列出MCP工具的参数
type ListMCPOption struct {
	Name        *string                // 按名称搜索
	Sources     []entity.MCPSource     // 按来源列表搜索
	IsActive    *bool                  // 按添加状态搜索
	Types       []entity.MCPType       // 按类型列表搜索
	Creator     *string                // 按创建者搜索，用于筛选个人MCP
	SessionRole *nextagent.SessionRole // 按SessionRole搜索 (二期新增)
}

// GetMCPsByKeysOption 根据MCPKeys获取MCP工具的参数
type GetMCPsByKeysOption struct {
	MCPKeys []*entity.MCPKey // 要查询的MCP键列表
}

var (
	// ErrMCPNotFound 表示 MCP 服务不存在
	ErrMCPNotFound = errors.New("mcp not found")
	// ErrMCPUnavailable 表示 MCP 服务不可用
	ErrMCPUnavailable = errors.New("mcp server is unavailable")
	// ErrMCPActiveLimit 表示命中mcp激活上限
	ErrMCPActiveLimit = errors.New("the number of activated mcp services exceeds the limit")
)

// ServiceImpl 是 MCP 服务的实现
type ServiceImpl struct {
	dao               *dal.DAO
	idGen             uuid.Generator
	spaceService      *spaceservice.Service
	permissionService *permissionservice.Service
}

// NewService 创建一个新的 MCP 服务实例
func NewService(dao *dal.DAO, spaceService *spaceservice.Service, permissionService *permissionservice.Service) Service {
	return &ServiceImpl{
		dao:               dao,
		idGen:             uuid.GetDefaultGenerator(nil),
		spaceService:      spaceService,
		permissionService: permissionService,
	}
}

// CreateMCP 创建 MCP 工具
func (s *ServiceImpl) CreateMCP(ctx context.Context, opt *CreateMCPOption) (*entity.MCP, error) {
	log.V1.CtxInfo(ctx, "[MCP] CreateMCP request: %#v", opt)
	spaceID, err := s.spaceService.MustGetSpaceIDWithDefault(ctx, opt.SpaceID, opt.Creator)
	if err != nil {
		return nil, err
	}
	// 将请求转换为 MCP 实体
	mcp := &entity.MCP{
		MCPKey: entity.MCPKey{
			MCPID:  gptr.Indirect(opt.UID),
			Source: opt.Source,
		},
		Name:          opt.Name,
		EnName:        opt.EnName,
		Description:   opt.Description,
		EnDescription: opt.EnDescription,
		IconURL:       opt.IconURL,
		Config:        *opt.Config,
		Creator:       opt.Creator,
		Type:          opt.Type,
		ForceActive:   opt.ForceActive,
		SessionRoles:  opt.SessionRoles,
		SourceSpaceID: spaceID,
		Scope:         opt.Scope,
	}
	// 如果uid为空
	if len(mcp.MCPID) == 0 {
		mcp.MCPID = s.idGen.NewID()
	}

	// 校验mcp是否可用
	valid := s.ValidateMCP(ctx, &ValidateMCPOption{
		Config:         opt.Config,
		Source:         opt.Source,
		Type:           opt.Type,
		Token:          opt.Token,
		LarkUserOpenID: opt.LarkUserOpenID,
	})
	if !valid {
		return nil, ErrMCPUnavailable
	}
	// 校验可见范围
	var (
		status        entity.ResourceStatus
		isSpacePublic *bool
	)
	space, err := s.spaceService.GetSpace(ctx, spaceservice.GetSpaceOption{
		SpaceID: spaceID,
		Sync:    true,
	})
	if err != nil {
		return nil, err
	}
	if space.Type.IsPersonal() {
		mcp.Scope = lo.Ternary(opt.Scope == entity.MCPScopePublic, entity.MCPScopePublic, entity.MCPScopePrivate)
		status = lo.Ternary(opt.Scope == entity.MCPScopePublic, entity.ResourceStatusPublic, entity.ResourceStatusPrivate)
	} else { // 项目空间内单独处理可见范围
		mcp.Scope = lo.Ternary(opt.Scope == entity.MCPScopeProjectPublic, entity.MCPScopeProjectPublic, entity.MCPScopePrivate)
		status = entity.ResourceStatusPrivate
		if opt.Scope == entity.MCPScopeProjectPublic {
			isSpacePublic = lo.ToPtr(true)
		}
	}
	// 调用 DAL 层创建 MCP
	createdMCP, err := s.dao.CreateMCP(ctx, mcp)
	if err != nil {
		log.V1.CtxError(ctx, "[MCP] CreateMCP error: %v", err)
		return nil, errors.Wrap(err, "failed to create mcp")
	}
	_, err = s.permissionService.CreateResource(ctx, permissionservice.CreateResourceOption{
		Owner:         opt.Creator,
		Type:          entity.ResourceTypeMCP,
		ExternalID:    createdMCP.MCPID,
		Status:        &status,
		GroupID:       &opt.SpaceID,
		IsSpacePublic: isSpacePublic,
	})
	if err != nil {
		return nil, errors.Wrap(err, "failed to create resource for mcp")
	}
	log.V1.CtxInfo(ctx, "[MCP] CreateMCP success: %s", createdMCP.MCPID)
	return createdMCP, nil
}

// UpdateMCP 更新 MCP 工具
func (s *ServiceImpl) UpdateMCP(ctx context.Context, opt *UpdateMCPOption) (*entity.MCP, error) {
	log.V1.CtxInfo(ctx, "[MCP] UpdateMCP request: %#v", opt)

	// 检查 MCP 是否存在
	existingMCP, err := s.dao.GetMCPByID(ctx, opt.MCPID, opt.Source)
	if err != nil {
		log.V1.CtxError(ctx, "[MCP] GetMCPByID error: %v", err)
		return nil, errors.Wrap(err, "failed to get mcp")
	}

	if existingMCP == nil {
		log.V1.CtxError(ctx, "[MCP] MCP not found: %s, source: %d", opt.MCPID, opt.Source)
		return nil, ErrMCPNotFound
	}

	tempValidateMCP := opt.FromMCP(existingMCP)
	// 校验mcp是否可用
	valid := s.ValidateMCP(ctx, &ValidateMCPOption{
		Config:         &tempValidateMCP.Config,
		Source:         tempValidateMCP.Source,
		Type:           tempValidateMCP.Type,
		Token:          opt.Token,
		LarkUserOpenID: opt.LarkUserOpenID,
	})
	if !valid {
		return nil, ErrMCPUnavailable
	}
	spaceID, err := s.spaceService.MustGetSpaceIDWithDefault(ctx, existingMCP.SourceSpaceID, opt.Username)
	if err != nil {
		return nil, err
	}
	// 构造DAL层更新选项
	dalOpt := &dal.UpdateMCPOption{
		MCPID:         opt.MCPID,
		Source:        opt.Source,
		Name:          opt.Name,
		EnName:        opt.EnName,
		Description:   opt.Description,
		EnDescription: opt.EnDescription,
		IconURL:       opt.IconURL,
		Type:          opt.Type,
		ForceActive:   opt.ForceActive,
		Config:        opt.Config,
		SessionRoles:  opt.SessionRoles,
		Scope:         opt.Scope,
	}
	space, err := s.spaceService.GetSpace(ctx, spaceservice.GetSpaceOption{SpaceID: spaceID})
	if err != nil {
		return nil, err
	}
	// 项目空间内不允许设置为全局公开
	if existingMCP.Scope != opt.Scope && opt.Scope == entity.MCPScopePublic && space.Type.IsProject() {
		return nil, serverservice.ErrNotAllowedSetToPublic
	}

	// 调用 DAL 层更新 MCP
	err = s.dao.UpdateMCP(ctx, dalOpt)
	if err != nil {
		log.V1.CtxError(ctx, "[MCP] UpdateMCP error: %v", err)
		if err.Error() == "mcp not found" {
			return nil, ErrMCPNotFound
		}
		return nil, errors.Wrap(err, "failed to update mcp")
	}

	// 使用FromMCP创建更新后的MCP实体并返回，无需再次查询数据库
	updatedMCP := opt.FromMCP(existingMCP)
	// 更新MCP资源和权限
	if existingMCP.Scope != opt.Scope {
		var o = permissionservice.UpdateResourceOptions{
			ResourceID:         nil,
			ResourceExternalID: &existingMCP.MCPID,
			ResourceType:       entity.ResourceTypeMCP.Ptr(),
			Owner:              &opt.Username,
			Status:             nil,
			IsSpacePublic:      nil,
		}
		if space.Type.IsProject() {
			o.IsSpacePublic = lo.ToPtr(lo.Ternary(opt.Scope == entity.MCPScopeProjectPublic, true, false))
		} else {
			o.Status = lo.ToPtr(lo.Ternary(opt.Scope == entity.MCPScopePublic, entity.ResourceStatusPublic, entity.ResourceStatusPrivate))
		}
		// 更新资源及权限
		_, err = s.permissionService.UpdateResource(ctx, o)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to update template resource")
		}
	}
	log.V1.CtxInfo(ctx, "[MCP] UpdateMCP success: %s", opt.MCPID)
	return updatedMCP, nil
}

// ListMCP 列出 MCP 工具
func (s *ServiceImpl) ListMCP(ctx context.Context, opt *ListMCPOption) ([]*entity.MCP, int32, error) {
	log.V1.CtxInfo(ctx, "[MCP] ListMCP request: %#v", opt)

	// 1. 处理筛选条件，分别查询公共和个人MCP并合并结果
	mcps, total, err := s.queryAllMCPs(ctx, opt)
	if err != nil {
		return nil, 0, err
	}

	// 2. 设置激活状态
	if opt.Creator != nil {
		mcps, err = s.setMCPsActivationStatus(ctx, mcps, *opt.Creator, "")
		if err != nil {
			return nil, 0, err
		}
	}

	// 3. 根据IsActive过滤结果
	if opt.IsActive != nil {
		filteredMCPs := make([]*entity.MCP, 0, len(mcps))
		filterValue := *opt.IsActive
		for _, mcp := range mcps {
			// 只保留激活状态与过滤条件匹配的MCP
			if mcp.IsActive == filterValue {
				filteredMCPs = append(filteredMCPs, mcp)
			}
		}
		// 更新结果集合和总数
		mcps = filteredMCPs
		total = int32(len(filteredMCPs))
		log.V1.CtxInfo(ctx, "[MCP] Filtered by IsActive=%v: %d items remaining", filterValue, total)
	}

	// 4. 根据SessionRole过滤结果 (二期新增) - Service层过滤
	if opt.SessionRole != nil {
		filteredMCPs := entity.FilterMCPsBySessionRole(mcps, *opt.SessionRole)
		mcps = filteredMCPs
		total = int32(len(filteredMCPs))
		log.V1.CtxInfo(ctx, "[MCP] Filtered by SessionRole=%v: %d items remaining", *opt.SessionRole, total)
	}

	// 5. 在Service层进行排序
	s.sortMCPsByPriority(mcps, -1, 0)

	log.V1.CtxInfo(ctx, "[MCP] ListMCP success: total=%d", total)
	return mcps, total, nil
}

// queryAllMCPs 查询所有满足条件的MCP，确保个人MCP只返回当前用户创建的
func (s *ServiceImpl) queryAllMCPs(ctx context.Context, opt *ListMCPOption) ([]*entity.MCP, int32, error) {
	// 查询公共MCP
	publicMCPs, publicTotal, err := s.queryPublicMCPs(ctx, opt)
	if err != nil {
		return nil, 0, err
	}

	// 查询个人MCP（如果有创建者信息）
	var personalMCPs []*entity.MCP
	var personalTotal int32
	if opt.Creator != nil {
		personalMCPs, personalTotal, err = s.queryPersonalMCPs(ctx, opt, *opt.Creator)
		if err != nil {
			return nil, 0, err
		}
	}

	// 合并结果
	allMCPs := append(publicMCPs, personalMCPs...)
	totalCount := publicTotal + personalTotal

	log.V1.CtxInfo(ctx, "[MCP] Query result: total=%d (public=%d, personal=%d)",
		totalCount, publicTotal, personalTotal)
	return allMCPs, totalCount, nil
}

// queryPublicMCPs 查询公共MCP（非个人创建）
func (s *ServiceImpl) queryPublicMCPs(ctx context.Context, opt *ListMCPOption) ([]*entity.MCP, int32, error) {
	// 确定要查询的公共来源
	var publicSources []entity.MCPSource

	if len(opt.Sources) == 0 {
		// 如果未指定来源，默认查询所有公共来源（除个人外）
		publicSources = []entity.MCPSource{
			entity.MCPSourceAIME,
			entity.MCPSourceCloud,
			// 可添加其他非个人来源
		}
	} else {
		// 从指定来源中筛选出公共来源
		for _, source := range opt.Sources {
			if source != entity.MCPSourceUserDefine {
				publicSources = append(publicSources, source)
			}
		}
	}

	// 如果没有公共来源要查询，直接返回空结果
	if len(publicSources) == 0 {
		return []*entity.MCP{}, 0, nil
	}

	// 构建查询选项
	listOpt := &dal.ListMCPsOption{
		Name:    opt.Name,
		Sources: publicSources,
		Types:   opt.Types,
		// 公共MCP不需要指定Creator
		// SessionRole过滤移到Service层处理
	}

	// 执行查询
	mcps, total, err := s.dao.ListMCPs(ctx, listOpt)
	if err != nil {
		log.V1.CtxError(ctx, "[MCP] ListMCPs public sources error: %v", err)
		return nil, 0, errors.Wrap(err, "failed to list public mcps")
	}

	log.V1.CtxInfo(ctx, "[MCP] Query result (public sources): total=%d", total)
	return mcps, total, nil
}

// queryPersonalMCPs 查询个人MCP（仅当前用户创建的）
func (s *ServiceImpl) queryPersonalMCPs(ctx context.Context, opt *ListMCPOption, username string) ([]*entity.MCP, int32, error) {
	// 检查是否需要查询个人来源
	var needQueryPersonal bool

	if len(opt.Sources) == 0 {
		// 如果未指定来源，默认也查询个人来源
		needQueryPersonal = true
	} else {
		// 检查指定的来源中是否包含个人来源
		for _, source := range opt.Sources {
			if source == entity.MCPSourceUserDefine {
				needQueryPersonal = true
				break
			}
		}
	}

	// 如果不需要查询个人来源，直接返回空结果
	if !needQueryPersonal {
		return []*entity.MCP{}, 0, nil
	}

	// 构建查询选项
	listOpt := &dal.ListMCPsOption{
		Name:    opt.Name,
		Sources: []entity.MCPSource{entity.MCPSourceUserDefine},
		Types:   opt.Types,
		Creator: &username, // 必须指定创建者，确保只返回当前用户的个人MCP
		// SessionRole过滤移到Service层处理
	}

	// 执行查询
	mcps, total, err := s.dao.ListMCPs(ctx, listOpt)
	if err != nil {
		log.V1.CtxError(ctx, "[MCP] ListMCPs personal error: %v", err)
		return nil, 0, errors.Wrap(err, "failed to list personal mcps")
	}

	log.V1.CtxInfo(ctx, "[MCP] Query result (personal source): total=%d", total)
	return mcps, total, nil
}

// setMCPsActivationStatus 设置MCP的激活状态
func (s *ServiceImpl) setMCPsActivationStatus(ctx context.Context, mcps []*entity.MCP, username, spaceID string) ([]*entity.MCP, error) {
	// 获取用户激活的MCP，一般不多，所以直接全查出来
	activatedMCPs, err := s.dao.GetUserActivatedMCPs(ctx, username, spaceID)
	if err != nil {
		log.V1.CtxError(ctx, "[MCP] GetUserActivatedMCPs error: %v", err)
		return nil, errors.Wrap(err, "failed to get user activated mcps")
	}

	// 构建激活状态映射
	activeMap := make(map[entity.MCPSource]map[string]bool)
	for _, activeMCP := range activatedMCPs {
		if activeMap[activeMCP.Source] == nil {
			activeMap[activeMCP.Source] = map[string]bool{}
		}
		activeMap[activeMCP.Source][activeMCP.MCPID] = true
	}

	// 获取用户取消激活的AIMEMCP
	deactivatedMap, err := s.GetUserDeactivatedAIMEMCPsMap(ctx, username)
	if err != nil {
		log.V1.CtxError(ctx, "[MCP] GetUserDeactivatedAIMEMCPsMap error: %v", err)
		return nil, errors.Wrap(err, "failed to get user deactivated AIME mcps")
	}

	// 设置激活状态
	for i := range mcps {
		mcp := mcps[i]

		// 激活优先级：ForceActive > UserMCP设置了Active的MCP > AIME默认激活（但取消激活的AIME MCP除外）

		// 1. 如果MCP设置了ForceActive为true，则总是显示为激活状态
		if mcp.ForceActive {
			mcp.IsActive = true
		} else if activeMap[mcp.Source][mcp.MCPID] {
			// 2. 如果用户明确激活了这个MCP，则显示为激活状态
			mcp.IsActive = true
		} else if mcp.Source == entity.MCPSourceAIME && !deactivatedMap[mcp.MCPID] {
			// 3. 如果是AIME MCP并且用户没有取消激活，则默认为激活状态
			mcp.IsActive = true
		} else {
			// 4. 其他情况（Cloud或UserDefine），默认为非激活状态
			mcp.IsActive = false
		}
	}

	log.V1.CtxInfo(ctx, "[MCP] Set activation status for %d MCPs, user has %d activated MCPs, %d deactivated MCPs",
		len(mcps), len(activatedMCPs), len(deactivatedMap))
	return mcps, nil
}

// sortMCPsByPriority 按优先级排序MCP列表并返回, limit 小于 0 代表不限制数量
func (s *ServiceImpl) sortMCPsByPriority(mcps []*entity.MCP, limit int, startID int64) []*entity.MCP {
	if len(mcps) == 0 {
		return nil
	}
	// 在内存中进行排序 - 按照以下规则:
	// 1. 已添加 > 未添加
	// 2. AIME > UserDefine > Cloud
	// 3. 同来源下按创建时间升序
	sort.Slice(mcps, func(i, j int) bool {
		// 优先级1: 已添加的排在前面
		if mcps[i].IsActive != mcps[j].IsActive {
			return mcps[i].IsActive
		}

		// 优先级2: 按来源排序 AIME(1) > UserDefine(2) > Cloud(3)
		if mcps[i].Source != mcps[j].Source {
			return mcps[i].Source < mcps[j].Source
		}

		// 优先级3: 相同来源按创建时间升序
		return mcps[i].CreatedAt.Before(mcps[j].CreatedAt)
	})

	log.V1.CtxDebug(context.Background(), "[MCP] Sorted %d MCPs by priority", len(mcps))
	// 过滤出排列在 start id 之后的MCP
	part := make([]*entity.MCP, 0, len(mcps))
	if startID > 0 {
		start := false
		for _, v := range mcps {
			if v.ID == startID {
				start = true
				continue
			}
			if start {
				part = append(part, v)
			}
		}
	} else {
		part = mcps
	}
	if len(part) == 0 {
		return nil
	}
	var end int
	if limit <= 0 {
		end = len(part)
	} else {
		end = lo.Ternary(limit < len(part), limit, len(part))
	}
	return part[:end]
}

// GetMCPsByKeys 根据MCPKeys获取MCP工具列表
func (s *ServiceImpl) GetMCPsByKeys(ctx context.Context, opt *GetMCPsByKeysOption) ([]*entity.MCP, error) {
	log.V1.CtxInfo(ctx, "[MCP] GetMCPsByKeys request: %d keys", len(opt.MCPKeys))

	if len(opt.MCPKeys) == 0 {
		return []*entity.MCP{}, nil
	}

	// 1. 按来源分组MCPKeys以加速查询
	keysBySource := make(map[entity.MCPSource][]string)
	for _, key := range opt.MCPKeys {
		if key == nil {
			continue
		}
		keysBySource[key.Source] = append(keysBySource[key.Source], key.MCPID)
	}

	// 2. 按来源分别查询MCP
	var allMCPs []*entity.MCP
	for source, mcpIDs := range keysBySource {
		mcps, err := s.dao.GetMCPsByIDsAndSource(ctx, mcpIDs, source)
		if err != nil {
			log.V1.CtxError(ctx, "[MCP] GetMCPsByIDsAndSource error for source %d: %v", source, err)
			return nil, errors.Wrap(err, "failed to get mcps by ids and source")
		}
		allMCPs = append(allMCPs, mcps...)
	}

	// 3. 组装成map，以MCPKey为键
	mcpMap := make(map[entity.MCPKey]*entity.MCP)
	for _, mcp := range allMCPs {
		key := entity.MCPKey{
			MCPID:  mcp.MCPID,
			Source: mcp.Source,
		}
		mcpMap[key] = mcp
	}

	// 4. 按照opt.MCPKeys的顺序取出结果
	result := make([]*entity.MCP, 0, len(opt.MCPKeys))
	for _, key := range opt.MCPKeys {
		if key == nil {
			continue
		}
		if mcp, exists := mcpMap[*key]; exists {
			result = append(result, mcp)
		}
	}

	log.V1.CtxInfo(ctx, "[MCP] GetMCPsByKeys success: found %d MCPs", len(result))
	return result, nil
}

// ModifyMCPActivation 修改MCP工具的激活状态（激活或取消激活）
func (s *ServiceImpl) ModifyMCPActivation(ctx context.Context, username string, mcpID string, source entity.MCPSource,
	status entity.ActiveStatus, spaceID string) (*entity.MCP, error) {
	log.V1.CtxInfo(ctx, "[MCP] ModifyMCPActivation request: username=%s, mcpID=%v, status=%d", username, mcpID, status)

	spaceID, err := s.spaceService.MustGetSpaceIDWithDefault(ctx, spaceID, username)
	if err != nil {
		log.V1.CtxError(ctx, "[MCP] MustGetSpaceIDWithDefault error: %v", err)
		return nil, err
	}
	// 先获取MCP信息
	mcp, err := s.dao.GetMCPByID(ctx, mcpID, source)
	if err != nil {
		log.V1.CtxError(ctx, "[MCP] GetMCPByID error: %v", err)
		return nil, errors.Wrap(err, "failed to get mcp")
	}
	if mcp == nil {
		log.V1.CtxError(ctx, "[MCP] MCP not found: %d", mcpID)
		return nil, ErrMCPNotFound
	}

	// 强制激活的不让取消
	if mcp.Source == entity.MCPSourceAIME && mcp.ForceActive && status == entity.ActiveStatusDeactivate {
		return nil, errors.New("this mcp prohibit this operation")
	}
	// 校验激活上限
	if status == entity.ActiveStatusActivate {
		if spaceID != "" {
			count, err := s.CountSpaceMCP(ctx, &ListSpaceMCPOption{
				SpaceID: spaceID,
				User:    &authentity.Account{Username: username},
			})
			if err != nil {
				log.V1.CtxError(ctx, "[MCP] CountSpaceMCP error: %v", err)
				return nil, errors.Wrap(err, "failed to count space mcps")
			}
			if count.ActivateCount >= entity.MCPActivateLimit {
				return nil, ErrMCPActiveLimit
			}
		} else {
			_, total, err := s.ListMCP(ctx, &ListMCPOption{
				IsActive: gptr.Of(true),
				Creator:  gptr.Of(username),
			})
			if err != nil {
				log.V1.CtxError(ctx, "[MCP] ListMCPs error: %v", err)
				return nil, errors.Wrap(err, "failed to list active mcps")
			}
			if total >= entity.MCPActivateLimit {
				return nil, ErrMCPActiveLimit
			}
		}
	}

	_, err = s.dao.ModifyMCPActivation(ctx, username, mcpID, status, source, spaceID)
	if err != nil {
		log.V1.CtxError(ctx, "[MCP] ModifyMCPActivation error: %v", err)
		return nil, errors.Wrap(err, "failed to deactivate mcp")
	}

	mcp.IsActive = status == entity.ActiveStatusActivate
	log.V1.CtxInfo(ctx, "[MCP] ModifyMCPActivation success: %s", mcp.MCPID)
	return mcp, nil
}

// GetMCPByID 根据ID获取MCP工具
func (s *ServiceImpl) GetMCPByID(ctx context.Context, id string, source entity.MCPSource) (*entity.MCP, error) {
	log.V1.CtxInfo(ctx, "[MCP] GetMCPByID request: id=%d, source=%d", id, source)

	// 调用 DAL 层获取MCP
	mcp, err := s.dao.GetMCPByID(ctx, id, source)
	if err != nil {
		log.V1.CtxError(ctx, "[MCP] GetMCPByID error: %v", err)
		return nil, errors.Wrap(err, "failed to get mcp")
	}

	if mcp == nil {
		log.V1.CtxError(ctx, "[MCP] MCP not found: %d", id)
		return nil, ErrMCPNotFound
	}

	log.V1.CtxInfo(ctx, "[MCP] GetMCPByID success: %s", mcp.MCPID)
	return mcp, nil
}

// GetUserDeactivatedAIMEMCPsMap 获取用户取消激活的AIME来源MCP ID映射
func (s *ServiceImpl) GetUserDeactivatedAIMEMCPsMap(ctx context.Context, username string) (map[string]bool, error) {
	deactivatedIDs, err := s.dao.GetUserDeactivatedAIMEMCPs(ctx, username)
	if err != nil {
		log.V1.CtxError(ctx, "[MCP] GetUserDeactivatedAIMEMCPs error: %v", err)
		return nil, errors.Wrap(err, "failed to get user deactivated AIME mcps")
	}

	// 构建ID映射
	deactivatedMap := make(map[string]bool, len(deactivatedIDs))
	for _, id := range deactivatedIDs {
		deactivatedMap[id] = true
	}

	return deactivatedMap, nil
}

func ConvertToMCPProvider(mcp *entity.MCP, index int) *mcptool.MCPProvider {
	if mcp == nil {
		return nil
	}
	name := replaceSpecialChars(mcp.Name)
	provider := mcptool.MCPProvider{
		// 非AIME的话，传递的id为名称_序列id(仅做去重，因此直接用这里的排序id)
		ID: choose.If(mcp.Source == entity.MCPSourceAIME, mcp.MCPID, fmt.Sprintf("%s_%d", name, index)),
		// 如果来源不是AIME，则ID和name需要拼接成"name_id"格式以防止重名
		Name:        choose.If(mcp.Source == entity.MCPSourceAIME, name, fmt.Sprintf("%s_%d", name, index)),
		Description: mcp.Description,
		Type:        mcptool.MCPSource(mcp.Source),
		MCPType:     mcptool.MCPType(mcp.Type), // 二期新增：设置MCP类型（使用枚举转换）
	}

	// 根据MCP类型设置不同的字段
	switch mcp.Type {
	case entity.MCPTypeSTDIO:
		provider.Cmd = gptr.Indirect(mcp.Config.Command)
		provider.Args = mcp.Config.Args

		// 将map[string]string转换为[]string格式的环境变量
		provider.Env = make([]string, 0, len(mcp.Config.Env))
		for key, value := range mcp.Config.Env {
			provider.Env = append(provider.Env, key+"="+value)
		}
	case entity.MCPTypeSSE:
		if mcp.Config.BaseURL != nil {
			provider.BaseURL = *mcp.Config.BaseURL
		}
	case entity.MCPTypeStreamableHTTP:
		// 二期新增：StreamableHTTP类型
		if mcp.Config.BaseURL != nil {
			provider.BaseURL = *mcp.Config.BaseURL
		}
	case entity.MCPTypeCloudSDK:
		// 二期新增：CloudSDK类型
		if mcp.Config.PSM != nil {
			provider.PSM = *mcp.Config.PSM
		}
	}
	return &provider
}

// 匹配所有非中文、非英文、非数字、非下划线、非空格的字符
var specialRegex = regexp.MustCompile(`[^a-zA-Z0-9_\s\p{Han}]`)

// 将字符串中的特殊字符替换为下划线
func replaceSpecialChars(s string) string {
	return specialRegex.ReplaceAllString(s, "_")
}

// ValidateMCPOption 验证MCP工具的参数
type ValidateMCPOption struct {
	Config         *entity.MCPConfig // MCP工具参数结构
	Source         entity.MCPSource  // 来源
	Type           entity.MCPType    // MCP工具类型
	Token          string            // 验证mcp时需要token校验mcp
	LarkUserOpenID string            // 验证mcp时需要lark user open id
}

// ValidateMCP 验证MCP工具配置是否有效，仅校验个人来源
func (s *ServiceImpl) ValidateMCP(ctx context.Context, opt *ValidateMCPOption) bool {
	log.V1.CtxInfo(ctx, "[MCP] ValidateMCP request: %#v", opt)
	// 仅校验个人来源
	if opt.Source != entity.MCPSourceUserDefine {
		return true
	}

	// Config不能为空
	if opt.Config == nil {
		return false
	}
	// 临时创建一个MCP对象，用于验证
	tempMCP := &entity.MCP{
		MCPKey: entity.MCPKey{
			MCPID:  s.idGen.NewID(), // 生成一个临时ID
			Source: opt.Source,
		},
		Name:        "temp_validation_mcp",
		Description: "Temporary MCP for validation",
		Config:      gptr.Indirect(opt.Config),
		Type:        opt.Type,
	}

	provider := ConvertToMCPProvider(tempMCP, 1)
	ctx = context.WithValue(ctx, mcptool.CommonHeaderXJwtToken, opt.Token)
	ctx = context.WithValue(ctx, mcptool.CommonHeaderUserOpenLarkID, opt.LarkUserOpenID)
	// todo 未来如果需要返回mcp初始化错误，请在这里露出错误
	cli, err := mcptool.ConnectMCP(ctx, provider)
	if err != nil {
		log.V1.CtxError(ctx, "[MCP] ValidateMCP ConnectMCP error: %v", err)
		return false
	}

	// 关闭会话，避免资源浪费
	cli.Close()

	log.V1.CtxInfo(ctx, "[MCP] ValidateMCP success")
	return true
}
