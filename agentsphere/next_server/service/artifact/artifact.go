package artifact

import (
	"archive/zip"
	"bytes"
	"context"
	"crypto/md5"
	"encoding/hex"
	"io"
	"path"
	"sort"
	"strings"

	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/videoarch/imagex-sdk-golang/v2/service/imagex"
	"github.com/AlekSi/pointer"
	"github.com/cenkalti/backoff/v4"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"github.com/sourcegraph/conc/iter"
	"go.uber.org/fx"
	"gorm.io/gorm"

	agententity "code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal"
	serverdal "code.byted.org/devgpt/kiwis/agentsphere/next_server/dal"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	serverservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/service/user"
	authentity "code.byted.org/devgpt/kiwis/copilotstack/common/auth/entity"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/conv"
	"code.byted.org/devgpt/kiwis/lib/metrics"
	"code.byted.org/devgpt/kiwis/lib/util"
	"code.byted.org/devgpt/kiwis/lib/uuid"
	"code.byted.org/devgpt/kiwis/port/db"
	"code.byted.org/devgpt/kiwis/port/lark"
	"code.byted.org/devgpt/kiwis/port/tos"

	tossdk "code.byted.org/gopkg/tos"
)

type Service struct {
	tccConf      *config.AgentSphereTCCConfig
	dao          *dal.DAO
	larkClient   lark.Client
	tos          tos.Client
	idGen        uuid.Generator
	imagexClient *imagex.ImageXClient
	userService  *user.Service
}

type CreateServiceOption struct {
	fx.In
	TCCConf      *config.AgentSphereTCCConfig
	DAO          *dal.DAO
	Tos          tos.Client
	LarkClient   lark.Client
	ImagexClient *imagex.ImageXClient
	UserService  *user.Service
}

func NewService(opt CreateServiceOption) (*Service, error) {
	s := &Service{
		idGen:        uuid.GetDefaultGenerator(nil),
		tccConf:      opt.TCCConf,
		dao:          opt.DAO,
		tos:          opt.Tos,
		larkClient:   opt.LarkClient,
		imagexClient: opt.ImagexClient,
		userService:  opt.UserService,
	}
	return s, nil
}

type CreateArtifactOption struct {
	SessionID string
	Type      entity.ArtifactType
	Source    entity.ArtifactSource
	Key       string
	Metadata  entity.ArtifactMetadata
}

func (s *Service) CreateArtifact(ctx context.Context, opt CreateArtifactOption) (result *entity.Artifact, err error) {
	reportMetricsFunc := metrics.NSM.ReportArtifactMetrics()
	defer func() {
		reportMetricsFunc("CreateArtifact", string(opt.Source), err != nil, serverservice.ErrorToErrorReason(err))
	}()

	sessionID := opt.SessionID
	if sessionID != "unknown" {
		_, err := s.dao.GetSessionWithDeleted(ctx, dal.GetSessionOption{
			ID: sessionID,
		})
		if err != nil {
			return nil, errors.WithMessagef(err, "failed to get session %s for artifact", sessionID)
		}
	}

	previousVersions, err := s.dao.ListArtifacts(ctx, serverdal.ListArtifactsOption{
		SessionID: opt.SessionID,
		Key:       lo.ToPtr(opt.Key),
	})
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get previous versions of artifact %s", opt.Key)
	}

	return s.dao.CreateArtifact(ctx, serverdal.CreateArtifactOption{
		SessionID: sessionID,
		ID:        s.idGen.NewID(),
		Status:    entity.ArtifactStatusDraft,
		Source:    opt.Source,
		Type:      opt.Type,
		Key:       opt.Key,
		Version:   int32(len(previousVersions) + 1),
		Metadata:  opt.Metadata,
	})
}

type CreateLinkArtifactOption struct {
	Url               string
	ArtifactKeySource entity.LinkArtifactKeySource // artifact key 生成来源
	ArtifactSource    entity.ArtifactSource        // artifact 来源
	Display           bool
	Title             string

	DeploymentID *string // 部署的link，有此id
}

func (s *Service) CreateLinkArtifact(ctx context.Context, sessionID string, opt CreateLinkArtifactOption) (result *entity.Artifact, err error) {
	reportMetricsFunc := metrics.NSM.ReportArtifactMetrics()
	defer func() {
		reportMetricsFunc("CreateLinkArtifact", string(opt.ArtifactSource), err != nil, serverservice.ErrorToErrorReason(err))
	}()

	l := entity.LinkContent{
		URL: opt.Url,
	}
	var (
		artifactKey string
		fileMeta    entity.FileMeta
	)
	switch opt.ArtifactKeySource {
	case entity.LinkArtifactKeySourceDeployment:
		if opt.DeploymentID == nil {
			return nil, errors.Wrap(serverservice.ErrorArtifactNotFound, "deployment id is required")
		}
		artifactKey = s.GenerateLinkArtifactKey(entity.LinkArtifactKeySourceDeployment, *opt.DeploymentID)
		fileMeta = entity.FileMeta{Content: conv.JSONString(l), Type: entity.ArtifactTypeLink, Name: opt.Title, SubType: entity.LinkArtifactKeySourceDeployment.String()}
	case entity.LinkArtifactKeySourceLarkDoc:
		artifactKey = s.GenerateLinkArtifactKey(entity.LinkArtifactKeySourceLarkDoc, util.MD5([]byte(opt.Url)))
		fileMeta = entity.FileMeta{Content: conv.JSONString(l), Type: entity.ArtifactTypeLink, Name: opt.Title, SubType: entity.LinkArtifactKeySourceLarkDoc.String()}
	case entity.LinkArtifactKeySourceLarkSheet:
		artifactKey = s.GenerateLinkArtifactKey(entity.LinkArtifactKeySourceLarkSheet, util.MD5([]byte(opt.Url)))
		fileMeta = entity.FileMeta{Content: conv.JSONString(l), Type: entity.ArtifactTypeLink, Name: opt.Title, SubType: entity.LinkArtifactKeySourceLarkSheet.String()}
	case entity.LinkArtifactKeySourceURL:
		artifactKey = s.GenerateLinkArtifactKey(entity.LinkArtifactKeySourceURL, util.MD5([]byte(opt.Url)))
		fileMeta = entity.FileMeta{Content: conv.JSONString(l), Type: entity.ArtifactTypeLink, Name: opt.Title, SubType: entity.LinkArtifactKeySourceURL.String()}
	default:
		return nil, errors.New("unknown artifact key source")
	}
	version := int32(1)

	// 如果存在返回
	artifact, err := s.dao.GetArtifactByArtifactKey(ctx, sessionID, artifactKey, version, true)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, errors.WithMessage(err, "get artifact failed")
	}
	if artifact != nil {
		return artifact, nil
	}

	// 如果不存在create
	artifact, err = s.dao.CreateArtifact(ctx, dal.CreateArtifactOption{
		SessionID: sessionID,
		ID:        s.idGen.NewID(),
		Type:      entity.ArtifactTypeLink,
		Status:    entity.ArtifactStatusCompleted,
		Source:    opt.ArtifactSource,
		Key:       artifactKey,
		Version:   version, // link 类型暂时版本都是1
		Files:     entity.FileMetas{fileMeta},
		Display:   opt.Display,
	})
	if err != nil { // 如果唯一索引冲突，再查一次返回
		if errors.Is(err, gorm.ErrDuplicatedKey) || db.IsDuplicateKeyError(err) {
			return s.dao.GetArtifactByArtifactKey(ctx, sessionID, artifactKey, version, true)
		}
		return nil, errors.WithMessagef(err, "failed to create link artifact, session id: %v", sessionID)
	}
	return artifact, err
}

type GetArtifactOption struct {
	ID   string
	Sync bool
}

func (s *Service) GetArtifact(ctx context.Context, opt GetArtifactOption) (*entity.Artifact, error) {
	return s.dao.GetArtifact(ctx, serverdal.GetArtifactOption{
		ID:   opt.ID,
		Sync: opt.Sync,
	})
}

type GetArtifactByKeyOption struct {
	SessionID   string
	ArtifactKey string
	Sync        bool
}

func (s *Service) GetArtifactByKey(ctx context.Context, opt GetArtifactByKeyOption) (*entity.Artifact, error) {
	return s.dao.GetArtifactByArtifactKey(ctx, opt.SessionID, opt.ArtifactKey, 1, opt.Sync)
}

type ListSessionArtifactsOption struct {
	SessionID   string
	Status      []entity.ArtifactStatus
	Display     *bool
	ArtifactIDs []string
}

func (s *Service) ListSessionArtifacts(ctx context.Context, opt ListSessionArtifactsOption) ([]*entity.Artifact, error) {
	return s.dao.ListArtifacts(ctx, serverdal.ListArtifactsOption{
		SessionID:   opt.SessionID,
		Status:      opt.Status,
		Display:     opt.Display,
		ArtifactIDs: opt.ArtifactIDs,
	})
}

type GetLatestArtifactOption struct {
	SessionID string
	Type      []entity.ArtifactType
}

func (s *Service) GetLatestArtifactByType(ctx context.Context, opt GetLatestArtifactOption) (*entity.Artifact, error) {
	return s.dao.GetLatestArtifactByType(ctx, serverdal.GetLatestArtifactOption{
		SessionID: opt.SessionID,
		Type:      opt.Type,
	})
}

type ListReplayArtifactsOption struct {
	ReplayID string
	Display  *bool
}

func (s *Service) ListReplayArtifacts(ctx context.Context, opt ListReplayArtifactsOption) ([]*entity.Artifact, error) {
	replay, err := s.dao.GetReplay(ctx, serverdal.GetReplayOption{
		ID:    opt.ReplayID,
		Sync:  false,
		Cache: true,
	})
	if err != nil {
		return nil, errors.WithMessagef(err, "failed to get replay, replay_id: %s", opt.ReplayID)
	}
	if replay == nil {
		return nil, errors.Errorf("replay is nil, replay_id: %s", opt.ReplayID)
	}

	// 兼容旧replay没有写入snapshot_meta的情况，要根据SessionID获取
	if replay.SnapshotMeta == nil {
		return s.ListSessionArtifacts(ctx, ListSessionArtifactsOption{
			SessionID: replay.SessionID,
			Display:   opt.Display,
		})
	}
	// 这里的 artifactsIDs 是一个 string 数组，表示 artifact 的 ID 列表
	artifactsIDs := replay.SnapshotMeta.ArtifactIDs
	if len(artifactsIDs) == 0 {
		return []*entity.Artifact{}, nil
	}
	return s.dao.ListArtifacts(ctx, serverdal.ListArtifactsOption{
		SessionID:   replay.SessionID,
		ArtifactIDs: artifactsIDs,
		Display:     opt.Display,
	})
}

type UpdateArtifactOption struct {
	ID        string
	Status    *entity.ArtifactStatus
	Metadata  entity.ArtifactMetadata
	SessionID *string
}

func (s *Service) UpdateArtifact(ctx context.Context, opt UpdateArtifactOption) (*entity.Artifact, error) {
	return s.dao.UpdateArtifact(ctx, serverdal.UpdateArtifactOption{
		ID:        opt.ID,
		Status:    opt.Status,
		Metadata:  opt.Metadata,
		SessionID: opt.SessionID,
	})
}

type UpdateArtifactFileOption struct {
	ID        string
	Name      string
	LarkToken string
}

func (s *Service) UpdateArtifactFile(ctx context.Context, opt UpdateArtifactFileOption) (*entity.Artifact, error) {
	return s.dao.UpdateArtifactFiles(ctx, serverdal.UpdateArtifactFnOption{
		ID: opt.ID,
		UpdateArtifact: func(artifact *entity.Artifact) {
			var files entity.FileMetas
			for _, file := range artifact.FileMetas {
				if file.Name == opt.Name {
					file.LarkToken = opt.LarkToken
				}
				files = append(files, file)
			}
			artifact.FileMetas = files
		},
	})
}

type UploadArtifactOption struct {
	// ID          string
	Artifact    *entity.Artifact
	Path        string
	Content     io.ReadCloser
	ContentSize int64
	Type        *entity.ArtifactType
}

var imageExts = []string{"gif", "jpeg", "tiff", "png", "heic", "heif", "avif", "avis", "bmp", "jpg", "jpe"}

func (s *Service) UploadArtifact(ctx context.Context, opt UploadArtifactOption) (result *entity.Artifact, err error) {
	reportMetricsFunc := metrics.NSM.ReportArtifactMetrics()
	defer func() {
		reportMetricsFunc("UploadArtifact", "all", err != nil, serverservice.ErrorToErrorReason(err))
	}()

	// for runtime, upload happens immediately after artifact is created and we need to ensure to read latest data
	// artifact, err := s.dao.GetArtifact(ctx, serverdal.GetArtifactOption{ID: opt.ID, Sync: true})
	// if err != nil {
	// 	return nil, errors.WithMessage(err, "artifact not found")
	// }
	artifact := opt.Artifact
	if artifact == nil {
		return nil, errors.New("artifact is nil")
	}

	fullpath := path.Join(tosPrefix(artifact), opt.Path)
	log.V1.CtxInfo(ctx, "save to TOS, path: %s", fullpath)
	var (
		content   []byte
		imagexURI string
		buf       bytes.Buffer
		md5Str    string
		md5Hash   = md5.New()
	)
	reader := io.TeeReader(opt.Content, io.MultiWriter(&buf, md5Hash))
	artifactType := artifact.Type
	if opt.Type != nil {
		artifactType = *opt.Type
	}
	linkSource := entity.LinkArtifactKeySource("")
	if artifact.Type == entity.ArtifactTypeLink {
		linkSource = "link"
	}
	artifactType, subType := GetArtifactTypeAndSubType(opt.Path, buf.Bytes(), linkSource)
	if artifactType != entity.ArtifactTypeLink {
		// 空文件不上传
		if opt.ContentSize > 0 {
			err = backoff.Retry(func() error {
				err = s.tos.PutObject(ctx, fullpath, opt.ContentSize, reader)
				if err != nil {
					if tossdk.IsRetryableError(err) {
						return err
					}
					return backoff.Permanent(err)
				}
				return nil
			}, backoff.WithMaxRetries(backoff.NewExponentialBackOff(), 3))
		}
		if err != nil {
			return nil, errors.WithMessage(err, "failed to upload artifact")
		}

		// 需要再reader被tos.PutObject读取后，md5才可以计算出来，TeeReader的机制
		md5Str = hex.EncodeToString(md5Hash.Sum(nil))

		if artifactType == entity.ArtifactTypeImage {
			imagexConf := s.tccConf.ImageXConfig.GetValue()
			params := &imagex.UploadImageParam{
				ServiceId: imagexConf.ServiceID,
			}
			result, err := s.imagexClient.UploadImageByIOreader(ctx, params, &buf)
			if err != nil {
				return nil, errors.WithMessage(err, "failed to upload image to imagex")
			}
			if len(result.Results) < 1 {
				return nil, errors.New("faild get imagex info")
			}
			if result.Results[0].UriStatus != imagex.UploadSuccessCode {
				return nil, errors.Errorf("faild upload to imagex, %d", result.Results[0].UriStatus)
			}
			imagexURI = result.Results[0].Uri
		}
	} else {
		content, err = io.ReadAll(opt.Content)
		if err != nil {
			return nil, errors.WithMessage(err, "failed read content")
		}
	}

	return s.dao.UpdateArtifactFiles(ctx, serverdal.UpdateArtifactFnOption{
		ID: artifact.ID,
		UpdateArtifact: func(artifact *entity.Artifact) {
			filenameMap := lo.SliceToMap(artifact.FileMetas, func(item entity.FileMeta) (string, entity.FileMeta) {
				return item.Name, item
			})
			if _, ok := filenameMap[opt.Path]; !ok {
				artifact.FileMetas = append(artifact.FileMetas, entity.FileMeta{
					Name:      opt.Path,
					Size:      opt.ContentSize,
					Content:   string(content),
					MD5:       md5Str,
					ImageXURI: imagexURI,
					Type:      artifactType,
					SubType:   subType,
				})
			}
			sort.Sort(artifact.FileMetas)
			if artifact.Status == entity.ArtifactStatusDraft && len(artifact.FileMetas) > 0 {
				artifact.Status = entity.ArtifactStatusCreating
			}
		},
	})
}

// 目前仅限制 logs 类型的 artifact 下载，禁止非管理员访问，避免 prompt 和 token 等信息泄露
func (s *Service) checkArtifactPermission(ctx context.Context, artifact entity.Artifact, account *authentity.Account) bool {
	if artifact.Type != entity.ArtifactTypeLogs {
		return true
	}
	if account == nil {
		return false
	}
	if s.userService.IsDeveloper(account) {
		return true
	}
	return false
}

type GetArtifactFileMetaOption struct {
	ID   string
	Name string
}

func (s *Service) GetArtifactFileMeta(ctx context.Context, opt GetArtifactFileMetaOption) (*entity.FileMeta, error) {
	artifact, err := s.dao.GetArtifact(ctx, serverdal.GetArtifactOption{ID: opt.ID})
	if err != nil {
		return nil, err
	}

	for _, fileMeta := range artifact.FileMetas {
		if fileMeta.Name == opt.Name {
			return &fileMeta, nil
		}
	}
	return nil, errors.New("artifact file not found")
}

type GetArtifactFileOption struct {
	Artifact entity.Artifact
	Path     string
	Account  *authentity.Account
	TosCtx   *context.Context
	// SkipPermissionCheck 用于特殊场景（如 MCP trace）跳过权限检查
	SkipPermissionCheck bool
}

func (s *Service) GetArtifactFile(ctx context.Context, opt GetArtifactFileOption) (r io.ReadCloser, meta *entity.FileMeta, err error) {
	reportMetricsFunc := metrics.NSM.ReportArtifactMetrics()
	defer func() {
		reportMetricsFunc("GetArtifactFile", "all", err != nil, serverservice.ErrorToErrorReason(err))
	}()

	artifact, err := s.dao.GetArtifact(ctx, serverdal.GetArtifactOption{ID: opt.Artifact.ID})
	if err != nil {
		return nil, nil, err
	}

	if !opt.SkipPermissionCheck && !s.checkArtifactPermission(ctx, opt.Artifact, opt.Account) {
		return io.NopCloser(bytes.NewReader([]byte(""))), nil, nil
	}

	var file *entity.FileMeta
	for _, fileMeta := range artifact.FileMetas {
		if fileMeta.Name == opt.Path {
			file = &fileMeta
			break
		}
	}
	if file == nil {
		return nil, nil, serverservice.ErrorArtifactNotFound
	}

	if artifact.Type == entity.ArtifactTypeLink {
		content := strings.ReplaceAll(file.Content, "neuma-boe.bytedance.net", "aime-boe-app.bytedance.net")
		content = strings.ReplaceAll(content, "neuma-app.bytedance.net", "aime-app.bytedance.net")
		content = strings.ReplaceAll(content, "devagent-boe-app.bytedance.net", "aime-boe-app.bytedance.net")
		content = strings.ReplaceAll(content, "devagent-app.bytedance.net", "aime-app.bytedance.net")
		return io.NopCloser(strings.NewReader(content)), file, nil
	}

	var tosCtx context.Context = ctx
	if opt.TosCtx != nil {
		tosCtx = *opt.TosCtx
	}

	// 空文件直接返回，不从tos取
	if file.Size == 0 {
		return io.NopCloser(strings.NewReader("")), file, nil
	}
	obj, err := s.tos.GetObject(tosCtx, path.Join(tosPrefix(artifact), opt.Path))
	if err != nil {
		return nil, nil, errors.WithMessage(err, "failed to retrieve artifact file")
	}
	return obj.R, file, nil
}

func (s *Service) GetArtifactFileContent(ctx context.Context, opt GetArtifactFileOption) (b []byte, meta *entity.FileMeta, err error) {
	reportMetricsFunc := metrics.NSM.ReportArtifactMetrics()
	defer func() {
		reportMetricsFunc("GetArtifactFileContent", "all", err != nil, serverservice.ErrorToErrorReason(err))
	}()

	reader, meta, err := s.GetArtifactFile(ctx, opt)
	if err != nil {
		return nil, nil, errors.WithMessage(err, "failed to retrieve artifact file")
	}
	body, err := io.ReadAll(reader)
	if err != nil {
		return nil, nil, errors.WithMessage(err, "failed to read artifact file")
	}
	return body, meta, nil
}

func (s *Service) UpdateArtifactToDisplay(ctx context.Context, id string) (*entity.Artifact, error) {
	artifact, err := s.dao.UpdateArtifact(ctx, dal.UpdateArtifactOption{
		ID:      id,
		Display: pointer.To(true),
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to update artifact")
	}
	return artifact, nil
}

type CopyToNewArtifactOption struct {
	OldArtifactID string
	NewSessionID  string
}

func (s *Service) CopyToNewArtifact(ctx context.Context, opt CopyToNewArtifactOption) (*entity.Artifact, error) {
	artifact, err := s.dao.GetArtifact(ctx, serverdal.GetArtifactOption{ID: opt.OldArtifactID})
	if err != nil {
		return nil, err
	}
	if opt.NewSessionID == "" {
		return nil, errors.New("session id should not be empty")
	}

	newArtifact, err := s.dao.CreateArtifact(ctx, serverdal.CreateArtifactOption{
		SessionID: opt.NewSessionID,
		ID:        s.idGen.NewID(),
		Type:      artifact.Type,
		Status:    artifact.Status,
		Source:    artifact.Source,
		Key:       s.idGen.NewID(),
		Version:   1,
		Files:     artifact.FileMetas,
		Metadata:  artifact.Metadata,
		Display:   artifact.Display,
	})

	files := artifact.FileMetas
	if len(files) == 0 {
		return newArtifact, nil
	}
	for _, file := range files {
		err = s.tos.CopyObject(ctx, path.Join(tosPrefix(artifact), file.Name), path.Join(tosPrefix(newArtifact), file.Name))
		if err != nil {
			return nil, errors.WithMessage(err, "failed to copy object")
		}
	}
	return newArtifact, nil
}

func tosPrefix(artifact *entity.Artifact) string {
	prefix := "next_artifacts"
	switch artifact.Type {
	case entity.ArtifactTypeFile:
		prefix += "/file"
	case entity.ArtifactTypeCode:
		prefix += "/code"
	case entity.ArtifactTypeImage:
		prefix += "/image"
	case entity.ArtifactTypeLink:
		prefix += "/link"
	case entity.ArtifactTypeLogs:
		prefix += "/logs"
	case entity.ArtifactTypeResult:
		prefix += "/result"
	}
	return path.Join(prefix, artifact.ID)
}

func (s *Service) GetAndUpdateAttachmentArtifact(ctx context.Context, sessionID string, attachment agententity.Attachment, updateDisplay bool) (*entity.Attachment, error) {
	// 根据filename获取匹配的文件
	var (
		finalAttachmentArtifactID string
		finalAttachmentType       string
		fileMeta                  entity.FileMeta
		url                       string
		title                     string
	)

	switch attachment.Type {
	case agententity.AttachmentTypeLarkDoc, agententity.AttachmentTypeLarkSheet, agententity.AttachmentTypeURL:
		if attachment.URL != "" {
			url = attachment.URL
		}
		if isHTTPURL(attachment.Path) { // 兼容旧的逻辑，可能会通过 path 传飞书文档链接
			url = attachment.Path
			title = attachment.Path
		} else {
			title = attachment.Path
		}
		// 做artifact创建
		var artifactKeySource entity.LinkArtifactKeySource
		switch attachment.Type {
		case agententity.AttachmentTypeLarkDoc:
			artifactKeySource = entity.LinkArtifactKeySourceLarkDoc
		case agententity.AttachmentTypeLarkSheet:
			artifactKeySource = entity.LinkArtifactKeySourceLarkSheet
		case agententity.AttachmentTypeURL:
			artifactKeySource = entity.LinkArtifactKeySourceURL
		}

		createLinkArtifactOption := CreateLinkArtifactOption{
			Url:               url,
			ArtifactKeySource: artifactKeySource,
			ArtifactSource:    entity.ArtifactSourceAgent,
			Title:             title,
		}
		artifact, err := s.CreateLinkArtifact(ctx, sessionID, createLinkArtifactOption)
		if err != nil {
			return nil, errors.WithMessagef(err, "failed to create lark doc artifact")
		}
		if artifact == nil {
			return nil, serverservice.ErrorArtifactNotFound
		}

		finalAttachmentArtifactID = artifact.ID
		finalAttachmentType = string(artifact.Type)
		if len(artifact.FileMetas) > 0 {
			fileMeta = artifact.FileMetas[0]
		}
	case agententity.AttachmentTypeDeployment:
		// 如果是deployment的artifacts，需要做一下trick转换
		// 先通过artifact id查找到 deployment id, 再通过 deployment id 查到所对应的artifacts id 做转换
		artifact, err := s.GetArtifact(ctx, GetArtifactOption{ID: attachment.ArtifactID, Sync: true})
		if err != nil {
			return nil, errors.WithMessagef(err, "failed to get attachment deployment artifact, artifact id:%s, err: %s", attachment.ArtifactID, err)
		}
		if artifact == nil {
			return nil, serverservice.ErrorArtifactNotFound
		}

		deployment, err := s.dao.GetDeploymentByArtifactID(ctx, serverdal.GetDeploymentByArtifactIDOption{
			ArtifactID: artifact.ID,
			Sync:       true,
			Cache:      true,
		})
		if err != nil {
			return nil, errors.WithMessagef(err, "failed to get attachment deployment artifact, artifact id:%s", attachment.ArtifactID)
		}

		deploymentArtifactKey := s.GenerateLinkArtifactKey(entity.LinkArtifactKeySourceDeployment, deployment.ID)
		deploymentArtifact, err := s.dao.GetArtifactByArtifactKey(ctx, deployment.SessionID, deploymentArtifactKey, 1, true) // 部署artifact key版本是固定的1
		if err != nil {
			return nil, errors.WithMessagef(err, "failed to get attachment deployment artifact, artifact key:%s", deploymentArtifactKey)
		}

		finalAttachmentArtifactID = deploymentArtifact.ID
		finalAttachmentType = string(deploymentArtifact.Type)
		url = deployment.URL
		if len(deploymentArtifact.FileMetas) > 0 {
			fileMeta = deploymentArtifact.FileMetas[0]
		}
		log.V1.CtxInfo(ctx, "attachment artifact is deployed, convert to deployment artifact. origin artifact: %s, attachment artifact: %s", attachment.ArtifactID, deploymentArtifact.ID)
	default: // 默认文件处理方式
		artifact, err := s.GetArtifact(ctx, GetArtifactOption{ID: attachment.ArtifactID, Sync: true})
		if err != nil {
			return nil, errors.WithMessagef(err, "failed to operate artifact, id: %v", attachment.ArtifactID)
		}
		if artifact == nil {
			return nil, serverservice.ErrorArtifactNotFound
		}

		finalAttachmentArtifactID = artifact.ID
		finalAttachmentType = string(artifact.Type)
		for _, file := range artifact.FileMetas {
			if file.Name != attachment.Path {
				continue
			}
			fileMeta = file
			break
		}
		if fileMeta.Name == "" {
			return nil, serverservice.ErrorArtifactNotFound
		}
		// 如果是图片类型，生成 url
		if fileMeta.Type == entity.ArtifactTypeImage {
			url = fileMeta.ImageURL(s.tccConf.ImageXConfig.GetValue().DefaultDomain, s.tccConf.ImageXConfig.GetValue().DefaultTemplateID)
		}
	}

	if finalAttachmentArtifactID == "" || finalAttachmentType == "" {
		return nil, errors.WithMessage(serverservice.ErrorArtifactNotFound, "artifact id or type not found")
	}

	// 更新为结果产物展示
	if updateDisplay {
		_, err := s.UpdateArtifactToDisplay(ctx, finalAttachmentArtifactID)
		if err != nil {
			logs.V1.CtxError(ctx, "failed to update artifact to display, id: %v", finalAttachmentArtifactID)
		}
	}
	return &entity.Attachment{
		ID:            finalAttachmentArtifactID,
		FileName:      fileMeta.Name,
		Path:          fileMeta.Name,
		Type:          finalAttachmentType,
		URL:           url,
		ContentType:   string(fileMeta.Type),
		SubType:       fileMeta.SubType,
		ContentLength: fileMeta.Size,
		LarkToken:     fileMeta.LarkToken,
	}, nil
}

type GetSessionArtifactIDsOption struct {
	Types   []entity.ArtifactType
	Display *bool
	Sync    bool
}

func (s *Service) GetSessionArtifactIDs(ctx context.Context, sessionID string, opt GetSessionArtifactIDsOption) ([]string, error) {
	return s.dao.GetSessionArtifactIDs(ctx, sessionID, dal.GetSessionArtifactIDsOption{
		Types:   opt.Types,
		Display: opt.Display,
		Sync:    opt.Sync,
	})
}

type GetArtifactFileContentOption struct {
	Artifact entity.Artifact
	Path     string
	Account  *authentity.Account
}

type BatchDownloadArtifactOption struct {
	SessionID         string
	ArtifactIDToPaths map[string][]string
	Account           *authentity.Account
}

// BatchDownloadArtifacts downloads multiple files from multiple artifacts
func (s *Service) BatchDownloadArtifacts(ctx context.Context, opt BatchDownloadArtifactOption) (map[string]map[string][]byte, error) {
	reportMetricsFunc := metrics.NSM.ReportArtifactMetrics()
	var err error
	defer func() {
		reportMetricsFunc("BatchDownloadArtifacts", "all", err != nil, serverservice.ErrorToErrorReason(err))
	}()

	result := make(map[string]map[string][]byte)

	// 从 ArtifactIDToPaths 中提取所有的 artifact IDs
	artifactIDs := make([]string, 0, len(opt.ArtifactIDToPaths))
	for artifactID := range opt.ArtifactIDToPaths {
		artifactIDs = append(artifactIDs, artifactID)
	}

	// Get reference map for the session
	referenceMap, err := s.GetReferenceMap(ctx, opt.SessionID)
	if err != nil {
		log.V1.CtxWarn(ctx, "failed to get reference map for session %s: %v", opt.SessionID, err)
		// Continue without reference map
	}

	// Create a mapper to process artifacts in parallel
	mapper := iter.Mapper[string, map[string][]byte]{
		MaxGoroutines: 4,
	}

	// Process each artifact
	artifactResults, err := mapper.MapErr(artifactIDs, func(artifactID *string) (map[string][]byte, error) {
		// Get the artifact
		artifact, err := s.GetArtifact(ctx, GetArtifactOption{ID: *artifactID})
		if err != nil {
			return nil, errors.WithMessagef(err, "failed to get artifact %s", *artifactID)
		}

		// 以防夹带非这个session的artifact_id
		if artifact.SessionID != opt.SessionID {
			return nil, errors.WithMessagef(serverservice.ErrorArtifactNotFound, "artifact %s not in session %s", *artifactID, opt.SessionID)
		}

		// Check permission
		if !s.checkArtifactPermission(ctx, *artifact, opt.Account) {
			return make(map[string][]byte), nil
		}

		// Create a map to store file contents for this artifact
		artifactFiles := make(map[string][]byte)

		// 获取当前 artifact 对应的路径
		paths, ok := opt.ArtifactIDToPaths[*artifactID]
		if !ok {
			return artifactFiles, nil
		}

		// Process each path for this artifact
		for _, path := range paths {
			// Check if the file exists in the artifact
			fileExists := false
			for _, fileMeta := range artifact.FileMetas {
				if fileMeta.Name == path {
					fileExists = true
					break
				}
			}

			if !fileExists {
				continue
			}

			// Get the file content
			content, _, err := s.GetArtifactFileContent(ctx, GetArtifactFileOption{
				Artifact: *artifact,
				Path:     path,
				Account:  opt.Account,
			})
			if err != nil {
				log.V1.CtxError(ctx, "failed to get file content for artifact %s, path %s: %v", *artifactID, path, err)
				continue
			}

			// Apply ReplaceCiteTagsByLine if the file is a markdown and have a reference map
			if len(referenceMap) > 0 && entity.IsMarkdownFile(path) {
				contentReader := bytes.NewReader(content)
				// Apply ReplaceCiteTagsByLine
				processedReader, size, err := s.ReplaceCiteTagsByLine(contentReader, referenceMap)
				if err != nil {
					log.V1.CtxWarn(ctx, "failed to replace cite tags for artifact %s, path %s: %v", *artifactID, path, err)
					// Continue with original content
				} else {
					processedBody := make([]byte, size)
					_, err = processedReader.Read(processedBody)
					if err != nil {
						log.V1.CtxWarn(ctx, "failed to read processed content for artifact %s, path %s: %v", *artifactID, path, err)
					} else {
						content = processedBody
					}
				}
			}

			// Store the content
			artifactFiles[path] = content
		}

		return artifactFiles, nil
	})

	if err != nil {
		return nil, err
	}

	// Combine results
	for i, artifactID := range artifactIDs {
		result[artifactID] = artifactResults[i]
	}

	return result, nil
}

type BatchDownloadArtifactZipOption struct {
	SessionID         string
	ArtifactIDToPaths map[string][]string
	Account           *authentity.Account
	ZipName           string
}

// BatchDownloadArtifactsAsZip downloads multiple files from multiple artifacts and packages them as a ZIP file
func (s *Service) BatchDownloadArtifactsAsZip(ctx context.Context, opt BatchDownloadArtifactZipOption) (io.ReadCloser, error) {
	reportMetricsFunc := metrics.NSM.ReportArtifactMetrics()
	var err error
	defer func() {
		reportMetricsFunc("BatchDownloadArtifactsAsZip", "all", err != nil, serverservice.ErrorToErrorReason(err))
	}()

	// Create a buffer to store the ZIP file
	zipBuffer := new(bytes.Buffer)
	zipWriter := zip.NewWriter(zipBuffer)

	// Download the artifacts
	results, err := s.BatchDownloadArtifacts(ctx, BatchDownloadArtifactOption{
		SessionID:         opt.SessionID,
		ArtifactIDToPaths: opt.ArtifactIDToPaths,
		Account:           opt.Account,
	})
	if err != nil {
		return nil, err
	}

	// Add files to the ZIP
	for artifactID, files := range results {
		for path, content := range files {
			// Create a file in the ZIP with a flattened path
			// Use the path directly without creating artifact ID directories
			zipPath := path
			writer, err := zipWriter.Create(zipPath)
			if err != nil {
				log.V1.CtxError(ctx, "failed to create file in ZIP for artifact %s, path %s: %v", artifactID, path, err)
				continue
			}

			// Write the content to the ZIP
			_, err = writer.Write(content)
			if err != nil {
				log.V1.CtxError(ctx, "failed to write content to ZIP for artifact %s, path %s: %v", artifactID, path, err)
				continue
			}
		}
	}

	// Close the ZIP writer
	err = zipWriter.Close()
	if err != nil {
		return nil, errors.WithMessage(err, "failed to close ZIP writer")
	}

	// Return a reader for the ZIP buffer
	return io.NopCloser(bytes.NewReader(zipBuffer.Bytes())), nil
}
