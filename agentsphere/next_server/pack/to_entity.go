package pack

import (
	"encoding/json"

	agententity "code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	"github.com/samber/lo"
)

func ConvertModifyTemplateToEntity(dto *nextagent.ModifyTemplate) *entity.ModifyTemplate {
	if dto == nil {
		return nil
	}
	return &entity.ModifyTemplate{
		Category:  dto.Category,
		Name:      dto.Name,
		Prompt:    dto.Prompt,
		Variables: convertTemplateVariablesToEntity(dto.Variables),
		Scope:     convertTemplateScopeToEntity(dto.Scope),
		Label:     dto.Label,
	}
}

func convertTemplateVariablesToEntity(variables []*nextagent.TemplateVariable) []*entity.TemplateVariableSchema {
	if variables == nil {
		return nil
	}
	var entityVariables = make([]*entity.TemplateVariableSchema, len(variables))
	for _, v := range variables {
		if v == nil {
			continue
		}
		entityVariables = append(entityVariables, &entity.TemplateVariableSchema{
			ID:                v.ID,
			Name:              v.Name,
			Default:           lo.FromPtr(v.DefaultValue),
			Description:       lo.FromPtr(v.Description),
			Placeholder:       lo.FromPtr(v.Placeholder),
			SelectContent:     lo.FromPtr(v.SelectContent),
			AllowedUploadFile: v.Type == nextagent.TemplateVariableTypeUploadWithText,
		})
	}
	return entityVariables
}

func convertTemplateScopeToEntity(dto nextagent.TemplateScope) string {
	switch dto {
	case nextagent.TemplateScope_Public:
		return entity.TemplateScopePublic
	case nextagent.TemplateScope_Private:
		return entity.TemplateScopePrivate
	case nextagent.TemplateScope_Shared:
		return entity.TemplateScopeShared
	case nextagent.TemplateScope_Official:
		return entity.TemplateScopeOfficial
	case nextagent.TemplateScope_ProjectPublic:
		return entity.TemplateScopeProjectPublic
	default:
		return entity.TemplateScopePrivate
	}
}

func ConvertExpSOPToEntity(exp *string) (*agententity.ExpSOP, error) {
	if exp == nil {
		return nil, nil
	}
	var expSOP agententity.ExpSOP
	err := json.Unmarshal([]byte(*exp), &expSOP)
	if err != nil {
		return nil, err
	}
	return &expSOP, nil
}

func ConvertExperienceStatusToEntity(status nextagent.TemplateExperienceStatus) string {
	switch status {
	case nextagent.TemplateExperienceStatus_Success:
		return entity.TemplateExperienceStatusSuccess
	case nextagent.TemplateExperienceStatus_Failed:
		return entity.TemplateExperienceStatusFailed
	}
	return ""
}

func ConvertTemplateSourceToEntity(source *nextagent.TemplateSource) entity.TemplateSource {
	if source == nil {
		return entity.TemplateSourceAll
	}
	switch *source {
	case nextagent.TemplateSource_All:
		return entity.TemplateSourceAll
	case nextagent.TemplateSource_My:
		return entity.TemplateSourceMy
	case nextagent.TemplateSource_Star:
		return entity.TemplateSourceStar
	default:
		return entity.TemplateSourceAll
	}
}

func ConvertToolCallsToEntities(calls []*nextagent.ToolCall) []*entity.ToolCall {
	if len(calls) == 0 {
		return nil
	}
	var entities = make([]*entity.ToolCall, 0, len(calls))
	for _, call := range calls {
		if call == nil {
			continue
		}
		entities = append(entities, &entity.ToolCall{
			ID:            call.ToolCallID,
			Name:          call.Name,
			Content:       call.Content,
			Action:        convertToolCallActionToEntity(call.Action),
			NeedKeepLogin: lo.FromPtr(call.NeedKeepLogin),
		})
	}
	return entities
}

func convertToolCallActionToEntity(action nextagent.ToolCallAction) agententity.ToolCallAction {
	switch action {
	case nextagent.ToolCallAction_Reject:
		return agententity.ToolCallActionReject
	case nextagent.ToolCallAction_Confirm:
		return agententity.ToolCallActionConfirm
	case nextagent.ToolCallAction_Timeout:
		return agententity.ToolCallActionTimeout
	default:
		return ""
	}
}

func ConvertTemplateFormValueDetailToEntity(dto *nextagent.TemplateFormValueDetail) entity.TemplateFormValue {
	if dto == nil {
		return entity.TemplateFormValue{}
	}
	var variables = make(map[string]*entity.TemplateVariableValue)
	for k, v := range dto.Variables {
		if v == nil {
			continue
		}
		variables[k] = &entity.TemplateVariableValue{
			Content:     v.Content,
			Attachments: nil,
		}
	}
	return entity.TemplateFormValue{
		Variables: variables,
	}
}

func ConvertMCPScopeToEntity(scope nextagent.MCPScope) entity.MCPScope {
	switch scope {
	case nextagent.MCPScope_Public:
		return entity.MCPScopePublic
	case nextagent.MCPScope_Private:
		return entity.MCPScopePrivate
	case nextagent.MCPScope_ProjectPublic:
		return entity.MCPScopeProjectPublic
	default:
		return entity.MCPScopePrivate
	}
}

func ConvertCodebaseMentionToEntity(m *nextagent.CodebaseMention) *agententity.CodebaseMention {
	if m == nil {
		return nil
	}
	return &agententity.CodebaseMention{
		RepoName: m.RepoName,
		Branch:   lo.FromPtr(m.Branch),
		Tag:      lo.FromPtr(m.Tag),
		Path:     lo.FromPtr(lo.Ternary(m.FilePath != nil, m.FilePath, m.Directory)),
	}
}

func ConvertKnowledgeMentionToEntity(m *nextagent.KnowledgeMention, knowledge *entity.Document) *agententity.LarkDocMention {
	if m == nil || knowledge == nil {
		return nil
	}
	return &agententity.LarkDocMention{
		DocID:           lo.FromPtr(knowledge.DocToken),
		Title:           lo.FromPtr(m.Title),
		URL:             lo.FromPtr(m.URL),
		KnowledgeBaseID: knowledge.DatasetID,
	}
}

func ConvertAttachmentMentionToEntity(m *nextagent.AttachmentMention) *agententity.AttachmentMention {
	if m == nil {
		return nil
	}
	return &agententity.AttachmentMention{
		ArtifactID: m.ArtifactID,
		Path:       m.Path,
	}
}

func ConvertMentionTypeToEntity(t nextagent.MentionType) agententity.MentionType {
	switch t {
	case nextagent.MentionTypeKnowledgeBase:
		return agententity.MentionTypeLarkDoc
	case nextagent.MentionTypeRepository, nextagent.MentionTypeRepositoryBranch, nextagent.MentionTypeRepositoryDir, nextagent.MentionTypeRepositoryFile:
		return agententity.MentionTypeCodebase
	case nextagent.MentionTypeAttachment:
		return agententity.MentionTypeAttachment
	default:
		return nextagent.MentionTypeKnowledgeBase // 默认返回知识库类型
	}
}
