package entity

import (
	"time"

	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
)

// MCPSource 表示MCP工具来源
// 1-AIME 2-UserDefine 3-Cloud
// 与IDL保持一致
//go:generate stringer -type=MCPSource

type MCPSource int

const (
	MCPSourceAIME       MCPSource = 1 // AIME平台
	MCPSourceUserDefine MCPSource = 2 // 用户自定义
	MCPSourceCloud      MCPSource = 3 // 字节云平台
)

// MCPScope 表示MCP工具来源
// 从IDL扩展而来，与IDL保持一致
//go:generate stringer -type=MCPScope

type MCPScope int

const (
	MCPScopePrivate       MCPScope = 1 // 个人
	MCPScopePublic        MCPScope = 2 // 公司内公开
	MCPScopeProjectPublic MCPScope = 3 // 项目内公开
)

// MCPType 表示MCP工具类型
// 1-STDIO 2-SSE 3-StreamableHTTP 4-CloudSDK
// 与IDL保持一致
//go:generate stringer -type=MCPType

type MCPType int

const (
	MCPTypeSTDIO          MCPType = 1 // STDIO类型
	MCPTypeSSE            MCPType = 2 // SSE类型
	MCPTypeStreamableHTTP MCPType = 3 // StreamableHTTP类型 (二期新增)
	MCPTypeCloudSDK       MCPType = 4 // CloudSDK类型 (二期新增)
)

// ActiveStatus 表示MCP工具激活状态
// 1-激活 2-取消激活
// 与IDL保持一致
//go:generate stringer -type=ActiveStatus

type ActiveStatus int

const (
	ActiveStatusActivate   ActiveStatus = 1 // 激活
	ActiveStatusDeactivate ActiveStatus = 2 // 取消激活
)

// MCPConfig MCP工具参数结构
type MCPConfig struct {
	Command *string           `json:"command"`  // 命令
	Args    []string          `json:"args"`     // 参数列表
	Env     map[string]string `json:"env"`      // 环境变量
	BaseURL *string           `json:"base_url"` // SSE基础URL
	PSM     *string           `json:"psm"`      // PSM参数，用于StreamableHTTP和CloudSDK类型 (二期新增)
}

// MCP (Multi-Cloud Platform) 工具定义
// 对应 mcp.thrift 中的 MCP 结构体
// IsActive 表示是否已添加到个人工具
type MCP struct {
	ID int64 `json:"id"` // 数据库表自增主键
	MCPKey
	Name          string                  `json:"name"`
	EnName        string                  `json:"en_name"`
	Description   string                  `json:"description"`
	EnDescription string                  `json:"en_description"`
	IconURL       string                  `json:"icon_url"`
	Config        MCPConfig               `json:"config" gorm:"type:json"`
	Creator       string                  `json:"creator"`
	CreatedAt     time.Time               `json:"created_at"`
	UpdatedAt     time.Time               `json:"updated_at"`
	IsActive      bool                    `json:"is_active"`
	Type          MCPType                 `json:"type"`          // MCP工具类型
	ForceActive   bool                    `json:"force_active"`  // 是否强制激活
	SessionRoles  []nextagent.SessionRole `json:"session_roles"` // 支持的SessionRole列表，nil表示支持所有Role (二期新增)
	SourceSpaceID string                  `json:"source_space_id"`
	Scope         MCPScope                `json:"scope"`
	Permissions   []PermissionAction      `json:"permissions"`
}

// MCPKey MCP 唯一主键
type MCPKey struct {
	MCPID  string // mcp的id，同来源id唯一
	Source MCPSource
}

const MCPActivateLimit = 20 // 用户激活MCP的个数上限

type MCPCount struct {
	PublicCount   int64 // 公司内公开的MCP总数，会根据筛选条件变化
	CustomCount   int64 // 用户自定义的MCP总数，会根据筛选条件变化
	ActivateCount int64 // 已激活的MCP总数，不会根据筛选条件变化
	ActivateLimit int64 // 激活MCP的上限，不会根据筛选条件变化
}
