package entity

import (
	"time"

	agententity "code.byted.org/devgpt/kiwis/agentsphere/entity"
)

type Message struct {
	ID          string
	SessionID   string
	TaskID      string
	Role        MessageRole
	Content     MessageContent
	Creator     string
	Attachments []*Attachment
	Options     MessageOptions
	Status      MessageStatus
	Mentions    []*agententity.Mention
	CreatedAt   time.Time
	UpdatedAt   time.Time
}

type MessageContent struct {
	Content   string      `json:"content"`
	ToolCalls []*ToolCall `json:"tool_calls,omitempty"`
}

type MessageRole string

type MessageOptions struct {
	Locale string `json:"locale"`
}

const (
	MessageRoleUser      MessageRole = "user"
	MessageRoleAssistant MessageRole = "assistant"
)

type MessageStatus int

const (
	MessageStatusSent MessageStatus = 0
	MessageStatusWait MessageStatus = 1
)
