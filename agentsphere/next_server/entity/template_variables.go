package entity

import (
	"fmt"
	"time"

	"github.com/samber/lo"
)

type TemplateVariable struct {
	ID         string            `json:"id"`
	Key        string            `json:"key"`
	Value      TemplateFormValue `json:"value"`
	TemplateID string            `json:"template_id"`
	Creator    string            `json:"creator"`
	SpaceID    string            `json:"space_id"`
	CreatedAt  time.Time         `json:"created_at"`
	UpdatedAt  time.Time         `json:"updated_at"`
}

func (v TemplateFormValue) GetTemplateFormValueUniqueKey() string {
	var key string
	for k, v := range v.Variables {
		key += fmt.Sprintf("%s_%s", k, v.GetTemplateVariableValueUniqueKey())
	}
	return GetSHA256HashByContent(key)
}

func (v *TemplateVariableValue) GetTemplateVariableValueUniqueKey() string {
	if v == nil {
		return ""
	}
	var key = lo.FromPtr(v.Content)
	for _, attachment := range v.Attachments {
		key += fmt.Sprintf("%s_%s", attachment.ID, attachment.FileName)
	}
	return GetSHA256HashByContent(key)
}
