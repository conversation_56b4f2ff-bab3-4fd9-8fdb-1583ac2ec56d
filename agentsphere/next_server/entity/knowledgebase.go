package entity

import "time"

type KnowledgebaseEvent struct {
	DocumentContentEvent *DocumentContentEvent `json:"document_content_event,omitempty"`
}

type DocumentContentEvent struct {
	ID               string                `json:"id"`
	DatasetID        string                `json:"dataset_id"`
	Creator          string                `json:"creator"`
	SourceType       DocumentSourceType    `json:"source_type"`
	SourceUid        string                `json:"source_uid"`
	DocToken         *string               `json:"doc_token,omitempty"`
	ContentType      DocumentContentType   `json:"content_type"`
	LastUpdatedAt    time.Time             `json:"last_updated_at"`
	Title            string                `json:"title"`
	OldProcessStatus DocumentProcessStatus `json:"old_process_status"`
	ForceUpdate      bool                  `json:"force_update"`
}
