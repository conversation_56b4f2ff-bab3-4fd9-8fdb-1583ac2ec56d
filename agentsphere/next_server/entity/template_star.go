package entity

import (
	"time"
)

// TemplateStar 表示模板收藏实体
type TemplateStar struct {
	ID         string    `json:"id"`          // 唯一字符串ID，通常是UUID
	TemplateID string    `json:"template_id"` // 模板ID
	Username   string    `json:"username"`    // 用户名
	SpaceID    string    `json:"space_id"`    // 空间ID
	CreatedAt  time.Time `json:"created_at"`  // 创建时间
	UpdatedAt  time.Time `json:"updated_at"`  // 更新时间
}
