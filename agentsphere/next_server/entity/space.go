package entity

import (
	"time"

	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
)

// SpaceStatus defines the status of a space.
type SpaceStatus string

const (
	SpaceStatusActive   SpaceStatus = nextagent.SpaceStatusActive
	SpaceStatusInactive SpaceStatus = nextagent.SpaceStatusInactive
	SpaceStatusDeleted  SpaceStatus = nextagent.SpaceStatusDeleted
)

type SpaceType string

const (
	SpaceTypePersonal SpaceType = "personal"
	SpaceTypeProject  SpaceType = "project"
)

// Space represents a space entity.
type Space struct {
	ID          string         `json:"id"`
	Name        string         `json:"name"`
	NameEN      string         `json:"name_en"`
	Description string         `json:"description"`
	Creator     string         `json:"creator"`
	Type        SpaceType      `json:"type"` // Type of the space (personal, project, etc.)
	Status      SpaceStatus    `json:"status"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   *time.Time     `json:"deleted_at,omitempty"`
	Members     []*SpaceMember `json:"members,omitempty"` // Optional: for responses that include members
	DataSetID   string         `json:"dataset_id"`
}

func (t SpaceType) IsPersonal() bool {
	return t == SpaceTypePersonal
}

func (t SpaceType) IsProject() bool {
	return t == SpaceTypeProject
}

// SpaceMember represents a member of a space.
type SpaceMember struct {
	Type PermissionType `json:"type"`
	Name string         `json:"name"`
	Role PermissionRole `json:"role"`
	// CreatedAt time.Time      `json:"created_at"`
	// UpdatedAt time.Time      `json:"updated_at"`
}

// ToIDL converts an entity.SpaceMember to a nextagent.SpaceMember.
func (sm *SpaceMember) ToIDL() *nextagent.SpaceMember {
	if sm == nil {
		return nil
	}
	return &nextagent.SpaceMember{
		Name: sm.Name,
		Type: nextagent.PermissionType(sm.Type),
		Role: nextagent.PermissionRole(sm.Role),
	}
}
