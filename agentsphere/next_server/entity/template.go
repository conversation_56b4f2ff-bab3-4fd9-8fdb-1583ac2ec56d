package entity

import (
	"sort"
	"time"

	"github.com/samber/lo"

	agententity "code.byted.org/devgpt/kiwis/agentsphere/entity"
)

type TemplateFileMetadata struct{}

type TemplateVariableSchemaList []*TemplateVariableSchema

type TemplateVariableSchema struct {
	ID                string `json:"id"`
	Name              string `json:"name"`
	Default           string `json:"default"`
	Description       string `json:"description"`
	Placeholder       string `json:"placeholder"`
	SelectContent     string `json:"select_content"`
	AllowedUploadFile bool   `json:"allowed_upload_file"`
}

// TemplateVersion 表示模板版本实体
type TemplateVersion struct {
	ID              int64                      `json:"-"` // 数据表主键ID
	VersionID       string                     `json:"version_id"`
	TemplateID      string                     `json:"template_id"`
	Name            string                     `json:"name"`
	Status          string                     `json:"status"`
	Version         string                     `json:"version"`
	Scope           string                     `json:"scope"`
	Category        string                     `json:"category"`
	Creator         string                     `json:"creator"`
	SessionID       string                     `json:"session_id"`
	EventTimestamp  int64                      `json:"event_timestamp"`
	PromptContent   string                     `json:"prompt_content"`
	PromptVariables TemplateVariableSchemaList `json:"prompt_variables"`
	Plan            string                     `json:"plan"`
	PlanSteps       []string                   `json:"plan_steps"`
	ExpSop          *agententity.ExpSOP        `json:"exp_sop,omitempty"`
	SupportMCPs     []*MCPKey                  `json:"support_mcps"`
	Expired         bool                       `json:"expired"` // 用来标记经验是否过期，如 expired 为 true，则 exp_sop 在执行模板时不会生效
	Edited          bool                       `json:"edited"`  // 记录模板是否有编辑过，用来判断模板是否可用，如 edited 为 true
	Label           string                     `json:"label"`
	StarCount       int                        `json:"star_count"`
	SourceSpaceID   string                     `json:"source_space_id"`
	CreatedAt       time.Time                  `json:"created_at"`
	UpdatedAt       time.Time                  `json:"updated_at"`
	// 以下字段需要计算得到
	MCPs    []*MCP  `json:"mcps"`    // 支持的 MCP 详情列表
	Starred *bool   `json:"starred"` // 是否被当前用户收藏
	ShareID *string `json:"share_id"`
}

func (t *TemplateVersion) HasExperience() bool {
	return t.ExpSop != nil || t.Plan != ""
}

func (t *TemplateVersion) HasOrGeneratingExperience() bool {
	return t.HasExperience() || t.Status == TemplateStatusGenerating
}

func (t *TemplateVersion) PlanStepStatus() string {
	if t.Edited {
		return TemplatePlanStepStatusNone
	}
	if t.Status == TemplateStatusGenerating {
		return TemplatePlanStepStatusGenerating
	}
	if t.Status == TemplateStatusAvailable {
		return TemplatePlanStepStatusGenerated
	}
	return ""
}

func (t TemplateVariableSchemaList) ConvertToPlaceholder() []agententity.Placeholder {
	if t == nil {
		return nil
	}
	vars := []*TemplateVariableSchema(t)
	return lo.FilterMap(vars, func(v *TemplateVariableSchema, _ int) (agententity.Placeholder, bool) {
		if v == nil {
			return agententity.Placeholder{}, false
		}
		return agententity.Placeholder{
			Name:            v.Name,
			Default:         v.Default,
			Description:     v.Description,
			RefAattachments: v.AllowedUploadFile,
		}, true
	})
}

// 模板状态常量
const (
	TemplateStatusDraft      = "Draft"      // 草稿状态，此时对用户不可见
	TemplateStatusGenerating = "Generating" // 生成经验中，此时用户可见，但是不可执行
	TemplateStatusAvailable  = "Available"  // 可用状态，此时用户可见，且可执行
)

// 模板作用域常量
const (
	TemplateScopePublic        = "Public"        // 公开模板，所有用户可见
	TemplateScopePrivate       = "Private"       // 私有模板，仅创建者可见
	TemplateScopeShared        = "Shared"        // 共享模板，创建者和被分享用户可见
	TemplateScopeOfficial      = "Official"      // 官方模板，所有用户可见
	TemplateScopeProjectPublic = "ProjectPublic" // 项目内公开的模板，项目内成员可见
)

func (t *TemplateVersion) CanRead(username string) bool {
	if t == nil {
		return false
	}
	if t.Scope == TemplateScopePublic || t.Scope == TemplateScopeOfficial || t.Scope == TemplateScopeShared {
		return true
	}
	if t.Scope == TemplateScopePrivate && t.Creator == username {
		return true
	}
	return false
}

func (t *TemplateVersion) CanEdit(username string) bool {
	if t == nil {
		return false
	}
	return t.Creator == username
}

func (t *TemplateVersion) CanDelete(username string) bool {
	if t == nil {
		return false
	}
	// 被分享用户也有删除权限，但删除的只是分享记录，原模板不受影响
	if t.Scope == TemplateScopeShared {
		return true
	}
	return t.Creator == username
}

type TemplateSource string

const (
	TemplateSourceAll  TemplateSource = "all"
	TemplateSourceMy   TemplateSource = "my"
	TemplateSourceStar TemplateSource = "star"
)

type ModifyTemplate struct {
	Category  string                    `json:"category"`
	Name      *string                   `json:"name"`
	Prompt    *string                   `json:"prompt"`
	Variables []*TemplateVariableSchema `json:"variables"`
	Scope     string                    `json:"scope"` // 快捷模板的作用域，public、private、shared
	Label     string                    `json:"label"`

	// 正常情况下用户不会用到，仅管理员使用
	Edited bool `json:"edited"`
}

// 管理员手动修改/优化模版
type OpsModifyTemplate struct {
	Name                   *string
	ExpProgressPlan        *string
	ExpSOP                 *string
	Expired                *bool
	Edited                 *bool
	QueryTemplate          *string
	QueryTemplateVariables *string
	Scope                  *string
	SupportMCPs            *string
}

type TemplateKey struct {
	SessionID            string `json:"session_id"`
	LatestEventTimestamp int64  `json:"latest_event_timestamp"`
}

const (
	TemplateExperienceStatusSuccess = "success"
	TemplateExperienceStatusFailed  = "failed"
)

const (
	TemplatePlanStepStatusNone       = "none"       // 当前模板无需生成执行规划（比如因为用户编辑过模板）
	TemplatePlanStepStatusGenerating = "generating" // 当前模板正在生成执行规划
	TemplatePlanStepStatusGenerated  = "generated"  // 生成结束
)

type TemplateVersionPartials []*TemplateVersionPartial

// TemplateVersionPartial 表示模板版本实体部分字段
type TemplateVersionPartial struct {
	ID         int64     `json:"-"` // 数据表主键ID
	TemplateID string    `json:"template_id"`
	Scope      string    `json:"scope"`
	UpdatedAt  time.Time `json:"updated_at"`
	StarCount  int       `json:"star_count"`
}

// GetTemplateIDsByOffsetLimit 对列表排序并根据offset和limit获取模板ID列表
func (t TemplateVersionPartials) GetTemplateIDsByOffsetLimit(offset, limit int, startID int64) []string {
	if len(t) == 0 {
		return nil
	}
	scopePriority := map[string]int{
		TemplateScopeOfficial: 3,
		TemplateScopePublic:   2,
		TemplateScopePrivate:  1,
	}
	// 排序，优先级如下：
	// 1. 按照收藏个数倒序排列
	// 2. 按照模板scope排序，official > public > private
	// 3. 按照更新时间倒序排列
	sort.Slice(t, func(i int, j int) bool {
		// 收藏个数倒序
		if t[i].StarCount != t[j].StarCount {
			return t[i].StarCount > t[j].StarCount
		}
		res := scopePriority[t[i].Scope] - scopePriority[t[j].Scope]
		if res != 0 {
			return res > 0
		}
		return t[i].UpdatedAt.After(t[j].UpdatedAt)
	})
	// 过滤出排列在 start id 之后的模板ID
	part := make([]*TemplateVersionPartial, 0, len(t))
	if startID > 0 {
		start := false
		for _, v := range t {
			if v.ID == startID {
				start = true
				continue
			}
			if start {
				part = append(part, v)
			}
		}
	} else {
		part = t
	}
	if len(part) == 0 {
		return nil
	}
	var end int
	if limit <= 0 {
		end = len(part)
	} else {
		end = lo.Ternary(offset+limit < len(part), offset+limit, len(part))
	}
	// 分页
	return lo.Map(part[offset:end], func(item *TemplateVersionPartial, _ int) string {
		return item.TemplateID
	})
}
