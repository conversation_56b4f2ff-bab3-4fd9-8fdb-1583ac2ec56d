package main

import (
	agentservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/agent"
	artifactService "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/artifact"
	"code.byted.org/devgpt/kiwis/lib/metrics"
	"code.byted.org/videoarch/imagex-sdk-golang/v2/base"
	"code.byted.org/videoarch/imagex-sdk-golang/v2/service/imagex"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/fx"

	"code.byted.org/devgpt/kiwis/agentsphere/common/credential"
	"code.byted.org/devgpt/kiwis/agentsphere/common/redissemaphore"
	nextserverdal "code.byted.org/devgpt/kiwis/agentsphere/next_server/dal"
	activityservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/activity"
	deploymentservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/deployment"
	nextKnowledgebaseService "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/knowledgebase"
	nextLarkService "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/lark"
	permissionservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/permission"
	replayservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/replay"
	nextsessionservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/session"
	sessioncollectionservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/session_collection"
	shareservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/share"
	spaceservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/space"
	traceservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/trace"
	userservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/user"
	larkservice "code.byted.org/devgpt/kiwis/agentsphere/notification"
	runtimedal "code.byted.org/devgpt/kiwis/agentsphere/runtime/dal"
	runtimeservice "code.byted.org/devgpt/kiwis/agentsphere/runtime/service"
	serverdal "code.byted.org/devgpt/kiwis/agentsphere/server/dal"
	serverservice "code.byted.org/devgpt/kiwis/agentsphere/server/service"
	sessiondal "code.byted.org/devgpt/kiwis/agentsphere/session/dal"
	sessionservice "code.byted.org/devgpt/kiwis/agentsphere/session/service"
	"code.byted.org/devgpt/kiwis/copilotstack/common/auth"
	"code.byted.org/devgpt/kiwis/copilotstack/llm"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/di"
	libtcc "code.byted.org/devgpt/kiwis/lib/tcc"
	"code.byted.org/devgpt/kiwis/port/codebase"
	"code.byted.org/devgpt/kiwis/port/db"
	"code.byted.org/devgpt/kiwis/port/lark"
	"code.byted.org/devgpt/kiwis/port/redis"
	"code.byted.org/devgpt/kiwis/port/stratocube"
	"code.byted.org/devgpt/kiwis/port/tcc"
	"code.byted.org/devgpt/kiwis/port/tos"

	// Tool to capture panic and OOM issues,
	// https://bytedance.feishu.cn/docs/doccnJd5mBoumB2wnmfg9P3D3Ve
	_ "code.byted.org/temai/blame_helper"
)

var ConfigModule = fx.Options(
	// Load config from file.
	fx.Provide(config.LoadConfigFunc[config.AgentSphereConfig]("conf/config.agentsphere.apiserver")),
	// Provide sub config items to di graph.
	fx.Provide(di.StructProvider(new(config.AgentSphereConfig), false)),
	// Create tcc client.
	fx.Provide(tcc.NewClient),
	// Inject tcc config to struct.
	fx.Provide(func(cli tcc.Client) (*config.AgentSphereTCCConfig, error) {
		c := new(config.AgentSphereTCCConfig)
		if err := libtcc.InjectTCCConfig(c, cli); err != nil {
			return nil, errors.WithMessage(err, "failed to inject tcc config")
		}
		return c, nil
	}),
	// Provide sub TCC config items to di graph.
	fx.Provide(di.StructProvider(new(config.AgentSphereTCCConfig), false)),
)

func main() {
	app := fx.New(
		HertzModule,
		ConfigModule,
		auth.Module,
		MQModule,
		redis.Module,
		fx.Provide(db.NewRDSClient),
		fx.Provide(redissemaphore.NewRedisSemaphore),
		fx.Provide(credential.NewCache),
		fx.Provide(func(tosConfig *libtcc.GenericConfig[config.TOSConfig]) (tos.Client, error) {
			conf := tosConfig.GetValue()
			return tos.NewClient(conf.Bucket, conf.AccessKey, conf.SecretKey)
		}),
		fx.Provide(func(stratocubeConfig *libtcc.GenericConfig[config.StratoCubeConfig]) stratocube.Client {
			conf := stratocubeConfig.GetValue()
			return stratocube.NewClient(stratocube.ClientOption{
				BaseURL: conf.BaseURL,
				Tenant: lo.Map(stratocubeConfig.GetValue().TenantConfig, func(item config.StratoCubeTenantConfig, index int) stratocube.Tenant {
					return stratocube.Tenant{
						Key:    item.TenantKey,
						ID:     item.TenantID,
						Name:   item.TenantName,
						Secret: item.Secret,
					}
				}),
			})
		}),
		// AgentSphere runtime container providers.
		fx.Provide(runtimeservice.NewRuntimeProviderRegistry),
		// AgentSphere server service.
		fx.Provide(serverservice.NewService),
		// AgentSphere server DAO.
		fx.Provide(di.StructConstructor(new(serverdal.DAO))),
		// AgentSphere runtime server DAO.
		fx.Provide(di.StructConstructor(new(runtimedal.DAO))),
		// NextAgent server DAO
		fx.Provide(di.StructConstructor(new(nextserverdal.DAO))),
		// AgentSphere runtime server service.
		fx.Provide(runtimeservice.NewService),
		// AgentSphere session server service.
		fx.Provide(sessionservice.NewService),
		// AgentSphere next artifact service
		fx.Provide(artifactService.NewService),
		// AgentSphere next deployment service
		fx.Provide(deploymentservice.NewService),
		// AgentSphere next replay service
		fx.Provide(replayservice.NewService),
		// AgentSphere next activity service
		fx.Provide(activityservice.NewService),
		// AgentSphere next user service
		fx.Provide(userservice.NewService),
		// AgentSphere next session_collection service
		fx.Provide(sessioncollectionservice.NewService),
		// AgentSphere next session service
		fx.Provide(nextsessionservice.NewService),
		// AgentSphere s
		fx.Provide(shareservice.NewService),
		// AgentSphere next session service
		fx.Provide(traceservice.NewService),
		// AgentSphere next agent service
		fx.Provide(agentservice.NewService),
		// AgentSphere permission service
		fx.Provide(permissionservice.NewService),
		// AgentSphere knowledgebase service
		fx.Provide(nextKnowledgebaseService.NewService),
		// AgentSphere space service.
		fx.Provide(spaceservice.NewService),
		// AgentSphere
		fx.Provide(func(tccConf *config.AgentSphereTCCConfig) lark.Client {
			conf := tccConf.NeumaLarkAppConfig.GetValue()
			return lark.NewClient(conf.AppID, conf.AppSecret, conf.DevGPTServiceToken, conf.RedirectURI)
		}),
		// AgentSphere next lark service
		fx.Provide(nextLarkService.NewService),

		fx.Provide(func(tccConf *config.AgentSphereTCCConfig) *imagex.ImageXClient {
			instance := imagex.NewInstance()
			conf := tccConf.ImageXConfig.GetValue()
			instance.SetCredential(base.Credentials{
				AccessKeyID:     conf.AccessKey,
				SecretAccessKey: conf.SecretKey,
			})
			return instance
		}),

		// AgentSphere session server DAO.
		fx.Provide(di.StructConstructor(new(sessiondal.DAO))),
		// LLM service.
		llm.Module,
		codebase.Module,
		// Lark service.
		fx.Provide(func(tccConf *config.AgentSphereTCCConfig) *larkservice.Service {
			conf := tccConf.LarkAppConfig.GetValue()
			return larkservice.NewService(conf)
		}),
		fx.Invoke(metrics.InitNextServerMetric),
		fx.Invoke(metrics.InitMetric),
	)
	app.Run()
}
