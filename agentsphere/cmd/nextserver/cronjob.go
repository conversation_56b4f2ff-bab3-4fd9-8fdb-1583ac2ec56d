package main

import (
	"os"

	nextKnowledgebaseService "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/knowledgebase"
	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/logs/v2"
	"go.uber.org/fx"
)

var CronJobModule = fx.Invoke(func(knowledgebaseService *nextKnowledgebaseService.Service) error {
	if env.Cluster() != "cronjob" {
		return nil
	}
	if os.Getenv("KNOWLEDGEBASSE_ENABLE_PATROL") == "1" {
		logs.Info("knowledgebase patrol is started")
		return knowledgebaseService.StartCron()
	}
	return nil
})
