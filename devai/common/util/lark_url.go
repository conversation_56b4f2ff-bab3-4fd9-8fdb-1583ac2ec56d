package util

import (
	"context"
	"net/url"
	"strings"

	"code.byted.org/gopkg/logs/v2"
)

func ParseLarkDocURL(ctx context.Context, u string) (sourceType string, sourceKey string) {
	parse, err := url.Parse(u)
	if err != nil {
		logs.CtxError(ctx, "failed to parse lark doc url: %s, err: %v", u, err)
		return "", ""
	}
	split := strings.Split(parse.Path, "/")
	if len(split) < 3 {
		return "", ""
	}
	switch parse.Host {
	case "cloud.bytedance.net":
		return "bytecloud", split[len(split)-1]
	case "bytedance.larkoffice.com", "bytedance.feishu.cn":
		switch split[1] {
		case "wiki", "docx", "doc":
			return split[1], split[2]
		case "docs":
			return "doc", split[2]
		case "base":
			return "bitable", split[2]
		case "sheets":
			return "sheet", split[2]
		default:
			return "", ""
		}
	default:
		return "", ""
	}
}
