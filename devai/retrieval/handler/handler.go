package handler

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"time"

	devaiutil "code.byted.org/devgpt/kiwis/devai/common/util"

	htmltomarkdown "github.com/<PERSON>/html-to-markdown/v2"

	"code.byted.org/devgpt/kiwis/lib/hertz"
	"code.byted.org/devgpt/kiwis/port/abase"

	"golang.org/x/sync/errgroup"

	"code.byted.org/devgpt/kiwis/search/dal/crawler"

	datadal "code.byted.org/devgpt/kiwis/devai/data/dal"
	"code.byted.org/devgpt/kiwis/devai/knowledge/dal"
	"code.byted.org/devgpt/kiwis/lib/util"

	"code.byted.org/devgpt/kiwis/devai/data/entity"
	"code.byted.org/devgpt/kiwis/port/arcosite"
	"code.byted.org/devgpt/kiwis/port/bytecloud"

	"code.byted.org/gopkg/logs/v2"
	larkdrive "github.com/larksuite/oapi-sdk-go/v3/service/drive/v1"
	"github.com/pkg/errors"
	"github.com/samber/lo"

	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/common"
	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/devai"
	"code.byted.org/devgpt/kiwis/devai/common/constant"
	"code.byted.org/devgpt/kiwis/devai/data/service/loader"
	"code.byted.org/devgpt/kiwis/devai/retrieval/pack"
	"code.byted.org/devgpt/kiwis/devai/retrieval/service/strategy"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/kitex"
	"code.byted.org/devgpt/kiwis/lib/tcc"
	"code.byted.org/devgpt/kiwis/port/lark"
)

type LarkMetadataCache = abase.Client
type RetrievalHandler struct {
	StrategyService   *strategy.Service
	AgentXConfig      *tcc.GenericConfig[config.AgentXTCCConfig]
	ByteCloudClient   bytecloud.Client
	ArcoSiteClient    arcosite.Client
	DataDAO           *datadal.DAO
	DAO               *dal.DAO
	Crawler           crawler.Crawler
	LarkMetadataCache *LarkMetadataCache
}

func (h *RetrievalHandler) Recall(ctx context.Context, req *devai.RecallRequest) (r *devai.RecallResponse, err error) {
	if req == nil || req.TopK == 0 || req.Query == "" {
		return nil, kitex.NewKitexError(common.ErrorCode_ErrParamInvalid, errors.New("param invalid"))
	}
	larkUserToken := ""
	if req.RecallExtra != nil && req.RecallExtra.UserLarkToken != nil {
		larkUserToken = *req.RecallExtra.UserLarkToken
	}
	segments, err := h.StrategyService.Run(ctx, req.Query, req.TopK, strategy.Opt{
		NodeIDs:       req.NodeIDs,
		Intent:        req.Intent,
		Username:      req.Username,
		LarkUserToken: larkUserToken,
		SourceTypes:   req.SourceType,
	})
	if err != nil {
		return nil, err
	}
	return &devai.RecallResponse{
		RecallSegments: pack.RecallSegmentsFromEntities(segments),
	}, nil
}

func (h *RetrievalHandler) RecallOpenAPI(ctx context.Context, req *devai.RecallOpenAPIRequest) (r *devai.RecallOpenAPIResponse, err error) {
	if req == nil || req.TopK == 0 || req.Query == "" {
		return nil, kitex.NewKitexError(common.ErrorCode_ErrParamInvalid, errors.New("param invalid"))
	}
	segments, err := h.StrategyService.Run(ctx, req.Query, req.TopK, strategy.Opt{
		NodeIDs: req.NodeIDs,
	})
	if err != nil {
		return nil, err
	}
	return &devai.RecallOpenAPIResponse{
		RecallSegments: pack.RecallSegmentsFromEntities(segments),
	}, nil
}

func (h *RetrievalHandler) GetRelatedKnowledgeAPI(ctx context.Context, req *devai.GetRelatedKnowledgeRequest) (r *devai.GetRelatedKnowledgeResponse, err error) {
	if req == nil || req.Query == "" {
		return nil, kitex.NewKitexError(common.ErrorCode_ErrParamInvalid, errors.New("param invalid"))
	}
	segments, err := h.StrategyService.GetRelatedKnowledgeAPI(ctx, req.Query)
	if err != nil {
		return nil, err
	}

	return &devai.GetRelatedKnowledgeResponse{
		RecallSegments: pack.RecallSegmentsFromEntities(segments),
	}, nil
}

func (h *RetrievalHandler) CoarseRecallOpenAPI(ctx context.Context, req *devai.CoarseRecallOpenAPIRequest) (r *devai.CoarseRecallOpenAPIResponse, err error) {
	segments, err := h.StrategyService.CoarseRecall(ctx, req.Query, "", strategy.Opt{
		NodeIDs: req.NodeIDs,
	})
	if err != nil {
		return nil, err
	}
	return &devai.CoarseRecallOpenAPIResponse{
		RecallSegments: pack.RecallSegmentsFromEntities(segments),
	}, nil
}

func (h *RetrievalHandler) ToutiaoHybridSearchAgentAPI(ctx context.Context, req *devai.ToutiaoHybridRecallAgentRequest) (r *devai.ToutiaoHybridRecallAgentResponse, err error) {
	if req == nil || req.TopK == 0 {
		return nil, kitex.NewKitexError(common.ErrorCode_ErrParamInvalid, errors.New("param invalid"))
	}

	segments, err := h.StrategyService.ToutiaoHybridSearch(ctx, req.Query, req.TopK)
	if err != nil {
		return nil, err
	}

	return &devai.ToutiaoHybridRecallAgentResponse{
		RecallSegments: pack.RecallSegmentsFromEntities(segments),
	}, nil
}

func (h *RetrievalHandler) RecallAgentAPI(ctx context.Context, req *devai.RecallAgentRequest) (r *devai.RecallAgentResponse, err error) {
	if req == nil || req.TopK == 0 {
		return nil, kitex.NewKitexError(common.ErrorCode_ErrParamInvalid, errors.New("param invalid"))
	}
	logs.CtxInfo(ctx, "[RecallAgentAPI] RecallExtra is %v", req.RecallExtra)
	larkUserToken := ""
	if req.RecallExtra != nil && req.RecallExtra.UserLarkToken != nil {
		larkUserToken = *req.RecallExtra.UserLarkToken
	}
	user := ""
	if req.RecallExtra != nil && req.RecallExtra.UserName != nil {
		user = *req.RecallExtra.UserName
	}
	segments, err := h.StrategyService.AgentRecall(ctx, req.Query, user, strategy.AgentRecallOpt{
		LarkUserToken:     larkUserToken,
		EnableBytedSearch: lo.FromPtrOr(req.EnableBytedSearch, true),
		Recallers:         req.Recallers,
	})
	if err != nil {
		return nil, err
	}
	return &devai.RecallAgentResponse{
		RecallSegments: pack.RecallSegmentsFromEntities(segments),
	}, nil
}

func (h *RetrievalHandler) CheckLarkToken(ctx context.Context, req *devai.CheckLarkTokenRequest) (r *devai.CheckLarkTokenResponse, err error) {
	value := h.AgentXConfig.GetValue()
	client := lark.NewClient(value.LarkAppID, value.LarkAppToken, "", "")
	_, err = client.SearchLarkData(ctx, 1, "test", req.Token, nil)
	if err != nil {
		return nil, kitex.NewKitexError(common.ErrorCode_ErrParamInvalid, err)
	}
	return &devai.CheckLarkTokenResponse{}, nil
}

func (h *RetrievalHandler) AgentReadLark(ctx context.Context, req *devai.AgentReadLarkRequest) (*devai.AgentReadLarkResponse, error) {
	if len(req.DocumentID) > 0 {
		content, err := h.getDocumentByDocumentID(ctx, util.FromString(req.DocumentID))
		if err != nil {
			return nil, errors.WithMessage(err, "failed to get document by knowledge id")
		}
		return &devai.AgentReadLarkResponse{
			Content: content,
		}, nil
	}
	sourceType, sourceKey := devaiutil.ParseLarkDocURL(ctx, req.URL)
	value := h.AgentXConfig.GetValue()
	larkClient := lark.NewClient(value.LarkAppID, value.LarkAppToken, "", "")
	if sourceType == "wiki" {
		node, err := larkClient.GetWikiNode(ctx, sourceKey, "", lark.Option{UserAccessToken: req.LarkToken})
		if err != nil {
			return nil, errors.WithMessage(err, "failed to get wiki node")
		}
		switch lo.FromPtr(node.ObjType) {
		case "doc", "docx":
			sourceType = lo.FromPtr(node.ObjType)
		default:
			return nil, errors.New("unsupported wiki node type")
		}
		sourceKey = lo.FromPtr(node.ObjToken)
	}
	var (
		content string
		err     error
	)
	switch sourceType {
	case "bytecloud":
		cloudLoader := loader.NewByteCloudLoader(h.ByteCloudClient, h.ArcoSiteClient, larkClient)
		content, err = cloudLoader.Load(ctx, loader.LoadOption{
			Source:    entity.DatasourceSource{},
			SourceKey: sourceKey,
		})
		if err != nil {
			return nil, errors.WithMessage(err, "failed to load lark doc")
		}
	case "docx", "doc":
		larkLoader := loader.NewLarkLoaderWithOptions(larkClient, lark.Option{UserAccessToken: req.LarkToken})
		content, err = larkLoader.Load(ctx, loader.LoadOption{
			SourceKey:   sourceKey,
			ContentType: constant.DocumentContentType(sourceType),
		})
		if err != nil {
			return nil, errors.WithMessage(err, "failed to load lark doc")
		}
	default:
		return nil, errors.New("unsupported lark doc type")
	}
	return &devai.AgentReadLarkResponse{
		Content: content,
	}, nil
}

func (h *RetrievalHandler) getDocumentByDocumentID(ctx context.Context, documentID int64) (string, error) {
	value := h.AgentXConfig.GetValue()
	larkClient := lark.NewClient(value.LarkAppID, value.LarkAppToken, "", "")
	document, err := h.DataDAO.GetDocumentByID(ctx, documentID)
	if err != nil {
		return "", errors.WithMessage(err, "failed to get document by id")
	}
	switch document.Type {
	case constant.DocumentContentTypeBytecloud:
		cloudLoader := loader.NewByteCloudLoader(h.ByteCloudClient, h.ArcoSiteClient, larkClient)
		content, err := cloudLoader.Load(ctx, loader.LoadOption{
			Source:    entity.DatasourceSource{},
			SourceKey: document.SourceKey,
		})
		if err != nil {
			return "", errors.WithMessage(err, "failed to load bytecloud doc")
		}
		return content, nil
	case constant.DocumentContentTypeArcosite:
		datasourceItems, err := h.DataDAO.MGetDatasourceItemsByItemIDs(ctx, []int64{documentID})
		if err != nil {
			return "", errors.WithMessage(err, "failed to get datasource items by item ids")
		}
		if len(datasourceItems) == 0 || len(datasourceItems[documentID]) == 0 {
			return "", errors.New("datasource items not found")
		}
		datasourceID := datasourceItems[documentID][0].DatasourceID
		datasource, err := h.DataDAO.GetDatasourceByID(ctx, datasourceID)
		if err != nil {
			return "", errors.WithMessage(err, "failed to get datasource by id")
		}

		siteLoader := loader.NewArcoSiteLoader(h.ArcoSiteClient, larkClient, h.ByteCloudClient)
		arcoSiteSource := datasource.Source.ArcoSite
		if arcoSiteSource == nil {
			return "", errors.New("arcosite source not found")
		}
		content, err := siteLoader.Load(context.Background(), loader.LoadOption{
			SourceType:          "",
			ContentType:         constant.DocumentContentTypeArcosite,
			SourceKey:           document.SourceKey,
			Operator:            "",
			DocumentExtra:       nil,
			DatasourceSourceKey: arcoSiteSource.AppID,
		})
		if err != nil {
			return "", errors.WithMessage(err, "failed to load arcosite doc")
		}
		return content, nil
	default:
		return "", errors.New("unsupported document type")
	}
}

func (h *RetrievalHandler) AgentReadLarkMeta(ctx context.Context, req *devai.AgentReadLarkMetaRequest) (*devai.AgentReadLarkMetaResponse, error) {
	value := h.AgentXConfig.GetValue()
	client := lark.NewClient(value.LarkAppID, value.LarkAppToken, "", "")
	sourceType, sourceKey := devaiutil.ParseLarkDocURL(ctx, req.URL)
	if sourceType == "" || sourceKey == "" {
		logs.CtxError(ctx, "failed to parse lark doc url: %s", req.URL)
		return nil, kitex.NewKitexError(common.ErrorCode_ErrParamInvalid, errors.Errorf("failed to parse lark url %s", req.URL))
	}
	cache, err := h.getLarkDocMetadataCache(ctx, sourceType, sourceKey)
	if cache != nil && cache.CreatedAt.After(time.Now().Add(time.Hour*24)) {
		return &devai.AgentReadLarkMetaResponse{
			UV:             cache.UV,
			PV:             cache.PV,
			LikeCount:      cache.LikeCount,
			UVToday:        cache.UVToday,
			PVToday:        cache.PVToday,
			LikeCountToday: cache.LikeCountToday,
			LastModifyTime: cache.LastModifyTime,
			OnwerID:        cache.OwnerID,
			SecLabelName:   cache.SecLabelName,
			LastModifyUser: cache.LastModifyUser,
		}, nil
	}
	var fileMeta *larkdrive.BatchQueryMetaRespData
	var fileStatistics lark.FileStatistics
	group, ctx := errgroup.WithContext(ctx)
	group.Go(func() error {
		fileMeta, err = client.GetFilesMeta(ctx, []*lark.RequestDoc{
			{
				DocToken: sourceKey,
				DocType:  sourceType,
			},
		}, req.LarkToken, lark.Option{UserIDType: lark.UserIDTypeOpenID})
		if err != nil {
			return errors.WithMessage(err, "failed to get file meta from lark")
		}
		return nil
	})

	group.Go(func() error {
		fileStatistics, err = client.GetLarkFileStatisticsInfo(ctx, sourceKey, sourceType, req.LarkToken)
		if err != nil {
			return errors.WithMessage(err, "failed to get file statistics from lark")
		}
		return nil
	})

	if err = group.Wait(); err != nil {
		logs.CtxError(ctx, "failed to get file meta and statistics from lark, err: %v", err)
		if cache != nil {
			// Fallback to cache's value.
			return &devai.AgentReadLarkMetaResponse{
				UV:             cache.UV,
				PV:             cache.PV,
				LikeCount:      cache.LikeCount,
				UVToday:        cache.UVToday,
				PVToday:        cache.PVToday,
				LikeCountToday: cache.LikeCountToday,
				LastModifyTime: cache.LastModifyTime,
				OnwerID:        cache.OwnerID,
				SecLabelName:   cache.SecLabelName,
				LastModifyUser: cache.LastModifyUser,
			}, nil
		}
		return nil, errors.WithMessage(err, "failed to get file meta and statistics from lark")
	}

	result := devai.AgentReadLarkMetaResponse{}

	if fileMeta != nil && len(fileMeta.Metas) == 1 && fileMeta.Metas[0] != nil {
		result.LastModifyTime = lo.FromPtr(fileMeta.Metas[0].LatestModifyTime)
		result.OnwerID = lo.FromPtr(fileMeta.Metas[0].OwnerId)
		result.SecLabelName = lo.FromPtr(fileMeta.Metas[0].SecLabelName)
		result.LastModifyUser = lo.FromPtr(fileMeta.Metas[0].LatestModifyUser)
	}

	result.UV = int32(fileStatistics.UV)
	result.PV = int32(fileStatistics.PV)
	result.UVToday = int32(fileStatistics.UVToday)
	result.PVToday = int32(fileStatistics.PVToday)
	result.LikeCount = int32(fileStatistics.LikeCount)
	result.LikeCountToday = int32(fileStatistics.LikeCountToday)
	err = h.saveLarkDocMetadataCache(ctx, err, sourceType, sourceKey, result)
	if err != nil {
		logs.CtxError(ctx, "failed to set lark meta getLarkDocMetadataCache, err: %v", err)
	}
	return &result, nil
}

func (h *RetrievalHandler) saveLarkDocMetadataCache(ctx context.Context, err error, sourceType string, sourceKey string, result devai.AgentReadLarkMetaResponse) error {
	// Retry to read from cache.
	value, err := json.Marshal(&larkDocMetadataCache{
		CreatedAt:      time.Now(),
		UV:             result.UV,
		PV:             result.PV,
		LikeCount:      result.LikeCount,
		UVToday:        result.UVToday,
		PVToday:        result.PVToday,
		LikeCountToday: result.LikeCountToday,
		LastModifyTime: result.LastModifyTime,
		OwnerID:        result.OnwerID,
		SecLabelName:   result.SecLabelName,
		LastModifyUser: result.LastModifyUser,
	})
	if err != nil {
		return errors.WithStack(err)
	}
	err = h.LarkMetadataCache.Set(ctx, fmt.Sprintf("%s.%s", sourceType, sourceKey), value, 0)
	return err
}

func (h *RetrievalHandler) getLarkDocMetadataCache(ctx context.Context, sourceType string, sourceKey string) (*larkDocMetadataCache, error) {
	// Retry to read from cache.
	cacheContent, err := h.LarkMetadataCache.Get(ctx, fmt.Sprintf("%s.%s", sourceType, sourceKey))
	if err != nil && !abase.IsNil(err) {
		logs.CtxError(ctx, "failed to get lark meta from getLarkDocMetadataCache, err: %v", err)
	}
	var cache *larkDocMetadataCache
	if len(cacheContent) > 0 {
		err := json.Unmarshal([]byte(cacheContent), &cache)
		if err != nil {
			cache = nil
			logs.CtxError(ctx, "failed to unmarshal lark meta from getLarkDocMetadataCache, err: %v", err)
		}
	}
	logs.CtxInfo(ctx, "get lark meta from getLarkDocMetadataCache, cache: %v", cache)
	return cache, err
}

type larkDocMetadataCache struct {
	CreatedAt      time.Time `json:"created_at"`
	UV             int32     `json:"uv"`
	PV             int32     `json:"pv"`
	LikeCount      int32     `json:"like_count"`
	UVToday        int32     `json:"uv_today"`
	PVToday        int32     `json:"pv_today"`
	LikeCountToday int32     `json:"like_count_today"`
	LastModifyTime string    `json:"last_modify_time"`
	OwnerID        string    `json:"owner_id"`
	SecLabelName   string    `json:"sec_label_name"`
	LastModifyUser string    `json:"last_modify_user"`
}

func (h *RetrievalHandler) AgentReadWeb(ctx context.Context, req *devai.AgentReadWebRequest) (*devai.AgentReadWebResponse, error) {
	client, err := hertz.NewClient("https://stratos-browser-mcp.bytedance.net", hertz.NewHTTPClientOption{
		Timeout: time.Second * 30,
	})
	if err != nil {
		return nil, err
	}
	type response struct {
		Content string `json:"content"`
	}
	var resp response
	var (
		markdownContent string
		htmlContent     string
	)
	escape := url.QueryEscape(req.URL)
	_, err = client.DoJSONReq(ctx, http.MethodGet, "/?url="+escape, hertz.ReqOption{
		Result:          &resp,
		SetTTEnvHeaders: true,
	})
	if err != nil {
		logs.CtxError(ctx, "failed to get web content from stratos, err: %v", err)
	} else {
		htmlContent = resp.Content
		markdownContent, err = htmltomarkdown.ConvertString(htmlContent)
	}
	if len(markdownContent) == 0 {

		// Fallback to caller.
		logs.CtxError(ctx, "failed to get web content, err: %v", err)
		markdownContent, err = h.Crawler.Crawl(ctx, req.URL)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to crawl web content")
		}
	}
	return &devai.AgentReadWebResponse{
		HTMLContent:     htmlContent,
		MarkdownContent: markdownContent,
	}, nil
}
