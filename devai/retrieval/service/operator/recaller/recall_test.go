package recaller

import (
	"context"
	"testing"

	"go.uber.org/mock/gomock"
	"gotest.tools/v3/assert"

	knowledgebaseIDL "code.byted.org/devgpt/kiwis/api/idl/kitex_gen/knowledgebase"
	"code.byted.org/devgpt/kiwis/devai/data/test"
	"code.byted.org/devgpt/kiwis/devai/knowledge/entity"
	knowledgetest "code.byted.org/devgpt/kiwis/devai/knowledge/test"
	"code.byted.org/devgpt/kiwis/lib/metrics"
	"code.byted.org/devgpt/kiwis/port/lark"
	larkmock "code.byted.org/devgpt/kiwis/port/lark/mock"
)

// Lark Search Operator Unit Test
func TestLarkSearchOperator(t *testing.T) {
	ctx := context.Background()

	_ = metrics.InitDevAIKnowledgeMetric()
	tests := []struct {
		name      string
		mockFunc  func(ctx context.Context, mockOption *mockOption)
		checkFunc func(t *testing.T, h *LarkSearchOperator)
	}{
		{
			name: "lark search operator",
			mockFunc: func(ctx context.Context, mockOption *mockOption) {
				mockOption.MockLarkCli.EXPECT().SearchLarkData(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&lark.SearchMeta{
					Data: struct {
						Passages []lark.SearchPassage `json:"passages"`
					}(struct{ Passages []lark.SearchPassage }{
						[]lark.SearchPassage{},
					}),
				}, nil)
			},
			checkFunc: func(t *testing.T, operator *LarkSearchOperator) {
				_, err := operator.Run(ctx)
				assert.NilError(t, err)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			operator, opt := newTestLarkSearchOperator(t)
			tt.mockFunc(ctx, opt)
			tt.checkFunc(t, operator)
		})
	}

}

func newTestLarkSearchOperator(t *testing.T) (*LarkSearchOperator, *mockOption) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	var (
		larkCli = larkmock.NewMockClient(ctrl)
	)

	operator := &LarkSearchOperator{
		LarkClient: larkCli,
		MetaData: LarkSearchMetaData{
			Query:     "oncall",
			TopK:      10,
			UserToken: "**********************************************",
		},
	}
	opt := &mockOption{
		MockLarkCli: larkCli,
	}
	return operator, opt
}

// Lark Baike Operator Unit Test
func TestLarkBaikeOperator(t *testing.T) {
	ctx := context.Background()

	_ = metrics.InitDevAIKnowledgeMetric()
	tests := []struct {
		name      string
		mockFunc  func(ctx context.Context, mockOption *mockOption)
		checkFunc func(t *testing.T, h *LarkBaikeOperator)
	}{
		{
			name: "lark baike operator",
			mockFunc: func(ctx context.Context, mockOption *mockOption) {
				mockOption.MockLarkCli.EXPECT().GetLarkBaikePhrases(gomock.Any(), gomock.Any()).Return(nil, nil)
				mockOption.MockLarkCli.EXPECT().GetLarkEntityDesc(gomock.Any(), gomock.Any()).Return(nil, nil)
			},
			checkFunc: func(t *testing.T, operator *LarkBaikeOperator) {
				_, err := operator.Run(ctx)
				assert.NilError(t, err)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			operator, opt := newTestLarkBaikeOperator(t)
			tt.mockFunc(ctx, opt)
			tt.checkFunc(t, operator)
		})
	}
}

func newTestLarkBaikeOperator(t *testing.T) (*LarkBaikeOperator, *mockOption) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	var (
		larkCli = larkmock.NewMockClient(ctrl)
	)

	operator := &LarkBaikeOperator{
		LarkClient: larkCli,
		MetaData: LarkBaikeMetaData{
			Query: "oncall",
		},
	}
	opt := &mockOption{
		MockLarkCli: larkCli,
	}
	return operator, opt
}

// Knowledge Base Operator Unit Test
func TestKnowledgeBaseOperator(t *testing.T) {
	ctx := context.Background()

	_ = metrics.InitDevAIKnowledgeMetric()
	tests := []struct {
		name      string
		mockFunc  func(ctx context.Context, mockOption *mockOption)
		checkFunc func(t *testing.T, h *KnowledgeBaseOperator)
	}{
		{
			name: "knowledge base operator",
			mockFunc: func(ctx context.Context, mockOption *mockOption) {
				mockOption.MockKnowledgebaseService.EXPECT().Recall(gomock.Any(), gomock.Any()).AnyTimes().Return(
					[]*knowledgebaseIDL.RetrievalSegment{
						{
							Segment: &knowledgebaseIDL.Segment{
								Type: "document",
								DocumentSegment: &knowledgebaseIDL.DocumentSegment{
									Content: "oncall",
								},
							},
						},
						{
							Segment: &knowledgebaseIDL.Segment{
								Type: "qa",
								QASegment: &knowledgebaseIDL.QASegment{
									Question: "oncall",
									Answer:   "oncall",
								},
							},
						},
					}, nil)
				mockOption.MockKnowledgeService.EXPECT().MGetKnowledgeByIDs(gomock.Any(), gomock.Any()).AnyTimes().Return(nil, nil)
				mockOption.MockKnowledgeService.EXPECT().MGetKnowledgePaths(gomock.Any(), gomock.Any()).AnyTimes().Return(map[int64]*entity.Knowledge{
					123: {
						ID: 123,
					},
				}, nil)
				mockOption.MockKnowledgeTreeNodeService.EXPECT().GetRetrievalTreeNodeIDs(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return([]int64{123}, nil)
			},
			checkFunc: func(t *testing.T, operator *KnowledgeBaseOperator) {
				_, err := operator.Run(ctx)
				assert.NilError(t, err)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			operator, opt := newTestKnowledgeBaseOperator(t)
			tt.mockFunc(ctx, opt)
			tt.checkFunc(t, operator)
		})
	}
}

func newTestKnowledgeBaseOperator(t *testing.T) (*KnowledgeBaseOperator, *mockOption) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	var (
		knowledgeService         = knowledgetest.NewMockKnowledgeService(ctrl)
		knowledgebaseService     = test.NewMockKnowledgebaseService(ctrl)
		knowledgeTreeNodeService = knowledgetest.NewMockKnowledgeTreeNodeService(ctrl)
	)
	operator := &KnowledgeBaseOperator{
		knowledgeService:         knowledgeService,
		knowledgebaseService:     knowledgebaseService,
		knowledgeTreeNodeService: knowledgeTreeNodeService,
		metaData: KnowledgeBaseMetaData{
			Query:       "oncall",
			TopK:        10,
			Index:       "test",
			SourceTypes: []string{"bytecloud"},
		},
	}
	opt := &mockOption{
		MockKnowledgeService:         knowledgeService,
		MockKnowledgebaseService:     knowledgebaseService,
		MockKnowledgeTreeNodeService: knowledgeTreeNodeService,
	}
	return operator, opt
}

type mockOption struct {
	MockKnowledgeService         *knowledgetest.MockKnowledgeService
	MockKnowledgebaseService     *test.MockKnowledgebaseService
	MockKnowledgeTreeNodeService *knowledgetest.MockKnowledgeTreeNodeService
	MockLarkCli                  *larkmock.MockClient
}
