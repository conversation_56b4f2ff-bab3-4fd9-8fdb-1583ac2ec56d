package recaller

import (
	"context"
	"errors"
	"regexp"
	"strings"

	"code.byted.org/gopkg/logs/v2"

	"code.byted.org/devgpt/kiwis/devai/common/constant"
	"code.byted.org/devgpt/kiwis/devai/retrieval/entity"
	"code.byted.org/devgpt/kiwis/port/lark"
)

type LarkSearchOperator struct {
	LarkClient lark.Client
	MetaData   LarkSearchMetaData
}

type LarkSearchMetaData struct {
	Query     string
	TopK      int
	UserToken string
	AppID     string
	AppSecret string
}

func NewLarkSearchMetaData(query string, topK int, userToken string, AppID string, AppSecret string) LarkSearchMetaData {
	return LarkSearchMetaData{
		Query:     query,
		TopK:      topK,
		UserToken: userToken,
		AppID:     AppID,
		AppSecret: AppSecret,
	}
}

func NewLarkSearchOperator(client lark.Client, larkSearchMetaData LarkSearchMetaData) *LarkSearchOperator {
	return &LarkSearchOperator{
		LarkClient: client,
		MetaData:   larkSearchMetaData,
	}
}

func (o *LarkSearchOperator) postprocess(content string) string {
	content = strings.ReplaceAll(content, "[title]", "")
	content = strings.ReplaceAll(content, "[content]", "\n")
	re := regexp.MustCompile(`\[heading\d*\]`)
	content = re.ReplaceAllString(content, "#")
	return content
}

func (o *LarkSearchOperator) Run(ctx context.Context) (*entity.OperatorData, error) {
	logs.CtxInfo(ctx, "[LarkSearchOperator] recall question is %v", o.MetaData.Query)
	var err error
	logs.CtxInfo(ctx, "[LarkSearchOperator] o.MetaData.UserToken is %v", o.MetaData.UserToken)

	client := o.LarkClient
	if o.MetaData.AppID != "" && o.MetaData.AppSecret != "" {
		client = lark.NewClient(o.MetaData.AppID, o.MetaData.AppSecret, "", "")
	}
	resp, err := client.SearchLarkData(ctx, o.MetaData.TopK, o.MetaData.Query, o.MetaData.UserToken, nil)
	if err != nil {
		logs.CtxError(ctx, "[LarkSearchOperator] SearchLarkData err is %v", err)
		return nil, err
	}
	if resp == nil {
		return nil, errors.New("[SearchLarkData] resp is nil")
	}
	passages := resp.Data.Passages
	segments := make([]*entity.Segment, 0, len(passages))
	for _, v := range passages {
		URL := v.URL
		content := o.postprocess(v.Content)
		segments = append(segments, &entity.Segment{
			SegmentID:  v.ID,
			Type:       constant.SegmentTypeSearch,
			Content:    content,
			SourceType: constant.SourceTypeLarkSearch,
			SearchSegment: &entity.SearchSegment{
				Title:      v.Title,
				Content:    content,
				Link:       &URL,
				SourceType: string(constant.SourceTypeLarkSearch),
			}})

	}
	logs.CtxInfo(ctx, "[LarkSearchOperator] recall result is %v", segments)
	return &entity.OperatorData{
		Type: entity.OperatorRecaller,
		RecallData: &entity.RecallData{
			Provider: entity.OperatorProviderLarkSearch,
			Segments: segments,
		},
	}, nil
}
