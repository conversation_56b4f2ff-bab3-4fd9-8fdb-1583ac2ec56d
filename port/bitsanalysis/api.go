package bitsanalysis

import (
	"context"

	"code.byted.org/devgpt/kiwis/lib/hertz"
)

type ReqOption func(option *hertz.ReqOption)

type ListIssuesRequest struct {
	RepoName       string   `json:"repo_name" mapstructure:"repo_name" description:"必填, git仓库名称" query:"repo_name" url:"repo_name"`
	Scene          string   `json:"scene" mapstructure:"scene" description:"必填, 应用场景名称, 可选枚举值: quality_analysis, merge_check, deploy_check, resolution_check." query:"scene" url:"scene"`
	RuleName       string   `json:"rule_name" mapstructure:"rule_name" description:"非必填, 问题对应的规则名称" query:"rule_name" url:"rule_name"`
	RuleNames      []string `json:"rule_names" mapstructure:"rule_names" description:"非必填, 问题对应的规则名称列表" query:"rule_names" url:"rule_names"`
	Severities     []string `json:"severities" mapstructure:"severities" description:"非必填, 问题的严重等级列表" query:"severities"`
	AssigneeName   string   `json:"assignee_name" mapstructure:"assignee_name" description:"非必填, 问题的责任人名称" query:"assignee_name" url:"assignee_name"`
	AssigneeNames  []string `json:"assignee_names" mapstructure:"assignee_names" description:"非必填, 问题的责任人名称列表" query:"assignee_names" url:"assignee_names"`
	FilePathPrefix *string  `json:"file_path_prefix" mapstructure:"file_path_prefix" description:"非必填, 问题对应的文件路径前缀" query:"file_path_prefix" url:"file_path_prefix"`
	ResolutionID   int64    `json:"resolution_id" mapstructure:"resolution_id" description:"非必填, 仅当scene为resolution_check时, 该参数必填,为治理专项的ID, 其他场景下不需要" query:"resolution_id" url:"resolution_id"`
}

type AimeIssue struct {
	ID                   int64          `json:"id"` // id
	RuleName             string         `json:"rule_name"`
	FilePath             string         `json:"file_path"`
	LineStart            int64          `json:"line_start"`
	LineEnd              int64          `json:"line_end"`
	Message              string         `json:"message"`
	Detail               string         `json:"detail"`
	CodeSnippet          string         `json:"code_snippet"`
	RelatedRuleKnowledge []string       `json:"related_rule_knowledge"` // 关联的规则知识库
	FixSuggestion        *FixSuggestion `json:"fix_suggestion"`         // 修复建议
}

type FixSuggestion struct {
	// The location of the code to be replaced
	LocationOfReplacedCode LocationOfReplacedCode `json:"location_of_replaced_code"`
	// The replaced code
	SuggestedCode      string `json:"suggested_code"`
	SuggestExplanation string `json:"suggest_explanation"`
}

type LocationOfReplacedCode struct {
	Start int64 `json:"start"`
	End   int64 `json:"end"`
}

type ListIssuesResponse struct {
	Issues []AimeIssue `json:"issues"`
	Total  int64       `json:"total"`
}

// Client defines methods for Bits Analysis API.

//go:generate go run go.uber.org/mock/mockgen -destination mock/bitsanalysis_mock_gen.go -package bitsanalysismock . Client
type Client interface {
	ListIssues(ctx context.Context, request *ListIssuesRequest, options ...ReqOption) (*ListIssuesResponse, error)
	ParseURL(ctx context.Context, rawURL string, options ...ReqOption) (map[string]string, error)
}
