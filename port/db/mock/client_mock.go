// Code generated by MockGen. DO NOT EDIT.
// Source: code.byted.org/devgpt/kiwis/port/db (interfaces: Client)
//
// Generated by this command:
//
//	mockgen -destination=./mock/client_mock.go -package=mock code.byted.org/devgpt/kiwis/port/db Client
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
	gorm "gorm.io/gorm"
)

// MockClient is a mock of Client interface.
type MockClient struct {
	ctrl     *gomock.Controller
	recorder *MockClientMockRecorder
	isgomock struct{}
}

// MockClientMockRecorder is the mock recorder for MockClient.
type MockClientMockRecorder struct {
	mock *MockClient
}

// NewMockClient creates a new mock instance.
func NewMockClient(ctrl *gomock.Controller) *MockClient {
	mock := &MockClient{ctrl: ctrl}
	mock.recorder = &MockClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockClient) EXPECT() *MockClientMockRecorder {
	return m.recorder
}

// GetDialect mocks base method.
func (m *MockClient) GetDialect() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDialect")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetDialect indicates an expected call of GetDialect.
func (mr *MockClientMockRecorder) GetDialect() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDialect", reflect.TypeOf((*MockClient)(nil).GetDialect))
}

// NewRequest mocks base method.
func (m *MockClient) NewRequest(ctx context.Context) *gorm.DB {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NewRequest", ctx)
	ret0, _ := ret[0].(*gorm.DB)
	return ret0
}

// NewRequest indicates an expected call of NewRequest.
func (mr *MockClientMockRecorder) NewRequest(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewRequest", reflect.TypeOf((*MockClient)(nil).NewRequest), ctx)
}

// WithReadDB mocks base method.
func (m *MockClient) WithReadDB(ctx context.Context) *gorm.DB {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithReadDB", ctx)
	ret0, _ := ret[0].(*gorm.DB)
	return ret0
}

// WithReadDB indicates an expected call of WithReadDB.
func (mr *MockClientMockRecorder) WithReadDB(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithReadDB", reflect.TypeOf((*MockClient)(nil).WithReadDB), ctx)
}

// WithWriteDB mocks base method.
func (m *MockClient) WithWriteDB(ctx context.Context) *gorm.DB {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithWriteDB", ctx)
	ret0, _ := ret[0].(*gorm.DB)
	return ret0
}

// WithWriteDB indicates an expected call of WithWriteDB.
func (mr *MockClientMockRecorder) WithWriteDB(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithWriteDB", reflect.TypeOf((*MockClient)(nil).WithWriteDB), ctx)
}
