package db

import (
	"bytes"
	"context"
	"strings"

	"github.com/go-sql-driver/mysql"
	"github.com/pkg/errors"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"gorm.io/plugin/dbresolver"

	"code.byted.org/gorm/bytedgorm"

	"code.byted.org/devgpt/kiwis/lib/config"
)

const (
	DialectMySQL  = "mysql"
	DialectSQLite = "sqlite"
)

// Client is the general SQL DB interface.
//
//go:generate mockgen -destination=./mock/client_mock.go -package=mock code.byted.org/devgpt/kiwis/port/db Client
type Client interface {
	// NewRequest creates a new DB request.
	NewRequest(ctx context.Context) *gorm.DB
	GetDialect() string
	// Add other requests or method if needed.

	WithWriteDB(ctx context.Context) *gorm.DB
	WithReadDB(ctx context.Context) *gorm.DB
}

var _ Client = &client{}

type client struct {
	db      *gorm.DB
	dialect string // SQL dialect, mysql/sqlite/...
}

func (c *client) NewRequest(ctx context.Context) *gorm.DB {
	return c.db.WithContext(ctx)
}

func (c *client) GetDialect() string {
	return c.dialect
}

func (c *client) WithWriteDB(ctx context.Context) *gorm.DB {
	return c.db.WithContext(ctx).Clauses(dbresolver.Write)
}

func (c *client) WithReadDB(ctx context.Context) *gorm.DB {
	return c.db.WithContext(ctx).Clauses(dbresolver.Read)
}

// NewRDSClient creates ByteDance RDS MySQL client.
func NewRDSClient(conf config.RDSConfig) (Client, error) {
	dbConfig := bytedgorm.MySQL(conf.PSM, conf.DBName)
	if conf.WithReadReplicas == nil || *conf.WithReadReplicas {
		dbConfig = dbConfig.WithReadReplicas()
	}
	if conf.ClientFoundRows != nil && *conf.ClientFoundRows {
		dbConfig = dbConfig.With(func(dbConfig *bytedgorm.DBConfig) {
			dbConfig.DSNParams.Add("clientFoundRows", "true")
		})
	}

	opts := []gorm.Option{
		bytedgorm.WithDefaults(),
		bytedgorm.Logger{
			LogLevel:                  ParseLogLevel(conf.LogLevel),
			IgnoreRecordNotFoundError: true,
		},
	}
	if conf.WithSecurityScanSupport {
		opts = append(opts, bytedgorm.WithSecurityScanSupport())
	}

	db, err := gorm.Open(
		dbConfig,
		opts...,
	)

	if err != nil {
		return nil, err
	}

	cli := &client{
		db:      db,
		dialect: DialectMySQL,
	}

	return cli, nil
}

func IsRecordNotFoundError(err error) bool {
	return errors.Is(err, gorm.ErrRecordNotFound)
}

// IsDuplicateKeyError checks if the error is a duplicate key error.
// It's MySQL specific.
func IsDuplicateKeyError(err error) bool {
	mysqlSQL := &mysql.MySQLError{}
	if !errors.As(err, &mysqlSQL) {
		return false
	}
	return mysqlSQL.Number == 1062 && bytes.Equal(mysqlSQL.SQLState[:], []byte("23000"))
}

func ParseLogLevel(level *string) logger.LogLevel {
	if level == nil {
		return logger.Warn
	}
	switch strings.ToLower(*level) {
	case "warn", "wanring":
		return logger.Warn
	case "error":
		return logger.Error
	case "info":
		return logger.Info
	}
	return logger.Warn
}
