package flowresource

import (
	"bytes"
	"context"

	"github.com/pkg/errors"

	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/flow/resource_sdk/service/resource"
	"code.byted.org/overpass/flow_alice_resource_center/kitex_gen/resource_center_model"
)

type SDKClient struct {
	client     resource.Client
	tenantID   int64
	tenantName string
	sceneID    int64
	sceneName  string
}

func NewFlowResourceSDKClient(client resource.Client, tccConfig *config.FlowResourceConfig) *SDKClient {
	if client == nil {
		client = resource.NewClient(context.Background())
	}
	return &SDKClient{
		client:     client,
		tenantID:   tccConfig.TenantID,
		tenantName: tccConfig.TenantName,
		sceneID:    tccConfig.SceneID,
		sceneName:  tccConfig.SceneName,
	}
}

func (s *SDKClient) UploadResource(ctx context.Context, fileContent []byte, userID int64) (string, error) {
	uploadResult, err := s.client.Upload(ctx, resource.UploadResourceParams{
		TenantId:     s.tenantID,
		SceneId:      s.sceneID,
		UserId:       userID,
		ResourceType: resource_center_model.ResourceType_Image,
	}, bytes.NewReader(fileContent))
	if err != nil {
		return "", err
	}
	if uploadResult.Uri == "" {
		return "", errors.New("upload succeeded but Uri is empty")
	}
	return uploadResult.Uri, nil
}

func (s *SDKClient) UploadFileResource(ctx context.Context, filename string, fileContent []byte, userID int64) (string, error) {
	var resourceMeta resource_center_model.ResourceMeta
	resourceMeta.FileMeta = &resource_center_model.FileMeta{
		FileName: filename,
	}

	uploadResult, err := s.client.Upload(ctx,
		resource.UploadResourceParams{
			TenantId:     s.tenantID,
			SceneId:      s.sceneID,
			UserId:       userID,
			ResourceType: resource_center_model.ResourceType_File,
			ResourceMeta: resourceMeta,
		},
		bytes.NewReader(fileContent),
		resource.UploadWithContentType("application/octet-stream"), // 默认走下载流程
	)
	if err != nil {
		return "", err
	}
	if uploadResult.Uri == "" {
		return "", errors.New("upload succeeded but Uri is empty")
	}
	return uploadResult.Uri, nil
}

func (s *SDKClient) SignResourceURL(ctx context.Context, uri string, userID int64) (string, string, error) {
	signResults, err := s.client.SignResourceURL(ctx, &resource.SignResourceURLParams{
		UserID: userID,
		Resources: []*resource.ResourceMeta{
			{
				Uri: uri,
			},
		},
	}, resource.UseSignMode(resource.SignMode_ResourceCenterRPC))
	if err != nil {
		return "", "", err
	}
	if len(signResults) == 0 {
		return "", "", errors.New("file url is empty")
	}
	return signResults[0].URL.MainURL, signResults[0].URL.BackupURL, nil
}
