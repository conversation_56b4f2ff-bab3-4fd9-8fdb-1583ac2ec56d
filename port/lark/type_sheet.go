package lark

type SheetCell struct {
	Type            string           `json:"type"`
	Text            *Text            `json:"text,omitempty"`
	Value           *Value           `json:"value,omitempty"`
	Image           *Image           `json:"image,omitempty"`
	File            *File            `json:"file,omitempty"`
	Reminder        *Reminder        `json:"reminder,omitempty"`
	Formula         *Formula         `json:"formula,omitempty"`
	MentionUser     *MentionUser     `json:"mention_user,omitempty"`
	MentionDocument *MentionDocument `json:"mention_document,omitempty"`
	Link            *Link            `json:"link,omitempty"`
	DateTime        *DateTime        `json:"date_time,omitempty"`
}

const (
	SheetCellTypeText            = "text"
	SheetCellTypeValue           = "value"
	SheetCellTypeImage           = "image"
	SheetCellTypeFile            = "file"
	SheetCellTypeReminder        = "reminder"
	SheetCellTypeFormula         = "formula"
	SheetCellTypeMentionUser     = "mention_user"
	SheetCellTypeMentionDocument = "mention_document"
	SheetCellTypeLink            = "link"
	SheetCellTypeDateTime        = "date_time"
)

type Text struct {
	Text string `json:"text"`
}

type Value struct {
	Value string `json:"value"`
}

type DateTime struct {
	DateTime string `json:"date_time"`
}

type Image struct {
	ImageToken string `json:"image_token"`
}

type File struct { //附件
	FileToken string `json:"file_token"`
	Name      string `json:"name"`
}

type Reminder struct { //提醒
	NotifyDateTime string   `json:"notify_date_time"`
	NotifyUserID   []string `json:"notify_user_id"`
	NotifyText     string   `json:"notify_text"`
	NotifyStrategy int      `json:"notify_strategy"`
}

type Formula struct {
	Formula       string `json:"formula"`
	FormulaValue  string `json:"formula_value"`
	AffectedRange string `json:"affected_range"`
}

type MentionUser struct {
	Name   string `json:"name"`
	UserID string `json:"user_id"`
	Notify bool   `json:"notify"`
}

type MentionDocument struct {
	Title      string `json:"title"`
	ObjectType string `json:"object_type"`
	Token      string `json:"token"`
	Link       string `json:"link"`
}

type Link struct {
	Text string `json:"text"`
	Link string `json:"link"`
}
