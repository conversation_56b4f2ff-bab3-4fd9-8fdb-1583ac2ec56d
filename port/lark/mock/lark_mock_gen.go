// Code generated by MockGen. DO NOT EDIT.
// Source: code.byted.org/devgpt/kiwis/port/lark (interfaces: Client)
//
// Generated by this command:
//
//	mockgen -destination mock/lark_mock_gen.go -typed -package lark . Client
//

// Package lark is a generated GoMock package.
package lark

import (
	context "context"
	io "io"
	reflect "reflect"

	lark "code.byted.org/devgpt/kiwis/port/lark"
	larkbaike "github.com/larksuite/oapi-sdk-go/v3/service/baike/v1"
	larkbitable "github.com/larksuite/oapi-sdk-go/v3/service/bitable/v1"
	larkboard "github.com/larksuite/oapi-sdk-go/v3/service/board/v1"
	larkcontact "github.com/larksuite/oapi-sdk-go/v3/service/contact/v3"
	larkdocx "github.com/larksuite/oapi-sdk-go/v3/service/docx/v1"
	larkdrive "github.com/larksuite/oapi-sdk-go/v3/service/drive/v1"
	larkim "github.com/larksuite/oapi-sdk-go/v3/service/im/v1"
	larklingo "github.com/larksuite/oapi-sdk-go/v3/service/lingo/v1"
	larksheets "github.com/larksuite/oapi-sdk-go/v3/service/sheets/v3"
	larkwiki "github.com/larksuite/oapi-sdk-go/v3/service/wiki/v2"
	gomock "go.uber.org/mock/gomock"
)

// MockClient is a mock of Client interface.
type MockClient struct {
	ctrl     *gomock.Controller
	recorder *MockClientMockRecorder
}

// MockClientMockRecorder is the mock recorder for MockClient.
type MockClientMockRecorder struct {
	mock *MockClient
}

// NewMockClient creates a new mock instance.
func NewMockClient(ctrl *gomock.Controller) *MockClient {
	mock := &MockClient{ctrl: ctrl}
	mock.recorder = &MockClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockClient) EXPECT() *MockClientMockRecorder {
	return m.recorder
}

// AddLarkFilePermission mocks base method.
func (m *MockClient) AddLarkFilePermission(arg0 context.Context, arg1, arg2, arg3, arg4 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddLarkFilePermission", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddLarkFilePermission indicates an expected call of AddLarkFilePermission.
func (mr *MockClientMockRecorder) AddLarkFilePermission(arg0, arg1, arg2, arg3, arg4 any) *MockClientAddLarkFilePermissionCall {
	mr.mock.ctrl.T.Helper()
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddLarkFilePermission", reflect.TypeOf((*MockClient)(nil).AddLarkFilePermission), arg0, arg1, arg2, arg3, arg4)
	return &MockClientAddLarkFilePermissionCall{Call: call}
}

// MockClientAddLarkFilePermissionCall wrap *gomock.Call
type MockClientAddLarkFilePermissionCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockClientAddLarkFilePermissionCall) Return(arg0 error) *MockClientAddLarkFilePermissionCall {
	c.Call = c.Call.Return(arg0)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockClientAddLarkFilePermissionCall) Do(f func(context.Context, string, string, string, string) error) *MockClientAddLarkFilePermissionCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockClientAddLarkFilePermissionCall) DoAndReturn(f func(context.Context, string, string, string, string) error) *MockClientAddLarkFilePermissionCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// AddRowForLarkSheet mocks base method.
func (m *MockClient) AddRowForLarkSheet(arg0 context.Context, arg1, arg2 string, arg3 [][]any) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddRowForLarkSheet", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddRowForLarkSheet indicates an expected call of AddRowForLarkSheet.
func (mr *MockClientMockRecorder) AddRowForLarkSheet(arg0, arg1, arg2, arg3 any) *MockClientAddRowForLarkSheetCall {
	mr.mock.ctrl.T.Helper()
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddRowForLarkSheet", reflect.TypeOf((*MockClient)(nil).AddRowForLarkSheet), arg0, arg1, arg2, arg3)
	return &MockClientAddRowForLarkSheetCall{Call: call}
}

// MockClientAddRowForLarkSheetCall wrap *gomock.Call
type MockClientAddRowForLarkSheetCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockClientAddRowForLarkSheetCall) Return(arg0 error) *MockClientAddRowForLarkSheetCall {
	c.Call = c.Call.Return(arg0)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockClientAddRowForLarkSheetCall) Do(f func(context.Context, string, string, [][]any) error) *MockClientAddRowForLarkSheetCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockClientAddRowForLarkSheetCall) DoAndReturn(f func(context.Context, string, string, [][]any) error) *MockClientAddRowForLarkSheetCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// BatchDeleteDocxBlockChildren mocks base method.
func (m *MockClient) BatchDeleteDocxBlockChildren(arg0 context.Context, arg1, arg2 string, arg3, arg4 int, arg5 string) (*larkdocx.BatchDeleteDocumentBlockChildrenRespData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchDeleteDocxBlockChildren", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(*larkdocx.BatchDeleteDocumentBlockChildrenRespData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchDeleteDocxBlockChildren indicates an expected call of BatchDeleteDocxBlockChildren.
func (mr *MockClientMockRecorder) BatchDeleteDocxBlockChildren(arg0, arg1, arg2, arg3, arg4, arg5 any) *MockClientBatchDeleteDocxBlockChildrenCall {
	mr.mock.ctrl.T.Helper()
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchDeleteDocxBlockChildren", reflect.TypeOf((*MockClient)(nil).BatchDeleteDocxBlockChildren), arg0, arg1, arg2, arg3, arg4, arg5)
	return &MockClientBatchDeleteDocxBlockChildrenCall{Call: call}
}

// MockClientBatchDeleteDocxBlockChildrenCall wrap *gomock.Call
type MockClientBatchDeleteDocxBlockChildrenCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockClientBatchDeleteDocxBlockChildrenCall) Return(arg0 *larkdocx.BatchDeleteDocumentBlockChildrenRespData, arg1 error) *MockClientBatchDeleteDocxBlockChildrenCall {
	c.Call = c.Call.Return(arg0, arg1)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockClientBatchDeleteDocxBlockChildrenCall) Do(f func(context.Context, string, string, int, int, string) (*larkdocx.BatchDeleteDocumentBlockChildrenRespData, error)) *MockClientBatchDeleteDocxBlockChildrenCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockClientBatchDeleteDocxBlockChildrenCall) DoAndReturn(f func(context.Context, string, string, int, int, string) (*larkdocx.BatchDeleteDocumentBlockChildrenRespData, error)) *MockClientBatchDeleteDocxBlockChildrenCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// BatchUpdateDocxBlocks mocks base method.
func (m *MockClient) BatchUpdateDocxBlocks(arg0 context.Context, arg1 string, arg2 []*larkdocx.UpdateBlockRequest, arg3 string) (*larkdocx.BatchUpdateDocumentBlockRespData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchUpdateDocxBlocks", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*larkdocx.BatchUpdateDocumentBlockRespData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchUpdateDocxBlocks indicates an expected call of BatchUpdateDocxBlocks.
func (mr *MockClientMockRecorder) BatchUpdateDocxBlocks(arg0, arg1, arg2, arg3 any) *MockClientBatchUpdateDocxBlocksCall {
	mr.mock.ctrl.T.Helper()
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchUpdateDocxBlocks", reflect.TypeOf((*MockClient)(nil).BatchUpdateDocxBlocks), arg0, arg1, arg2, arg3)
	return &MockClientBatchUpdateDocxBlocksCall{Call: call}
}

// MockClientBatchUpdateDocxBlocksCall wrap *gomock.Call
type MockClientBatchUpdateDocxBlocksCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockClientBatchUpdateDocxBlocksCall) Return(arg0 *larkdocx.BatchUpdateDocumentBlockRespData, arg1 error) *MockClientBatchUpdateDocxBlocksCall {
	c.Call = c.Call.Return(arg0, arg1)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockClientBatchUpdateDocxBlocksCall) Do(f func(context.Context, string, []*larkdocx.UpdateBlockRequest, string) (*larkdocx.BatchUpdateDocumentBlockRespData, error)) *MockClientBatchUpdateDocxBlocksCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockClientBatchUpdateDocxBlocksCall) DoAndReturn(f func(context.Context, string, []*larkdocx.UpdateBlockRequest, string) (*larkdocx.BatchUpdateDocumentBlockRespData, error)) *MockClientBatchUpdateDocxBlocksCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// CreateExportTasks mocks base method.
func (m *MockClient) CreateExportTasks(arg0 context.Context, arg1, arg2, arg3, arg4, arg5 string) (*string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateExportTasks", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(*string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateExportTasks indicates an expected call of CreateExportTasks.
func (mr *MockClientMockRecorder) CreateExportTasks(arg0, arg1, arg2, arg3, arg4, arg5 any) *MockClientCreateExportTasksCall {
	mr.mock.ctrl.T.Helper()
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateExportTasks", reflect.TypeOf((*MockClient)(nil).CreateExportTasks), arg0, arg1, arg2, arg3, arg4, arg5)
	return &MockClientCreateExportTasksCall{Call: call}
}

// MockClientCreateExportTasksCall wrap *gomock.Call
type MockClientCreateExportTasksCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockClientCreateExportTasksCall) Return(arg0 *string, arg1 error) *MockClientCreateExportTasksCall {
	c.Call = c.Call.Return(arg0, arg1)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockClientCreateExportTasksCall) Do(f func(context.Context, string, string, string, string, string) (*string, error)) *MockClientCreateExportTasksCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockClientCreateExportTasksCall) DoAndReturn(f func(context.Context, string, string, string, string, string) (*string, error)) *MockClientCreateExportTasksCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// CreateFolder mocks base method.
func (m *MockClient) CreateFolder(arg0 context.Context, arg1, arg2 string, arg3 ...lark.Option) (*larkdrive.CreateFolderFileRespData, error) {
	m.ctrl.T.Helper()
	varargs := []any{arg0, arg1, arg2}
	for _, a := range arg3 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateFolder", varargs...)
	ret0, _ := ret[0].(*larkdrive.CreateFolderFileRespData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateFolder indicates an expected call of CreateFolder.
func (mr *MockClientMockRecorder) CreateFolder(arg0, arg1, arg2 any, arg3 ...any) *MockClientCreateFolderCall {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{arg0, arg1, arg2}, arg3...)
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateFolder", reflect.TypeOf((*MockClient)(nil).CreateFolder), varargs...)
	return &MockClientCreateFolderCall{Call: call}
}

// MockClientCreateFolderCall wrap *gomock.Call
type MockClientCreateFolderCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockClientCreateFolderCall) Return(arg0 *larkdrive.CreateFolderFileRespData, arg1 error) *MockClientCreateFolderCall {
	c.Call = c.Call.Return(arg0, arg1)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockClientCreateFolderCall) Do(f func(context.Context, string, string, ...lark.Option) (*larkdrive.CreateFolderFileRespData, error)) *MockClientCreateFolderCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockClientCreateFolderCall) DoAndReturn(f func(context.Context, string, string, ...lark.Option) (*larkdrive.CreateFolderFileRespData, error)) *MockClientCreateFolderCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// CreateImportTask mocks base method.
func (m *MockClient) CreateImportTask(arg0 context.Context, arg1 *larkdrive.CreateImportTaskReq, arg2 string) (*string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateImportTask", arg0, arg1, arg2)
	ret0, _ := ret[0].(*string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateImportTask indicates an expected call of CreateImportTask.
func (mr *MockClientMockRecorder) CreateImportTask(arg0, arg1, arg2 any) *MockClientCreateImportTaskCall {
	mr.mock.ctrl.T.Helper()
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateImportTask", reflect.TypeOf((*MockClient)(nil).CreateImportTask), arg0, arg1, arg2)
	return &MockClientCreateImportTaskCall{Call: call}
}

// MockClientCreateImportTaskCall wrap *gomock.Call
type MockClientCreateImportTaskCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockClientCreateImportTaskCall) Return(arg0 *string, arg1 error) *MockClientCreateImportTaskCall {
	c.Call = c.Call.Return(arg0, arg1)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockClientCreateImportTaskCall) Do(f func(context.Context, *larkdrive.CreateImportTaskReq, string) (*string, error)) *MockClientCreateImportTaskCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockClientCreateImportTaskCall) DoAndReturn(f func(context.Context, *larkdrive.CreateImportTaskReq, string) (*string, error)) *MockClientCreateImportTaskCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// CreateLarkDocx mocks base method.
func (m *MockClient) CreateLarkDocx(arg0 context.Context, arg1, arg2, arg3 string) (*larkdocx.CreateDocumentRespData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateLarkDocx", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*larkdocx.CreateDocumentRespData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateLarkDocx indicates an expected call of CreateLarkDocx.
func (mr *MockClientMockRecorder) CreateLarkDocx(arg0, arg1, arg2, arg3 any) *MockClientCreateLarkDocxCall {
	mr.mock.ctrl.T.Helper()
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateLarkDocx", reflect.TypeOf((*MockClient)(nil).CreateLarkDocx), arg0, arg1, arg2, arg3)
	return &MockClientCreateLarkDocxCall{Call: call}
}

// MockClientCreateLarkDocxCall wrap *gomock.Call
type MockClientCreateLarkDocxCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockClientCreateLarkDocxCall) Return(arg0 *larkdocx.CreateDocumentRespData, arg1 error) *MockClientCreateLarkDocxCall {
	c.Call = c.Call.Return(arg0, arg1)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockClientCreateLarkDocxCall) Do(f func(context.Context, string, string, string) (*larkdocx.CreateDocumentRespData, error)) *MockClientCreateLarkDocxCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockClientCreateLarkDocxCall) DoAndReturn(f func(context.Context, string, string, string) (*larkdocx.CreateDocumentRespData, error)) *MockClientCreateLarkDocxCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// CreateSheet mocks base method.
func (m *MockClient) CreateSheet(arg0 context.Context, arg1 string) (*larksheets.CreateSpreadsheetRespData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateSheet", arg0, arg1)
	ret0, _ := ret[0].(*larksheets.CreateSpreadsheetRespData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateSheet indicates an expected call of CreateSheet.
func (mr *MockClientMockRecorder) CreateSheet(arg0, arg1 any) *MockClientCreateSheetCall {
	mr.mock.ctrl.T.Helper()
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateSheet", reflect.TypeOf((*MockClient)(nil).CreateSheet), arg0, arg1)
	return &MockClientCreateSheetCall{Call: call}
}

// MockClientCreateSheetCall wrap *gomock.Call
type MockClientCreateSheetCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockClientCreateSheetCall) Return(arg0 *larksheets.CreateSpreadsheetRespData, arg1 error) *MockClientCreateSheetCall {
	c.Call = c.Call.Return(arg0, arg1)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockClientCreateSheetCall) Do(f func(context.Context, string) (*larksheets.CreateSpreadsheetRespData, error)) *MockClientCreateSheetCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockClientCreateSheetCall) DoAndReturn(f func(context.Context, string) (*larksheets.CreateSpreadsheetRespData, error)) *MockClientCreateSheetCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// DownLoadExportFile mocks base method.
func (m *MockClient) DownLoadExportFile(arg0 context.Context, arg1, arg2 string) (*larkdrive.DownloadExportTaskResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DownLoadExportFile", arg0, arg1, arg2)
	ret0, _ := ret[0].(*larkdrive.DownloadExportTaskResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DownLoadExportFile indicates an expected call of DownLoadExportFile.
func (mr *MockClientMockRecorder) DownLoadExportFile(arg0, arg1, arg2 any) *MockClientDownLoadExportFileCall {
	mr.mock.ctrl.T.Helper()
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DownLoadExportFile", reflect.TypeOf((*MockClient)(nil).DownLoadExportFile), arg0, arg1, arg2)
	return &MockClientDownLoadExportFileCall{Call: call}
}

// MockClientDownLoadExportFileCall wrap *gomock.Call
type MockClientDownLoadExportFileCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockClientDownLoadExportFileCall) Return(arg0 *larkdrive.DownloadExportTaskResp, arg1 error) *MockClientDownLoadExportFileCall {
	c.Call = c.Call.Return(arg0, arg1)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockClientDownLoadExportFileCall) Do(f func(context.Context, string, string) (*larkdrive.DownloadExportTaskResp, error)) *MockClientDownLoadExportFileCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockClientDownLoadExportFileCall) DoAndReturn(f func(context.Context, string, string) (*larkdrive.DownloadExportTaskResp, error)) *MockClientDownLoadExportFileCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// DownloadLarkMedia mocks base method.
func (m *MockClient) DownloadLarkMedia(arg0 context.Context, arg1, arg2 string) (*larkdrive.DownloadMediaResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DownloadLarkMedia", arg0, arg1, arg2)
	ret0, _ := ret[0].(*larkdrive.DownloadMediaResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DownloadLarkMedia indicates an expected call of DownloadLarkMedia.
func (mr *MockClientMockRecorder) DownloadLarkMedia(arg0, arg1, arg2 any) *MockClientDownloadLarkMediaCall {
	mr.mock.ctrl.T.Helper()
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DownloadLarkMedia", reflect.TypeOf((*MockClient)(nil).DownloadLarkMedia), arg0, arg1, arg2)
	return &MockClientDownloadLarkMediaCall{Call: call}
}

// MockClientDownloadLarkMediaCall wrap *gomock.Call
type MockClientDownloadLarkMediaCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockClientDownloadLarkMediaCall) Return(arg0 *larkdrive.DownloadMediaResp, arg1 error) *MockClientDownloadLarkMediaCall {
	c.Call = c.Call.Return(arg0, arg1)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockClientDownloadLarkMediaCall) Do(f func(context.Context, string, string) (*larkdrive.DownloadMediaResp, error)) *MockClientDownloadLarkMediaCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockClientDownloadLarkMediaCall) DoAndReturn(f func(context.Context, string, string) (*larkdrive.DownloadMediaResp, error)) *MockClientDownloadLarkMediaCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// DownloadWhiteboardAsImage mocks base method.
func (m *MockClient) DownloadWhiteboardAsImage(arg0 context.Context, arg1, arg2 string) (*larkboard.DownloadAsImageWhiteboardResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DownloadWhiteboardAsImage", arg0, arg1, arg2)
	ret0, _ := ret[0].(*larkboard.DownloadAsImageWhiteboardResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DownloadWhiteboardAsImage indicates an expected call of DownloadWhiteboardAsImage.
func (mr *MockClientMockRecorder) DownloadWhiteboardAsImage(arg0, arg1, arg2 any) *MockClientDownloadWhiteboardAsImageCall {
	mr.mock.ctrl.T.Helper()
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DownloadWhiteboardAsImage", reflect.TypeOf((*MockClient)(nil).DownloadWhiteboardAsImage), arg0, arg1, arg2)
	return &MockClientDownloadWhiteboardAsImageCall{Call: call}
}

// MockClientDownloadWhiteboardAsImageCall wrap *gomock.Call
type MockClientDownloadWhiteboardAsImageCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockClientDownloadWhiteboardAsImageCall) Return(arg0 *larkboard.DownloadAsImageWhiteboardResp, arg1 error) *MockClientDownloadWhiteboardAsImageCall {
	c.Call = c.Call.Return(arg0, arg1)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockClientDownloadWhiteboardAsImageCall) Do(f func(context.Context, string, string) (*larkboard.DownloadAsImageWhiteboardResp, error)) *MockClientDownloadWhiteboardAsImageCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockClientDownloadWhiteboardAsImageCall) DoAndReturn(f func(context.Context, string, string) (*larkboard.DownloadAsImageWhiteboardResp, error)) *MockClientDownloadWhiteboardAsImageCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// GetDocMeta mocks base method.
func (m *MockClient) GetDocMeta(arg0 context.Context, arg1 []*lark.RequestDoc, arg2 string, arg3 ...lark.Option) (*larkdrive.BatchQueryMetaRespData, error) {
	m.ctrl.T.Helper()
	varargs := []any{arg0, arg1, arg2}
	for _, a := range arg3 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetDocMeta", varargs...)
	ret0, _ := ret[0].(*larkdrive.BatchQueryMetaRespData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDocMeta indicates an expected call of GetDocMeta.
func (mr *MockClientMockRecorder) GetDocMeta(arg0, arg1, arg2 any, arg3 ...any) *MockClientGetDocMetaCall {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{arg0, arg1, arg2}, arg3...)
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDocMeta", reflect.TypeOf((*MockClient)(nil).GetDocMeta), varargs...)
	return &MockClientGetDocMetaCall{Call: call}
}

// MockClientGetDocMetaCall wrap *gomock.Call
type MockClientGetDocMetaCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockClientGetDocMetaCall) Return(arg0 *larkdrive.BatchQueryMetaRespData, arg1 error) *MockClientGetDocMetaCall {
	c.Call = c.Call.Return(arg0, arg1)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockClientGetDocMetaCall) Do(f func(context.Context, []*lark.RequestDoc, string, ...lark.Option) (*larkdrive.BatchQueryMetaRespData, error)) *MockClientGetDocMetaCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockClientGetDocMetaCall) DoAndReturn(f func(context.Context, []*lark.RequestDoc, string, ...lark.Option) (*larkdrive.BatchQueryMetaRespData, error)) *MockClientGetDocMetaCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// GetExportTask mocks base method.
func (m *MockClient) GetExportTask(arg0 context.Context, arg1, arg2, arg3 string) (bool, string, string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetExportTask", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(string)
	ret2, _ := ret[2].(string)
	ret3, _ := ret[3].(error)
	return ret0, ret1, ret2, ret3
}

// GetExportTask indicates an expected call of GetExportTask.
func (mr *MockClientMockRecorder) GetExportTask(arg0, arg1, arg2, arg3 any) *MockClientGetExportTaskCall {
	mr.mock.ctrl.T.Helper()
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExportTask", reflect.TypeOf((*MockClient)(nil).GetExportTask), arg0, arg1, arg2, arg3)
	return &MockClientGetExportTaskCall{Call: call}
}

// MockClientGetExportTaskCall wrap *gomock.Call
type MockClientGetExportTaskCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockClientGetExportTaskCall) Return(arg0 bool, arg1, arg2 string, arg3 error) *MockClientGetExportTaskCall {
	c.Call = c.Call.Return(arg0, arg1, arg2, arg3)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockClientGetExportTaskCall) Do(f func(context.Context, string, string, string) (bool, string, string, error)) *MockClientGetExportTaskCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockClientGetExportTaskCall) DoAndReturn(f func(context.Context, string, string, string) (bool, string, string, error)) *MockClientGetExportTaskCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// GetFilesMeta mocks base method.
func (m *MockClient) GetFilesMeta(arg0 context.Context, arg1 []*lark.RequestDoc, arg2 string, arg3 ...lark.Option) (*larkdrive.BatchQueryMetaRespData, error) {
	m.ctrl.T.Helper()
	varargs := []any{arg0, arg1, arg2}
	for _, a := range arg3 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetFilesMeta", varargs...)
	ret0, _ := ret[0].(*larkdrive.BatchQueryMetaRespData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFilesMeta indicates an expected call of GetFilesMeta.
func (mr *MockClientMockRecorder) GetFilesMeta(arg0, arg1, arg2 any, arg3 ...any) *MockClientGetFilesMetaCall {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{arg0, arg1, arg2}, arg3...)
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFilesMeta", reflect.TypeOf((*MockClient)(nil).GetFilesMeta), varargs...)
	return &MockClientGetFilesMetaCall{Call: call}
}

// MockClientGetFilesMetaCall wrap *gomock.Call
type MockClientGetFilesMetaCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockClientGetFilesMetaCall) Return(arg0 *larkdrive.BatchQueryMetaRespData, arg1 error) *MockClientGetFilesMetaCall {
	c.Call = c.Call.Return(arg0, arg1)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockClientGetFilesMetaCall) Do(f func(context.Context, []*lark.RequestDoc, string, ...lark.Option) (*larkdrive.BatchQueryMetaRespData, error)) *MockClientGetFilesMetaCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockClientGetFilesMetaCall) DoAndReturn(f func(context.Context, []*lark.RequestDoc, string, ...lark.Option) (*larkdrive.BatchQueryMetaRespData, error)) *MockClientGetFilesMetaCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// GetImportTask mocks base method.
func (m *MockClient) GetImportTask(arg0 context.Context, arg1, arg2 string) (*larkdrive.GetImportTaskRespData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetImportTask", arg0, arg1, arg2)
	ret0, _ := ret[0].(*larkdrive.GetImportTaskRespData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetImportTask indicates an expected call of GetImportTask.
func (mr *MockClientMockRecorder) GetImportTask(arg0, arg1, arg2 any) *MockClientGetImportTaskCall {
	mr.mock.ctrl.T.Helper()
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetImportTask", reflect.TypeOf((*MockClient)(nil).GetImportTask), arg0, arg1, arg2)
	return &MockClientGetImportTaskCall{Call: call}
}

// MockClientGetImportTaskCall wrap *gomock.Call
type MockClientGetImportTaskCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockClientGetImportTaskCall) Return(arg0 *larkdrive.GetImportTaskRespData, arg1 error) *MockClientGetImportTaskCall {
	c.Call = c.Call.Return(arg0, arg1)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockClientGetImportTaskCall) Do(f func(context.Context, string, string) (*larkdrive.GetImportTaskRespData, error)) *MockClientGetImportTaskCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockClientGetImportTaskCall) DoAndReturn(f func(context.Context, string, string) (*larkdrive.GetImportTaskRespData, error)) *MockClientGetImportTaskCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// GetJSApiTicket mocks base method.
func (m *MockClient) GetJSApiTicket(arg0 context.Context, arg1 string) (lark.JSApiTicket, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetJSApiTicket", arg0, arg1)
	ret0, _ := ret[0].(lark.JSApiTicket)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetJSApiTicket indicates an expected call of GetJSApiTicket.
func (mr *MockClientMockRecorder) GetJSApiTicket(arg0, arg1 any) *MockClientGetJSApiTicketCall {
	mr.mock.ctrl.T.Helper()
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetJSApiTicket", reflect.TypeOf((*MockClient)(nil).GetJSApiTicket), arg0, arg1)
	return &MockClientGetJSApiTicketCall{Call: call}
}

// MockClientGetJSApiTicketCall wrap *gomock.Call
type MockClientGetJSApiTicketCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockClientGetJSApiTicketCall) Return(arg0 lark.JSApiTicket, arg1 error) *MockClientGetJSApiTicketCall {
	c.Call = c.Call.Return(arg0, arg1)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockClientGetJSApiTicketCall) Do(f func(context.Context, string) (lark.JSApiTicket, error)) *MockClientGetJSApiTicketCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockClientGetJSApiTicketCall) DoAndReturn(f func(context.Context, string) (lark.JSApiTicket, error)) *MockClientGetJSApiTicketCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// GetLarkBaikePhrases mocks base method.
func (m *MockClient) GetLarkBaikePhrases(arg0 context.Context, arg1 string) ([]*larkbaike.Phrase, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLarkBaikePhrases", arg0, arg1)
	ret0, _ := ret[0].([]*larkbaike.Phrase)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLarkBaikePhrases indicates an expected call of GetLarkBaikePhrases.
func (mr *MockClientMockRecorder) GetLarkBaikePhrases(arg0, arg1 any) *MockClientGetLarkBaikePhrasesCall {
	mr.mock.ctrl.T.Helper()
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLarkBaikePhrases", reflect.TypeOf((*MockClient)(nil).GetLarkBaikePhrases), arg0, arg1)
	return &MockClientGetLarkBaikePhrasesCall{Call: call}
}

// MockClientGetLarkBaikePhrasesCall wrap *gomock.Call
type MockClientGetLarkBaikePhrasesCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockClientGetLarkBaikePhrasesCall) Return(arg0 []*larkbaike.Phrase, arg1 error) *MockClientGetLarkBaikePhrasesCall {
	c.Call = c.Call.Return(arg0, arg1)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockClientGetLarkBaikePhrasesCall) Do(f func(context.Context, string) ([]*larkbaike.Phrase, error)) *MockClientGetLarkBaikePhrasesCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockClientGetLarkBaikePhrasesCall) DoAndReturn(f func(context.Context, string) ([]*larkbaike.Phrase, error)) *MockClientGetLarkBaikePhrasesCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// GetLarkDoc mocks base method.
func (m *MockClient) GetLarkDoc(arg0 context.Context, arg1, arg2 string, arg3 ...lark.Option) (*lark.DocMeta, error) {
	m.ctrl.T.Helper()
	varargs := []any{arg0, arg1, arg2}
	for _, a := range arg3 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLarkDoc", varargs...)
	ret0, _ := ret[0].(*lark.DocMeta)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLarkDoc indicates an expected call of GetLarkDoc.
func (mr *MockClientMockRecorder) GetLarkDoc(arg0, arg1, arg2 any, arg3 ...any) *MockClientGetLarkDocCall {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{arg0, arg1, arg2}, arg3...)
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLarkDoc", reflect.TypeOf((*MockClient)(nil).GetLarkDoc), varargs...)
	return &MockClientGetLarkDocCall{Call: call}
}

// MockClientGetLarkDocCall wrap *gomock.Call
type MockClientGetLarkDocCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockClientGetLarkDocCall) Return(arg0 *lark.DocMeta, arg1 error) *MockClientGetLarkDocCall {
	c.Call = c.Call.Return(arg0, arg1)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockClientGetLarkDocCall) Do(f func(context.Context, string, string, ...lark.Option) (*lark.DocMeta, error)) *MockClientGetLarkDocCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockClientGetLarkDocCall) DoAndReturn(f func(context.Context, string, string, ...lark.Option) (*lark.DocMeta, error)) *MockClientGetLarkDocCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// GetLarkDocBlock mocks base method.
func (m *MockClient) GetLarkDocBlock(arg0 context.Context, arg1, arg2 string, arg3 ...lark.Option) (*lark.DocContent, error) {
	m.ctrl.T.Helper()
	varargs := []any{arg0, arg1, arg2}
	for _, a := range arg3 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLarkDocBlock", varargs...)
	ret0, _ := ret[0].(*lark.DocContent)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLarkDocBlock indicates an expected call of GetLarkDocBlock.
func (mr *MockClientMockRecorder) GetLarkDocBlock(arg0, arg1, arg2 any, arg3 ...any) *MockClientGetLarkDocBlockCall {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{arg0, arg1, arg2}, arg3...)
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLarkDocBlock", reflect.TypeOf((*MockClient)(nil).GetLarkDocBlock), varargs...)
	return &MockClientGetLarkDocBlockCall{Call: call}
}

// MockClientGetLarkDocBlockCall wrap *gomock.Call
type MockClientGetLarkDocBlockCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockClientGetLarkDocBlockCall) Return(arg0 *lark.DocContent, arg1 error) *MockClientGetLarkDocBlockCall {
	c.Call = c.Call.Return(arg0, arg1)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockClientGetLarkDocBlockCall) Do(f func(context.Context, string, string, ...lark.Option) (*lark.DocContent, error)) *MockClientGetLarkDocBlockCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockClientGetLarkDocBlockCall) DoAndReturn(f func(context.Context, string, string, ...lark.Option) (*lark.DocContent, error)) *MockClientGetLarkDocBlockCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// GetLarkDocComments mocks base method.
func (m *MockClient) GetLarkDocComments(arg0 context.Context, arg1 bool, arg2, arg3, arg4 string) ([]*larkdrive.FileComment, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLarkDocComments", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].([]*larkdrive.FileComment)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLarkDocComments indicates an expected call of GetLarkDocComments.
func (mr *MockClientMockRecorder) GetLarkDocComments(arg0, arg1, arg2, arg3, arg4 any) *MockClientGetLarkDocCommentsCall {
	mr.mock.ctrl.T.Helper()
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLarkDocComments", reflect.TypeOf((*MockClient)(nil).GetLarkDocComments), arg0, arg1, arg2, arg3, arg4)
	return &MockClientGetLarkDocCommentsCall{Call: call}
}

// MockClientGetLarkDocCommentsCall wrap *gomock.Call
type MockClientGetLarkDocCommentsCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockClientGetLarkDocCommentsCall) Return(arg0 []*larkdrive.FileComment, arg1 error) *MockClientGetLarkDocCommentsCall {
	c.Call = c.Call.Return(arg0, arg1)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockClientGetLarkDocCommentsCall) Do(f func(context.Context, bool, string, string, string) ([]*larkdrive.FileComment, error)) *MockClientGetLarkDocCommentsCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockClientGetLarkDocCommentsCall) DoAndReturn(f func(context.Context, bool, string, string, string) ([]*larkdrive.FileComment, error)) *MockClientGetLarkDocCommentsCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// GetLarkDocx mocks base method.
func (m *MockClient) GetLarkDocx(arg0 context.Context, arg1, arg2 string, arg3 ...lark.Option) (*larkdocx.GetDocumentRespData, error) {
	m.ctrl.T.Helper()
	varargs := []any{arg0, arg1, arg2}
	for _, a := range arg3 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLarkDocx", varargs...)
	ret0, _ := ret[0].(*larkdocx.GetDocumentRespData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLarkDocx indicates an expected call of GetLarkDocx.
func (mr *MockClientMockRecorder) GetLarkDocx(arg0, arg1, arg2 any, arg3 ...any) *MockClientGetLarkDocxCall {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{arg0, arg1, arg2}, arg3...)
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLarkDocx", reflect.TypeOf((*MockClient)(nil).GetLarkDocx), varargs...)
	return &MockClientGetLarkDocxCall{Call: call}
}

// MockClientGetLarkDocxCall wrap *gomock.Call
type MockClientGetLarkDocxCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockClientGetLarkDocxCall) Return(arg0 *larkdocx.GetDocumentRespData, arg1 error) *MockClientGetLarkDocxCall {
	c.Call = c.Call.Return(arg0, arg1)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockClientGetLarkDocxCall) Do(f func(context.Context, string, string, ...lark.Option) (*larkdocx.GetDocumentRespData, error)) *MockClientGetLarkDocxCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockClientGetLarkDocxCall) DoAndReturn(f func(context.Context, string, string, ...lark.Option) (*larkdocx.GetDocumentRespData, error)) *MockClientGetLarkDocxCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// GetLarkDocxBlock mocks base method.
func (m *MockClient) GetLarkDocxBlock(arg0 context.Context, arg1, arg2 string, arg3 ...lark.Option) ([]*larkdocx.Block, error) {
	m.ctrl.T.Helper()
	varargs := []any{arg0, arg1, arg2}
	for _, a := range arg3 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLarkDocxBlock", varargs...)
	ret0, _ := ret[0].([]*larkdocx.Block)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLarkDocxBlock indicates an expected call of GetLarkDocxBlock.
func (mr *MockClientMockRecorder) GetLarkDocxBlock(arg0, arg1, arg2 any, arg3 ...any) *MockClientGetLarkDocxBlockCall {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{arg0, arg1, arg2}, arg3...)
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLarkDocxBlock", reflect.TypeOf((*MockClient)(nil).GetLarkDocxBlock), varargs...)
	return &MockClientGetLarkDocxBlockCall{Call: call}
}

// MockClientGetLarkDocxBlockCall wrap *gomock.Call
type MockClientGetLarkDocxBlockCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockClientGetLarkDocxBlockCall) Return(arg0 []*larkdocx.Block, arg1 error) *MockClientGetLarkDocxBlockCall {
	c.Call = c.Call.Return(arg0, arg1)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockClientGetLarkDocxBlockCall) Do(f func(context.Context, string, string, ...lark.Option) ([]*larkdocx.Block, error)) *MockClientGetLarkDocxBlockCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockClientGetLarkDocxBlockCall) DoAndReturn(f func(context.Context, string, string, ...lark.Option) ([]*larkdocx.Block, error)) *MockClientGetLarkDocxBlockCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// GetLarkDocxBlocks mocks base method.
func (m *MockClient) GetLarkDocxBlocks(arg0 context.Context, arg1, arg2 string) ([]*larkdocx.Block, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLarkDocxBlocks", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*larkdocx.Block)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLarkDocxBlocks indicates an expected call of GetLarkDocxBlocks.
func (mr *MockClientMockRecorder) GetLarkDocxBlocks(arg0, arg1, arg2 any) *MockClientGetLarkDocxBlocksCall {
	mr.mock.ctrl.T.Helper()
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLarkDocxBlocks", reflect.TypeOf((*MockClient)(nil).GetLarkDocxBlocks), arg0, arg1, arg2)
	return &MockClientGetLarkDocxBlocksCall{Call: call}
}

// MockClientGetLarkDocxBlocksCall wrap *gomock.Call
type MockClientGetLarkDocxBlocksCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockClientGetLarkDocxBlocksCall) Return(arg0 []*larkdocx.Block, arg1 error) *MockClientGetLarkDocxBlocksCall {
	c.Call = c.Call.Return(arg0, arg1)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockClientGetLarkDocxBlocksCall) Do(f func(context.Context, string, string) ([]*larkdocx.Block, error)) *MockClientGetLarkDocxBlocksCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockClientGetLarkDocxBlocksCall) DoAndReturn(f func(context.Context, string, string) ([]*larkdocx.Block, error)) *MockClientGetLarkDocxBlocksCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// GetLarkDocxRawContent mocks base method.
func (m *MockClient) GetLarkDocxRawContent(arg0 context.Context, arg1, arg2 string) (*lark.DocxContent, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLarkDocxRawContent", arg0, arg1, arg2)
	ret0, _ := ret[0].(*lark.DocxContent)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLarkDocxRawContent indicates an expected call of GetLarkDocxRawContent.
func (mr *MockClientMockRecorder) GetLarkDocxRawContent(arg0, arg1, arg2 any) *MockClientGetLarkDocxRawContentCall {
	mr.mock.ctrl.T.Helper()
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLarkDocxRawContent", reflect.TypeOf((*MockClient)(nil).GetLarkDocxRawContent), arg0, arg1, arg2)
	return &MockClientGetLarkDocxRawContentCall{Call: call}
}

// MockClientGetLarkDocxRawContentCall wrap *gomock.Call
type MockClientGetLarkDocxRawContentCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockClientGetLarkDocxRawContentCall) Return(arg0 *lark.DocxContent, arg1 error) *MockClientGetLarkDocxRawContentCall {
	c.Call = c.Call.Return(arg0, arg1)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockClientGetLarkDocxRawContentCall) Do(f func(context.Context, string, string) (*lark.DocxContent, error)) *MockClientGetLarkDocxRawContentCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockClientGetLarkDocxRawContentCall) DoAndReturn(f func(context.Context, string, string) (*lark.DocxContent, error)) *MockClientGetLarkDocxRawContentCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// GetLarkEntity mocks base method.
func (m *MockClient) GetLarkEntity(arg0 context.Context, arg1 string) (*larklingo.Entity, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLarkEntity", arg0, arg1)
	ret0, _ := ret[0].(*larklingo.Entity)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLarkEntity indicates an expected call of GetLarkEntity.
func (mr *MockClientMockRecorder) GetLarkEntity(arg0, arg1 any) *MockClientGetLarkEntityCall {
	mr.mock.ctrl.T.Helper()
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLarkEntity", reflect.TypeOf((*MockClient)(nil).GetLarkEntity), arg0, arg1)
	return &MockClientGetLarkEntityCall{Call: call}
}

// MockClientGetLarkEntityCall wrap *gomock.Call
type MockClientGetLarkEntityCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockClientGetLarkEntityCall) Return(arg0 *larklingo.Entity, arg1 error) *MockClientGetLarkEntityCall {
	c.Call = c.Call.Return(arg0, arg1)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockClientGetLarkEntityCall) Do(f func(context.Context, string) (*larklingo.Entity, error)) *MockClientGetLarkEntityCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockClientGetLarkEntityCall) DoAndReturn(f func(context.Context, string) (*larklingo.Entity, error)) *MockClientGetLarkEntityCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// GetLarkEntityDesc mocks base method.
func (m *MockClient) GetLarkEntityDesc(arg0 context.Context, arg1 []string) ([]*lark.EntityInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLarkEntityDesc", arg0, arg1)
	ret0, _ := ret[0].([]*lark.EntityInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLarkEntityDesc indicates an expected call of GetLarkEntityDesc.
func (mr *MockClientMockRecorder) GetLarkEntityDesc(arg0, arg1 any) *MockClientGetLarkEntityDescCall {
	mr.mock.ctrl.T.Helper()
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLarkEntityDesc", reflect.TypeOf((*MockClient)(nil).GetLarkEntityDesc), arg0, arg1)
	return &MockClientGetLarkEntityDescCall{Call: call}
}

// MockClientGetLarkEntityDescCall wrap *gomock.Call
type MockClientGetLarkEntityDescCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockClientGetLarkEntityDescCall) Return(arg0 []*lark.EntityInfo, arg1 error) *MockClientGetLarkEntityDescCall {
	c.Call = c.Call.Return(arg0, arg1)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockClientGetLarkEntityDescCall) Do(f func(context.Context, []string) ([]*lark.EntityInfo, error)) *MockClientGetLarkEntityDescCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockClientGetLarkEntityDescCall) DoAndReturn(f func(context.Context, []string) ([]*lark.EntityInfo, error)) *MockClientGetLarkEntityDescCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// GetLarkFileStatisticsInfo mocks base method.
func (m *MockClient) GetLarkFileStatisticsInfo(arg0 context.Context, arg1, arg2, arg3 string, arg4 ...lark.Option) (lark.FileStatistics, error) {
	m.ctrl.T.Helper()
	varargs := []any{arg0, arg1, arg2, arg3}
	for _, a := range arg4 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLarkFileStatisticsInfo", varargs...)
	ret0, _ := ret[0].(lark.FileStatistics)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLarkFileStatisticsInfo indicates an expected call of GetLarkFileStatisticsInfo.
func (mr *MockClientMockRecorder) GetLarkFileStatisticsInfo(arg0, arg1, arg2, arg3 any, arg4 ...any) *MockClientGetLarkFileStatisticsInfoCall {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{arg0, arg1, arg2, arg3}, arg4...)
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLarkFileStatisticsInfo", reflect.TypeOf((*MockClient)(nil).GetLarkFileStatisticsInfo), varargs...)
	return &MockClientGetLarkFileStatisticsInfoCall{Call: call}
}

// MockClientGetLarkFileStatisticsInfoCall wrap *gomock.Call
type MockClientGetLarkFileStatisticsInfoCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockClientGetLarkFileStatisticsInfoCall) Return(arg0 lark.FileStatistics, arg1 error) *MockClientGetLarkFileStatisticsInfoCall {
	c.Call = c.Call.Return(arg0, arg1)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockClientGetLarkFileStatisticsInfoCall) Do(f func(context.Context, string, string, string, ...lark.Option) (lark.FileStatistics, error)) *MockClientGetLarkFileStatisticsInfoCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockClientGetLarkFileStatisticsInfoCall) DoAndReturn(f func(context.Context, string, string, string, ...lark.Option) (lark.FileStatistics, error)) *MockClientGetLarkFileStatisticsInfoCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// GetRootFolderMeta mocks base method.
func (m *MockClient) GetRootFolderMeta(arg0 context.Context, arg1 string) (*lark.RootFolderMeta, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRootFolderMeta", arg0, arg1)
	ret0, _ := ret[0].(*lark.RootFolderMeta)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRootFolderMeta indicates an expected call of GetRootFolderMeta.
func (mr *MockClientMockRecorder) GetRootFolderMeta(arg0, arg1 any) *MockClientGetRootFolderMetaCall {
	mr.mock.ctrl.T.Helper()
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRootFolderMeta", reflect.TypeOf((*MockClient)(nil).GetRootFolderMeta), arg0, arg1)
	return &MockClientGetRootFolderMetaCall{Call: call}
}

// MockClientGetRootFolderMetaCall wrap *gomock.Call
type MockClientGetRootFolderMetaCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockClientGetRootFolderMetaCall) Return(arg0 *lark.RootFolderMeta, arg1 error) *MockClientGetRootFolderMetaCall {
	c.Call = c.Call.Return(arg0, arg1)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockClientGetRootFolderMetaCall) Do(f func(context.Context, string) (*lark.RootFolderMeta, error)) *MockClientGetRootFolderMetaCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockClientGetRootFolderMetaCall) DoAndReturn(f func(context.Context, string) (*lark.RootFolderMeta, error)) *MockClientGetRootFolderMetaCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// GetSheetMarkdownContent mocks base method.
func (m *MockClient) GetSheetMarkdownContent(arg0 context.Context, arg1, arg2 string, arg3 []string, arg4 string) (*lark.SheetMarkdownData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSheetMarkdownContent", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*lark.SheetMarkdownData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSheetMarkdownContent indicates an expected call of GetSheetMarkdownContent.
func (mr *MockClientMockRecorder) GetSheetMarkdownContent(arg0, arg1, arg2, arg3, arg4 any) *MockClientGetSheetMarkdownContentCall {
	mr.mock.ctrl.T.Helper()
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSheetMarkdownContent", reflect.TypeOf((*MockClient)(nil).GetSheetMarkdownContent), arg0, arg1, arg2, arg3, arg4)
	return &MockClientGetSheetMarkdownContentCall{Call: call}
}

// MockClientGetSheetMarkdownContentCall wrap *gomock.Call
type MockClientGetSheetMarkdownContentCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockClientGetSheetMarkdownContentCall) Return(arg0 *lark.SheetMarkdownData, arg1 error) *MockClientGetSheetMarkdownContentCall {
	c.Call = c.Call.Return(arg0, arg1)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockClientGetSheetMarkdownContentCall) Do(f func(context.Context, string, string, []string, string) (*lark.SheetMarkdownData, error)) *MockClientGetSheetMarkdownContentCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockClientGetSheetMarkdownContentCall) DoAndReturn(f func(context.Context, string, string, []string, string) (*lark.SheetMarkdownData, error)) *MockClientGetSheetMarkdownContentCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// GetSheetsSubIDs mocks base method.
func (m *MockClient) GetSheetsSubIDs(arg0 context.Context, arg1, arg2 string) (*larksheets.QuerySpreadsheetSheetResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSheetsSubIDs", arg0, arg1, arg2)
	ret0, _ := ret[0].(*larksheets.QuerySpreadsheetSheetResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSheetsSubIDs indicates an expected call of GetSheetsSubIDs.
func (mr *MockClientMockRecorder) GetSheetsSubIDs(arg0, arg1, arg2 any) *MockClientGetSheetsSubIDsCall {
	mr.mock.ctrl.T.Helper()
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSheetsSubIDs", reflect.TypeOf((*MockClient)(nil).GetSheetsSubIDs), arg0, arg1, arg2)
	return &MockClientGetSheetsSubIDsCall{Call: call}
}

// MockClientGetSheetsSubIDsCall wrap *gomock.Call
type MockClientGetSheetsSubIDsCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockClientGetSheetsSubIDsCall) Return(arg0 *larksheets.QuerySpreadsheetSheetResp, arg1 error) *MockClientGetSheetsSubIDsCall {
	c.Call = c.Call.Return(arg0, arg1)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockClientGetSheetsSubIDsCall) Do(f func(context.Context, string, string) (*larksheets.QuerySpreadsheetSheetResp, error)) *MockClientGetSheetsSubIDsCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockClientGetSheetsSubIDsCall) DoAndReturn(f func(context.Context, string, string) (*larksheets.QuerySpreadsheetSheetResp, error)) *MockClientGetSheetsSubIDsCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// GetUserAccessToken mocks base method.
func (m *MockClient) GetUserAccessToken(arg0 context.Context, arg1 string) (lark.UserToken, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserAccessToken", arg0, arg1)
	ret0, _ := ret[0].(lark.UserToken)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserAccessToken indicates an expected call of GetUserAccessToken.
func (mr *MockClientMockRecorder) GetUserAccessToken(arg0, arg1 any) *MockClientGetUserAccessTokenCall {
	mr.mock.ctrl.T.Helper()
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserAccessToken", reflect.TypeOf((*MockClient)(nil).GetUserAccessToken), arg0, arg1)
	return &MockClientGetUserAccessTokenCall{Call: call}
}

// MockClientGetUserAccessTokenCall wrap *gomock.Call
type MockClientGetUserAccessTokenCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockClientGetUserAccessTokenCall) Return(arg0 lark.UserToken, arg1 error) *MockClientGetUserAccessTokenCall {
	c.Call = c.Call.Return(arg0, arg1)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockClientGetUserAccessTokenCall) Do(f func(context.Context, string) (lark.UserToken, error)) *MockClientGetUserAccessTokenCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockClientGetUserAccessTokenCall) DoAndReturn(f func(context.Context, string) (lark.UserToken, error)) *MockClientGetUserAccessTokenCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// GetWikiNode mocks base method.
func (m *MockClient) GetWikiNode(arg0 context.Context, arg1, arg2 string, arg3 ...lark.Option) (*larkwiki.Node, error) {
	m.ctrl.T.Helper()
	varargs := []any{arg0, arg1, arg2}
	for _, a := range arg3 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetWikiNode", varargs...)
	ret0, _ := ret[0].(*larkwiki.Node)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWikiNode indicates an expected call of GetWikiNode.
func (mr *MockClientMockRecorder) GetWikiNode(arg0, arg1, arg2 any, arg3 ...any) *MockClientGetWikiNodeCall {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{arg0, arg1, arg2}, arg3...)
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWikiNode", reflect.TypeOf((*MockClient)(nil).GetWikiNode), varargs...)
	return &MockClientGetWikiNodeCall{Call: call}
}

// MockClientGetWikiNodeCall wrap *gomock.Call
type MockClientGetWikiNodeCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockClientGetWikiNodeCall) Return(arg0 *larkwiki.Node, arg1 error) *MockClientGetWikiNodeCall {
	c.Call = c.Call.Return(arg0, arg1)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockClientGetWikiNodeCall) Do(f func(context.Context, string, string, ...lark.Option) (*larkwiki.Node, error)) *MockClientGetWikiNodeCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockClientGetWikiNodeCall) DoAndReturn(f func(context.Context, string, string, ...lark.Option) (*larkwiki.Node, error)) *MockClientGetWikiNodeCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// GetWikiNodeInfo mocks base method.
func (m *MockClient) GetWikiNodeInfo(arg0 context.Context, arg1, arg2 string) (*larkwiki.GetNodeSpaceResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWikiNodeInfo", arg0, arg1, arg2)
	ret0, _ := ret[0].(*larkwiki.GetNodeSpaceResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWikiNodeInfo indicates an expected call of GetWikiNodeInfo.
func (mr *MockClientMockRecorder) GetWikiNodeInfo(arg0, arg1, arg2 any) *MockClientGetWikiNodeInfoCall {
	mr.mock.ctrl.T.Helper()
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWikiNodeInfo", reflect.TypeOf((*MockClient)(nil).GetWikiNodeInfo), arg0, arg1, arg2)
	return &MockClientGetWikiNodeInfoCall{Call: call}
}

// MockClientGetWikiNodeInfoCall wrap *gomock.Call
type MockClientGetWikiNodeInfoCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockClientGetWikiNodeInfoCall) Return(arg0 *larkwiki.GetNodeSpaceResp, arg1 error) *MockClientGetWikiNodeInfoCall {
	c.Call = c.Call.Return(arg0, arg1)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockClientGetWikiNodeInfoCall) Do(f func(context.Context, string, string) (*larkwiki.GetNodeSpaceResp, error)) *MockClientGetWikiNodeInfoCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockClientGetWikiNodeInfoCall) DoAndReturn(f func(context.Context, string, string) (*larkwiki.GetNodeSpaceResp, error)) *MockClientGetWikiNodeInfoCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// InsertDocxBlockDescendants mocks base method.
func (m *MockClient) InsertDocxBlockDescendants(arg0 context.Context, arg1, arg2 string, arg3 int, arg4 []string, arg5 []*larkdocx.Block, arg6 string) (*lark.DocxBlockDescendantsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertDocxBlockDescendants", arg0, arg1, arg2, arg3, arg4, arg5, arg6)
	ret0, _ := ret[0].(*lark.DocxBlockDescendantsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InsertDocxBlockDescendants indicates an expected call of InsertDocxBlockDescendants.
func (mr *MockClientMockRecorder) InsertDocxBlockDescendants(arg0, arg1, arg2, arg3, arg4, arg5, arg6 any) *MockClientInsertDocxBlockDescendantsCall {
	mr.mock.ctrl.T.Helper()
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertDocxBlockDescendants", reflect.TypeOf((*MockClient)(nil).InsertDocxBlockDescendants), arg0, arg1, arg2, arg3, arg4, arg5, arg6)
	return &MockClientInsertDocxBlockDescendantsCall{Call: call}
}

// MockClientInsertDocxBlockDescendantsCall wrap *gomock.Call
type MockClientInsertDocxBlockDescendantsCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockClientInsertDocxBlockDescendantsCall) Return(arg0 *lark.DocxBlockDescendantsResponse, arg1 error) *MockClientInsertDocxBlockDescendantsCall {
	c.Call = c.Call.Return(arg0, arg1)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockClientInsertDocxBlockDescendantsCall) Do(f func(context.Context, string, string, int, []string, []*larkdocx.Block, string) (*lark.DocxBlockDescendantsResponse, error)) *MockClientInsertDocxBlockDescendantsCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockClientInsertDocxBlockDescendantsCall) DoAndReturn(f func(context.Context, string, string, int, []string, []*larkdocx.Block, string) (*lark.DocxBlockDescendantsResponse, error)) *MockClientInsertDocxBlockDescendantsCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// ListAppTableField mocks base method.
func (m *MockClient) ListAppTableField(arg0 context.Context, arg1, arg2, arg3 string, arg4 int, arg5 string) (*larkbitable.ListAppTableFieldRespData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListAppTableField", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(*larkbitable.ListAppTableFieldRespData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListAppTableField indicates an expected call of ListAppTableField.
func (mr *MockClientMockRecorder) ListAppTableField(arg0, arg1, arg2, arg3, arg4, arg5 any) *MockClientListAppTableFieldCall {
	mr.mock.ctrl.T.Helper()
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAppTableField", reflect.TypeOf((*MockClient)(nil).ListAppTableField), arg0, arg1, arg2, arg3, arg4, arg5)
	return &MockClientListAppTableFieldCall{Call: call}
}

// MockClientListAppTableFieldCall wrap *gomock.Call
type MockClientListAppTableFieldCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockClientListAppTableFieldCall) Return(arg0 *larkbitable.ListAppTableFieldRespData, arg1 error) *MockClientListAppTableFieldCall {
	c.Call = c.Call.Return(arg0, arg1)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockClientListAppTableFieldCall) Do(f func(context.Context, string, string, string, int, string) (*larkbitable.ListAppTableFieldRespData, error)) *MockClientListAppTableFieldCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockClientListAppTableFieldCall) DoAndReturn(f func(context.Context, string, string, string, int, string) (*larkbitable.ListAppTableFieldRespData, error)) *MockClientListAppTableFieldCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// ListBitableAppTable mocks base method.
func (m *MockClient) ListBitableAppTable(arg0 context.Context, arg1, arg2 string, arg3 int, arg4 string) (*larkbitable.ListAppTableRespData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListBitableAppTable", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*larkbitable.ListAppTableRespData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListBitableAppTable indicates an expected call of ListBitableAppTable.
func (mr *MockClientMockRecorder) ListBitableAppTable(arg0, arg1, arg2, arg3, arg4 any) *MockClientListBitableAppTableCall {
	mr.mock.ctrl.T.Helper()
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListBitableAppTable", reflect.TypeOf((*MockClient)(nil).ListBitableAppTable), arg0, arg1, arg2, arg3, arg4)
	return &MockClientListBitableAppTableCall{Call: call}
}

// MockClientListBitableAppTableCall wrap *gomock.Call
type MockClientListBitableAppTableCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockClientListBitableAppTableCall) Return(arg0 *larkbitable.ListAppTableRespData, arg1 error) *MockClientListBitableAppTableCall {
	c.Call = c.Call.Return(arg0, arg1)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockClientListBitableAppTableCall) Do(f func(context.Context, string, string, int, string) (*larkbitable.ListAppTableRespData, error)) *MockClientListBitableAppTableCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockClientListBitableAppTableCall) DoAndReturn(f func(context.Context, string, string, int, string) (*larkbitable.ListAppTableRespData, error)) *MockClientListBitableAppTableCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// ListFiles mocks base method.
func (m *MockClient) ListFiles(arg0 context.Context, arg1, arg2 string, arg3 int, arg4, arg5 string, arg6 ...lark.Option) (*larkdrive.ListFileRespData, error) {
	m.ctrl.T.Helper()
	varargs := []any{arg0, arg1, arg2, arg3, arg4, arg5}
	for _, a := range arg6 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListFiles", varargs...)
	ret0, _ := ret[0].(*larkdrive.ListFileRespData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListFiles indicates an expected call of ListFiles.
func (mr *MockClientMockRecorder) ListFiles(arg0, arg1, arg2, arg3, arg4, arg5 any, arg6 ...any) *MockClientListFilesCall {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{arg0, arg1, arg2, arg3, arg4, arg5}, arg6...)
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListFiles", reflect.TypeOf((*MockClient)(nil).ListFiles), varargs...)
	return &MockClientListFilesCall{Call: call}
}

// MockClientListFilesCall wrap *gomock.Call
type MockClientListFilesCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockClientListFilesCall) Return(arg0 *larkdrive.ListFileRespData, arg1 error) *MockClientListFilesCall {
	c.Call = c.Call.Return(arg0, arg1)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockClientListFilesCall) Do(f func(context.Context, string, string, int, string, string, ...lark.Option) (*larkdrive.ListFileRespData, error)) *MockClientListFilesCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockClientListFilesCall) DoAndReturn(f func(context.Context, string, string, int, string, string, ...lark.Option) (*larkdrive.ListFileRespData, error)) *MockClientListFilesCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// ListLarkUsers mocks base method.
func (m *MockClient) ListLarkUsers(arg0 context.Context, arg1 []string, arg2 string, arg3 ...lark.Option) ([]*larkcontact.User, error) {
	m.ctrl.T.Helper()
	varargs := []any{arg0, arg1, arg2}
	for _, a := range arg3 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListLarkUsers", varargs...)
	ret0, _ := ret[0].([]*larkcontact.User)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListLarkUsers indicates an expected call of ListLarkUsers.
func (mr *MockClientMockRecorder) ListLarkUsers(arg0, arg1, arg2 any, arg3 ...any) *MockClientListLarkUsersCall {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{arg0, arg1, arg2}, arg3...)
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListLarkUsers", reflect.TypeOf((*MockClient)(nil).ListLarkUsers), varargs...)
	return &MockClientListLarkUsersCall{Call: call}
}

// MockClientListLarkUsersCall wrap *gomock.Call
type MockClientListLarkUsersCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockClientListLarkUsersCall) Return(arg0 []*larkcontact.User, arg1 error) *MockClientListLarkUsersCall {
	c.Call = c.Call.Return(arg0, arg1)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockClientListLarkUsersCall) Do(f func(context.Context, []string, string, ...lark.Option) ([]*larkcontact.User, error)) *MockClientListLarkUsersCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockClientListLarkUsersCall) DoAndReturn(f func(context.Context, []string, string, ...lark.Option) ([]*larkcontact.User, error)) *MockClientListLarkUsersCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// ListPermissionMember mocks base method.
func (m *MockClient) ListPermissionMember(arg0 context.Context, arg1 *larkdrive.ListPermissionMemberReq, arg2 string) (*larkdrive.ListPermissionMemberRespData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListPermissionMember", arg0, arg1, arg2)
	ret0, _ := ret[0].(*larkdrive.ListPermissionMemberRespData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListPermissionMember indicates an expected call of ListPermissionMember.
func (mr *MockClientMockRecorder) ListPermissionMember(arg0, arg1, arg2 any) *MockClientListPermissionMemberCall {
	mr.mock.ctrl.T.Helper()
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListPermissionMember", reflect.TypeOf((*MockClient)(nil).ListPermissionMember), arg0, arg1, arg2)
	return &MockClientListPermissionMemberCall{Call: call}
}

// MockClientListPermissionMemberCall wrap *gomock.Call
type MockClientListPermissionMemberCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockClientListPermissionMemberCall) Return(arg0 *larkdrive.ListPermissionMemberRespData, arg1 error) *MockClientListPermissionMemberCall {
	c.Call = c.Call.Return(arg0, arg1)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockClientListPermissionMemberCall) Do(f func(context.Context, *larkdrive.ListPermissionMemberReq, string) (*larkdrive.ListPermissionMemberRespData, error)) *MockClientListPermissionMemberCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockClientListPermissionMemberCall) DoAndReturn(f func(context.Context, *larkdrive.ListPermissionMemberReq, string) (*larkdrive.ListPermissionMemberRespData, error)) *MockClientListPermissionMemberCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// ListWikiNodes mocks base method.
func (m *MockClient) ListWikiNodes(arg0 context.Context, arg1, arg2, arg3 string) ([]*larkwiki.Node, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListWikiNodes", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]*larkwiki.Node)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListWikiNodes indicates an expected call of ListWikiNodes.
func (mr *MockClientMockRecorder) ListWikiNodes(arg0, arg1, arg2, arg3 any) *MockClientListWikiNodesCall {
	mr.mock.ctrl.T.Helper()
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListWikiNodes", reflect.TypeOf((*MockClient)(nil).ListWikiNodes), arg0, arg1, arg2, arg3)
	return &MockClientListWikiNodesCall{Call: call}
}

// MockClientListWikiNodesCall wrap *gomock.Call
type MockClientListWikiNodesCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockClientListWikiNodesCall) Return(arg0 []*larkwiki.Node, arg1 error) *MockClientListWikiNodesCall {
	c.Call = c.Call.Return(arg0, arg1)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockClientListWikiNodesCall) Do(f func(context.Context, string, string, string) ([]*larkwiki.Node, error)) *MockClientListWikiNodesCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockClientListWikiNodesCall) DoAndReturn(f func(context.Context, string, string, string) ([]*larkwiki.Node, error)) *MockClientListWikiNodesCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// MGetUserIDByEmail mocks base method.
func (m *MockClient) MGetUserIDByEmail(arg0 context.Context, arg1 []string, arg2 lark.UserIDType) (map[string]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MGetUserIDByEmail", arg0, arg1, arg2)
	ret0, _ := ret[0].(map[string]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MGetUserIDByEmail indicates an expected call of MGetUserIDByEmail.
func (mr *MockClientMockRecorder) MGetUserIDByEmail(arg0, arg1, arg2 any) *MockClientMGetUserIDByEmailCall {
	mr.mock.ctrl.T.Helper()
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MGetUserIDByEmail", reflect.TypeOf((*MockClient)(nil).MGetUserIDByEmail), arg0, arg1, arg2)
	return &MockClientMGetUserIDByEmailCall{Call: call}
}

// MockClientMGetUserIDByEmailCall wrap *gomock.Call
type MockClientMGetUserIDByEmailCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockClientMGetUserIDByEmailCall) Return(arg0 map[string]string, arg1 error) *MockClientMGetUserIDByEmailCall {
	c.Call = c.Call.Return(arg0, arg1)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockClientMGetUserIDByEmailCall) Do(f func(context.Context, []string, lark.UserIDType) (map[string]string, error)) *MockClientMGetUserIDByEmailCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockClientMGetUserIDByEmailCall) DoAndReturn(f func(context.Context, []string, lark.UserIDType) (map[string]string, error)) *MockClientMGetUserIDByEmailCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// QuerySheet mocks base method.
func (m *MockClient) QuerySheet(arg0 context.Context, arg1, arg2 string) (*larksheets.QuerySpreadsheetSheetRespData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QuerySheet", arg0, arg1, arg2)
	ret0, _ := ret[0].(*larksheets.QuerySpreadsheetSheetRespData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QuerySheet indicates an expected call of QuerySheet.
func (mr *MockClientMockRecorder) QuerySheet(arg0, arg1, arg2 any) *MockClientQuerySheetCall {
	mr.mock.ctrl.T.Helper()
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QuerySheet", reflect.TypeOf((*MockClient)(nil).QuerySheet), arg0, arg1, arg2)
	return &MockClientQuerySheetCall{Call: call}
}

// MockClientQuerySheetCall wrap *gomock.Call
type MockClientQuerySheetCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockClientQuerySheetCall) Return(arg0 *larksheets.QuerySpreadsheetSheetRespData, arg1 error) *MockClientQuerySheetCall {
	c.Call = c.Call.Return(arg0, arg1)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockClientQuerySheetCall) Do(f func(context.Context, string, string) (*larksheets.QuerySpreadsheetSheetRespData, error)) *MockClientQuerySheetCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockClientQuerySheetCall) DoAndReturn(f func(context.Context, string, string) (*larksheets.QuerySpreadsheetSheetRespData, error)) *MockClientQuerySheetCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// ReadBatchLarkSheet mocks base method.
func (m *MockClient) ReadBatchLarkSheet(arg0 context.Context, arg1, arg2, arg3 string) (*lark.SheetData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReadBatchLarkSheet", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*lark.SheetData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReadBatchLarkSheet indicates an expected call of ReadBatchLarkSheet.
func (mr *MockClientMockRecorder) ReadBatchLarkSheet(arg0, arg1, arg2, arg3 any) *MockClientReadBatchLarkSheetCall {
	mr.mock.ctrl.T.Helper()
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReadBatchLarkSheet", reflect.TypeOf((*MockClient)(nil).ReadBatchLarkSheet), arg0, arg1, arg2, arg3)
	return &MockClientReadBatchLarkSheetCall{Call: call}
}

// MockClientReadBatchLarkSheetCall wrap *gomock.Call
type MockClientReadBatchLarkSheetCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockClientReadBatchLarkSheetCall) Return(arg0 *lark.SheetData, arg1 error) *MockClientReadBatchLarkSheetCall {
	c.Call = c.Call.Return(arg0, arg1)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockClientReadBatchLarkSheetCall) Do(f func(context.Context, string, string, string) (*lark.SheetData, error)) *MockClientReadBatchLarkSheetCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockClientReadBatchLarkSheetCall) DoAndReturn(f func(context.Context, string, string, string) (*lark.SheetData, error)) *MockClientReadBatchLarkSheetCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// RefreshToken mocks base method.
func (m *MockClient) RefreshToken(arg0 context.Context, arg1 string) (lark.UserToken, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RefreshToken", arg0, arg1)
	ret0, _ := ret[0].(lark.UserToken)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RefreshToken indicates an expected call of RefreshToken.
func (mr *MockClientMockRecorder) RefreshToken(arg0, arg1 any) *MockClientRefreshTokenCall {
	mr.mock.ctrl.T.Helper()
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RefreshToken", reflect.TypeOf((*MockClient)(nil).RefreshToken), arg0, arg1)
	return &MockClientRefreshTokenCall{Call: call}
}

// MockClientRefreshTokenCall wrap *gomock.Call
type MockClientRefreshTokenCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockClientRefreshTokenCall) Return(arg0 lark.UserToken, arg1 error) *MockClientRefreshTokenCall {
	c.Call = c.Call.Return(arg0, arg1)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockClientRefreshTokenCall) Do(f func(context.Context, string) (lark.UserToken, error)) *MockClientRefreshTokenCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockClientRefreshTokenCall) DoAndReturn(f func(context.Context, string) (lark.UserToken, error)) *MockClientRefreshTokenCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// SearchBitableRecord mocks base method.
func (m *MockClient) SearchBitableRecord(arg0 context.Context, arg1, arg2, arg3, arg4 string, arg5 int, arg6 string) (*larkbitable.SearchAppTableRecordRespData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchBitableRecord", arg0, arg1, arg2, arg3, arg4, arg5, arg6)
	ret0, _ := ret[0].(*larkbitable.SearchAppTableRecordRespData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchBitableRecord indicates an expected call of SearchBitableRecord.
func (mr *MockClientMockRecorder) SearchBitableRecord(arg0, arg1, arg2, arg3, arg4, arg5, arg6 any) *MockClientSearchBitableRecordCall {
	mr.mock.ctrl.T.Helper()
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchBitableRecord", reflect.TypeOf((*MockClient)(nil).SearchBitableRecord), arg0, arg1, arg2, arg3, arg4, arg5, arg6)
	return &MockClientSearchBitableRecordCall{Call: call}
}

// MockClientSearchBitableRecordCall wrap *gomock.Call
type MockClientSearchBitableRecordCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockClientSearchBitableRecordCall) Return(arg0 *larkbitable.SearchAppTableRecordRespData, arg1 error) *MockClientSearchBitableRecordCall {
	c.Call = c.Call.Return(arg0, arg1)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockClientSearchBitableRecordCall) Do(f func(context.Context, string, string, string, string, int, string) (*larkbitable.SearchAppTableRecordRespData, error)) *MockClientSearchBitableRecordCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockClientSearchBitableRecordCall) DoAndReturn(f func(context.Context, string, string, string, string, int, string) (*larkbitable.SearchAppTableRecordRespData, error)) *MockClientSearchBitableRecordCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// SearchLarkData mocks base method.
func (m *MockClient) SearchLarkData(arg0 context.Context, arg1 int, arg2, arg3 string, arg4 *lark.PassageDisableSearch) (*lark.SearchMeta, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchLarkData", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*lark.SearchMeta)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchLarkData indicates an expected call of SearchLarkData.
func (mr *MockClientMockRecorder) SearchLarkData(arg0, arg1, arg2, arg3, arg4 any) *MockClientSearchLarkDataCall {
	mr.mock.ctrl.T.Helper()
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchLarkData", reflect.TypeOf((*MockClient)(nil).SearchLarkData), arg0, arg1, arg2, arg3, arg4)
	return &MockClientSearchLarkDataCall{Call: call}
}

// MockClientSearchLarkDataCall wrap *gomock.Call
type MockClientSearchLarkDataCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockClientSearchLarkDataCall) Return(arg0 *lark.SearchMeta, arg1 error) *MockClientSearchLarkDataCall {
	c.Call = c.Call.Return(arg0, arg1)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockClientSearchLarkDataCall) Do(f func(context.Context, int, string, string, *lark.PassageDisableSearch) (*lark.SearchMeta, error)) *MockClientSearchLarkDataCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockClientSearchLarkDataCall) DoAndReturn(f func(context.Context, int, string, string, *lark.PassageDisableSearch) (*lark.SearchMeta, error)) *MockClientSearchLarkDataCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// SearchLarkObject mocks base method.
func (m *MockClient) SearchLarkObject(arg0 context.Context, arg1 *lark.SearchLarkObjectRequest, arg2 string) (*lark.SearchLarkObjectRespData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchLarkObject", arg0, arg1, arg2)
	ret0, _ := ret[0].(*lark.SearchLarkObjectRespData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchLarkObject indicates an expected call of SearchLarkObject.
func (mr *MockClientMockRecorder) SearchLarkObject(arg0, arg1, arg2 any) *MockClientSearchLarkObjectCall {
	mr.mock.ctrl.T.Helper()
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchLarkObject", reflect.TypeOf((*MockClient)(nil).SearchLarkObject), arg0, arg1, arg2)
	return &MockClientSearchLarkObjectCall{Call: call}
}

// MockClientSearchLarkObjectCall wrap *gomock.Call
type MockClientSearchLarkObjectCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockClientSearchLarkObjectCall) Return(arg0 *lark.SearchLarkObjectRespData, arg1 error) *MockClientSearchLarkObjectCall {
	c.Call = c.Call.Return(arg0, arg1)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockClientSearchLarkObjectCall) Do(f func(context.Context, *lark.SearchLarkObjectRequest, string) (*lark.SearchLarkObjectRespData, error)) *MockClientSearchLarkObjectCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockClientSearchLarkObjectCall) DoAndReturn(f func(context.Context, *lark.SearchLarkObjectRequest, string) (*lark.SearchLarkObjectRespData, error)) *MockClientSearchLarkObjectCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// SendLarkApplicationMessage mocks base method.
func (m *MockClient) SendLarkApplicationMessage(arg0 context.Context, arg1 lark.ReceiveIDType, arg2, arg3, arg4 string) (*larkim.CreateMessageRespData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendLarkApplicationMessage", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*larkim.CreateMessageRespData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendLarkApplicationMessage indicates an expected call of SendLarkApplicationMessage.
func (mr *MockClientMockRecorder) SendLarkApplicationMessage(arg0, arg1, arg2, arg3, arg4 any) *MockClientSendLarkApplicationMessageCall {
	mr.mock.ctrl.T.Helper()
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendLarkApplicationMessage", reflect.TypeOf((*MockClient)(nil).SendLarkApplicationMessage), arg0, arg1, arg2, arg3, arg4)
	return &MockClientSendLarkApplicationMessageCall{Call: call}
}

// MockClientSendLarkApplicationMessageCall wrap *gomock.Call
type MockClientSendLarkApplicationMessageCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockClientSendLarkApplicationMessageCall) Return(arg0 *larkim.CreateMessageRespData, arg1 error) *MockClientSendLarkApplicationMessageCall {
	c.Call = c.Call.Return(arg0, arg1)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockClientSendLarkApplicationMessageCall) Do(f func(context.Context, lark.ReceiveIDType, string, string, string) (*larkim.CreateMessageRespData, error)) *MockClientSendLarkApplicationMessageCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockClientSendLarkApplicationMessageCall) DoAndReturn(f func(context.Context, lark.ReceiveIDType, string, string, string) (*larkim.CreateMessageRespData, error)) *MockClientSendLarkApplicationMessageCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// TransferOwnerPermissionMember mocks base method.
func (m *MockClient) TransferOwnerPermissionMember(arg0 context.Context, arg1 *larkdrive.TransferOwnerPermissionMemberReq, arg2 string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TransferOwnerPermissionMember", arg0, arg1, arg2)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TransferOwnerPermissionMember indicates an expected call of TransferOwnerPermissionMember.
func (mr *MockClientMockRecorder) TransferOwnerPermissionMember(arg0, arg1, arg2 any) *MockClientTransferOwnerPermissionMemberCall {
	mr.mock.ctrl.T.Helper()
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TransferOwnerPermissionMember", reflect.TypeOf((*MockClient)(nil).TransferOwnerPermissionMember), arg0, arg1, arg2)
	return &MockClientTransferOwnerPermissionMemberCall{Call: call}
}

// MockClientTransferOwnerPermissionMemberCall wrap *gomock.Call
type MockClientTransferOwnerPermissionMemberCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockClientTransferOwnerPermissionMemberCall) Return(arg0 string, arg1 error) *MockClientTransferOwnerPermissionMemberCall {
	c.Call = c.Call.Return(arg0, arg1)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockClientTransferOwnerPermissionMemberCall) Do(f func(context.Context, *larkdrive.TransferOwnerPermissionMemberReq, string) (string, error)) *MockClientTransferOwnerPermissionMemberCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockClientTransferOwnerPermissionMemberCall) DoAndReturn(f func(context.Context, *larkdrive.TransferOwnerPermissionMemberReq, string) (string, error)) *MockClientTransferOwnerPermissionMemberCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// UploadLarkFile mocks base method.
func (m *MockClient) UploadLarkFile(arg0 context.Context, arg1, arg2 string, arg3 int, arg4 io.Reader) (*string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UploadLarkFile", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UploadLarkFile indicates an expected call of UploadLarkFile.
func (mr *MockClientMockRecorder) UploadLarkFile(arg0, arg1, arg2, arg3, arg4 any) *MockClientUploadLarkFileCall {
	mr.mock.ctrl.T.Helper()
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UploadLarkFile", reflect.TypeOf((*MockClient)(nil).UploadLarkFile), arg0, arg1, arg2, arg3, arg4)
	return &MockClientUploadLarkFileCall{Call: call}
}

// MockClientUploadLarkFileCall wrap *gomock.Call
type MockClientUploadLarkFileCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockClientUploadLarkFileCall) Return(arg0 *string, arg1 error) *MockClientUploadLarkFileCall {
	c.Call = c.Call.Return(arg0, arg1)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockClientUploadLarkFileCall) Do(f func(context.Context, string, string, int, io.Reader) (*string, error)) *MockClientUploadLarkFileCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockClientUploadLarkFileCall) DoAndReturn(f func(context.Context, string, string, int, io.Reader) (*string, error)) *MockClientUploadLarkFileCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// UploadLarkMedia mocks base method.
func (m *MockClient) UploadLarkMedia(arg0 context.Context, arg1 *larkdrive.UploadAllMediaReqBody, arg2 string) (*string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UploadLarkMedia", arg0, arg1, arg2)
	ret0, _ := ret[0].(*string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UploadLarkMedia indicates an expected call of UploadLarkMedia.
func (mr *MockClientMockRecorder) UploadLarkMedia(arg0, arg1, arg2 any) *MockClientUploadLarkMediaCall {
	mr.mock.ctrl.T.Helper()
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UploadLarkMedia", reflect.TypeOf((*MockClient)(nil).UploadLarkMedia), arg0, arg1, arg2)
	return &MockClientUploadLarkMediaCall{Call: call}
}

// MockClientUploadLarkMediaCall wrap *gomock.Call
type MockClientUploadLarkMediaCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockClientUploadLarkMediaCall) Return(arg0 *string, arg1 error) *MockClientUploadLarkMediaCall {
	c.Call = c.Call.Return(arg0, arg1)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockClientUploadLarkMediaCall) Do(f func(context.Context, *larkdrive.UploadAllMediaReqBody, string) (*string, error)) *MockClientUploadLarkMediaCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockClientUploadLarkMediaCall) DoAndReturn(f func(context.Context, *larkdrive.UploadAllMediaReqBody, string) (*string, error)) *MockClientUploadLarkMediaCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// WriteBatchLarkSheet mocks base method.
func (m *MockClient) WriteBatchLarkSheet(arg0 context.Context, arg1 string, arg2 []*lark.SheetValueRange) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WriteBatchLarkSheet", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// WriteBatchLarkSheet indicates an expected call of WriteBatchLarkSheet.
func (mr *MockClientMockRecorder) WriteBatchLarkSheet(arg0, arg1, arg2 any) *MockClientWriteBatchLarkSheetCall {
	mr.mock.ctrl.T.Helper()
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WriteBatchLarkSheet", reflect.TypeOf((*MockClient)(nil).WriteBatchLarkSheet), arg0, arg1, arg2)
	return &MockClientWriteBatchLarkSheetCall{Call: call}
}

// MockClientWriteBatchLarkSheetCall wrap *gomock.Call
type MockClientWriteBatchLarkSheetCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockClientWriteBatchLarkSheetCall) Return(arg0 error) *MockClientWriteBatchLarkSheetCall {
	c.Call = c.Call.Return(arg0)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockClientWriteBatchLarkSheetCall) Do(f func(context.Context, string, []*lark.SheetValueRange) error) *MockClientWriteBatchLarkSheetCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockClientWriteBatchLarkSheetCall) DoAndReturn(f func(context.Context, string, []*lark.SheetValueRange) error) *MockClientWriteBatchLarkSheetCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}
