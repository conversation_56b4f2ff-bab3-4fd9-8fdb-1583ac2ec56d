export LOCAL_MODULE := code.byted.org/devgpt/kiwis
export SHELL := /bin/bash

PWD := $(shell pwd)

# 静态库路径
# mac是darwin-arm64版本的
STATIC_LIB_DIR_MAC :=  $(PWD)/resources/link/mac
STATIC_LIB_DIR_LINUX := $(PWD)/resources/link/linux
# 检测操作系统和架构
UNAME_S := $(shell uname -s)
UNAME_M := $(shell uname -m)

# 根据操作系统和架构选择静态库路径
ifeq ($(UNAME_S), Darwin)
    STATIC_LIB_DIR := $(STATIC_LIB_DIR_MAC)
else ifeq ($(UNAME_S), Linux)
    ifeq ($(UNAME_M), x86_64)
        STATIC_LIB_DIR := $(STATIC_LIB_DIR_LINUX)
    endif
endif

# Generate Go code from thrift IDL.
.PHONY: gen_idl
gen_idl: gen_copilot_idl gen_llm_idl
	@echo "generated code from IDL"

gen:
	go generate ./...

# Build server binary for LLMStack uni-service.
.PHONY: build_devgpt_server
build_devgpt_server:
	go build -ldflags "-s -w" -o output/devgpt_server code.byted.org/devgpt/kiwis/apps/aiservice/cmd/server

# Build server binary for copilot service.
.PHONY: build_copilot_server
build_copilot_server:
	go build -ldflags "-s -w" -o output/copilot_server code.byted.org/devgpt/kiwis/bitscopilot/cmd/server

# Build server binary for context service.
.PHONY: build_context_server
build_context_server:
	go build -ldflags "-s -w" -o output/context_server code.byted.org/devgpt/kiwis/apps/aiservice/cmd/contextserver

# Build server binary for ide copilot.
.PHONY: build_ide_cronjob
build_ide_cronjob:
	go build -tags libtokenizer -ldflags "-extldflags '-L$(STATIC_LIB_DIR) -pthread' -s -w" -o output/main code.byted.org/devgpt/kiwis/idecopilot/cmd/cronjob

.PHONY: build_ide_server
build_ide_server:
	go build -tags "libtokenizer fornax" -ldflags "-extldflags '-L$(STATIC_LIB_DIR) -pthread' -s -w -X google.golang.org/protobuf/reflect/protoregistry.conflictPolicy=ignore" -o output/ide_server code.byted.org/devgpt/kiwis/idecopilot/cmd/aiserver

.PHONY: build_log_analysis_server
build_log_analysis_server:
	go build -ldflags "-s -w" -o output/log_analysis_server code.byted.org/devgpt/kiwis/apps/loganalysis/cmd/server

.PHONY: build_log_analysis_consumer_server
build_log_analysis_consumer_server:
	go build -ldflags "-w -s" -o output/main code.byted.org/devgpt/kiwis/apps/loganalysis/cmd/faasserver

.PHONY: build_knowledgebase
build_knowledgebase:
	go build -tags fornax -ldflags "-s -w -X google.golang.org/protobuf/reflect/protoregistry.conflictPolicy=ignore" -o output/knowledgebase code.byted.org/devgpt/kiwis/knowledgebase/cmd/server
build_splitter:
	go build -ldflags "-s -w" -o output/knowledgebase code.byted.org/devgpt/kiwis/knowledgebase/cmd/splitter

.PHONY: build_memory
build_memory:
	go build -ldflags "-s -w" -o output/memory code.byted.org/devgpt/kiwis/memory/cmd/server

.PHONY: build_memory_faas_tcc
build_memory_faas_tcc:
	go build -v -ldflags "-s -w" -o output/main code.byted.org/devgpt/kiwis/memory_faas/tcc_faas

.PHONY: build_memory_faas_scm
build_memory_faas_scm:
	go build -v -ldflags "-s -w" -o output/main code.byted.org/devgpt/kiwis/memory_faas/scm_faas

.PHONY: build_codeassist_repos_faas
build_codeassist_repos_faas:
	go build -v -ldflags "-s -w" -o output/main code.byted.org/devgpt/kiwis/codeassist/cmd/reporefreshfaas

.PHONY: build_codeassist_evaluation_faas
build_codeassist_evaluation_faas:
	export CGO_ENABLED=0 && go build -v -ldflags "-s -w" -o output/main code.byted.org/devgpt/kiwis/codeassist/cmd/evaluationfaas

.PHONY: build_mentor_cronjob build_mentor_http
build_mentor_cronjob:
	go build -ldflags "-s -w" -o output/main code.byted.org/devgpt/kiwis/mentor/cmd/faas_server
build_mentor_http:
	go build -ldflags "-s -w" -o output/main code.byted.org/devgpt/kiwis/mentor/cmd/http_server

.PHONY: build_search_server
build_search_server:
	go build -ldflags "-s -w" -o output/search_server code.byted.org/devgpt/kiwis/search/server/

.PHONY: build_llminfra_server
build_llminfra_server:
	go build -ldflags "-s -w" -o output/llminfra_server code.byted.org/devgpt/kiwis/llminfra/cmd/server/

.PHONY: build_devai
build_devai_knowledge:
	go build -ldflags "-s -w" -o output/devai_knowledgebase code.byted.org/devgpt/kiwis/devai/cmd/knowledge

.PHONY: build_flowoperator
build_flowoperator:
	go build -ldflags "-s -w" -o output/flowoperator code.byted.org/devgpt/kiwis/flowoperator/cmd/operator

build_devai_assistant:
	go build -ldflags "-s -w" -o output/devai_assistant code.byted.org/devgpt/kiwis/devai/cmd/assistant

build_devai_knowledgeview_dsyncer_faas:
	go build -ldflags "-s -w" -o output/main code.byted.org/devgpt/kiwis/devai/cmd/knowledgeview_dsyncer_faas

build_devgpt2devai_dsyncer_faas:
	go build -ldflags "-s -w" -o output/main code.byted.org/devgpt/kiwis/devai/cmd/devgpt2devai_dsyncer_faas

.PHONY: build_devai_datasource_faas
build_devai_datasource_faas:
	CGO_ENABLED=0 go build -ldflags "-s -w" -o output/main code.byted.org/devgpt/kiwis/devai/cmd/datasource_faas

.PHONY: build_devai_datasource_cronjob
build_devai_datasource_cronjob:
	go build -ldflags "-s -w" -o output/main code.byted.org/devgpt/kiwis/devai/cmd/cronjob/datasource_cronjob

.PHONY: build_devai_tos2hdfs_cronjob
build_devai_tos2hdfs_cronjob:
	go build -ldflags "-s -w" -o output/main code.byted.org/devgpt/kiwis/devai/cmd/cronjob/tos2hdfs_cronjob

.PHONY: build_bits_copilot_agent
build_bits_copilot_agent:
	go build -o output/bits_copilot_agent code.byted.org/devgpt/kiwis/bitscopilot/cmd/agent

.PHONY: build_agentsphere_apiserver
build_agentsphere_apiserver:
	go build -ldflags "-s -w" -o output/agentsphere_apiserver code.byted.org/devgpt/kiwis/agentsphere/cmd/apiserver

.PHONY: build_agentsphere_nextserver
build_agentsphere_nextserver:
	go build -ldflags "-s -w" -o output/agentsphere_nextserver code.byted.org/devgpt/kiwis/agentsphere/cmd/nextserver

.PHONY: build_imageproxy
build_imageproxy:
	go build -ldflags "-s -w" -o output/imageproxy code.byted.org/devgpt/kiwis/agentsphere/cmd/imageproxy

.PHONY: build_agentsphere_runtime
build_agentsphere_runtime:
	go build -o output/agentsphere_runtime code.byted.org/devgpt/kiwis/agentsphere/cmd/runtime

.PHONY: build_agentsphere_relay
build_agentsphere_relay:
	go build -ldflags "-s -w" -o output/agentsphere_relay code.byted.org/devgpt/kiwis/agentsphere/cmd/relay

.PHONY: build_codeassist_assistant
build_codeassist_assistant:
	go build -tags fornax -ldflags "-s -w -X google.golang.org/protobuf/reflect/protoregistry.conflictPolicy=ignore" -o output/codeassist_assistant code.byted.org/devgpt/kiwis/codeassist/cmd/assistant

.PHONY: build_codeassist_contexttask_consumer
build_codeassist_contexttask_consumer:
	go build -tags fornax -ldflags "-s -w -X google.golang.org/protobuf/reflect/protoregistry.conflictPolicy=ignore" -o output/main code.byted.org/devgpt/kiwis/codeassist/cmd/contexttaskconsumer

.PHONY: build_codeassist_sandbox_recycle
build_codeassist_sandbox_recycle:
	go build -tags fornax -ldflags "-s -w -X google.golang.org/protobuf/reflect/protoregistry.conflictPolicy=ignore" -o output/main code.byted.org/devgpt/kiwis/codeassist/cmd/sandboxrecycle

.PHONY: build_render_server
build_render_server:
	go build -tags "libtokenizer fornax" -ldflags "-extldflags '-L$(STATIC_LIB_DIR) -pthread' -s -w -X google.golang.org/protobuf/reflect/protoregistry.conflictPolicy=ignore" -o output/render_server code.byted.org/devgpt/kiwis/render/cmd/server

.PHONY: build_idecopilot_entitlement_consumer
build_idecopilot_entitlement_consumer:
	go build -o output/main code.byted.org/devgpt/kiwis/idecopilot/cmd/entitlementconsumer

# Build server binary for codeai service.
.PHONY: build_codeai_server
build_codeai_server:
	go build -ldflags "-s -w" -o output/codeai_server code.byted.org/devgpt/kiwis/codeai/cmd/server

# Format Go code.
.PHONY: lint
lint:
	@gofmt -w .
	@go run golang.org/x/tools/cmd/goimports -local $(LOCAL_MODULE) -w $$(find . -type f -name '*.go' -not -path "*_gen*" -not -path "*_po.go" -not -path "./kitex_gen/*")
	@go mod tidy
format_devai:
	cd devai && go run golang.org/x/tools/cmd/goimports -local $(LOCAL_MODULE) -w $$(find . -type f -name '*.go' -not -path "*_gen*" -not -path "*_po.go" -not -path "./kitex_gen/*" -not -path "*/po/*")
check_devai:
	go generate ./devai/cmd/...

.PHONY: style_test
style_test:

# Run the go unit tests.
.PHONY: test
test:
	go test ./...

.PHONY: gen_ide_idl
gen_ide_idl:
	@echo "generating Kitex service code for idecopilot idl..."
	go run code.byted.org/devgpt/kiwis/dev/tools/gen kitex  -frugal-pretouch=false \
 		-deep-copy-api=false \
 		-module code.byted.org/devgpt/kiwis \
 		-combine-service \
 		-gen-path api/idl/kitex_gen \
 		-I api/idl/idecopilot \
 		-type thrift \
 		api/idl/idecopilot/service.thrift
 .PHONY: gen_knowledgebase_idl
 gen_knowledgebase_idl:
	@echo "generating Kitex service code for knowledgebase idl..."
	go run code.byted.org/devgpt/kiwis/dev/tools/gen kitex  -frugal-pretouch=false \
  		-deep-copy-api=false \
  		-module code.byted.org/devgpt/kiwis \
  		-gen-path api/idl/kitex_gen \
  		-I api/idl/knowledgebase \
  		-type thrift \
  		-combine-service \
  		api/idl/knowledgebase/service.thrift

 .PHONY: gen_memory_idl
 gen_memory_idl:
	@echo "generating Kitex service code for memory idl..."
	go run code.byted.org/devgpt/kiwis/dev/tools/gen kitex  -frugal-pretouch=false \
  		-deep-copy-api=false \
  		-module code.byted.org/devgpt/kiwis \
  		-gen-path api/idl/kitex_gen \
  		-I api/idl/memory \
  		-type thrift \
  		-combine-service \
  		api/idl/memory/service.thrift


.PHONY: gen_copilot_idl
gen_copilot_idl:
	@echo "generating model code for copilot idl..."
	go run code.byted.org/devgpt/kiwis/dev/tools/gen hertztool model --snake_tag --idl api/idl/copilotstack/chat.thrift --model_dir api/idl/hertz_gen -t=template=slim
	go run code.byted.org/devgpt/kiwis/dev/tools/gen hertztool model --snake_tag --idl api/idl/copilotstack/app.thrift --model_dir api/idl/hertz_gen -t=template=slim
	go run code.byted.org/devgpt/kiwis/dev/tools/gen hertztool model --snake_tag --idl api/idl/copilotstack/intent.thrift --model_dir api/idl/hertz_gen -t=template=slim
	go run code.byted.org/devgpt/kiwis/dev/tools/gen hertztool model --snake_tag --idl api/idl/copilotstack/journal.thrift --model_dir api/idl/hertz_gen -t=template=slim
	# KiteX 生成 grpc 代码还不支持 -gen-path 来支持生成代码路径 😅
	# 需要先 go install code.byted.org/kite/kitex/tool/cmd/kitex@v1.13.2，他们最新版本工具也有 bug 😅
	cd api/idl && \
    		go run code.byted.org/devgpt/kiwis/dev/tools/gen kitex -frugal-pretouch=false -deep-copy-api=false -module code.byted.org/devgpt/kiwis -I copilotstack/thirdparty/processserver -type protobuf copilotstack/thirdparty/processserver/service.proto
	cd api/idl && \
        	go run code.byted.org/devgpt/kiwis/dev/tools/gen kitex -frugal-pretouch=false -deep-copy-api=false -module code.byted.org/devgpt/kiwis -I copilotstack/thirdparty/processserver -type protobuf copilotstack/thirdparty/processserver/ut_service.proto
	cd api/idl && \
            go run code.byted.org/devgpt/kiwis/dev/tools/gen kitex -frugal-pretouch=false -deep-copy-api=false -module code.byted.org/devgpt/kiwis -I copilotstack/thirdparty/coze/protobuf/copilot -type protobuf copilotstack/thirdparty/coze/protobuf/copilot/chat_engine_service.proto
#   TODO(caoyunxiang): 后续迁移到 submodule idl 仓库
#	cd api/idl && \
#        	go run code.byted.org/devgpt/kiwis/dev/tools/gen kitex -frugal-pretouch=false -deep-copy-api=false -module code.byted.org/devgpt/kiwis -I copilotstack/thirdparty/processserver -type protobuf copilotstack/thirdparty/processserver/service.proto
#	cd api/idl && \
#			go run code.byted.org/devgpt/kiwis/dev/tools/gen kitex -frugal-pretouch=false -deep-copy-api=false -module code.byted.org/devgpt/kiwis -I copilotstack/thirdparty/processserver -type protobuf copilotstack/thirdparty/processserver/ut_service.proto

.PHONY: gen_llm_idl
gen_llm_idl:
	@echo "generating model code for llm idl..."
	go run code.byted.org/devgpt/kiwis/dev/tools/gen hertztool model --snake_tag --idl api/idl/llmstack/auth.thrift --model_dir api/idl/hertz_gen -t=template=slim
	go run code.byted.org/devgpt/kiwis/dev/tools/gen hertztool model --snake_tag --idl api/idl/llmstack/llm.thrift --model_dir api/idl/hertz_gen -t=template=slim
	go run code.byted.org/devgpt/kiwis/dev/tools/gen hertztool model --snake_tag --idl api/idl/llmstack/context.thrift --model_dir api/idl/hertz_gen -t=template=slim
	go run code.byted.org/devgpt/kiwis/dev/tools/gen hertztool model --snake_tag --idl api/idl/llmstack/journal.thrift --model_dir api/idl/hertz_gen -t=template=slim

.PHONY: gen_llminfra_idl
gen_llminfra_idl:
	@echo "generating Kitex service code for llminfra idl..."
	go run code.byted.org/devgpt/kiwis/dev/tools/gen kitex  -frugal-pretouch=false \
 		-deep-copy-api=false \
 		-module code.byted.org/devgpt/kiwis \
 		-gen-path api/idl/kitex_gen \
 		-I api/idl/llminfra \
 		-type thrift \
 		api/idl/llminfra/service.thrift

.PHONY: gen_devai_idl
 gen_devai_idl:
	@echo "generating Kitex service code for devai idl..."
	go run code.byted.org/devgpt/kiwis/dev/tools/gen kitex  -frugal-pretouch=false \
  		-deep-copy-api=false \
  		-module code.byted.org/devgpt/kiwis \
  		-gen-path api/idl/kitex_gen \
  		-I api/idl/devai \
  		-type thrift \
  		-combine-service \
  		api/idl/devai/devai.thrift

.PHONY: gen_flow_operator_idl
 gen_flow_operator_idl:
	@echo "generating Kitex service code for flow operator idl..."
	go run code.byted.org/devgpt/kiwis/dev/tools/gen kitex  -frugal-pretouch=false \
  		-deep-copy-api=false \
  		-module code.byted.org/devgpt/kiwis \
  		-gen-path api/idl/kitex_gen \
  		-I api/idl/flowoperator \
  		-type thrift \
  		-combine-service \
  		api/idl/flowoperator/service.thrift

.PHONY: gen_agent_ckg_idl
gen_agent_ckg_idl:
	@echo "generating codekg rpc code for IDE agent experiment..."
	cd api/idl && \
		go run code.byted.org/devgpt/kiwis/dev/tools/gen kitex -frugal-pretouch=false -deep-copy-api=false -module code.byted.org/devgpt/kiwis -type protobuf idecopilot/thirdparty/codekg/protocol/codekg.proto

.PHONY: gen_bytedagi_ultraman_idl
gen_bytedagi_ultraman_idl:
	@echo "generating bytedagi llm ultraman rpc code..."
	cd api/idl && \
		go run code.byted.org/devgpt/kiwis/dev/tools/gen kitex -frugal-pretouch=false -deep-copy-api=false -module code.byted.org/devgpt/kiwis -type protobuf idecopilot/thirdparty/bytedagi/inference.proto

.PHONY: gen_bits_agent_idl
gen_bits_agent_idl:
	@echo "generating Kitex service code bits agent idl..."
	go run code.byted.org/devgpt/kiwis/dev/tools/gen kitex  -frugal-pretouch=false \
 		-deep-copy-api=false \
 		-module code.byted.org/devgpt/kiwis \
 		-combine-service \
 		-gen-path api/idl/kitex_gen \
 		-I api/idl/bitscopilot/agent \
 		-type thrift \
 		api/idl/bitscopilot/agent/service.thrift

.PHONY: gen_codeassist_idl
 gen_codeassist_idl:
	@echo "generating Kitex service code for code assist idl..."
	go run code.byted.org/devgpt/kiwis/dev/tools/gen kitex -disable-self-update -frugal-pretouch=false \
  		-deep-copy-api=false \
  		-module code.byted.org/devgpt/kiwis \
  		-gen-path api/idl/kitex_gen \
  		-I api/idl/codeassist \
  		-type thrift \
  		-tpl multiple_services \
  		api/idl/codeassist/service.thrift

.PHONY: gen_render_idl
gen_render_idl:
	@echo "generating Kitex service code for render idl..."
	go run code.byted.org/devgpt/kiwis/dev/tools/gen kitex  -frugal-pretouch=false \
 		-deep-copy-api=false \
 		-module code.byted.org/devgpt/kiwis \
 		-gen-path api/idl/kitex_gen \
 		-I api/idl/render \
 		-type thrift \
 		api/idl/render/service.thrift

.PHONY: gen_codeai_idl
gen_codeai_idl:
	@echo "generating model code for codeai idl..."
	go run code.byted.org/devgpt/kiwis/dev/tools/gen hertztool model --snake_tag --idl api/idl/codeai/chat.thrift --model_dir api/idl/hertz_gen -t=template=slim
