package handler

import (
	// 标准库
	"bytes"
	"context"
	"crypto/sha256"
	"encoding/json"
	"fmt"
	"github.com/cenkalti/backoff/v4"
	"regexp"
	"runtime/debug"
	"strings"
	"time"

	// 外部库
	"github.com/go-enry/go-enry/v2"
	"github.com/pkg/errors"
	"gorm.io/gorm"

	// 内部库
	codebasesdk "code.byted.org/codebase/sdk"
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/gopkg/logs/v2/log"

	entity "code.byted.org/devgpt/kiwis/api/idl/kitex_gen/memory"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/memory/dal"
	"code.byted.org/devgpt/kiwis/memory/service/chat"
	"code.byted.org/devgpt/kiwis/memory/service/storage"
	"code.byted.org/devgpt/kiwis/memory/utils"
	"code.byted.org/devgpt/kiwis/port/codebase"
	"code.byted.org/devgpt/kiwis/port/redis"
)

// MQHandler 这里需要使用dataset service里的方法传入，不使用impl的方式单独搞一个code repo updator了
type MQHandler struct {
	handler        *MemoryHandler
	dal            *dal.DAO
	codebaseCli    codebase.Client
	redisCli       redis.Client
	tccConfig      *config.MemoryTCCConfig
	storageService *storage.Service
	chatService    *chat.Service
}

type Review struct {
	File     string `json:"file"`
	Summary  string `json:"summary"`
	Review   string `json:"review"`
	Category string `json:"category"`
	Snippet  string `json:"snippet"`
	Severity string `json:"severity"`
	Example  string `json:"example"`
}
type ReviewResponse struct {
	Response []Review `json:"response"`
}

func NewMQHandler(dal *dal.DAO, handler *MemoryHandler, codebaseCli codebase.Client, redisCli redis.Client,
	tccConfig *config.MemoryTCCConfig, storageService *storage.Service, chatService *chat.Service) *MQHandler {
	return &MQHandler{dal: dal, handler: handler, codebaseCli: codebaseCli, redisCli: redisCli,
		tccConfig: tccConfig, storageService: storageService, chatService: chatService}
}

const (
	firstNBytes = 8000
	firstNLines = 10

	// commit去重的过期时间
	commitTTL = 4 * 24 * time.Hour
	// 容忍的最长Commit时间
	maxCommitTime        = 3 * 24 * time.Hour
	llmTruncationCharNum = 128000
	mentorModelDsv3      = "ep-20250703155947-txsfg"
)

// HandleCodebaseEvent handles mq event from codebase when new commits is pushed.
func (h *MQHandler) HandleCodebaseEvent(ctx context.Context, message []byte) error {
	defer func() {
		// There are many pointer in MQEvent, it's annoying to check nil every time, and it's easy to leak
		e := recover()
		if e == nil {
			return
		}
		log.V1.CtxError(ctx, "[HandleCodebaseEvent]panic in HandleCodebaseEvent: %+v, the payload: %s,stacktrace: %s", e, string(message),
			string(debug.Stack()))
	}()
	event := &codebasesdk.MQEvent{}
	err := json.Unmarshal(message, event)
	if err != nil {
		log.V1.CtxError(ctx, "[HandleCodebaseEvent]failed to unmarshal mq event", "err", err)
		return err
	}
	// 这里不确定为什么无法通过MR信息获取diff change，直接使用push的信息替代处理
	if event.Type != codebasesdk.MQEventTypePush {
		return nil
	}
	// 在mysql中判断是否该用户存在
	if event.Push.Sender == nil || event.Push.Sender.Email == nil {
		logs.CtxWarn(ctx, "sender is nil, event is %+v", event)
		return nil
	}
	exist, err := h.storageService.GetMemoryUserByEmail(ctx, *event.Push.Sender.Email)

	if err != nil {
		return errors.WithMessage(err, "[HandleCodebaseEvent]failed to get memory user by email")
	}
	if !exist {
		return nil
	}
	ctx = logs.CtxAddKVs(ctx, "type", event.Type, "action", event.Action)
	if !strings.HasPrefix(event.Push.Ref, codebase.RefsHeadPrefix) {
		log.V1.Info("[HandleCodebaseEvent]it's not a branch push event, ignore it, ref: %s", event.Push.Ref)
		return nil
	}
	ref := strings.TrimPrefix(event.Push.Ref, codebase.RefsHeadPrefix)
	repoName := *event.Push.Repo.Name
	gitURL := *event.Push.Repo.GitURL
	pushAuthorEmail := *event.Push.Sender.Email
	ctx = logs.CtxAddKVs(ctx, "repo_name", repoName, "ref", ref, "gitUrl", gitURL)

	globalMatcher := utils.NewMatcher([]string{
		`// Code generated by .* DO NOT EDIT\.`,
		`// Code generated by .*`,
		`DO NOT EDIT\.$`,
		`// THIS IS AN AUTOGENERATED FILE\. DO NOT EDIT THIS FILE DIRECTLY\.`,
		`// Copyright \(c\) Microsoft Corporation\. All rights reserved\. Licensed under the MIT license\.`,
		`The contents of this file are subject to the Mozilla Public License Version`,
		`Free Software Foundation, Inc`,
		`// Generated by the protocol buffer compiler\.  DO NOT EDIT!`,
		`// Type definitions for React`,
	}, []string{
		"DO NOT EDIT",
		"Generated by",
		"Free Software Foundation",
		"found in the LICENSE file",
		"Code generated by",
		"# Autogenerated by Thrift Compiler",
	})

	// before sha只存在db中一次，如果存在commit的parent，则后面使用commit parent，而不用before sha了
	beforeSHA := event.Push.BeforeSHA

	for _, v := range event.Push.Commits {
		// 如果存在相同commit，则忽略skip这次处理
		ret, err := h.dal.GetMemoryCommitByEventID(ctx, beforeSHA, *v.SHA)
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.WithMessage(err, "[HandleCodebaseEvent]failed to get memory commit by event id")
		}
		if ret != nil && ret.ID > 0 {
			logs.CtxInfo(ctx, "[HandleCodebaseEvent]commit already exists, skip it")
			beforeSHA = *v.SHA
			continue
		}
		// 单独获取commit info
		info, err := h.codebaseCli.GetCommitInfo(ctx, repoName, *v.SHA)
		if err != nil {
			return errors.WithMessage(err, "[HandleCodebaseEvent]failed to get commit info")
		}
		if info == nil || info.Message == nil || info.Author == nil || info.Author.Email == nil || info.Committer == nil || info.Committer.Date == nil {
			logs.CtxWarn(ctx, "[HandleCodebaseEvent]commit info is nil, skip it")
			beforeSHA = *v.SHA
			continue
		}

		if *info.Author.Email != pushAuthorEmail {
			logs.CtxWarn(ctx, "[HandleCodebaseEvent]push author(%+v) != commit author email(%+v)", *info.Author.Email, pushAuthorEmail)
			beforeSHA = *v.SHA
			continue
		}

		// 将time.now和*info.Committer.Date相减，如果时间差大于x天，则continue
		if time.Since(*info.Committer.Date) > maxCommitTime {
			logs.CtxInfo(ctx, "[HandleCodebaseEvent]commit is too old, skip it")
			beforeSHA = *v.SHA
			continue
		}

		// 排除merge...branch...into的commit
		pattern := `(?i)merge\s+.*branch\s+.*\s+into\s+.*`
		re := regexp.MustCompile(pattern)
		if re.MatchString(*info.Message) {
			logs.CtxInfo(ctx, "[HandleCodebaseEvent]merge branch into current or MR Push event, skip it")
			beforeSHA = *v.SHA
			continue
		}

		// 排除See merge request的commit
		if strings.Contains(*info.Message, "See merge request:") {
			logs.CtxInfo(ctx, "[HandleCodebaseEvent]rebase in MR Push event, skip it")
			beforeSHA = *v.SHA
			continue
		}

		// 如果存在parent，本次循环中使用parent作为before sha
		if info.Parents != nil && len(info.Parents) > 0 && info.Parents[0] != nil {
			beforeSHA = info.Parents[0].SHA
		}

		// 如果是某仓库第一次push，则beforeSHA为4b825dc642cb6eb9a060e54bf8d69288fbee4904
		if beforeSHA == "0000000000000000000000000000000000000000" {
			beforeSHA = "4b825dc642cb6eb9a060e54bf8d69288fbee4904"
		}

		// 将commit信息存到Mysql里
		_, err = h.dal.CreateMemoryCommit(ctx, &dal.CreateMemoryCommitOption{
			OccurTime:     *info.Committer.Date,
			BeforeEventID: beforeSHA,
			AfterEventID:  *v.SHA,
			SourceName:    repoName,
			UserEmail:     *info.Author.Email,
			CommitInfo:    *info.Message,
		})
		if err != nil {
			logs.CtxError(ctx, "[HandleCodebaseEvent]failed to create memory commit,err is %+v", err)
			continue
		}

		// 获取commit中的每一个文件，并传到LLM中进行分析
		diffs, err := h.codebaseCli.GetDiffFileList(ctx, repoName, beforeSHA, *v.SHA, false)
		if err != nil {
			logs.CtxError(ctx, "[HandleCodebaseEvent]failed to get diff file list, err is %+v", err)
			continue
		}

		diffInCommitLevel := fmt.Sprintf("## commit message: %s", *info.Message)

		for _, diff := range diffs {
			if utils.IsAutoGenerated(diff.Path) {
				logs.CtxInfo(ctx, "file is auto generated, file path is %+v", diff.Path)
				continue
			}

			// 如果是go文件，还需获取完整文件进行判断
			if strings.HasSuffix(diff.Path, ".go") {
				fileContent, err := h.codebaseCli.GetBlobByPath(ctx, repoName, *v.SHA, diff.Path)
				if err != nil {
					return errors.WithMessage(err, "[HandleCodebaseEvent]failed to get file content")
				}
				if len(fileContent.ContentRaw) == 0 || fileContent.ContentRaw == nil {
					logs.CtxWarn(ctx, "[HandleCodebaseEvent]file content is nil, skip it")
					continue
				}

				// check first xxx bytes of the file
				toBeMatchedLen := firstNBytes
				if len(fileContent.ContentRaw) < toBeMatchedLen {
					toBeMatchedLen = len(fileContent.ContentRaw)
				}
				toBeMatched := fileContent.ContentRaw[:toBeMatchedLen]
				if globalMatcher.DoesMatchFileHead(string(toBeMatched)) {
					logs.CtxInfo(ctx, "file is auto generated, file path is %+v", diff.Path)
					continue
				}

				// check first xxx lines of the file
				lines := bytes.Split(toBeMatched, []byte("\n"))
				if len(lines) > firstNLines {
					lines = lines[:firstNLines]
				}
				for _, line := range lines {
					if globalMatcher.DoesMatchFileLine(string(line)) {
						logs.CtxInfo(ctx, "file is auto generated, file path is %+v", diff.Path)
						continue
					}
				}
			}

			fileDiff, err := h.codebaseCli.GetDiffFile(ctx, repoName, codebase.GetDiffFileOption{
				FromSHA:      beforeSHA,
				ToSHA:        *v.SHA,
				File:         diff.Path,
				Format:       codebase.DiffFileFormatPatch,
				Context:      "3",
				UseMergeBase: false,
			})

			if err != nil {
				logs.CtxError(ctx, "[HandleCodebaseEvent]failed to get diff file, err is %+v", err)
				continue
			}
			if fileDiff.ContentPatch == nil || len(*fileDiff.ContentPatch) == 0 {
				logs.CtxWarn(ctx, "[HandleCodebaseEvent]content patch is nil, skip it")
				continue
			}

			// 这里查下相同内容是否已经存在，如果存在则跳过，如果不存在则存redis中
			hash, err := h.hashContent(*fileDiff.ContentPatch)
			if err != nil {
				logs.CtxWarn(ctx, "hash content fail, err is %+v", err)
			} else {
				_, err := h.redisCli.Get(ctx, hash)
				if err != nil {
					if redis.IsNil(err) {
						// 写数据
						logs.CtxInfo(ctx, "content not exists, write it to redis, hash is %+v", hash)
						h.redisCli.Set(ctx, hash, "1", commitTTL)
					} else {
						logs.CtxWarn(ctx, "get redis fail, err is %+v", err)
					}
				} else {
					// if exist then skip
					logs.CtxInfo(ctx, "content already exists, skip it, hash is %+v", hash)
					continue
				}
			}

			lang := enry.GetLanguage(diff.Path, []byte(*fileDiff.ContentPatch))

			contentPatch := *fileDiff.ContentPatch

			if len(contentPatch) > llmTruncationCharNum {
				contentPatch = contentPatch[:llmTruncationCharNum]
			}

			logs.CtxInfo(ctx, "[HandleCodebaseEvent] get file diff, file path is %+v, content len is %+v", diff.Path, len(contentPatch))

			var diffSummary string
			err = backoff.Retry(func() error {
				diffSummary, err = h.chatService.RequestChat(ctx, h.chatService.GetSummaryMessage(contentPatch, lang),
					mentorModelDsv3, 0, 600*time.Second, false)
				return err
			}, backoff.WithMaxRetries(backoff.NewExponentialBackOff(), 3))

			if err != nil {
				logs.CtxError(ctx, "[HandleCodebaseEvent]failed to request chat for diff summary, err is %+v", err)
				continue
			}

			if err = h.storageService.IndexCommitInfo(ctx, &storage.IndexMRInfoOption{
				IndexInfo:            diffSummary,
				EmbeddingInfo:        diffSummary,
				UserIDs:              []string{*info.Author.Email},
				MemoryType:           entity.MemoryType_Fact,
				OccurTime:            info.Committer.Date.Unix(),
				MemoryEntity:         entity.MemoryEntity_Code,
				Weight:               1,
				SourceName:           repoName,
				FileName:             diff.Path,
				FromSha:              beforeSHA,
				ToSha:                *v.SHA,
				CommitID:             info.GitCommitMeta.SHA,
				CommitMessage:        *info.Message,
				Describe:             *info.Message,
				Language:             lang,
				IgnoreEmbeddingError: false,
			}); err != nil {
				logs.CtxError(ctx, "[HandleCodebaseEvent]failed to index MR info, err is %+v", err)
				continue
			}
			diffInCommitLevel += fmt.Sprintf("### file name: %s\n### diff: \n```%s\n%s\n```\n\n", diff.Path, lang, contentPatch)
		}

		if len(diffInCommitLevel) > llmTruncationCharNum {
			diffInCommitLevel = diffInCommitLevel[:llmTruncationCharNum]
		}
		// 如果上述diff部分未能获取file content,则直接忽略commit review
		if !strings.Contains(diffInCommitLevel, "### file name:") {
			logs.CtxInfo(ctx, "[HandleCodebaseEvent]diffInCommitLevel is empty")
			continue
		}

		logs.CtxInfo(ctx, "[HandleCodebaseEvent] diffInCommitLevel len is %+v", len(diffInCommitLevel))

		var diffReview string
		err = backoff.Retry(func() error {
			diffReview, err = h.chatService.RequestChat(ctx, h.chatService.GetReviewMessage(diffInCommitLevel),
				mentorModelDsv3, 0, 600*time.Second, true)
			return err
		}, backoff.WithMaxRetries(backoff.NewExponentialBackOff(), 3))

		if err != nil {
			logs.CtxError(ctx, "[HandleCodebaseEvent]failed to request chat for diff review, err is %+v", err)
			continue
		}

		// 将review结果存到mysql和vector db中
		reviews, err := h.extractJSON(diffReview)
		if err != nil {
			logs.CtxError(ctx, "[HandleCodebaseEvent]failed to extract JSON, err is %+v, diffReview is %+v", err, diffReview)
			continue
		}
		// 对reviews进行分批存储处理
		for _, review := range reviews {
			if review.Review == "" {
				logs.CtxError(ctx, "[HandleCodebaseEvent]extract JSON review is empty")
				continue
			}
			//logs.CtxInfo(ctx, "%+v", review)
			jsonBytes, err := json.Marshal(review)

			if err != nil {
				logs.CtxWarn(ctx, "json marshal fail, err is %+v", err)
				continue
			}
			fileName := review.File
			if err = h.storageService.IndexCommitInfo(ctx, &storage.IndexMRInfoOption{
				IndexInfo:            string(jsonBytes),
				EmbeddingInfo:        review.Review,
				UserIDs:              []string{*info.Author.Email},
				MemoryType:           entity.MemoryType_Experience,
				OccurTime:            info.Committer.Date.Unix(),
				MemoryEntity:         entity.MemoryEntity_Code,
				Weight:               1,
				SourceName:           repoName,
				FileName:             fileName,
				Ref:                  ref,
				FromSha:              beforeSHA,
				ToSha:                *v.SHA,
				CommitID:             info.GitCommitMeta.SHA,
				CommitMessage:        *info.Message,
				Describe:             *info.Message,
				Language:             "",
				IgnoreEmbeddingError: false,
			}); err != nil {
				logs.CtxError(ctx, "[HandleCodebaseEvent]failed to index MR info, err is %+v", err)
				continue
			}
		}
		beforeSHA = *v.SHA
	}

	return nil

}

// extractJSON 使用正则表达式从文本中提取JSON内容，并解析为Review数组
func (h *MQHandler) extractJSON(text string) ([]Review, error) {
	// 使用正则表达式匹配JSON部分
	re := regexp.MustCompile(`(?s)\{.*\}|\[.*\]`)
	matches := re.FindStringSubmatch(text)
	if len(matches) == 0 {
		return nil, fmt.Errorf("no JSON found in text")
	}
	jsonStr := matches[0]

	var reviews []Review
	if jsonStr[0] == '[' {
		// 解析为Review数组
		err := json.Unmarshal([]byte(jsonStr), &reviews)
		if err != nil {
			return nil, fmt.Errorf("failed to unmarshal JSON array: %v", err)
		}
	} else if strings.Contains(jsonStr, "response") {
		// 解析为ReviewResponse结构
		var reviewResp ReviewResponse
		err := json.Unmarshal([]byte(jsonStr), &reviewResp)
		if err != nil {

			return nil, fmt.Errorf("failed to unmarshal JSON array: %v", err)
		}
		for _, v := range reviewResp.Response {
			reviews = append(reviews, v)
		}

	} else {
		// 解析为单个Review对象
		var review Review
		err := json.Unmarshal([]byte(jsonStr), &review)
		if err != nil {
			return nil, fmt.Errorf("failed to unmarshal JSON object: %v", err)
		}
		reviews = append(reviews, review)
	}

	return reviews, nil
}

func (h *MQHandler) hashContent(s string) (string, error) {
	buf := &bytes.Buffer{}
	if _, err := buf.Write([]byte(s)); err != nil {
		return "", err
	}
	return fmt.Sprintf("0%x", sha256.Sum256(buf.Bytes())), nil
}
