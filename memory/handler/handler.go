package handler

import (
	// 标准库
	"context"
	"encoding/json"
	"fmt"
	"github.com/samber/lo"
	"time"
	// 外部库
	"github.com/cenkalti/backoff/v4"
	"github.com/pkg/errors"
	// 内部库
	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/memory"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/memory/dal"
	"code.byted.org/devgpt/kiwis/memory/service/chat"
	"code.byted.org/devgpt/kiwis/memory/service/storage"
	"code.byted.org/gopkg/logs/v2"
)

const (
	recentReflectionNum = 2
	modelName           = "ep-20250703155947-txsfg"
)

type MemoryHandler struct {
	dal            *dal.DAO
	storageService *storage.Service
	tccConfig      *config.MemoryTCCConfig
	chatService    *chat.Service
}

func NewMemoryHandler(dal *dal.DAO, storageService *storage.Service,
	tccConfig *config.MemoryTCCConfig, chatService *chat.Service) *MemoryHandler {
	handler := &MemoryHandler{
		dal:            dal,
		storageService: storageService,
		tccConfig:      tccConfig,
		chatService:    chatService,
	}
	return handler
}

func (h *MemoryHandler) RetrieveMemory(ctx context.Context, req *memory.RetrieveMemoryRequest) (r *memory.RetrieveMemoryResponse, err error) {
	// real logic
	// 先根据输入的query，将其转换为多个向量
	// 将多个向量求均值，然后根据均值向量 + dsl去viking中查询对应内容
	// 查询到内容（vector + segment id）后，再从byte doc中通过id拿到具体内容并进行拼凑

	// user profile从再handler里面拿？
	rOption := &storage.RetrievalOption{}
	if req.Query != nil {
		rOption.Query = req.Query
	}
	if req.UserEmail != nil {
		rOption.UserEmail = *req.UserEmail
	}
	if req.StartTime != nil {
		rOption.StartTime = *req.StartTime
	}
	if req.EndTime != nil {
		rOption.EndTime = *req.EndTime
	}
	if req.MemoryType != nil {
		rOption.MemoryType = *req.MemoryType
	}
	if req.MemoryEntity != nil {
		rOption.MemoryEntity = *req.MemoryEntity
	}
	if req.TopK != nil {
		rOption.TopK = *req.TopK
	} else {
		rOption.TopK = 10
	}
	if req.Tags != nil {
		rOption.Tags = req.Tags
	}
	ret, err := h.storageService.Retrieval(ctx, rOption)
	if err != nil {
		return nil, err
	}

	// 将结构进行转换
	m := make([]*memory.RetrievalMemory, 0)
	for _, v := range ret {
		extra := storage.ExtraInfo{}
		err = json.Unmarshal([]byte(v.Segment.Extra), &extra)
		if err != nil {
			logs.CtxError(ctx, "[RetrieveMemory]failed to unmarshal extra", "err", err, "extra", v.Segment.Extra)
			return nil, errors.WithMessage(err, "[RetrieveMemory]failed to unmarshal extra")
		}

		switch *req.MemoryType {
		case memory.MemoryType_Experience:
			reviewInfo := memory.CodeReviewInfo{}
			if err = json.Unmarshal([]byte(extra.IndexInfo), &reviewInfo); err != nil {
				logs.CtxError(ctx, "[RetrieveMemory]failed to unmarshal review info", "err", err, "indexInfo", extra.IndexInfo)
				return nil, errors.WithMessage(err, "[RetrieveMemory]failed to unmarshal review info")
			}

			// list == review == Fact
			reviewExtra := &memory.CodeReviewExtra{
				UserEmail: extra.UserEmail,
				Language:  extra.Language,
				// same as review in struct
				Feedback: reviewInfo.Review,
				// same as example in struct
				Suggestion:     reviewInfo.Snippet,
				RepoName:       extra.SourceName,
				CommitID:       extra.ToSha,
				FileName:       extra.FileName,
				Ref:            extra.Ref,
				OccurTime:      extra.OccurTime,
				CodeReviewInfo: &reviewInfo,
			}
			reviewStr, err := json.Marshal(reviewExtra)
			if err != nil {
				return nil, err
			}
			m = append(m, &memory.RetrievalMemory{
				Segment: &memory.Segment{
					Type: memory.MemoryType_Experience,
					ID:   v.Segment.ID,
					// same as review in struct
					Content: reviewInfo.Review,
					Extra:   string(reviewStr),
					Vector:  v.Segment.Vector,
				},
				Score: v.Score,
			})

		case memory.MemoryType_Fact:
			// summary == diff == Experience
			codeDiffExtra := &memory.CodeFileDiffExtra{
				UserEmail:      extra.UserEmail,
				RepoName:       extra.SourceName,
				CommitID:       extra.ToSha,
				CommitMessage:  extra.CommitMessage,
				MergeRequestID: "",
				FileName:       extra.FileName,
				Ref:            extra.Ref,
				OccurTime:      extra.OccurTime,
			}

			diffStr, err := json.Marshal(codeDiffExtra)
			if err != nil {
				return nil, err
			}

			m = append(m, &memory.RetrievalMemory{
				Segment: &memory.Segment{
					Type:    memory.MemoryType_Fact,
					ID:      v.Segment.ID,
					Content: extra.IndexInfo,
					Extra:   string(diffStr),
					Vector:  v.Segment.Vector,
				},
				Score: v.Score,
			})
		}
	}

	// todo dal.getUserProfile
	profile := ""

	return &memory.RetrieveMemoryResponse{
		RetrievalMemory: m,
		UserProfile:     &profile,
	}, nil
}

func (h *MemoryHandler) IndexGraphMemory(ctx context.Context, req *memory.IndexGraphMemoryRequest) (r *memory.IndexGraphMemoryResponse, err error) {
	if req.GraphMemorySource == memory.GraphMemorySource_Unknown {
		logs.CtxError(ctx, "[IndexGraphMemory]unknown graph memory source")
		return &memory.IndexGraphMemoryResponse{Success: false}, errors.New("[IndexGraphMemory]unknown graph memory source")
	}

	// 1. 搜索entity，同时将entity以isNewEntity(key name, value id)和entity desc(key name, value desc)的形式存入map进行返回
	entityExists, _, err := h.storageService.GetGraphMemoryEntities(ctx, req.MemoryEntityInfo)
	if err != nil {
		return &memory.IndexGraphMemoryResponse{Success: false}, err
	}
	// todo 调用LLM，总结Entity的desc相关信息

	var updateEntities, insertEntities []*memory.MemoryEntityInfo
	for _, v := range req.MemoryEntityInfo {
		if entityID, ok := entityExists[v.EntityName]; ok && entityID > 0 {
			updateEntities = append(updateEntities, v)
		} else {
			insertEntities = append(insertEntities, v)
		}
	}

	allEntities, entityTypes, err := h.storageService.IndexGraphMemoryEntities(ctx, updateEntities, insertEntities, entityExists)
	if err != nil {
		return &memory.IndexGraphMemoryResponse{Success: false}, err
	}

	// 使用allEntities填充relation相关信息
	for k, v := range req.MemoryRelation {
		req.MemoryRelation[k].FromEntityID = allEntities[v.FromEntityName]
		req.MemoryRelation[k].ToEntityID = allEntities[v.ToEntityName]
	}

	// 搜索relation表，查看是否存在相同type/from entity id/to entity id的relation
	relationExists, _, err := h.storageService.GetGraphMemoryRelations(ctx, req.MemoryRelation)
	if err != nil {
		return &memory.IndexGraphMemoryResponse{Success: false}, err
	}
	// todo 调用LLM，总结Relation的desc相关信息

	// 2. 当relation在表中不存在，则创建relation，否则更新relation
	var updateRelations, insertRelations []*memory.MemoryRelation
	for _, v := range req.MemoryRelation {
		if relationID, ok := relationExists[fmt.Sprintf("%d_%d_%s", v.FromEntityID, v.ToEntityID, v.RelationType)]; ok && relationID > 0 {
			updateRelations = append(updateRelations, v)
		} else {
			insertRelations = append(insertRelations, v)
		}
	}

	err = h.storageService.IndexGraphMemoryRelations(ctx, updateRelations, insertRelations, relationExists, entityTypes)
	if err != nil {
		return &memory.IndexGraphMemoryResponse{Success: false}, err
	}

	// 3. 存 activity，假设此处act不需要使用LLM进行总结
	// 因activity中的entity ID是从entity表中获取的，所以需要先存entity表，后处理activity
	for k, v := range req.MemoryActivity {
		req.MemoryActivity[k].FromEntityID = allEntities[v.FromEntityName]
		req.MemoryActivity[k].ToEntityID = allEntities[v.ToEntityName]
	}
	err = h.storageService.IndexGraphMemoryActivities(ctx, req.MemoryActivity)
	if err != nil {
		return &memory.IndexGraphMemoryResponse{Success: false}, err
	}

	// 4. 调用LLM，获取reflection的总结，并存mysql + viking
	for k, v := range req.MemoryReflection {
		// 请求成功率较低，进行3次重试
		var ret string
		err = backoff.Retry(func() error {
			ret, err = h.chatService.RequestChat(ctx, h.chatService.GetEventBridgeMessage(v.EventInfo, req.GraphMemorySource),
				modelName, 0, 600*time.Second, false)
			return err
		}, backoff.WithMaxRetries(backoff.NewExponentialBackOff(), 3))

		if err != nil {
			logs.CtxError(ctx, "[IndexGraphMemory]failed to get review message", "err", err)
		}
		req.MemoryReflection[k].Description = ret
		req.MemoryReflection[k].FromEntityID = allEntities[v.FromEntityName]
		req.MemoryReflection[k].ToEntityID = allEntities[v.ToEntityName]
	}

	err = h.storageService.IndexGraphMemoryReflections(ctx, req.MemoryReflection)
	if err != nil {
		return &memory.IndexGraphMemoryResponse{Success: false}, err
	}

	return &memory.IndexGraphMemoryResponse{Success: true}, nil
}

func (h *MemoryHandler) IndexDialogueGraphMemory(ctx context.Context, req *memory.IndexDialogueGraphMemoryRequest) (r *memory.IndexDialogueGraphMemoryResponse, err error) {
	return &memory.IndexDialogueGraphMemoryResponse{}, nil
}

func (h *MemoryHandler) RetrieveGraphMemory(ctx context.Context, req *memory.RetrieveGraphMemoryRequest) (r *memory.RetrieveGraphMemoryResponse, err error) {
	ret := &memory.RetrieveGraphMemoryResponse{}
	entityIDs := make([]int64, 0)                              // 所有entity的id集合，用户从mysql中查询entity的信息
	graphEntityIDs := make([]int64, 0)                         // 用于在图中查询的entity集合，包括from和to
	graphEntityRelations := make(map[string]map[int64][]int64) // types: from entity id -> []slice to entity id
	fromEntityIDs := make([]int64, 0)                          // 所有from entity的id集合，用户从mysql中查询relation的信息
	toEntityIDs := make([]int64, 0)                            // 所有to entity的id集合，用户从mysql中查询relation的信息
	containsRelationTypes := make(map[string]bool)             // 所有relation type的集合，用户从mysql中查询relation的信息

	entityMap := make(map[int64]*dal.MemoryEntityPO)      // entity id -> entity info
	relationMap := make(map[string]*dal.MemoryRelationPO) // type_fromid_toid -> relation info

	// 1. 从viking中获取所有和query相关的信息
	graphDSL, err := h.storageService.RetrievalGraphMemoryFromViking(ctx, req.Query)
	if err != nil {
		logs.CtxError(ctx, "[RetrieveGraphMemory]failed to get dsl from viking", "err", err)
		return ret, err
	}

	relationTypes, err := h.dal.GetMemoryRelationTypes(ctx)
	if err != nil {
		logs.CtxError(ctx, "[RetrieveGraphMemory]failed to get relation types", "err", err)
		return ret, err
	}

	// 2. 将graphDSL中的信息分别存储，entity记录ID，relation记录ID和from/to entity id
	for _, v := range graphDSL {
		switch v.GraphMemoryType {
		case memory.GraphMemoryType_Entity:
			entityIDs = append(entityIDs, v.MysqlID)
			graphEntityIDs = append(graphEntityIDs, v.MysqlID)
		case memory.GraphMemoryType_Relation, memory.GraphMemoryType_Reflection:
			entityIDs = append(entityIDs, v.FromEntityID, v.ToEntityID)
			if _, ok := graphEntityRelations[v.RelationType]; !ok {
				graphEntityRelations[v.RelationType] = make(map[int64][]int64)
			}
			graphEntityRelations[v.RelationType][v.FromEntityID] = append(graphEntityRelations[v.RelationType][v.FromEntityID], v.ToEntityID)

			// 拼凑后续需要搜relation表的信息
			if _, ok := containsRelationTypes[v.RelationType]; !ok {
				containsRelationTypes[v.RelationType] = true
			}
			fromEntityIDs = append(fromEntityIDs, v.FromEntityID)
			toEntityIDs = append(toEntityIDs, v.ToEntityID)
		}
	}

	// 4. 获取ByteGraph中，所有以graphEntities为起点的relation，获取graphEntityRelations的map结构
	// types: from entity id -> []slice to entity id
	tempGraphEntityRelations, err := h.storageService.RetrievalGraphEntitiesFromByteGraph(ctx, graphEntityIDs, relationTypes)
	if err != nil {
		logs.CtxError(ctx, "[RetrieveGraphMemory]failed to get entity relations from byte graph", "err", err)
		return ret, err
	}
	// 将ByteGraph中的信息合并到graphEntityRelations中，同时将需要搜索的to entity id写入到entities中
	for tempType, tempEntityRelations := range tempGraphEntityRelations {
		if _, ok := graphEntityRelations[tempType]; !ok {
			graphEntityRelations[tempType] = tempEntityRelations
			for tempFromEntityID, tempToEntityIDs := range tempEntityRelations {
				entityIDs = append(entityIDs, tempFromEntityID)
				entityIDs = append(entityIDs, tempToEntityIDs...)
				fromEntityIDs = append(fromEntityIDs, tempFromEntityID)
				toEntityIDs = append(toEntityIDs, tempToEntityIDs...)
			}
		} else {
			for tempFromEntityID, tempToEntityIDs := range tempEntityRelations {
				graphEntityRelations[tempType][tempFromEntityID] = append(graphEntityRelations[tempType][tempFromEntityID], tempToEntityIDs...)
				entityIDs = append(entityIDs, tempFromEntityID)
				entityIDs = append(entityIDs, tempToEntityIDs...)
				fromEntityIDs = append(fromEntityIDs, tempFromEntityID)
				toEntityIDs = append(toEntityIDs, tempToEntityIDs...)
			}
		}
	}

	// 5. 搜索所有相关的entity slice
	allEntities, err := h.dal.GetMemoryEntitiesByIDs(ctx, lo.Uniq(entityIDs))
	if err != nil {
		logs.CtxError(ctx, "[RetrieveGraphMemory]failed to get entities", "err", err)
		return ret, err
	}
	// 构建map[entityID]entity的slice
	for _, v := range allEntities {
		entityMap[v.ID] = v
	}

	// 6. 搜索所有相关的relation slice（搜笛卡尔积后进行筛选使用）
	allRelations, err := h.dal.GetMemoryRelationsByTypeAndFromToIDs(ctx, containsRelationTypes, lo.Uniq(fromEntityIDs), lo.Uniq(toEntityIDs))
	if err != nil {
		logs.CtxError(ctx, "[RetrieveGraphMemory]failed to get relations", "err", err)
		return ret, err
	}
	// 构建relation的map结构
	for _, v := range allRelations {
		relationMap[h.storageService.GenerateRelationUID(v.FromEntityID, v.ToEntityID, v.RelationType)] = v
	}

	// 7. 拼凑返回的信息
	// 循环搜reflection，获取top 10 reflection（todo 看是否有更好的办法，或者并行获取，目前该方案搜索太慢）
	for tempRelation, fromToIDs := range graphEntityRelations {
		for tempFromID, tempToIDs := range fromToIDs {
			for _, tempToID := range tempToIDs {
				// 获取近10次的reflection
				recentReflection, err := h.dal.GetTopKMemoryReflectionsByByTypeAndFromToIDs(ctx, recentReflectionNum, tempRelation, tempFromID, tempToID)
				if err != nil {
					logs.CtxError(ctx, "[RetrieveGraphMemory]failed to get recent reflections", "err", err)
					continue
				}
				if len(recentReflection) == 0 {
					continue
				}
				var fromName, fromDesc, toName, toDesc, relationDesc string
				if _, ok := entityMap[tempFromID]; ok {
					fromName = entityMap[tempFromID].EntityName
					fromDesc = entityMap[tempFromID].Description
				}
				if _, ok := entityMap[tempToID]; ok {
					toName = entityMap[tempToID].EntityName
					toDesc = entityMap[tempToID].Description
				}
				if _, ok := relationMap[h.storageService.GenerateRelationUID(tempFromID, tempToID, tempRelation)]; ok {
					relationDesc = relationMap[h.storageService.GenerateRelationUID(tempFromID, tempToID, tempRelation)].Description
				}
				ret.RetrieveGraphMemoryInfos = append(ret.RetrieveGraphMemoryInfos, &memory.RetrieveGraphMemoryInfo{
					FromEntityID:          tempFromID,
					FromEntityName:        fromName,
					FromEntityDescription: fromDesc,
					ToEntityID:            tempToID,
					ToEntityName:          toName,
					ToEntityDescription:   toDesc,
					RelationDescription:   relationDesc,
					Reflections: lo.FilterMap(recentReflection, func(e *dal.MemoryReflectionPO, index int) (*memory.MemoryReflection, bool) {
						if e.Description != "" {
							return &memory.MemoryReflection{
								OccurTime:    e.OccurTime.Unix(),
								FromEntityID: e.FromEntityID,
								ToEntityID:   e.ToEntityID,
								Description:  e.Description,
								RelationType: e.RelationType,
							}, true
						}
						return nil, false
					}),
					RelationType: tempRelation,
				})
			}
		}
	}

	return ret, nil
}
