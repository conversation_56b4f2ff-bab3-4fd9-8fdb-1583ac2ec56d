package chat

import (
	// 标准库
	"bytes"
	entity "code.byted.org/devgpt/kiwis/api/idl/kitex_gen/memory"
	"code.byted.org/devgpt/kiwis/memory/utils"
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"os"
	"time"

	// 外部库
	"gopkg.in/yaml.v3"

	// 内部库
	"code.byted.org/gopkg/localcache"
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/gopkg/pkg/errors"

	"code.byted.org/devgpt/kiwis/lib/config"
)

type Service struct {
	tccConfig    *config.MemoryTCCConfig
	byteJwtToken string
	arkBearer    string // 方舟平台的bearer token
}

// Message 定义了消息结构
type Message struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

type ReviewPromptData struct {
	SysPrompt string `yaml:"sys_prompt"`
	UserMsg   string `yaml:"user_msg"`
}

const (
	chatVerseJwt    = "chatVerseJwt"
	mentorModelDsv3 = "ep-20250703155947-txsfg"
)

// cache jwt token，1h过期
var cache = localcache.MustNewSimpleCache(
	10,
	time.Hour,
	localcache.LRU,
	localcache.WithName("flow.ide.memory_bd"),
)

// NewService new请求open ai的request，后续尝试迁移使用llm的callAzureChatCompletion？
func NewService(tccConfig *config.MemoryTCCConfig) (*Service, error) {
	s := Service{tccConfig: tccConfig}
	token := string(tccConfig.ByteCloudAuthJwt.GetValue())
	//jwt, err := getByteJwtToken(context.Background(), token)
	//if err != nil {
	//	return &s, errors.WithMessage(err, "failed to create LLM Service")
	//}
	s.byteJwtToken = token
	s.arkBearer = string(tccConfig.ArkBearer.GetValue())
	return &s, nil
}

func getByteJwtToken(ctx context.Context, auth string) (string, error) {
	url := "https://cloud.bytedance.net/auth/api/v1/jwt"
	req, err := http.NewRequest(http.MethodGet, url, nil)
	if err != nil {
		logs.CtxError(ctx, "创建请求失败: %v\n", err)
		return "", err
	}
	req.Header.Add("Authorization", "Bearer "+auth)

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		logs.CtxError(ctx, "JWT 令牌获取失败: %v\n", err)
		return "", err
	}
	defer resp.Body.Close()

	// 检查HTTP应答状态码
	if resp.StatusCode != http.StatusOK {
		logs.CtxError(ctx, "非200响应状态码: %v\n", resp.StatusCode)
		return "", err
	}

	// 提取JWT令牌
	jwtToken := resp.Header.Get("x-jwt-token")
	if jwtToken == "" {
		logs.CtxError(ctx, "JWT 令牌获取失败: 令牌为空\n")
		return "", err
	}
	return jwtToken, nil
}

// GetSummaryMessage 生成LLM的总结消息
func (s *Service) GetSummaryMessage(codeDiff string, language string) []Message {

	return []Message{
		{
			Role:    "system",
			Content: "你是一位code diff summarizer，你需要对提供的code diff数据进行简明扼要地总结。\n你需要简明扼要地对新提交的代码片段进行总结和描述，主要包括\n- 本次代码修改的主要目的总结\n- 新增的代码内容总结\n- 移除的代码内容总结。\n注意\n- 总结中不需要展开代码的细节内容，只需要用自然语言描述代码的修改目的、新增内容、移除内容。\n- 重点突出新增代码中的关键变量声明和代码逻辑。",
		}, {
			Role:    "user",
			Content: fmt.Sprintf("下面是一位初级开发人员提交的 %s 变更代码，'-' 表示被删除的代码行，'+' 表示新增的代码行:\n\n```\n%s\n```", language, codeDiff) + "\n请根据上述代码修改记录，请简明扼要地对新提交的代码片段进行总结和描述。",
		},
	}
}

// GetReviewMessage 生成LLM的审查意见消息
func (s *Service) GetReviewMessage(codeDiff string) []Message {
	var reviewPromptData ReviewPromptData
	filePath := "./prompt/review_prompt.yaml"
	//filePath := "./memory/service/chat/prompt/review_prompt.yaml"
	file, err := os.ReadFile(filePath)
	if err != nil {
		logs.V1.Error(fmt.Sprintf("review prompt file load failed %s\n", err))
		return []Message{}
	}
	err = yaml.Unmarshal(file, &reviewPromptData)
	if err != nil {
		logs.V1.Error(fmt.Sprintf("review prompt file parsing failed %s\n", err))
		return []Message{}
	}
	return []Message{
		{
			Role:    "system",
			Content: reviewPromptData.SysPrompt,
		},
		{
			Role:    "user",
			Content: fmt.Sprintf(reviewPromptData.UserMsg, codeDiff),
		},
	}
}

// GetEventBridgeMessage 针对EventBridge事件，生成LLM的总结
func (s *Service) GetEventBridgeMessage(eventInfo string, source entity.GraphMemorySource) []Message {
	var reviewPromptData ReviewPromptData
	var filePath string
	switch source {
	case entity.GraphMemorySource_EventBridge:
		filePath = "./prompt/event_bridge_prompt.yaml"
		if utils.IsMac() {
			filePath = "./memory/service/chat/prompt/event_bridge_prompt.yaml"
		}
	case entity.GraphMemorySource_SCM:
		filePath = "./prompt/scm_prompt.yaml"
		if utils.IsMac() {
			filePath = "./memory/service/chat/prompt/scm_prompt.yaml"
		}
	default:
		return []Message{}
	}

	file, err := os.ReadFile(filePath)
	if err != nil {
		logs.V1.Error(fmt.Sprintf("[GetEventBridgeMessage]review prompt file load failed %s\n", err))
		return []Message{}
	}
	err = yaml.Unmarshal(file, &reviewPromptData)
	if err != nil {
		logs.V1.Error(fmt.Sprintf("[GetEventBridgeMessage]review prompt file parsing failed %s\n", err))
		return []Message{}
	}
	return []Message{
		{
			Role:    "system",
			Content: reviewPromptData.SysPrompt,
		},
		{
			Role:    "user",
			Content: fmt.Sprintf(reviewPromptData.UserMsg, eventInfo),
		},
	}
}

// RequestChat 请求LLM,调用时需要将message拼成role/user对话的形式
func (s *Service) RequestChat(ctx context.Context, messages []Message, model string, temperature int, timeout time.Duration, jsonFormat bool) (string, error) {
	if len(messages) == 0 {
		return "", errors.New("empty messages")
	}
	url := "https://chatverse.bytedance.net/v1/chat"
	if model == mentorModelDsv3 {
		url = "https://ark-cn-beijing.bytedance.net/api/v3/chat/completions"
	}

	//var jwt string
	//if val, err := cache.Get(ctx, chatVerseJwt); err != nil {
	//	newJwt, err := getByteJwtToken(ctx, s.byteJwtToken)
	//	if err != nil {
	//		return "", errors.WithMessage(err, "[RequestChat]failed to get byte jwt token")
	//	}
	//	err = cache.Set(ctx, chatVerseJwt, newJwt)
	//	if err != nil {
	//		return "", errors.WithMessage(err, "[RequestChat]failed to set jwt token")
	//	}
	//	jwt = newJwt
	//} else {
	//	jwt = val.(string)
	//}

	headers := map[string]string{
		// 添加需要的 headers，例如认证:
		"Authorization": fmt.Sprintf("Bearer %s", s.arkBearer),
		"Content-Type":  "application/json",
		//"X-Jwt-Token":   jwt,
	}

	type respFormat struct {
		Type string `json:"type"`
	}

	// 对 messages 的处理和Python代码略有不同，您可以在调用前预处理您的数据
	data := map[string]interface{}{
		"messages": messages,
		"model":    model,
		//"temperature": temperature,
		//"max_tokens":  4096,
		//"stream":      false,
	}
	if jsonFormat {
		data["response_format"] = respFormat{Type: "json_object"}
	}

	jsonData, err := json.Marshal(data)
	if err != nil {
		logs.CtxError(ctx, "Failed to marshal data: %v, %v\n", err, messages)
		return "", err
	}

	client := &http.Client{
		Timeout: timeout,
	}
	request, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		logs.CtxError(ctx, "Failed to create request: %v, %v\n", err, messages)
		return "", err
	}
	for key, value := range headers {
		request.Header.Add(key, value)
	}

	resp, err := client.Do(request)
	if err != nil {
		logs.CtxError(ctx, "Chat API调用失败: %v, %v\n", err, messages)
		return "", err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		logs.CtxError(ctx, "resp is %+v", resp)
		return "", fmt.Errorf("received non-200 response status: %v", resp.StatusCode)
	}

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response body: %v", err)
	}

	if model == mentorModelDsv3 {
		var response struct {
			//Data struct {
			Choices []struct {
				Message struct {
					Content string `json:"content"`
				} `json:"message"`
			} `json:"choices"`
			//} `json:"data"`
		}
		err = json.Unmarshal(body, &response)
		if err != nil {
			return "", fmt.Errorf("failed to unmarshal response: %v", err)
		}

		if len(response.Choices) == 0 {
			return "", fmt.Errorf("no choices found in response")
		}

		return response.Choices[0].Message.Content, nil
	} else {
		var response struct {
			Data struct {
				Choices []struct {
					Message struct {
						Content string `json:"content"`
					} `json:"message"`
				} `json:"choices"`
			} `json:"data"`
		}
		err = json.Unmarshal(body, &response)
		if err != nil {
			return "", fmt.Errorf("failed to unmarshal response: %v", err)
		}

		if len(response.Data.Choices) == 0 {
			return "", fmt.Errorf("no choices found in response")
		}

		return response.Data.Choices[0].Message.Content, nil
	}

}
