package embeddingindex

import (
	"context"
	"encoding/json"
	"time"

	"github.com/pkg/errors"
	"github.com/samber/lo"

	"code.byted.org/devgpt/kiwis/lib/metrics"
	"code.byted.org/gopkg/logs/v2"
	vikingclient "code.byted.org/lagrange/viking_go_client"
)

const (
	doubaoOpname = "hnsw"
)

func newDoubaoVikingProxyClient(idc string) (*vikingclient.VikingProxyServiceClient, error) {
	// HARDCODE here temporarily.
	return vikingclient.NewVikingProxyClient("data.devgpt.viking_proxy", idc, "default", "golang", 10000, 10000)
}

func (i *EmbeddingIndexer) recallFromDouBao(ctx context.Context, req *vikingclient.RecallRequest) (*vikingclient.RecallResp, error) {
	logs.V1.CtxInfo(ctx, "recalling from gl data center: %+v", req)
	if i.doubaoProxyClient == nil {
		return nil, nil
	}
	tags := &metrics.VikingTag{Cluster: i.vikingCli.Corpus, Index: i.index}
	_ = metrics.M.VikingRecallRate.WithTags(tags).Add(1)
	now := time.Now()
	defer metrics.TraceTimer(metrics.M.VikingRecallTimer.WithTags(tags))()
	request := vikingclient.NewProxySimpleRecallRequest(i.vikingCli.Corpus, i.modelName, i.index, doubaoOpname, "default",
		req.TopK, 128)
	request.SetQueryVec(lo.Map(req.Embedding, func(item float32, _ int) float64 {
		return float64(item)
	}))
	dslContent, err := json.Marshal(req.DslInfo)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to marshal dsl info")
	}
	request.SetDSLQuery(string(dslContent))
	// Use context.Background() here to avoid any abortion.
	recall, err := i.doubaoProxyClient.SimpleRecall(context.Background(), request)
	if err != nil {
		_ = metrics.M.VikingRecallError.WithTags(tags).Add(1)
		return nil, errors.WithMessage(err, "failed to recall from gl data center")
	}
	resp := &vikingclient.RecallResp{}
	for idx, score := range recall.Scores {
		recallResult := &vikingclient.SimpleRecallRpcResult{
			LabelLower64: uint64(recall.LabelLower[idx]),
			LabelUpper64: uint64(recall.LabelUpper[idx]),
			Scores:       score,
		}
		resp.Result = append(resp.Result, recallResult)
	}
	logs.CtxInfo(ctx, "recall from gl data center use %d ms: %v", time.Since(now).Milliseconds(), resp)
	return resp, nil
}

func (i *EmbeddingIndexer) recallFromDouBaoDiskANN(ctx context.Context, req *vikingclient.RecallRequest) (*vikingclient.RecallResp, error) {
	logs.V1.CtxInfo(ctx, "recalling from lq data center diskann: %+v", req)
	if i.doubaoDiskANNProxyClient == nil {
		return nil, nil
	}
	tags := &metrics.VikingTag{Cluster: i.vikingCli.Corpus, Index: "disk"}
	_ = metrics.M.VikingRecallRate.WithTags(tags).Add(1)
	now := time.Now()
	defer metrics.TraceTimer(metrics.M.VikingRecallTimer.WithTags(tags))()
	request := vikingclient.NewProxySimpleRecallRequest(i.vikingCli.Corpus, i.modelName, "disk", "diskann", "default",
		req.TopK, 128)
	request.SetQueryVec(lo.Map(req.Embedding, func(item float32, _ int) float64 {
		return float64(item)
	}))
	dslContent, err := json.Marshal(req.DslInfo)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to marshal dsl info")
	}
	request.SetDSLQuery(string(dslContent))
	// Use context.Background() here to avoid any abortion.
	recall, err := i.doubaoDiskANNProxyClient.SimpleRecall(context.Background(), request)
	if err != nil {
		_ = metrics.M.VikingRecallError.WithTags(tags).Add(1)
		return nil, errors.WithMessage(err, "failed to recall from lq data center diskann")
	}
	resp := &vikingclient.RecallResp{}
	for idx, score := range recall.Scores {
		recallResult := &vikingclient.SimpleRecallRpcResult{
			LabelLower64: uint64(recall.LabelLower[idx]),
			LabelUpper64: uint64(recall.LabelUpper[idx]),
			Scores:       score,
		}
		resp.Result = append(resp.Result, recallResult)
	}

	logs.CtxInfo(ctx, "recall from lq data center diskann: %+v", recall.LabelLower)
	logs.CtxInfo(ctx, "recall from lq data center diskann use %d ms: %v", time.Since(now).Milliseconds(), resp)

	return resp, nil
}
