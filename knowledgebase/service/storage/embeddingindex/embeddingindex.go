package embeddingindex

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"math"
	"runtime/debug"
	"strings"
	"sync"
	"time"

	"github.com/cenkalti/backoff/v4"
	"github.com/hashicorp/go-multierror"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"golang.org/x/sync/errgroup"

	"code.byted.org/devgpt/kiwis/knowledgebase/common"
	"code.byted.org/devgpt/kiwis/knowledgebase/service/embedding"
	"code.byted.org/devgpt/kiwis/knowledgebase/service/storage"
	"code.byted.org/devgpt/kiwis/knowledgebase/service/storage/async_embedding"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/conv"
	"code.byted.org/devgpt/kiwis/lib/util"
	"code.byted.org/devgpt/kiwis/port/abase"
	"code.byted.org/devgpt/kiwis/port/viking"
	"code.byted.org/gopkg/ctxvalues"
	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/logs/v2"
	vikingclient "code.byted.org/lagrange/viking_go_client"
)

const (
	datasetIDsDSLKey    = "dataset_ids"
	datasourceIDsDSLKey = "datasource_ids"
	fileIDsDSLKey       = "file_ids"

	batchSize = 100

	CodeAssistPSM = "flow.codeassist.knowledgebase"
)

// EmbeddingIndexer is the indexer use vector to recall.
type EmbeddingIndexer struct {
	vikingCli             *viking.Client
	abase                 *abase.Client
	cacheAbase            *abase.Client
	embeddingService      embedding.Embedder
	asyncEmbeddingService storage.AsyncEmbedder
	embeddingModel        string
	index                 string
	modelName             string
	// wg is only used to wait for the async write used in testing.
	wg                       sync.WaitGroup
	proxyClient              *vikingclient.VikingProxyServiceClient
	doubaoProxyClient        *vikingclient.VikingProxyServiceClient
	doubaoDiskANNProxyClient *vikingclient.VikingProxyServiceClient
}

var _ storage.Indexer = (*EmbeddingIndexer)(nil)

func NewEmbeddingIndexer(vikingCli *viking.Client, abase *abase.Client, cacheAbase *abase.Client,
	embeddingService embedding.Embedder, asyncEmbedder storage.AsyncEmbedder,
	embeddingModel string, vikingConf *config.VikingStoreConfig) (*EmbeddingIndexer, error) {
	i := &EmbeddingIndexer{
		vikingCli:             vikingCli,
		abase:                 abase,
		cacheAbase:            cacheAbase,
		embeddingService:      embeddingService,
		embeddingModel:        embeddingModel,
		asyncEmbeddingService: asyncEmbedder,
		index:                 vikingConf.Index,
		modelName:             vikingConf.ModelName,
	}
	if vikingCli.Corpus == corpus {
		var err error
		i.proxyClient, err = newVikingProxyClient()
		if err != nil {
			return nil, err
		}
	}
	if env.PSM() == CodeAssistPSM {
		var err error
		i.doubaoProxyClient, err = newDoubaoVikingProxyClient("gl")
		if err != nil {
			return nil, err
		}
		i.doubaoDiskANNProxyClient, err = newDoubaoVikingProxyClient("lq")
		if err != nil {
			return nil, err
		}
	}
	go i.work()
	return i, nil
}

func (i *EmbeddingIndexer) work() {
	for {
		ctx := context.Background()
		asyncEmbeddingResponse, err := i.asyncEmbeddingService.Read(ctx)
		if err != nil {
			if errors.Is(err, io.EOF) {
				logs.V1.Info("async embedding is closed")
			}
			logs.V1.Error("failed to read embedding from async embedder: %+v", err)
			// Cool down a few time.
			time.Sleep(time.Second)
			i.wg.Done()
			continue
		}
		eVector, ok := asyncEmbeddingResponse.Object.(*nonEmbeddingIndexVector)
		if !ok {
			logs.V1.Error("bad type of object, got: %T", asyncEmbeddingResponse.Object)
			i.wg.Done()
			continue
		}
		go func() {
			defer func() {
				i.wg.Done()
				if r := recover(); r != nil {
					logs.V1.CtxError(ctx, "failed to save vector: %+v, %+v", r, string(debug.Stack()))
				}
			}()
			ctx = ctxvalues.SetLogID(ctx, eVector.LogID)
			if err := asyncEmbeddingResponse.Error; err != nil {
				logs.V1.CtxError(ctx, "failed to embedding in async embedding: %+v", err)
				return
			}
			err := backoff.Retry(func() error {
				eVector.V = lo.Map(asyncEmbeddingResponse.Data.Embedding, func(item float32, index int) float64 {
					return float64(item)
				})
				err = i.saveVectorsToViking(ctx, []*nonEmbeddingIndexVector{
					eVector,
				})
				if err != nil {
					return err
				}
				// cache embedding result
				bytes, err := json.Marshal(eVector.V)
				if err != nil {
					logs.V1.CtxError(ctx, "failed to marshal vector cache")
					return nil
				}
				vectorCacheAbaseKey := buildVectorCacheAbaseKey(i.embeddingModel, eVector.EmbeddingContent)
				if err = i.cacheAbase.Set(ctx, vectorCacheAbaseKey, string(bytes), time.Hour*24*7); err != nil {
					logs.V1.CtxError(ctx, "failed to set vector cache: %+v", err)
				}
				logs.V1.CtxInfo(ctx, "cached vector %s", vectorCacheAbaseKey)
				return nil
			}, backoff.WithMaxRetries(backoff.NewExponentialBackOff(), 5))
			if err != nil {
				logs.CtxError(ctx, "failed to save vector: %+v", err)
			}
		}()
	}
}

// vectorMeta stores in abase and presents vector in viking.
// Get data from viking is only for debug.
type vectorMeta struct {
	// No need to store upper & lower, the key of abase contains label.
	Upper      uint64                  `json:"-"`
	Lower      uint64                  `json:"-"`
	V          []float64               `json:"v"`
	SegmentID  string                  `json:"s"`
	DSLInfo    map[string]interface{}  `json:"d"`
	References []storage.FileReference `json:"r"`
}

func (v *vectorMeta) Label() string {
	return viking.BuildLabel(v.Upper, v.Lower)
}

type embeddingIndexVector struct {
	*vectorMeta
	UpdateReferences []storage.FileReference
	Segment          *storage.Segment
}

type nonEmbeddingIndexVector struct {
	*vectorMeta
	EmbeddingContent string
	Segment          *storage.Segment
	Option           *storage.UpdateSegmentOption
	// LogID is used for async embedding.
	LogID string
}

// SaveSegments saves or updates segments.
// If the vectors of segments exists, it updates DSL info by segment references.
// If the vectors of segments is not exists, it's embedding and save.
func (i *EmbeddingIndexer) SaveSegments(ctx context.Context, options ...*storage.UpdateSegmentOption) error {
	events := common.GetStatistics(ctx, common.CKGInitIndexEventName).GetAllEvents()
	for _, event := range events {
		event.SetParam(common.EmbeddingModelParamKey, i.embeddingModel)
	}

	nonIndexedVectors, indexedVectors, err := i.getIndexedAndUnindexedVectors(ctx, options...)
	if err != nil {
		return errors.WithMessagef(err, "failed to query vectors")
	}
	updatedVikingData := make([]*vikingclient.VikingDbData, 0)
	multiErr := &multierror.Error{}
	for _, data := range indexedVectors {
		data.References = lo.Uniq(append(data.References, data.UpdateReferences...))
		data.DSLInfo = i.buildDSL(data.References, data.Segment.Tags)
		updatedVikingData = append(updatedVikingData, &vikingclient.VikingDbData{
			LabelLower: data.Lower,
			LabelUpper: data.Upper,
			Context:    []string{"default"},
			DslInfo:    data.DSLInfo,
			FVector:    data.V,
		})
	}
	err = i.msetVectorMeta(ctx, lo.Map(indexedVectors, func(item *embeddingIndexVector, index int) *vectorMeta {
		return item.vectorMeta
	}))
	if err != nil {
		return errors.WithMessagef(err, "failed to update vector meta")
	}

	group, _ := errgroup.WithContext(ctx)
	// No limit.
	group.SetLimit(-1)
	for _, data := range updatedVikingData {
		data := data
		group.Go(func() error {
			_, _, err = i.vikingCli.AddData([]*vikingclient.VikingDbData{data})
			if err != nil {
				return errors.WithMessagef(err, "failed to update vector data")
			}
			return nil
		})
	}
	waitError := group.Wait()
	if waitError != nil {
		return errors.WithMessagef(waitError, "failed to group update vector data")
	}
	err = i.embeddingSaveNewVectors(ctx, nonIndexedVectors)
	if err != nil {
		multiErr = multierror.Append(multiErr, errors.WithMessagef(err, "failed to add new segment"))
	}
	return multiErr.ErrorOrNil()
}

func (i *EmbeddingIndexer) DeleteSegments(ctx context.Context, options ...*storage.DeleteSegmentOption) error {
	if len(options) == 0 {
		logs.V1.CtxWarn(ctx, "no options for Indexer deleting segments")
		return nil
	}
	start := time.Now()
	segmentIDOptionMap := map[string][]*storage.DeleteSegmentOption{}
	segmentUniqueIDs := make([]string, 0)
	for _, option := range options {
		for _, segmentUniqueID := range option.SegmentUniqueIDs {
			segmentIDOptionMap[segmentUniqueID] = append(segmentIDOptionMap[segmentUniqueID], option)
			segmentUniqueIDs = append(segmentUniqueIDs, segmentUniqueID)
		}
	}
	indexedVectors, err := i.getIndexedVectorMetas(ctx, segmentUniqueIDs)
	if err != nil {
		return errors.WithMessagef(err, "failed to query vectors")
	}
	logs.V1.CtxInfo(ctx, "get indexed and unindexed vectors cost: %dms", time.Since(start).Milliseconds())

	updatedVectors := make([]*vectorMeta, 0)
	deletedSegments := make(map[string]bool)
	for segmentID, vectors := range indexedVectors {
		options := segmentIDOptionMap[segmentID]
		for _, meta := range vectors {
			for _, optionData := range options {
				excludeFileRef := optionData.FileReference
				meta.References = lo.Filter(meta.References, func(ref storage.FileReference, index int) bool {
					if ref.DatasetID == excludeFileRef.DatasetID && ref.DatasourceID == excludeFileRef.DatasourceID &&
						ref.FileID == excludeFileRef.FileID {
						return false
					}
					return true
				})
			}
			if len(meta.References) == 0 {
				// Mark the segment is deleted.
				// The references for vectors in the same segment should be identical. If one vector is no reference,
				// all other vectors should be deleted.
				deletedSegments[segmentID] = true
				continue
			}
			if meta.DSLInfo == nil {
				meta.DSLInfo = map[string]interface{}{}
			}
			newDSL := i.buildDSL(meta.References, nil)
			for k, v := range newDSL {
				// Override by indexer's injected DSLs.
				meta.DSLInfo[k] = v
			}
			updatedVectors = append(updatedVectors, meta)
		}
	}

	start = time.Now()
	if len(updatedVectors) > 0 {
		err = i.msetVectorMeta(ctx, updatedVectors)
		if err != nil {
			return errors.WithMessagef(err, "failed to update vector meta")
		}

		group, _ := errgroup.WithContext(ctx)
		// No limit.
		group.SetLimit(-1)
		updatedVikingData := lo.Map(updatedVectors, func(meta *vectorMeta, index int) *vikingclient.VikingDbData {
			return &vikingclient.VikingDbData{
				LabelLower: meta.Lower,
				LabelUpper: meta.Upper,
				Context:    []string{"default"},
				DslInfo:    meta.DSLInfo,
				FVector:    meta.V,
			}
		})
		for _, data := range updatedVikingData {
			data := data
			group.Go(func() error {
				_, _, err := i.vikingCli.AddData([]*vikingclient.VikingDbData{data})
				if err != nil {
					return errors.WithMessagef(err, "failed to update vector data")
				}
				return nil
			})
		}
		waitError := group.Wait()
		if waitError != nil {
			return errors.WithMessagef(waitError, "failed to group update vector data")
		}
	}
	logs.V1.CtxInfo(ctx, "update vector dsl cost: %dms", time.Since(start).Milliseconds())

	start = time.Now()
	if len(deletedSegments) > 0 {
		deleteVectors := make(map[string]map[string]*vectorMeta, len(deletedSegments))
		for segmentUniqueID, _ := range deletedSegments {
			deleteVectors[segmentUniqueID] = indexedVectors[segmentUniqueID]
		}
		err := i.deleteSegments(ctx, deleteVectors)
		if err != nil {
			return err
		}
	}
	logs.V1.CtxInfo(ctx, "delete vector cost: %dms", time.Since(start).Milliseconds())
	return nil
}

func (i *EmbeddingIndexer) deleteSegments(ctx context.Context, deleteSegments map[string]map[string]*vectorMeta) error {
	// FIXME: the vector maybe in embedding queue.
	//   mark the vector label and pre-check the mark before embedding and save.
	vikingLabels := make([]*vikingclient.LabelLowerAndUpper, 0, len(deleteSegments))
	vectorLabels := make([]string, 0, len(deleteSegments))
	for _, vectors := range deleteSegments {
		for _, meta := range vectors {
			vikingLabels = append(vikingLabels, &vikingclient.LabelLowerAndUpper{
				LabelLower64: meta.Lower,
				LabelUpper64: meta.Upper,
			})
			vectorLabels = append(vectorLabels, meta.Label())
		}
	}
	_, err := viking.BatchDeleteData(i.vikingCli, batchSize, vikingLabels)
	if err != nil {
		return errors.WithMessagef(err, "failed to delete vector data")
	}
	// NOTICE: Delete viking first.
	if err = i.expireVectorMeta(ctx, vectorLabels); err != nil {
		return errors.WithMessagef(err, "failed to delete vector meta")
	}
	segmentUniqueIDs := lo.MapToSlice(deleteSegments, func(segmentUniqueID string, vectors map[string]*vectorMeta) string {
		return segmentUniqueID
	})
	if err = i.delSegmentVectorLabels(ctx, segmentUniqueIDs); err != nil {
		return errors.WithMessagef(err, "failed to delete vector set")
	}
	return nil
}

// QuerySegments recalls segments by options.
// It returns segment ids with file reference.
func (i *EmbeddingIndexer) QuerySegments(ctx context.Context,
	option *storage.QuerySegmentOption) ([]*storage.QuerySegmentResult, error) {
	var (
		response *embedding.EmbeddingResponse
		err      error
	)

	err = backoff.Retry(func() error {
		response, err = i.embeddingService.Embedding(ctx, &embedding.EmbeddingRequest{
			Model:  i.embeddingModel,
			Inputs: []string{option.Query},
		})
		if err != nil {
			return errors.WithMessagef(err, "failed to embedding")
		}
		return nil
	}, backoff.WithMaxRetries(backoff.NewExponentialBackOff(), 3))
	if err != nil {
		return nil, errors.WithMessagef(err, "failed to embedding query content")
	}
	vector := response.Data[0].Embedding
	dslInfo := i.buildQueryDSL(option.DatasetIDs, option.DatasourceIDs, option.FileIDs, option.Matches)
	var (
		resp  *vikingclient.RecallResp
		logID string
	)
	vikingReq := &vikingclient.RecallRequest{
		Embedding: vector,
		DslInfo:   dslInfo,
		Index:     i.index,
		TopK:      2 * option.TopN,
	}
	var diskannResp *vikingclient.RecallResp
	wg := sync.WaitGroup{}

	if env.PSM() == CodeAssistPSM {
		if resp, err = i.recallFromDouBao(ctx, vikingReq); err != nil {
			if err != nil {
				return nil, &common.CustomError{
					Ignore:  false,
					Err:     errors.WithMessagef(err, "failed to recall from doubao"),
					ErrType: common.CustomErrorTypeViking,
				}
			}
		}
		go func() {
			defer func() {
				r := recover()
				if r != nil {
					logs.V1.CtxError(ctx, "panic in recallFromDouBaoDiskANN: %v, %s", r, string(debug.Stack()))
				}
			}()

			diskannResp, err = i.recallFromDouBaoDiskANN(ctx, vikingReq)
			if err != nil {
				logs.V1.CtxError(ctx, "failed to recall from doubao diskann: %+v", err)
				return
			}
			if diskannResp == nil || len(diskannResp.Result) == 0 {
				logs.V1.CtxError(ctx, "failed to recall from doubao diskann: empty result")
			}
		}()
	} else {
		wg.Add(1)
		go func() {
			defer func() {
				r := recover()
				if r != nil {
					logs.V1.CtxError(ctx, "panic in recallFromDiskANN: %v, %s", r, string(debug.Stack()))
				}
			}()
			defer wg.Done()

			// Dry recall from diskANN to collect performance data.
			var err error
			if diskannResp, err = i.recallFromDiskANN(ctx, vikingReq); err != nil {
				logs.V1.CtxError(ctx, "failed to recall from diskann: %+v", err)
			}
		}()

		err = backoff.Retry(func() error {
			resp, logID, err = i.vikingCli.Recall(vikingReq)
			if err != nil {
				return err
			}
			return nil
		}, backoff.WithMaxRetries(backoff.NewConstantBackOff(time.Millisecond*50), 3))

		if err != nil {
			wg.Wait()
			if diskannResp != nil {
				logs.V1.CtxWarn(ctx, "failed to recall, logID: %s, err: %v", logID, err)
				resp = diskannResp
			} else {
				return nil, &common.CustomError{
					Ignore:  false,
					Err:     errors.WithMessagef(err, "failed to recall, logID: %s", logID),
					ErrType: common.CustomErrorTypeViking,
				}
			}
		}

		if resp == nil || len(resp.Result) == 0 {
			logs.V1.CtxWarn(ctx, "no vectors recalled, logID: %s, opt: %+v", logID, util.MarshalStruct(option))
			logs.V1.CtxWarn(ctx, "viking request: %+v", util.MarshalStruct(vikingReq))
			wg.Wait()
			if diskannResp != nil {
				logs.V1.CtxWarn(ctx, "using diskann response, logID: %s", logID)
				resp = diskannResp
			} else {
				return []*storage.QuerySegmentResult{}, nil
			}
		}
	}

	if resp == nil || len(resp.Result) == 0 {
		logs.V1.CtxWarn(ctx, "no vectors recalled, logID: %s, opt: %+v", logID, util.MarshalStruct(option))
		logs.V1.CtxWarn(ctx, "viking request: %+v", util.MarshalStruct(vikingReq))
		return []*storage.QuerySegmentResult{}, nil
	}
	scores := make(map[string]float64)
	recalledVectorLabels := lo.Map(resp.Result, func(item *vikingclient.SimpleRecallRpcResult, index int) string {
		label := viking.BuildLabel(item.LabelUpper64, item.LabelLower64)
		scores[label] = item.Scores
		return label
	})
	logs.V1.CtxInfo(ctx, "recalled %d vectors, top n: %d", len(resp.Result), option.TopN)
	logs.V1.CtxInfo(ctx, "recalled vectors: %+v", recalledVectorLabels)
	vectorMetas, err := i.mgetVectorMeta(ctx, recalledVectorLabels)
	if err != nil {
		return nil, errors.WithMessagef(err, "failed to get vector meta")
	}
	recalledSegments := make([]*storage.QuerySegmentResult, 0, option.TopN)
	segmentUniqueIDMap := map[string]bool{}
	for _, vectorLabel := range recalledVectorLabels {
		vectorMeta, ok := vectorMetas[vectorLabel]
		if !ok {
			logs.V1.CtxError(ctx, "vector meta not found: %s", vectorLabel)
			continue
		}
		for _, reference := range vectorMeta.References {
			if referenceMatch(reference, option) {
				if segmentUniqueIDMap[vectorMeta.SegmentID] {
					continue
				}
				segmentUniqueIDMap[vectorMeta.SegmentID] = true
				recalledSegments = append(recalledSegments, &storage.QuerySegmentResult{
					SegmentUniqueID: vectorMeta.SegmentID,
					FileReference: &storage.FileReference{
						DatasetID:    reference.DatasetID,
						DatasourceID: reference.DatasourceID,
						FileID:       reference.FileID,
					},
					Score: scores[vectorMeta.Label()],
				})
				break
			}
		}
		if len(recalledSegments) >= int(option.TopN) {
			break
		}
	}
	// Deduplicated by segment id.
	return recalledSegments, nil
}

// getSegmentVectorLabels gets indexed vector labels.
func (i *EmbeddingIndexer) getSegmentVectorLabels(ctx context.Context, segmentUniqueIDs []string) (map[string][]string, error) {
	vectorMap := map[string][]string{}
	vectorSets, err := abase.BatchMGet(ctx, i.abase, batchSize, lo.Map(segmentUniqueIDs, func(segmentUniqueID string, index int) string {
		return buildVectorSetAbaseKey(segmentUniqueID)
	})...)
	if err != nil {
		return nil, errors.WithMessagef(err, "failed to get vectors")
	}
	for idx, vectorSet := range vectorSets {
		if vectorSet == nil {
			continue
		}
		vectorSet := vectorSet.(string)
		vectorLabels := strings.Split(vectorSet, ",")
		vectorMap[segmentUniqueIDs[idx]] = vectorLabels
	}
	return vectorMap, nil
}

func (i *EmbeddingIndexer) saveSegmentVectorLabels(ctx context.Context, vectorLabels map[string][]string) error {
	abaseMSetPairs := make([]string, 0, len(vectorLabels)*2)
	for segmentID, vectorLabels := range vectorLabels {
		abaseKey := buildVectorSetAbaseKey(segmentID)
		abaseMSetPairs = append(abaseMSetPairs, abaseKey, strings.Join(vectorLabels, ","))
	}
	err := abase.BatchMSet(ctx, i.abase, batchSize, abaseMSetPairs...)
	if err != nil {
		return errors.WithMessagef(err, "failed to save vector set")
	}
	return nil
}

func (i *EmbeddingIndexer) delSegmentVectorLabels(ctx context.Context, segmentUniqueIDs []string) error {
	abaseKeys := lo.Map(segmentUniqueIDs, func(segmentUniqueID string, index int) string {
		return buildVectorSetAbaseKey(segmentUniqueID)
	})
	_, err := abase.BatchDel(ctx, i.abase, batchSize, abaseKeys...)
	if err != nil {
		return errors.WithMessagef(err, "failed to delete vector set")
	}
	return nil
}

func (i *EmbeddingIndexer) mgetVectorMeta(ctx context.Context, labels []string) (map[string]*vectorMeta, error) {
	vectorMetaRaws, err := abase.BatchMGet(ctx, i.abase, batchSize, lo.Map(labels, func(label string, index int) string {
		return buildVectorAbaseKey(label)
	})...)
	if err != nil {
		return nil, errors.WithMessagef(err, "failed to get vector meta")
	}
	result := map[string]*vectorMeta{}
	multiErr := &multierror.Error{}
	for idx, vectorMetaRaw := range vectorMetaRaws {
		if vectorMetaRaw == nil {
			continue
		}
		vectorMetaRaw := vectorMetaRaw.(string)
		label := labels[idx]
		upper, lower, err := viking.ParseLabel(label)
		if err != nil {
			multiErr = multierror.Append(multiErr, errors.Errorf("failed to parse label: %s", label))
			continue
		}
		if len(vectorMetaRaw) == 0 {
			// FIXME: 2 cases: 1) vector is in embedding queue; 2) failed to update meta.
			continue
		}
		vm := &vectorMeta{}
		err = json.Unmarshal([]byte(vectorMetaRaw), vm)
		if err != nil {
			multiErr = multierror.Append(multiErr, errors.Errorf("failed to unmarshal vector meta: %s", vectorMetaRaw))
			continue
		}
		vm.Upper = upper
		vm.Lower = lower
		result[label] = vm
	}
	return result, nil
}

func (i *EmbeddingIndexer) msetVectorMeta(ctx context.Context, vectors []*vectorMeta) error {
	if len(vectors) == 0 {
		return nil
	}
	multiErr := &multierror.Error{}
	vectorAbaseMap := make(map[string]string)
	for _, data := range vectors {
		vectorMetaRaw, err := json.Marshal(data)
		if err != nil {
			multiErr = multierror.Append(multiErr, errors.WithMessagef(err, "failed to marshal vector meta"))
			continue
		}
		vectorAbaseKey := buildVectorAbaseKey(data.Label())
		vectorAbaseMap[vectorAbaseKey] = string(vectorMetaRaw)
	}
	msetAbasePairs := make([]string, 0, len(vectors)*2)
	for vectorAbaseKey, vectorMeta := range vectorAbaseMap {
		msetAbasePairs = append(msetAbasePairs, vectorAbaseKey, vectorMeta)
	}
	err := abase.BatchMSet(ctx, i.abase, batchSize, msetAbasePairs...)
	if err != nil {
		multiErr = multierror.Append(multiErr, errors.WithMessagef(err, "failed to save vector meta"))
	}
	return multiErr.ErrorOrNil()
}

func (i *EmbeddingIndexer) delVectorMeta(ctx context.Context, vectorLabels []string) error {
	delKeys := make([]string, 0, len(vectorLabels))
	for _, vectorLabel := range vectorLabels {
		delKeys = append(delKeys, buildVectorAbaseKey(vectorLabel))
	}
	_, err := abase.BatchDel(ctx, i.abase, batchSize, delKeys...)
	if err != nil {
		return errors.WithMessagef(err, "failed to delete vector meta")
	}
	return nil
}

func (i *EmbeddingIndexer) expireVectorMeta(ctx context.Context, vectorLabels []string) error {
	delKeys := make([]string, 0, len(vectorLabels))
	for _, vectorLabel := range vectorLabels {
		delKeys = append(delKeys, buildVectorAbaseKey(vectorLabel))
	}
	_, err := abase.BatchExpire(ctx, i.abase, batchSize, time.Hour, delKeys...)
	if err != nil {
		return errors.WithMessagef(err, "failed to delete vector meta")
	}
	return nil
}

func (i *EmbeddingIndexer) getIndexedAndUnindexedVectors(ctx context.Context, options ...*storage.UpdateSegmentOption) (nonIndexedVectors []*nonEmbeddingIndexVector, indexedVectors []*embeddingIndexVector, err error) {
	type embeddingIndexSegment struct {
		*storage.Segment
		Option     *storage.UpdateSegmentOption
		References []storage.FileReference
	}
	segmentsMap := make(map[string]*embeddingIndexSegment)
	for _, option := range options {
		for _, segment := range option.Segments {
			if embeddingSegment, ok := segmentsMap[segment.SegmentUniqueID]; ok {
				embeddingSegment.References = append(embeddingSegment.References, *option.FileReference)
				continue
			}
			segmentsMap[segment.SegmentUniqueID] = &embeddingIndexSegment{
				Segment:    segment,
				Option:     option,
				References: []storage.FileReference{*option.FileReference},
			}
		}
	}
	segments := lo.MapToSlice(segmentsMap, func(key string, value *embeddingIndexSegment) *embeddingIndexSegment {
		return value
	})
	allIndexedVectorMetas, err := i.getIndexedVectorMetas(ctx, lo.Map(segments, func(s *embeddingIndexSegment, _ int) string {
		return s.SegmentUniqueID
	}))
	if err != nil {
		return nil, nil, errors.WithMessagef(err, "failed to get indexed vector metas")
	}
	for _, segment := range segments {
		indexedVectorMetas := allIndexedVectorMetas[segment.SegmentUniqueID]
		event := common.GetEvent(ctx, common.CKGInitIndexEventName, segment.Option.FileReference.FileID)
		storedVectors := conv.DefaultAny[int](event.GetParam(common.StoredVectorsParamKey))
		if len(indexedVectorMetas) == 0 {
			// This segment is not indexed.
			for _, content := range segment.EmbeddingContents {
				if len(strings.TrimSpace(content)) == 0 {
					continue
				}
				storedVectors++
				nonIndexedVector := &nonEmbeddingIndexVector{
					vectorMeta: &vectorMeta{
						Upper:     util.RandomInt(uint64(1), math.MaxUint32),
						Lower:     util.RandomInt(uint64(1), math.MaxUint32),
						V:         nil,
						SegmentID: segment.SegmentUniqueID,
						// Build DSLInfo later.
						DSLInfo:    nil,
						References: lo.Uniq(segment.References),
					},
					EmbeddingContent: content,
					Segment:          segment.Segment,
					Option:           segment.Option,
				}
				nonIndexedVector.DSLInfo = i.buildDSL(nonIndexedVector.References, segment.Segment.Tags)
				nonIndexedVectors = append(nonIndexedVectors, nonIndexedVector)
			}
			event.SetParam(common.StoredVectorsParamKey, storedVectors)
			continue
		}
		for _, meta := range indexedVectorMetas {
			indexedVectors = append(indexedVectors, &embeddingIndexVector{
				vectorMeta:       meta,
				UpdateReferences: segment.References,
				Segment:          segment.Segment,
			})
		}
	}
	return nonIndexedVectors, indexedVectors, nil
}

func (i *EmbeddingIndexer) getIndexedVectorMetas(ctx context.Context, segmentUniqueIDs []string) (map[string]map[string]*vectorMeta, error) {
	vectorLabelM, err := i.getSegmentVectorLabels(ctx, segmentUniqueIDs)
	if err != nil {
		return nil, errors.WithMessagef(err, "failed to get vector vectorLabelM")
	}
	indexedVectorLabels := make([]string, 0)
	for _, segmentUniqueID := range segmentUniqueIDs {
		vectorLabels := vectorLabelM[segmentUniqueID]
		indexedVectorLabels = append(indexedVectorLabels, vectorLabels...)
	}
	vectorMetas, err := i.mgetVectorMeta(ctx, indexedVectorLabels)
	if err != nil {
		return nil, errors.WithMessagef(err, "failed to get vector meta")
	}
	result := make(map[string]map[string]*vectorMeta, len(segmentUniqueIDs))
	for _, segmentUniqueID := range segmentUniqueIDs {
		vectorMatas := make(map[string]*vectorMeta)
		labels := vectorLabelM[segmentUniqueID]
		for _, label := range labels {
			vm, ok := vectorMetas[label]
			if !ok {
				continue
			}
			vectorMatas[label] = vm
		}
		if len(vectorMetas) == 0 {
			continue
		}
		result[segmentUniqueID] = vectorMatas
	}
	return result, nil
}

func (i *EmbeddingIndexer) embeddingSaveNewVectors(ctx context.Context, vectors []*nonEmbeddingIndexVector) error {
	if len(vectors) == 0 {
		return nil
	}
	segmentVectors := map[string][]string{}
	for _, indexVector := range vectors {
		segmentUniqueID := indexVector.SegmentID
		segmentVectors[segmentUniqueID] = append(segmentVectors[segmentUniqueID], indexVector.Label())
	}
	err := i.saveSegmentVectorLabels(ctx, segmentVectors)
	if err != nil {
		return errors.WithMessagef(err, "failed to save vector set")
	}

	// get cached vector metas, if exists, directly save
	vectorCacheRaws, err := abase.BatchMGet(ctx, i.cacheAbase, batchSize, lo.Map(vectors,
		func(v *nonEmbeddingIndexVector, _ int) string {
			return buildVectorCacheAbaseKey(i.embeddingModel, v.EmbeddingContent)
		})...)
	if err != nil {
		return errors.WithMessagef(err, "failed to get vector cache")
	}
	uncachedVectors := make([]*nonEmbeddingIndexVector, 0)
	cachedVectors := make([]*nonEmbeddingIndexVector, 0)
	for idx, vectorCacheRaw := range vectorCacheRaws {
		if vectorCacheRaw == nil {
			uncachedVectors = append(uncachedVectors, vectors[idx])
			continue
		}
		vectorCacheRaw := vectorCacheRaw.(string)
		var fVector []float64
		err = json.Unmarshal([]byte(vectorCacheRaw), &fVector)
		if err != nil {
			logs.V1.CtxError(ctx, "failed to unmarshal vector cache: %+v", err)
			uncachedVectors = append(uncachedVectors, vectors[idx])
			continue
		}
		vectors[idx].V = fVector
		cachedVectors = append(cachedVectors, vectors[idx])
	}
	if err = i.saveVectorsToViking(ctx, cachedVectors); err != nil {
		logs.V1.CtxError(ctx, "failed to save cached vectors: %+v", err)
	}

	indexEventStats := common.GetStatistics(ctx, common.CKGIndexEventName)
	indexEventStats.GetEvent(common.CKGIndexEventName).SetParam(common.VectorsToEmbedParamKey, len(vectors))
	indexEventStats.GetEvent(common.CKGIndexEventName).SetParam(common.VectorsCachedParamKey, len(cachedVectors))
	indexEventStats.Report(ctx, common.CKGIndexEventName)

	i.wg.Add(len(uncachedVectors))
	return i.asyncEmbeddingService.Write(ctx, lo.Map(uncachedVectors,
		func(item *nonEmbeddingIndexVector, index int) *async_embedding.Request {
			item.LogID, _ = ctxvalues.LogID(ctx)
			return &async_embedding.Request{
				Input:  item.EmbeddingContent,
				Object: item,
			}
		})...)
}

func (i *EmbeddingIndexer) saveVectorsToViking(ctx context.Context, vectors []*nonEmbeddingIndexVector) error {
	vikingDatas := make([]*vikingclient.VikingDbData, 0, len(vectors))
	for _, v := range vectors {
		dslInfo := i.buildDSL(v.References, v.Segment.Tags)
		vikingDatas = append(vikingDatas, &vikingclient.VikingDbData{
			LabelLower: v.Lower,
			LabelUpper: v.Upper,
			DslInfo:    dslInfo,
			FVector:    v.V,
			Context:    []string{"default"},
		})
	}
	err := i.msetVectorMeta(ctx, lo.Map(vectors, func(item *nonEmbeddingIndexVector, index int) *vectorMeta {
		return &vectorMeta{
			Upper:      item.Upper,
			Lower:      item.Lower,
			V:          item.V,
			SegmentID:  item.SegmentID,
			DSLInfo:    item.DSLInfo,
			References: item.References,
		}
	}))
	if err != nil {
		return errors.WithMessagef(err, "failed to save vector meta")
	}
	// TODO: auto adjust batch size.
	_, err = viking.BatchAddData(i.vikingCli, 2, vikingDatas)
	if err != nil {
		return &common.CustomError{
			Ignore:  false,
			Err:     errors.WithMessagef(err, "failed to add vectors"),
			ErrType: common.CustomErrorTypeViking,
		}
	}
	return err
}

func referenceMatch(reference storage.FileReference, options *storage.QuerySegmentOption) bool {
	for _, datasetID := range options.DatasetIDs {
		if reference.DatasetID != datasetID {
			continue
		}
		if len(options.DatasourceIDs) == 0 {
			return true
		}
		for _, datasourceID := range options.DatasourceIDs {
			if reference.DatasourceID != datasourceID {
				continue
			}
			if len(options.FileIDs) == 0 {
				return true
			}
			for _, fileID := range options.FileIDs {
				if reference.FileID == fileID {
					return true
				}
			}
		}
	}
	return false
}

func (i *EmbeddingIndexer) GetCacheEmbeddingsByContents(ctx context.Context, options ...*storage.GetCacheEmbeddingByContentOption) (*storage.GetCacheEmbeddingByContentResult, error) {
	vectorCacheRaws, err := abase.BatchMGet(ctx, i.cacheAbase, batchSize, lo.Map(options,
		func(option *storage.GetCacheEmbeddingByContentOption, _ int) string {
			// 逻辑上确保 option 不为 nil
			return buildVectorCacheAbaseKey(i.embeddingModel, option.EmbeddingContent)
		})...)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get vector cache")
	}
	resp := &storage.GetCacheEmbeddingByContentResult{
		// key will be EmbeddingContent
		EmbeddingMapping: make(map[string][]float64),
	}
	for idx, vectorCacheRaw := range vectorCacheRaws {
		if vectorCacheRaw == nil {
			// 缓存未命中，不需向 resp 中写字段
			continue
		}
		vectorCacheRaw := vectorCacheRaw.(string)
		var fVector []float64
		err = json.Unmarshal([]byte(vectorCacheRaw), &fVector)
		if err != nil {
			logs.V1.CtxError(ctx, "failed to unmarshal vector cache: %+v", err)
			continue
		}
		if idx >= len(options) {
			logs.V1.CtxError(ctx, "unexpected error: idx %d >= len(options) %d", idx, len(options))
			continue
		}
		resp.EmbeddingMapping[options[idx].EmbeddingContent] = fVector
	}
	return resp, nil
}

func (i *EmbeddingIndexer) WriteCacheByEmbedding(ctx context.Context, option *storage.WriteCacheEmbeddingOption) error {
	if option == nil {
		return nil
	}
	if len(option.Embeddings) != len(option.EmbeddingContents) {
		return errors.New("embedding and embedding content length not equal")
	}
	multiErr := &multierror.Error{}
	msetAbasePairs := make([]string, 0, len(option.Embeddings)*2)
	abaseCacheKeys := make(map[string]bool)
	// 构造 cache key 与 value
	for idx := range option.EmbeddingContents {
		embeddingContent, embeddings := option.EmbeddingContents[idx], option.Embeddings[idx]
		cacheEmbedding, err := json.Marshal(embeddings.Embedding)
		if err != nil {
			logs.V1.CtxError(ctx, "failed to marshal embedding data")
			multiErr = multierror.Append(multiErr, errors.WithMessagef(err, "failed to marshal embedding data"))
			continue
		}
		cacheKey := buildVectorCacheAbaseKey(i.embeddingModel, embeddingContent)
		if _, existed := abaseCacheKeys[cacheKey]; existed {
			continue
		}
		abaseCacheKeys[cacheKey] = true
		msetAbasePairs = append(msetAbasePairs, cacheKey, string(cacheEmbedding))
	}
	err := backoff.Retry(func() error {
		err := abase.BatchMSet(ctx, i.cacheAbase, batchSize, msetAbasePairs...)
		if err != nil {
			errMsg := fmt.Sprintf("failed to save vector cache in retry loop, since %v, %v", err, abaseCacheKeys)
			newErr := errors.WithMessagef(err, errMsg)
			logs.V1.CtxError(ctx, errMsg)
			multiErr = multierror.Append(multiErr, newErr)
			return newErr
		}
		logs.V1.CtxInfo(ctx, "cached vector %v", abaseCacheKeys)
		return nil
	}, backoff.WithMaxRetries(backoff.NewExponentialBackOff(), 5))
	if err != nil {
		multiErr = multierror.Append(multiErr, errors.WithMessagef(err, "failed to save vector cache"))
	}
	SafeGo(ctx, func() {
		keyList := lo.Keys(abaseCacheKeys)
		count, expireErr := i.cacheAbase.MExpire(ctx, 7*24*time.Hour, keyList...)
		if expireErr != nil {
			logs.V1.CtxError(ctx, "failed to set expire for vector cache, err %v", expireErr)
		}
		logs.V1.CtxInfo(ctx, "vector cache set expire key size=%d count=%d key(one)=%s", len(keyList), count, keyList[0])
	})
	return multiErr.ErrorOrNil()
}

func SafeGo(ctx context.Context, f func()) {
	go func() {
		defer func() {
			if rec := recover(); rec != nil {
				logs.V1.CtxInfo(ctx, "panic recovered, err: %v, %s", rec, string(debug.Stack()))
			}
		}()
		f()
	}()
}

func (i *EmbeddingIndexer) Wait() {
	i.wg.Wait()
}

func (i *EmbeddingIndexer) GetEmbeddingModelName(ctx context.Context) (string, error) {
	if len(i.embeddingModel) == 0 {
		return "", errors.New("embedding model name is empty")
	}
	return i.embeddingModel, nil
}
