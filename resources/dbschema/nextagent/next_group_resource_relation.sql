CREATE TABLE `next_group_resource_relation`
(
    `id`          BIGINT UNSIGNED  NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT 'ID',
    `resource_id` VARCHAR(40)      NOT NULL COMMENT 'resource id',
    `group_id`    VARCHAR(40)      NOT NULL COMMENT 'group id',
    UNIQUE KEY    `uk_group_id_resource_id` (`group_id`, `resource_id`) COMMENT 'uniq key with group id and resource id',
    KEY           `idx_resource_id` (`resource_id`) COMMENT 'query by resource id'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='group resource relation';