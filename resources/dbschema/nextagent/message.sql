CREATE TABLE `next_message` -- next agent message
(
    `id`          BIGINT UNSIGNED                                                       NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT 'ID',
    `uid`         VARCHAR(40)                                                           NOT NULL COMMENT 'unique string ID, usually UUID',
    `session_id`  VARCHAR(40)                                                           NOT NULL COMMENT 'session ID',
    `task_id`     VARCHAR(40)                                                           NOT NULL COMMENT 'task ID',
    `role`        VARCHAR(20)                                                           NOT NULL COMMENT 'message role',
    `content`     JSON                                                                  NOT NULL COMMENT 'message content',
    `creator`     VARCHAR(64)                                                           NOT NULL COMMENT 'session creator',
    `attachments` JSON                                                                  NOT NULL COMMENT 'message attachments',
    `options`     JSON                                                                  COMMENT 'message options',
    `mentions`    JSON                                                                  COMMENT 'message mentions',
    `status`      int                                                                   NOT NULL DEFAULT 0 COMMENT 'status 0 sent, 1 wait',
    `created_at`  TIMESTAMP       DEFAULT CURRENT_TIMESTAMP                             NOT NULL COMMENT 'create timestamp',
    `updated_at`  TIMESTAMP       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL COMMENT 'last update timestamp',
    `deleted_at`  BIGINT UNSIGNED DEFAULT 0                                             NOT NULL COMMENT 'deleted at',
    KEY `idx_uid` (`uid`) COMMENT 'query agent run by ID',
    KEY `idx_session_id` (`session_id`) COMMENT 'query message by session ID',
    KEY `idx_task_id` (`task_id`) COMMENT 'query message run by task ID'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='message';