CREATE TABLE `template_star` -- template star
(
    `id`         BIGINT UNSIGNED                                                       NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT 'ID',
    `uid`        VARCHAR(40)                                                           NOT NULL COMMENT 'unique string ID, usually UUID',
    `template_id` VARCHAR(40)                                                          NOT NULL COMMENT 'template id',
    `username`   VA<PERSON>HAR(64)                                                           NOT NULL COMMENT 'username',
    `space_id`   VARCHAR(40)                                                           NOT NULL COMMENT 'space id',
    `created_at` TIMESTAMP       DEFAULT CURRENT_TIMESTAMP                             NOT NULL COMMENT 'create timestamp',
    `updated_at` TIMESTAMP       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL COMMENT 'last update timestamp',
    `deleted_at` BIGINT UNSIGNED DEFAULT 0                                             NOT NULL COMMENT 'deleted at',
    KEY `idx_template_id` (`template_id`) COMMENT 'get stars by template id',
    <PERSON><PERSON>Y `idx_username` (`username`) COMMENT 'get stars by username'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='template star';