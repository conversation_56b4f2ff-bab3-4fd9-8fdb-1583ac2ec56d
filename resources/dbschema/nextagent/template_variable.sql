CREATE TABLE `next_template_variable`
(
    `id`               BIGINT UNSIGNED                                                       NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT 'ID',
    `uid`              VARCHAR(40)                                                           NOT NULL COMMENT 'unique string ID, usually UUID',
    `form_key`         VARCHAR(64)                                                           NOT NULL COMMENT 'key',
    `form_value`       JSON                                                                  NOT NULL COMMENT 'value',
    `template_id`      VARCHAR(40)     DEFAULT ''                                            NOT NULL COMMENT 'template ID, usually UUID',
    `creator`          VARCHAR(64)     DEFAULT ''                                            NOT NULL COMMENT 'creator',
    `space_id`         VARCHAR(40)     DEFAULT ''                                            NOT NULL COMMENT 'space id',
    `created_at`       TIMESTAMP       DEFAULT CURRENT_TIMESTAMP                             NOT NULL COMMENT 'create timestamp',
    `updated_at`       TIMESTAMP       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL COMMENT 'last update timestamp',
    `deleted_at`       BIGINT UNSIGNED DEFAULT 0                                             NOT NULL COMMENT 'deleted at',
    KEY `idx_uid` (`uid`) COMMENT 'query by ID',
    UNIQUE KEY `uk_template_id_creator_key` (`template_id`, `creator`, `form_key`) COMMENT 'query by template id and creator and key'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='template_variable';