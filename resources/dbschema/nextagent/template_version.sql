CREATE TABLE `template_version` -- template version
(
    `id`         BIGINT UNSIGNED                                                       NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT 'ID',
    `uid`        VARCHAR(40)                                                           NOT NULL COMMENT 'unique string ID, usually UUID',
    `template_id` VARCHAR(40)                                                          NOT NULL COMMENT 'template id',
    `name`       VARCHAR(1024)                                                         NOT NULL COMMENT 'template name',
    `status`     VARCHAR(64)                                                           NOT NULL COMMENT 'template status',
    `version`    VARCHAR(64)                                                           NOT NULL COMMENT 'template version',
    `scope`      VARCHAR(64)                                                           NOT NULL COMMENT 'private/public',
    `category`   VARCHAR(64)                                                           NOT NULL COMMENT 'category, such as 代码开发、数据分析',
    `label`      VARCHAR(64)          DEFAULT ''                                       NOT NULL COMMENT 'label, such as 前端、客户端、服务端',
    `creator`    VARCHAR(64)                                                           NOT NULL COMMENT 'creator',
    `session_id` VARCHAR(40)          DEFAULT ''                                       NOT NULL COMMENT 'session id, optional',
    `event_timestamp` BIGINT UNSIGNED DEFAULT 0                                        NOT NULL COMMENT 'latest event timestamp',
    `prompt_content`      TEXT                                                         NULL COMMENT 'prompt_content',
    `prompt_variables`    JSON                                                         NULL COMMENT 'prompt_variables',
    `plan`                TEXT                                                         NULL COMMENT 'plan',
    `plan_steps`          JSON                                                         NULL COMMENT 'plan steps',
    `exp_sop`             JSON                                                         NULL COMMENT 'exp sop',
    `support_mcps`        JSON                                                         NOT NULL COMMENT 'support mcps',
    `expired`     BOOLEAN        DEFAULT FALSE                                         NOT NULL COMMENT 'if expired, exp_sop should by expired',
    `edited`     BOOLEAN        DEFAULT FALSE                                          NOT NULL COMMENT 'whether user edited template',
    `star_count`  INT UNSIGNED   DEFAULT 0                                             NOT NULL COMMENT 'star count',
    `source_space_id`    VARCHAR(40)         DEFAULT ''                                NOT NULL COMMENT 'source space id',
    `created_at` TIMESTAMP       DEFAULT CURRENT_TIMESTAMP                             NOT NULL COMMENT 'create timestamp',
    `updated_at` TIMESTAMP       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL COMMENT 'last update timestamp',
    `deleted_at` BIGINT UNSIGNED DEFAULT 0                                             NOT NULL COMMENT 'deleted at',
    KEY `idx_uid` (`uid`) COMMENT 'get template version by id',
    KEY `idx_status_category` (`status`, `category`) COMMENT 'get template version by status and category',
    UNIQUE KEY `uk_template_id_version` (`template_id`, `version`) COMMENT 'get template by ID and version',
    KEY `idx_session_id_event_timestamp` (`session_id`, `event_timestamp`) COMMENT 'get template version by session id and event timestamp',
    KEY `idx_space_id` (`space_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='template version';