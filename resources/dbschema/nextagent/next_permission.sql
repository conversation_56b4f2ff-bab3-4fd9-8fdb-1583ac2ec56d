CREATE TABLE `next_permission`
(
    `id`            BIGINT UNSIGNED  NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT 'ID',
    `uid`           VARCHAR(40)      NOT NULL COMMENT 'unique string ID, usually UUID',
    `resource_id`   VARCHAR(40)      NOT NULL COMMENT 'resource id',
    `resource_type` VARCHAR(40)      NOT NULL COMMENT 'resource type',
    `type`          VARCHAR(40)      NOT NULL COMMENT 'permission type',
    `external_id`   VARCHAR(64)      NOT NULL COMMENT 'external id maybe username, space id, department id',
    `role`          int              NOT NULL COMMENT 'permission role, 1000 vistor, 2000 member, 3000 manager',
    `created_at`    TIMESTAMP        DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT 'create timestamp',
    `updated_at`    TIMESTAMP        DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL COMMENT 'last update timestamp',
    `deleted_at`    BIGINT UNSIGNED  DEFAULT 0  NOT NULL COMMENT 'deleted at',
    UNIQUE KEY      `uk_uid` (`uid`) COMMENT 'query resource ID',
    UNIQUE KEY      `uk_resource_id_type_external_id_role_deleted_at` (`resource_id`, `type`, `external_id`, `role`, `deleted_at`) COMMENT 'unique key',
    KEY             `idx_resource_id_resource_type` (`resource_id`, `resource_type`) COMMENT 'query by resource id and resource type',
    KEY             `idx_external_id_type_resource_type` (`external_id`, `type`, `resource_type`) COMMENT 'query by external_id and type and resource_type'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='permission';