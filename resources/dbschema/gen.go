package dbschema

//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input llmstack/model.sql -destination ../../apps/aiservice/aiservice/manager/dal/po/model_po_gen.go  -package=po
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input llmstack/account.sql -destination ../../apps/aiservice/common/account/dal/po/account_po_gen.go  -package=po
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input llmstack/dataset.sql -destination ../../apps/aiservice/knowledgebase/dal/dataset_po_gen.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input llmstack/datasource.sql -destination ../../apps/aiservice/knowledgebase/dal/datasource_po_gen.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input llmstack/datastore.sql -destination ../../apps/aiservice/knowledgebase/dal/datastore_po_gen.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input llmstack/code_repository.sql -destination ../../apps/aiservice/knowledgebase/dal/code_repository_po_gen.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input llmstack/feedback.sql -destination ../../apps/aiservice/aiservice/journal/dal/po/feedback_po_gen.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input llmstack/prompt_completion.sql -destination ../../apps/aiservice/aiservice/journal/dal/po/prompt_completion_po_gen.go

// Copilot tables.
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input copilotstack/copilot_conversation.sql -destination ../../copilotstack/chat/dal/po/copilot_conversation_po_gen.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input copilotstack/copilot_message.sql -destination ../../copilotstack/chat/dal/po/copilot_message_po_gen.go

// copilot
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input copilot/feedback.sql -destination ../../idecopilot/journal/dal/po/feedback_po_gen.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input copilot/prompt_completion.sql -destination ../../idecopilot/journal/dal/po/prompt_completion_po_gen.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input copilot/code_completion.sql -destination ../../idecopilot/journal/dal/po/code_completion_po_gen.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input copilot/completion_multi_point.sql -destination ../../idecopilot/journal/dal/po/completion_multi_point_po_gen.go

// idecopilot
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input idecopilot/conversation.sql -destination ../../idecopilot/chat/dal/po/conversation_po_gen.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input idecopilot/project_knowledgebase.sql -destination ../../idecopilot/knowledgebase/dal/project_knowledgebase_po.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input idecopilot/workspace_knowledgebase.sql -destination ../../idecopilot/knowledgebase/dal/workspace_knowledgebase_po.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input idecopilot/user_growth_activity.sql -destination ../../idecopilot/ugactivity/dal/po/user_growth_activity_po.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input idecopilot/prompt_pipeline.sql -destination ../../idecopilot/journal/dal/po/prompt_pipeline.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input idecopilot/agent_run.sql -destination ../../idecopilot/agent/dal/po/run_po_gen.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input idecopilot/agent_run_memory.sql -destination ../../idecopilot/agent/dal/po/memory_po_gen.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input idecopilot/agent_run_step.sql -destination ../../idecopilot/agent/dal/po/step_po_gen.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input idecopilot/agent_run_message.sql -destination ../../idecopilot/agent/dal/po/message_po_gen.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input idecopilot/event.sql -destination ../../idecopilot/usergrowth/dal/po/event_po_gen.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input idecopilot/invitation.sql -destination ../../idecopilot/usergrowth/dal/po/invitation_po_gen.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input idecopilot/point_transaction.sql -destination ../../idecopilot/usergrowth/dal/po/point_transaction_po_gen.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input idecopilot/prize.sql -destination ../../idecopilot/usergrowth/dal/po/prize_po_gen.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input idecopilot/prize_redeem.sql -destination ../../idecopilot/usergrowth/dal/po/prize_redeem_po_gen.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input idecopilot/prize_ticket.sql -destination ../../idecopilot/usergrowth/dal/po/prize_ticket_po_gen.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input idecopilot/llm_proxy_model_quota.sql -destination ../../idecopilot/llmproxy/dal/po/llm_proxy_model_quota_po_gen.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input idecopilot/recipient_info.sql -destination ../../idecopilot/usergrowth/dal/po/recipient_info_po_gen.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input idecopilot/ai_usage.sql -destination ../../idecopilot/aiusage/dal/po/ai_usage_po_gen.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input idecopilot/ai_usage_record.sql -destination ../../idecopilot/aiusage/dal/po/ai_usage_record_po_gen.go
// knowledgebase
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input knowledgebase/config.sql -destination ../../knowledgebase/dal/config_po_gen.go

// search
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input search/chat_record.sql -destination ../../search/dal/db/po/chat_record_gen.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input search/feedback.sql -destination ../../search/dal/db/po/feedback_gen.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input search/session.sql -destination ../../search/dal/db/po/session_gen.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input search/discovery.sql -destination ../../search/dal/db/po/discovery_gen.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input search/tool_record.sql -destination ../../search/dal/db/po/tool_record_gen.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input search/lark_user.sql -destination ../../search/dal/db/po/lark_user_gen.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input search/lark_doc.sql -destination ../../search/dal/db/po/lark_doc_gen.go

// bitscopilot
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input bitscopilot/prompt_completion.sql -destination ../../bitscopilot/journal/dal/po/prompt_completion_po_gen.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input bitscopilot/feedback.sql -destination ../../bitscopilot/journal/dal/po/feedback_po_gen.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input bitscopilot/agent/agent_run.sql -destination ../../bitscopilot/agent/agents/dal/po/agent_run_po_gen.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input bitscopilot/agent/tool.sql -destination ../../bitscopilot/agent/tool/dal/po/tool_po_gen.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input bitscopilot/agent/toolkit.sql -destination ../../bitscopilot/agent/tool/dal/po/toolkit_po_gen.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input bitscopilot/agent/toolkit_binding.sql -destination ../../bitscopilot/agent/tool/dal/po/toolkit_binding_po_gen.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input bitscopilot/agent/app.sql -destination ../../bitscopilot/agent/app/dal/po/app_po_gen.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input bitscopilot/agent/conversations.sql -destination ../../bitscopilot/agent/chat/dal/po/conversation_po_gen.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input bitscopilot/agent/message.sql -destination ../../bitscopilot/agent/chat/dal/po/message_po_gen.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input bitscopilot/agent/artifact.sql -destination ../../bitscopilot/agent/workflow/dal/po/artifact_po_gen.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input bitscopilot/agent/knowledge.sql -destination ../../bitscopilot/agent/knowledge/dal/po/knowledge_po_gen.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input bitscopilot/agent/mcp_server.sql -destination ../../bitscopilot/agent/mcp/dal/po/mcp_server_po_gen.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input bitscopilot/agent/mcp_server_collection.sql -destination ../../bitscopilot/agent/mcp/dal/po/mcp_server_collection_po_gen.go

// llminfra
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input llminfra/biz_line.sql -destination ../../llminfra/bizline/dal/po/bizline_po.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input llminfra/scene.sql -destination ../../llminfra/scene/dal/po/scene_po.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input llminfra/model.sql -destination ../../llminfra/model/dal/po/model_po.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input llminfra/model_version.sql -destination ../../llminfra/model/dal/po/model_version_po.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input llminfra/scene_model_relation.sql -destination ../../llminfra/model/dal/po/scene_model_relation_po.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input llminfra/metric.sql -destination ../../llminfra/metric/dal/po/metric_po.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input llminfra/scene_metric_relation.sql -destination ../../llminfra/metric/dal/po/scene_metric_relation_po.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input llminfra/eval_task.sql -destination ../../llminfra/evaluation/dal/po/eval_task_po.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input llminfra/eval_subtask.sql -destination ../../llminfra/evaluation/dal/po/eval_subtask_po.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input llminfra/eval_report.sql -destination ../../llminfra/evaluation/dal/po/eval_report_po.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input llminfra/prompt.sql -destination ../../llminfra/prompt/dal/po/prompt_po.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input llminfra/prompt_version.sql -destination ../../llminfra/prompt/dal/po/prompt_version_po.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input llminfra/scene_prompt_relation.sql -destination ../../llminfra/prompt/dal/po/scene_prompt_relation_po.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input llminfra/eval_dataset.sql -destination ../../llminfra/dataset/dal/po/eval_dataset_po.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input llminfra/eval_dataset_version.sql -destination ../../llminfra/dataset/dal/po/eval_dataset_version_po.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input llminfra/scene_dataset_relation.sql -destination ../../llminfra/dataset/dal/po/scene_dataset_relation_po.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input llminfra/prompt_version_deploy.sql -destination ../../llminfra/prompt/dal/po/prompt_version_deploy_po.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input llminfra/prompt_version_deploy_workflow.sql -destination ../../llminfra/prompt/dal/po/prompt_version_deploy_workflow_po.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input llminfra/user_favorite.sql -destination ../../llminfra/userfavorite/dal/po/user_favorite.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input llminfra/analysis_dataset.sql -destination ../../llminfra/analysisdataset/dal/po/analysis_dataset_po.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input llminfra/analysis_template.sql -destination ../../llminfra/datasetcommon/dal/po/analysis_template_po.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input llminfra/dataset_clean_task.sql -destination ../../llminfra/editor/dal/po/dataset_clean_task_po.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input llminfra/train_dataset.sql -destination ../../llminfra/traindataset/dal/po/train_dataset.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input llminfra/train_dataset_version.sql -destination ../../llminfra/traindataset/dal/po/train_dataset_version.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input llminfra/scene_dataset_relation.sql -destination ../../llminfra/datasetcommon/dal/po/scene_dataset_relation_po.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input llminfra/entity_relation.sql -destination ../../llminfra/relation/dal/po/entity_relation_po.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input llminfra/model_experiment.sql -destination ../../llminfra/modelexperiment/dal/po/model_experiment_po.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input llminfra/scene_model_experiment_relation.sql -destination ../../llminfra/modelexperiment/dal/po/scene_model_experiment_relation_po.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input llminfra/data_process_task.sql -destination ../../llminfra/dataprocess/dal/po/data_process_task_po.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input llminfra/data_process_stage.sql -destination ../../llminfra/dataprocess/dal/po/data_process_stage_po.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input llminfra/dataset_process_within_stage.sql -destination ../../llminfra/dataprocess/dal/po/dataset_process_within_stage_po.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input llminfra/general_template.sql -destination ../../llminfra/template/dal/po/general_template.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input llminfra/tag.sql -destination ../../llminfra/tag/dal/po/tag_po.go

// devai-knowledge
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input devai/knowledge_tree_node.sql -destination ../../devai/knowledge/dal/po/knowledge_tree_node_po.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input devai/label.sql -destination ../../devai/knowledge/dal/po/label_po.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input devai/knowledge.sql -destination ../../devai/knowledge/dal/po/knowledge_po.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input devai/knowledge_relation.sql -destination ../../devai/knowledge/dal/po/knowledge_relation_po.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input devai/conversation_record.sql -destination ../../devai/knowledge/dal/po/conversation_record_po.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input devai/agent_lark_doc.sql -destination ../../devai/knowledge/dal/po/agent_lark_doc.go

// devai-data
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input devai/datasource.sql -destination ../../devai/data/dal/po/datasource_po.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input devai/datasource_item.sql -destination ../../devai/data/dal/po/datasource_item_po.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input devai/datasource_task.sql -destination ../../devai/data/dal/po/datasource_task_po.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input devai/document.sql -destination ../../devai/data/dal/po/document_po.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input devai/oncall.sql -destination ../../devai/data/dal/po/oncall_po.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input devai/qa.sql -destination ../../devai/data/dal/po/qa.go

// devai-assistant
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input devai/conversation.sql -destination ../../devai/chat/dal/po/conversation_po.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input devai/message.sql -destination ../../devai/chat/dal/po/message_po.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input devai/recall_segment.sql -destination ../../devai/chat/dal/po/recall_segment_po.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input devai/recommend_question.sql -destination ../../devai/chat/dal/po/recommend_question.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input devai/run.sql -destination ../../devai/chat/dal/po/run.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input devai/run_step.sql -destination ../../devai/chat/dal/po/run_step.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input devai/feedback.sql -destination ../../devai/journal/dal/po/feedback_po.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input devai/prompt_completion.sql -destination ../../devai/journal/dal/po/prompt_completion_po.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input devai/share.sql -destination ../../devai/share/dal/po/share_po.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input devai/lark_user.sql -destination ../../devai/account/dal/po/lark_user.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input devai/prompt.sql -destination ../../devai/chat/dal/po/prompt.go

// agent sphere
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input agentsphere/assignment.sql -destination ../../agentsphere/server/dal/po/assignment.go  -package=po
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input agentsphere/session.sql -destination ../../agentsphere/server/dal/po/session.go  -package=po
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input agentsphere/artifact.sql -destination ../../agentsphere/server/dal/po/artifact.go  -package=po
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input agentsphere/message.sql -destination ../../agentsphere/server/dal/po/message.go  -package=po
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input agentsphere/user.sql -destination ../../agentsphere/server/dal/po/user.go  -package=po
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input agentsphere/agent_run.sql -destination ../../agentsphere/runtime/dal/po/agent_run.go  -package=po
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input agentsphere/agent_run_step.sql -destination ../../agentsphere/runtime/dal/po/agent_run_step.go  -package=po
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input agentsphere/workspace_component_state.sql -destination ../../agentsphere/server/dal/po/workspace_component_state.go  -package=po
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input agentsphere/agent_run_tool_call.sql -destination ../../agentsphere/runtime/dal/po/agent_run_tool_call.go  -package=po
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input agentsphere/task.sql -destination ../../agentsphere/session/dal/po/task.go  -package=po
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input agentsphere/dataset.sql -destination ../../agentsphere/next_server/dal/po/dataset.go  -package=po

// codeassist
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input codeassist/context.sql -destination ../../codeassist/context/dal/po/context_po.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input codeassist/context_relation.sql -destination ../../codeassist/context/dal/po/context_relation_po.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input codeassist/directory.sql -destination ../../codeassist/context/dal/po/directory_po.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input codeassist/file.sql -destination ../../codeassist/context/dal/po/file_po.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input codeassist/repository.sql -destination ../../codeassist/context/dal/po/repository_po.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input codeassist/context_extra.sql -destination ../../codeassist/context/dal/po/context_extra_po.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input codeassist/artifact_templates.sql -destination ../../codeassist/chat/dal/po/artifact_templates_po.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input codeassist/sandbox_runtime.sql -destination ../../codeassist/sandbox/dal/po/sandbox_runtime_po.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input codeassist/artifacts_build_info.sql -destination ../../codeassist/sandbox/dal/po/artifacts_build_info_po.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input codeassist/artifacts_file_info.sql -destination ../../codeassist/sandbox/dal/po/artifacts_file_info_po.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input codeassist/sandbox.sql -destination ../../codeassist/sandbox/dal/po/sandbox_po.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input codeassist/task.sql -destination ../../codeassist/agent/dal/po/task_po.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input codeassist/agent_run.sql -destination ../../codeassist/agent/dal/po/agent_run_po.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input codeassist/agent_run_step.sql -destination ../../codeassist/agent/dal/po/agent_run_step_po.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input codeassist/agent_run_step_tool_call.sql -destination ../../codeassist/agent/dal/po/agent_run_step_tool_call_po.go

// next agent
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input nextagent/replay.sql -destination ../../agentsphere/next_server/dal/po/replay.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input nextagent/session.sql -destination ../../agentsphere/next_server/dal/po/session.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input nextagent/event.sql -destination ../../agentsphere/next_server/dal/po/event.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input nextagent/task.sql -destination ../../agentsphere/next_server/dal/po/task.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input nextagent/message.sql -destination ../../agentsphere/next_server/dal/po/message.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input nextagent/plan.sql -destination ../../agentsphere/next_server/dal/po/plan.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input nextagent/step.sql -destination ../../agentsphere/next_server/dal/po/step.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input nextagent/artifact.sql -destination ../../agentsphere/next_server/dal/po/artifact.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input nextagent/lark_user.sql -destination ../../agentsphere/next_server/dal/po/lark_user.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input nextagent/deployment.sql -destination ../../agentsphere/next_server/dal/po/deployment.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input nextagent/session_collection.sql -destination ../../agentsphere/next_server/dal/po/session_collection.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input nextagent/session_collection_run.sql -destination ../../agentsphere/next_server/dal/po/session_collection_run.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input nextagent/chat_completion.sql -destination ../../agentsphere/journal/dal/po/chat_completion.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input nextagent/template_variable.sql -destination ../../agentsphere/next_server/dal/po/template_variable.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input nextagent/agent.sql -destination ../../agentsphere/next_server/dal/po/agent.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input nextagent/agent_config.sql -destination ../../agentsphere/next_server/dal/po/agent_config.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input nextagent/agent_config_version.sql -destination ../../agentsphere/next_server/dal/po/agent_config_version.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input nextagent/agent_deploy.sql -destination ../../agentsphere/next_server/dal/po/agent_deploy.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input nextagent/prompt.sql -destination ../../agentsphere/next_server/dal/po/prompt.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input nextagent/prompt_version.sql -destination ../../agentsphere/next_server/dal/po/prompt_version.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input nextagent/activity_replay.sql -destination ../../agentsphere/next_server/dal/po/activity_replay.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input nextagent/activity_user.sql -destination ../../agentsphere/next_server/dal/po/activity_user.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input nextagent/user_group.sql -destination ../../agentsphere/next_server/dal/po/user_group.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input nextagent/invitation_code.sql -destination ../../agentsphere/next_server/dal/po/invitation_code.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input nextagent/template_version.sql -destination ../../agentsphere/next_server/dal/po/template_version.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input nextagent/template_file.sql -destination ../../agentsphere/next_server/dal/po/template_file.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input nextagent/debug_larkmd_conversion.sql -destination ../../agentsphere/next_server/dal/po/debug_larkmd_conversion.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input nextagent/template_share.sql -destination ../../agentsphere/next_server/dal/po/template_share.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input nextagent/template_share_target.sql -destination ../../agentsphere/next_server/dal/po/template_share_target.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input nextagent/template_star.sql -destination ../../agentsphere/next_server/dal/po/template_star.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input nextagent/next_group_resource_relation.sql -destination ../../agentsphere/next_server/dal/po/next_group_resource_relation.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input nextagent/next_permission.sql -destination ../../agentsphere/next_server/dal/po/next_permission.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input nextagent/next_resource.sql -destination ../../agentsphere/next_server/dal/po/next_resource.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input nextagent/dataset.sql -destination ../../agentsphere/next_server/dal/po/dataset.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input nextagent/document.sql -destination ../../agentsphere/next_server/dal/po/document.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input nextagent/segment.sql -destination ../../agentsphere/next_server/dal/po/segment.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input nextagent/space.sql -destination ../../agentsphere/next_server/dal/po/space.go
//go:generate go run code.byted.org/devgpt/kiwis/dev/tools/pogen -input nextagent/user_settings.sql -destination ../../agentsphere/next_server/dal/po/user_settings.go
