CREATE TABLE `agent_run_step_tool_call`
(
    `id`                BIGINT UNSIGNED                                                NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT 'ID',
    `unique_id`         VARCHAR(64)                                                    NOT NULL COMMENT 'unique string ID, usually UUID',
    `agent_run_id`      VARCHAR(64)                                                    NOT NULL COMMENT 'agent entity id',
    `agent_run_step_id` VARCHAR(64)                                                    NOT NULL COMMENT 'agent run step id',
    `tool_index`        BIGINT                                                         NOT NULL COMMENT 'tool index in step',
    `tool_name`         VARCHAR(128)                                                   NOT NULL COMMENT 'tool name',
    `status`            VARCHAR(64)                                                    NOT NULL COMMENT 'tool call status',
    `created_at`        DATETIME DEFAULT CURRENT_TIMESTAMP                             NOT NULL COMMENT 'create timestamp',
    `updated_at`        DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL COMMENT 'last update timestamp',
    unique key `uniq_unique_id` (`unique_id`),
    index `idx_agent_run_step_id` (`agent_run_step_id`),
    index `idx_agent_run_id` (`agent_run_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='agent run step tool call entity';