CREATE TABLE `agent_run`
(
    `id`              BIGINT UNSIGNED                                                NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT 'ID',
    `unique_id`       VARCHAR(64)                                                    NOT NULL COMMENT 'unique string ID, usually UUID',
    `task_id`         VARCHAR(64)                                                    NOT NULL COMMENT 'task id',
    `user_id`         VARCHAR(64)                                                    NOT NULL COMMENT 'user id',
    `conversation_id` VARCHAR(64)                                                    NOT NULL COMMENT 'conversation id',
    `agent_id`        VARCHAR(128)                                                   NOT NULL COMMENT 'agent ID, for specific scenarios',
    `agent_version`   VARCHAR(64)                                                    NOT NULL COMMENT 'agent version',
    `agent_meta`      JSON                                                           NULL COMMENT 'agent meta info',
    `agent_input`     JSON                                                           NULL COMMENT 'agent input data',
    `status`          TINYINT                                                        NOT NULL COMMENT '状态: 1-created,2-ready,3-running,4-completed,',
    `created_at`      DATETIME DEFAULT CURRENT_TIMESTAMP                             NOT NULL COMMENT 'create timestamp',
    `updated_at`      DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL COMMENT 'last update timestamp',
    unique key `uniq_unique_id` (`unique_id`),
    unique key `uniq_task_id` (`task_id`),
    index `idx_user_id` (`user_id`),
    index `idx_conversation_id` (`conversation_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='agent run entity';