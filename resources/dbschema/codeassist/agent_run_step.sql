CREATE TABLE `agent_run_step`
(
    `id`           BIGINT UNSIGNED                                                NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT 'ID',
    `unique_id`    VARCHAR(64)                                                    NOT NULL COMMENT 'unique string ID, usually UUID',
    `agent_run_id` VARCHAR(64)                                                    NOT NULL COMMENT 'agent entity id',
    `actor`        VARCHAR(128)                                                   NOT NULL COMMENT 'actor name',
    `actor_index`  BIGINT                                                         NOT NULL COMMENT 'actor index',
    `round`        BIGINT                                                         NOT NULL COMMENT 'round number in actor',
    `meta_data`    JSON                                                           NULL COMMENT 'agent run step meta data',
    `status`       TINYINT                                                        NOT NULL COMMENT 'status: 1-running,2-completed',
    `created_at`   DATETIME DEFAULT CURRENT_TIMESTAMP                             NOT NULL COMMENT 'create timestamp',
    `updated_at`   DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL COMMENT 'last update timestamp',
    unique key `uniq_unique_id` (`unique_id`),
    index `idx_run_id_status` (`agent_run_id`, `status`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='agent run step entity';