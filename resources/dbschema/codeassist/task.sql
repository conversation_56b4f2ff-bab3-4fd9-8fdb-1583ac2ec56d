CREATE TABLE `task`
(
    `id`              BIGINT UNSIGNED                                                 NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT 'ID',
    `unique_id`       VARCHAR(64)                                                     NOT NULL COMMENT 'unique string ID, usually UUID',
    `user_id`         VARCHAR(128)                                                    NOT NULL COMMENT 'user ID',
    `conversation_id` VARCHAR(128)                                                    NOT NULL COMMENT 'conversation id',
    `message_id`      VARCHAR(128)                                                    NOT NULL COMMENT 'message id',
    `upstream`        VARCHAR(64)                                                     NOT NULL COMMENT 'upstream type, e.g. doubao...',
    `status`          VARCHAR(64)                                                     NOT NULL COMMENT 'task status',
    `stage`           VARCHAR(64)                                                     NOT NULL COMMENT 'stage in running',
    `created_at`      TIMESTAMP DEFAULT CURRENT_TIMESTAMP                             NOT NULL COMMENT 'create timestamp',
    `updated_at`      TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL COMMENT 'last update timestamp',
    unique key `uniq_unique_id` (`unique_id`),
    unique key `uniq_message_id` (`message_id`),
    index `idx_user_id` (`user_id`),
    index `idx_conversation_id` (`conversation_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='task entity';